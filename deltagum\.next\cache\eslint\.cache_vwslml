[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\[sessionId]\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\contact\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\[id]\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\webhooks\\stripe\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\legal\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\professionals\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\CheckoutFloatingCandies.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ConfettiAnimation.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ContactFloatingCandies.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FAQFloatingCandies.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FloatingCandy.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\index.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\OrderSuccessAnimation.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ParallaxSection.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\TestimonialFloatingCandies.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartItem.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartSummary.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\EmptyCart.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\index.ts": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutForm.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutProgress.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\index.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\OrderSummary.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\PaymentMethods.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Footer.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Header.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\index.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\AgeVerificationModal.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CartModal.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CheckoutModal.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\index.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\ProductModal.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSelector.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSlider.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\index.ts": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\ProductCard.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\QuantitySelector.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\CheckoutModalProvider.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CartSection.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CheckoutSection.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ContactSection.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\FAQSection.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\HeroSection.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\index.ts": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ProductOverview.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\TestimonialsSection.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Badge.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Button.tsx": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Card.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\index.ts": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Input.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Loading.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Modal.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductImage.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductSkeleton.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Select.tsx": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\shuffle-cards.tsx": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Skeleton.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\testimonial-cards.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Toast.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\index.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-api.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-cart.ts": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-click-outside.ts": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-customer.ts": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-debounce.ts": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-intersection-observer.ts": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-local-storage.ts": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-products.ts": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-scroll-position.ts": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-window-size.ts": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAgeVerification.ts": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\animations.ts": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\auth.ts": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\config.ts": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\email.ts": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\flavors.ts": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\mock-data.ts": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\prisma.ts": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\stripe.ts": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\uploadthing.ts": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\utils.ts": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\validations.ts": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\cart-store.ts": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\checkout-modal-store.ts": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\customer-store.ts": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\index.ts": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\notification-store.ts": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\product-store.ts": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\ui-store.ts": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\types\\index.ts": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\create\\route.ts": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\login\\route.ts": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\logout\\route.ts": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\me\\route.ts": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\register\\route.ts": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-db\\route.ts": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAuthInit.ts": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\middleware.ts": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\auth-store.ts": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats\\route.ts": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\route.ts": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\user\\route.ts": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\bulk\\route.ts": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\route.ts": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\[variantId]\\route.ts": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\seed\\route.ts": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test\\database\\route.ts": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-orders\\route.ts": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-prisma\\route.ts": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\[filename]\\route.ts": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\user\\profile\\route.ts": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerDetails.tsx": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerList.tsx": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardStats.tsx": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardTest.tsx": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageManager.tsx": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageUpload.tsx": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderDetails.tsx": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderList.tsx": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\PricingTiers.tsx": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductDetails.tsx": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductForm.tsx": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductFormAdvanced.tsx": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductList.tsx": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\RoleManager.tsx": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\Settings.tsx": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleCustomerList.tsx": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleOrderList.tsx": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\DashboardHeader.tsx": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\Sidebar.tsx": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\StatCard.tsx": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\VariantManager.tsx": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripePaymentForm.tsx": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripeTestButton.tsx": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\debug\\StripeDebugPanel.tsx": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\profile\\UserOrders.tsx": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\StripeProvider.tsx": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\permissions.ts": "157"}, {"size": 13990, "mtime": 1752080900453, "results": "158", "hashOfConfig": "159"}, {"size": 5035, "mtime": 1752181837524, "results": "160", "hashOfConfig": "159"}, {"size": 1935, "mtime": 1751969325865, "results": "161", "hashOfConfig": "159"}, {"size": 6828, "mtime": 1752159728680, "results": "162", "hashOfConfig": "159"}, {"size": 3502, "mtime": 1752182076504, "results": "163", "hashOfConfig": "159"}, {"size": 3839, "mtime": 1752182043806, "results": "164", "hashOfConfig": "159"}, {"size": 8353, "mtime": 1752181777339, "results": "165", "hashOfConfig": "159"}, {"size": 4148, "mtime": 1752182096572, "results": "166", "hashOfConfig": "159"}, {"size": 8338, "mtime": 1752182200908, "results": "167", "hashOfConfig": "159"}, {"size": 2957, "mtime": 1752173741981, "results": "168", "hashOfConfig": "159"}, {"size": 3358, "mtime": 1751914442561, "results": "169", "hashOfConfig": "159"}, {"size": 1988, "mtime": 1751905531321, "results": "170", "hashOfConfig": "159"}, {"size": 15095, "mtime": 1752077600483, "results": "171", "hashOfConfig": "159"}, {"size": 10581, "mtime": 1752179808012, "results": "172", "hashOfConfig": "159"}, {"size": 15804, "mtime": 1752075621935, "results": "173", "hashOfConfig": "159"}, {"size": 11982, "mtime": 1752075683738, "results": "174", "hashOfConfig": "159"}, {"size": 2153, "mtime": 1751906680568, "results": "175", "hashOfConfig": "159"}, {"size": 4392, "mtime": 1752094078108, "results": "176", "hashOfConfig": "159"}, {"size": 5581, "mtime": 1751906793436, "results": "177", "hashOfConfig": "159"}, {"size": 4146, "mtime": 1751906763690, "results": "178", "hashOfConfig": "159"}, {"size": 4200, "mtime": 1751913074524, "results": "179", "hashOfConfig": "159"}, {"size": 1074, "mtime": 1751906856197, "results": "180", "hashOfConfig": "159"}, {"size": 8800, "mtime": 1751906828244, "results": "181", "hashOfConfig": "159"}, {"size": 5782, "mtime": 1752096261709, "results": "182", "hashOfConfig": "159"}, {"size": 3734, "mtime": 1751906740363, "results": "183", "hashOfConfig": "159"}, {"size": 5798, "mtime": 1751914613690, "results": "184", "hashOfConfig": "159"}, {"size": 8479, "mtime": 1751973506787, "results": "185", "hashOfConfig": "159"}, {"size": 6932, "mtime": 1751906184228, "results": "186", "hashOfConfig": "159"}, {"size": 122, "mtime": 1751906193685, "results": "187", "hashOfConfig": "159"}, {"size": 9792, "mtime": 1751974025042, "results": "188", "hashOfConfig": "159"}, {"size": 8386, "mtime": 1751906326576, "results": "189", "hashOfConfig": "159"}, {"size": 331, "mtime": 1752162759170, "results": "190", "hashOfConfig": "159"}, {"size": 7298, "mtime": 1751915313512, "results": "191", "hashOfConfig": "159"}, {"size": 15596, "mtime": 1752169325801, "results": "192", "hashOfConfig": "159"}, {"size": 10936, "mtime": 1752136626830, "results": "193", "hashOfConfig": "159"}, {"size": 14127, "mtime": 1752179498894, "results": "194", "hashOfConfig": "159"}, {"size": 175, "mtime": 1751904955025, "results": "195", "hashOfConfig": "159"}, {"size": 6324, "mtime": 1752162196749, "results": "196", "hashOfConfig": "159"}, {"size": 4629, "mtime": 1752075744239, "results": "197", "hashOfConfig": "159"}, {"size": 15484, "mtime": 1752179485390, "results": "198", "hashOfConfig": "159"}, {"size": 5810, "mtime": 1752056119091, "results": "199", "hashOfConfig": "159"}, {"size": 390, "mtime": 1751931777477, "results": "200", "hashOfConfig": "159"}, {"size": 997, "mtime": 1751904938338, "results": "201", "hashOfConfig": "159"}, {"size": 6871, "mtime": 1752029312658, "results": "202", "hashOfConfig": "159"}, {"size": 9633, "mtime": 1752096598052, "results": "203", "hashOfConfig": "159"}, {"size": 94, "mtime": 1751905943809, "results": "204", "hashOfConfig": "159"}, {"size": 8824, "mtime": 1752075404314, "results": "205", "hashOfConfig": "159"}, {"size": 4701, "mtime": 1752028686233, "results": "206", "hashOfConfig": "159"}, {"size": 360, "mtime": 1751973662926, "results": "207", "hashOfConfig": "159"}, {"size": 9271, "mtime": 1751982096544, "results": "208", "hashOfConfig": "159"}, {"size": 11109, "mtime": 1751917800908, "results": "209", "hashOfConfig": "159"}, {"size": 13899, "mtime": 1752158696531, "results": "210", "hashOfConfig": "159"}, {"size": 10948, "mtime": 1752075788033, "results": "211", "hashOfConfig": "159"}, {"size": 12000, "mtime": 1752135422441, "results": "212", "hashOfConfig": "159"}, {"size": 322, "mtime": 1752002026619, "results": "213", "hashOfConfig": "159"}, {"size": 10023, "mtime": 1752158134886, "results": "214", "hashOfConfig": "159"}, {"size": 3445, "mtime": 1752135985545, "results": "215", "hashOfConfig": "159"}, {"size": 6779, "mtime": 1751919262277, "results": "216", "hashOfConfig": "159"}, {"size": 4297, "mtime": 1751919108121, "results": "217", "hashOfConfig": "159"}, {"size": 4383, "mtime": 1751920081609, "results": "218", "hashOfConfig": "159"}, {"size": 1147, "mtime": 1752093997233, "results": "219", "hashOfConfig": "159"}, {"size": 9954, "mtime": 1752132072545, "results": "220", "hashOfConfig": "159"}, {"size": 4244, "mtime": 1751904549304, "results": "221", "hashOfConfig": "159"}, {"size": 5334, "mtime": 1752055978341, "results": "222", "hashOfConfig": "159"}, {"size": 2683, "mtime": 1752075579815, "results": "223", "hashOfConfig": "159"}, {"size": 1698, "mtime": 1752037776963, "results": "224", "hashOfConfig": "159"}, {"size": 1995, "mtime": 1751920113481, "results": "225", "hashOfConfig": "159"}, {"size": 2511, "mtime": 1752135889693, "results": "226", "hashOfConfig": "159"}, {"size": 411, "mtime": 1752037582057, "results": "227", "hashOfConfig": "159"}, {"size": 3517, "mtime": 1752135909034, "results": "228", "hashOfConfig": "159"}, {"size": 3670, "mtime": 1751904570609, "results": "229", "hashOfConfig": "159"}, {"size": 503, "mtime": 1751905013426, "results": "230", "hashOfConfig": "159"}, {"size": 1211, "mtime": 1751910816600, "results": "231", "hashOfConfig": "159"}, {"size": 358, "mtime": 1751903810535, "results": "232", "hashOfConfig": "159"}, {"size": 542, "mtime": 1751903651068, "results": "233", "hashOfConfig": "159"}, {"size": 92, "mtime": 1751903836366, "results": "234", "hashOfConfig": "159"}, {"size": 378, "mtime": 1751903605329, "results": "235", "hashOfConfig": "159"}, {"size": 1285, "mtime": 1751920423580, "results": "236", "hashOfConfig": "159"}, {"size": 1843, "mtime": 1751903597483, "results": "237", "hashOfConfig": "159"}, {"size": 91, "mtime": 1751903823409, "results": "238", "hashOfConfig": "159"}, {"size": 674, "mtime": 1751903634486, "results": "239", "hashOfConfig": "159"}, {"size": 662, "mtime": 1751903643125, "results": "240", "hashOfConfig": "159"}, {"size": 2254, "mtime": 1751931518494, "results": "241", "hashOfConfig": "159"}, {"size": 5466, "mtime": 1751905452786, "results": "242", "hashOfConfig": "159"}, {"size": 2429, "mtime": 1752182126137, "results": "243", "hashOfConfig": "159"}, {"size": 2982, "mtime": 1752075226881, "results": "244", "hashOfConfig": "159"}, {"size": 2848, "mtime": 1751903333245, "results": "245", "hashOfConfig": "159"}, {"size": 899, "mtime": 1752075559661, "results": "246", "hashOfConfig": "159"}, {"size": 3386, "mtime": 1752099001422, "results": "247", "hashOfConfig": "159"}, {"size": 755, "mtime": 1752176937104, "results": "248", "hashOfConfig": "159"}, {"size": 519, "mtime": 1751921891882, "results": "249", "hashOfConfig": "159"}, {"size": 1410, "mtime": 1751910941598, "results": "250", "hashOfConfig": "159"}, {"size": 8265, "mtime": 1751910893227, "results": "251", "hashOfConfig": "159"}, {"size": 7971, "mtime": 1752164690727, "results": "252", "hashOfConfig": "159"}, {"size": 3193, "mtime": 1751967855547, "results": "253", "hashOfConfig": "159"}, {"size": 333, "mtime": 1751973250881, "results": "254", "hashOfConfig": "159"}, {"size": 2100, "mtime": 1751903753069, "results": "255", "hashOfConfig": "159"}, {"size": 652, "mtime": 1752081874462, "results": "256", "hashOfConfig": "159"}, {"size": 2531, "mtime": 1751922060718, "results": "257", "hashOfConfig": "159"}, {"size": 3080, "mtime": 1752057854893, "results": "258", "hashOfConfig": "159"}, {"size": 3149, "mtime": 1751903789807, "results": "259", "hashOfConfig": "159"}, {"size": 5645, "mtime": 1752097783415, "results": "260", "hashOfConfig": "159"}, {"size": 17142, "mtime": 1752181540081, "results": "261", "hashOfConfig": "159"}, {"size": 1600, "mtime": 1752181918346, "results": "262", "hashOfConfig": "159"}, {"size": 2117, "mtime": 1752088205248, "results": "263", "hashOfConfig": "159"}, {"size": 618, "mtime": 1752081794017, "results": "264", "hashOfConfig": "159"}, {"size": 1291, "mtime": 1752136965687, "results": "265", "hashOfConfig": "159"}, {"size": 4535, "mtime": 1752181899379, "results": "266", "hashOfConfig": "159"}, {"size": 728, "mtime": 1752094197586, "results": "267", "hashOfConfig": "159"}, {"size": 22320, "mtime": 1752179871466, "results": "268", "hashOfConfig": "159"}, {"size": 265, "mtime": 1752082579534, "results": "269", "hashOfConfig": "159"}, {"size": 815, "mtime": 1752131292533, "results": "270", "hashOfConfig": "159"}, {"size": 4236, "mtime": 1752134359685, "results": "271", "hashOfConfig": "159"}, {"size": 4575, "mtime": 1752134816407, "results": "272", "hashOfConfig": "159"}, {"size": 4534, "mtime": 1752173372224, "results": "273", "hashOfConfig": "159"}, {"size": 2354, "mtime": 1752134787059, "results": "274", "hashOfConfig": "159"}, {"size": 6923, "mtime": 1752182348967, "results": "275", "hashOfConfig": "159"}, {"size": 6172, "mtime": 1752182285352, "results": "276", "hashOfConfig": "159"}, {"size": 5697, "mtime": 1752156433527, "results": "277", "hashOfConfig": "159"}, {"size": 8499, "mtime": 1752182645630, "results": "278", "hashOfConfig": "159"}, {"size": 1234, "mtime": 1752134861252, "results": "279", "hashOfConfig": "159"}, {"size": 3259, "mtime": 1752179835778, "results": "280", "hashOfConfig": "159"}, {"size": 1471, "mtime": 1752176185844, "results": "281", "hashOfConfig": "159"}, {"size": 3255, "mtime": 1752104326962, "results": "282", "hashOfConfig": "159"}, {"size": 2628, "mtime": 1752158109808, "results": "283", "hashOfConfig": "159"}, {"size": 3595, "mtime": 1752108494011, "results": "284", "hashOfConfig": "159"}, {"size": 12282, "mtime": 1752134119038, "results": "285", "hashOfConfig": "159"}, {"size": 6399, "mtime": 1752168615462, "results": "286", "hashOfConfig": "159"}, {"size": 8747, "mtime": 1752171092748, "results": "287", "hashOfConfig": "159"}, {"size": 3069, "mtime": 1752134918558, "results": "288", "hashOfConfig": "159"}, {"size": 11542, "mtime": 1752104709327, "results": "289", "hashOfConfig": "159"}, {"size": 9720, "mtime": 1752135084899, "results": "290", "hashOfConfig": "159"}, {"size": 9162, "mtime": 1752101759112, "results": "291", "hashOfConfig": "159"}, {"size": 10775, "mtime": 1752134938528, "results": "292", "hashOfConfig": "159"}, {"size": 10294, "mtime": 1752134953711, "results": "293", "hashOfConfig": "159"}, {"size": 8188, "mtime": 1752135044952, "results": "294", "hashOfConfig": "159"}, {"size": 13241, "mtime": 1752181183197, "results": "295", "hashOfConfig": "159"}, {"size": 13820, "mtime": 1752181238812, "results": "296", "hashOfConfig": "159"}, {"size": 11571, "mtime": 1752106544784, "results": "297", "hashOfConfig": "159"}, {"size": 10197, "mtime": 1752101449686, "results": "298", "hashOfConfig": "159"}, {"size": 10909, "mtime": 1752106686169, "results": "299", "hashOfConfig": "159"}, {"size": 13755, "mtime": 1752158495276, "results": "300", "hashOfConfig": "159"}, {"size": 7092, "mtime": 1752101380133, "results": "301", "hashOfConfig": "159"}, {"size": 10361, "mtime": 1752105711820, "results": "302", "hashOfConfig": "159"}, {"size": 13958, "mtime": 1752105409705, "results": "303", "hashOfConfig": "159"}, {"size": 6126, "mtime": 1752107906239, "results": "304", "hashOfConfig": "159"}, {"size": 8577, "mtime": 1752107941253, "results": "305", "hashOfConfig": "159"}, {"size": 5892, "mtime": 1752105510737, "results": "306", "hashOfConfig": "159"}, {"size": 4299, "mtime": 1752105482769, "results": "307", "hashOfConfig": "159"}, {"size": 2082, "mtime": 1752105457295, "results": "308", "hashOfConfig": "159"}, {"size": 22313, "mtime": 1752157088911, "results": "309", "hashOfConfig": "159"}, {"size": 6152, "mtime": 1752173681612, "results": "310", "hashOfConfig": "159"}, {"size": 3229, "mtime": 1752162598829, "results": "311", "hashOfConfig": "159"}, {"size": 5483, "mtime": 1752164770944, "results": "312", "hashOfConfig": "159"}, {"size": 7858, "mtime": 1752108688612, "results": "313", "hashOfConfig": "159"}, {"size": 914, "mtime": 1752162040145, "results": "314", "hashOfConfig": "159"}, {"size": 4245, "mtime": 1752110944116, "results": "315", "hashOfConfig": "159"}, {"filePath": "316", "messages": "317", "suppressedMessages": "318", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "18vo38s", {"filePath": "319", "messages": "320", "suppressedMessages": "321", "errorCount": 3, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "322", "messages": "323", "suppressedMessages": "324", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "325", "messages": "326", "suppressedMessages": "327", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "328", "messages": "329", "suppressedMessages": "330", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "331", "messages": "332", "suppressedMessages": "333", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "334", "messages": "335", "suppressedMessages": "336", "errorCount": 4, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 1, "fixableWarningCount": 0, "source": null}, {"filePath": "337", "messages": "338", "suppressedMessages": "339", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "340", "messages": "341", "suppressedMessages": "342", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "343", "messages": "344", "suppressedMessages": "345", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "346", "messages": "347", "suppressedMessages": "348", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "349", "messages": "350", "suppressedMessages": "351", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "352", "messages": "353", "suppressedMessages": "354", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "355", "messages": "356", "suppressedMessages": "357", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "358", "messages": "359", "suppressedMessages": "360", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "361", "messages": "362", "suppressedMessages": "363", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "364", "messages": "365", "suppressedMessages": "366", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "367", "messages": "368", "suppressedMessages": "369", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "370", "messages": "371", "suppressedMessages": "372", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "373", "messages": "374", "suppressedMessages": "375", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "376", "messages": "377", "suppressedMessages": "378", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "379", "messages": "380", "suppressedMessages": "381", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "382", "messages": "383", "suppressedMessages": "384", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "385", "messages": "386", "suppressedMessages": "387", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "388", "messages": "389", "suppressedMessages": "390", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "391", "messages": "392", "suppressedMessages": "393", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "394", "messages": "395", "suppressedMessages": "396", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "397", "messages": "398", "suppressedMessages": "399", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "400", "messages": "401", "suppressedMessages": "402", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "403", "messages": "404", "suppressedMessages": "405", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "406", "messages": "407", "suppressedMessages": "408", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "409", "messages": "410", "suppressedMessages": "411", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "412", "messages": "413", "suppressedMessages": "414", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "415", "messages": "416", "suppressedMessages": "417", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "418", "messages": "419", "suppressedMessages": "420", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "421", "messages": "422", "suppressedMessages": "423", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "424", "messages": "425", "suppressedMessages": "426", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "427", "messages": "428", "suppressedMessages": "429", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "430", "messages": "431", "suppressedMessages": "432", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "433", "messages": "434", "suppressedMessages": "435", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "436", "messages": "437", "suppressedMessages": "438", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "439", "messages": "440", "suppressedMessages": "441", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "442", "messages": "443", "suppressedMessages": "444", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "445", "messages": "446", "suppressedMessages": "447", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "448", "messages": "449", "suppressedMessages": "450", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "451", "messages": "452", "suppressedMessages": "453", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "454", "messages": "455", "suppressedMessages": "456", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "457", "messages": "458", "suppressedMessages": "459", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "460", "messages": "461", "suppressedMessages": "462", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "463", "messages": "464", "suppressedMessages": "465", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "466", "messages": "467", "suppressedMessages": "468", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "469", "messages": "470", "suppressedMessages": "471", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "472", "messages": "473", "suppressedMessages": "474", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "475", "messages": "476", "suppressedMessages": "477", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "478", "messages": "479", "suppressedMessages": "480", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "481", "messages": "482", "suppressedMessages": "483", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "484", "messages": "485", "suppressedMessages": "486", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "487", "messages": "488", "suppressedMessages": "489", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "490", "messages": "491", "suppressedMessages": "492", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "493", "messages": "494", "suppressedMessages": "495", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "496", "messages": "497", "suppressedMessages": "498", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "499", "messages": "500", "suppressedMessages": "501", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "502", "messages": "503", "suppressedMessages": "504", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "505", "messages": "506", "suppressedMessages": "507", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "508", "messages": "509", "suppressedMessages": "510", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "511", "messages": "512", "suppressedMessages": "513", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "514", "messages": "515", "suppressedMessages": "516", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "517", "messages": "518", "suppressedMessages": "519", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "520", "messages": "521", "suppressedMessages": "522", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "523", "messages": "524", "suppressedMessages": "525", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "526", "messages": "527", "suppressedMessages": "528", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "529", "messages": "530", "suppressedMessages": "531", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "532", "messages": "533", "suppressedMessages": "534", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "535", "messages": "536", "suppressedMessages": "537", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "538", "messages": "539", "suppressedMessages": "540", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "541", "messages": "542", "suppressedMessages": "543", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "544", "messages": "545", "suppressedMessages": "546", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "547", "messages": "548", "suppressedMessages": "549", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "550", "messages": "551", "suppressedMessages": "552", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "553", "messages": "554", "suppressedMessages": "555", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "556", "messages": "557", "suppressedMessages": "558", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "559", "messages": "560", "suppressedMessages": "561", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "562", "messages": "563", "suppressedMessages": "564", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "565", "messages": "566", "suppressedMessages": "567", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "568", "messages": "569", "suppressedMessages": "570", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "571", "messages": "572", "suppressedMessages": "573", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "574", "messages": "575", "suppressedMessages": "576", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "577", "messages": "578", "suppressedMessages": "579", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "580", "messages": "581", "suppressedMessages": "582", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "583", "messages": "584", "suppressedMessages": "585", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "586", "messages": "587", "suppressedMessages": "588", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "589", "messages": "590", "suppressedMessages": "591", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "592", "messages": "593", "suppressedMessages": "594", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "595", "messages": "596", "suppressedMessages": "597", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "598", "messages": "599", "suppressedMessages": "600", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "601", "messages": "602", "suppressedMessages": "603", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "604", "messages": "605", "suppressedMessages": "606", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "607", "messages": "608", "suppressedMessages": "609", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "610", "messages": "611", "suppressedMessages": "612", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "613", "messages": "614", "suppressedMessages": "615", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "616", "messages": "617", "suppressedMessages": "618", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "619", "messages": "620", "suppressedMessages": "621", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "622", "messages": "623", "suppressedMessages": "624", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 24, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "625", "messages": "626", "suppressedMessages": "627", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "628", "messages": "629", "suppressedMessages": "630", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "631", "messages": "632", "suppressedMessages": "633", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "634", "messages": "635", "suppressedMessages": "636", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "637", "messages": "638", "suppressedMessages": "639", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "640", "messages": "641", "suppressedMessages": "642", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "643", "messages": "644", "suppressedMessages": "645", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "646", "messages": "647", "suppressedMessages": "648", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "649", "messages": "650", "suppressedMessages": "651", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "652", "messages": "653", "suppressedMessages": "654", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "655", "messages": "656", "suppressedMessages": "657", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "658", "messages": "659", "suppressedMessages": "660", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "661", "messages": "662", "suppressedMessages": "663", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "664", "messages": "665", "suppressedMessages": "666", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "667", "messages": "668", "suppressedMessages": "669", "errorCount": 2, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "670", "messages": "671", "suppressedMessages": "672", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "673", "messages": "674", "suppressedMessages": "675", "errorCount": 14, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "676", "messages": "677", "suppressedMessages": "678", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "679", "messages": "680", "suppressedMessages": "681", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "682", "messages": "683", "suppressedMessages": "684", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "685", "messages": "686", "suppressedMessages": "687", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "688", "messages": "689", "suppressedMessages": "690", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "691", "messages": "692", "suppressedMessages": "693", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "694", "messages": "695", "suppressedMessages": "696", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "697", "messages": "698", "suppressedMessages": "699", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "700", "messages": "701", "suppressedMessages": "702", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "703", "messages": "704", "suppressedMessages": "705", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "706", "messages": "707", "suppressedMessages": "708", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "709", "messages": "710", "suppressedMessages": "711", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "712", "messages": "713", "suppressedMessages": "714", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "715", "messages": "716", "suppressedMessages": "717", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "718", "messages": "719", "suppressedMessages": "720", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "721", "messages": "722", "suppressedMessages": "723", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "724", "messages": "725", "suppressedMessages": "726", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "727", "messages": "728", "suppressedMessages": "729", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "730", "messages": "731", "suppressedMessages": "732", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "733", "messages": "734", "suppressedMessages": "735", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "736", "messages": "737", "suppressedMessages": "738", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "739", "messages": "740", "suppressedMessages": "741", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "742", "messages": "743", "suppressedMessages": "744", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "745", "messages": "746", "suppressedMessages": "747", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "748", "messages": "749", "suppressedMessages": "750", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "751", "messages": "752", "suppressedMessages": "753", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "754", "messages": "755", "suppressedMessages": "756", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "757", "messages": "758", "suppressedMessages": "759", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "760", "messages": "761", "suppressedMessages": "762", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "763", "messages": "764", "suppressedMessages": "765", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "766", "messages": "767", "suppressedMessages": "768", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "769", "messages": "770", "suppressedMessages": "771", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "772", "messages": "773", "suppressedMessages": "774", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "775", "messages": "776", "suppressedMessages": "777", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "778", "messages": "779", "suppressedMessages": "780", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "781", "messages": "782", "suppressedMessages": "783", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "784", "messages": "785", "suppressedMessages": "786", "errorCount": 1, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\about\\page.tsx", ["787", "788", "789", "790", "791", "792", "793", "794", "795", "796", "797", "798"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\route.ts", ["799", "800", "801", "802"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\[sessionId]\\route.ts", ["803"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\contact\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\route.ts", ["804"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\[id]\\route.ts", ["805"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\route.ts", ["806", "807", "808", "809", "810", "811", "812"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\[id]\\route.ts", ["813", "814"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\route.ts", ["815", "816"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\route.ts", ["817", "818"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\webhooks\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\legal\\page.tsx", ["819", "820", "821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx", ["840", "841", "842", "843", "844", "845", "846", "847"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\professionals\\page.tsx", ["848", "849"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\CheckoutFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ConfettiAnimation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ContactFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FAQFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FloatingCandy.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\OrderSuccessAnimation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ParallaxSection.tsx", ["850", "851"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\TestimonialFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartItem.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartSummary.tsx", ["852"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\EmptyCart.tsx", ["853", "854", "855"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutForm.tsx", ["856", "857"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutProgress.tsx", ["858"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\OrderSummary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\PaymentMethods.tsx", ["859", "860", "861"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Footer.tsx", ["862"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Header.tsx", ["863", "864"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\AgeVerificationModal.tsx", ["865", "866", "867", "868", "869"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CartModal.tsx", ["870", "871"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CheckoutModal.tsx", ["872"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\ProductModal.tsx", ["873"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSlider.tsx", ["874", "875", "876"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\ProductCard.tsx", ["877", "878", "879", "880", "881", "882", "883"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\QuantitySelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\CheckoutModalProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CartSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CheckoutSection.tsx", ["884", "885", "886"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ContactSection.tsx", ["887"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\HeroSection.tsx", ["888"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ProductOverview.tsx", ["889", "890", "891", "892"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Badge.tsx", ["893", "894", "895", "896", "897", "898"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Button.tsx", ["899", "900", "901", "902", "903", "904"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Card.tsx", ["905", "906", "907", "908", "909", "910"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Input.tsx", ["911", "912", "913", "914", "915", "916", "917", "918", "919", "920", "921", "922"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductImage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\shuffle-cards.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\testimonial-cards.tsx", ["923", "924", "925"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Toast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-api.ts", ["926"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-cart.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-click-outside.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-customer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-debounce.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-intersection-observer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-local-storage.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-products.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-scroll-position.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-window-size.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAgeVerification.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\animations.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\auth.ts", ["927", "928"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\email.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\flavors.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\mock-data.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\stripe.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\uploadthing.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\cart-store.ts", ["929"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\checkout-modal-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\customer-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\notification-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\product-store.ts", ["930"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\ui-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx", ["931", "932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\create\\route.ts", ["956"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\logout\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\me\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\register\\route.ts", ["957", "958"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-db\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx", ["959", "960", "961", "962", "963"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAuthInit.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\middleware.ts", ["964", "965"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\auth-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\route.ts", ["966"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\user\\route.ts", ["967"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\bulk\\route.ts", ["968", "969", "970", "971", "972", "973"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\route.ts", ["974", "975", "976"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\[variantId]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\seed\\route.ts", ["977", "978", "979", "980", "981", "982", "983", "984", "985", "986", "987", "988", "989", "990", "991"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test\\database\\route.ts", ["992"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-orders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-prisma\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts", ["993", "994", "995"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\[filename]\\route.ts", ["996"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\user\\profile\\route.ts", ["997"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx", ["998", "999", "1000"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx", ["1001", "1002", "1003", "1004"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx", ["1005", "1006", "1007", "1008"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx", ["1009", "1010"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerDetails.tsx", ["1011"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerList.tsx", ["1012", "1013"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardStats.tsx", ["1014", "1015"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardTest.tsx", ["1016", "1017"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageManager.tsx", ["1018", "1019"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageUpload.tsx", ["1020", "1021", "1022"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderDetails.tsx", ["1023", "1024"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderList.tsx", ["1025"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\PricingTiers.tsx", ["1026"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductDetails.tsx", ["1027", "1028", "1029"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductForm.tsx", ["1030", "1031", "1032"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductFormAdvanced.tsx", ["1033", "1034", "1035", "1036", "1037"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductList.tsx", ["1038", "1039", "1040"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\RoleManager.tsx", ["1041"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\Settings.tsx", ["1042", "1043", "1044", "1045", "1046"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleCustomerList.tsx", ["1047", "1048"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleOrderList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\DashboardHeader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\VariantManager.tsx", ["1049", "1050", "1051", "1052", "1053"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripePaymentForm.tsx", ["1054", "1055", "1056", "1057"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripeTestButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\debug\\StripeDebugPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\profile\\UserOrders.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\StripeProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\permissions.ts", ["1058", "1059", "1060", "1061", "1062", "1063"], [], {"ruleId": "1064", "severity": 1, "message": "1065", "line": 25, "column": 26, "nodeType": "1066", "messageId": "1067", "suggestions": "1068"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 26, "column": 41, "nodeType": "1066", "messageId": "1067", "suggestions": "1069"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 64, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1070"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 64, "column": 28, "nodeType": "1066", "messageId": "1067", "suggestions": "1071"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 65, "column": 51, "nodeType": "1066", "messageId": "1067", "suggestions": "1072"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 74, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1073"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 79, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1074"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 82, "column": 37, "nodeType": "1066", "messageId": "1067", "suggestions": "1075"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 211, "column": 30, "nodeType": "1066", "messageId": "1067", "suggestions": "1076"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 227, "column": 38, "nodeType": "1066", "messageId": "1067", "suggestions": "1077"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 269, "column": 24, "nodeType": "1066", "messageId": "1067", "suggestions": "1078"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 311, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1079"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 22, "column": 15, "nodeType": "1082", "messageId": "1083", "endLine": 22, "endColumn": 32}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 48, "column": 13, "nodeType": "1082", "messageId": "1083", "endLine": 48, "endColumn": 30}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 52, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 52, "endColumn": 34}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 72, "column": 46, "nodeType": "1086", "messageId": "1087", "endLine": 72, "endColumn": 49, "suggestions": "1088"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 27, "column": 16, "nodeType": "1086", "messageId": "1087", "endLine": 27, "endColumn": 19, "suggestions": "1089"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 85, "column": 13, "nodeType": "1082", "messageId": "1083", "endLine": 85, "endColumn": 30}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 69, "column": 59, "nodeType": "1086", "messageId": "1087", "endLine": 69, "endColumn": 62, "suggestions": "1090"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 15, "column": 18, "nodeType": "1086", "messageId": "1087", "endLine": 15, "endColumn": 21, "suggestions": "1091"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 132, "column": 44, "nodeType": "1086", "messageId": "1087", "endLine": 132, "endColumn": 47, "suggestions": "1092"}, {"ruleId": "1093", "severity": 2, "message": "1094", "line": 153, "column": 11, "nodeType": "1095", "messageId": "1096", "endLine": 153, "endColumn": 27, "fix": "1097"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 166, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 166, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 194, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 194, "endColumn": 34}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 213, "column": 56, "nodeType": "1086", "messageId": "1087", "endLine": 213, "endColumn": 59, "suggestions": "1098"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 215, "column": 23, "nodeType": "1082", "messageId": "1083", "endLine": 215, "endColumn": 40}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 76, "column": 56, "nodeType": "1086", "messageId": "1087", "endLine": 76, "endColumn": 59, "suggestions": "1099"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 115, "column": 50, "nodeType": "1086", "messageId": "1087", "endLine": 115, "endColumn": 53, "suggestions": "1100"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 39, "column": 31, "nodeType": "1086", "messageId": "1087", "endLine": 39, "endColumn": 34, "suggestions": "1101"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 198, "column": 11, "nodeType": "1082", "messageId": "1083", "endLine": 198, "endColumn": 28}, {"ruleId": "1102", "severity": 1, "message": "1103", "line": 61, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 61, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1105", "line": 61, "column": 23, "nodeType": null, "messageId": "1104", "endLine": 61, "endColumn": 35}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 22, "column": 51, "nodeType": "1066", "messageId": "1067", "suggestions": "1106"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 61, "column": 25, "nodeType": "1066", "messageId": "1067", "suggestions": "1107"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 61, "column": 30, "nodeType": "1066", "messageId": "1067", "suggestions": "1108"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 62, "column": 53, "nodeType": "1066", "messageId": "1067", "suggestions": "1109"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 71, "column": 28, "nodeType": "1066", "messageId": "1067", "suggestions": "1110"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 76, "column": 25, "nodeType": "1066", "messageId": "1067", "suggestions": "1111"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 79, "column": 42, "nodeType": "1066", "messageId": "1067", "suggestions": "1112"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 96, "column": 34, "nodeType": "1066", "messageId": "1067", "suggestions": "1113"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 112, "column": 42, "nodeType": "1066", "messageId": "1067", "suggestions": "1114"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 156, "column": 44, "nodeType": "1066", "messageId": "1067", "suggestions": "1115"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 217, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1116"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 218, "column": 48, "nodeType": "1066", "messageId": "1067", "suggestions": "1117"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 225, "column": 39, "nodeType": "1066", "messageId": "1067", "suggestions": "1118"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 238, "column": 65, "nodeType": "1066", "messageId": "1067", "suggestions": "1119"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 239, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1120"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 239, "column": 65, "nodeType": "1066", "messageId": "1067", "suggestions": "1121"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 244, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1122"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 254, "column": 63, "nodeType": "1066", "messageId": "1067", "suggestions": "1123"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 256, "column": 47, "nodeType": "1066", "messageId": "1067", "suggestions": "1124"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 273, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1125"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 279, "column": 29, "nodeType": "1066", "messageId": "1067", "suggestions": "1126"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 23, "column": 58, "nodeType": "1086", "messageId": "1087", "endLine": 23, "endColumn": 61, "suggestions": "1127"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 24, "column": 58, "nodeType": "1086", "messageId": "1087", "endLine": 24, "endColumn": 61, "suggestions": "1128"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 55, "column": 41, "nodeType": "1086", "messageId": "1087", "endLine": 55, "endColumn": 44, "suggestions": "1129"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 124, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1130"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 141, "column": 19, "nodeType": "1133", "endLine": 150, "endColumn": 21}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 175, "column": 27, "nodeType": "1133", "endLine": 184, "endColumn": 29}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 254, "column": 19, "nodeType": "1133", "endLine": 261, "endColumn": 21}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 336, "column": 68, "nodeType": "1086", "messageId": "1087", "endLine": 336, "endColumn": 71, "suggestions": "1134"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 163, "column": 31, "nodeType": "1066", "messageId": "1067", "suggestions": "1135"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 239, "column": 27, "nodeType": "1066", "messageId": "1067", "suggestions": "1136"}, {"ruleId": "1137", "severity": 1, "message": "1138", "line": 94, "column": 9, "nodeType": "1095", "endLine": 94, "endColumn": 21}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 152, "column": 9, "nodeType": "1133", "endLine": 152, "endColumn": 75}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 80, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 80, "endColumn": 19}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 62, "column": 35, "nodeType": "1066", "messageId": "1067", "suggestions": "1140"}, {"ruleId": "1064", "severity": 1, "message": "1141", "line": 188, "column": 15, "nodeType": "1066", "messageId": "1067", "suggestions": "1142"}, {"ruleId": "1064", "severity": 1, "message": "1141", "line": 189, "column": 65, "nodeType": "1066", "messageId": "1067", "suggestions": "1143"}, {"ruleId": "1102", "severity": 1, "message": "1144", "line": 43, "column": 5, "nodeType": null, "messageId": "1104", "endLine": 43, "endColumn": 10}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 76, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 76, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1145", "line": 66, "column": 19, "nodeType": null, "messageId": "1104", "endLine": 66, "endColumn": 29}, {"ruleId": "1102", "severity": 1, "message": "1146", "line": 20, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 20, "endColumn": 12}, {"ruleId": "1102", "severity": 1, "message": "1147", "line": 24, "column": 17, "nodeType": null, "messageId": "1104", "endLine": 24, "endColumn": 26}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 354, "column": 31, "nodeType": "1066", "messageId": "1067", "suggestions": "1148"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 76, "column": 17, "nodeType": "1133", "endLine": 80, "endColumn": 19}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 90, "column": 17, "nodeType": "1133", "endLine": 94, "endColumn": 19}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 178, "column": 27, "nodeType": "1133", "endLine": 182, "endColumn": 29}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 59, "column": 31, "nodeType": "1066", "messageId": "1067", "suggestions": "1149"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 73, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1150"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 95, "column": 29, "nodeType": "1066", "messageId": "1067", "suggestions": "1151"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 102, "column": 29, "nodeType": "1066", "messageId": "1067", "suggestions": "1152"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 127, "column": 29, "nodeType": "1066", "messageId": "1067", "suggestions": "1153"}, {"ruleId": "1102", "severity": 1, "message": "1154", "line": 17, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 17, "endColumn": 20}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 19, "column": 23, "nodeType": "1082", "messageId": "1083", "endLine": 19, "endColumn": 42}, {"ruleId": "1102", "severity": 1, "message": "1155", "line": 24, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 24, "endColumn": 17}, {"ruleId": "1102", "severity": 1, "message": "1156", "line": 4, "column": 17, "nodeType": null, "messageId": "1104", "endLine": 4, "endColumn": 28}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 60, "column": 28, "nodeType": "1086", "messageId": "1087", "endLine": 60, "endColumn": 31, "suggestions": "1157"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 88, "column": 13, "nodeType": "1133", "endLine": 99, "endColumn": 15}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 90, "column": 37, "nodeType": "1086", "messageId": "1087", "endLine": 90, "endColumn": 40, "suggestions": "1158"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 28, "column": 36, "nodeType": "1086", "messageId": "1087", "endLine": 28, "endColumn": 39, "suggestions": "1159"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 28, "column": 44, "nodeType": "1086", "messageId": "1087", "endLine": 28, "endColumn": 47, "suggestions": "1160"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 33, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 33, "endColumn": 37, "suggestions": "1161"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 79, "column": 13, "nodeType": "1133", "endLine": 86, "endColumn": 15}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 199, "column": 36, "nodeType": "1086", "messageId": "1087", "endLine": 199, "endColumn": 39, "suggestions": "1162"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 199, "column": 48, "nodeType": "1086", "messageId": "1087", "endLine": 199, "endColumn": 51, "suggestions": "1163"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 206, "column": 32, "nodeType": "1086", "messageId": "1087", "endLine": 206, "endColumn": 35, "suggestions": "1164"}, {"ruleId": "1102", "severity": 1, "message": "1155", "line": 19, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 19, "endColumn": 19}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 104, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1165"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 215, "column": 39, "nodeType": "1066", "messageId": "1067", "suggestions": "1166"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 357, "column": 47, "nodeType": "1066", "messageId": "1067", "suggestions": "1167"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 195, "column": 19, "nodeType": "1133", "endLine": 203, "endColumn": 21}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 24, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 24, "endColumn": 37, "suggestions": "1168"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 34, "column": 37, "nodeType": "1086", "messageId": "1087", "endLine": 34, "endColumn": 40, "suggestions": "1169"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 157, "column": 21, "nodeType": "1133", "endLine": 164, "endColumn": 23}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 197, "column": 42, "nodeType": "1086", "messageId": "1087", "endLine": 197, "endColumn": 45, "suggestions": "1170"}, {"ruleId": "1102", "severity": 1, "message": "1171", "line": 144, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 144, "endColumn": 23}, {"ruleId": "1102", "severity": 1, "message": "1172", "line": 145, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 145, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1173", "line": 146, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 146, "endColumn": 27}, {"ruleId": "1102", "severity": 1, "message": "1174", "line": 147, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 147, "endColumn": 13}, {"ruleId": "1102", "severity": 1, "message": "1175", "line": 148, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 148, "endColumn": 18}, {"ruleId": "1102", "severity": 1, "message": "1176", "line": 149, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 149, "endColumn": 16}, {"ruleId": "1102", "severity": 1, "message": "1174", "line": 150, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 150, "endColumn": 13}, {"ruleId": "1102", "severity": 1, "message": "1175", "line": 151, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 151, "endColumn": 18}, {"ruleId": "1102", "severity": 1, "message": "1176", "line": 152, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 152, "endColumn": 16}, {"ruleId": "1102", "severity": 1, "message": "1171", "line": 153, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 153, "endColumn": 23}, {"ruleId": "1102", "severity": 1, "message": "1172", "line": 154, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 154, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1173", "line": 155, "column": 7, "nodeType": null, "messageId": "1104", "endLine": 155, "endColumn": 27}, {"ruleId": "1102", "severity": 1, "message": "1177", "line": 105, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 105, "endColumn": 27}, {"ruleId": "1102", "severity": 1, "message": "1178", "line": 106, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 106, "endColumn": 25}, {"ruleId": "1102", "severity": 1, "message": "1179", "line": 107, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 107, "endColumn": 31}, {"ruleId": "1102", "severity": 1, "message": "1180", "line": 108, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 108, "endColumn": 17}, {"ruleId": "1102", "severity": 1, "message": "1181", "line": 109, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 109, "endColumn": 22}, {"ruleId": "1102", "severity": 1, "message": "1182", "line": 110, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 110, "endColumn": 20}, {"ruleId": "1102", "severity": 1, "message": "1177", "line": 171, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 171, "endColumn": 31}, {"ruleId": "1102", "severity": 1, "message": "1178", "line": 172, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 172, "endColumn": 29}, {"ruleId": "1102", "severity": 1, "message": "1179", "line": 173, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 173, "endColumn": 35}, {"ruleId": "1102", "severity": 1, "message": "1180", "line": 174, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 174, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1181", "line": 175, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 175, "endColumn": 26}, {"ruleId": "1102", "severity": 1, "message": "1182", "line": 176, "column": 15, "nodeType": null, "messageId": "1104", "endLine": 176, "endColumn": 24}, {"ruleId": "1102", "severity": 1, "message": "1177", "line": 353, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 353, "endColumn": 29}, {"ruleId": "1102", "severity": 1, "message": "1178", "line": 354, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 354, "endColumn": 27}, {"ruleId": "1102", "severity": 1, "message": "1179", "line": 355, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 355, "endColumn": 33}, {"ruleId": "1102", "severity": 1, "message": "1180", "line": 356, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 356, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1181", "line": 357, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 357, "endColumn": 24}, {"ruleId": "1102", "severity": 1, "message": "1182", "line": 358, "column": 13, "nodeType": null, "messageId": "1104", "endLine": 358, "endColumn": 22}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 79, "column": 7, "nodeType": "1133", "endLine": 89, "endColumn": 9}, {"ruleId": "1064", "severity": 1, "message": "1141", "line": 91, "column": 9, "nodeType": "1066", "messageId": "1067", "suggestions": "1183"}, {"ruleId": "1064", "severity": 1, "message": "1141", "line": 91, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1184"}, {"ruleId": "1185", "severity": 1, "message": "1186", "line": 41, "column": 18, "nodeType": "1187", "endLine": 41, "endColumn": 33}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 45, "column": 23, "nodeType": "1082", "messageId": "1083", "endLine": 45, "endColumn": 40}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 80, "column": 26, "nodeType": "1086", "messageId": "1087", "endLine": 80, "endColumn": 29, "suggestions": "1188"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 81, "column": 60, "nodeType": "1086", "messageId": "1087", "endLine": 81, "endColumn": 63, "suggestions": "1189"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 56, "column": 37, "nodeType": "1086", "messageId": "1087", "endLine": 56, "endColumn": 40, "suggestions": "1190"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 40, "column": 58, "nodeType": "1086", "messageId": "1087", "endLine": 40, "endColumn": 61, "suggestions": "1191"}, {"ruleId": "1102", "severity": 1, "message": "1192", "line": 43, "column": 10, "nodeType": null, "messageId": "1104", "endLine": 43, "endColumn": 22}, {"ruleId": "1102", "severity": 1, "message": "1193", "line": 44, "column": 10, "nodeType": null, "messageId": "1104", "endLine": 44, "endColumn": 26}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 44, "column": 60, "nodeType": "1086", "messageId": "1087", "endLine": 44, "endColumn": 63, "suggestions": "1194"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 48, "column": 54, "nodeType": "1086", "messageId": "1087", "endLine": 48, "endColumn": 57, "suggestions": "1195"}, {"ruleId": "1185", "severity": 1, "message": "1196", "line": 53, "column": 6, "nodeType": "1197", "endLine": 53, "endColumn": 8, "suggestions": "1198"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 68, "column": 39, "nodeType": "1086", "messageId": "1087", "endLine": 68, "endColumn": 42, "suggestions": "1199"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 73, "column": 39, "nodeType": "1086", "messageId": "1087", "endLine": 73, "endColumn": 42, "suggestions": "1200"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 78, "column": 49, "nodeType": "1086", "messageId": "1087", "endLine": 78, "endColumn": 52, "suggestions": "1201"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 101, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 101, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1202", "line": 111, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 111, "endColumn": 26}, {"ruleId": "1102", "severity": 1, "message": "1203", "line": 128, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 128, "endColumn": 27}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 128, "column": 41, "nodeType": "1086", "messageId": "1087", "endLine": 128, "endColumn": 44, "suggestions": "1204"}, {"ruleId": "1102", "severity": 1, "message": "1205", "line": 133, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 133, "endColumn": 30}, {"ruleId": "1102", "severity": 1, "message": "1206", "line": 139, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 139, "endColumn": 24}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 139, "column": 35, "nodeType": "1086", "messageId": "1087", "endLine": 139, "endColumn": 38, "suggestions": "1207"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 161, "column": 33, "nodeType": "1086", "messageId": "1087", "endLine": 161, "endColumn": 36, "suggestions": "1208"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 166, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 166, "endColumn": 19}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 203, "column": 57, "nodeType": "1066", "messageId": "1067", "suggestions": "1209"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 215, "column": 35, "nodeType": "1066", "messageId": "1067", "suggestions": "1210"}, {"ruleId": "1211", "severity": 2, "message": "1212", "line": 217, "column": 19, "nodeType": "1133", "endLine": 220, "endColumn": 20}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 221, "column": 31, "nodeType": "1066", "messageId": "1067", "suggestions": "1213"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 231, "column": 36, "nodeType": "1066", "messageId": "1067", "suggestions": "1214"}, {"ruleId": "1102", "severity": 1, "message": "1215", "line": 253, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 253, "endColumn": 25}, {"ruleId": "1102", "severity": 1, "message": "1216", "line": 262, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 262, "endColumn": 14}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 30, "column": 13, "nodeType": "1082", "messageId": "1083", "endLine": 30, "endColumn": 30}, {"ruleId": "1102", "severity": 1, "message": "1217", "line": 65, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 65, "endColumn": 21}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 120, "column": 15, "nodeType": "1082", "messageId": "1083", "endLine": 120, "endColumn": 32}, {"ruleId": "1102", "severity": 1, "message": "1218", "line": 95, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 95, "endColumn": 21}, {"ruleId": "1185", "severity": 1, "message": "1219", "line": 139, "column": 6, "nodeType": "1197", "endLine": 139, "endColumn": 27, "suggestions": "1220"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 160, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 160, "endColumn": 19}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 513, "column": 27, "nodeType": "1066", "messageId": "1067", "suggestions": "1221"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 572, "column": 35, "nodeType": "1133", "endLine": 576, "endColumn": 37}, {"ruleId": "1102", "severity": 1, "message": "1222", "line": 3, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 3, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1223", "line": 11, "column": 28, "nodeType": null, "messageId": "1104", "endLine": 11, "endColumn": 35}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 51, "column": 22, "nodeType": "1086", "messageId": "1087", "endLine": 51, "endColumn": 25, "suggestions": "1224"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 14, "column": 10, "nodeType": "1086", "messageId": "1087", "endLine": 14, "endColumn": 13, "suggestions": "1225"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 37, "column": 17, "nodeType": "1086", "messageId": "1087", "endLine": 37, "endColumn": 20, "suggestions": "1226"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 49, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 49, "endColumn": 37, "suggestions": "1227"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 69, "column": 21, "nodeType": "1082", "messageId": "1083", "endLine": 69, "endColumn": 38}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 88, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 88, "endColumn": 37, "suggestions": "1228"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 105, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 105, "endColumn": 37, "suggestions": "1229"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 125, "column": 21, "nodeType": "1082", "messageId": "1083", "endLine": 125, "endColumn": 38}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 78, "column": 13, "nodeType": "1082", "messageId": "1083", "endLine": 78, "endColumn": 30}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 147, "column": 30, "nodeType": "1086", "messageId": "1087", "endLine": 147, "endColumn": 33, "suggestions": "1230"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 165, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 165, "endColumn": 34}, {"ruleId": "1102", "severity": 1, "message": "1223", "line": 6, "column": 28, "nodeType": null, "messageId": "1104", "endLine": 6, "endColumn": 35}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 24, "column": 15, "nodeType": "1082", "messageId": "1083", "endLine": 24, "endColumn": 32}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 40, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 40, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 53, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 53, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 66, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 66, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 85, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 85, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 92, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 92, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 99, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 99, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 111, "column": 15, "nodeType": "1082", "messageId": "1083", "endLine": 111, "endColumn": 32}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 127, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 127, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 137, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 137, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 153, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 153, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 160, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 160, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 167, "column": 17, "nodeType": "1082", "messageId": "1083", "endLine": 167, "endColumn": 34}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 182, "column": 13, "nodeType": "1082", "messageId": "1083", "endLine": 182, "endColumn": 30}, {"ruleId": "1102", "severity": 1, "message": "1231", "line": 10, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 10, "endColumn": 17}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 79, "column": 16, "nodeType": "1082", "messageId": "1083", "endLine": 79, "endColumn": 29}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 97, "column": 17, "nodeType": "1086", "messageId": "1087", "endLine": 97, "endColumn": 20, "suggestions": "1232"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 97, "column": 25, "nodeType": "1086", "messageId": "1087", "endLine": 97, "endColumn": 28, "suggestions": "1233"}, {"ruleId": "1080", "severity": 2, "message": "1081", "line": 75, "column": 16, "nodeType": "1082", "messageId": "1083", "endLine": 75, "endColumn": 29}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 11, "column": 87, "nodeType": "1086", "messageId": "1087", "endLine": 11, "endColumn": 90, "suggestions": "1234"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 47, "column": 21, "nodeType": "1086", "messageId": "1087", "endLine": 47, "endColumn": 24, "suggestions": "1235"}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 69, "column": 13, "nodeType": "1133", "endLine": 76, "endColumn": 15}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 89, "column": 20, "nodeType": "1066", "messageId": "1067", "suggestions": "1236"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 20, "column": 36, "nodeType": "1086", "messageId": "1087", "endLine": 20, "endColumn": 39, "suggestions": "1237"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 80, "column": 57, "nodeType": "1066", "messageId": "1067", "suggestions": "1238"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 123, "column": 47, "nodeType": "1066", "messageId": "1067", "suggestions": "1239"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 171, "column": 23, "nodeType": "1066", "messageId": "1067", "suggestions": "1240"}, {"ruleId": "1102", "severity": 1, "message": "1241", "line": 28, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 28, "endColumn": 15}, {"ruleId": "1102", "severity": 1, "message": "1242", "line": 63, "column": 16, "nodeType": null, "messageId": "1104", "endLine": 63, "endColumn": 19}, {"ruleId": "1185", "severity": 1, "message": "1243", "line": 71, "column": 6, "nodeType": "1197", "endLine": 71, "endColumn": 17, "suggestions": "1244"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 100, "column": 49, "nodeType": "1066", "messageId": "1067", "suggestions": "1245"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 7, "column": 40, "nodeType": "1086", "messageId": "1087", "endLine": 7, "endColumn": 43, "suggestions": "1246"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 73, "column": 17, "nodeType": "1066", "messageId": "1067", "suggestions": "1247"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 29, "column": 11, "nodeType": "1086", "messageId": "1087", "endLine": 29, "endColumn": 14, "suggestions": "1248"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 34, "column": 11, "nodeType": "1086", "messageId": "1087", "endLine": 34, "endColumn": 14, "suggestions": "1249"}, {"ruleId": "1102", "severity": 1, "message": "1242", "line": 66, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 66, "endColumn": 17}, {"ruleId": "1102", "severity": 1, "message": "1242", "line": 64, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 64, "endColumn": 17}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 256, "column": 23, "nodeType": "1133", "endLine": 263, "endColumn": 25}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 325, "column": 31, "nodeType": "1066", "messageId": "1067", "suggestions": "1250"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 336, "column": 39, "nodeType": "1066", "messageId": "1067", "suggestions": "1251"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 130, "column": 27, "nodeType": "1066", "messageId": "1067", "suggestions": "1252"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 167, "column": 39, "nodeType": "1066", "messageId": "1067", "suggestions": "1253"}, {"ruleId": "1102", "severity": 1, "message": "1254", "line": 27, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 27, "endColumn": 12}, {"ruleId": "1185", "severity": 1, "message": "1255", "line": 120, "column": 6, "nodeType": "1197", "endLine": 120, "endColumn": 8, "suggestions": "1256"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 170, "column": 35, "nodeType": "1066", "messageId": "1067", "suggestions": "1257"}, {"ruleId": "1102", "severity": 1, "message": "1258", "line": 79, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 79, "endColumn": 27}, {"ruleId": "1102", "severity": 1, "message": "1259", "line": 119, "column": 9, "nodeType": null, "messageId": "1104", "endLine": 119, "endColumn": 22}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 265, "column": 40, "nodeType": "1066", "messageId": "1067", "suggestions": "1260"}, {"ruleId": "1102", "severity": 1, "message": "1261", "line": 4, "column": 10, "nodeType": null, "messageId": "1104", "endLine": 4, "endColumn": 16}, {"ruleId": "1102", "severity": 1, "message": "1262", "line": 4, "column": 19, "nodeType": null, "messageId": "1104", "endLine": 4, "endColumn": 33}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 81, "column": 34, "nodeType": "1086", "messageId": "1087", "endLine": 81, "endColumn": 37, "suggestions": "1263"}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 183, "column": 45, "nodeType": "1066", "messageId": "1067", "suggestions": "1264"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 41, "column": 29, "nodeType": "1086", "messageId": "1087", "endLine": 41, "endColumn": 32, "suggestions": "1265"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 228, "column": 38, "nodeType": null, "messageId": "1104", "endLine": 228, "endColumn": 43}, {"ruleId": "1131", "severity": 1, "message": "1132", "line": 241, "column": 25, "nodeType": "1133", "endLine": 245, "endColumn": 27}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 37, "column": 21, "nodeType": "1086", "messageId": "1087", "endLine": 37, "endColumn": 24, "suggestions": "1266"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 66, "column": 29, "nodeType": "1086", "messageId": "1087", "endLine": 66, "endColumn": 32, "suggestions": "1267"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 108, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 108, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 132, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 132, "endColumn": 19}, {"ruleId": "1064", "severity": 1, "message": "1065", "line": 326, "column": 41, "nodeType": "1066", "messageId": "1067", "suggestions": "1268"}, {"ruleId": "1102", "severity": 1, "message": "1262", "line": 4, "column": 19, "nodeType": null, "messageId": "1104", "endLine": 4, "endColumn": 33}, {"ruleId": "1102", "severity": 1, "message": "1242", "line": 36, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 36, "endColumn": 17}, {"ruleId": "1102", "severity": 1, "message": "1242", "line": 58, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 58, "endColumn": 17}, {"ruleId": "1102", "severity": 1, "message": "1269", "line": 9, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 9, "endColumn": 7}, {"ruleId": "1102", "severity": 1, "message": "1270", "line": 9, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 9, "endColumn": 7}, {"ruleId": "1102", "severity": 1, "message": "1271", "line": 10, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 10, "endColumn": 8}, {"ruleId": "1102", "severity": 1, "message": "1272", "line": 11, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 11, "endColumn": 9}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 44, "column": 52, "nodeType": "1086", "messageId": "1087", "endLine": 44, "endColumn": 55, "suggestions": "1273"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 53, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 53, "endColumn": 19}, {"ruleId": "1102", "severity": 1, "message": "1274", "line": 9, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 9, "endColumn": 14}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 20, "column": 12, "nodeType": "1086", "messageId": "1087", "endLine": 20, "endColumn": 15, "suggestions": "1275"}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 97, "column": 18, "nodeType": null, "messageId": "1104", "endLine": 97, "endColumn": 23}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 152, "column": 16, "nodeType": null, "messageId": "1104", "endLine": 152, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 185, "column": 16, "nodeType": null, "messageId": "1104", "endLine": 185, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 225, "column": 16, "nodeType": null, "messageId": "1104", "endLine": 225, "endColumn": 21}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 298, "column": 14, "nodeType": null, "messageId": "1104", "endLine": 298, "endColumn": 19}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 12, "column": 17, "nodeType": "1086", "messageId": "1087", "endLine": 12, "endColumn": 20, "suggestions": "1276"}, {"ruleId": "1102", "severity": 1, "message": "1146", "line": 16, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 16, "endColumn": 12}, {"ruleId": "1102", "severity": 1, "message": "1147", "line": 22, "column": 17, "nodeType": null, "messageId": "1104", "endLine": 22, "endColumn": 26}, {"ruleId": "1102", "severity": 1, "message": "1277", "line": 24, "column": 11, "nodeType": null, "messageId": "1104", "endLine": 24, "endColumn": 26}, {"ruleId": "1102", "severity": 1, "message": "1139", "line": 20, "column": 12, "nodeType": null, "messageId": "1104", "endLine": 20, "endColumn": 17}, {"ruleId": "1278", "severity": 2, "message": "1279", "line": 52, "column": 29, "nodeType": "1095", "messageId": "1280", "endLine": 52, "endColumn": 37}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 53, "column": 59, "nodeType": "1086", "messageId": "1087", "endLine": 53, "endColumn": 62, "suggestions": "1281"}, {"ruleId": "1084", "severity": 1, "message": "1085", "line": 71, "column": 19, "nodeType": "1086", "messageId": "1087", "endLine": 71, "endColumn": 22, "suggestions": "1282"}, {"ruleId": "1102", "severity": 1, "message": "1283", "line": 146, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 146, "endColumn": 15}, {"ruleId": "1102", "severity": 1, "message": "1284", "line": 147, "column": 3, "nodeType": null, "messageId": "1104", "endLine": 147, "endColumn": 11}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1285", "1286", "1287", "1288"], ["1289", "1290", "1291", "1292"], ["1293", "1294", "1295", "1296"], ["1297", "1298", "1299", "1300"], ["1301", "1302", "1303", "1304"], ["1305", "1306", "1307", "1308"], ["1309", "1310", "1311", "1312"], ["1313", "1314", "1315", "1316"], ["1317", "1318", "1319", "1320"], ["1321", "1322", "1323", "1324"], ["1325", "1326", "1327", "1328"], ["1329", "1330", "1331", "1332"], "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1333", "1334"], ["1335", "1336"], ["1337", "1338"], ["1339", "1340"], ["1341", "1342"], "prefer-const", "'existingCustomer' is never reassigned. Use 'const' instead.", "Identifier", "useConst", {"range": "1343", "text": "1344"}, ["1345", "1346"], ["1347", "1348"], ["1349", "1350"], ["1351", "1352"], "@typescript-eslint/no-unused-vars", "'variants' is assigned a value but never used.", "unusedVar", "'pricingTiers' is assigned a value but never used.", ["1353", "1354", "1355", "1356"], ["1357", "1358", "1359", "1360"], ["1361", "1362", "1363", "1364"], ["1365", "1366", "1367", "1368"], ["1369", "1370", "1371", "1372"], ["1373", "1374", "1375", "1376"], ["1377", "1378", "1379", "1380"], ["1381", "1382", "1383", "1384"], ["1385", "1386", "1387", "1388"], ["1389", "1390", "1391", "1392"], ["1393", "1394", "1395", "1396"], ["1397", "1398", "1399", "1400"], ["1401", "1402", "1403", "1404"], ["1405", "1406", "1407", "1408"], ["1409", "1410", "1411", "1412"], ["1413", "1414", "1415", "1416"], ["1417", "1418", "1419", "1420"], ["1421", "1422", "1423", "1424"], ["1425", "1426", "1427", "1428"], ["1429", "1430", "1431", "1432"], ["1433", "1434", "1435", "1436"], ["1437", "1438"], ["1439", "1440"], ["1441", "1442"], ["1443", "1444", "1445", "1446"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1447", "1448"], ["1449", "1450", "1451", "1452"], ["1453", "1454", "1455", "1456"], "react-hooks/rules-of-hooks", "React Hook \"useTransform\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", "'error' is defined but never used.", ["1457", "1458", "1459", "1460"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1461", "1462", "1463", "1464"], ["1465", "1466", "1467", "1468"], "'watch' is assigned a value but never used.", "'isUpcoming' is assigned a value but never used.", "'onSuccess' is defined but never used.", "'clearCart' is assigned a value but never used.", ["1469", "1470", "1471", "1472"], ["1473", "1474", "1475", "1476"], ["1477", "1478", "1479", "1480"], ["1481", "1482", "1483", "1484"], ["1485", "1486", "1487", "1488"], ["1489", "1490", "1491", "1492"], "'openModal' is assigned a value but never used.", "'customer' is assigned a value but never used.", "'ModalHeader' is defined but never used.", ["1493", "1494"], ["1495", "1496"], ["1497", "1498"], ["1499", "1500"], ["1501", "1502"], ["1503", "1504"], ["1505", "1506"], ["1507", "1508"], ["1509", "1510", "1511", "1512"], ["1513", "1514", "1515", "1516"], ["1517", "1518", "1519", "1520"], ["1521", "1522"], ["1523", "1524"], ["1525", "1526"], "'onAnimationStart' is assigned a value but never used.", "'onAnimationEnd' is assigned a value but never used.", "'onAnimationIteration' is assigned a value but never used.", "'onDrag' is assigned a value but never used.", "'onDragStart' is assigned a value but never used.", "'onDragEnd' is assigned a value but never used.", "'onAnimationStart' is defined but never used.", "'onAnimationEnd' is defined but never used.", "'onAnimationIteration' is defined but never used.", "'onDrag' is defined but never used.", "'onDragStart' is defined but never used.", "'onDragEnd' is defined but never used.", ["1527", "1528", "1529", "1530"], ["1531", "1532", "1533", "1534"], "react-hooks/exhaustive-deps", "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", ["1535", "1536"], ["1537", "1538"], ["1539", "1540"], ["1541", "1542"], "'customerView' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", ["1543", "1544"], ["1545", "1546"], "React Hook useEffect has a missing dependency: 'checkAuth'. Either include it or remove the dependency array.", "ArrayExpression", ["1547"], ["1548", "1549"], ["1550", "1551"], ["1552", "1553"], "'handleQuickAction' is assigned a value but never used.", "'handleViewCustomer' is assigned a value but never used.", ["1554", "1555"], "'handleBackToCustomers' is assigned a value but never used.", "'handleViewOrder' is assigned a value but never used.", ["1556", "1557"], ["1558", "1559"], ["1560", "1561", "1562", "1563"], ["1564", "1565", "1566", "1567"], "@next/next/no-html-link-for-pages", "Do not use an `<a>` element to navigate to `/`. Use `<Link />` from `next/link` instead. See: https://nextjs.org/docs/messages/no-html-link-for-pages", ["1568", "1569", "1570", "1571"], ["1572", "1573", "1574", "1575"], "'staggerContainer' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'dbError' is defined but never used.", "'handleLogout' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["1576"], ["1577", "1578", "1579", "1580"], "'AuthUser' is defined but never used.", "'request' is defined but never used.", ["1581", "1582"], ["1583", "1584"], ["1585", "1586"], ["1587", "1588"], ["1589", "1590"], ["1591", "1592"], ["1593", "1594"], "'result' is assigned a value but never used.", ["1595", "1596"], ["1597", "1598"], ["1599", "1600"], ["1601", "1602"], ["1603", "1604", "1605", "1606"], ["1607", "1608"], ["1609", "1610", "1611", "1612"], ["1613", "1614", "1615", "1616"], ["1617", "1618", "1619", "1620"], "'router' is assigned a value but never used.", "'err' is defined but never used.", "React Hook useEffect has a missing dependency: 'clearCart'. Either include it or remove the dependency array.", ["1621"], ["1622", "1623", "1624", "1625"], ["1626", "1627"], ["1628", "1629", "1630", "1631"], ["1632", "1633"], ["1634", "1635"], ["1636", "1637", "1638", "1639"], ["1640", "1641", "1642", "1643"], ["1644", "1645", "1646", "1647"], ["1648", "1649", "1650", "1651"], "'maxImages' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleFileUpload'. Either include it or remove the dependency array.", ["1652"], ["1653", "1654", "1655", "1656"], "'handleStatusChange' is assigned a value but never used.", "'statusOptions' is assigned a value but never used.", ["1657", "1658", "1659", "1660"], "'motion' is defined but never used.", "'ProductVariant' is defined but never used.", ["1661", "1662"], ["1663", "1664", "1665", "1666"], ["1667", "1668"], ["1669", "1670"], ["1671", "1672"], ["1673", "1674", "1675", "1676"], "'Star' is defined but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'Shield' is defined but never used.", ["1677", "1678"], "'ShoppingBag' is defined but never used.", ["1679", "1680"], ["1681", "1682"], "'addNotification' is assigned a value but never used.", "@typescript-eslint/no-unsafe-function-type", "The `Function` type accepts any function-like value.\nPrefer explicitly defining any function parameters and return type.", "bannedFunctionType", ["1683", "1684"], ["1685", "1686"], "'requiredRole' is defined but never used.", "'fallback' is defined but never used.", {"messageId": "1687", "data": "1688", "fix": "1689", "desc": "1690"}, {"messageId": "1687", "data": "1691", "fix": "1692", "desc": "1693"}, {"messageId": "1687", "data": "1694", "fix": "1695", "desc": "1696"}, {"messageId": "1687", "data": "1697", "fix": "1698", "desc": "1699"}, {"messageId": "1687", "data": "1700", "fix": "1701", "desc": "1690"}, {"messageId": "1687", "data": "1702", "fix": "1703", "desc": "1693"}, {"messageId": "1687", "data": "1704", "fix": "1705", "desc": "1696"}, {"messageId": "1687", "data": "1706", "fix": "1707", "desc": "1699"}, {"messageId": "1687", "data": "1708", "fix": "1709", "desc": "1690"}, {"messageId": "1687", "data": "1710", "fix": "1711", "desc": "1693"}, {"messageId": "1687", "data": "1712", "fix": "1713", "desc": "1696"}, {"messageId": "1687", "data": "1714", "fix": "1715", "desc": "1699"}, {"messageId": "1687", "data": "1716", "fix": "1717", "desc": "1690"}, {"messageId": "1687", "data": "1718", "fix": "1719", "desc": "1693"}, {"messageId": "1687", "data": "1720", "fix": "1721", "desc": "1696"}, {"messageId": "1687", "data": "1722", "fix": "1723", "desc": "1699"}, {"messageId": "1687", "data": "1724", "fix": "1725", "desc": "1690"}, {"messageId": "1687", "data": "1726", "fix": "1727", "desc": "1693"}, {"messageId": "1687", "data": "1728", "fix": "1729", "desc": "1696"}, {"messageId": "1687", "data": "1730", "fix": "1731", "desc": "1699"}, {"messageId": "1687", "data": "1732", "fix": "1733", "desc": "1690"}, {"messageId": "1687", "data": "1734", "fix": "1735", "desc": "1693"}, {"messageId": "1687", "data": "1736", "fix": "1737", "desc": "1696"}, {"messageId": "1687", "data": "1738", "fix": "1739", "desc": "1699"}, {"messageId": "1687", "data": "1740", "fix": "1741", "desc": "1690"}, {"messageId": "1687", "data": "1742", "fix": "1743", "desc": "1693"}, {"messageId": "1687", "data": "1744", "fix": "1745", "desc": "1696"}, {"messageId": "1687", "data": "1746", "fix": "1747", "desc": "1699"}, {"messageId": "1687", "data": "1748", "fix": "1749", "desc": "1690"}, {"messageId": "1687", "data": "1750", "fix": "1751", "desc": "1693"}, {"messageId": "1687", "data": "1752", "fix": "1753", "desc": "1696"}, {"messageId": "1687", "data": "1754", "fix": "1755", "desc": "1699"}, {"messageId": "1687", "data": "1756", "fix": "1757", "desc": "1690"}, {"messageId": "1687", "data": "1758", "fix": "1759", "desc": "1693"}, {"messageId": "1687", "data": "1760", "fix": "1761", "desc": "1696"}, {"messageId": "1687", "data": "1762", "fix": "1763", "desc": "1699"}, {"messageId": "1687", "data": "1764", "fix": "1765", "desc": "1690"}, {"messageId": "1687", "data": "1766", "fix": "1767", "desc": "1693"}, {"messageId": "1687", "data": "1768", "fix": "1769", "desc": "1696"}, {"messageId": "1687", "data": "1770", "fix": "1771", "desc": "1699"}, {"messageId": "1687", "data": "1772", "fix": "1773", "desc": "1690"}, {"messageId": "1687", "data": "1774", "fix": "1775", "desc": "1693"}, {"messageId": "1687", "data": "1776", "fix": "1777", "desc": "1696"}, {"messageId": "1687", "data": "1778", "fix": "1779", "desc": "1699"}, {"messageId": "1687", "data": "1780", "fix": "1781", "desc": "1690"}, {"messageId": "1687", "data": "1782", "fix": "1783", "desc": "1693"}, {"messageId": "1687", "data": "1784", "fix": "1785", "desc": "1696"}, {"messageId": "1687", "data": "1786", "fix": "1787", "desc": "1699"}, {"messageId": "1788", "fix": "1789", "desc": "1790"}, {"messageId": "1791", "fix": "1792", "desc": "1793"}, {"messageId": "1788", "fix": "1794", "desc": "1790"}, {"messageId": "1791", "fix": "1795", "desc": "1793"}, {"messageId": "1788", "fix": "1796", "desc": "1790"}, {"messageId": "1791", "fix": "1797", "desc": "1793"}, {"messageId": "1788", "fix": "1798", "desc": "1790"}, {"messageId": "1791", "fix": "1799", "desc": "1793"}, {"messageId": "1788", "fix": "1800", "desc": "1790"}, {"messageId": "1791", "fix": "1801", "desc": "1793"}, [4424, 4517], "const existingCustomer = await prisma.customer.findUnique({\n        where: { email },\n      });", {"messageId": "1788", "fix": "1802", "desc": "1790"}, {"messageId": "1791", "fix": "1803", "desc": "1793"}, {"messageId": "1788", "fix": "1804", "desc": "1790"}, {"messageId": "1791", "fix": "1805", "desc": "1793"}, {"messageId": "1788", "fix": "1806", "desc": "1790"}, {"messageId": "1791", "fix": "1807", "desc": "1793"}, {"messageId": "1788", "fix": "1808", "desc": "1790"}, {"messageId": "1791", "fix": "1809", "desc": "1793"}, {"messageId": "1687", "data": "1810", "fix": "1811", "desc": "1690"}, {"messageId": "1687", "data": "1812", "fix": "1813", "desc": "1693"}, {"messageId": "1687", "data": "1814", "fix": "1815", "desc": "1696"}, {"messageId": "1687", "data": "1816", "fix": "1817", "desc": "1699"}, {"messageId": "1687", "data": "1818", "fix": "1819", "desc": "1690"}, {"messageId": "1687", "data": "1820", "fix": "1821", "desc": "1693"}, {"messageId": "1687", "data": "1822", "fix": "1823", "desc": "1696"}, {"messageId": "1687", "data": "1824", "fix": "1825", "desc": "1699"}, {"messageId": "1687", "data": "1826", "fix": "1827", "desc": "1690"}, {"messageId": "1687", "data": "1828", "fix": "1829", "desc": "1693"}, {"messageId": "1687", "data": "1830", "fix": "1831", "desc": "1696"}, {"messageId": "1687", "data": "1832", "fix": "1833", "desc": "1699"}, {"messageId": "1687", "data": "1834", "fix": "1835", "desc": "1690"}, {"messageId": "1687", "data": "1836", "fix": "1837", "desc": "1693"}, {"messageId": "1687", "data": "1838", "fix": "1839", "desc": "1696"}, {"messageId": "1687", "data": "1840", "fix": "1841", "desc": "1699"}, {"messageId": "1687", "data": "1842", "fix": "1843", "desc": "1690"}, {"messageId": "1687", "data": "1844", "fix": "1845", "desc": "1693"}, {"messageId": "1687", "data": "1846", "fix": "1847", "desc": "1696"}, {"messageId": "1687", "data": "1848", "fix": "1849", "desc": "1699"}, {"messageId": "1687", "data": "1850", "fix": "1851", "desc": "1690"}, {"messageId": "1687", "data": "1852", "fix": "1853", "desc": "1693"}, {"messageId": "1687", "data": "1854", "fix": "1855", "desc": "1696"}, {"messageId": "1687", "data": "1856", "fix": "1857", "desc": "1699"}, {"messageId": "1687", "data": "1858", "fix": "1859", "desc": "1690"}, {"messageId": "1687", "data": "1860", "fix": "1861", "desc": "1693"}, {"messageId": "1687", "data": "1862", "fix": "1863", "desc": "1696"}, {"messageId": "1687", "data": "1864", "fix": "1865", "desc": "1699"}, {"messageId": "1687", "data": "1866", "fix": "1867", "desc": "1690"}, {"messageId": "1687", "data": "1868", "fix": "1869", "desc": "1693"}, {"messageId": "1687", "data": "1870", "fix": "1871", "desc": "1696"}, {"messageId": "1687", "data": "1872", "fix": "1873", "desc": "1699"}, {"messageId": "1687", "data": "1874", "fix": "1875", "desc": "1690"}, {"messageId": "1687", "data": "1876", "fix": "1877", "desc": "1693"}, {"messageId": "1687", "data": "1878", "fix": "1879", "desc": "1696"}, {"messageId": "1687", "data": "1880", "fix": "1881", "desc": "1699"}, {"messageId": "1687", "data": "1882", "fix": "1883", "desc": "1690"}, {"messageId": "1687", "data": "1884", "fix": "1885", "desc": "1693"}, {"messageId": "1687", "data": "1886", "fix": "1887", "desc": "1696"}, {"messageId": "1687", "data": "1888", "fix": "1889", "desc": "1699"}, {"messageId": "1687", "data": "1890", "fix": "1891", "desc": "1690"}, {"messageId": "1687", "data": "1892", "fix": "1893", "desc": "1693"}, {"messageId": "1687", "data": "1894", "fix": "1895", "desc": "1696"}, {"messageId": "1687", "data": "1896", "fix": "1897", "desc": "1699"}, {"messageId": "1687", "data": "1898", "fix": "1899", "desc": "1690"}, {"messageId": "1687", "data": "1900", "fix": "1901", "desc": "1693"}, {"messageId": "1687", "data": "1902", "fix": "1903", "desc": "1696"}, {"messageId": "1687", "data": "1904", "fix": "1905", "desc": "1699"}, {"messageId": "1687", "data": "1906", "fix": "1907", "desc": "1690"}, {"messageId": "1687", "data": "1908", "fix": "1909", "desc": "1693"}, {"messageId": "1687", "data": "1910", "fix": "1911", "desc": "1696"}, {"messageId": "1687", "data": "1912", "fix": "1913", "desc": "1699"}, {"messageId": "1687", "data": "1914", "fix": "1915", "desc": "1690"}, {"messageId": "1687", "data": "1916", "fix": "1917", "desc": "1693"}, {"messageId": "1687", "data": "1918", "fix": "1919", "desc": "1696"}, {"messageId": "1687", "data": "1920", "fix": "1921", "desc": "1699"}, {"messageId": "1687", "data": "1922", "fix": "1923", "desc": "1690"}, {"messageId": "1687", "data": "1924", "fix": "1925", "desc": "1693"}, {"messageId": "1687", "data": "1926", "fix": "1927", "desc": "1696"}, {"messageId": "1687", "data": "1928", "fix": "1929", "desc": "1699"}, {"messageId": "1687", "data": "1930", "fix": "1931", "desc": "1690"}, {"messageId": "1687", "data": "1932", "fix": "1933", "desc": "1693"}, {"messageId": "1687", "data": "1934", "fix": "1935", "desc": "1696"}, {"messageId": "1687", "data": "1936", "fix": "1937", "desc": "1699"}, {"messageId": "1687", "data": "1938", "fix": "1939", "desc": "1690"}, {"messageId": "1687", "data": "1940", "fix": "1941", "desc": "1693"}, {"messageId": "1687", "data": "1942", "fix": "1943", "desc": "1696"}, {"messageId": "1687", "data": "1944", "fix": "1945", "desc": "1699"}, {"messageId": "1687", "data": "1946", "fix": "1947", "desc": "1690"}, {"messageId": "1687", "data": "1948", "fix": "1949", "desc": "1693"}, {"messageId": "1687", "data": "1950", "fix": "1951", "desc": "1696"}, {"messageId": "1687", "data": "1952", "fix": "1953", "desc": "1699"}, {"messageId": "1687", "data": "1954", "fix": "1955", "desc": "1690"}, {"messageId": "1687", "data": "1956", "fix": "1957", "desc": "1693"}, {"messageId": "1687", "data": "1958", "fix": "1959", "desc": "1696"}, {"messageId": "1687", "data": "1960", "fix": "1961", "desc": "1699"}, {"messageId": "1687", "data": "1962", "fix": "1963", "desc": "1690"}, {"messageId": "1687", "data": "1964", "fix": "1965", "desc": "1693"}, {"messageId": "1687", "data": "1966", "fix": "1967", "desc": "1696"}, {"messageId": "1687", "data": "1968", "fix": "1969", "desc": "1699"}, {"messageId": "1687", "data": "1970", "fix": "1971", "desc": "1690"}, {"messageId": "1687", "data": "1972", "fix": "1973", "desc": "1693"}, {"messageId": "1687", "data": "1974", "fix": "1975", "desc": "1696"}, {"messageId": "1687", "data": "1976", "fix": "1977", "desc": "1699"}, {"messageId": "1788", "fix": "1978", "desc": "1790"}, {"messageId": "1791", "fix": "1979", "desc": "1793"}, {"messageId": "1788", "fix": "1980", "desc": "1790"}, {"messageId": "1791", "fix": "1981", "desc": "1793"}, {"messageId": "1788", "fix": "1982", "desc": "1790"}, {"messageId": "1791", "fix": "1983", "desc": "1793"}, {"messageId": "1687", "data": "1984", "fix": "1985", "desc": "1690"}, {"messageId": "1687", "data": "1986", "fix": "1987", "desc": "1693"}, {"messageId": "1687", "data": "1988", "fix": "1989", "desc": "1696"}, {"messageId": "1687", "data": "1990", "fix": "1991", "desc": "1699"}, {"messageId": "1788", "fix": "1992", "desc": "1790"}, {"messageId": "1791", "fix": "1993", "desc": "1793"}, {"messageId": "1687", "data": "1994", "fix": "1995", "desc": "1690"}, {"messageId": "1687", "data": "1996", "fix": "1997", "desc": "1693"}, {"messageId": "1687", "data": "1998", "fix": "1999", "desc": "1696"}, {"messageId": "1687", "data": "2000", "fix": "2001", "desc": "1699"}, {"messageId": "1687", "data": "2002", "fix": "2003", "desc": "1690"}, {"messageId": "1687", "data": "2004", "fix": "2005", "desc": "1693"}, {"messageId": "1687", "data": "2006", "fix": "2007", "desc": "1696"}, {"messageId": "1687", "data": "2008", "fix": "2009", "desc": "1699"}, {"messageId": "1687", "data": "2010", "fix": "2011", "desc": "1690"}, {"messageId": "1687", "data": "2012", "fix": "2013", "desc": "1693"}, {"messageId": "1687", "data": "2014", "fix": "2015", "desc": "1696"}, {"messageId": "1687", "data": "2016", "fix": "2017", "desc": "1699"}, {"messageId": "1687", "data": "2018", "fix": "2019", "desc": "2020"}, {"messageId": "1687", "data": "2021", "fix": "2022", "desc": "2023"}, {"messageId": "1687", "data": "2024", "fix": "2025", "desc": "2026"}, {"messageId": "1687", "data": "2027", "fix": "2028", "desc": "2029"}, {"messageId": "1687", "data": "2030", "fix": "2031", "desc": "2020"}, {"messageId": "1687", "data": "2032", "fix": "2033", "desc": "2023"}, {"messageId": "1687", "data": "2034", "fix": "2035", "desc": "2026"}, {"messageId": "1687", "data": "2036", "fix": "2037", "desc": "2029"}, {"messageId": "1687", "data": "2038", "fix": "2039", "desc": "1690"}, {"messageId": "1687", "data": "2040", "fix": "2041", "desc": "1693"}, {"messageId": "1687", "data": "2042", "fix": "2043", "desc": "1696"}, {"messageId": "1687", "data": "2044", "fix": "2045", "desc": "1699"}, {"messageId": "1687", "data": "2046", "fix": "2047", "desc": "1690"}, {"messageId": "1687", "data": "2048", "fix": "2049", "desc": "1693"}, {"messageId": "1687", "data": "2050", "fix": "2051", "desc": "1696"}, {"messageId": "1687", "data": "2052", "fix": "2053", "desc": "1699"}, {"messageId": "1687", "data": "2054", "fix": "2055", "desc": "1690"}, {"messageId": "1687", "data": "2056", "fix": "2057", "desc": "1693"}, {"messageId": "1687", "data": "2058", "fix": "2059", "desc": "1696"}, {"messageId": "1687", "data": "2060", "fix": "2061", "desc": "1699"}, {"messageId": "1687", "data": "2062", "fix": "2063", "desc": "1690"}, {"messageId": "1687", "data": "2064", "fix": "2065", "desc": "1693"}, {"messageId": "1687", "data": "2066", "fix": "2067", "desc": "1696"}, {"messageId": "1687", "data": "2068", "fix": "2069", "desc": "1699"}, {"messageId": "1687", "data": "2070", "fix": "2071", "desc": "1690"}, {"messageId": "1687", "data": "2072", "fix": "2073", "desc": "1693"}, {"messageId": "1687", "data": "2074", "fix": "2075", "desc": "1696"}, {"messageId": "1687", "data": "2076", "fix": "2077", "desc": "1699"}, {"messageId": "1687", "data": "2078", "fix": "2079", "desc": "1690"}, {"messageId": "1687", "data": "2080", "fix": "2081", "desc": "1693"}, {"messageId": "1687", "data": "2082", "fix": "2083", "desc": "1696"}, {"messageId": "1687", "data": "2084", "fix": "2085", "desc": "1699"}, {"messageId": "1788", "fix": "2086", "desc": "1790"}, {"messageId": "1791", "fix": "2087", "desc": "1793"}, {"messageId": "1788", "fix": "2088", "desc": "1790"}, {"messageId": "1791", "fix": "2089", "desc": "1793"}, {"messageId": "1788", "fix": "2090", "desc": "1790"}, {"messageId": "1791", "fix": "2091", "desc": "1793"}, {"messageId": "1788", "fix": "2092", "desc": "1790"}, {"messageId": "1791", "fix": "2093", "desc": "1793"}, {"messageId": "1788", "fix": "2094", "desc": "1790"}, {"messageId": "1791", "fix": "2095", "desc": "1793"}, {"messageId": "1788", "fix": "2096", "desc": "1790"}, {"messageId": "1791", "fix": "2097", "desc": "1793"}, {"messageId": "1788", "fix": "2098", "desc": "1790"}, {"messageId": "1791", "fix": "2099", "desc": "1793"}, {"messageId": "1788", "fix": "2100", "desc": "1790"}, {"messageId": "1791", "fix": "2101", "desc": "1793"}, {"messageId": "1687", "data": "2102", "fix": "2103", "desc": "1690"}, {"messageId": "1687", "data": "2104", "fix": "2105", "desc": "1693"}, {"messageId": "1687", "data": "2106", "fix": "2107", "desc": "1696"}, {"messageId": "1687", "data": "2108", "fix": "2109", "desc": "1699"}, {"messageId": "1687", "data": "2110", "fix": "2111", "desc": "1690"}, {"messageId": "1687", "data": "2112", "fix": "2113", "desc": "1693"}, {"messageId": "1687", "data": "2114", "fix": "2115", "desc": "1696"}, {"messageId": "1687", "data": "2116", "fix": "2117", "desc": "1699"}, {"messageId": "1687", "data": "2118", "fix": "2119", "desc": "1690"}, {"messageId": "1687", "data": "2120", "fix": "2121", "desc": "1693"}, {"messageId": "1687", "data": "2122", "fix": "2123", "desc": "1696"}, {"messageId": "1687", "data": "2124", "fix": "2125", "desc": "1699"}, {"messageId": "1788", "fix": "2126", "desc": "1790"}, {"messageId": "1791", "fix": "2127", "desc": "1793"}, {"messageId": "1788", "fix": "2128", "desc": "1790"}, {"messageId": "1791", "fix": "2129", "desc": "1793"}, {"messageId": "1788", "fix": "2130", "desc": "1790"}, {"messageId": "1791", "fix": "2131", "desc": "1793"}, {"messageId": "1687", "data": "2132", "fix": "2133", "desc": "2020"}, {"messageId": "1687", "data": "2134", "fix": "2135", "desc": "2023"}, {"messageId": "1687", "data": "2136", "fix": "2137", "desc": "2026"}, {"messageId": "1687", "data": "2138", "fix": "2139", "desc": "2029"}, {"messageId": "1687", "data": "2140", "fix": "2141", "desc": "2020"}, {"messageId": "1687", "data": "2142", "fix": "2143", "desc": "2023"}, {"messageId": "1687", "data": "2144", "fix": "2145", "desc": "2026"}, {"messageId": "1687", "data": "2146", "fix": "2147", "desc": "2029"}, {"messageId": "1788", "fix": "2148", "desc": "1790"}, {"messageId": "1791", "fix": "2149", "desc": "1793"}, {"messageId": "1788", "fix": "2150", "desc": "1790"}, {"messageId": "1791", "fix": "2151", "desc": "1793"}, {"messageId": "1788", "fix": "2152", "desc": "1790"}, {"messageId": "1791", "fix": "2153", "desc": "1793"}, {"messageId": "1788", "fix": "2154", "desc": "1790"}, {"messageId": "1791", "fix": "2155", "desc": "1793"}, {"messageId": "1788", "fix": "2156", "desc": "1790"}, {"messageId": "1791", "fix": "2157", "desc": "1793"}, {"messageId": "1788", "fix": "2158", "desc": "1790"}, {"messageId": "1791", "fix": "2159", "desc": "1793"}, {"desc": "2160", "fix": "2161"}, {"messageId": "1788", "fix": "2162", "desc": "1790"}, {"messageId": "1791", "fix": "2163", "desc": "1793"}, {"messageId": "1788", "fix": "2164", "desc": "1790"}, {"messageId": "1791", "fix": "2165", "desc": "1793"}, {"messageId": "1788", "fix": "2166", "desc": "1790"}, {"messageId": "1791", "fix": "2167", "desc": "1793"}, {"messageId": "1788", "fix": "2168", "desc": "1790"}, {"messageId": "1791", "fix": "2169", "desc": "1793"}, {"messageId": "1788", "fix": "2170", "desc": "1790"}, {"messageId": "1791", "fix": "2171", "desc": "1793"}, {"messageId": "1788", "fix": "2172", "desc": "1790"}, {"messageId": "1791", "fix": "2173", "desc": "1793"}, {"messageId": "1687", "data": "2174", "fix": "2175", "desc": "1690"}, {"messageId": "1687", "data": "2176", "fix": "2177", "desc": "1693"}, {"messageId": "1687", "data": "2178", "fix": "2179", "desc": "1696"}, {"messageId": "1687", "data": "2180", "fix": "2181", "desc": "1699"}, {"messageId": "1687", "data": "2182", "fix": "2183", "desc": "1690"}, {"messageId": "1687", "data": "2184", "fix": "2185", "desc": "1693"}, {"messageId": "1687", "data": "2186", "fix": "2187", "desc": "1696"}, {"messageId": "1687", "data": "2188", "fix": "2189", "desc": "1699"}, {"messageId": "1687", "data": "2190", "fix": "2191", "desc": "1690"}, {"messageId": "1687", "data": "2192", "fix": "2193", "desc": "1693"}, {"messageId": "1687", "data": "2194", "fix": "2195", "desc": "1696"}, {"messageId": "1687", "data": "2196", "fix": "2197", "desc": "1699"}, {"messageId": "1687", "data": "2198", "fix": "2199", "desc": "1690"}, {"messageId": "1687", "data": "2200", "fix": "2201", "desc": "1693"}, {"messageId": "1687", "data": "2202", "fix": "2203", "desc": "1696"}, {"messageId": "1687", "data": "2204", "fix": "2205", "desc": "1699"}, {"desc": "2206", "fix": "2207"}, {"messageId": "1687", "data": "2208", "fix": "2209", "desc": "1690"}, {"messageId": "1687", "data": "2210", "fix": "2211", "desc": "1693"}, {"messageId": "1687", "data": "2212", "fix": "2213", "desc": "1696"}, {"messageId": "1687", "data": "2214", "fix": "2215", "desc": "1699"}, {"messageId": "1788", "fix": "2216", "desc": "1790"}, {"messageId": "1791", "fix": "2217", "desc": "1793"}, {"messageId": "1788", "fix": "2218", "desc": "1790"}, {"messageId": "1791", "fix": "2219", "desc": "1793"}, {"messageId": "1788", "fix": "2220", "desc": "1790"}, {"messageId": "1791", "fix": "2221", "desc": "1793"}, {"messageId": "1788", "fix": "2222", "desc": "1790"}, {"messageId": "1791", "fix": "2223", "desc": "1793"}, {"messageId": "1788", "fix": "2224", "desc": "1790"}, {"messageId": "1791", "fix": "2225", "desc": "1793"}, {"messageId": "1788", "fix": "2226", "desc": "1790"}, {"messageId": "1791", "fix": "2227", "desc": "1793"}, {"messageId": "1788", "fix": "2228", "desc": "1790"}, {"messageId": "1791", "fix": "2229", "desc": "1793"}, {"messageId": "1788", "fix": "2230", "desc": "1790"}, {"messageId": "1791", "fix": "2231", "desc": "1793"}, {"messageId": "1788", "fix": "2232", "desc": "1790"}, {"messageId": "1791", "fix": "2233", "desc": "1793"}, {"messageId": "1788", "fix": "2234", "desc": "1790"}, {"messageId": "1791", "fix": "2235", "desc": "1793"}, {"messageId": "1788", "fix": "2236", "desc": "1790"}, {"messageId": "1791", "fix": "2237", "desc": "1793"}, {"messageId": "1687", "data": "2238", "fix": "2239", "desc": "1690"}, {"messageId": "1687", "data": "2240", "fix": "2241", "desc": "1693"}, {"messageId": "1687", "data": "2242", "fix": "2243", "desc": "1696"}, {"messageId": "1687", "data": "2244", "fix": "2245", "desc": "1699"}, {"messageId": "1788", "fix": "2246", "desc": "1790"}, {"messageId": "1791", "fix": "2247", "desc": "1793"}, {"messageId": "1687", "data": "2248", "fix": "2249", "desc": "1690"}, {"messageId": "1687", "data": "2250", "fix": "2251", "desc": "1693"}, {"messageId": "1687", "data": "2252", "fix": "2253", "desc": "1696"}, {"messageId": "1687", "data": "2254", "fix": "2255", "desc": "1699"}, {"messageId": "1687", "data": "2256", "fix": "2257", "desc": "1690"}, {"messageId": "1687", "data": "2258", "fix": "2259", "desc": "1693"}, {"messageId": "1687", "data": "2260", "fix": "2261", "desc": "1696"}, {"messageId": "1687", "data": "2262", "fix": "2263", "desc": "1699"}, {"messageId": "1687", "data": "2264", "fix": "2265", "desc": "1690"}, {"messageId": "1687", "data": "2266", "fix": "2267", "desc": "1693"}, {"messageId": "1687", "data": "2268", "fix": "2269", "desc": "1696"}, {"messageId": "1687", "data": "2270", "fix": "2271", "desc": "1699"}, {"desc": "2272", "fix": "2273"}, {"messageId": "1687", "data": "2274", "fix": "2275", "desc": "1690"}, {"messageId": "1687", "data": "2276", "fix": "2277", "desc": "1693"}, {"messageId": "1687", "data": "2278", "fix": "2279", "desc": "1696"}, {"messageId": "1687", "data": "2280", "fix": "2281", "desc": "1699"}, {"messageId": "1788", "fix": "2282", "desc": "1790"}, {"messageId": "1791", "fix": "2283", "desc": "1793"}, {"messageId": "1687", "data": "2284", "fix": "2285", "desc": "1690"}, {"messageId": "1687", "data": "2286", "fix": "2287", "desc": "1693"}, {"messageId": "1687", "data": "2288", "fix": "2289", "desc": "1696"}, {"messageId": "1687", "data": "2290", "fix": "2291", "desc": "1699"}, {"messageId": "1788", "fix": "2292", "desc": "1790"}, {"messageId": "1791", "fix": "2293", "desc": "1793"}, {"messageId": "1788", "fix": "2294", "desc": "1790"}, {"messageId": "1791", "fix": "2295", "desc": "1793"}, {"messageId": "1687", "data": "2296", "fix": "2297", "desc": "1690"}, {"messageId": "1687", "data": "2298", "fix": "2299", "desc": "1693"}, {"messageId": "1687", "data": "2300", "fix": "2301", "desc": "1696"}, {"messageId": "1687", "data": "2302", "fix": "2303", "desc": "1699"}, {"messageId": "1687", "data": "2304", "fix": "2305", "desc": "1690"}, {"messageId": "1687", "data": "2306", "fix": "2307", "desc": "1693"}, {"messageId": "1687", "data": "2308", "fix": "2309", "desc": "1696"}, {"messageId": "1687", "data": "2310", "fix": "2311", "desc": "1699"}, {"messageId": "1687", "data": "2312", "fix": "2313", "desc": "1690"}, {"messageId": "1687", "data": "2314", "fix": "2315", "desc": "1693"}, {"messageId": "1687", "data": "2316", "fix": "2317", "desc": "1696"}, {"messageId": "1687", "data": "2318", "fix": "2319", "desc": "1699"}, {"messageId": "1687", "data": "2320", "fix": "2321", "desc": "1690"}, {"messageId": "1687", "data": "2322", "fix": "2323", "desc": "1693"}, {"messageId": "1687", "data": "2324", "fix": "2325", "desc": "1696"}, {"messageId": "1687", "data": "2326", "fix": "2327", "desc": "1699"}, {"desc": "2328", "fix": "2329"}, {"messageId": "1687", "data": "2330", "fix": "2331", "desc": "1690"}, {"messageId": "1687", "data": "2332", "fix": "2333", "desc": "1693"}, {"messageId": "1687", "data": "2334", "fix": "2335", "desc": "1696"}, {"messageId": "1687", "data": "2336", "fix": "2337", "desc": "1699"}, {"messageId": "1687", "data": "2338", "fix": "2339", "desc": "1690"}, {"messageId": "1687", "data": "2340", "fix": "2341", "desc": "1693"}, {"messageId": "1687", "data": "2342", "fix": "2343", "desc": "1696"}, {"messageId": "1687", "data": "2344", "fix": "2345", "desc": "1699"}, {"messageId": "1788", "fix": "2346", "desc": "1790"}, {"messageId": "1791", "fix": "2347", "desc": "1793"}, {"messageId": "1687", "data": "2348", "fix": "2349", "desc": "1690"}, {"messageId": "1687", "data": "2350", "fix": "2351", "desc": "1693"}, {"messageId": "1687", "data": "2352", "fix": "2353", "desc": "1696"}, {"messageId": "1687", "data": "2354", "fix": "2355", "desc": "1699"}, {"messageId": "1788", "fix": "2356", "desc": "1790"}, {"messageId": "1791", "fix": "2357", "desc": "1793"}, {"messageId": "1788", "fix": "2358", "desc": "1790"}, {"messageId": "1791", "fix": "2359", "desc": "1793"}, {"messageId": "1788", "fix": "2360", "desc": "1790"}, {"messageId": "1791", "fix": "2361", "desc": "1793"}, {"messageId": "1687", "data": "2362", "fix": "2363", "desc": "1690"}, {"messageId": "1687", "data": "2364", "fix": "2365", "desc": "1693"}, {"messageId": "1687", "data": "2366", "fix": "2367", "desc": "1696"}, {"messageId": "1687", "data": "2368", "fix": "2369", "desc": "1699"}, {"messageId": "1788", "fix": "2370", "desc": "1790"}, {"messageId": "1791", "fix": "2371", "desc": "1793"}, {"messageId": "1788", "fix": "2372", "desc": "1790"}, {"messageId": "1791", "fix": "2373", "desc": "1793"}, {"messageId": "1788", "fix": "2374", "desc": "1790"}, {"messageId": "1791", "fix": "2375", "desc": "1793"}, {"messageId": "1788", "fix": "2376", "desc": "1790"}, {"messageId": "1791", "fix": "2377", "desc": "1793"}, {"messageId": "1788", "fix": "2378", "desc": "1790"}, {"messageId": "1791", "fix": "2379", "desc": "1793"}, "replaceWithAlt", {"alt": "2380"}, {"range": "2381", "text": "2382"}, "Replace with `&apos;`.", {"alt": "2383"}, {"range": "2384", "text": "2385"}, "Replace with `&lsquo;`.", {"alt": "2386"}, {"range": "2387", "text": "2388"}, "Replace with `&#39;`.", {"alt": "2389"}, {"range": "2390", "text": "2391"}, "Replace with `&rsquo;`.", {"alt": "2380"}, {"range": "2392", "text": "2393"}, {"alt": "2383"}, {"range": "2394", "text": "2395"}, {"alt": "2386"}, {"range": "2396", "text": "2397"}, {"alt": "2389"}, {"range": "2398", "text": "2399"}, {"alt": "2380"}, {"range": "2400", "text": "2401"}, {"alt": "2383"}, {"range": "2402", "text": "2403"}, {"alt": "2386"}, {"range": "2404", "text": "2405"}, {"alt": "2389"}, {"range": "2406", "text": "2407"}, {"alt": "2380"}, {"range": "2408", "text": "2409"}, {"alt": "2383"}, {"range": "2410", "text": "2411"}, {"alt": "2386"}, {"range": "2412", "text": "2413"}, {"alt": "2389"}, {"range": "2414", "text": "2415"}, {"alt": "2380"}, {"range": "2416", "text": "2417"}, {"alt": "2383"}, {"range": "2418", "text": "2419"}, {"alt": "2386"}, {"range": "2420", "text": "2421"}, {"alt": "2389"}, {"range": "2422", "text": "2423"}, {"alt": "2380"}, {"range": "2424", "text": "2425"}, {"alt": "2383"}, {"range": "2426", "text": "2427"}, {"alt": "2386"}, {"range": "2428", "text": "2429"}, {"alt": "2389"}, {"range": "2430", "text": "2431"}, {"alt": "2380"}, {"range": "2432", "text": "2433"}, {"alt": "2383"}, {"range": "2434", "text": "2435"}, {"alt": "2386"}, {"range": "2436", "text": "2437"}, {"alt": "2389"}, {"range": "2438", "text": "2439"}, {"alt": "2380"}, {"range": "2440", "text": "2441"}, {"alt": "2383"}, {"range": "2442", "text": "2443"}, {"alt": "2386"}, {"range": "2444", "text": "2445"}, {"alt": "2389"}, {"range": "2446", "text": "2447"}, {"alt": "2380"}, {"range": "2448", "text": "2449"}, {"alt": "2383"}, {"range": "2450", "text": "2451"}, {"alt": "2386"}, {"range": "2452", "text": "2453"}, {"alt": "2389"}, {"range": "2454", "text": "2455"}, {"alt": "2380"}, {"range": "2456", "text": "2457"}, {"alt": "2383"}, {"range": "2458", "text": "2459"}, {"alt": "2386"}, {"range": "2460", "text": "2461"}, {"alt": "2389"}, {"range": "2462", "text": "2463"}, {"alt": "2380"}, {"range": "2464", "text": "2465"}, {"alt": "2383"}, {"range": "2466", "text": "2467"}, {"alt": "2386"}, {"range": "2468", "text": "2469"}, {"alt": "2389"}, {"range": "2470", "text": "2471"}, {"alt": "2380"}, {"range": "2472", "text": "2473"}, {"alt": "2383"}, {"range": "2474", "text": "2475"}, {"alt": "2386"}, {"range": "2476", "text": "2477"}, {"alt": "2389"}, {"range": "2478", "text": "2479"}, "suggestUnknown", {"range": "2480", "text": "2481"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2482", "text": "2483"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2484", "text": "2481"}, {"range": "2485", "text": "2483"}, {"range": "2486", "text": "2481"}, {"range": "2487", "text": "2483"}, {"range": "2488", "text": "2481"}, {"range": "2489", "text": "2483"}, {"range": "2490", "text": "2481"}, {"range": "2491", "text": "2483"}, {"range": "2492", "text": "2481"}, {"range": "2493", "text": "2483"}, {"range": "2494", "text": "2481"}, {"range": "2495", "text": "2483"}, {"range": "2496", "text": "2481"}, {"range": "2497", "text": "2483"}, {"range": "2498", "text": "2481"}, {"range": "2499", "text": "2483"}, {"alt": "2380"}, {"range": "2500", "text": "2501"}, {"alt": "2383"}, {"range": "2502", "text": "2503"}, {"alt": "2386"}, {"range": "2504", "text": "2505"}, {"alt": "2389"}, {"range": "2506", "text": "2507"}, {"alt": "2380"}, {"range": "2508", "text": "2509"}, {"alt": "2383"}, {"range": "2510", "text": "2511"}, {"alt": "2386"}, {"range": "2512", "text": "2513"}, {"alt": "2389"}, {"range": "2514", "text": "2515"}, {"alt": "2380"}, {"range": "2516", "text": "2517"}, {"alt": "2383"}, {"range": "2518", "text": "2519"}, {"alt": "2386"}, {"range": "2520", "text": "2521"}, {"alt": "2389"}, {"range": "2522", "text": "2523"}, {"alt": "2380"}, {"range": "2524", "text": "2525"}, {"alt": "2383"}, {"range": "2526", "text": "2527"}, {"alt": "2386"}, {"range": "2528", "text": "2529"}, {"alt": "2389"}, {"range": "2530", "text": "2531"}, {"alt": "2380"}, {"range": "2532", "text": "2533"}, {"alt": "2383"}, {"range": "2534", "text": "2535"}, {"alt": "2386"}, {"range": "2536", "text": "2537"}, {"alt": "2389"}, {"range": "2538", "text": "2539"}, {"alt": "2380"}, {"range": "2540", "text": "2541"}, {"alt": "2383"}, {"range": "2542", "text": "2543"}, {"alt": "2386"}, {"range": "2544", "text": "2545"}, {"alt": "2389"}, {"range": "2546", "text": "2547"}, {"alt": "2380"}, {"range": "2548", "text": "2549"}, {"alt": "2383"}, {"range": "2550", "text": "2551"}, {"alt": "2386"}, {"range": "2552", "text": "2553"}, {"alt": "2389"}, {"range": "2554", "text": "2555"}, {"alt": "2380"}, {"range": "2556", "text": "2557"}, {"alt": "2383"}, {"range": "2558", "text": "2559"}, {"alt": "2386"}, {"range": "2560", "text": "2561"}, {"alt": "2389"}, {"range": "2562", "text": "2563"}, {"alt": "2380"}, {"range": "2564", "text": "2457"}, {"alt": "2383"}, {"range": "2565", "text": "2459"}, {"alt": "2386"}, {"range": "2566", "text": "2461"}, {"alt": "2389"}, {"range": "2567", "text": "2463"}, {"alt": "2380"}, {"range": "2568", "text": "2569"}, {"alt": "2383"}, {"range": "2570", "text": "2571"}, {"alt": "2386"}, {"range": "2572", "text": "2573"}, {"alt": "2389"}, {"range": "2574", "text": "2575"}, {"alt": "2380"}, {"range": "2576", "text": "2577"}, {"alt": "2383"}, {"range": "2578", "text": "2579"}, {"alt": "2386"}, {"range": "2580", "text": "2581"}, {"alt": "2389"}, {"range": "2582", "text": "2583"}, {"alt": "2380"}, {"range": "2584", "text": "2585"}, {"alt": "2383"}, {"range": "2586", "text": "2587"}, {"alt": "2386"}, {"range": "2588", "text": "2589"}, {"alt": "2389"}, {"range": "2590", "text": "2591"}, {"alt": "2380"}, {"range": "2592", "text": "2593"}, {"alt": "2383"}, {"range": "2594", "text": "2595"}, {"alt": "2386"}, {"range": "2596", "text": "2597"}, {"alt": "2389"}, {"range": "2598", "text": "2599"}, {"alt": "2380"}, {"range": "2600", "text": "2601"}, {"alt": "2383"}, {"range": "2602", "text": "2603"}, {"alt": "2386"}, {"range": "2604", "text": "2605"}, {"alt": "2389"}, {"range": "2606", "text": "2607"}, {"alt": "2380"}, {"range": "2608", "text": "2609"}, {"alt": "2383"}, {"range": "2610", "text": "2611"}, {"alt": "2386"}, {"range": "2612", "text": "2613"}, {"alt": "2389"}, {"range": "2614", "text": "2615"}, {"alt": "2380"}, {"range": "2616", "text": "2617"}, {"alt": "2383"}, {"range": "2618", "text": "2619"}, {"alt": "2386"}, {"range": "2620", "text": "2621"}, {"alt": "2389"}, {"range": "2622", "text": "2623"}, {"alt": "2380"}, {"range": "2624", "text": "2625"}, {"alt": "2383"}, {"range": "2626", "text": "2627"}, {"alt": "2386"}, {"range": "2628", "text": "2629"}, {"alt": "2389"}, {"range": "2630", "text": "2631"}, {"alt": "2380"}, {"range": "2632", "text": "2633"}, {"alt": "2383"}, {"range": "2634", "text": "2635"}, {"alt": "2386"}, {"range": "2636", "text": "2637"}, {"alt": "2389"}, {"range": "2638", "text": "2639"}, {"alt": "2380"}, {"range": "2640", "text": "2641"}, {"alt": "2383"}, {"range": "2642", "text": "2643"}, {"alt": "2386"}, {"range": "2644", "text": "2645"}, {"alt": "2389"}, {"range": "2646", "text": "2647"}, {"alt": "2380"}, {"range": "2648", "text": "2649"}, {"alt": "2383"}, {"range": "2650", "text": "2651"}, {"alt": "2386"}, {"range": "2652", "text": "2653"}, {"alt": "2389"}, {"range": "2654", "text": "2655"}, {"alt": "2380"}, {"range": "2656", "text": "2657"}, {"alt": "2383"}, {"range": "2658", "text": "2659"}, {"alt": "2386"}, {"range": "2660", "text": "2661"}, {"alt": "2389"}, {"range": "2662", "text": "2663"}, {"range": "2664", "text": "2481"}, {"range": "2665", "text": "2483"}, {"range": "2666", "text": "2481"}, {"range": "2667", "text": "2483"}, {"range": "2668", "text": "2481"}, {"range": "2669", "text": "2483"}, {"alt": "2380"}, {"range": "2670", "text": "2671"}, {"alt": "2383"}, {"range": "2672", "text": "2673"}, {"alt": "2386"}, {"range": "2674", "text": "2675"}, {"alt": "2389"}, {"range": "2676", "text": "2677"}, {"range": "2678", "text": "2481"}, {"range": "2679", "text": "2483"}, {"alt": "2380"}, {"range": "2680", "text": "2681"}, {"alt": "2383"}, {"range": "2682", "text": "2683"}, {"alt": "2386"}, {"range": "2684", "text": "2685"}, {"alt": "2389"}, {"range": "2686", "text": "2687"}, {"alt": "2380"}, {"range": "2688", "text": "2689"}, {"alt": "2383"}, {"range": "2690", "text": "2691"}, {"alt": "2386"}, {"range": "2692", "text": "2693"}, {"alt": "2389"}, {"range": "2694", "text": "2695"}, {"alt": "2380"}, {"range": "2696", "text": "2697"}, {"alt": "2383"}, {"range": "2698", "text": "2699"}, {"alt": "2386"}, {"range": "2700", "text": "2701"}, {"alt": "2389"}, {"range": "2702", "text": "2703"}, {"alt": "2704"}, {"range": "2705", "text": "2706"}, "Replace with `&quot;`.", {"alt": "2707"}, {"range": "2708", "text": "2709"}, "Replace with `&ldquo;`.", {"alt": "2710"}, {"range": "2711", "text": "2712"}, "Replace with `&#34;`.", {"alt": "2713"}, {"range": "2714", "text": "2715"}, "Replace with `&rdquo;`.", {"alt": "2704"}, {"range": "2716", "text": "2717"}, {"alt": "2707"}, {"range": "2718", "text": "2719"}, {"alt": "2710"}, {"range": "2720", "text": "2721"}, {"alt": "2713"}, {"range": "2722", "text": "2723"}, {"alt": "2380"}, {"range": "2724", "text": "2725"}, {"alt": "2383"}, {"range": "2726", "text": "2727"}, {"alt": "2386"}, {"range": "2728", "text": "2729"}, {"alt": "2389"}, {"range": "2730", "text": "2731"}, {"alt": "2380"}, {"range": "2732", "text": "2733"}, {"alt": "2383"}, {"range": "2734", "text": "2735"}, {"alt": "2386"}, {"range": "2736", "text": "2737"}, {"alt": "2389"}, {"range": "2738", "text": "2739"}, {"alt": "2380"}, {"range": "2740", "text": "2741"}, {"alt": "2383"}, {"range": "2742", "text": "2743"}, {"alt": "2386"}, {"range": "2744", "text": "2745"}, {"alt": "2389"}, {"range": "2746", "text": "2747"}, {"alt": "2380"}, {"range": "2748", "text": "2749"}, {"alt": "2383"}, {"range": "2750", "text": "2751"}, {"alt": "2386"}, {"range": "2752", "text": "2753"}, {"alt": "2389"}, {"range": "2754", "text": "2755"}, {"alt": "2380"}, {"range": "2756", "text": "2757"}, {"alt": "2383"}, {"range": "2758", "text": "2759"}, {"alt": "2386"}, {"range": "2760", "text": "2761"}, {"alt": "2389"}, {"range": "2762", "text": "2763"}, {"alt": "2380"}, {"range": "2764", "text": "2765"}, {"alt": "2383"}, {"range": "2766", "text": "2767"}, {"alt": "2386"}, {"range": "2768", "text": "2769"}, {"alt": "2389"}, {"range": "2770", "text": "2771"}, {"range": "2772", "text": "2481"}, {"range": "2773", "text": "2483"}, {"range": "2774", "text": "2481"}, {"range": "2775", "text": "2483"}, {"range": "2776", "text": "2481"}, {"range": "2777", "text": "2483"}, {"range": "2778", "text": "2481"}, {"range": "2779", "text": "2483"}, {"range": "2780", "text": "2481"}, {"range": "2781", "text": "2483"}, {"range": "2782", "text": "2481"}, {"range": "2783", "text": "2483"}, {"range": "2784", "text": "2481"}, {"range": "2785", "text": "2483"}, {"range": "2786", "text": "2481"}, {"range": "2787", "text": "2483"}, {"alt": "2380"}, {"range": "2788", "text": "2789"}, {"alt": "2383"}, {"range": "2790", "text": "2791"}, {"alt": "2386"}, {"range": "2792", "text": "2793"}, {"alt": "2389"}, {"range": "2794", "text": "2795"}, {"alt": "2380"}, {"range": "2796", "text": "2797"}, {"alt": "2383"}, {"range": "2798", "text": "2799"}, {"alt": "2386"}, {"range": "2800", "text": "2801"}, {"alt": "2389"}, {"range": "2802", "text": "2803"}, {"alt": "2380"}, {"range": "2804", "text": "2805"}, {"alt": "2383"}, {"range": "2806", "text": "2807"}, {"alt": "2386"}, {"range": "2808", "text": "2809"}, {"alt": "2389"}, {"range": "2810", "text": "2811"}, {"range": "2812", "text": "2481"}, {"range": "2813", "text": "2483"}, {"range": "2814", "text": "2481"}, {"range": "2815", "text": "2483"}, {"range": "2816", "text": "2481"}, {"range": "2817", "text": "2483"}, {"alt": "2704"}, {"range": "2818", "text": "2819"}, {"alt": "2707"}, {"range": "2820", "text": "2821"}, {"alt": "2710"}, {"range": "2822", "text": "2823"}, {"alt": "2713"}, {"range": "2824", "text": "2825"}, {"alt": "2704"}, {"range": "2826", "text": "2827"}, {"alt": "2707"}, {"range": "2828", "text": "2829"}, {"alt": "2710"}, {"range": "2830", "text": "2831"}, {"alt": "2713"}, {"range": "2832", "text": "2833"}, {"range": "2834", "text": "2481"}, {"range": "2835", "text": "2483"}, {"range": "2836", "text": "2481"}, {"range": "2837", "text": "2483"}, {"range": "2838", "text": "2481"}, {"range": "2839", "text": "2483"}, {"range": "2840", "text": "2481"}, {"range": "2841", "text": "2483"}, {"range": "2842", "text": "2481"}, {"range": "2843", "text": "2483"}, {"range": "2844", "text": "2481"}, {"range": "2845", "text": "2483"}, "Update the dependencies array to be: [checkAuth]", {"range": "2846", "text": "2847"}, {"range": "2848", "text": "2481"}, {"range": "2849", "text": "2483"}, {"range": "2850", "text": "2481"}, {"range": "2851", "text": "2483"}, {"range": "2852", "text": "2481"}, {"range": "2853", "text": "2483"}, {"range": "2854", "text": "2481"}, {"range": "2855", "text": "2483"}, {"range": "2856", "text": "2481"}, {"range": "2857", "text": "2483"}, {"range": "2858", "text": "2481"}, {"range": "2859", "text": "2483"}, {"alt": "2380"}, {"range": "2860", "text": "2861"}, {"alt": "2383"}, {"range": "2862", "text": "2863"}, {"alt": "2386"}, {"range": "2864", "text": "2865"}, {"alt": "2389"}, {"range": "2866", "text": "2867"}, {"alt": "2380"}, {"range": "2868", "text": "2869"}, {"alt": "2383"}, {"range": "2870", "text": "2871"}, {"alt": "2386"}, {"range": "2872", "text": "2873"}, {"alt": "2389"}, {"range": "2874", "text": "2875"}, {"alt": "2380"}, {"range": "2876", "text": "2877"}, {"alt": "2383"}, {"range": "2878", "text": "2879"}, {"alt": "2386"}, {"range": "2880", "text": "2881"}, {"alt": "2389"}, {"range": "2882", "text": "2883"}, {"alt": "2380"}, {"range": "2884", "text": "2885"}, {"alt": "2383"}, {"range": "2886", "text": "2887"}, {"alt": "2386"}, {"range": "2888", "text": "2889"}, {"alt": "2389"}, {"range": "2890", "text": "2891"}, "Update the dependencies array to be: [user?.id, activeTab, fetchOrders]", {"range": "2892", "text": "2893"}, {"alt": "2380"}, {"range": "2894", "text": "2895"}, {"alt": "2383"}, {"range": "2896", "text": "2897"}, {"alt": "2386"}, {"range": "2898", "text": "2899"}, {"alt": "2389"}, {"range": "2900", "text": "2901"}, {"range": "2902", "text": "2481"}, {"range": "2903", "text": "2483"}, {"range": "2904", "text": "2481"}, {"range": "2905", "text": "2483"}, {"range": "2906", "text": "2481"}, {"range": "2907", "text": "2483"}, {"range": "2908", "text": "2481"}, {"range": "2909", "text": "2483"}, {"range": "2910", "text": "2481"}, {"range": "2911", "text": "2483"}, {"range": "2912", "text": "2481"}, {"range": "2913", "text": "2483"}, {"range": "2914", "text": "2481"}, {"range": "2915", "text": "2483"}, {"range": "2916", "text": "2481"}, {"range": "2917", "text": "2483"}, {"range": "2918", "text": "2481"}, {"range": "2919", "text": "2483"}, {"range": "2920", "text": "2481"}, {"range": "2921", "text": "2483"}, {"range": "2922", "text": "2481"}, {"range": "2923", "text": "2483"}, {"alt": "2380"}, {"range": "2924", "text": "2925"}, {"alt": "2383"}, {"range": "2926", "text": "2927"}, {"alt": "2386"}, {"range": "2928", "text": "2929"}, {"alt": "2389"}, {"range": "2930", "text": "2931"}, {"range": "2932", "text": "2481"}, {"range": "2933", "text": "2483"}, {"alt": "2380"}, {"range": "2934", "text": "2935"}, {"alt": "2383"}, {"range": "2936", "text": "2937"}, {"alt": "2386"}, {"range": "2938", "text": "2939"}, {"alt": "2389"}, {"range": "2940", "text": "2941"}, {"alt": "2380"}, {"range": "2942", "text": "2943"}, {"alt": "2383"}, {"range": "2944", "text": "2945"}, {"alt": "2386"}, {"range": "2946", "text": "2947"}, {"alt": "2389"}, {"range": "2948", "text": "2949"}, {"alt": "2380"}, {"range": "2950", "text": "2951"}, {"alt": "2383"}, {"range": "2952", "text": "2953"}, {"alt": "2386"}, {"range": "2954", "text": "2955"}, {"alt": "2389"}, {"range": "2956", "text": "2957"}, "Update the dependencies array to be: [clearCart, sessionId]", {"range": "2958", "text": "2959"}, {"alt": "2380"}, {"range": "2960", "text": "2961"}, {"alt": "2383"}, {"range": "2962", "text": "2963"}, {"alt": "2386"}, {"range": "2964", "text": "2965"}, {"alt": "2389"}, {"range": "2966", "text": "2967"}, {"range": "2968", "text": "2481"}, {"range": "2969", "text": "2483"}, {"alt": "2380"}, {"range": "2970", "text": "2971"}, {"alt": "2383"}, {"range": "2972", "text": "2973"}, {"alt": "2386"}, {"range": "2974", "text": "2975"}, {"alt": "2389"}, {"range": "2976", "text": "2977"}, {"range": "2978", "text": "2481"}, {"range": "2979", "text": "2483"}, {"range": "2980", "text": "2481"}, {"range": "2981", "text": "2483"}, {"alt": "2380"}, {"range": "2982", "text": "2983"}, {"alt": "2383"}, {"range": "2984", "text": "2985"}, {"alt": "2386"}, {"range": "2986", "text": "2987"}, {"alt": "2389"}, {"range": "2988", "text": "2989"}, {"alt": "2380"}, {"range": "2990", "text": "2991"}, {"alt": "2383"}, {"range": "2992", "text": "2993"}, {"alt": "2386"}, {"range": "2994", "text": "2995"}, {"alt": "2389"}, {"range": "2996", "text": "2997"}, {"alt": "2380"}, {"range": "2998", "text": "2999"}, {"alt": "2383"}, {"range": "3000", "text": "3001"}, {"alt": "2386"}, {"range": "3002", "text": "3003"}, {"alt": "2389"}, {"range": "3004", "text": "3005"}, {"alt": "2380"}, {"range": "3006", "text": "3007"}, {"alt": "2383"}, {"range": "3008", "text": "3009"}, {"alt": "2386"}, {"range": "3010", "text": "3011"}, {"alt": "2389"}, {"range": "3012", "text": "3013"}, "Update the dependencies array to be: [handleFileUpload]", {"range": "3014", "text": "3015"}, {"alt": "2380"}, {"range": "3016", "text": "3017"}, {"alt": "2383"}, {"range": "3018", "text": "3019"}, {"alt": "2386"}, {"range": "3020", "text": "3021"}, {"alt": "2389"}, {"range": "3022", "text": "3023"}, {"alt": "2380"}, {"range": "3024", "text": "3025"}, {"alt": "2383"}, {"range": "3026", "text": "3027"}, {"alt": "2386"}, {"range": "3028", "text": "3029"}, {"alt": "2389"}, {"range": "3030", "text": "3031"}, {"range": "3032", "text": "2481"}, {"range": "3033", "text": "2483"}, {"alt": "2380"}, {"range": "3034", "text": "3035"}, {"alt": "2383"}, {"range": "3036", "text": "3037"}, {"alt": "2386"}, {"range": "3038", "text": "3039"}, {"alt": "2389"}, {"range": "3040", "text": "3041"}, {"range": "3042", "text": "2481"}, {"range": "3043", "text": "2483"}, {"range": "3044", "text": "2481"}, {"range": "3045", "text": "2483"}, {"range": "3046", "text": "2481"}, {"range": "3047", "text": "2483"}, {"alt": "2380"}, {"range": "3048", "text": "3049"}, {"alt": "2383"}, {"range": "3050", "text": "3051"}, {"alt": "2386"}, {"range": "3052", "text": "3053"}, {"alt": "2389"}, {"range": "3054", "text": "3055"}, {"range": "3056", "text": "2481"}, {"range": "3057", "text": "2483"}, {"range": "3058", "text": "2481"}, {"range": "3059", "text": "2483"}, {"range": "3060", "text": "2481"}, {"range": "3061", "text": "2483"}, {"range": "3062", "text": "2481"}, {"range": "3063", "text": "2483"}, {"range": "3064", "text": "2481"}, {"range": "3065", "text": "2483"}, "&apos;", [996, 1191], "\n              Découvrez l&apos;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&lsquo;", [996, 1191], "\n              Découvrez l&lsquo;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&#39;", [996, 1191], "\n              Découvrez l&#39;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&rsquo;", [996, 1191], "\n              Découvrez l&rsquo;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&apos;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&lsquo;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&#39;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&rsquo;univers des produits premium à base de\n              Delta-9 THC.\n            ", [2773, 2812], "\n                    Qu&apos;il s'agisse de ", [2773, 2812], "\n                    Qu&lsquo;il s'agisse de ", [2773, 2812], "\n                    Qu&#39;il s'agisse de ", [2773, 2812], "\n                    Qu&rsquo;il s'agisse de ", [2773, 2812], "\n                    Qu'il s&apos;agisse de ", [2773, 2812], "\n                    Qu'il s&lsquo;agisse de ", [2773, 2812], "\n                    Qu'il s&#39;agisse de ", [2773, 2812], "\n                    Qu'il s&rsquo;agisse de ", [2891, 2959], " ou d&apos;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&lsquo;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&#39;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&rsquo;autres formats à venir, nos\n                    produits sont ", [3309, 3379], "\n                    Qu&apos;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&lsquo;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&#39;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&rsquo;est-ce que le Delta-9 THC ?\n                  ", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&apos;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&lsquo;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&#39;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&rsquo;elle soit reconnue pour son", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&apos;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&lsquo;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&#39;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&rsquo;Union européenne, à condition que le\n                    produit ne soit", [9140, 9212], "\n                  ⚠️ En cas d&apos;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&lsquo;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&#39;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&rsquo;utilisation non conforme\n                ", [9989, 10038], "• Trouble de l&apos;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&lsquo;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&#39;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&rsquo;attention ou ralentissement moteur", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&apos;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&lsquo;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&#39;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&rsquo;allaitement\n                    ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&apos;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&lsquo;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&#39;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&rsquo;utilisateur.\n                ", [2221, 2224], "unknown", [2221, 2224], "never", [815, 818], [815, 818], [1787, 1790], [1787, 1790], [557, 560], [557, 560], [3840, 3843], [3840, 3843], [6790, 6793], [6790, 6793], [1918, 1921], [1918, 1921], [3174, 3177], [3174, 3177], [1203, 1206], [1203, 1206], [743, 837], "\n              Informations légales et conditions d&apos;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&lsquo;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&#39;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&rsquo;utilisation du site Deltagum.\n            ", [2620, 2661], "\n                      Qu&apos;il s'agisse de ", [2620, 2661], "\n                      Qu&lsquo;il s'agisse de ", [2620, 2661], "\n                      Qu&#39;il s'agisse de ", [2620, 2661], "\n                      Qu&rsquo;il s'agisse de ", [2620, 2661], "\n                      Qu'il s&apos;agisse de ", [2620, 2661], "\n                      Qu'il s&lsquo;agisse de ", [2620, 2661], "\n                      Qu'il s&#39;agisse de ", [2620, 2661], "\n                      Qu'il s&rsquo;agisse de ", [2742, 2812], " ou d&apos;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&lsquo;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&#39;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&rsquo;autres formats à venir, nos\n                      produits sont ", [3174, 3251], "\n                      📋 Qu&apos;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&lsquo;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&#39;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&rsquo;est-ce que le Delta-9 THC ?\n                    ", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&apos;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&lsquo;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&#39;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&rsquo;elle soit reconnue pour son", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&apos;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&lsquo;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&#39;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&rsquo;Union européenne, à condition que le\n                      produit ne soit", [4540, 4620], "\n                      ⚠️ En cas d&apos;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&lsquo;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&#39;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&rsquo;utilisation non conforme\n                    ", [5454, 5503], [5454, 5503], [5454, 5503], [5454, 5503], [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&apos;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&lsquo;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&#39;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&rsquo;utilisateur.\n                      ", [9609, 9996], "\n                  L&apos;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&lsquo;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&#39;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&rsquo;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&apos;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&lsquo;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&#39;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&rsquo;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&apos;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&lsquo;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&#39;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&rsquo;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&apos;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&lsquo;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&#39;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&rsquo;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&apos;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&lsquo;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&#39;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&rsquo;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&apos;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&lsquo;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&#39;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&rsquo;opposition aux\n                  données personnelles vous concernant.\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&apos;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&lsquo;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&#39;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&rsquo;adresse : <EMAIL>\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&apos;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&lsquo;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&#39;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&rsquo;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&apos;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&lsquo;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&#39;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&rsquo;utilisation de cookies.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&apos;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&lsquo;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&#39;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&rsquo;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&apos;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&lsquo;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&#39;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&rsquo;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [865, 868], [865, 868], [934, 937], [934, 937], [1927, 1930], [1927, 1930], [4109, 4151], "\n            Retour à l&apos;accueil\n          ", [4109, 4151], "\n            Retour à l&lsquo;accueil\n          ", [4109, 4151], "\n            Retour à l&#39;accueil\n          ", [4109, 4151], "\n            Retour à l&rsquo;accueil\n          ", [13876, 13879], [13876, 13879], [6222, 6287], "\n                      Nom de l&apos;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&lsquo;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&#39;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&rsquo;entreprise *\n                    ", [9473, 9530], "\n                    Type d&apos;activité *\n                  ", [9473, 9530], "\n                    Type d&lsquo;activité *\n                  ", [9473, 9530], "\n                    Type d&#39;activité *\n                  ", [9473, 9530], "\n                    Type d&rsquo;activité *\n                  ", [1785, 1966], "\n              Il semble que vous n&apos;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&lsquo;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&#39;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&rsquo;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", "&quot;", [6488, 6629], "\n              &quot;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&ldquo;", [6488, 6629], "\n              &ldquo;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&#34;", [6488, 6629], "\n              &#34;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&rdquo;", [6488, 6629], "\n              &rdquo;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&quot;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&ldquo;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&#34;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&rdquo;\n            ", [10929, 10994], "\n                        Date d&apos;expiration\n                      ", [10929, 10994], "\n                        Date d&lsquo;expiration\n                      ", [10929, 10994], "\n                        Date d&#39;expiration\n                      ", [10929, 10994], "\n                        Date d&rsquo;expiration\n                      ", [1742, 1792], "\n                Vérification d&apos;âge\n              ", [1742, 1792], "\n                Vérification d&lsquo;âge\n              ", [1742, 1792], "\n                Vérification d&#39;âge\n              ", [1742, 1792], "\n                Vérification d&rsquo;âge\n              ", [2265, 2356], "\n                  L&apos;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&lsquo;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&#39;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&rsquo;accès est strictement réservé aux personnes majeures.\n                ", [3175, 3241], "\n                    ✓ Oui, j&apos;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&lsquo;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&#39;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&rsquo;ai 18 ans ou plus\n                  ", [3477, 3544], "\n                    ✗ Non, j&apos;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&lsquo;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&#39;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&rsquo;ai moins de 18 ans\n                  ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&apos;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&lsquo;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&#39;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&rsquo;utilisation.\n              ", [1613, 1616], [1613, 1616], [2530, 2533], [2530, 2533], [711, 714], [711, 714], [719, 722], [719, 722], [890, 893], [890, 893], [6747, 6750], [6747, 6750], [6759, 6762], [6759, 6762], [7028, 7031], [7028, 7031], [3486, 3576], "\n            Plus qu&apos;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&lsquo;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&#39;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&rsquo;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [8045, 8119], "\n                            Retour à l&apos;accueil\n                          ", [8045, 8119], "\n                            Retour à l&lsquo;accueil\n                          ", [8045, 8119], "\n                            Retour à l&#39;accueil\n                          ", [8045, 8119], "\n                            Retour à l&rsquo;accueil\n                          ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&apos;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&lsquo;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&#39;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&rsquo;hésitez pas à nous appeler\n                  directement.\n                ", [734, 737], [734, 737], [1043, 1046], [1043, 1046], [7981, 7984], [7981, 7984], [3338, 3348], "\n        &quot;", [3338, 3348], "\n        &ldquo;", [3338, 3348], "\n        &#34;", [3338, 3348], "\n        &rdquo;", [3361, 3369], "&quot;\n      ", [3361, 3369], "&ldquo;\n      ", [3361, 3369], "&#34;\n      ", [3361, 3369], "&rdquo;\n      ", [2349, 2352], [2349, 2352], [2408, 2411], [2408, 2411], [1737, 1740], [1737, 1740], [1119, 1122], [1119, 1122], [1303, 1306], [1303, 1306], [1472, 1475], [1472, 1475], [1568, 1570], "[checkAuth]", [1906, 1909], [1906, 1909], [2021, 2024], [2021, 2024], [2149, 2152], [2149, 2152], [3296, 3299], [3296, 3299], [3551, 3554], [3551, 3554], [4124, 4127], [4124, 4127], [5722, 5804], "\n                    Vous devez vous connecter en tant qu&apos;admin\n                  ", [5722, 5804], "\n                    Vous devez vous connecter en tant qu&lsquo;admin\n                  ", [5722, 5804], "\n                    Vous devez vous connecter en tant qu&#39;admin\n                  ", [5722, 5804], "\n                    Vous devez vous connecter en tant qu&rsquo;admin\n                  ", [6176, 6258], "\n                    Votre compte n&apos;a pas les permissions admin\n                  ", [6176, 6258], "\n                    Votre compte n&lsquo;a pas les permissions admin\n                  ", [6176, 6258], "\n                    Votre compte n&#39;a pas les permissions admin\n                  ", [6176, 6258], "\n                    Votre compte n&rsquo;a pas les permissions admin\n                  ", [6439, 6497], "\n                    Retour à l&apos;accueil\n                  ", [6439, 6497], "\n                    Retour à l&lsquo;accueil\n                  ", [6439, 6497], "\n                    Retour à l&#39;accueil\n                  ", [6439, 6497], "\n                    Retour à l&rsquo;accueil\n                  ", [6736, 6806], "\n                  🧪 Page de test d&apos;authentification\n                ", [6736, 6806], "\n                  🧪 Page de test d&lsquo;authentification\n                ", [6736, 6806], "\n                  🧪 Page de test d&#39;authentification\n                ", [6736, 6806], "\n                  🧪 Page de test d&rsquo;authentification\n                ", [3226, 3247], "[user?.id, activeTab, fetchOrders]", [17812, 17893], "\n                    Vous n&apos;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&lsquo;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&#39;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&rsquo;avez pas encore passé de commande.\n                  ", [1398, 1401], [1398, 1401], [434, 437], [434, 437], [1044, 1047], [1044, 1047], [1418, 1421], [1418, 1421], [2725, 2728], [2725, 2728], [3291, 3294], [3291, 3294], [3991, 3994], [3991, 3994], [2883, 2886], [2883, 2886], [2891, 2894], [2891, 2894], [416, 419], [416, 419], [1241, 1244], [1241, 1244], [2667, 2713], "\n                  S&apos;inscrire\n                ", [2667, 2713], "\n                  S&lsquo;inscrire\n                ", [2667, 2713], "\n                  S&#39;inscrire\n                ", [2667, 2713], "\n                  S&rsquo;inscrire\n                ", [661, 664], [661, 664], [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&apos;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&lsquo;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&#39;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&rsquo;a été débité de votre\n            compte.\n          ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&apos;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&lsquo;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&#39;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&rsquo;achat\n                ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&apos;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&lsquo;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              Besoin d&#39;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&rsquo;aide ? Notre équipe est là pour vous accompagner.\n            ", [1857, 1868], "[<PERSON><PERSON><PERSON>, sessionId]", [2869, 2887], "Retour à l&apos;accueil", [2869, 2887], "Retour à l&lsquo;accueil", [2869, 2887], "Retour à l&#39;accueil", [2869, 2887], "Retour à l&rsquo;accueil", [168, 171], [168, 171], [1740, 1783], "\n          Test d&apos;Authentification\n        ", [1740, 1783], "\n          Test d&lsquo;Authentification\n        ", [1740, 1783], "\n          Test d&#39;Authentification\n        ", [1740, 1783], "\n          Test d&rsquo;Authentification\n        ", [499, 502], [499, 502], [523, 526], [523, 526], [10060, 10185], "\n                  Considérez l&apos;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&lsquo;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&#39;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&rsquo;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10499, 10621], "\n                  Les permissions et l&apos;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&lsquo;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&#39;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&rsquo;authentification sont correctement\n                  configurées.\n                ", [3252, 3297], "\n            Gestionnaire d&apos;Images\n          ", [3252, 3297], "\n            Gestionnaire d&lsquo;Images\n          ", [3252, 3297], "\n            Gestionnaire d&#39;Images\n          ", [3252, 3297], "\n            Gestionnaire d&rsquo;Images\n          ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&apos;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&lsquo;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&#39;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&rsquo;à 5MB chacune\n                ", [3156, 3158], "[handleFileUpload]", [4853, 4907], "\n              JPG, PNG, WebP jusqu&apos;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&lsquo;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&#39;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&rsquo;à 5MB\n            ", [7754, 7765], "Aujourd&apos;hui", [7754, 7765], "Aujourd&lsquo;hui", [7754, 7765], "Aujourd&#39;hui", [7754, 7765], "Aujourd&rsquo;hui", [3031, 3034], [3031, 3034], [7513, 7627], "\n                    Les paliers permettent d&apos;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&lsquo;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&#39;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&rsquo;offrir des réductions pour les achats en quantité\n                  ", [1063, 1066], [1063, 1066], [744, 747], [744, 747], [1507, 1510], [1507, 1510], [11316, 11388], "\n                          JPG, PNG jusqu&apos;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&lsquo;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&#39;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&rsquo;à 5MB\n                        ", [1212, 1215], [1212, 1215], [371, 374], [371, 374], [390, 393], [390, 393], [1233, 1236], [1233, 1236], [1782, 1785], [1782, 1785]]