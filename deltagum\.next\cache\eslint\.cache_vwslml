[{"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\about\\page.tsx": "1", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\route.ts": "2", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\[sessionId]\\route.ts": "3", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\contact\\route.ts": "4", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\route.ts": "5", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\[id]\\route.ts": "6", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\route.ts": "7", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\[id]\\route.ts": "8", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\route.ts": "9", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\route.ts": "10", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\webhooks\\stripe\\route.ts": "11", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx": "12", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\legal\\page.tsx": "13", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\page.tsx": "14", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx": "15", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\professionals\\page.tsx": "16", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\CheckoutFloatingCandies.tsx": "17", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ConfettiAnimation.tsx": "18", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ContactFloatingCandies.tsx": "19", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FAQFloatingCandies.tsx": "20", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FloatingCandy.tsx": "21", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\index.ts": "22", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\OrderSuccessAnimation.tsx": "23", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ParallaxSection.tsx": "24", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\TestimonialFloatingCandies.tsx": "25", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartItem.tsx": "26", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartSummary.tsx": "27", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\EmptyCart.tsx": "28", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\index.ts": "29", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutForm.tsx": "30", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutProgress.tsx": "31", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\index.ts": "32", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\OrderSummary.tsx": "33", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\PaymentMethods.tsx": "34", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Footer.tsx": "35", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Header.tsx": "36", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\index.ts": "37", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx": "38", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\AgeVerificationModal.tsx": "39", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CartModal.tsx": "40", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CheckoutModal.tsx": "41", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\index.ts": "42", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\ProductModal.tsx": "43", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSelector.tsx": "44", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSlider.tsx": "45", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\index.ts": "46", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\ProductCard.tsx": "47", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\QuantitySelector.tsx": "48", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\CheckoutModalProvider.tsx": "49", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CartSection.tsx": "50", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CheckoutSection.tsx": "51", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ContactSection.tsx": "52", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\FAQSection.tsx": "53", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\HeroSection.tsx": "54", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\index.ts": "55", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ProductOverview.tsx": "56", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\TestimonialsSection.tsx": "57", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Badge.tsx": "58", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Button.tsx": "59", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Card.tsx": "60", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\index.ts": "61", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Input.tsx": "62", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Loading.tsx": "63", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Modal.tsx": "64", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductImage.tsx": "65", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductSkeleton.tsx": "66", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Select.tsx": "67", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\shuffle-cards.tsx": "68", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Skeleton.tsx": "69", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\testimonial-cards.tsx": "70", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Toast.tsx": "71", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\index.ts": "72", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-api.ts": "73", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-cart.ts": "74", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-click-outside.ts": "75", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-customer.ts": "76", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-debounce.ts": "77", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-intersection-observer.ts": "78", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-local-storage.ts": "79", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-products.ts": "80", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-scroll-position.ts": "81", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-window-size.ts": "82", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAgeVerification.ts": "83", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\animations.ts": "84", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\auth.ts": "85", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\config.ts": "86", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\email.ts": "87", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\flavors.ts": "88", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\mock-data.ts": "89", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\prisma.ts": "90", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\stripe.ts": "91", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\uploadthing.ts": "92", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\utils.ts": "93", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\validations.ts": "94", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\cart-store.ts": "95", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\checkout-modal-store.ts": "96", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\customer-store.ts": "97", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\index.ts": "98", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\notification-store.ts": "99", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\product-store.ts": "100", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\ui-store.ts": "101", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\types\\index.ts": "102", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx": "103", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\create\\route.ts": "104", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\login\\route.ts": "105", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\logout\\route.ts": "106", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\me\\route.ts": "107", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\register\\route.ts": "108", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-db\\route.ts": "109", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx": "110", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAuthInit.ts": "111", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\middleware.ts": "112", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\auth-store.ts": "113", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats\\route.ts": "114", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\route.ts": "115", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\user\\route.ts": "116", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\bulk\\route.ts": "117", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\route.ts": "118", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\[variantId]\\route.ts": "119", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\seed\\route.ts": "120", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test\\database\\route.ts": "121", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-orders\\route.ts": "122", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-prisma\\route.ts": "123", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts": "124", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\[filename]\\route.ts": "125", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\user\\profile\\route.ts": "126", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx": "127", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx": "128", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx": "129", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx": "130", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerDetails.tsx": "131", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerList.tsx": "132", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardStats.tsx": "133", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardTest.tsx": "134", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageManager.tsx": "135", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageUpload.tsx": "136", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderDetails.tsx": "137", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderList.tsx": "138", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\PricingTiers.tsx": "139", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductDetails.tsx": "140", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductForm.tsx": "141", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductFormAdvanced.tsx": "142", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductList.tsx": "143", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\RoleManager.tsx": "144", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\Settings.tsx": "145", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleCustomerList.tsx": "146", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleOrderList.tsx": "147", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\DashboardHeader.tsx": "148", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\Sidebar.tsx": "149", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\StatCard.tsx": "150", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\VariantManager.tsx": "151", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripePaymentForm.tsx": "152", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripeTestButton.tsx": "153", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\debug\\StripeDebugPanel.tsx": "154", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\profile\\UserOrders.tsx": "155", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\StripeProvider.tsx": "156", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\permissions.ts": "157", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats-simple\\route.ts": "158", "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-stats\\route.ts": "159"}, {"size": 13990, "mtime": 1752080900453, "results": "160", "hashOfConfig": "161"}, {"size": 5035, "mtime": 1752187951224, "results": "162", "hashOfConfig": "161"}, {"size": 1935, "mtime": 1751969325865, "results": "163", "hashOfConfig": "161"}, {"size": 6828, "mtime": 1752159728680, "results": "164", "hashOfConfig": "161"}, {"size": 3502, "mtime": 1752187951249, "results": "165", "hashOfConfig": "161"}, {"size": 3839, "mtime": 1752182043806, "results": "166", "hashOfConfig": "161"}, {"size": 8360, "mtime": 1752188006594, "results": "167", "hashOfConfig": "161"}, {"size": 4148, "mtime": 1752182096572, "results": "168", "hashOfConfig": "161"}, {"size": 8338, "mtime": 1752187951294, "results": "169", "hashOfConfig": "161"}, {"size": 2957, "mtime": 1752173741981, "results": "170", "hashOfConfig": "161"}, {"size": 3358, "mtime": 1751914442561, "results": "171", "hashOfConfig": "161"}, {"size": 1988, "mtime": 1751905531321, "results": "172", "hashOfConfig": "161"}, {"size": 15095, "mtime": 1752077600483, "results": "173", "hashOfConfig": "161"}, {"size": 573, "mtime": 1752184775515, "results": "174", "hashOfConfig": "161"}, {"size": 15804, "mtime": 1752075621935, "results": "175", "hashOfConfig": "161"}, {"size": 11982, "mtime": 1752075683738, "results": "176", "hashOfConfig": "161"}, {"size": 2153, "mtime": 1751906680568, "results": "177", "hashOfConfig": "161"}, {"size": 4392, "mtime": 1752094078108, "results": "178", "hashOfConfig": "161"}, {"size": 5581, "mtime": 1751906793436, "results": "179", "hashOfConfig": "161"}, {"size": 4146, "mtime": 1751906763690, "results": "180", "hashOfConfig": "161"}, {"size": 4200, "mtime": 1751913074524, "results": "181", "hashOfConfig": "161"}, {"size": 1074, "mtime": 1751906856197, "results": "182", "hashOfConfig": "161"}, {"size": 8800, "mtime": 1751906828244, "results": "183", "hashOfConfig": "161"}, {"size": 5782, "mtime": 1752096261709, "results": "184", "hashOfConfig": "161"}, {"size": 3734, "mtime": 1751906740363, "results": "185", "hashOfConfig": "161"}, {"size": 5798, "mtime": 1751914613690, "results": "186", "hashOfConfig": "161"}, {"size": 8479, "mtime": 1751973506787, "results": "187", "hashOfConfig": "161"}, {"size": 6932, "mtime": 1751906184228, "results": "188", "hashOfConfig": "161"}, {"size": 122, "mtime": 1751906193685, "results": "189", "hashOfConfig": "161"}, {"size": 9792, "mtime": 1751974025042, "results": "190", "hashOfConfig": "161"}, {"size": 8386, "mtime": 1751906326576, "results": "191", "hashOfConfig": "161"}, {"size": 331, "mtime": 1752162759170, "results": "192", "hashOfConfig": "161"}, {"size": 7298, "mtime": 1751915313512, "results": "193", "hashOfConfig": "161"}, {"size": 15596, "mtime": 1752169325801, "results": "194", "hashOfConfig": "161"}, {"size": 10942, "mtime": 1752186968761, "results": "195", "hashOfConfig": "161"}, {"size": 14127, "mtime": 1752179498894, "results": "196", "hashOfConfig": "161"}, {"size": 175, "mtime": 1751904955025, "results": "197", "hashOfConfig": "161"}, {"size": 6324, "mtime": 1752162196749, "results": "198", "hashOfConfig": "161"}, {"size": 4629, "mtime": 1752075744239, "results": "199", "hashOfConfig": "161"}, {"size": 16551, "mtime": 1752187429336, "results": "200", "hashOfConfig": "161"}, {"size": 5810, "mtime": 1752056119091, "results": "201", "hashOfConfig": "161"}, {"size": 390, "mtime": 1751931777477, "results": "202", "hashOfConfig": "161"}, {"size": 997, "mtime": 1751904938338, "results": "203", "hashOfConfig": "161"}, {"size": 6871, "mtime": 1752029312658, "results": "204", "hashOfConfig": "161"}, {"size": 9633, "mtime": 1752096598052, "results": "205", "hashOfConfig": "161"}, {"size": 94, "mtime": 1751905943809, "results": "206", "hashOfConfig": "161"}, {"size": 8824, "mtime": 1752075404314, "results": "207", "hashOfConfig": "161"}, {"size": 4701, "mtime": 1752028686233, "results": "208", "hashOfConfig": "161"}, {"size": 360, "mtime": 1751973662926, "results": "209", "hashOfConfig": "161"}, {"size": 9739, "mtime": 1752187197093, "results": "210", "hashOfConfig": "161"}, {"size": 11109, "mtime": 1751917800908, "results": "211", "hashOfConfig": "161"}, {"size": 13899, "mtime": 1752158696531, "results": "212", "hashOfConfig": "161"}, {"size": 10948, "mtime": 1752075788033, "results": "213", "hashOfConfig": "161"}, {"size": 12000, "mtime": 1752135422441, "results": "214", "hashOfConfig": "161"}, {"size": 322, "mtime": 1752002026619, "results": "215", "hashOfConfig": "161"}, {"size": 10023, "mtime": 1752158134886, "results": "216", "hashOfConfig": "161"}, {"size": 3445, "mtime": 1752135985545, "results": "217", "hashOfConfig": "161"}, {"size": 6779, "mtime": 1751919262277, "results": "218", "hashOfConfig": "161"}, {"size": 4297, "mtime": 1751919108121, "results": "219", "hashOfConfig": "161"}, {"size": 4383, "mtime": 1751920081609, "results": "220", "hashOfConfig": "161"}, {"size": 1147, "mtime": 1752093997233, "results": "221", "hashOfConfig": "161"}, {"size": 9954, "mtime": 1752132072545, "results": "222", "hashOfConfig": "161"}, {"size": 4244, "mtime": 1751904549304, "results": "223", "hashOfConfig": "161"}, {"size": 5334, "mtime": 1752055978341, "results": "224", "hashOfConfig": "161"}, {"size": 2683, "mtime": 1752075579815, "results": "225", "hashOfConfig": "161"}, {"size": 1698, "mtime": 1752037776963, "results": "226", "hashOfConfig": "161"}, {"size": 1995, "mtime": 1751920113481, "results": "227", "hashOfConfig": "161"}, {"size": 2511, "mtime": 1752135889693, "results": "228", "hashOfConfig": "161"}, {"size": 411, "mtime": 1752037582057, "results": "229", "hashOfConfig": "161"}, {"size": 3517, "mtime": 1752135909034, "results": "230", "hashOfConfig": "161"}, {"size": 3670, "mtime": 1751904570609, "results": "231", "hashOfConfig": "161"}, {"size": 503, "mtime": 1751905013426, "results": "232", "hashOfConfig": "161"}, {"size": 1211, "mtime": 1751910816600, "results": "233", "hashOfConfig": "161"}, {"size": 358, "mtime": 1751903810535, "results": "234", "hashOfConfig": "161"}, {"size": 542, "mtime": 1751903651068, "results": "235", "hashOfConfig": "161"}, {"size": 92, "mtime": 1751903836366, "results": "236", "hashOfConfig": "161"}, {"size": 378, "mtime": 1751903605329, "results": "237", "hashOfConfig": "161"}, {"size": 1285, "mtime": 1751920423580, "results": "238", "hashOfConfig": "161"}, {"size": 1843, "mtime": 1751903597483, "results": "239", "hashOfConfig": "161"}, {"size": 91, "mtime": 1751903823409, "results": "240", "hashOfConfig": "161"}, {"size": 674, "mtime": 1751903634486, "results": "241", "hashOfConfig": "161"}, {"size": 662, "mtime": 1751903643125, "results": "242", "hashOfConfig": "161"}, {"size": 2254, "mtime": 1751931518494, "results": "243", "hashOfConfig": "161"}, {"size": 5466, "mtime": 1751905452786, "results": "244", "hashOfConfig": "161"}, {"size": 2429, "mtime": 1752187951575, "results": "245", "hashOfConfig": "161"}, {"size": 2982, "mtime": 1752075226881, "results": "246", "hashOfConfig": "161"}, {"size": 2848, "mtime": 1751903333245, "results": "247", "hashOfConfig": "161"}, {"size": 899, "mtime": 1752075559661, "results": "248", "hashOfConfig": "161"}, {"size": 3386, "mtime": 1752099001422, "results": "249", "hashOfConfig": "161"}, {"size": 755, "mtime": 1752176937104, "results": "250", "hashOfConfig": "161"}, {"size": 519, "mtime": 1751921891882, "results": "251", "hashOfConfig": "161"}, {"size": 1410, "mtime": 1751910941598, "results": "252", "hashOfConfig": "161"}, {"size": 8265, "mtime": 1751910893227, "results": "253", "hashOfConfig": "161"}, {"size": 7971, "mtime": 1752164690727, "results": "254", "hashOfConfig": "161"}, {"size": 3193, "mtime": 1751967855547, "results": "255", "hashOfConfig": "161"}, {"size": 333, "mtime": 1751973250881, "results": "256", "hashOfConfig": "161"}, {"size": 2100, "mtime": 1751903753069, "results": "257", "hashOfConfig": "161"}, {"size": 652, "mtime": 1752081874462, "results": "258", "hashOfConfig": "161"}, {"size": 2531, "mtime": 1751922060718, "results": "259", "hashOfConfig": "161"}, {"size": 3080, "mtime": 1752057854893, "results": "260", "hashOfConfig": "161"}, {"size": 3149, "mtime": 1751903789807, "results": "261", "hashOfConfig": "161"}, {"size": 5645, "mtime": 1752097783415, "results": "262", "hashOfConfig": "161"}, {"size": 18358, "mtime": 1752185768403, "results": "263", "hashOfConfig": "161"}, {"size": 1600, "mtime": 1752187894027, "results": "264", "hashOfConfig": "161"}, {"size": 2117, "mtime": 1752088205248, "results": "265", "hashOfConfig": "161"}, {"size": 618, "mtime": 1752081794017, "results": "266", "hashOfConfig": "161"}, {"size": 1291, "mtime": 1752136965687, "results": "267", "hashOfConfig": "161"}, {"size": 4535, "mtime": 1752187951211, "results": "268", "hashOfConfig": "161"}, {"size": 728, "mtime": 1752094197586, "results": "269", "hashOfConfig": "161"}, {"size": 22320, "mtime": 1752179871466, "results": "270", "hashOfConfig": "161"}, {"size": 265, "mtime": 1752082579534, "results": "271", "hashOfConfig": "161"}, {"size": 815, "mtime": 1752131292533, "results": "272", "hashOfConfig": "161"}, {"size": 4236, "mtime": 1752134359685, "results": "273", "hashOfConfig": "161"}, {"size": 4688, "mtime": 1752185523737, "results": "274", "hashOfConfig": "161"}, {"size": 4534, "mtime": 1752173372224, "results": "275", "hashOfConfig": "161"}, {"size": 2354, "mtime": 1752134787059, "results": "276", "hashOfConfig": "161"}, {"size": 6923, "mtime": 1752187951328, "results": "277", "hashOfConfig": "161"}, {"size": 6172, "mtime": 1752187951349, "results": "278", "hashOfConfig": "161"}, {"size": 5697, "mtime": 1752156433527, "results": "279", "hashOfConfig": "161"}, {"size": 8499, "mtime": 1752187951364, "results": "280", "hashOfConfig": "161"}, {"size": 1234, "mtime": 1752134861252, "results": "281", "hashOfConfig": "161"}, {"size": 3259, "mtime": 1752179835778, "results": "282", "hashOfConfig": "161"}, {"size": 1471, "mtime": 1752176185844, "results": "283", "hashOfConfig": "161"}, {"size": 3450, "mtime": 1752188457589, "results": "284", "hashOfConfig": "161"}, {"size": 2666, "mtime": 1752188736106, "results": "285", "hashOfConfig": "161"}, {"size": 3595, "mtime": 1752108494011, "results": "286", "hashOfConfig": "161"}, {"size": 12282, "mtime": 1752134119038, "results": "287", "hashOfConfig": "161"}, {"size": 6399, "mtime": 1752168615462, "results": "288", "hashOfConfig": "161"}, {"size": 8747, "mtime": 1752171092748, "results": "289", "hashOfConfig": "161"}, {"size": 3069, "mtime": 1752134918558, "results": "290", "hashOfConfig": "161"}, {"size": 11542, "mtime": 1752104709327, "results": "291", "hashOfConfig": "161"}, {"size": 9720, "mtime": 1752135084899, "results": "292", "hashOfConfig": "161"}, {"size": 9162, "mtime": 1752101759112, "results": "293", "hashOfConfig": "161"}, {"size": 10775, "mtime": 1752134938528, "results": "294", "hashOfConfig": "161"}, {"size": 10294, "mtime": 1752134953711, "results": "295", "hashOfConfig": "161"}, {"size": 8188, "mtime": 1752135044952, "results": "296", "hashOfConfig": "161"}, {"size": 13241, "mtime": 1752181183197, "results": "297", "hashOfConfig": "161"}, {"size": 13678, "mtime": 1752184381977, "results": "298", "hashOfConfig": "161"}, {"size": 11571, "mtime": 1752106544784, "results": "299", "hashOfConfig": "161"}, {"size": 10197, "mtime": 1752101449686, "results": "300", "hashOfConfig": "161"}, {"size": 10909, "mtime": 1752106686169, "results": "301", "hashOfConfig": "161"}, {"size": 13755, "mtime": 1752158495276, "results": "302", "hashOfConfig": "161"}, {"size": 7092, "mtime": 1752101380133, "results": "303", "hashOfConfig": "161"}, {"size": 10361, "mtime": 1752105711820, "results": "304", "hashOfConfig": "161"}, {"size": 13958, "mtime": 1752105409705, "results": "305", "hashOfConfig": "161"}, {"size": 6126, "mtime": 1752107906239, "results": "306", "hashOfConfig": "161"}, {"size": 8577, "mtime": 1752107941253, "results": "307", "hashOfConfig": "161"}, {"size": 5892, "mtime": 1752105510737, "results": "308", "hashOfConfig": "161"}, {"size": 4299, "mtime": 1752105482769, "results": "309", "hashOfConfig": "161"}, {"size": 2082, "mtime": 1752105457295, "results": "310", "hashOfConfig": "161"}, {"size": 22313, "mtime": 1752157088911, "results": "311", "hashOfConfig": "161"}, {"size": 6152, "mtime": 1752173681612, "results": "312", "hashOfConfig": "161"}, {"size": 3229, "mtime": 1752162598829, "results": "313", "hashOfConfig": "161"}, {"size": 5483, "mtime": 1752164770944, "results": "314", "hashOfConfig": "161"}, {"size": 7858, "mtime": 1752108688612, "results": "315", "hashOfConfig": "161"}, {"size": 914, "mtime": 1752162040145, "results": "316", "hashOfConfig": "161"}, {"size": 4260, "mtime": 1752188039423, "results": "317", "hashOfConfig": "161"}, {"size": 1827, "mtime": 1752185549891, "results": "318", "hashOfConfig": "161"}, {"size": 780, "mtime": 1752185481144, "results": "319", "hashOfConfig": "161"}, {"filePath": "320", "messages": "321", "suppressedMessages": "322", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "18vo38s", {"filePath": "323", "messages": "324", "suppressedMessages": "325", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "326", "messages": "327", "suppressedMessages": "328", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "329", "messages": "330", "suppressedMessages": "331", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "332", "messages": "333", "suppressedMessages": "334", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "335", "messages": "336", "suppressedMessages": "337", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "338", "messages": "339", "suppressedMessages": "340", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "341", "messages": "342", "suppressedMessages": "343", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "344", "messages": "345", "suppressedMessages": "346", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "347", "messages": "348", "suppressedMessages": "349", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "350", "messages": "351", "suppressedMessages": "352", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "353", "messages": "354", "suppressedMessages": "355", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "356", "messages": "357", "suppressedMessages": "358", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 21, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "359", "messages": "360", "suppressedMessages": "361", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "362", "messages": "363", "suppressedMessages": "364", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "365", "messages": "366", "suppressedMessages": "367", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "368", "messages": "369", "suppressedMessages": "370", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "371", "messages": "372", "suppressedMessages": "373", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "374", "messages": "375", "suppressedMessages": "376", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "377", "messages": "378", "suppressedMessages": "379", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "380", "messages": "381", "suppressedMessages": "382", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "383", "messages": "384", "suppressedMessages": "385", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "386", "messages": "387", "suppressedMessages": "388", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "389", "messages": "390", "suppressedMessages": "391", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "392", "messages": "393", "suppressedMessages": "394", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "395", "messages": "396", "suppressedMessages": "397", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "398", "messages": "399", "suppressedMessages": "400", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "401", "messages": "402", "suppressedMessages": "403", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "404", "messages": "405", "suppressedMessages": "406", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "407", "messages": "408", "suppressedMessages": "409", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "410", "messages": "411", "suppressedMessages": "412", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "413", "messages": "414", "suppressedMessages": "415", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "416", "messages": "417", "suppressedMessages": "418", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "419", "messages": "420", "suppressedMessages": "421", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "422", "messages": "423", "suppressedMessages": "424", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "425", "messages": "426", "suppressedMessages": "427", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "428", "messages": "429", "suppressedMessages": "430", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "431", "messages": "432", "suppressedMessages": "433", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "434", "messages": "435", "suppressedMessages": "436", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "437", "messages": "438", "suppressedMessages": "439", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "440", "messages": "441", "suppressedMessages": "442", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "443", "messages": "444", "suppressedMessages": "445", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "446", "messages": "447", "suppressedMessages": "448", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "449", "messages": "450", "suppressedMessages": "451", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "452", "messages": "453", "suppressedMessages": "454", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "455", "messages": "456", "suppressedMessages": "457", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "458", "messages": "459", "suppressedMessages": "460", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "461", "messages": "462", "suppressedMessages": "463", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "464", "messages": "465", "suppressedMessages": "466", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "467", "messages": "468", "suppressedMessages": "469", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "470", "messages": "471", "suppressedMessages": "472", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "473", "messages": "474", "suppressedMessages": "475", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "476", "messages": "477", "suppressedMessages": "478", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "479", "messages": "480", "suppressedMessages": "481", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "482", "messages": "483", "suppressedMessages": "484", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "485", "messages": "486", "suppressedMessages": "487", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "488", "messages": "489", "suppressedMessages": "490", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "491", "messages": "492", "suppressedMessages": "493", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "494", "messages": "495", "suppressedMessages": "496", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "497", "messages": "498", "suppressedMessages": "499", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "500", "messages": "501", "suppressedMessages": "502", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "503", "messages": "504", "suppressedMessages": "505", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 12, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "506", "messages": "507", "suppressedMessages": "508", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "509", "messages": "510", "suppressedMessages": "511", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "512", "messages": "513", "suppressedMessages": "514", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "515", "messages": "516", "suppressedMessages": "517", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "518", "messages": "519", "suppressedMessages": "520", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "521", "messages": "522", "suppressedMessages": "523", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "524", "messages": "525", "suppressedMessages": "526", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "527", "messages": "528", "suppressedMessages": "529", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "530", "messages": "531", "suppressedMessages": "532", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "533", "messages": "534", "suppressedMessages": "535", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "536", "messages": "537", "suppressedMessages": "538", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "539", "messages": "540", "suppressedMessages": "541", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "542", "messages": "543", "suppressedMessages": "544", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "545", "messages": "546", "suppressedMessages": "547", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "548", "messages": "549", "suppressedMessages": "550", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "551", "messages": "552", "suppressedMessages": "553", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "554", "messages": "555", "suppressedMessages": "556", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "557", "messages": "558", "suppressedMessages": "559", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "560", "messages": "561", "suppressedMessages": "562", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "563", "messages": "564", "suppressedMessages": "565", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "566", "messages": "567", "suppressedMessages": "568", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "569", "messages": "570", "suppressedMessages": "571", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "572", "messages": "573", "suppressedMessages": "574", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "575", "messages": "576", "suppressedMessages": "577", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "578", "messages": "579", "suppressedMessages": "580", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "581", "messages": "582", "suppressedMessages": "583", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "584", "messages": "585", "suppressedMessages": "586", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "587", "messages": "588", "suppressedMessages": "589", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "590", "messages": "591", "suppressedMessages": "592", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "593", "messages": "594", "suppressedMessages": "595", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "596", "messages": "597", "suppressedMessages": "598", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "599", "messages": "600", "suppressedMessages": "601", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "602", "messages": "603", "suppressedMessages": "604", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "605", "messages": "606", "suppressedMessages": "607", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "608", "messages": "609", "suppressedMessages": "610", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "611", "messages": "612", "suppressedMessages": "613", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "614", "messages": "615", "suppressedMessages": "616", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "617", "messages": "618", "suppressedMessages": "619", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "620", "messages": "621", "suppressedMessages": "622", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "623", "messages": "624", "suppressedMessages": "625", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "626", "messages": "627", "suppressedMessages": "628", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 25, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "629", "messages": "630", "suppressedMessages": "631", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "632", "messages": "633", "suppressedMessages": "634", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "635", "messages": "636", "suppressedMessages": "637", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "638", "messages": "639", "suppressedMessages": "640", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "641", "messages": "642", "suppressedMessages": "643", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "644", "messages": "645", "suppressedMessages": "646", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "647", "messages": "648", "suppressedMessages": "649", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "650", "messages": "651", "suppressedMessages": "652", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "653", "messages": "654", "suppressedMessages": "655", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "656", "messages": "657", "suppressedMessages": "658", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "659", "messages": "660", "suppressedMessages": "661", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "662", "messages": "663", "suppressedMessages": "664", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "665", "messages": "666", "suppressedMessages": "667", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "668", "messages": "669", "suppressedMessages": "670", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "671", "messages": "672", "suppressedMessages": "673", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "674", "messages": "675", "suppressedMessages": "676", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "677", "messages": "678", "suppressedMessages": "679", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "680", "messages": "681", "suppressedMessages": "682", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "683", "messages": "684", "suppressedMessages": "685", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "686", "messages": "687", "suppressedMessages": "688", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "689", "messages": "690", "suppressedMessages": "691", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "692", "messages": "693", "suppressedMessages": "694", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "695", "messages": "696", "suppressedMessages": "697", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "698", "messages": "699", "suppressedMessages": "700", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "701", "messages": "702", "suppressedMessages": "703", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "704", "messages": "705", "suppressedMessages": "706", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "707", "messages": "708", "suppressedMessages": "709", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "710", "messages": "711", "suppressedMessages": "712", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "713", "messages": "714", "suppressedMessages": "715", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "716", "messages": "717", "suppressedMessages": "718", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "719", "messages": "720", "suppressedMessages": "721", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "722", "messages": "723", "suppressedMessages": "724", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "725", "messages": "726", "suppressedMessages": "727", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "728", "messages": "729", "suppressedMessages": "730", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "731", "messages": "732", "suppressedMessages": "733", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "734", "messages": "735", "suppressedMessages": "736", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "737", "messages": "738", "suppressedMessages": "739", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "740", "messages": "741", "suppressedMessages": "742", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "743", "messages": "744", "suppressedMessages": "745", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "746", "messages": "747", "suppressedMessages": "748", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "749", "messages": "750", "suppressedMessages": "751", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "752", "messages": "753", "suppressedMessages": "754", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "755", "messages": "756", "suppressedMessages": "757", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "758", "messages": "759", "suppressedMessages": "760", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "761", "messages": "762", "suppressedMessages": "763", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "764", "messages": "765", "suppressedMessages": "766", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "767", "messages": "768", "suppressedMessages": "769", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "770", "messages": "771", "suppressedMessages": "772", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "773", "messages": "774", "suppressedMessages": "775", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "776", "messages": "777", "suppressedMessages": "778", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "779", "messages": "780", "suppressedMessages": "781", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "782", "messages": "783", "suppressedMessages": "784", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "785", "messages": "786", "suppressedMessages": "787", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "788", "messages": "789", "suppressedMessages": "790", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "791", "messages": "792", "suppressedMessages": "793", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "794", "messages": "795", "suppressedMessages": "796", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\about\\page.tsx", ["797", "798", "799", "800", "801", "802", "803", "804", "805", "806", "807", "808"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\route.ts", ["809"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\[sessionId]\\route.ts", ["810"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\contact\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\[id]\\route.ts", ["811"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\route.ts", ["812", "813", "814", "815"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\[id]\\route.ts", ["816", "817"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\route.ts", ["818"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\route.ts", ["819", "820"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\webhooks\\stripe\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\legal\\page.tsx", ["821", "822", "823", "824", "825", "826", "827", "828", "829", "830", "831", "832", "833", "834", "835", "836", "837", "838", "839", "840", "841"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx", ["842", "843", "844", "845", "846", "847", "848", "849"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\professionals\\page.tsx", ["850", "851"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\CheckoutFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ConfettiAnimation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ContactFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FAQFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\FloatingCandy.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\OrderSuccessAnimation.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\ParallaxSection.tsx", ["852", "853"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\animations\\TestimonialFloatingCandies.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartItem.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\CartSummary.tsx", ["854"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\EmptyCart.tsx", ["855", "856", "857"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\cart\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutForm.tsx", ["858", "859"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\CheckoutProgress.tsx", ["860"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\OrderSummary.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\PaymentMethods.tsx", ["861", "862", "863"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Footer.tsx", ["864", "865"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Header.tsx", ["866", "867"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\AgeVerificationModal.tsx", ["868", "869", "870", "871", "872"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CartModal.tsx", ["873"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\CheckoutModal.tsx", ["874"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\modals\\ProductModal.tsx", ["875"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\FlavorSlider.tsx", ["876", "877", "878"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\ProductCard.tsx", ["879", "880", "881", "882", "883", "884", "885"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\product\\QuantitySelector.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\CheckoutModalProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CartSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\CheckoutSection.tsx", ["886", "887", "888"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ContactSection.tsx", ["889"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\FAQSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\HeroSection.tsx", ["890"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\ProductOverview.tsx", ["891", "892", "893", "894"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\sections\\TestimonialsSection.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Badge.tsx", ["895", "896", "897", "898", "899", "900"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Button.tsx", ["901", "902", "903", "904", "905", "906"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Card.tsx", ["907", "908", "909", "910", "911", "912"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Input.tsx", ["913", "914", "915", "916", "917", "918", "919", "920", "921", "922", "923", "924"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Loading.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Modal.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductImage.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\ProductSkeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Select.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\shuffle-cards.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Skeleton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\testimonial-cards.tsx", ["925", "926", "927"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\ui\\Toast.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-api.ts", ["928"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-cart.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-click-outside.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-customer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-debounce.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-intersection-observer.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-local-storage.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-products.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-scroll-position.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\use-window-size.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAgeVerification.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\animations.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\auth.ts", ["929"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\config.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\email.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\flavors.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\mock-data.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\prisma.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\stripe.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\uploadthing.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\validations.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\cart-store.ts", ["930"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\checkout-modal-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\customer-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\notification-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\product-store.ts", ["931"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\ui-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx", ["932", "933", "934", "935", "936", "937", "938", "939", "940", "941", "942", "943", "944", "945", "946", "947", "948", "949", "950", "951", "952", "953", "954", "955", "956"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\create\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\login\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\logout\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\me\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\register\\route.ts", ["957"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-db\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx", ["958", "959", "960", "961", "962"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\hooks\\useAuthInit.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\middleware.ts", ["963", "964"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\stores\\auth-store.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\route.ts", ["965"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\user\\route.ts", ["966"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\bulk\\route.ts", ["967", "968", "969", "970"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\route.ts", ["971"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\[variantId]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\seed\\route.ts", ["972"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test\\database\\route.ts", ["973"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-orders\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-prisma\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts", ["974", "975"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\[filename]\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\user\\profile\\route.ts", ["976"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx", ["977", "978", "979"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx", ["980", "981", "982", "983"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx", ["984", "985", "986", "987"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx", ["988", "989"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerDetails.tsx", ["990"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\CustomerList.tsx", ["991", "992"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardStats.tsx", ["993", "994"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\DashboardTest.tsx", ["995", "996"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageManager.tsx", ["997", "998"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ImageUpload.tsx", ["999", "1000", "1001"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderDetails.tsx", ["1002", "1003"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\OrderList.tsx", ["1004"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\PricingTiers.tsx", ["1005"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductDetails.tsx", ["1006", "1007", "1008"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductForm.tsx", ["1009", "1010", "1011"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductFormAdvanced.tsx", ["1012", "1013", "1014", "1015", "1016"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ProductList.tsx", ["1017", "1018", "1019"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\RoleManager.tsx", ["1020"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\Settings.tsx", ["1021", "1022", "1023", "1024", "1025"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleCustomerList.tsx", ["1026", "1027"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\SimpleOrderList.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\DashboardHeader.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\ui\\StatCard.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\admin\\VariantManager.tsx", ["1028", "1029", "1030", "1031", "1032"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripePaymentForm.tsx", ["1033", "1034", "1035", "1036"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\checkout\\StripeTestButton.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\debug\\StripeDebugPanel.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\profile\\UserOrders.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\providers\\StripeProvider.tsx", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\lib\\permissions.ts", ["1037", "1038", "1039", "1040", "1041", "1042", "1043"], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats-simple\\route.ts", [], [], "C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-stats\\route.ts", [], [], {"ruleId": "1044", "severity": 1, "message": "1045", "line": 25, "column": 26, "nodeType": "1046", "messageId": "1047", "suggestions": "1048"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 26, "column": 41, "nodeType": "1046", "messageId": "1047", "suggestions": "1049"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 64, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1050"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 64, "column": 28, "nodeType": "1046", "messageId": "1047", "suggestions": "1051"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 65, "column": 51, "nodeType": "1046", "messageId": "1047", "suggestions": "1052"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 74, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1053"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 79, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1054"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 82, "column": 37, "nodeType": "1046", "messageId": "1047", "suggestions": "1055"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 211, "column": 30, "nodeType": "1046", "messageId": "1047", "suggestions": "1056"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 227, "column": 38, "nodeType": "1046", "messageId": "1047", "suggestions": "1057"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 269, "column": 24, "nodeType": "1046", "messageId": "1047", "suggestions": "1058"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 311, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1059"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 72, "column": 46, "nodeType": "1062", "messageId": "1063", "endLine": 72, "endColumn": 49, "suggestions": "1064"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 27, "column": 16, "nodeType": "1062", "messageId": "1063", "endLine": 27, "endColumn": 19, "suggestions": "1065"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 69, "column": 59, "nodeType": "1062", "messageId": "1063", "endLine": 69, "endColumn": 62, "suggestions": "1066"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 15, "column": 18, "nodeType": "1062", "messageId": "1063", "endLine": 15, "endColumn": 21, "suggestions": "1067"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 72, "column": 15, "nodeType": "1062", "messageId": "1063", "endLine": 72, "endColumn": 18, "suggestions": "1068"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 132, "column": 44, "nodeType": "1062", "messageId": "1063", "endLine": 132, "endColumn": 47, "suggestions": "1069"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 213, "column": 56, "nodeType": "1062", "messageId": "1063", "endLine": 213, "endColumn": 59, "suggestions": "1070"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 76, "column": 56, "nodeType": "1062", "messageId": "1063", "endLine": 76, "endColumn": 59, "suggestions": "1071"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 115, "column": 50, "nodeType": "1062", "messageId": "1063", "endLine": 115, "endColumn": 53, "suggestions": "1072"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 39, "column": 31, "nodeType": "1062", "messageId": "1063", "endLine": 39, "endColumn": 34, "suggestions": "1073"}, {"ruleId": "1074", "severity": 1, "message": "1075", "line": 61, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 61, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1077", "line": 61, "column": 23, "nodeType": null, "messageId": "1076", "endLine": 61, "endColumn": 35}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 22, "column": 51, "nodeType": "1046", "messageId": "1047", "suggestions": "1078"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 61, "column": 25, "nodeType": "1046", "messageId": "1047", "suggestions": "1079"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 61, "column": 30, "nodeType": "1046", "messageId": "1047", "suggestions": "1080"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 62, "column": 53, "nodeType": "1046", "messageId": "1047", "suggestions": "1081"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 71, "column": 28, "nodeType": "1046", "messageId": "1047", "suggestions": "1082"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 76, "column": 25, "nodeType": "1046", "messageId": "1047", "suggestions": "1083"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 79, "column": 42, "nodeType": "1046", "messageId": "1047", "suggestions": "1084"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 96, "column": 34, "nodeType": "1046", "messageId": "1047", "suggestions": "1085"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 112, "column": 42, "nodeType": "1046", "messageId": "1047", "suggestions": "1086"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 156, "column": 44, "nodeType": "1046", "messageId": "1047", "suggestions": "1087"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 217, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1088"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 218, "column": 48, "nodeType": "1046", "messageId": "1047", "suggestions": "1089"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 225, "column": 39, "nodeType": "1046", "messageId": "1047", "suggestions": "1090"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 238, "column": 65, "nodeType": "1046", "messageId": "1047", "suggestions": "1091"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 239, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1092"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 239, "column": 65, "nodeType": "1046", "messageId": "1047", "suggestions": "1093"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 244, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1094"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 254, "column": 63, "nodeType": "1046", "messageId": "1047", "suggestions": "1095"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 256, "column": 47, "nodeType": "1046", "messageId": "1047", "suggestions": "1096"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 273, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1097"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 279, "column": 29, "nodeType": "1046", "messageId": "1047", "suggestions": "1098"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 23, "column": 58, "nodeType": "1062", "messageId": "1063", "endLine": 23, "endColumn": 61, "suggestions": "1099"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 24, "column": 58, "nodeType": "1062", "messageId": "1063", "endLine": 24, "endColumn": 61, "suggestions": "1100"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 55, "column": 41, "nodeType": "1062", "messageId": "1063", "endLine": 55, "endColumn": 44, "suggestions": "1101"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 124, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1102"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 141, "column": 19, "nodeType": "1105", "endLine": 150, "endColumn": 21}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 175, "column": 27, "nodeType": "1105", "endLine": 184, "endColumn": 29}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 254, "column": 19, "nodeType": "1105", "endLine": 261, "endColumn": 21}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 336, "column": 68, "nodeType": "1062", "messageId": "1063", "endLine": 336, "endColumn": 71, "suggestions": "1106"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 163, "column": 31, "nodeType": "1046", "messageId": "1047", "suggestions": "1107"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 239, "column": 27, "nodeType": "1046", "messageId": "1047", "suggestions": "1108"}, {"ruleId": "1109", "severity": 1, "message": "1110", "line": 94, "column": 9, "nodeType": "1111", "endLine": 94, "endColumn": 21}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 152, "column": 9, "nodeType": "1105", "endLine": 152, "endColumn": 75}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 80, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 80, "endColumn": 19}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 62, "column": 35, "nodeType": "1046", "messageId": "1047", "suggestions": "1113"}, {"ruleId": "1044", "severity": 1, "message": "1114", "line": 188, "column": 15, "nodeType": "1046", "messageId": "1047", "suggestions": "1115"}, {"ruleId": "1044", "severity": 1, "message": "1114", "line": 189, "column": 65, "nodeType": "1046", "messageId": "1047", "suggestions": "1116"}, {"ruleId": "1074", "severity": 1, "message": "1117", "line": 43, "column": 5, "nodeType": null, "messageId": "1076", "endLine": 43, "endColumn": 10}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 76, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 76, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1118", "line": 66, "column": 19, "nodeType": null, "messageId": "1076", "endLine": 66, "endColumn": 29}, {"ruleId": "1074", "severity": 1, "message": "1119", "line": 20, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 20, "endColumn": 12}, {"ruleId": "1074", "severity": 1, "message": "1120", "line": 24, "column": 17, "nodeType": null, "messageId": "1076", "endLine": 24, "endColumn": 26}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 354, "column": 31, "nodeType": "1046", "messageId": "1047", "suggestions": "1121"}, {"ruleId": "1074", "severity": 1, "message": "1122", "line": 7, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 7, "endColumn": 24}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 76, "column": 17, "nodeType": "1105", "endLine": 80, "endColumn": 19}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 90, "column": 17, "nodeType": "1105", "endLine": 94, "endColumn": 19}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 178, "column": 27, "nodeType": "1105", "endLine": 182, "endColumn": 29}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 59, "column": 31, "nodeType": "1046", "messageId": "1047", "suggestions": "1123"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 73, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1124"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 95, "column": 29, "nodeType": "1046", "messageId": "1047", "suggestions": "1125"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 102, "column": 29, "nodeType": "1046", "messageId": "1047", "suggestions": "1126"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 127, "column": 29, "nodeType": "1046", "messageId": "1047", "suggestions": "1127"}, {"ruleId": "1074", "severity": 1, "message": "1128", "line": 17, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 17, "endColumn": 20}, {"ruleId": "1074", "severity": 1, "message": "1129", "line": 24, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 24, "endColumn": 17}, {"ruleId": "1074", "severity": 1, "message": "1130", "line": 4, "column": 17, "nodeType": null, "messageId": "1076", "endLine": 4, "endColumn": 28}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 60, "column": 28, "nodeType": "1062", "messageId": "1063", "endLine": 60, "endColumn": 31, "suggestions": "1131"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 88, "column": 13, "nodeType": "1105", "endLine": 99, "endColumn": 15}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 90, "column": 37, "nodeType": "1062", "messageId": "1063", "endLine": 90, "endColumn": 40, "suggestions": "1132"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 28, "column": 36, "nodeType": "1062", "messageId": "1063", "endLine": 28, "endColumn": 39, "suggestions": "1133"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 28, "column": 44, "nodeType": "1062", "messageId": "1063", "endLine": 28, "endColumn": 47, "suggestions": "1134"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 33, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 33, "endColumn": 37, "suggestions": "1135"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 79, "column": 13, "nodeType": "1105", "endLine": 86, "endColumn": 15}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 199, "column": 36, "nodeType": "1062", "messageId": "1063", "endLine": 199, "endColumn": 39, "suggestions": "1136"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 199, "column": 48, "nodeType": "1062", "messageId": "1063", "endLine": 199, "endColumn": 51, "suggestions": "1137"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 206, "column": 32, "nodeType": "1062", "messageId": "1063", "endLine": 206, "endColumn": 35, "suggestions": "1138"}, {"ruleId": "1074", "severity": 1, "message": "1129", "line": 19, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 19, "endColumn": 19}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 104, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1139"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 215, "column": 39, "nodeType": "1046", "messageId": "1047", "suggestions": "1140"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 357, "column": 47, "nodeType": "1046", "messageId": "1047", "suggestions": "1141"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 195, "column": 19, "nodeType": "1105", "endLine": 203, "endColumn": 21}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 24, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 24, "endColumn": 37, "suggestions": "1142"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 34, "column": 37, "nodeType": "1062", "messageId": "1063", "endLine": 34, "endColumn": 40, "suggestions": "1143"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 157, "column": 21, "nodeType": "1105", "endLine": 164, "endColumn": 23}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 197, "column": 42, "nodeType": "1062", "messageId": "1063", "endLine": 197, "endColumn": 45, "suggestions": "1144"}, {"ruleId": "1074", "severity": 1, "message": "1145", "line": 144, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 144, "endColumn": 23}, {"ruleId": "1074", "severity": 1, "message": "1146", "line": 145, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 145, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1147", "line": 146, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 146, "endColumn": 27}, {"ruleId": "1074", "severity": 1, "message": "1148", "line": 147, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 147, "endColumn": 13}, {"ruleId": "1074", "severity": 1, "message": "1149", "line": 148, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 148, "endColumn": 18}, {"ruleId": "1074", "severity": 1, "message": "1150", "line": 149, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 149, "endColumn": 16}, {"ruleId": "1074", "severity": 1, "message": "1148", "line": 150, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 150, "endColumn": 13}, {"ruleId": "1074", "severity": 1, "message": "1149", "line": 151, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 151, "endColumn": 18}, {"ruleId": "1074", "severity": 1, "message": "1150", "line": 152, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 152, "endColumn": 16}, {"ruleId": "1074", "severity": 1, "message": "1145", "line": 153, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 153, "endColumn": 23}, {"ruleId": "1074", "severity": 1, "message": "1146", "line": 154, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 154, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1147", "line": 155, "column": 7, "nodeType": null, "messageId": "1076", "endLine": 155, "endColumn": 27}, {"ruleId": "1074", "severity": 1, "message": "1151", "line": 105, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 105, "endColumn": 27}, {"ruleId": "1074", "severity": 1, "message": "1152", "line": 106, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 106, "endColumn": 25}, {"ruleId": "1074", "severity": 1, "message": "1153", "line": 107, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 107, "endColumn": 31}, {"ruleId": "1074", "severity": 1, "message": "1154", "line": 108, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 108, "endColumn": 17}, {"ruleId": "1074", "severity": 1, "message": "1155", "line": 109, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 109, "endColumn": 22}, {"ruleId": "1074", "severity": 1, "message": "1156", "line": 110, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 110, "endColumn": 20}, {"ruleId": "1074", "severity": 1, "message": "1151", "line": 171, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 171, "endColumn": 31}, {"ruleId": "1074", "severity": 1, "message": "1152", "line": 172, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 172, "endColumn": 29}, {"ruleId": "1074", "severity": 1, "message": "1153", "line": 173, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 173, "endColumn": 35}, {"ruleId": "1074", "severity": 1, "message": "1154", "line": 174, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 174, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1155", "line": 175, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 175, "endColumn": 26}, {"ruleId": "1074", "severity": 1, "message": "1156", "line": 176, "column": 15, "nodeType": null, "messageId": "1076", "endLine": 176, "endColumn": 24}, {"ruleId": "1074", "severity": 1, "message": "1151", "line": 353, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 353, "endColumn": 29}, {"ruleId": "1074", "severity": 1, "message": "1152", "line": 354, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 354, "endColumn": 27}, {"ruleId": "1074", "severity": 1, "message": "1153", "line": 355, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 355, "endColumn": 33}, {"ruleId": "1074", "severity": 1, "message": "1154", "line": 356, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 356, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1155", "line": 357, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 357, "endColumn": 24}, {"ruleId": "1074", "severity": 1, "message": "1156", "line": 358, "column": 13, "nodeType": null, "messageId": "1076", "endLine": 358, "endColumn": 22}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 79, "column": 7, "nodeType": "1105", "endLine": 89, "endColumn": 9}, {"ruleId": "1044", "severity": 1, "message": "1114", "line": 91, "column": 9, "nodeType": "1046", "messageId": "1047", "suggestions": "1157"}, {"ruleId": "1044", "severity": 1, "message": "1114", "line": 91, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1158"}, {"ruleId": "1159", "severity": 1, "message": "1160", "line": 41, "column": 18, "nodeType": "1161", "endLine": 41, "endColumn": 33}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 80, "column": 26, "nodeType": "1062", "messageId": "1063", "endLine": 80, "endColumn": 29, "suggestions": "1162"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 81, "column": 60, "nodeType": "1062", "messageId": "1063", "endLine": 81, "endColumn": 63, "suggestions": "1163"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 56, "column": 37, "nodeType": "1062", "messageId": "1063", "endLine": 56, "endColumn": 40, "suggestions": "1164"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 33, "column": 58, "nodeType": "1062", "messageId": "1063", "endLine": 33, "endColumn": 61, "suggestions": "1165"}, {"ruleId": "1074", "severity": 1, "message": "1166", "line": 36, "column": 10, "nodeType": null, "messageId": "1076", "endLine": 36, "endColumn": 22}, {"ruleId": "1074", "severity": 1, "message": "1167", "line": 37, "column": 10, "nodeType": null, "messageId": "1076", "endLine": 37, "endColumn": 26}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 37, "column": 60, "nodeType": "1062", "messageId": "1063", "endLine": 37, "endColumn": 63, "suggestions": "1168"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 41, "column": 54, "nodeType": "1062", "messageId": "1063", "endLine": 41, "endColumn": 57, "suggestions": "1169"}, {"ruleId": "1159", "severity": 1, "message": "1170", "line": 81, "column": 6, "nodeType": "1171", "endLine": 81, "endColumn": 8, "suggestions": "1172"}, {"ruleId": "1074", "severity": 1, "message": "1173", "line": 85, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 85, "endColumn": 21}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 96, "column": 39, "nodeType": "1062", "messageId": "1063", "endLine": 96, "endColumn": 42, "suggestions": "1174"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 101, "column": 39, "nodeType": "1062", "messageId": "1063", "endLine": 101, "endColumn": 42, "suggestions": "1175"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 106, "column": 49, "nodeType": "1062", "messageId": "1063", "endLine": 106, "endColumn": 52, "suggestions": "1176"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 129, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 129, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1177", "line": 139, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 139, "endColumn": 26}, {"ruleId": "1074", "severity": 1, "message": "1178", "line": 156, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 156, "endColumn": 27}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 156, "column": 41, "nodeType": "1062", "messageId": "1063", "endLine": 156, "endColumn": 44, "suggestions": "1179"}, {"ruleId": "1074", "severity": 1, "message": "1180", "line": 161, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 161, "endColumn": 30}, {"ruleId": "1074", "severity": 1, "message": "1181", "line": 167, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 167, "endColumn": 24}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 167, "column": 35, "nodeType": "1062", "messageId": "1063", "endLine": 167, "endColumn": 38, "suggestions": "1182"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 189, "column": 33, "nodeType": "1062", "messageId": "1063", "endLine": 189, "endColumn": 36, "suggestions": "1183"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 194, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 194, "endColumn": 19}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 231, "column": 57, "nodeType": "1046", "messageId": "1047", "suggestions": "1184"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 243, "column": 35, "nodeType": "1046", "messageId": "1047", "suggestions": "1185"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 249, "column": 31, "nodeType": "1046", "messageId": "1047", "suggestions": "1186"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 259, "column": 36, "nodeType": "1046", "messageId": "1047", "suggestions": "1187"}, {"ruleId": "1074", "severity": 1, "message": "1188", "line": 281, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 281, "endColumn": 25}, {"ruleId": "1074", "severity": 1, "message": "1189", "line": 290, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 290, "endColumn": 14}, {"ruleId": "1074", "severity": 1, "message": "1190", "line": 65, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 65, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1173", "line": 95, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 95, "endColumn": 21}, {"ruleId": "1159", "severity": 1, "message": "1191", "line": 139, "column": 6, "nodeType": "1171", "endLine": 139, "endColumn": 27, "suggestions": "1192"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 160, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 160, "endColumn": 19}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 513, "column": 27, "nodeType": "1046", "messageId": "1047", "suggestions": "1193"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 572, "column": 35, "nodeType": "1105", "endLine": 576, "endColumn": 37}, {"ruleId": "1074", "severity": 1, "message": "1194", "line": 3, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 3, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1195", "line": 11, "column": 28, "nodeType": null, "messageId": "1076", "endLine": 11, "endColumn": 35}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 51, "column": 22, "nodeType": "1062", "messageId": "1063", "endLine": 51, "endColumn": 25, "suggestions": "1196"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 14, "column": 10, "nodeType": "1062", "messageId": "1063", "endLine": 14, "endColumn": 13, "suggestions": "1197"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 37, "column": 17, "nodeType": "1062", "messageId": "1063", "endLine": 37, "endColumn": 20, "suggestions": "1198"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 49, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 49, "endColumn": 37, "suggestions": "1199"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 88, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 88, "endColumn": 37, "suggestions": "1200"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 105, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 105, "endColumn": 37, "suggestions": "1201"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 147, "column": 30, "nodeType": "1062", "messageId": "1063", "endLine": 147, "endColumn": 33, "suggestions": "1202"}, {"ruleId": "1074", "severity": 1, "message": "1195", "line": 6, "column": 28, "nodeType": null, "messageId": "1076", "endLine": 6, "endColumn": 35}, {"ruleId": "1074", "severity": 1, "message": "1203", "line": 10, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 10, "endColumn": 17}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 110, "column": 15, "nodeType": "1062", "messageId": "1063", "endLine": 110, "endColumn": 18, "suggestions": "1204"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 110, "column": 23, "nodeType": "1062", "messageId": "1063", "endLine": 110, "endColumn": 26, "suggestions": "1205"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 11, "column": 87, "nodeType": "1062", "messageId": "1063", "endLine": 11, "endColumn": 90, "suggestions": "1206"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 47, "column": 21, "nodeType": "1062", "messageId": "1063", "endLine": 47, "endColumn": 24, "suggestions": "1207"}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 69, "column": 13, "nodeType": "1105", "endLine": 76, "endColumn": 15}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 89, "column": 20, "nodeType": "1046", "messageId": "1047", "suggestions": "1208"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 20, "column": 36, "nodeType": "1062", "messageId": "1063", "endLine": 20, "endColumn": 39, "suggestions": "1209"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 80, "column": 57, "nodeType": "1046", "messageId": "1047", "suggestions": "1210"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 123, "column": 47, "nodeType": "1046", "messageId": "1047", "suggestions": "1211"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 171, "column": 23, "nodeType": "1046", "messageId": "1047", "suggestions": "1212"}, {"ruleId": "1074", "severity": 1, "message": "1213", "line": 28, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 28, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1214", "line": 63, "column": 16, "nodeType": null, "messageId": "1076", "endLine": 63, "endColumn": 19}, {"ruleId": "1159", "severity": 1, "message": "1215", "line": 71, "column": 6, "nodeType": "1171", "endLine": 71, "endColumn": 17, "suggestions": "1216"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 100, "column": 49, "nodeType": "1046", "messageId": "1047", "suggestions": "1217"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 7, "column": 40, "nodeType": "1062", "messageId": "1063", "endLine": 7, "endColumn": 43, "suggestions": "1218"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 73, "column": 17, "nodeType": "1046", "messageId": "1047", "suggestions": "1219"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 29, "column": 11, "nodeType": "1062", "messageId": "1063", "endLine": 29, "endColumn": 14, "suggestions": "1220"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 34, "column": 11, "nodeType": "1062", "messageId": "1063", "endLine": 34, "endColumn": 14, "suggestions": "1221"}, {"ruleId": "1074", "severity": 1, "message": "1214", "line": 66, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 66, "endColumn": 17}, {"ruleId": "1074", "severity": 1, "message": "1214", "line": 64, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 64, "endColumn": 17}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 256, "column": 23, "nodeType": "1105", "endLine": 263, "endColumn": 25}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 325, "column": 31, "nodeType": "1046", "messageId": "1047", "suggestions": "1222"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 336, "column": 39, "nodeType": "1046", "messageId": "1047", "suggestions": "1223"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 130, "column": 27, "nodeType": "1046", "messageId": "1047", "suggestions": "1224"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 167, "column": 39, "nodeType": "1046", "messageId": "1047", "suggestions": "1225"}, {"ruleId": "1074", "severity": 1, "message": "1226", "line": 27, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 27, "endColumn": 12}, {"ruleId": "1159", "severity": 1, "message": "1227", "line": 120, "column": 6, "nodeType": "1171", "endLine": 120, "endColumn": 8, "suggestions": "1228"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 170, "column": 35, "nodeType": "1046", "messageId": "1047", "suggestions": "1229"}, {"ruleId": "1074", "severity": 1, "message": "1230", "line": 79, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 79, "endColumn": 27}, {"ruleId": "1074", "severity": 1, "message": "1231", "line": 119, "column": 9, "nodeType": null, "messageId": "1076", "endLine": 119, "endColumn": 22}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 260, "column": 40, "nodeType": "1046", "messageId": "1047", "suggestions": "1232"}, {"ruleId": "1074", "severity": 1, "message": "1233", "line": 4, "column": 10, "nodeType": null, "messageId": "1076", "endLine": 4, "endColumn": 16}, {"ruleId": "1074", "severity": 1, "message": "1234", "line": 4, "column": 19, "nodeType": null, "messageId": "1076", "endLine": 4, "endColumn": 33}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 81, "column": 34, "nodeType": "1062", "messageId": "1063", "endLine": 81, "endColumn": 37, "suggestions": "1235"}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 183, "column": 45, "nodeType": "1046", "messageId": "1047", "suggestions": "1236"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 41, "column": 29, "nodeType": "1062", "messageId": "1063", "endLine": 41, "endColumn": 32, "suggestions": "1237"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 228, "column": 38, "nodeType": null, "messageId": "1076", "endLine": 228, "endColumn": 43}, {"ruleId": "1103", "severity": 1, "message": "1104", "line": 241, "column": 25, "nodeType": "1105", "endLine": 245, "endColumn": 27}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 37, "column": 21, "nodeType": "1062", "messageId": "1063", "endLine": 37, "endColumn": 24, "suggestions": "1238"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 66, "column": 29, "nodeType": "1062", "messageId": "1063", "endLine": 66, "endColumn": 32, "suggestions": "1239"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 108, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 108, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 132, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 132, "endColumn": 19}, {"ruleId": "1044", "severity": 1, "message": "1045", "line": 326, "column": 41, "nodeType": "1046", "messageId": "1047", "suggestions": "1240"}, {"ruleId": "1074", "severity": 1, "message": "1234", "line": 4, "column": 19, "nodeType": null, "messageId": "1076", "endLine": 4, "endColumn": 33}, {"ruleId": "1074", "severity": 1, "message": "1214", "line": 36, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 36, "endColumn": 17}, {"ruleId": "1074", "severity": 1, "message": "1214", "line": 58, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 58, "endColumn": 17}, {"ruleId": "1074", "severity": 1, "message": "1241", "line": 9, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 9, "endColumn": 7}, {"ruleId": "1074", "severity": 1, "message": "1242", "line": 9, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 9, "endColumn": 7}, {"ruleId": "1074", "severity": 1, "message": "1243", "line": 10, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 10, "endColumn": 8}, {"ruleId": "1074", "severity": 1, "message": "1244", "line": 11, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 11, "endColumn": 9}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 44, "column": 52, "nodeType": "1062", "messageId": "1063", "endLine": 44, "endColumn": 55, "suggestions": "1245"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 53, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 53, "endColumn": 19}, {"ruleId": "1074", "severity": 1, "message": "1246", "line": 9, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 9, "endColumn": 14}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 20, "column": 12, "nodeType": "1062", "messageId": "1063", "endLine": 20, "endColumn": 15, "suggestions": "1247"}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 97, "column": 18, "nodeType": null, "messageId": "1076", "endLine": 97, "endColumn": 23}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 152, "column": 16, "nodeType": null, "messageId": "1076", "endLine": 152, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 185, "column": 16, "nodeType": null, "messageId": "1076", "endLine": 185, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 225, "column": 16, "nodeType": null, "messageId": "1076", "endLine": 225, "endColumn": 21}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 298, "column": 14, "nodeType": null, "messageId": "1076", "endLine": 298, "endColumn": 19}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 12, "column": 17, "nodeType": "1062", "messageId": "1063", "endLine": 12, "endColumn": 20, "suggestions": "1248"}, {"ruleId": "1074", "severity": 1, "message": "1119", "line": 16, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 16, "endColumn": 12}, {"ruleId": "1074", "severity": 1, "message": "1120", "line": 22, "column": 17, "nodeType": null, "messageId": "1076", "endLine": 22, "endColumn": 26}, {"ruleId": "1074", "severity": 1, "message": "1249", "line": 24, "column": 11, "nodeType": null, "messageId": "1076", "endLine": 24, "endColumn": 26}, {"ruleId": "1074", "severity": 1, "message": "1112", "line": 20, "column": 12, "nodeType": null, "messageId": "1076", "endLine": 20, "endColumn": 17}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 52, "column": 39, "nodeType": "1062", "messageId": "1063", "endLine": 52, "endColumn": 42, "suggestions": "1250"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 52, "column": 49, "nodeType": "1062", "messageId": "1063", "endLine": 52, "endColumn": 52, "suggestions": "1251"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 53, "column": 59, "nodeType": "1062", "messageId": "1063", "endLine": 53, "endColumn": 62, "suggestions": "1252"}, {"ruleId": "1060", "severity": 1, "message": "1061", "line": 71, "column": 19, "nodeType": "1062", "messageId": "1063", "endLine": 71, "endColumn": 22, "suggestions": "1253"}, {"ruleId": "1074", "severity": 1, "message": "1254", "line": 146, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 146, "endColumn": 15}, {"ruleId": "1074", "severity": 1, "message": "1255", "line": 147, "column": 3, "nodeType": null, "messageId": "1076", "endLine": 147, "endColumn": 11}, "react/no-unescaped-entities", "`'` can be escaped with `&apos;`, `&lsquo;`, `&#39;`, `&rsquo;`.", "JSXText", "unescapedEntityAlts", ["1256", "1257", "1258", "1259"], ["1260", "1261", "1262", "1263"], ["1264", "1265", "1266", "1267"], ["1268", "1269", "1270", "1271"], ["1272", "1273", "1274", "1275"], ["1276", "1277", "1278", "1279"], ["1280", "1281", "1282", "1283"], ["1284", "1285", "1286", "1287"], ["1288", "1289", "1290", "1291"], ["1292", "1293", "1294", "1295"], ["1296", "1297", "1298", "1299"], ["1300", "1301", "1302", "1303"], "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["1304", "1305"], ["1306", "1307"], ["1308", "1309"], ["1310", "1311"], ["1312", "1313"], ["1314", "1315"], ["1316", "1317"], ["1318", "1319"], ["1320", "1321"], ["1322", "1323"], "@typescript-eslint/no-unused-vars", "'variants' is assigned a value but never used.", "unusedVar", "'pricingTiers' is assigned a value but never used.", ["1324", "1325", "1326", "1327"], ["1328", "1329", "1330", "1331"], ["1332", "1333", "1334", "1335"], ["1336", "1337", "1338", "1339"], ["1340", "1341", "1342", "1343"], ["1344", "1345", "1346", "1347"], ["1348", "1349", "1350", "1351"], ["1352", "1353", "1354", "1355"], ["1356", "1357", "1358", "1359"], ["1360", "1361", "1362", "1363"], ["1364", "1365", "1366", "1367"], ["1368", "1369", "1370", "1371"], ["1372", "1373", "1374", "1375"], ["1376", "1377", "1378", "1379"], ["1380", "1381", "1382", "1383"], ["1384", "1385", "1386", "1387"], ["1388", "1389", "1390", "1391"], ["1392", "1393", "1394", "1395"], ["1396", "1397", "1398", "1399"], ["1400", "1401", "1402", "1403"], ["1404", "1405", "1406", "1407"], ["1408", "1409"], ["1410", "1411"], ["1412", "1413"], ["1414", "1415", "1416", "1417"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["1418", "1419"], ["1420", "1421", "1422", "1423"], ["1424", "1425", "1426", "1427"], "react-hooks/rules-of-hooks", "React Hook \"useTransform\" cannot be called inside a callback. React Hooks must be called in a React function component or a custom React Hook function.", "Identifier", "'error' is defined but never used.", ["1428", "1429", "1430", "1431"], "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", ["1432", "1433", "1434", "1435"], ["1436", "1437", "1438", "1439"], "'watch' is assigned a value but never used.", "'isUpcoming' is assigned a value but never used.", "'onSuccess' is defined but never used.", "'clearCart' is assigned a value but never used.", ["1440", "1441", "1442", "1443"], "'scrollToSection' is assigned a value but never used.", ["1444", "1445", "1446", "1447"], ["1448", "1449", "1450", "1451"], ["1452", "1453", "1454", "1455"], ["1456", "1457", "1458", "1459"], ["1460", "1461", "1462", "1463"], "'openModal' is assigned a value but never used.", "'customer' is assigned a value but never used.", "'ModalHeader' is defined but never used.", ["1464", "1465"], ["1466", "1467"], ["1468", "1469"], ["1470", "1471"], ["1472", "1473"], ["1474", "1475"], ["1476", "1477"], ["1478", "1479"], ["1480", "1481", "1482", "1483"], ["1484", "1485", "1486", "1487"], ["1488", "1489", "1490", "1491"], ["1492", "1493"], ["1494", "1495"], ["1496", "1497"], "'onAnimationStart' is assigned a value but never used.", "'onAnimationEnd' is assigned a value but never used.", "'onAnimationIteration' is assigned a value but never used.", "'onDrag' is assigned a value but never used.", "'onDragStart' is assigned a value but never used.", "'onDragEnd' is assigned a value but never used.", "'onAnimationStart' is defined but never used.", "'onAnimationEnd' is defined but never used.", "'onAnimationIteration' is defined but never used.", "'onDrag' is defined but never used.", "'onDragStart' is defined but never used.", "'onDragEnd' is defined but never used.", ["1498", "1499", "1500", "1501"], ["1502", "1503", "1504", "1505"], "react-hooks/exhaustive-deps", "React Hook useEffect has a spread element in its dependency array. This means we can't statically verify whether you've passed the correct dependencies.", "SpreadElement", ["1506", "1507"], ["1508", "1509"], ["1510", "1511"], ["1512", "1513"], "'customerView' is assigned a value but never used.", "'selectedCustomer' is assigned a value but never used.", ["1514", "1515"], ["1516", "1517"], "React Hook useEffect has a missing dependency: 'checkAuth'. Either include it or remove the dependency array.", "ArrayExpression", ["1518"], "'handleLogout' is assigned a value but never used.", ["1519", "1520"], ["1521", "1522"], ["1523", "1524"], "'handleQuickAction' is assigned a value but never used.", "'handleViewCustomer' is assigned a value but never used.", ["1525", "1526"], "'handleBackToCustomers' is assigned a value but never used.", "'handleViewOrder' is assigned a value but never used.", ["1527", "1528"], ["1529", "1530"], ["1531", "1532", "1533", "1534"], ["1535", "1536", "1537", "1538"], ["1539", "1540", "1541", "1542"], ["1543", "1544", "1545", "1546"], "'staggerContainer' is assigned a value but never used.", "'stats' is assigned a value but never used.", "'dbError' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchOrders'. Either include it or remove the dependency array.", ["1547"], ["1548", "1549", "1550", "1551"], "'AuthUser' is defined but never used.", "'request' is defined but never used.", ["1552", "1553"], ["1554", "1555"], ["1556", "1557"], ["1558", "1559"], ["1560", "1561"], ["1562", "1563"], ["1564", "1565"], "'result' is assigned a value but never used.", ["1566", "1567"], ["1568", "1569"], ["1570", "1571"], ["1572", "1573"], ["1574", "1575", "1576", "1577"], ["1578", "1579"], ["1580", "1581", "1582", "1583"], ["1584", "1585", "1586", "1587"], ["1588", "1589", "1590", "1591"], "'router' is assigned a value but never used.", "'err' is defined but never used.", "React Hook useEffect has a missing dependency: 'clearCart'. Either include it or remove the dependency array.", ["1592"], ["1593", "1594", "1595", "1596"], ["1597", "1598"], ["1599", "1600", "1601", "1602"], ["1603", "1604"], ["1605", "1606"], ["1607", "1608", "1609", "1610"], ["1611", "1612", "1613", "1614"], ["1615", "1616", "1617", "1618"], ["1619", "1620", "1621", "1622"], "'maxImages' is assigned a value but never used.", "React Hook useCallback has a missing dependency: 'handleFileUpload'. Either include it or remove the dependency array.", ["1623"], ["1624", "1625", "1626", "1627"], "'handleStatusChange' is assigned a value but never used.", "'statusOptions' is assigned a value but never used.", ["1628", "1629", "1630", "1631"], "'motion' is defined but never used.", "'ProductVariant' is defined but never used.", ["1632", "1633"], ["1634", "1635", "1636", "1637"], ["1638", "1639"], ["1640", "1641"], ["1642", "1643"], ["1644", "1645", "1646", "1647"], "'Star' is defined but never used.", "'Mail' is defined but never used.", "'Globe' is defined but never used.", "'Shield' is defined but never used.", ["1648", "1649"], "'ShoppingBag' is defined but never used.", ["1650", "1651"], ["1652", "1653"], "'addNotification' is assigned a value but never used.", ["1654", "1655"], ["1656", "1657"], ["1658", "1659"], ["1660", "1661"], "'requiredRole' is defined but never used.", "'fallback' is defined but never used.", {"messageId": "1662", "data": "1663", "fix": "1664", "desc": "1665"}, {"messageId": "1662", "data": "1666", "fix": "1667", "desc": "1668"}, {"messageId": "1662", "data": "1669", "fix": "1670", "desc": "1671"}, {"messageId": "1662", "data": "1672", "fix": "1673", "desc": "1674"}, {"messageId": "1662", "data": "1675", "fix": "1676", "desc": "1665"}, {"messageId": "1662", "data": "1677", "fix": "1678", "desc": "1668"}, {"messageId": "1662", "data": "1679", "fix": "1680", "desc": "1671"}, {"messageId": "1662", "data": "1681", "fix": "1682", "desc": "1674"}, {"messageId": "1662", "data": "1683", "fix": "1684", "desc": "1665"}, {"messageId": "1662", "data": "1685", "fix": "1686", "desc": "1668"}, {"messageId": "1662", "data": "1687", "fix": "1688", "desc": "1671"}, {"messageId": "1662", "data": "1689", "fix": "1690", "desc": "1674"}, {"messageId": "1662", "data": "1691", "fix": "1692", "desc": "1665"}, {"messageId": "1662", "data": "1693", "fix": "1694", "desc": "1668"}, {"messageId": "1662", "data": "1695", "fix": "1696", "desc": "1671"}, {"messageId": "1662", "data": "1697", "fix": "1698", "desc": "1674"}, {"messageId": "1662", "data": "1699", "fix": "1700", "desc": "1665"}, {"messageId": "1662", "data": "1701", "fix": "1702", "desc": "1668"}, {"messageId": "1662", "data": "1703", "fix": "1704", "desc": "1671"}, {"messageId": "1662", "data": "1705", "fix": "1706", "desc": "1674"}, {"messageId": "1662", "data": "1707", "fix": "1708", "desc": "1665"}, {"messageId": "1662", "data": "1709", "fix": "1710", "desc": "1668"}, {"messageId": "1662", "data": "1711", "fix": "1712", "desc": "1671"}, {"messageId": "1662", "data": "1713", "fix": "1714", "desc": "1674"}, {"messageId": "1662", "data": "1715", "fix": "1716", "desc": "1665"}, {"messageId": "1662", "data": "1717", "fix": "1718", "desc": "1668"}, {"messageId": "1662", "data": "1719", "fix": "1720", "desc": "1671"}, {"messageId": "1662", "data": "1721", "fix": "1722", "desc": "1674"}, {"messageId": "1662", "data": "1723", "fix": "1724", "desc": "1665"}, {"messageId": "1662", "data": "1725", "fix": "1726", "desc": "1668"}, {"messageId": "1662", "data": "1727", "fix": "1728", "desc": "1671"}, {"messageId": "1662", "data": "1729", "fix": "1730", "desc": "1674"}, {"messageId": "1662", "data": "1731", "fix": "1732", "desc": "1665"}, {"messageId": "1662", "data": "1733", "fix": "1734", "desc": "1668"}, {"messageId": "1662", "data": "1735", "fix": "1736", "desc": "1671"}, {"messageId": "1662", "data": "1737", "fix": "1738", "desc": "1674"}, {"messageId": "1662", "data": "1739", "fix": "1740", "desc": "1665"}, {"messageId": "1662", "data": "1741", "fix": "1742", "desc": "1668"}, {"messageId": "1662", "data": "1743", "fix": "1744", "desc": "1671"}, {"messageId": "1662", "data": "1745", "fix": "1746", "desc": "1674"}, {"messageId": "1662", "data": "1747", "fix": "1748", "desc": "1665"}, {"messageId": "1662", "data": "1749", "fix": "1750", "desc": "1668"}, {"messageId": "1662", "data": "1751", "fix": "1752", "desc": "1671"}, {"messageId": "1662", "data": "1753", "fix": "1754", "desc": "1674"}, {"messageId": "1662", "data": "1755", "fix": "1756", "desc": "1665"}, {"messageId": "1662", "data": "1757", "fix": "1758", "desc": "1668"}, {"messageId": "1662", "data": "1759", "fix": "1760", "desc": "1671"}, {"messageId": "1662", "data": "1761", "fix": "1762", "desc": "1674"}, {"messageId": "1763", "fix": "1764", "desc": "1765"}, {"messageId": "1766", "fix": "1767", "desc": "1768"}, {"messageId": "1763", "fix": "1769", "desc": "1765"}, {"messageId": "1766", "fix": "1770", "desc": "1768"}, {"messageId": "1763", "fix": "1771", "desc": "1765"}, {"messageId": "1766", "fix": "1772", "desc": "1768"}, {"messageId": "1763", "fix": "1773", "desc": "1765"}, {"messageId": "1766", "fix": "1774", "desc": "1768"}, {"messageId": "1763", "fix": "1775", "desc": "1765"}, {"messageId": "1766", "fix": "1776", "desc": "1768"}, {"messageId": "1763", "fix": "1777", "desc": "1765"}, {"messageId": "1766", "fix": "1778", "desc": "1768"}, {"messageId": "1763", "fix": "1779", "desc": "1765"}, {"messageId": "1766", "fix": "1780", "desc": "1768"}, {"messageId": "1763", "fix": "1781", "desc": "1765"}, {"messageId": "1766", "fix": "1782", "desc": "1768"}, {"messageId": "1763", "fix": "1783", "desc": "1765"}, {"messageId": "1766", "fix": "1784", "desc": "1768"}, {"messageId": "1763", "fix": "1785", "desc": "1765"}, {"messageId": "1766", "fix": "1786", "desc": "1768"}, {"messageId": "1662", "data": "1787", "fix": "1788", "desc": "1665"}, {"messageId": "1662", "data": "1789", "fix": "1790", "desc": "1668"}, {"messageId": "1662", "data": "1791", "fix": "1792", "desc": "1671"}, {"messageId": "1662", "data": "1793", "fix": "1794", "desc": "1674"}, {"messageId": "1662", "data": "1795", "fix": "1796", "desc": "1665"}, {"messageId": "1662", "data": "1797", "fix": "1798", "desc": "1668"}, {"messageId": "1662", "data": "1799", "fix": "1800", "desc": "1671"}, {"messageId": "1662", "data": "1801", "fix": "1802", "desc": "1674"}, {"messageId": "1662", "data": "1803", "fix": "1804", "desc": "1665"}, {"messageId": "1662", "data": "1805", "fix": "1806", "desc": "1668"}, {"messageId": "1662", "data": "1807", "fix": "1808", "desc": "1671"}, {"messageId": "1662", "data": "1809", "fix": "1810", "desc": "1674"}, {"messageId": "1662", "data": "1811", "fix": "1812", "desc": "1665"}, {"messageId": "1662", "data": "1813", "fix": "1814", "desc": "1668"}, {"messageId": "1662", "data": "1815", "fix": "1816", "desc": "1671"}, {"messageId": "1662", "data": "1817", "fix": "1818", "desc": "1674"}, {"messageId": "1662", "data": "1819", "fix": "1820", "desc": "1665"}, {"messageId": "1662", "data": "1821", "fix": "1822", "desc": "1668"}, {"messageId": "1662", "data": "1823", "fix": "1824", "desc": "1671"}, {"messageId": "1662", "data": "1825", "fix": "1826", "desc": "1674"}, {"messageId": "1662", "data": "1827", "fix": "1828", "desc": "1665"}, {"messageId": "1662", "data": "1829", "fix": "1830", "desc": "1668"}, {"messageId": "1662", "data": "1831", "fix": "1832", "desc": "1671"}, {"messageId": "1662", "data": "1833", "fix": "1834", "desc": "1674"}, {"messageId": "1662", "data": "1835", "fix": "1836", "desc": "1665"}, {"messageId": "1662", "data": "1837", "fix": "1838", "desc": "1668"}, {"messageId": "1662", "data": "1839", "fix": "1840", "desc": "1671"}, {"messageId": "1662", "data": "1841", "fix": "1842", "desc": "1674"}, {"messageId": "1662", "data": "1843", "fix": "1844", "desc": "1665"}, {"messageId": "1662", "data": "1845", "fix": "1846", "desc": "1668"}, {"messageId": "1662", "data": "1847", "fix": "1848", "desc": "1671"}, {"messageId": "1662", "data": "1849", "fix": "1850", "desc": "1674"}, {"messageId": "1662", "data": "1851", "fix": "1852", "desc": "1665"}, {"messageId": "1662", "data": "1853", "fix": "1854", "desc": "1668"}, {"messageId": "1662", "data": "1855", "fix": "1856", "desc": "1671"}, {"messageId": "1662", "data": "1857", "fix": "1858", "desc": "1674"}, {"messageId": "1662", "data": "1859", "fix": "1860", "desc": "1665"}, {"messageId": "1662", "data": "1861", "fix": "1862", "desc": "1668"}, {"messageId": "1662", "data": "1863", "fix": "1864", "desc": "1671"}, {"messageId": "1662", "data": "1865", "fix": "1866", "desc": "1674"}, {"messageId": "1662", "data": "1867", "fix": "1868", "desc": "1665"}, {"messageId": "1662", "data": "1869", "fix": "1870", "desc": "1668"}, {"messageId": "1662", "data": "1871", "fix": "1872", "desc": "1671"}, {"messageId": "1662", "data": "1873", "fix": "1874", "desc": "1674"}, {"messageId": "1662", "data": "1875", "fix": "1876", "desc": "1665"}, {"messageId": "1662", "data": "1877", "fix": "1878", "desc": "1668"}, {"messageId": "1662", "data": "1879", "fix": "1880", "desc": "1671"}, {"messageId": "1662", "data": "1881", "fix": "1882", "desc": "1674"}, {"messageId": "1662", "data": "1883", "fix": "1884", "desc": "1665"}, {"messageId": "1662", "data": "1885", "fix": "1886", "desc": "1668"}, {"messageId": "1662", "data": "1887", "fix": "1888", "desc": "1671"}, {"messageId": "1662", "data": "1889", "fix": "1890", "desc": "1674"}, {"messageId": "1662", "data": "1891", "fix": "1892", "desc": "1665"}, {"messageId": "1662", "data": "1893", "fix": "1894", "desc": "1668"}, {"messageId": "1662", "data": "1895", "fix": "1896", "desc": "1671"}, {"messageId": "1662", "data": "1897", "fix": "1898", "desc": "1674"}, {"messageId": "1662", "data": "1899", "fix": "1900", "desc": "1665"}, {"messageId": "1662", "data": "1901", "fix": "1902", "desc": "1668"}, {"messageId": "1662", "data": "1903", "fix": "1904", "desc": "1671"}, {"messageId": "1662", "data": "1905", "fix": "1906", "desc": "1674"}, {"messageId": "1662", "data": "1907", "fix": "1908", "desc": "1665"}, {"messageId": "1662", "data": "1909", "fix": "1910", "desc": "1668"}, {"messageId": "1662", "data": "1911", "fix": "1912", "desc": "1671"}, {"messageId": "1662", "data": "1913", "fix": "1914", "desc": "1674"}, {"messageId": "1662", "data": "1915", "fix": "1916", "desc": "1665"}, {"messageId": "1662", "data": "1917", "fix": "1918", "desc": "1668"}, {"messageId": "1662", "data": "1919", "fix": "1920", "desc": "1671"}, {"messageId": "1662", "data": "1921", "fix": "1922", "desc": "1674"}, {"messageId": "1662", "data": "1923", "fix": "1924", "desc": "1665"}, {"messageId": "1662", "data": "1925", "fix": "1926", "desc": "1668"}, {"messageId": "1662", "data": "1927", "fix": "1928", "desc": "1671"}, {"messageId": "1662", "data": "1929", "fix": "1930", "desc": "1674"}, {"messageId": "1662", "data": "1931", "fix": "1932", "desc": "1665"}, {"messageId": "1662", "data": "1933", "fix": "1934", "desc": "1668"}, {"messageId": "1662", "data": "1935", "fix": "1936", "desc": "1671"}, {"messageId": "1662", "data": "1937", "fix": "1938", "desc": "1674"}, {"messageId": "1662", "data": "1939", "fix": "1940", "desc": "1665"}, {"messageId": "1662", "data": "1941", "fix": "1942", "desc": "1668"}, {"messageId": "1662", "data": "1943", "fix": "1944", "desc": "1671"}, {"messageId": "1662", "data": "1945", "fix": "1946", "desc": "1674"}, {"messageId": "1662", "data": "1947", "fix": "1948", "desc": "1665"}, {"messageId": "1662", "data": "1949", "fix": "1950", "desc": "1668"}, {"messageId": "1662", "data": "1951", "fix": "1952", "desc": "1671"}, {"messageId": "1662", "data": "1953", "fix": "1954", "desc": "1674"}, {"messageId": "1763", "fix": "1955", "desc": "1765"}, {"messageId": "1766", "fix": "1956", "desc": "1768"}, {"messageId": "1763", "fix": "1957", "desc": "1765"}, {"messageId": "1766", "fix": "1958", "desc": "1768"}, {"messageId": "1763", "fix": "1959", "desc": "1765"}, {"messageId": "1766", "fix": "1960", "desc": "1768"}, {"messageId": "1662", "data": "1961", "fix": "1962", "desc": "1665"}, {"messageId": "1662", "data": "1963", "fix": "1964", "desc": "1668"}, {"messageId": "1662", "data": "1965", "fix": "1966", "desc": "1671"}, {"messageId": "1662", "data": "1967", "fix": "1968", "desc": "1674"}, {"messageId": "1763", "fix": "1969", "desc": "1765"}, {"messageId": "1766", "fix": "1970", "desc": "1768"}, {"messageId": "1662", "data": "1971", "fix": "1972", "desc": "1665"}, {"messageId": "1662", "data": "1973", "fix": "1974", "desc": "1668"}, {"messageId": "1662", "data": "1975", "fix": "1976", "desc": "1671"}, {"messageId": "1662", "data": "1977", "fix": "1978", "desc": "1674"}, {"messageId": "1662", "data": "1979", "fix": "1980", "desc": "1665"}, {"messageId": "1662", "data": "1981", "fix": "1982", "desc": "1668"}, {"messageId": "1662", "data": "1983", "fix": "1984", "desc": "1671"}, {"messageId": "1662", "data": "1985", "fix": "1986", "desc": "1674"}, {"messageId": "1662", "data": "1987", "fix": "1988", "desc": "1665"}, {"messageId": "1662", "data": "1989", "fix": "1990", "desc": "1668"}, {"messageId": "1662", "data": "1991", "fix": "1992", "desc": "1671"}, {"messageId": "1662", "data": "1993", "fix": "1994", "desc": "1674"}, {"messageId": "1662", "data": "1995", "fix": "1996", "desc": "1997"}, {"messageId": "1662", "data": "1998", "fix": "1999", "desc": "2000"}, {"messageId": "1662", "data": "2001", "fix": "2002", "desc": "2003"}, {"messageId": "1662", "data": "2004", "fix": "2005", "desc": "2006"}, {"messageId": "1662", "data": "2007", "fix": "2008", "desc": "1997"}, {"messageId": "1662", "data": "2009", "fix": "2010", "desc": "2000"}, {"messageId": "1662", "data": "2011", "fix": "2012", "desc": "2003"}, {"messageId": "1662", "data": "2013", "fix": "2014", "desc": "2006"}, {"messageId": "1662", "data": "2015", "fix": "2016", "desc": "1665"}, {"messageId": "1662", "data": "2017", "fix": "2018", "desc": "1668"}, {"messageId": "1662", "data": "2019", "fix": "2020", "desc": "1671"}, {"messageId": "1662", "data": "2021", "fix": "2022", "desc": "1674"}, {"messageId": "1662", "data": "2023", "fix": "2024", "desc": "1665"}, {"messageId": "1662", "data": "2025", "fix": "2026", "desc": "1668"}, {"messageId": "1662", "data": "2027", "fix": "2028", "desc": "1671"}, {"messageId": "1662", "data": "2029", "fix": "2030", "desc": "1674"}, {"messageId": "1662", "data": "2031", "fix": "2032", "desc": "1665"}, {"messageId": "1662", "data": "2033", "fix": "2034", "desc": "1668"}, {"messageId": "1662", "data": "2035", "fix": "2036", "desc": "1671"}, {"messageId": "1662", "data": "2037", "fix": "2038", "desc": "1674"}, {"messageId": "1662", "data": "2039", "fix": "2040", "desc": "1665"}, {"messageId": "1662", "data": "2041", "fix": "2042", "desc": "1668"}, {"messageId": "1662", "data": "2043", "fix": "2044", "desc": "1671"}, {"messageId": "1662", "data": "2045", "fix": "2046", "desc": "1674"}, {"messageId": "1662", "data": "2047", "fix": "2048", "desc": "1665"}, {"messageId": "1662", "data": "2049", "fix": "2050", "desc": "1668"}, {"messageId": "1662", "data": "2051", "fix": "2052", "desc": "1671"}, {"messageId": "1662", "data": "2053", "fix": "2054", "desc": "1674"}, {"messageId": "1662", "data": "2055", "fix": "2056", "desc": "1665"}, {"messageId": "1662", "data": "2057", "fix": "2058", "desc": "1668"}, {"messageId": "1662", "data": "2059", "fix": "2060", "desc": "1671"}, {"messageId": "1662", "data": "2061", "fix": "2062", "desc": "1674"}, {"messageId": "1763", "fix": "2063", "desc": "1765"}, {"messageId": "1766", "fix": "2064", "desc": "1768"}, {"messageId": "1763", "fix": "2065", "desc": "1765"}, {"messageId": "1766", "fix": "2066", "desc": "1768"}, {"messageId": "1763", "fix": "2067", "desc": "1765"}, {"messageId": "1766", "fix": "2068", "desc": "1768"}, {"messageId": "1763", "fix": "2069", "desc": "1765"}, {"messageId": "1766", "fix": "2070", "desc": "1768"}, {"messageId": "1763", "fix": "2071", "desc": "1765"}, {"messageId": "1766", "fix": "2072", "desc": "1768"}, {"messageId": "1763", "fix": "2073", "desc": "1765"}, {"messageId": "1766", "fix": "2074", "desc": "1768"}, {"messageId": "1763", "fix": "2075", "desc": "1765"}, {"messageId": "1766", "fix": "2076", "desc": "1768"}, {"messageId": "1763", "fix": "2077", "desc": "1765"}, {"messageId": "1766", "fix": "2078", "desc": "1768"}, {"messageId": "1662", "data": "2079", "fix": "2080", "desc": "1665"}, {"messageId": "1662", "data": "2081", "fix": "2082", "desc": "1668"}, {"messageId": "1662", "data": "2083", "fix": "2084", "desc": "1671"}, {"messageId": "1662", "data": "2085", "fix": "2086", "desc": "1674"}, {"messageId": "1662", "data": "2087", "fix": "2088", "desc": "1665"}, {"messageId": "1662", "data": "2089", "fix": "2090", "desc": "1668"}, {"messageId": "1662", "data": "2091", "fix": "2092", "desc": "1671"}, {"messageId": "1662", "data": "2093", "fix": "2094", "desc": "1674"}, {"messageId": "1662", "data": "2095", "fix": "2096", "desc": "1665"}, {"messageId": "1662", "data": "2097", "fix": "2098", "desc": "1668"}, {"messageId": "1662", "data": "2099", "fix": "2100", "desc": "1671"}, {"messageId": "1662", "data": "2101", "fix": "2102", "desc": "1674"}, {"messageId": "1763", "fix": "2103", "desc": "1765"}, {"messageId": "1766", "fix": "2104", "desc": "1768"}, {"messageId": "1763", "fix": "2105", "desc": "1765"}, {"messageId": "1766", "fix": "2106", "desc": "1768"}, {"messageId": "1763", "fix": "2107", "desc": "1765"}, {"messageId": "1766", "fix": "2108", "desc": "1768"}, {"messageId": "1662", "data": "2109", "fix": "2110", "desc": "1997"}, {"messageId": "1662", "data": "2111", "fix": "2112", "desc": "2000"}, {"messageId": "1662", "data": "2113", "fix": "2114", "desc": "2003"}, {"messageId": "1662", "data": "2115", "fix": "2116", "desc": "2006"}, {"messageId": "1662", "data": "2117", "fix": "2118", "desc": "1997"}, {"messageId": "1662", "data": "2119", "fix": "2120", "desc": "2000"}, {"messageId": "1662", "data": "2121", "fix": "2122", "desc": "2003"}, {"messageId": "1662", "data": "2123", "fix": "2124", "desc": "2006"}, {"messageId": "1763", "fix": "2125", "desc": "1765"}, {"messageId": "1766", "fix": "2126", "desc": "1768"}, {"messageId": "1763", "fix": "2127", "desc": "1765"}, {"messageId": "1766", "fix": "2128", "desc": "1768"}, {"messageId": "1763", "fix": "2129", "desc": "1765"}, {"messageId": "1766", "fix": "2130", "desc": "1768"}, {"messageId": "1763", "fix": "2131", "desc": "1765"}, {"messageId": "1766", "fix": "2132", "desc": "1768"}, {"messageId": "1763", "fix": "2133", "desc": "1765"}, {"messageId": "1766", "fix": "2134", "desc": "1768"}, {"messageId": "1763", "fix": "2135", "desc": "1765"}, {"messageId": "1766", "fix": "2136", "desc": "1768"}, {"desc": "2137", "fix": "2138"}, {"messageId": "1763", "fix": "2139", "desc": "1765"}, {"messageId": "1766", "fix": "2140", "desc": "1768"}, {"messageId": "1763", "fix": "2141", "desc": "1765"}, {"messageId": "1766", "fix": "2142", "desc": "1768"}, {"messageId": "1763", "fix": "2143", "desc": "1765"}, {"messageId": "1766", "fix": "2144", "desc": "1768"}, {"messageId": "1763", "fix": "2145", "desc": "1765"}, {"messageId": "1766", "fix": "2146", "desc": "1768"}, {"messageId": "1763", "fix": "2147", "desc": "1765"}, {"messageId": "1766", "fix": "2148", "desc": "1768"}, {"messageId": "1763", "fix": "2149", "desc": "1765"}, {"messageId": "1766", "fix": "2150", "desc": "1768"}, {"messageId": "1662", "data": "2151", "fix": "2152", "desc": "1665"}, {"messageId": "1662", "data": "2153", "fix": "2154", "desc": "1668"}, {"messageId": "1662", "data": "2155", "fix": "2156", "desc": "1671"}, {"messageId": "1662", "data": "2157", "fix": "2158", "desc": "1674"}, {"messageId": "1662", "data": "2159", "fix": "2160", "desc": "1665"}, {"messageId": "1662", "data": "2161", "fix": "2162", "desc": "1668"}, {"messageId": "1662", "data": "2163", "fix": "2164", "desc": "1671"}, {"messageId": "1662", "data": "2165", "fix": "2166", "desc": "1674"}, {"messageId": "1662", "data": "2167", "fix": "2168", "desc": "1665"}, {"messageId": "1662", "data": "2169", "fix": "2170", "desc": "1668"}, {"messageId": "1662", "data": "2171", "fix": "2172", "desc": "1671"}, {"messageId": "1662", "data": "2173", "fix": "2174", "desc": "1674"}, {"messageId": "1662", "data": "2175", "fix": "2176", "desc": "1665"}, {"messageId": "1662", "data": "2177", "fix": "2178", "desc": "1668"}, {"messageId": "1662", "data": "2179", "fix": "2180", "desc": "1671"}, {"messageId": "1662", "data": "2181", "fix": "2182", "desc": "1674"}, {"desc": "2183", "fix": "2184"}, {"messageId": "1662", "data": "2185", "fix": "2186", "desc": "1665"}, {"messageId": "1662", "data": "2187", "fix": "2188", "desc": "1668"}, {"messageId": "1662", "data": "2189", "fix": "2190", "desc": "1671"}, {"messageId": "1662", "data": "2191", "fix": "2192", "desc": "1674"}, {"messageId": "1763", "fix": "2193", "desc": "1765"}, {"messageId": "1766", "fix": "2194", "desc": "1768"}, {"messageId": "1763", "fix": "2195", "desc": "1765"}, {"messageId": "1766", "fix": "2196", "desc": "1768"}, {"messageId": "1763", "fix": "2197", "desc": "1765"}, {"messageId": "1766", "fix": "2198", "desc": "1768"}, {"messageId": "1763", "fix": "2199", "desc": "1765"}, {"messageId": "1766", "fix": "2200", "desc": "1768"}, {"messageId": "1763", "fix": "2201", "desc": "1765"}, {"messageId": "1766", "fix": "2202", "desc": "1768"}, {"messageId": "1763", "fix": "2203", "desc": "1765"}, {"messageId": "1766", "fix": "2204", "desc": "1768"}, {"messageId": "1763", "fix": "2205", "desc": "1765"}, {"messageId": "1766", "fix": "2206", "desc": "1768"}, {"messageId": "1763", "fix": "2207", "desc": "1765"}, {"messageId": "1766", "fix": "2208", "desc": "1768"}, {"messageId": "1763", "fix": "2209", "desc": "1765"}, {"messageId": "1766", "fix": "2210", "desc": "1768"}, {"messageId": "1763", "fix": "2211", "desc": "1765"}, {"messageId": "1766", "fix": "2212", "desc": "1768"}, {"messageId": "1763", "fix": "2213", "desc": "1765"}, {"messageId": "1766", "fix": "2214", "desc": "1768"}, {"messageId": "1662", "data": "2215", "fix": "2216", "desc": "1665"}, {"messageId": "1662", "data": "2217", "fix": "2218", "desc": "1668"}, {"messageId": "1662", "data": "2219", "fix": "2220", "desc": "1671"}, {"messageId": "1662", "data": "2221", "fix": "2222", "desc": "1674"}, {"messageId": "1763", "fix": "2223", "desc": "1765"}, {"messageId": "1766", "fix": "2224", "desc": "1768"}, {"messageId": "1662", "data": "2225", "fix": "2226", "desc": "1665"}, {"messageId": "1662", "data": "2227", "fix": "2228", "desc": "1668"}, {"messageId": "1662", "data": "2229", "fix": "2230", "desc": "1671"}, {"messageId": "1662", "data": "2231", "fix": "2232", "desc": "1674"}, {"messageId": "1662", "data": "2233", "fix": "2234", "desc": "1665"}, {"messageId": "1662", "data": "2235", "fix": "2236", "desc": "1668"}, {"messageId": "1662", "data": "2237", "fix": "2238", "desc": "1671"}, {"messageId": "1662", "data": "2239", "fix": "2240", "desc": "1674"}, {"messageId": "1662", "data": "2241", "fix": "2242", "desc": "1665"}, {"messageId": "1662", "data": "2243", "fix": "2244", "desc": "1668"}, {"messageId": "1662", "data": "2245", "fix": "2246", "desc": "1671"}, {"messageId": "1662", "data": "2247", "fix": "2248", "desc": "1674"}, {"desc": "2249", "fix": "2250"}, {"messageId": "1662", "data": "2251", "fix": "2252", "desc": "1665"}, {"messageId": "1662", "data": "2253", "fix": "2254", "desc": "1668"}, {"messageId": "1662", "data": "2255", "fix": "2256", "desc": "1671"}, {"messageId": "1662", "data": "2257", "fix": "2258", "desc": "1674"}, {"messageId": "1763", "fix": "2259", "desc": "1765"}, {"messageId": "1766", "fix": "2260", "desc": "1768"}, {"messageId": "1662", "data": "2261", "fix": "2262", "desc": "1665"}, {"messageId": "1662", "data": "2263", "fix": "2264", "desc": "1668"}, {"messageId": "1662", "data": "2265", "fix": "2266", "desc": "1671"}, {"messageId": "1662", "data": "2267", "fix": "2268", "desc": "1674"}, {"messageId": "1763", "fix": "2269", "desc": "1765"}, {"messageId": "1766", "fix": "2270", "desc": "1768"}, {"messageId": "1763", "fix": "2271", "desc": "1765"}, {"messageId": "1766", "fix": "2272", "desc": "1768"}, {"messageId": "1662", "data": "2273", "fix": "2274", "desc": "1665"}, {"messageId": "1662", "data": "2275", "fix": "2276", "desc": "1668"}, {"messageId": "1662", "data": "2277", "fix": "2278", "desc": "1671"}, {"messageId": "1662", "data": "2279", "fix": "2280", "desc": "1674"}, {"messageId": "1662", "data": "2281", "fix": "2282", "desc": "1665"}, {"messageId": "1662", "data": "2283", "fix": "2284", "desc": "1668"}, {"messageId": "1662", "data": "2285", "fix": "2286", "desc": "1671"}, {"messageId": "1662", "data": "2287", "fix": "2288", "desc": "1674"}, {"messageId": "1662", "data": "2289", "fix": "2290", "desc": "1665"}, {"messageId": "1662", "data": "2291", "fix": "2292", "desc": "1668"}, {"messageId": "1662", "data": "2293", "fix": "2294", "desc": "1671"}, {"messageId": "1662", "data": "2295", "fix": "2296", "desc": "1674"}, {"messageId": "1662", "data": "2297", "fix": "2298", "desc": "1665"}, {"messageId": "1662", "data": "2299", "fix": "2300", "desc": "1668"}, {"messageId": "1662", "data": "2301", "fix": "2302", "desc": "1671"}, {"messageId": "1662", "data": "2303", "fix": "2304", "desc": "1674"}, {"desc": "2305", "fix": "2306"}, {"messageId": "1662", "data": "2307", "fix": "2308", "desc": "1665"}, {"messageId": "1662", "data": "2309", "fix": "2310", "desc": "1668"}, {"messageId": "1662", "data": "2311", "fix": "2312", "desc": "1671"}, {"messageId": "1662", "data": "2313", "fix": "2314", "desc": "1674"}, {"messageId": "1662", "data": "2315", "fix": "2316", "desc": "1665"}, {"messageId": "1662", "data": "2317", "fix": "2318", "desc": "1668"}, {"messageId": "1662", "data": "2319", "fix": "2320", "desc": "1671"}, {"messageId": "1662", "data": "2321", "fix": "2322", "desc": "1674"}, {"messageId": "1763", "fix": "2323", "desc": "1765"}, {"messageId": "1766", "fix": "2324", "desc": "1768"}, {"messageId": "1662", "data": "2325", "fix": "2326", "desc": "1665"}, {"messageId": "1662", "data": "2327", "fix": "2328", "desc": "1668"}, {"messageId": "1662", "data": "2329", "fix": "2330", "desc": "1671"}, {"messageId": "1662", "data": "2331", "fix": "2332", "desc": "1674"}, {"messageId": "1763", "fix": "2333", "desc": "1765"}, {"messageId": "1766", "fix": "2334", "desc": "1768"}, {"messageId": "1763", "fix": "2335", "desc": "1765"}, {"messageId": "1766", "fix": "2336", "desc": "1768"}, {"messageId": "1763", "fix": "2337", "desc": "1765"}, {"messageId": "1766", "fix": "2338", "desc": "1768"}, {"messageId": "1662", "data": "2339", "fix": "2340", "desc": "1665"}, {"messageId": "1662", "data": "2341", "fix": "2342", "desc": "1668"}, {"messageId": "1662", "data": "2343", "fix": "2344", "desc": "1671"}, {"messageId": "1662", "data": "2345", "fix": "2346", "desc": "1674"}, {"messageId": "1763", "fix": "2347", "desc": "1765"}, {"messageId": "1766", "fix": "2348", "desc": "1768"}, {"messageId": "1763", "fix": "2349", "desc": "1765"}, {"messageId": "1766", "fix": "2350", "desc": "1768"}, {"messageId": "1763", "fix": "2351", "desc": "1765"}, {"messageId": "1766", "fix": "2352", "desc": "1768"}, {"messageId": "1763", "fix": "2353", "desc": "1765"}, {"messageId": "1766", "fix": "2354", "desc": "1768"}, {"messageId": "1763", "fix": "2355", "desc": "1765"}, {"messageId": "1766", "fix": "2356", "desc": "1768"}, {"messageId": "1763", "fix": "2357", "desc": "1765"}, {"messageId": "1766", "fix": "2358", "desc": "1768"}, {"messageId": "1763", "fix": "2359", "desc": "1765"}, {"messageId": "1766", "fix": "2360", "desc": "1768"}, "replaceWithAlt", {"alt": "2361"}, {"range": "2362", "text": "2363"}, "Replace with `&apos;`.", {"alt": "2364"}, {"range": "2365", "text": "2366"}, "Replace with `&lsquo;`.", {"alt": "2367"}, {"range": "2368", "text": "2369"}, "Replace with `&#39;`.", {"alt": "2370"}, {"range": "2371", "text": "2372"}, "Replace with `&rsquo;`.", {"alt": "2361"}, {"range": "2373", "text": "2374"}, {"alt": "2364"}, {"range": "2375", "text": "2376"}, {"alt": "2367"}, {"range": "2377", "text": "2378"}, {"alt": "2370"}, {"range": "2379", "text": "2380"}, {"alt": "2361"}, {"range": "2381", "text": "2382"}, {"alt": "2364"}, {"range": "2383", "text": "2384"}, {"alt": "2367"}, {"range": "2385", "text": "2386"}, {"alt": "2370"}, {"range": "2387", "text": "2388"}, {"alt": "2361"}, {"range": "2389", "text": "2390"}, {"alt": "2364"}, {"range": "2391", "text": "2392"}, {"alt": "2367"}, {"range": "2393", "text": "2394"}, {"alt": "2370"}, {"range": "2395", "text": "2396"}, {"alt": "2361"}, {"range": "2397", "text": "2398"}, {"alt": "2364"}, {"range": "2399", "text": "2400"}, {"alt": "2367"}, {"range": "2401", "text": "2402"}, {"alt": "2370"}, {"range": "2403", "text": "2404"}, {"alt": "2361"}, {"range": "2405", "text": "2406"}, {"alt": "2364"}, {"range": "2407", "text": "2408"}, {"alt": "2367"}, {"range": "2409", "text": "2410"}, {"alt": "2370"}, {"range": "2411", "text": "2412"}, {"alt": "2361"}, {"range": "2413", "text": "2414"}, {"alt": "2364"}, {"range": "2415", "text": "2416"}, {"alt": "2367"}, {"range": "2417", "text": "2418"}, {"alt": "2370"}, {"range": "2419", "text": "2420"}, {"alt": "2361"}, {"range": "2421", "text": "2422"}, {"alt": "2364"}, {"range": "2423", "text": "2424"}, {"alt": "2367"}, {"range": "2425", "text": "2426"}, {"alt": "2370"}, {"range": "2427", "text": "2428"}, {"alt": "2361"}, {"range": "2429", "text": "2430"}, {"alt": "2364"}, {"range": "2431", "text": "2432"}, {"alt": "2367"}, {"range": "2433", "text": "2434"}, {"alt": "2370"}, {"range": "2435", "text": "2436"}, {"alt": "2361"}, {"range": "2437", "text": "2438"}, {"alt": "2364"}, {"range": "2439", "text": "2440"}, {"alt": "2367"}, {"range": "2441", "text": "2442"}, {"alt": "2370"}, {"range": "2443", "text": "2444"}, {"alt": "2361"}, {"range": "2445", "text": "2446"}, {"alt": "2364"}, {"range": "2447", "text": "2448"}, {"alt": "2367"}, {"range": "2449", "text": "2450"}, {"alt": "2370"}, {"range": "2451", "text": "2452"}, {"alt": "2361"}, {"range": "2453", "text": "2454"}, {"alt": "2364"}, {"range": "2455", "text": "2456"}, {"alt": "2367"}, {"range": "2457", "text": "2458"}, {"alt": "2370"}, {"range": "2459", "text": "2460"}, "suggestUnknown", {"range": "2461", "text": "2462"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "2463", "text": "2464"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "2465", "text": "2462"}, {"range": "2466", "text": "2464"}, {"range": "2467", "text": "2462"}, {"range": "2468", "text": "2464"}, {"range": "2469", "text": "2462"}, {"range": "2470", "text": "2464"}, {"range": "2471", "text": "2462"}, {"range": "2472", "text": "2464"}, {"range": "2473", "text": "2462"}, {"range": "2474", "text": "2464"}, {"range": "2475", "text": "2462"}, {"range": "2476", "text": "2464"}, {"range": "2477", "text": "2462"}, {"range": "2478", "text": "2464"}, {"range": "2479", "text": "2462"}, {"range": "2480", "text": "2464"}, {"range": "2481", "text": "2462"}, {"range": "2482", "text": "2464"}, {"alt": "2361"}, {"range": "2483", "text": "2484"}, {"alt": "2364"}, {"range": "2485", "text": "2486"}, {"alt": "2367"}, {"range": "2487", "text": "2488"}, {"alt": "2370"}, {"range": "2489", "text": "2490"}, {"alt": "2361"}, {"range": "2491", "text": "2492"}, {"alt": "2364"}, {"range": "2493", "text": "2494"}, {"alt": "2367"}, {"range": "2495", "text": "2496"}, {"alt": "2370"}, {"range": "2497", "text": "2498"}, {"alt": "2361"}, {"range": "2499", "text": "2500"}, {"alt": "2364"}, {"range": "2501", "text": "2502"}, {"alt": "2367"}, {"range": "2503", "text": "2504"}, {"alt": "2370"}, {"range": "2505", "text": "2506"}, {"alt": "2361"}, {"range": "2507", "text": "2508"}, {"alt": "2364"}, {"range": "2509", "text": "2510"}, {"alt": "2367"}, {"range": "2511", "text": "2512"}, {"alt": "2370"}, {"range": "2513", "text": "2514"}, {"alt": "2361"}, {"range": "2515", "text": "2516"}, {"alt": "2364"}, {"range": "2517", "text": "2518"}, {"alt": "2367"}, {"range": "2519", "text": "2520"}, {"alt": "2370"}, {"range": "2521", "text": "2522"}, {"alt": "2361"}, {"range": "2523", "text": "2524"}, {"alt": "2364"}, {"range": "2525", "text": "2526"}, {"alt": "2367"}, {"range": "2527", "text": "2528"}, {"alt": "2370"}, {"range": "2529", "text": "2530"}, {"alt": "2361"}, {"range": "2531", "text": "2532"}, {"alt": "2364"}, {"range": "2533", "text": "2534"}, {"alt": "2367"}, {"range": "2535", "text": "2536"}, {"alt": "2370"}, {"range": "2537", "text": "2538"}, {"alt": "2361"}, {"range": "2539", "text": "2540"}, {"alt": "2364"}, {"range": "2541", "text": "2542"}, {"alt": "2367"}, {"range": "2543", "text": "2544"}, {"alt": "2370"}, {"range": "2545", "text": "2546"}, {"alt": "2361"}, {"range": "2547", "text": "2438"}, {"alt": "2364"}, {"range": "2548", "text": "2440"}, {"alt": "2367"}, {"range": "2549", "text": "2442"}, {"alt": "2370"}, {"range": "2550", "text": "2444"}, {"alt": "2361"}, {"range": "2551", "text": "2552"}, {"alt": "2364"}, {"range": "2553", "text": "2554"}, {"alt": "2367"}, {"range": "2555", "text": "2556"}, {"alt": "2370"}, {"range": "2557", "text": "2558"}, {"alt": "2361"}, {"range": "2559", "text": "2560"}, {"alt": "2364"}, {"range": "2561", "text": "2562"}, {"alt": "2367"}, {"range": "2563", "text": "2564"}, {"alt": "2370"}, {"range": "2565", "text": "2566"}, {"alt": "2361"}, {"range": "2567", "text": "2568"}, {"alt": "2364"}, {"range": "2569", "text": "2570"}, {"alt": "2367"}, {"range": "2571", "text": "2572"}, {"alt": "2370"}, {"range": "2573", "text": "2574"}, {"alt": "2361"}, {"range": "2575", "text": "2576"}, {"alt": "2364"}, {"range": "2577", "text": "2578"}, {"alt": "2367"}, {"range": "2579", "text": "2580"}, {"alt": "2370"}, {"range": "2581", "text": "2582"}, {"alt": "2361"}, {"range": "2583", "text": "2584"}, {"alt": "2364"}, {"range": "2585", "text": "2586"}, {"alt": "2367"}, {"range": "2587", "text": "2588"}, {"alt": "2370"}, {"range": "2589", "text": "2590"}, {"alt": "2361"}, {"range": "2591", "text": "2592"}, {"alt": "2364"}, {"range": "2593", "text": "2594"}, {"alt": "2367"}, {"range": "2595", "text": "2596"}, {"alt": "2370"}, {"range": "2597", "text": "2598"}, {"alt": "2361"}, {"range": "2599", "text": "2600"}, {"alt": "2364"}, {"range": "2601", "text": "2602"}, {"alt": "2367"}, {"range": "2603", "text": "2604"}, {"alt": "2370"}, {"range": "2605", "text": "2606"}, {"alt": "2361"}, {"range": "2607", "text": "2608"}, {"alt": "2364"}, {"range": "2609", "text": "2610"}, {"alt": "2367"}, {"range": "2611", "text": "2612"}, {"alt": "2370"}, {"range": "2613", "text": "2614"}, {"alt": "2361"}, {"range": "2615", "text": "2616"}, {"alt": "2364"}, {"range": "2617", "text": "2618"}, {"alt": "2367"}, {"range": "2619", "text": "2620"}, {"alt": "2370"}, {"range": "2621", "text": "2622"}, {"alt": "2361"}, {"range": "2623", "text": "2624"}, {"alt": "2364"}, {"range": "2625", "text": "2626"}, {"alt": "2367"}, {"range": "2627", "text": "2628"}, {"alt": "2370"}, {"range": "2629", "text": "2630"}, {"alt": "2361"}, {"range": "2631", "text": "2632"}, {"alt": "2364"}, {"range": "2633", "text": "2634"}, {"alt": "2367"}, {"range": "2635", "text": "2636"}, {"alt": "2370"}, {"range": "2637", "text": "2638"}, {"alt": "2361"}, {"range": "2639", "text": "2640"}, {"alt": "2364"}, {"range": "2641", "text": "2642"}, {"alt": "2367"}, {"range": "2643", "text": "2644"}, {"alt": "2370"}, {"range": "2645", "text": "2646"}, {"range": "2647", "text": "2462"}, {"range": "2648", "text": "2464"}, {"range": "2649", "text": "2462"}, {"range": "2650", "text": "2464"}, {"range": "2651", "text": "2462"}, {"range": "2652", "text": "2464"}, {"alt": "2361"}, {"range": "2653", "text": "2654"}, {"alt": "2364"}, {"range": "2655", "text": "2656"}, {"alt": "2367"}, {"range": "2657", "text": "2658"}, {"alt": "2370"}, {"range": "2659", "text": "2660"}, {"range": "2661", "text": "2462"}, {"range": "2662", "text": "2464"}, {"alt": "2361"}, {"range": "2663", "text": "2664"}, {"alt": "2364"}, {"range": "2665", "text": "2666"}, {"alt": "2367"}, {"range": "2667", "text": "2668"}, {"alt": "2370"}, {"range": "2669", "text": "2670"}, {"alt": "2361"}, {"range": "2671", "text": "2672"}, {"alt": "2364"}, {"range": "2673", "text": "2674"}, {"alt": "2367"}, {"range": "2675", "text": "2676"}, {"alt": "2370"}, {"range": "2677", "text": "2678"}, {"alt": "2361"}, {"range": "2679", "text": "2680"}, {"alt": "2364"}, {"range": "2681", "text": "2682"}, {"alt": "2367"}, {"range": "2683", "text": "2684"}, {"alt": "2370"}, {"range": "2685", "text": "2686"}, {"alt": "2687"}, {"range": "2688", "text": "2689"}, "Replace with `&quot;`.", {"alt": "2690"}, {"range": "2691", "text": "2692"}, "Replace with `&ldquo;`.", {"alt": "2693"}, {"range": "2694", "text": "2695"}, "Replace with `&#34;`.", {"alt": "2696"}, {"range": "2697", "text": "2698"}, "Replace with `&rdquo;`.", {"alt": "2687"}, {"range": "2699", "text": "2700"}, {"alt": "2690"}, {"range": "2701", "text": "2702"}, {"alt": "2693"}, {"range": "2703", "text": "2704"}, {"alt": "2696"}, {"range": "2705", "text": "2706"}, {"alt": "2361"}, {"range": "2707", "text": "2708"}, {"alt": "2364"}, {"range": "2709", "text": "2710"}, {"alt": "2367"}, {"range": "2711", "text": "2712"}, {"alt": "2370"}, {"range": "2713", "text": "2714"}, {"alt": "2361"}, {"range": "2715", "text": "2716"}, {"alt": "2364"}, {"range": "2717", "text": "2718"}, {"alt": "2367"}, {"range": "2719", "text": "2720"}, {"alt": "2370"}, {"range": "2721", "text": "2722"}, {"alt": "2361"}, {"range": "2723", "text": "2724"}, {"alt": "2364"}, {"range": "2725", "text": "2726"}, {"alt": "2367"}, {"range": "2727", "text": "2728"}, {"alt": "2370"}, {"range": "2729", "text": "2730"}, {"alt": "2361"}, {"range": "2731", "text": "2732"}, {"alt": "2364"}, {"range": "2733", "text": "2734"}, {"alt": "2367"}, {"range": "2735", "text": "2736"}, {"alt": "2370"}, {"range": "2737", "text": "2738"}, {"alt": "2361"}, {"range": "2739", "text": "2740"}, {"alt": "2364"}, {"range": "2741", "text": "2742"}, {"alt": "2367"}, {"range": "2743", "text": "2744"}, {"alt": "2370"}, {"range": "2745", "text": "2746"}, {"alt": "2361"}, {"range": "2747", "text": "2748"}, {"alt": "2364"}, {"range": "2749", "text": "2750"}, {"alt": "2367"}, {"range": "2751", "text": "2752"}, {"alt": "2370"}, {"range": "2753", "text": "2754"}, {"range": "2755", "text": "2462"}, {"range": "2756", "text": "2464"}, {"range": "2757", "text": "2462"}, {"range": "2758", "text": "2464"}, {"range": "2759", "text": "2462"}, {"range": "2760", "text": "2464"}, {"range": "2761", "text": "2462"}, {"range": "2762", "text": "2464"}, {"range": "2763", "text": "2462"}, {"range": "2764", "text": "2464"}, {"range": "2765", "text": "2462"}, {"range": "2766", "text": "2464"}, {"range": "2767", "text": "2462"}, {"range": "2768", "text": "2464"}, {"range": "2769", "text": "2462"}, {"range": "2770", "text": "2464"}, {"alt": "2361"}, {"range": "2771", "text": "2772"}, {"alt": "2364"}, {"range": "2773", "text": "2774"}, {"alt": "2367"}, {"range": "2775", "text": "2776"}, {"alt": "2370"}, {"range": "2777", "text": "2778"}, {"alt": "2361"}, {"range": "2779", "text": "2780"}, {"alt": "2364"}, {"range": "2781", "text": "2782"}, {"alt": "2367"}, {"range": "2783", "text": "2784"}, {"alt": "2370"}, {"range": "2785", "text": "2786"}, {"alt": "2361"}, {"range": "2787", "text": "2788"}, {"alt": "2364"}, {"range": "2789", "text": "2790"}, {"alt": "2367"}, {"range": "2791", "text": "2792"}, {"alt": "2370"}, {"range": "2793", "text": "2794"}, {"range": "2795", "text": "2462"}, {"range": "2796", "text": "2464"}, {"range": "2797", "text": "2462"}, {"range": "2798", "text": "2464"}, {"range": "2799", "text": "2462"}, {"range": "2800", "text": "2464"}, {"alt": "2687"}, {"range": "2801", "text": "2802"}, {"alt": "2690"}, {"range": "2803", "text": "2804"}, {"alt": "2693"}, {"range": "2805", "text": "2806"}, {"alt": "2696"}, {"range": "2807", "text": "2808"}, {"alt": "2687"}, {"range": "2809", "text": "2810"}, {"alt": "2690"}, {"range": "2811", "text": "2812"}, {"alt": "2693"}, {"range": "2813", "text": "2814"}, {"alt": "2696"}, {"range": "2815", "text": "2816"}, {"range": "2817", "text": "2462"}, {"range": "2818", "text": "2464"}, {"range": "2819", "text": "2462"}, {"range": "2820", "text": "2464"}, {"range": "2821", "text": "2462"}, {"range": "2822", "text": "2464"}, {"range": "2823", "text": "2462"}, {"range": "2824", "text": "2464"}, {"range": "2825", "text": "2462"}, {"range": "2826", "text": "2464"}, {"range": "2827", "text": "2462"}, {"range": "2828", "text": "2464"}, "Update the dependencies array to be: [checkAuth]", {"range": "2829", "text": "2830"}, {"range": "2831", "text": "2462"}, {"range": "2832", "text": "2464"}, {"range": "2833", "text": "2462"}, {"range": "2834", "text": "2464"}, {"range": "2835", "text": "2462"}, {"range": "2836", "text": "2464"}, {"range": "2837", "text": "2462"}, {"range": "2838", "text": "2464"}, {"range": "2839", "text": "2462"}, {"range": "2840", "text": "2464"}, {"range": "2841", "text": "2462"}, {"range": "2842", "text": "2464"}, {"alt": "2361"}, {"range": "2843", "text": "2844"}, {"alt": "2364"}, {"range": "2845", "text": "2846"}, {"alt": "2367"}, {"range": "2847", "text": "2848"}, {"alt": "2370"}, {"range": "2849", "text": "2850"}, {"alt": "2361"}, {"range": "2851", "text": "2852"}, {"alt": "2364"}, {"range": "2853", "text": "2854"}, {"alt": "2367"}, {"range": "2855", "text": "2856"}, {"alt": "2370"}, {"range": "2857", "text": "2858"}, {"alt": "2361"}, {"range": "2859", "text": "2860"}, {"alt": "2364"}, {"range": "2861", "text": "2862"}, {"alt": "2367"}, {"range": "2863", "text": "2864"}, {"alt": "2370"}, {"range": "2865", "text": "2866"}, {"alt": "2361"}, {"range": "2867", "text": "2868"}, {"alt": "2364"}, {"range": "2869", "text": "2870"}, {"alt": "2367"}, {"range": "2871", "text": "2872"}, {"alt": "2370"}, {"range": "2873", "text": "2874"}, "Update the dependencies array to be: [user?.id, activeTab, fetchOrders]", {"range": "2875", "text": "2876"}, {"alt": "2361"}, {"range": "2877", "text": "2878"}, {"alt": "2364"}, {"range": "2879", "text": "2880"}, {"alt": "2367"}, {"range": "2881", "text": "2882"}, {"alt": "2370"}, {"range": "2883", "text": "2884"}, {"range": "2885", "text": "2462"}, {"range": "2886", "text": "2464"}, {"range": "2887", "text": "2462"}, {"range": "2888", "text": "2464"}, {"range": "2889", "text": "2462"}, {"range": "2890", "text": "2464"}, {"range": "2891", "text": "2462"}, {"range": "2892", "text": "2464"}, {"range": "2893", "text": "2462"}, {"range": "2894", "text": "2464"}, {"range": "2895", "text": "2462"}, {"range": "2896", "text": "2464"}, {"range": "2897", "text": "2462"}, {"range": "2898", "text": "2464"}, {"range": "2899", "text": "2462"}, {"range": "2900", "text": "2464"}, {"range": "2901", "text": "2462"}, {"range": "2902", "text": "2464"}, {"range": "2903", "text": "2462"}, {"range": "2904", "text": "2464"}, {"range": "2905", "text": "2462"}, {"range": "2906", "text": "2464"}, {"alt": "2361"}, {"range": "2907", "text": "2908"}, {"alt": "2364"}, {"range": "2909", "text": "2910"}, {"alt": "2367"}, {"range": "2911", "text": "2912"}, {"alt": "2370"}, {"range": "2913", "text": "2914"}, {"range": "2915", "text": "2462"}, {"range": "2916", "text": "2464"}, {"alt": "2361"}, {"range": "2917", "text": "2918"}, {"alt": "2364"}, {"range": "2919", "text": "2920"}, {"alt": "2367"}, {"range": "2921", "text": "2922"}, {"alt": "2370"}, {"range": "2923", "text": "2924"}, {"alt": "2361"}, {"range": "2925", "text": "2926"}, {"alt": "2364"}, {"range": "2927", "text": "2928"}, {"alt": "2367"}, {"range": "2929", "text": "2930"}, {"alt": "2370"}, {"range": "2931", "text": "2932"}, {"alt": "2361"}, {"range": "2933", "text": "2934"}, {"alt": "2364"}, {"range": "2935", "text": "2936"}, {"alt": "2367"}, {"range": "2937", "text": "2938"}, {"alt": "2370"}, {"range": "2939", "text": "2940"}, "Update the dependencies array to be: [clearCart, sessionId]", {"range": "2941", "text": "2942"}, {"alt": "2361"}, {"range": "2943", "text": "2944"}, {"alt": "2364"}, {"range": "2945", "text": "2946"}, {"alt": "2367"}, {"range": "2947", "text": "2948"}, {"alt": "2370"}, {"range": "2949", "text": "2950"}, {"range": "2951", "text": "2462"}, {"range": "2952", "text": "2464"}, {"alt": "2361"}, {"range": "2953", "text": "2954"}, {"alt": "2364"}, {"range": "2955", "text": "2956"}, {"alt": "2367"}, {"range": "2957", "text": "2958"}, {"alt": "2370"}, {"range": "2959", "text": "2960"}, {"range": "2961", "text": "2462"}, {"range": "2962", "text": "2464"}, {"range": "2963", "text": "2462"}, {"range": "2964", "text": "2464"}, {"alt": "2361"}, {"range": "2965", "text": "2966"}, {"alt": "2364"}, {"range": "2967", "text": "2968"}, {"alt": "2367"}, {"range": "2969", "text": "2970"}, {"alt": "2370"}, {"range": "2971", "text": "2972"}, {"alt": "2361"}, {"range": "2973", "text": "2974"}, {"alt": "2364"}, {"range": "2975", "text": "2976"}, {"alt": "2367"}, {"range": "2977", "text": "2978"}, {"alt": "2370"}, {"range": "2979", "text": "2980"}, {"alt": "2361"}, {"range": "2981", "text": "2982"}, {"alt": "2364"}, {"range": "2983", "text": "2984"}, {"alt": "2367"}, {"range": "2985", "text": "2986"}, {"alt": "2370"}, {"range": "2987", "text": "2988"}, {"alt": "2361"}, {"range": "2989", "text": "2990"}, {"alt": "2364"}, {"range": "2991", "text": "2992"}, {"alt": "2367"}, {"range": "2993", "text": "2994"}, {"alt": "2370"}, {"range": "2995", "text": "2996"}, "Update the dependencies array to be: [handleFileUpload]", {"range": "2997", "text": "2998"}, {"alt": "2361"}, {"range": "2999", "text": "3000"}, {"alt": "2364"}, {"range": "3001", "text": "3002"}, {"alt": "2367"}, {"range": "3003", "text": "3004"}, {"alt": "2370"}, {"range": "3005", "text": "3006"}, {"alt": "2361"}, {"range": "3007", "text": "3008"}, {"alt": "2364"}, {"range": "3009", "text": "3010"}, {"alt": "2367"}, {"range": "3011", "text": "3012"}, {"alt": "2370"}, {"range": "3013", "text": "3014"}, {"range": "3015", "text": "2462"}, {"range": "3016", "text": "2464"}, {"alt": "2361"}, {"range": "3017", "text": "3018"}, {"alt": "2364"}, {"range": "3019", "text": "3020"}, {"alt": "2367"}, {"range": "3021", "text": "3022"}, {"alt": "2370"}, {"range": "3023", "text": "3024"}, {"range": "3025", "text": "2462"}, {"range": "3026", "text": "2464"}, {"range": "3027", "text": "2462"}, {"range": "3028", "text": "2464"}, {"range": "3029", "text": "2462"}, {"range": "3030", "text": "2464"}, {"alt": "2361"}, {"range": "3031", "text": "3032"}, {"alt": "2364"}, {"range": "3033", "text": "3034"}, {"alt": "2367"}, {"range": "3035", "text": "3036"}, {"alt": "2370"}, {"range": "3037", "text": "3038"}, {"range": "3039", "text": "2462"}, {"range": "3040", "text": "2464"}, {"range": "3041", "text": "2462"}, {"range": "3042", "text": "2464"}, {"range": "3043", "text": "2462"}, {"range": "3044", "text": "2464"}, {"range": "3045", "text": "2462"}, {"range": "3046", "text": "2464"}, {"range": "3047", "text": "2462"}, {"range": "3048", "text": "2464"}, {"range": "3049", "text": "2462"}, {"range": "3050", "text": "2464"}, {"range": "3051", "text": "2462"}, {"range": "3052", "text": "2464"}, "&apos;", [996, 1191], "\n              Découvrez l&apos;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&lsquo;", [996, 1191], "\n              Découvrez l&lsquo;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&#39;", [996, 1191], "\n              Découvrez l&#39;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", "&rsquo;", [996, 1191], "\n              Découvrez l&rsquo;histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l'univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&apos;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&lsquo;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&#39;univers des produits premium à base de\n              Delta-9 THC.\n            ", [996, 1191], "\n              Découvrez l'histoire et les valeurs qui font de Deltagum une\n              marque de confiance dans l&rsquo;univers des produits premium à base de\n              Delta-9 THC.\n            ", [2773, 2812], "\n                    Qu&apos;il s'agisse de ", [2773, 2812], "\n                    Qu&lsquo;il s'agisse de ", [2773, 2812], "\n                    Qu&#39;il s'agisse de ", [2773, 2812], "\n                    Qu&rsquo;il s'agisse de ", [2773, 2812], "\n                    Qu'il s&apos;agisse de ", [2773, 2812], "\n                    Qu'il s&lsquo;agisse de ", [2773, 2812], "\n                    Qu'il s&#39;agisse de ", [2773, 2812], "\n                    Qu'il s&rsquo;agisse de ", [2891, 2959], " ou d&apos;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&lsquo;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&#39;autres formats à venir, nos\n                    produits sont ", [2891, 2959], " ou d&rsquo;autres formats à venir, nos\n                    produits sont ", [3309, 3379], "\n                    Qu&apos;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&lsquo;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&#39;est-ce que le Delta-9 THC ?\n                  ", [3309, 3379], "\n                    Qu&rsquo;est-ce que le Delta-9 THC ?\n                  ", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&apos;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&lsquo;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&#39;elle soit reconnue pour son", [3507, 3650], " est une molécule\n                    naturellement présente dans la plante de chanvre. Bien\n                    qu&rsquo;elle soit reconnue pour son", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&apos;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&lsquo;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&#39;Union européenne, à condition que le\n                    produit ne soit", [3714, 3916], ", une teneur\n                    inférieure à 0,3 % permet une commercialisation encadrée en\n                    France et dans l&rsquo;Union européenne, à condition que le\n                    produit ne soit", [9140, 9212], "\n                  ⚠️ En cas d&apos;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&lsquo;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&#39;utilisation non conforme\n                ", [9140, 9212], "\n                  ⚠️ En cas d&rsquo;utilisation non conforme\n                ", [9989, 10038], "• Trouble de l&apos;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&lsquo;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&#39;attention ou ralentissement moteur", [9989, 10038], "• Trouble de l&rsquo;attention ou ralentissement moteur", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&apos;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&lsquo;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&#39;allaitement\n                    ", [11702, 11833], "\n                      Usage fortement déconseillé pendant la grossesse et\n                      l&rsquo;allaitement\n                    ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&apos;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&lsquo;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&#39;utilisateur.\n                ", [13554, 13685], "\n                  ⚠️ Toute utilisation abusive engage la seule responsabilité de\n                  l&rsquo;utilisateur.\n                ", [2221, 2224], "unknown", [2221, 2224], "never", [815, 818], [815, 818], [1787, 1790], [1787, 1790], [557, 560], [557, 560], [2060, 2063], [2060, 2063], [3845, 3848], [3845, 3848], [6797, 6800], [6797, 6800], [1918, 1921], [1918, 1921], [3174, 3177], [3174, 3177], [1203, 1206], [1203, 1206], [743, 837], "\n              Informations légales et conditions d&apos;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&lsquo;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&#39;utilisation du site Deltagum.\n            ", [743, 837], "\n              Informations légales et conditions d&rsquo;utilisation du site Deltagum.\n            ", [2620, 2661], "\n                      Qu&apos;il s'agisse de ", [2620, 2661], "\n                      Qu&lsquo;il s'agisse de ", [2620, 2661], "\n                      Qu&#39;il s'agisse de ", [2620, 2661], "\n                      Qu&rsquo;il s'agisse de ", [2620, 2661], "\n                      Qu'il s&apos;agisse de ", [2620, 2661], "\n                      Qu'il s&lsquo;agisse de ", [2620, 2661], "\n                      Qu'il s&#39;agisse de ", [2620, 2661], "\n                      Qu'il s&rsquo;agisse de ", [2742, 2812], " ou d&apos;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&lsquo;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&#39;autres formats à venir, nos\n                      produits sont ", [2742, 2812], " ou d&rsquo;autres formats à venir, nos\n                      produits sont ", [3174, 3251], "\n                      📋 Qu&apos;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&lsquo;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&#39;est-ce que le Delta-9 THC ?\n                    ", [3174, 3251], "\n                      📋 Qu&rsquo;est-ce que le Delta-9 THC ?\n                    ", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&apos;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&lsquo;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&#39;elle soit reconnue pour son", [3367, 3514], " est une molécule\n                      naturellement présente dans la plante de chanvre. Bien\n                      qu&rsquo;elle soit reconnue pour son", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&apos;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&lsquo;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&#39;Union européenne, à condition que le\n                      produit ne soit", [3580, 3788], ", une teneur\n                      inférieure à 0,3 % permet une commercialisation encadrée\n                      en France et dans l&rsquo;Union européenne, à condition que le\n                      produit ne soit", [4540, 4620], "\n                      ⚠️ En cas d&apos;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&lsquo;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&#39;utilisation non conforme\n                    ", [4540, 4620], "\n                      ⚠️ En cas d&rsquo;utilisation non conforme\n                    ", [5454, 5503], [5454, 5503], [5454, 5503], [5454, 5503], [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&apos;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&lsquo;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&#39;utilisateur.\n                      ", [7349, 7498], "\n                        ⚠️ Toute utilisation abusive engage la seule\n                        responsabilité de l&rsquo;utilisateur.\n                      ", [9609, 9996], "\n                  L&apos;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&lsquo;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&#39;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L&rsquo;ensemble de ce site relève de la législation française et\n                  internationale sur le droit d'auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&apos;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&lsquo;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&#39;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [9609, 9996], "\n                  L'ensemble de ce site relève de la législation française et\n                  internationale sur le droit d&rsquo;auteur et la propriété\n                  intellectuelle. Tous les droits de reproduction sont réservés,\n                  y compris pour les documents téléchargeables et les\n                  représentations iconographiques et photographiques.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&apos;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&lsquo;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&#39;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10046, 10292], "\n                  La reproduction de tout ou partie de ce site sur un support\n                  électronique quel qu&rsquo;il soit est formellement interdite sauf\n                  autorisation expresse du directeur de la publication.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&apos;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&lsquo;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&#39;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d&rsquo;un droit\n                  d'accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&apos;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&lsquo;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&#39;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d&rsquo;accès, de rectification, de suppression et d'opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&apos;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&lsquo;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&#39;opposition aux\n                  données personnelles vous concernant.\n                ", [10599, 10971], "\n                  Conformément à la loi « Informatique et Libertés » du 6\n                  janvier 1978 modifiée et au Règlement Général sur la\n                  Protection des Données (RGPD), vous disposez d'un droit\n                  d'accès, de rectification, de suppression et d&rsquo;opposition aux\n                  données personnelles vous concernant.\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&apos;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&lsquo;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&#39;adresse : <EMAIL>\n                ", [11021, 11161], "\n                  Pour exercer ces droits, vous pouvez nous contacter à\n                  l&rsquo;adresse : <EMAIL>\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&apos;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&lsquo;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&#39;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l&rsquo;expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l'utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&apos;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&lsquo;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&#39;utilisation de cookies.\n                ", [11425, 11665], "\n                  Ce site utilise des cookies pour améliorer l'expérience\n                  utilisateur et analyser le trafic. En continuant à naviguer\n                  sur ce site, vous acceptez l&rsquo;utilisation de cookies.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&apos;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&lsquo;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&#39;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12220, 12505], "\n                  Les informations contenues sur ce site sont aussi précises que\n                  possible et le site remis à jour à différentes périodes de\n                  l&rsquo;ann<PERSON>, mais peut toutefois contenir des inexactitudes ou des\n                  omissions.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&apos;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&lsquo;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&#39;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [12555, 12867], "\n                  Si vous constatez une lacune, erreur ou ce qui parait être un\n                  dysfonctionnement, merci de bien vouloir le signaler par\n                  email, à l&rsquo;adresse <EMAIL>, en décrivant le\n                  problème de la façon la plus précise possible.\n                ", [865, 868], [865, 868], [934, 937], [934, 937], [1927, 1930], [1927, 1930], [4109, 4151], "\n            Retour à l&apos;accueil\n          ", [4109, 4151], "\n            Retour à l&lsquo;accueil\n          ", [4109, 4151], "\n            Retour à l&#39;accueil\n          ", [4109, 4151], "\n            Retour à l&rsquo;accueil\n          ", [13876, 13879], [13876, 13879], [6222, 6287], "\n                      Nom de l&apos;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&lsquo;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&#39;entreprise *\n                    ", [6222, 6287], "\n                      Nom de l&rsquo;entreprise *\n                    ", [9473, 9530], "\n                    Type d&apos;activité *\n                  ", [9473, 9530], "\n                    Type d&lsquo;activité *\n                  ", [9473, 9530], "\n                    Type d&#39;activité *\n                  ", [9473, 9530], "\n                    Type d&rsquo;activité *\n                  ", [1785, 1966], "\n              Il semble que vous n&apos;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&lsquo;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&#39;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", [1785, 1966], "\n              Il semble que vous n&rsquo;ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            ", "&quot;", [6488, 6629], "\n              &quot;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&ldquo;", [6488, 6629], "\n              &ldquo;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&#34;", [6488, 6629], "\n              &#34;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", "&rdquo;", [6488, 6629], "\n              &rdquo;Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&quot;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&ldquo;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&#34;\n            ", [6488, 6629], "\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.&rdquo;\n            ", [10929, 10994], "\n                        Date d&apos;expiration\n                      ", [10929, 10994], "\n                        Date d&lsquo;expiration\n                      ", [10929, 10994], "\n                        Date d&#39;expiration\n                      ", [10929, 10994], "\n                        Date d&rsquo;expiration\n                      ", [1742, 1792], "\n                Vérification d&apos;âge\n              ", [1742, 1792], "\n                Vérification d&lsquo;âge\n              ", [1742, 1792], "\n                Vérification d&#39;âge\n              ", [1742, 1792], "\n                Vérification d&rsquo;âge\n              ", [2265, 2356], "\n                  L&apos;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&lsquo;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&#39;accès est strictement réservé aux personnes majeures.\n                ", [2265, 2356], "\n                  L&rsquo;accès est strictement réservé aux personnes majeures.\n                ", [3175, 3241], "\n                    ✓ Oui, j&apos;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&lsquo;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&#39;ai 18 ans ou plus\n                  ", [3175, 3241], "\n                    ✓ <PERSON><PERSON>, j&rsquo;ai 18 ans ou plus\n                  ", [3477, 3544], "\n                    ✗ Non, j&apos;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&lsquo;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&#39;ai moins de 18 ans\n                  ", [3477, 3544], "\n                    ✗ Non, j&rsquo;ai moins de 18 ans\n                  ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&apos;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&lsquo;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&#39;utilisation.\n              ", [4329, 4460], "\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d&rsquo;utilisation.\n              ", [1613, 1616], [1613, 1616], [2530, 2533], [2530, 2533], [711, 714], [711, 714], [719, 722], [719, 722], [890, 893], [890, 893], [6747, 6750], [6747, 6750], [6759, 6762], [6759, 6762], [7028, 7031], [7028, 7031], [3486, 3576], "\n            Plus qu&apos;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&lsquo;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&#39;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [3486, 3576], "\n            Plus qu&rsquo;une étape pour savourer nos délicieux bonbons artisanaux !\n          ", [8045, 8119], "\n                            Retour à l&apos;accueil\n                          ", [8045, 8119], "\n                            Retour à l&lsquo;accueil\n                          ", [8045, 8119], "\n                            Retour à l&#39;accueil\n                          ", [8045, 8119], "\n                            Retour à l&rsquo;accueil\n                          ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&apos;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&lsquo;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&#39;hésitez pas à nous appeler\n                  directement.\n                ", [12800, 13003], "\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n&rsquo;hésitez pas à nous appeler\n                  directement.\n                ", [734, 737], [734, 737], [1043, 1046], [1043, 1046], [7981, 7984], [7981, 7984], [3338, 3348], "\n        &quot;", [3338, 3348], "\n        &ldquo;", [3338, 3348], "\n        &#34;", [3338, 3348], "\n        &rdquo;", [3361, 3369], "&quot;\n      ", [3361, 3369], "&ldquo;\n      ", [3361, 3369], "&#34;\n      ", [3361, 3369], "&rdquo;\n      ", [2349, 2352], [2349, 2352], [2408, 2411], [2408, 2411], [1737, 1740], [1737, 1740], [1118, 1121], [1118, 1121], [1302, 1305], [1302, 1305], [1471, 1474], [1471, 1474], [2504, 2506], "[checkAuth]", [2842, 2845], [2842, 2845], [2957, 2960], [2957, 2960], [3085, 3088], [3085, 3088], [4232, 4235], [4232, 4235], [4487, 4490], [4487, 4490], [5060, 5063], [5060, 5063], [6658, 6740], "\n                    Vous devez vous connecter en tant qu&apos;admin\n                  ", [6658, 6740], "\n                    Vous devez vous connecter en tant qu&lsquo;admin\n                  ", [6658, 6740], "\n                    Vous devez vous connecter en tant qu&#39;admin\n                  ", [6658, 6740], "\n                    Vous devez vous connecter en tant qu&rsquo;admin\n                  ", [7112, 7194], "\n                    Votre compte n&apos;a pas les permissions admin\n                  ", [7112, 7194], "\n                    Votre compte n&lsquo;a pas les permissions admin\n                  ", [7112, 7194], "\n                    Votre compte n&#39;a pas les permissions admin\n                  ", [7112, 7194], "\n                    Votre compte n&rsquo;a pas les permissions admin\n                  ", [7378, 7436], "\n                    Retour à l&apos;accueil\n                  ", [7378, 7436], "\n                    Retour à l&lsquo;accueil\n                  ", [7378, 7436], "\n                    Retour à l&#39;accueil\n                  ", [7378, 7436], "\n                    Retour à l&rsquo;accueil\n                  ", [7678, 7748], "\n                  🧪 Page de test d&apos;authentification\n                ", [7678, 7748], "\n                  🧪 Page de test d&lsquo;authentification\n                ", [7678, 7748], "\n                  🧪 Page de test d&#39;authentification\n                ", [7678, 7748], "\n                  🧪 Page de test d&rsquo;authentification\n                ", [3226, 3247], "[user?.id, activeTab, fetchOrders]", [17812, 17893], "\n                    Vous n&apos;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&lsquo;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&#39;avez pas encore passé de commande.\n                  ", [17812, 17893], "\n                    Vous n&rsquo;avez pas encore passé de commande.\n                  ", [1398, 1401], [1398, 1401], [434, 437], [434, 437], [1044, 1047], [1044, 1047], [1418, 1421], [1418, 1421], [2725, 2728], [2725, 2728], [3291, 3294], [3291, 3294], [3991, 3994], [3991, 3994], [3052, 3055], [3052, 3055], [3060, 3063], [3060, 3063], [416, 419], [416, 419], [1241, 1244], [1241, 1244], [2667, 2713], "\n                  S&apos;inscrire\n                ", [2667, 2713], "\n                  S&lsquo;inscrire\n                ", [2667, 2713], "\n                  S&#39;inscrire\n                ", [2667, 2713], "\n                  S&rsquo;inscrire\n                ", [661, 664], [661, 664], [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&apos;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&lsquo;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&#39;a été débité de votre\n            compte.\n          ", [2467, 2577], "\n            Votre paiement a été annulé. Aucun montant n&rsquo;a été débité de votre\n            compte.\n          ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&apos;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&lsquo;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&#39;achat\n                ", [4254, 4324], "\n                  Livraison gratuite dès 25€ d&rsquo;achat\n                ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&apos;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&lsquo;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              Besoin d&#39;aide ? Notre équipe est là pour vous accompagner.\n            ", [5905, 5991], "\n              <PERSON><PERSON><PERSON> d&rsquo;aide ? Notre équipe est là pour vous accompagner.\n            ", [1857, 1868], "[<PERSON><PERSON><PERSON>, sessionId]", [2869, 2887], "Retour à l&apos;accueil", [2869, 2887], "Retour à l&lsquo;accueil", [2869, 2887], "Retour à l&#39;accueil", [2869, 2887], "Retour à l&rsquo;accueil", [168, 171], [168, 171], [1740, 1783], "\n          Test d&apos;Authentification\n        ", [1740, 1783], "\n          Test d&lsquo;Authentification\n        ", [1740, 1783], "\n          Test d&#39;Authentification\n        ", [1740, 1783], "\n          Test d&rsquo;Authentification\n        ", [499, 502], [499, 502], [523, 526], [523, 526], [10060, 10185], "\n                  Considérez l&apos;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&lsquo;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&#39;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10060, 10185], "\n                  Considérez l&rsquo;ajout de tests automatisés et de monitoring en\n                  production.\n                ", [10499, 10621], "\n                  Les permissions et l&apos;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&lsquo;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&#39;authentification sont correctement\n                  configurées.\n                ", [10499, 10621], "\n                  Les permissions et l&rsquo;authentification sont correctement\n                  configurées.\n                ", [3252, 3297], "\n            Gestionnaire d&apos;Images\n          ", [3252, 3297], "\n            Gestionnaire d&lsquo;Images\n          ", [3252, 3297], "\n            Gestionnaire d&#39;Images\n          ", [3252, 3297], "\n            Gestionnaire d&rsquo;Images\n          ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&apos;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&lsquo;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&#39;à 5MB chacune\n                ", [4666, 4736], "\n                  JPG, PNG, WebP jusqu&rsquo;à 5MB chacune\n                ", [3156, 3158], "[handleFileUpload]", [4853, 4907], "\n              JPG, PNG, WebP jusqu&apos;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&lsquo;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&#39;à 5MB\n            ", [4853, 4907], "\n              JPG, PNG, WebP jusqu&rsquo;à 5MB\n            ", [7612, 7623], "Aujourd&apos;hui", [7612, 7623], "Aujourd&lsquo;hui", [7612, 7623], "Aujourd&#39;hui", [7612, 7623], "Aujourd&rsquo;hui", [3031, 3034], [3031, 3034], [7513, 7627], "\n                    Les paliers permettent d&apos;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&lsquo;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&#39;offrir des réductions pour les achats en quantité\n                  ", [7513, 7627], "\n                    Les paliers permettent d&rsquo;offrir des réductions pour les achats en quantité\n                  ", [1063, 1066], [1063, 1066], [744, 747], [744, 747], [1507, 1510], [1507, 1510], [11316, 11388], "\n                          JPG, PNG jusqu&apos;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&lsquo;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&#39;à 5MB\n                        ", [11316, 11388], "\n                          JPG, PNG jusqu&rsquo;à 5MB\n                        ", [1212, 1215], [1212, 1215], [371, 374], [371, 374], [390, 393], [390, 393], [1173, 1176], [1173, 1176], [1183, 1186], [1183, 1186], [1248, 1251], [1248, 1251], [1797, 1800], [1797, 1800]]