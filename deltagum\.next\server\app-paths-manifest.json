{"/_not-found/page": "app/_not-found/page.js", "/api/admin/create/route": "app/api/admin/create/route.js", "/api/admin/stats-simple/route": "app/api/admin/stats-simple/route.js", "/api/admin/stats/route": "app/api/admin/stats/route.js", "/api/auth/login/route": "app/api/auth/login/route.js", "/api/auth/logout/route": "app/api/auth/logout/route.js", "/api/auth/me/route": "app/api/auth/me/route.js", "/api/auth/register/route": "app/api/auth/register/route.js", "/api/checkout/route": "app/api/checkout/route.js", "/api/checkout/session/[sessionId]/route": "app/api/checkout/session/[sessionId]/route.js", "/api/checkout/session/route": "app/api/checkout/session/route.js", "/api/contact/route": "app/api/contact/route.js", "/api/customers/[id]/route": "app/api/customers/[id]/route.js", "/api/customers/route": "app/api/customers/route.js", "/api/orders/[id]/route": "app/api/orders/[id]/route.js", "/api/orders/route": "app/api/orders/route.js", "/api/orders/user/route": "app/api/orders/user/route.js", "/api/products/[id]/route": "app/api/products/[id]/route.js", "/api/products/[id]/variants/[variantId]/route": "app/api/products/[id]/variants/[variantId]/route.js", "/api/products/[id]/variants/bulk/route": "app/api/products/[id]/variants/bulk/route.js", "/api/products/[id]/variants/route": "app/api/products/[id]/variants/route.js", "/api/products/route": "app/api/products/route.js", "/api/seed/route": "app/api/seed/route.js", "/api/test-db/route": "app/api/test-db/route.js", "/api/test-orders/route": "app/api/test-orders/route.js", "/api/test-prisma/route": "app/api/test-prisma/route.js", "/api/test-stats/route": "app/api/test-stats/route.js", "/api/test/database/route": "app/api/test/database/route.js", "/api/upload/[filename]/route": "app/api/upload/[filename]/route.js", "/api/upload/route": "app/api/upload/route.js", "/api/user/profile/route": "app/api/user/profile/route.js", "/api/webhooks/stripe/route": "app/api/webhooks/stripe/route.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/admin/dashboard/page": "app/admin/dashboard/page.js", "/about/page": "app/about/page.js", "/auth/page": "app/auth/page.js", "/cancel/page": "app/cancel/page.js", "/page": "app/page.js", "/legal/page": "app/legal/page.js", "/products/[id]/page": "app/products/[id]/page.js", "/professionals/page": "app/professionals/page.js", "/profile/page": "app/profile/page.js", "/success/page": "app/success/page.js", "/test-auth/page": "app/test-auth/page.js"}