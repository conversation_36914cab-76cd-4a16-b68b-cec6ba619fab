(()=>{var e={};e.id=5957,e.ids=[5957],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4325:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>G});var t=a(166),r=a(23705),i=a(1550),l=a(66212),n=a(24961),c=a(39628),d=a(55050);(0,d.A)("users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["path",{d:"M16 3.128a4 4 0 0 1 0 7.744",key:"16gr8j"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]]);let o=(0,d.A)("dollar-sign",[["line",{x1:"12",x2:"12",y1:"2",y2:"22",key:"7eqyqh"}],["path",{d:"M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6",key:"1b0p4s"}]]),m=(0,d.A)("plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var x=a(11325),h=a.n(x),u=a(81040),p=a(14791),g=a(15580);let j=(0,d.A)("printer",[["path",{d:"M6 18H4a2 2 0 0 1-2-2v-5a2 2 0 0 1 2-2h16a2 2 0 0 1 2 2v5a2 2 0 0 1-2 2h-2",key:"143wyd"}],["path",{d:"M6 9V3a1 1 0 0 1 1-1h10a1 1 0 0 1 1 1v6",key:"1itne7"}],["rect",{x:"6",y:"14",width:"12",height:"8",rx:"1",key:"1ue0tg"}]]);var y=a(2926),v=a(39690),N=a(24057),f=a(92865),b=a(91475),w=a(35896);let k=(0,d.A)("circle-check-big",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]]),A=(0,d.A)("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),C=(0,d.A)("truck",[["path",{d:"M14 18V6a2 2 0 0 0-2-2H4a2 2 0 0 0-2 2v11a1 1 0 0 0 1 1h2",key:"wrbu53"}],["path",{d:"M15 18H9",key:"1lyqi6"}],["path",{d:"M19 18h2a1 1 0 0 0 1-1v-3.65a1 1 0 0 0-.22-.624l-3.48-4.35A1 1 0 0 0 17.52 8H14",key:"lysw3i"}],["circle",{cx:"17",cy:"18",r:"2",key:"332jqn"}],["circle",{cx:"7",cy:"18",r:"2",key:"19iecd"}]]);var E=a(59093);function D({order:e,onBack:s,onUpdateStatus:a}){return(0,t.jsxs)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:s,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Commande #",e.id.slice(-8)]}),(0,t.jsx)("p",{className:"text-gray-600",children:"D\xe9tails de la commande"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:`inline-flex px-3 py-1 rounded-full text-sm font-medium ${(e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PAID":return"bg-green-100 text-green-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"DELIVERED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(e.status)}`,children:(e=>{switch(e){case"PENDING":return"En attente";case"PAID":return"Pay\xe9e";case"SHIPPED":return"Exp\xe9di\xe9e";case"DELIVERED":return"Livr\xe9e";case"CANCELLED":return"Annul\xe9e";default:return e}})(e.status)}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",children:[(0,t.jsx)(j,{className:"w-4 h-4 mr-2"}),"Imprimer"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{children:["Articles command\xe9s (",e.items.length,")"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:e.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"relative w-16 h-16 rounded-lg overflow-hidden bg-white",children:(0,t.jsx)(E.default,{src:e.product.image||"/img/placeholder.svg",alt:e.product.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.product.name}),e.variant&&(0,t.jsxs)("p",{className:"text-sm text-gray-600 capitalize",children:["Saveur: ",e.variant.flavor]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[Number(e.price).toFixed(2),"€ \xd7 ",e.quantity]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("p",{className:"font-semibold text-gray-900",children:[(Number(e.price)*e.quantity).toFixed(2),"€"]})})]},s))})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations client"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Nom complet"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsxs)("p",{className:"text-gray-900",children:[e.customer.firstName," ",e.customer.lastName]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:e.customer.email})]})]}),e.shippingAddress.phone&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"T\xe9l\xe9phone"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:e.shippingAddress.phone})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Adresse de livraison"}),(0,t.jsxs)("div",{className:"flex items-start space-x-2 mt-1",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 text-gray-400 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-900",children:e.shippingAddress.street}),(0,t.jsxs)("p",{className:"text-gray-600",children:[e.shippingAddress.postalCode," ",e.shippingAddress.city]}),(0,t.jsx)("p",{className:"text-gray-600",children:e.shippingAddress.country})]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"R\xe9sum\xe9"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Sous-total"}),(0,t.jsxs)("span",{className:"font-medium",children:[Number(e.totalAmount).toFixed(2),"€"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Livraison"}),(0,t.jsx)("span",{className:"font-medium",children:"Gratuite"})]}),(0,t.jsx)("div",{className:"border-t pt-3",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-lg font-semibold",children:"Total"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-pink-600",children:[Number(e.totalAmount).toFixed(2),"€"]})]})})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Date de commande"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:new Date(e.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Num\xe9ro de commande"}),(0,t.jsxs)("p",{className:"text-gray-900 font-mono",children:["#",e.id]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Articles"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsxs)("p",{className:"text-gray-900",children:[e.items.length," article(s)"]})]})]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Actions"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:["PENDING"===e.status&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.$n,{className:"w-full bg-blue-600 hover:bg-blue-700",onClick:()=>a?.(e.id,"PROCESSING"),children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Marquer en cours"]}),(0,t.jsxs)(r.$n,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>a?.(e.id,"COMPLETED"),children:[(0,t.jsx)(k,{className:"w-4 h-4 mr-2"}),"Marquer termin\xe9e"]})]}),"PROCESSING"===e.status&&(0,t.jsxs)(r.$n,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>a?.(e.id,"COMPLETED"),children:[(0,t.jsx)(k,{className:"w-4 h-4 mr-2"}),"Marquer termin\xe9e"]}),"CANCELLED"!==e.status&&"COMPLETED"!==e.status&&(0,t.jsxs)(r.$n,{variant:"danger",className:"w-full",onClick:()=>a?.(e.id,"CANCELLED"),children:[(0,t.jsx)(A,{className:"w-4 h-4 mr-2"}),"Annuler la commande"]}),(0,t.jsxs)(r.$n,{variant:"outline",className:"w-full",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Contacter le client"]}),(0,t.jsxs)(r.$n,{variant:"outline",className:"w-full",children:[(0,t.jsx)(C,{className:"w-4 h-4 mr-2"}),"Suivi de livraison"]})]})]})]})]})]})}let P=(0,d.A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);var S=a(53340);function q({onViewOrder:e}){let[s,a]=(0,p.useState)([]),[i,d]=(0,p.useState)(!1),[m,x]=(0,p.useState)("ALL"),[h,u]=(0,p.useState)(""),[g,j]=(0,p.useState)("ALL"),v=async()=>{try{d(!0),console.log("\uD83D\uDD0D [ADMIN] R\xe9cup\xe9ration de toutes les commandes...");let e=await fetch("/api/orders"),s=await e.json();if(console.log("\uD83D\uDCE5 [ADMIN] R\xe9ponse API orders:",s),console.log("\uD83D\uDCCA [ADMIN] Statut de la r\xe9ponse:",e.status),s.success){let e=s.data.orders||[];console.log("✅ [ADMIN] Commandes r\xe9cup\xe9r\xe9es:",e.length),console.log("\uD83D\uDCCB [ADMIN] D\xe9tails des commandes:",e),a(e)}else console.error("❌ [ADMIN] Erreur lors du chargement des commandes:",s.error)}catch(e){console.error("❌ [ADMIN] Erreur de connexion:",e)}finally{d(!1)}},N=e=>new Date(e).toLocaleDateString("fr-FR",{day:"numeric",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}),f=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),w=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PAID":return"bg-green-100 text-green-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"DELIVERED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},k=e=>{switch(e){case"PENDING":return"En attente";case"PAID":return"Pay\xe9e";case"SHIPPED":return"Exp\xe9di\xe9e";case"DELIVERED":return"Livr\xe9e";case"CANCELLED":return"Annul\xe9e";default:return e}},A=s.filter(e=>{let s=e.id.toLowerCase().includes(h.toLowerCase())||e.customer.firstName.toLowerCase().includes(h.toLowerCase())||e.customer.lastName.toLowerCase().includes(h.toLowerCase())||e.customer.email.toLowerCase().includes(h.toLowerCase()),a="ALL"===m||e.status===m,t=!0;if("ALL"!==g){let s=new Date(e.createdAt),a=new Date;switch(g){case"TODAY":t=s.toDateString()===a.toDateString();break;case"WEEK":t=s>=new Date(a.getTime()-6048e5);break;case"MONTH":t=s>=new Date(a.getTime()-2592e6)}}return s&&a&&t});return i?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Commandes"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mt-2 animate-pulse"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(r.Zp,{className:"animate-pulse",children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded"})})},s))})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Commandes"}),(0,t.jsxs)("p",{className:"text-black",children:[A.length," commande(s) au total"]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(P,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Rechercher une commande...",value:h,onChange:e=>u(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white"})]}),(0,t.jsxs)("select",{value:m,onChange:e=>x(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white",children:[(0,t.jsxs)("option",{value:"ALL",children:["Tous les statuts (",s.length,")"]}),(0,t.jsxs)("option",{value:"PENDING",children:["En attente (",s.filter(e=>"PENDING"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"PAID",children:["Pay\xe9es (",s.filter(e=>"PAID"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"SHIPPED",children:["Exp\xe9di\xe9es (",s.filter(e=>"SHIPPED"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"DELIVERED",children:["Livr\xe9es (",s.filter(e=>"DELIVERED"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"CANCELLED",children:["Annul\xe9es (",s.filter(e=>"CANCELLED"===e.status).length,")"]})]}),(0,t.jsxs)("select",{value:g,onChange:e=>j(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white",children:[(0,t.jsx)("option",{value:"ALL",children:"Toutes les dates"}),(0,t.jsx)("option",{value:"TODAY",children:"Aujourd'hui"}),(0,t.jsx)("option",{value:"WEEK",children:"Cette semaine"}),(0,t.jsx)("option",{value:"MONTH",children:"Ce mois"})]}),(0,t.jsx)(r.$n,{onClick:v,variant:"outline",size:"sm",children:"Actualiser"})]}),0===A.length?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(c.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:h||"ALL"!==m||"ALL"!==g?"Aucune commande trouv\xe9e":"Aucune commande enregistr\xe9e"}),(0,t.jsx)("p",{className:"text-black",children:h||"ALL"!==m||"ALL"!==g?"Essayez de modifier vos filtres de recherche":"Les commandes appara\xeetront ici apr\xe8s les premiers achats"})]})}):(0,t.jsx)("div",{className:"space-y-4",children:A.map((s,a)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*a},children:(0,t.jsx)(r.Zp,{className:"hover:shadow-lg transition-shadow cursor-pointer",onClick:()=>e?.(s),children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-orange-500 rounded-lg flex items-center justify-center text-white font-semibold",children:(0,t.jsx)(c.A,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Commande #",s.id.slice(-8)]}),(0,t.jsx)("span",{className:`inline-flex px-2 py-1 rounded-full text-xs font-medium ${w(s.status)}`,children:k(s.status)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 mr-1"}),(0,t.jsxs)("span",{children:[s.customer.firstName," ",s.customer.lastName]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-1"}),(0,t.jsx)("span",{children:N(s.createdAt)})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-1"}),(0,t.jsxs)("span",{children:[s.items.length," article(s)"]})]})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"flex items-center text-2xl font-bold text-gray-900",children:[(0,t.jsx)(o,{className:"w-6 h-6 mr-1"}),Number(s.totalAmount).toFixed(2),"€"]}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:a=>{a.stopPropagation(),e?.(s)},className:"mt-2",children:[(0,t.jsx)(S.A,{className:"w-4 h-4 mr-2"}),"Voir d\xe9tails"]})]})]}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[s.items.slice(0,3).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 bg-gray-50 rounded-lg px-3 py-1",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:[e.quantity,"x"]}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.product.name}),e.variant&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.variant.flavor,")"]}),(0,t.jsx)("span",{className:"text-xs text-gray-400",children:f(e.price)})]},s)),s.items.length>3&&(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["+",s.items.length-3," autre(s)"]})]})})]})})},s.id))})]})}function $({product:e,onEdit:s,onBack:a,onAddVariant:i}){return(0,t.jsxs)(l.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:a,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:e.name}),(0,t.jsx)("p",{className:"text-gray-600",children:"D\xe9tails du produit"})]})]}),(0,t.jsxs)(r.$n,{onClick:s,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Modifier"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations g\xe9n\xe9rales"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Nom"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:e.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Prix de base"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:e.price?`${e.price}€`:"Variable"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Statut"}),(0,t.jsx)("span",{className:`inline-flex px-2 py-1 rounded-full text-xs font-medium ${e.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.active?"Actif":"Inactif"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Dosage"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:e.dosage||"Non sp\xe9cifi\xe9"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Description"}),(0,t.jsx)("p",{className:"text-gray-900 mt-1",children:e.description})]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r.ZB,{children:["Variantes (",e.variants?.length||0,")"]}),(0,t.jsxs)(r.$n,{size:"sm",onClick:i,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Ajouter une variante"]})]})}),(0,t.jsx)(r.Wu,{children:e.variants&&0!==e.variants.length?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:e.variants.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full border-2 border-gray-300",style:{backgroundColor:e.color}}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 capitalize",children:e.flavor}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Stock: ",e.stock," • SKU: ",e.sku]})]})]}),e.images&&e.images.length>0&&(0,t.jsxs)("div",{className:"mt-3 flex space-x-2",children:[e.images.slice(0,3).map((s,a)=>(0,t.jsx)("div",{className:"relative w-12 h-12 rounded-md overflow-hidden",children:(0,t.jsx)(E.default,{src:s,alt:`${e.flavor} ${a+1}`,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},a)),e.images.length>3&&(0,t.jsx)("div",{className:"w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.images.length-3]})})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(n.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Aucune variante cr\xe9\xe9e"}),(0,t.jsxs)(r.$n,{size:"sm",onClick:i,variant:"outline",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Cr\xe9er la premi\xe8re variante"]})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Paliers de prix"})}),(0,t.jsx)(r.Wu,{children:e.priceTiers&&0!==e.priceTiers.length?(0,t.jsx)("div",{className:"space-y-3",children:e.priceTiers.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("span",{className:"font-medium",children:[e.quantity," ",1===e.quantity?"unit\xe9":"unit\xe9s"]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-pink-600",children:[e.price,"€"]})]},s))}):(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Aucun palier de prix configur\xe9"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Les paliers permettent d'offrir des r\xe9ductions pour les achats en quantit\xe9"})]})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Image principale"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden bg-gray-100",children:(0,t.jsx)(E.default,{src:e.image||"/img/placeholder.svg",alt:e.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Statistiques"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Variantes"}),(0,t.jsx)("span",{className:"font-medium",children:e.variants?.length||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stock total"}),(0,t.jsx)("span",{className:"font-medium",children:e.variants?.reduce((e,s)=>e+s.stock,0)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Paliers de prix"}),(0,t.jsx)("span",{className:"font-medium",children:e.priceTiers?.length||0})]})]})]})]})]})]})}let L=(0,d.A)("image",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",ry:"2",key:"1m3agn"}],["circle",{cx:"9",cy:"9",r:"2",key:"af1f0g"}],["path",{d:"m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21",key:"1xmnt7"}]]),I=(0,d.A)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]);var M=a(28805),R=a(65480);let z=(0,d.A)("trash-2",[["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6",key:"4alrt4"}],["path",{d:"M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2",key:"v07s0e"}],["line",{x1:"10",x2:"10",y1:"11",y2:"17",key:"1uufr5"}],["line",{x1:"14",x2:"14",y1:"11",y2:"17",key:"xtxkd"}]]);function T({tiers:e,onTiersChange:s,currency:a="EUR"}){let[i,l]=(0,p.useState)(null),[c,d]=(0,p.useState)({quantity:1,price:0,discount:0}),x=(a,t)=>{s(e.map((e,s)=>s===a?t:e).sort((e,s)=>e.quantity-s.quantity)),l(null)},h=a=>{s(e.filter((e,s)=>s!==a))},u=(e,s)=>e<=0?0:Math.round((e-s)/e*100),g=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:a}).format(e),j=e.find(e=>1===e.quantity)?.price||0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Prix par palier"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"D\xe9finissez des prix d\xe9gressifs selon la quantit\xe9"})]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," palier(s)"]})]}),e.length>1&&j>0&&(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:(0,t.jsxs)(r.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,t.jsx)(o,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("h4",{className:"font-medium text-green-800",children:"\xc9conomies maximales"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:e.slice(1).map((e,s)=>{let a=u(j,e.price),r=(j-e.price)*e.quantity;return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," unit\xe9s"]}),(0,t.jsxs)("p",{className:"font-semibold text-green-700",children:["-",a,"%"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["\xc9conomie: ",g(r)]})]},e.id||s)})})]})}),(0,t.jsx)("div",{className:"space-y-3",children:e.map((e,s)=>(0,t.jsx)(r.Zp,{className:1===e.quantity?"border-blue-200 bg-blue-50":"",children:(0,t.jsx)(r.Wu,{className:"p-4",children:i===s?(0,t.jsx)(Z,{tier:e,onSave:e=>x(s,e),onCancel:()=>l(null),currency:a}):(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("span",{className:"font-medium text-gray-900",children:[e.quantity," ",1===e.quantity?"unit\xe9":"unit\xe9s"]}),1===e.quantity&&(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:"Prix de base"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:g(e.price)}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[g(e.price/e.quantity)," / unit\xe9"]}),e.quantity>1&&j>0&&(0,t.jsxs)("p",{className:"text-sm text-green-600",children:["-",u(j,e.price/e.quantity),"% par unit\xe9"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.$n,{size:"sm",variant:"outline",onClick:()=>l(s),children:(0,t.jsx)(w.A,{className:"w-4 h-4"})}),1!==e.quantity&&(0,t.jsx)(r.$n,{size:"sm",variant:"danger",onClick:()=>h(s),children:(0,t.jsx)(z,{className:"w-4 h-4"})})]})]})})},e.id||s))}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Ajouter un palier de prix"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantit\xe9"}),(0,t.jsx)(r.pd,{type:"number",min:"1",value:c.quantity,onChange:e=>d(s=>({...s,quantity:parseInt(e.target.value)||1})),placeholder:"Ex: 3"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Prix total (",a,")"]}),(0,t.jsx)(r.pd,{type:"number",min:"0",step:"0.01",value:c.price,onChange:e=>d(s=>({...s,price:parseFloat(e.target.value)||0})),placeholder:"Ex: 15.00"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Prix unitaire"}),(0,t.jsx)("div",{className:"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700",children:c.quantity>0&&c.price>0?g(c.price/c.quantity):g(0)})]})]}),c.quantity>1&&c.price>0&&j>0&&(0,t.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-green-700",children:"\xc9conomie par rapport au prix unitaire de base:"}),(0,t.jsxs)("span",{className:"font-semibold text-green-800",children:["-",u(j,c.price/c.quantity),"%"]})]}),(0,t.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["Le client \xe9conomise ",g(j*c.quantity-c.price),"en achetant ",c.quantity," unit\xe9s"]})]}),(0,t.jsxs)(r.$n,{onClick:()=>{c.quantity>0&&c.price>0&&(s([...e,{...c,id:Date.now().toString()}].sort((e,s)=>e.quantity-s.quantity)),d({quantity:1,price:0,discount:0}))},disabled:c.quantity<=0||c.price<=0||e.some(e=>e.quantity===c.quantity),className:"w-full",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Ajouter le palier"]}),e.some(e=>e.quantity===c.quantity)&&(0,t.jsx)("p",{className:"text-sm text-red-600 text-center",children:"Un palier existe d\xe9j\xe0 pour cette quantit\xe9"})]})]})]})}function Z({tier:e,onSave:s,onCancel:a,currency:i}){let l,[n,c]=(0,p.useState)(e);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantit\xe9"}),(0,t.jsx)(r.pd,{type:"number",min:"1",value:n.quantity,onChange:e=>c(s=>({...s,quantity:parseInt(e.target.value)||1})),disabled:1===e.quantity})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Prix total"}),(0,t.jsx)(r.pd,{type:"number",min:"0",step:"0.01",value:n.price,onChange:e=>c(s=>({...s,price:parseFloat(e.target.value)||0}))})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Prix unitaire: ",(l=n.quantity>0?n.price/n.quantity:0,new Intl.NumberFormat("fr-FR",{style:"currency",currency:i}).format(l))]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{size:"sm",onClick:()=>s(n),children:[(0,t.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Sauvegarder"]}),(0,t.jsxs)(r.$n,{size:"sm",variant:"outline",onClick:a,children:[(0,t.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]})}let V=(0,d.A)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]);function F({variants:e,onVariantsChange:s,productId:a}){let[i,l]=(0,p.useState)(null),[n,c]=(0,p.useState)([]),[d,o]=(0,p.useState)({flavor:"",color:"#ff6b9d",stock:0,sku:"",images:[]}),x=[{name:"Fraise",color:"#ff6b9d"},{name:"Myrtille",color:"#4dabf7"},{name:"Pomme",color:"#51cf66"},{name:"Orange",color:"#ff922b"},{name:"Citron",color:"#ffd43b"},{name:"Raisin",color:"#9775fa"}],h=e=>{if(!e)return"";let s=e.toUpperCase().replace(/\s+/g,"-"),a=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return`DELTA-${s}-${a}`},u=async()=>{if(d.flavor){let t=h(d.flavor),r={...d,sku:t};if(a)try{let t=await fetch(`/api/products/${a}/variants`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!t.ok){let e=await t.json();alert(e.error||"Erreur lors de la cr\xe9ation");return}let i=await t.json(),l=[...e,i.data];s(l)}catch(e){alert("Erreur de connexion");return}else s([...e,{...r,id:Date.now().toString()}]);o({flavor:"",color:"#ff6b9d",stock:0,sku:"",images:[]})}},g=async(t,r)=>{if(r.id&&a)try{let i=await fetch(`/api/products/${a}/variants/${r.id}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)});if(!i.ok){let e=await i.json();alert(e.error||"Erreur lors de la mise \xe0 jour");return}let l=await i.json(),n=e.map((e,s)=>s===t?l.data:e);s(n)}catch(e){alert("Erreur de connexion");return}else s(e.map((e,s)=>s===t?r:e));l(null)},j=async t=>{let r=e[t];if(r.id&&a)try{let e=await fetch(`/api/products/${a}/variants/${r.id}`,{method:"DELETE"});if(!e.ok){let s=await e.json();alert(s.error||"Erreur lors de la suppression");return}}catch(e){alert("Erreur de connexion");return}s(e.filter((e,s)=>s!==t))},y=async()=>{if(0!==n.length&&confirm(`\xcates-vous s\xfbr de vouloir supprimer ${n.length} variante(s) ?`)){if(a)try{let e=await fetch(`/api/products/${a}/variants/bulk`,{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({variantIds:n})});if(!e.ok){let s=await e.json();alert(s.error||"Erreur lors de la suppression");return}}catch(e){alert("Erreur de connexion");return}s(e.filter(e=>!n.includes(e.id||""))),c([])}},v=e=>{c(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},N=async(a,t)=>{let r=new FormData;r.append("file",a);try{let a=await fetch("/api/upload",{method:"POST",body:r}),i=await a.json();if(i.success)if(void 0!==t){let a=e.map((e,s)=>s===t?{...e,images:[...e.images,i.url]}:e);s(a)}else o(e=>({...e,images:[...e.images,i.url]}))}catch(e){alert("Erreur lors de l'upload de l'image")}},f=(a,t)=>{null===a?o(e=>({...e,images:e.images.filter((e,s)=>s!==t)})):s(e.map((e,s)=>s===a?{...e,images:e.images.filter((e,s)=>s!==t)}:e))};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Variants du produit"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[n.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[n.length," s\xe9lectionn\xe9(s)"]}),(0,t.jsxs)(r.$n,{onClick:y,variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(z,{className:"w-4 h-4 mr-1"}),"Supprimer"]})]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[e.length," variant(s)"]})]})]}),e.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 text-sm",children:(0,t.jsx)(r.$n,{onClick:()=>{let s=e.map(e=>e.id).filter(Boolean);c(n.length===s.length?[]:s)},variant:"outline",size:"sm",children:n.length===e.filter(e=>e.id).length?"D\xe9s\xe9lectionner tout":"S\xe9lectionner tout"})}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(r.$n,{onClick:()=>{let s=JSON.stringify(e,null,2),a=`data:application/json;charset=utf-8,${encodeURIComponent(s)}`,t=`variants-${new Date().toISOString().slice(0,10)}.json`,r=document.createElement("a");r.setAttribute("href",a),r.setAttribute("download",t),r.click()},variant:"outline",size:"sm",children:[(0,t.jsx)(V,{className:"w-4 h-4 mr-1"}),"Exporter"]})})]}),(0,t.jsx)("div",{className:"space-y-4",children:e.map((e,s)=>(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:i===s?(0,t.jsx)(O,{variant:e,onSave:e=>g(s,e),onCancel:()=>l(null),onImageUpload:e=>N(e,s),onRemoveImage:e=>f(s,e)}):(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e.id&&(0,t.jsx)("input",{type:"checkbox",checked:n.includes(e.id),onChange:()=>v(e.id),className:"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"}),(0,t.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-300",style:{backgroundColor:e.color}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 capitalize",children:e.flavor}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Stock: ",e.stock," • SKU: ",e.sku]})]}),e.images.length>0&&(0,t.jsxs)("div",{className:"flex space-x-1",children:[e.images.slice(0,3).map((s,a)=>(0,t.jsx)("div",{className:"relative w-8 h-8 rounded overflow-hidden",children:(0,t.jsx)(E.default,{src:s,alt:`${e.flavor} ${a+1}`,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},a)),e.images.length>3&&(0,t.jsx)("div",{className:"w-8 h-8 rounded bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.images.length-3]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.$n,{size:"sm",variant:"outline",onClick:()=>l(s),children:(0,t.jsx)(w.A,{className:"w-4 h-4"})}),(0,t.jsx)(r.$n,{size:"sm",variant:"danger",onClick:()=>j(s),children:(0,t.jsx)(z,{className:"w-4 h-4"})})]})]})})},e.id||s))}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Ajouter un variant"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Saveur"}),(0,t.jsxs)("select",{value:d.flavor,onChange:e=>{let s=x.find(s=>s.name===e.target.value);o(a=>({...a,flavor:e.target.value,color:s?.color||a.color}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-gray-900",children:[(0,t.jsx)("option",{value:"",className:"text-gray-700",children:"S\xe9lectionner une saveur"}),x.map(e=>(0,t.jsx)("option",{value:e.name,children:e.name},e.name))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stock"}),(0,t.jsx)(r.pd,{type:"number",min:"0",value:d.stock,onChange:e=>o(s=>({...s,stock:parseInt(e.target.value)||0}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Couleur"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"color",value:d.color,onChange:e=>o(s=>({...s,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),(0,t.jsx)(r.pd,{value:d.color,onChange:e=>o(s=>({...s,color:e.target.value})),placeholder:"#ff6b9d"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Images du variant"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&N(s)},className:"hidden",id:"variant-image-upload"}),(0,t.jsxs)("label",{htmlFor:"variant-image-upload",className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,t.jsx)(I,{className:"w-4 h-4 text-gray-700"}),(0,t.jsx)("span",{className:"text-gray-900",children:"Ajouter une image"})]})]}),d.images.length>0&&(0,t.jsx)("div",{className:"flex space-x-2 mt-3",children:d.images.map((e,s)=>(0,t.jsxs)("div",{className:"relative w-16 h-16 rounded-lg overflow-hidden",children:[(0,t.jsx)(E.default,{src:e,alt:`Variant ${s+1}`,fill:!0,className:"object-cover"}),(0,t.jsx)("button",{onClick:()=>f(null,s),className:"absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs",children:"\xd7"})]},s))})]}),(0,t.jsxs)(r.$n,{onClick:u,disabled:!d.flavor,className:"w-full",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Ajouter le variant"]})]})]})]})}function O({variant:e,onSave:s,onCancel:a,onImageUpload:i,onRemoveImage:l}){let[n,c]=(0,p.useState)(e);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(r.pd,{value:n.flavor,onChange:e=>c(s=>({...s,flavor:e.target.value})),placeholder:"Saveur"}),(0,t.jsx)(r.pd,{value:n.sku,onChange:e=>c(s=>({...s,sku:e.target.value})),placeholder:"SKU"}),(0,t.jsx)(r.pd,{type:"number",value:n.stock,onChange:e=>c(s=>({...s,stock:parseInt(e.target.value)||0})),placeholder:"Stock"})]}),n.images.length>0&&(0,t.jsxs)("div",{className:"flex space-x-2",children:[n.images.map((e,s)=>(0,t.jsxs)("div",{className:"relative w-12 h-12 rounded overflow-hidden",children:[(0,t.jsx)(E.default,{src:e,alt:`${n.flavor} ${s+1}`,fill:!0,className:"object-cover"}),(0,t.jsx)("button",{onClick:()=>l(s),className:"absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs",children:"\xd7"})]},s)),(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&i(s)},className:"hidden",id:`edit-variant-image-${e.id}`}),(0,t.jsx)("label",{htmlFor:`edit-variant-image-${e.id}`,className:"w-12 h-12 border-2 border-dashed border-gray-300 rounded flex items-center justify-center cursor-pointer hover:border-gray-400",children:(0,t.jsx)(m,{className:"w-4 h-4 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{size:"sm",onClick:()=>s(n),children:[(0,t.jsx)(R.A,{className:"w-4 h-4 mr-2"}),"Sauvegarder"]}),(0,t.jsxs)(r.$n,{size:"sm",variant:"outline",onClick:a,children:[(0,t.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]})}function W({product:e,onSave:s,onCancel:a}){let[i,c]=(0,p.useState)({name:"",description:"",dosage:"",image:"",active:!0}),[d,o]=(0,p.useState)([]),[m,x]=(0,p.useState)([{quantity:1,price:8}]),[h,u]=(0,p.useState)(!1),[j,y]=(0,p.useState)("general"),v=async e=>{let s=new FormData;s.append("file",e);try{let e=await fetch("/api/upload",{method:"POST",body:s}),a=await e.json();a.success?c(e=>({...e,image:a.url})):alert("Erreur lors de l'upload")}catch(e){alert("Erreur lors de l'upload")}},N=async e=>{e.preventDefault(),u(!0);try{let e={...i,variants:d,pricingTiers:m,basePrice:m.find(e=>1===e.quantity)?.price||0,image:i.image||"/img/placeholder.svg"};await s(e)}catch(e){alert("Erreur lors de la sauvegarde")}finally{u(!1)}},f=[{id:"general",label:"Informations g\xe9n\xe9rales",icon:n.A},{id:"variants",label:"Variants",icon:n.A},{id:"pricing",label:"Prix par palier",icon:n.A}];return(0,t.jsxs)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:a,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:e?"Modifier le produit":"Nouveau produit"}),(0,t.jsx)("p",{className:"text-gray-600",children:e?"Modifiez les informations du produit":"Cr\xe9ez un nouveau produit avec variants et prix par palier"})]})]})}),(0,t.jsxs)("form",{onSubmit:N,className:"space-y-6",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"flex space-x-8 px-6",children:f.map(e=>(0,t.jsxs)("button",{type:"button",onClick:()=>y(e.id),className:`py-4 px-1 border-b-2 font-medium text-sm ${j===e.id?"border-pink-500 text-pink-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:[(0,t.jsx)(e.icon,{className:"w-4 h-4 inline mr-2"}),e.label]},e.id))})}),(0,t.jsxs)("div",{className:"p-6",children:["general"===j&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom du produit *"}),(0,t.jsx)(r.pd,{value:i.name,onChange:e=>c(s=>({...s,name:e.target.value})),placeholder:"Ex: Cookie Delta-9",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Dosage"}),(0,t.jsx)(r.pd,{value:i.dosage,onChange:e=>c(s=>({...s,dosage:e.target.value})),placeholder:"Ex: 5mg THC"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,t.jsx)("textarea",{value:i.description,onChange:e=>c(s=>({...s,description:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none text-gray-900 placeholder:text-gray-700",placeholder:"Description d\xe9taill\xe9e du produit...",minLength:10})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Image principale"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[i.image?(0,t.jsxs)("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden",children:[(0,t.jsx)(E.default,{src:i.image,alt:"Produit",fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,t.jsx)("button",{type:"button",onClick:()=>{if(i.image&&i.image.includes("/uploads/")){let e=i.image.split("/").pop();e&&fetch(`/api/upload/${e}`,{method:"DELETE"}).catch(e=>console.error("Erreur lors de la suppression:",e))}c(e=>({...e,image:""}))},className:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm",children:"\xd7"})]}):(0,t.jsx)("div",{className:"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center",children:(0,t.jsx)(L,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{let s=e.target.files?.[0];s&&v(s)},className:"hidden",id:"main-image-upload"}),(0,t.jsxs)("label",{htmlFor:"main-image-upload",className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,t.jsx)(I,{className:"w-4 h-4 text-gray-700"}),(0,t.jsx)("span",{className:"text-gray-900",children:"Choisir une image"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-700 mt-1",children:"JPG, PNG jusqu'\xe0 5MB"}),(0,t.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"Les images sont stock\xe9es dans /public/uploads"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"active",checked:i.active,onChange:e=>c(s=>({...s,active:e.target.checked})),className:"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"active",className:"ml-2 block text-sm text-gray-900",children:"Produit actif (visible sur le site)"})]})]}),"variants"===j&&(0,t.jsx)(F,{variants:d,onVariantsChange:o,productId:e?.id}),"pricing"===j&&(0,t.jsx)(T,{tiers:m,onTiersChange:x,currency:"EUR"})]})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,t.jsxs)(r.$n,{type:"button",variant:"outline",onClick:a,children:[(0,t.jsx)(M.A,{className:"w-4 h-4 mr-2"}),"Annuler"]}),(0,t.jsxs)(r.$n,{type:"submit",disabled:h||!i.name,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(R.A,{className:"w-4 h-4 mr-2"}),h?"Sauvegarde...":"Sauvegarder"]})]})]})]})}function H({onAddProduct:e,onEditProduct:s,onViewProduct:a}){let[i,c]=(0,p.useState)([]),[d,o]=(0,p.useState)(!0),[x,h]=(0,p.useState)(null),u=async()=>{try{o(!0);let e=await fetch("/api/products"),s=await e.json();s.success?c(s.data.products||[]):h("Erreur lors du chargement des produits")}catch(e){h("Erreur de connexion")}finally{o(!1)}},g=async e=>{if(confirm("\xcates-vous s\xfbr de vouloir supprimer ce produit ?"))try{(await fetch(`/api/products/${e}`,{method:"DELETE"})).ok?c(i.filter(s=>s.id!==e)):alert("Erreur lors de la suppression")}catch(e){alert("Erreur de connexion")}};return d?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"})}):x?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,t.jsx)("p",{className:"text-red-600",children:x}),(0,t.jsx)(r.$n,{onClick:u,className:"mt-4",children:"R\xe9essayer"})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des produits"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[i.length," produit(s) au total"]})]}),(0,t.jsxs)(r.$n,{onClick:e,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Nouveau produit"]})]}),0===i.length?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(n.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Aucun produit"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Commencez par cr\xe9er votre premier produit Deltagum."}),(0,t.jsxs)(r.$n,{onClick:e,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(m,{className:"w-4 h-4 mr-2"}),"Cr\xe9er un produit"]})]})}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:i.map((e,i)=>(0,t.jsx)(l.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*i},children:(0,t.jsxs)(r.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,t.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[(0,t.jsx)(E.default,{src:e.image||"/img/placeholder.svg",alt:e.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${e.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.active?"Actif":"Inactif"})})]}),(0,t.jsxs)(r.aR,{className:"pb-2",children:[(0,t.jsx)(r.ZB,{className:"text-lg",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description})]}),(0,t.jsxs)(r.Wu,{className:"pt-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Prix de base"}),(0,t.jsx)("p",{className:"font-semibold",children:e.price?`${e.price}€`:"Variable"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Variants"}),(0,t.jsx)("p",{className:"font-semibold",children:e.variants?.length||0})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:()=>a(e),className:"flex-1",children:[(0,t.jsx)(S.A,{className:"w-4 h-4 mr-1"}),"Voir"]}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:()=>s(e),className:"flex-1",children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-1"}),"Modifier"]}),(0,t.jsx)(r.$n,{variant:"danger",size:"sm",onClick:()=>g(e.id),children:(0,t.jsx)(z,{className:"w-4 h-4"})})]})]})]})},e.id))})]})}function G(){let{user:e,isAuthenticated:s,logout:a,isAdmin:d,checkAuth:o}=(0,i.A)();(0,u.useRouter)();let[x,g]=(0,p.useState)("overview"),[j,y]=(0,p.useState)("list"),[v,N]=(0,p.useState)(null),[f,b]=(0,p.useState)("list"),[w,k]=(0,p.useState)(null),[A,C]=(0,p.useState)("list"),[E,P]=(0,p.useState)(null),[S,L]=(0,p.useState)({overview:{products:0,orders:0,customers:0,revenue:0,ordersGrowth:0},recentOrders:[],topProducts:[],monthlyStats:[]}),I=async()=>{try{console.log("\uD83D\uDD04 Chargement des statistiques...");let e=await fetch("/api/admin/stats-simple"),s=await e.json();console.log("\uD83D\uDCCA R\xe9ponse API stats:",s),s.success?(L(s.data),console.log("✅ Statistiques mises \xe0 jour:",s.data.overview)):console.error("❌ Erreur API stats:",s.error)}catch(e){console.error("❌ Erreur lors du chargement des statistiques:",e)}},M=()=>{N(null),y("form")},R=e=>{N(e),y("form")},z=async e=>{try{let s=v?`/api/products/${v.id}`:"/api/products",a=v?"PUT":"POST";(await fetch(s,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?(y("list"),N(null)):alert("Erreur lors de la sauvegarde")}catch(e){alert("Erreur de connexion")}},T=()=>{y("list"),N(null)},Z=async(e,s)=>{try{(await fetch(`/api/orders/${e}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok?(P(e=>e?{...e,status:s}:null),alert("Statut de la commande mis \xe0 jour avec succ\xe8s !")):alert("Erreur lors de la mise \xe0 jour du statut")}catch(e){alert("Erreur de connexion")}};if(console.log("Dashboard - isAuthenticated:",s),console.log("Dashboard - user:",e),console.log("Dashboard - isAdmin():",d()),!s||!d())return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"bg-white p-8 rounded-lg shadow-lg max-w-lg w-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"\uD83D\uDD10 Acc\xe8s Dashboard Admin"}),(0,t.jsxs)("div",{className:"text-left bg-gray-50 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h3",{className:"font-semibold mb-3",children:"\xc9tat actuel :"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:["✅ Authentifi\xe9: ",s?"Oui":"Non"]}),(0,t.jsxs)("p",{children:["\uD83D\uDC64 Utilisateur:"," ",e?`${e.firstName} ${e.lastName}`:"Aucun"]}),(0,t.jsxs)("p",{children:["\uD83C\uDFAD R\xf4le: ",e?.role||"Aucun"]}),(0,t.jsxs)("p",{children:["\uD83D\uDD11 Admin: ",d()?"Oui":"Non"]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[s?d()?null:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Votre compte n'a pas les permissions admin"}),(0,t.jsx)(h(),{href:"/",className:"inline-block bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700",children:"Retour \xe0 l'accueil"})]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Vous devez vous connecter en tant qu'admin"}),(0,t.jsx)("a",{href:"/auth",className:"inline-block bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700",children:"Se connecter"})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsx)("a",{href:"/test-auth",className:"text-sm text-blue-600 hover:underline",children:"\uD83E\uDDEA Page de test d'authentification"})})]})]})})});let V={initial:{opacity:0,y:20},animate:{opacity:1,y:0}},F={initial:{opacity:0,x:-20},animate:{opacity:1,x:0}};S.overview.products.toString(),n.A,S.overview.orders.toString(),c.A,S.overview.customers.toString(),S.overview.revenue.toFixed(2);let O=[{id:"overview",label:"Vue d'ensemble",icon:n.A},{id:"products",label:"Produits",icon:n.A},{id:"orders",label:"Commandes",icon:c.A}];return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:[(0,t.jsx)(l.P.header,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},className:"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-[99]",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)(l.P.h1,{initial:{scale:.9},animate:{scale:1},className:"text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent",children:"Deltagum Admin"})}),(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsxs)(l.P.span,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"text-gray-700 hidden sm:block",children:["Bonjour, ",e?.firstName," ",e?.lastName]})})]})})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)(l.P.div,{initial:F.initial,animate:F.animate,className:"w-full lg:w-64 bg-white rounded-xl shadow-lg p-6 border border-gray-200",children:(0,t.jsx)("nav",{className:"space-y-2",children:O.map(e=>(0,t.jsxs)("button",{onClick:()=>g(e.id),className:`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${x===e.id?"bg-pink-50 text-pink-600 border-l-4 border-pink-500":"text-gray-900 hover:bg-gray-50 hover:text-black"}`,children:[(0,t.jsx)(e.icon,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"font-medium",children:e.label})]},e.id))})}),(0,t.jsxs)("div",{className:"flex-1",children:["overview"===x&&(0,t.jsxs)(l.P.div,{initial:V.initial,animate:V.animate,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-black",children:"Statistiques"}),(0,t.jsxs)(r.$n,{onClick:I,variant:"outline",size:"sm",className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Actualiser"})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Produits"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-pink-600",children:S.overview.products})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Commandes"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:S.overview.orders})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Clients"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:S.overview.customers})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Revenus"}),(0,t.jsxs)("p",{className:"text-3xl font-bold text-orange-600",children:["€",S.overview.revenue.toFixed(2)]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{className:"mb-3",children:"Actions rapides"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)(r.$n,{onClick:M,className:"h-20 flex flex-col items-center justify-center space-y-2 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(m,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"Ajouter un produit"})]}),(0,t.jsxs)(r.$n,{variant:"outline",onClick:()=>g("orders"),className:"h-20 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)(c.A,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"Voir les commandes"})]})]})})]})]}),"products"===x&&(0,t.jsxs)(l.P.div,{initial:V.initial,animate:V.animate,className:"space-y-6",children:["list"===j&&(0,t.jsx)(H,{onAddProduct:M,onEditProduct:R,onViewProduct:e=>{N(e),y("details")}}),"form"===j&&(0,t.jsx)(W,{product:v,onSave:z,onCancel:T}),"details"===j&&v&&(0,t.jsx)($,{product:v,onEdit:()=>R(v),onBack:T,onAddVariant:()=>{alert("Gestion des variantes \xe0 impl\xe9menter")}})]}),"orders"===x&&(0,t.jsxs)(l.P.div,{initial:V.initial,animate:V.animate,className:"space-y-6",children:["list"===A&&(0,t.jsx)(q,{onViewOrder:e=>{P(e),C("details")}}),"details"===A&&E&&(0,t.jsx)(D,{order:E,onBack:()=>{C("list"),P(null)},onUpdateStatus:Z})]}),"overview"!==x&&"products"!==x&&"orders"!==x&&(0,t.jsx)(l.P.div,{initial:V.initial,animate:V.animate,className:"space-y-6",children:(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Section en d\xe9veloppement"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Cette section sera bient\xf4t disponible."})]})})})]})]})})]})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15580:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24057:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},24961:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},25781:(e,s,a)=>{"use strict";a.r(s),a.d(s,{GlobalError:()=>l.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var t=a(87628),r=a(42355),i=a(87979),l=a.n(i),n=a(15140),c={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);a.d(s,c);let d={children:["",{children:["admin",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,81289)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},x=new t.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/admin/dashboard/page",pathname:"/admin/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35896:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},36346:(e,s,a)=>{Promise.resolve().then(a.bind(a,4325))},39690:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},53340:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65480:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},73138:(e,s,a)=>{Promise.resolve().then(a.bind(a,81289))},73515:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>r});var t=a(67269);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,t.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81040:(e,s,a)=>{"use strict";var t=a(59076);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},81289:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>t});let t=(0,a(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\admin\\\\dashboard\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\admin\\dashboard\\page.tsx","default")},91475:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},92865:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(55050).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../../webpack-runtime.js");s.C(e);var a=e=>s(s.s=e),t=s.X(0,[7583,8500,7269,6964],()=>a(25781));module.exports=t})();