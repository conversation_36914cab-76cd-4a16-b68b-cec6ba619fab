(()=>{var e={};e.id=3786,e.ids=[3786],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},35410:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>x,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>p});var a={};t.r(a),t.d(a,{POST:()=>c});var s=t(73194),i=t(42355),n=t(41650),o=t(85514),d=t(82171),u=t(63723);async function c(){try{let e="<EMAIL>",r=await o.z.customer.findUnique({where:{email:e}});if(r)return u.NextResponse.json({message:"Admin existe d\xe9j\xe0",admin:{email:r.email,role:r.role}});let a=await d.Ay.hash("admin123",12),s=await o.z.customer.create({data:{id:t(55511).randomUUID(),email:e,password:a,firstName:"Admin",lastName:"Deltagum",phone:"+33123456789",address:"123 Rue de l'Administration",postalCode:"75001",city:"Paris",role:"ADMIN",updatedAt:new Date}});return u.NextResponse.json({message:"Admin cr\xe9\xe9 avec succ\xe8s",admin:{email:s.email,role:s.role},credentials:{email:"<EMAIL>",password:"admin123"}})}catch(e){return console.error("Erreur lors de la cr\xe9ation de l'admin:",e),u.NextResponse.json({error:"Erreur lors de la cr\xe9ation de l'admin"},{status:500})}}let l=new s.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/admin/create/route",pathname:"/api/admin/create",filename:"route",bundlePath:"app/api/admin/create/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\create\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:p,serverHooks:x}=l;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:p})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,r,t)=>{"use strict";let a;t.d(r,{z:()=>i});let s=require("@prisma/client");try{a=new s.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let i=a},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[7583,5696,2171],()=>t(35410));module.exports=a})();