(()=>{var e={};e.id=2070,e.ids=[2070],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59763:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>c});var o=t(73194),a=t(42355),i=t(41650),n=t(63723),u=t(85514);async function c(){try{console.log("\uD83D\uDCCA R\xe9cup\xe9ration des statistiques simplifi\xe9es...");let e=await u.z.product.count({where:{active:!0}}),r=await u.z.order.count(),t=await u.z.customer.count({where:{role:"USER"}}),s=(await u.z.order.aggregate({_sum:{totalAmount:!0},where:{status:{in:["PAID","SHIPPED","DELIVERED"]}}}))._sum.totalAmount||0,o={overview:{products:e,orders:r,customers:t,revenue:s,ordersGrowth:0},recentOrders:[],topProducts:[],monthlyStats:[]};return console.log("✅ Statistiques simplifi\xe9es:",o.overview),n.NextResponse.json({success:!0,data:o})}catch(e){return console.error("❌ Erreur lors de la r\xe9cup\xe9ration des statistiques:",e),n.NextResponse.json({success:!0,data:{overview:{products:0,orders:0,customers:0,revenue:0,ordersGrowth:0},recentOrders:[],topProducts:[],monthlyStats:[]},fallback:!0,error:e instanceof Error?e.message:String(e)})}}let p=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/admin/stats-simple/route",pathname:"/api/admin/stats-simple",filename:"route",bundlePath:"app/api/admin/stats-simple/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats-simple\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:m}=p;function v(){return(0,i.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>a});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let a=s},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696],()=>t(59763));module.exports=s})();