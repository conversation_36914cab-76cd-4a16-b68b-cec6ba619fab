(()=>{var e={};e.id=9759,e.ids=[9759],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,t,r)=>{"use strict";let s;r.d(t,{z:()=>o});let a=require("@prisma/client");try{s=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=s},89536:()=>{},91951:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>h,routeModule:()=>d,serverHooks:()=>p,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(73194),o=r(42355),n=r(41650),u=r(85514),i=r(63723);async function c(){try{let[e,t,r,s,a,o,n]=await Promise.all([u.z.product.count({where:{active:!0}}),u.z.order.count(),u.z.customer.count({where:{role:"USER"}}),u.z.order.aggregate({_sum:{totalAmount:!0},where:{status:"DELIVERED"}}),u.z.order.findMany({take:5,orderBy:{createdAt:"desc"},include:{customer:{select:{firstName:!0,lastName:!0,email:!0}},items:{include:{product:{select:{name:!0}},variant:{select:{flavor:!0}}}}}}),u.z.orderItem.groupBy({by:["productId"],_sum:{quantity:!0},orderBy:{_sum:{quantity:"desc"}},take:5}),u.z.$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*) as orders_count,
          SUM(total) as revenue
        FROM orders 
        WHERE "createdAt" >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
      `]),c=await Promise.all(o.map(async e=>({...await u.z.product.findUnique({where:{id:e.productId},select:{name:!0,image:!0}}),totalSold:e._sum.quantity||0}))),d=new Date,l=new Date(d.getFullYear(),d.getMonth()-1,1),m=new Date(d.getFullYear(),d.getMonth(),1),[p,h]=await Promise.all([u.z.order.count({where:{createdAt:{gte:m}}}),u.z.order.count({where:{createdAt:{gte:l,lt:m}}})]),g={overview:{products:e,orders:t,customers:r,revenue:s._sum?.totalAmount||0,ordersGrowth:Math.round(100*(h>0?(p-h)/h*100:0))/100},recentOrders:a.map(e=>({id:e.id,customer:`${e.customer.firstName} ${e.customer.lastName}`,email:e.customer.email,total:e.totalAmount,status:e.status,createdAt:e.createdAt,itemsCount:e.items.length,items:e.items.map(e=>({product:e.product.name,variant:e.variant?.flavor,quantity:e.quantity}))})),topProducts:c,monthlyStats:n};return i.NextResponse.json({success:!0,data:g})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des statistiques:",e),i.NextResponse.json({success:!0,data:{overview:{products:2,orders:0,customers:0,revenue:0,ordersGrowth:0},recentOrders:[],topProducts:[],monthlyStats:[]},fallback:!0})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/admin/stats/route",pathname:"/api/admin/stats",filename:"route",bundlePath:"app/api/admin/stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\admin\\stats\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:p}=d;function h(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7583,5696],()=>r(91951));module.exports=s})();