(()=>{var e={};e.id=7758,e.ids=[7758],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},29615:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>d});var a=t(73194),i=t(42355),o=t(41650),n=t(85514),u=t(82171),l=t(2783),p=t.n(l),c=t(63723);async function d(e){try{let{email:r,password:t}=await e.json();if(!r||!t)return c.NextResponse.json({error:"Email et mot de passe requis"},{status:400});let s=await n.z.customer.findUnique({where:{email:r},select:{id:!0,email:!0,password:!0,firstName:!0,lastName:!0,role:!0}});if(!s||!await u.Ay.compare(t,s.password||""))return c.NextResponse.json({error:"Identifiants invalides"},{status:401});let a=p().sign({userId:s.id,email:s.email,role:s.role},process.env.JWT_SECRET||"fallback-secret",{expiresIn:"7d"}),i=c.NextResponse.json({message:"Connexion r\xe9ussie",user:{id:s.id,email:s.email,firstName:s.firstName,lastName:s.lastName,role:s.role}});return i.cookies.set("auth-token",a,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),i}catch(e){return console.error("Erreur lors de la connexion:",e),c.NextResponse.json({error:"Erreur interne du serveur"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/login/route",pathname:"/api/auth/login",filename:"route",bundlePath:"app/api/auth/login/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\login\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:h}=m;function f(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>i});let a=require("@prisma/client");try{s=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let i=s},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,2783,2171],()=>t(29615));module.exports=s})();