(()=>{var e={};e.id=8673,e.ids=[8673],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27713:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>l});var o=t(73194),i=t(42355),a=t(41650),n=t(85514),u=t(2783),c=t.n(u),p=t(63723);async function l(e){try{let r=e.cookies.get("auth-token")?.value;if(!r)return p.NextResponse.json({error:"Non authentifi\xe9"},{status:401});let t=c().verify(r,process.env.JWT_SECRET||"fallback-secret"),s=await n.z.customer.findUnique({where:{id:t.userId},select:{id:!0,email:!0,firstName:!0,lastName:!0,phone:!0,address:!0,postalCode:!0,city:!0,role:!0,createdAt:!0}});if(!s)return p.NextResponse.json({error:"Utilisateur non trouv\xe9"},{status:404});return p.NextResponse.json({user:s})}catch(e){return console.error("Erreur lors de la v\xe9rification de l'utilisateur:",e),p.NextResponse.json({error:"Token invalide"},{status:401})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/auth/me/route",pathname:"/api/auth/me",filename:"route",bundlePath:"app/api/auth/me/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\me\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:h}=d;function v(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>i});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let i=s},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,2783],()=>t(27713));module.exports=s})();