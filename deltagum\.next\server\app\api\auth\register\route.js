(()=>{var e={};e.id=1612,e.ids=[1612],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>a});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let a=s},89536:()=>{},91458:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{POST:()=>p});var o=t(73194),a=t(42355),i=t(41650),n=t(85514),u=t(82171),l=t(2783),c=t.n(l),d=t(63723);async function p(e){try{let r,{email:s,password:o,firstName:a,lastName:i,phone:l,address:p,postalCode:m,city:x}=await e.json();if(!s||!o||!a||!i||!l||!p||!m||!x)return d.NextResponse.json({error:"Tous les champs sont requis"},{status:400});if(!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(s))return d.NextResponse.json({error:"Format d'email invalide"},{status:400});if(o.length<6)return d.NextResponse.json({error:"Le mot de passe doit contenir au moins 6 caract\xe8res"},{status:400});try{if(await n.z.customer.findUnique({where:{email:s}}))return d.NextResponse.json({error:"Un compte avec cet email existe d\xe9j\xe0"},{status:409})}catch(e){if(console.log("Erreur de connexion \xe0 la base de donn\xe9es, mode d\xe9mo activ\xe9"),"<EMAIL>"===s){let e=c().sign({userId:"demo-admin-id",email:s,role:"ADMIN"},process.env.JWT_SECRET||"fallback-secret",{expiresIn:"7d"}),r=d.NextResponse.json({message:"Inscription r\xe9ussie (mode d\xe9mo)",user:{id:"demo-admin-id",email:s,firstName:a,lastName:i,role:"ADMIN"}});return r.cookies.set("auth-token",e,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800}),r}return d.NextResponse.json({error:"Base de donn\xe9es temporairement indisponible. Veuillez r\xe9essayer plus tard."},{status:503})}let g=await u.Ay.hash(o,12),h="<EMAIL>"===s;try{r=await n.z.customer.create({data:{id:t(55511).randomUUID(),email:s,password:g,firstName:a,lastName:i,phone:l,address:p,postalCode:m,city:x,role:h?"ADMIN":"USER",updatedAt:new Date}})}catch(e){return console.log("Erreur lors de la cr\xe9ation de l'utilisateur:",e),d.NextResponse.json({error:"Erreur lors de la cr\xe9ation du compte. Base de donn\xe9es indisponible."},{status:503})}let f=c().sign({userId:r.id,email:r.email,role:r.role},process.env.JWT_SECRET||"fallback-secret",{expiresIn:"7d"}),v=d.NextResponse.json({message:"Inscription r\xe9ussie",user:{id:r.id,email:r.email,firstName:r.firstName,lastName:r.lastName,role:r.role}});return v.cookies.set("auth-token",f,{httpOnly:!0,secure:!0,sameSite:"lax",maxAge:604800,path:"/"}),v}catch(e){return console.error("Erreur lors de l'inscription:",e),d.NextResponse.json({error:"Erreur interne du serveur"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/auth/register/route",pathname:"/api/auth/register",filename:"route",bundlePath:"app/api/auth/register/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\auth\\register\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:h}=m;function f(){return(0,i.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,2783,2171],()=>t(91458));module.exports=s})();