(()=>{var e={};e.id=146,e.ids=[146],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},4967:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>v,routeModule:()=>p,serverHooks:()=>Y,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>j});var r={};i.r(r),i.d(r,{POST:()=>m});var a=i(73194),s=i(42355),o=i(41650),n=i(85514),c=i(42548),d=i(89909),u=i(63723);async function m(e){try{let t=await e.json(),i=d.ib.parse(t),r=await n.z.customer.findUnique({where:{email:i.customer.email}});r||(r=await n.z.customer.create({data:{id:globalThis.crypto.randomUUID(),email:i.customer.email,password:"",firstName:i.customer.firstName,lastName:i.customer.lastName,phone:i.customer.phone||"",address:i.customer.address||"",postalCode:i.customer.postalCode||"",city:i.customer.city||"",updatedAt:new Date}}));let a=0;for(let e of i.items)a+=e.price*e.quantity;let s=await n.z.order.create({data:{customerId:r.id,totalAmount:a,shippingAddress:i.shippingAddress,id:globalThis.crypto.randomUUID(),updatedAt:new Date,items:{create:i.items.map(e=>({id:globalThis.crypto.randomUUID(),productId:e.productId,variantId:e.variantId,quantity:e.quantity,price:e.price}))}},include:{customer:!0,items:{include:{product:!0,variant:!0}}}}),o=s.items.map(e=>({price_data:{currency:"eur",product_data:{name:`${e.product.name} - ${e.variant.flavor}`,description:e.product.description,images:e.product.image?[e.product.image]:[],metadata:{productId:e.productId,variantId:e.variantId}},unit_amount:Math.round(100*Number(e.price))},quantity:e.quantity}));25>Number(s.totalAmount)&&o.push({price_data:{currency:"eur",product_data:{name:"Frais de livraison",description:"Livraison standard",images:[],metadata:{productId:"shipping",variantId:"standard"}},unit_amount:490},quantity:1});let m=await c._.checkout.sessions.create({payment_method_types:["card"],line_items:o,mode:"payment",customer_email:s.customer.email,success_url:`${process.env.NEXTAUTH_URL}/success?session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${process.env.NEXTAUTH_URL}/cancel`,metadata:{orderId:s.id,customerId:s.customerId},shipping_address_collection:{allowed_countries:["FR","BE","CH","LU","MC"]},billing_address_collection:"required",phone_number_collection:{enabled:!0},custom_text:{shipping_address:{message:"Veuillez indiquer votre adresse de livraison pour vos d\xe9licieux bonbons Deltagum !"}}});await n.z.order.update({where:{id:s.id},data:{stripePaymentId:m.id}});let p={success:!0,data:{sessionId:m.id,url:m.url}};return u.NextResponse.json(p)}catch(t){console.error("Error creating checkout session:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la cr\xe9ation de la session de paiement"};return u.NextResponse.json(e,{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/checkout/route",pathname:"/api/checkout",filename:"route",bundlePath:"app/api/checkout/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:l,workUnitAsyncStorage:j,serverHooks:Y}=p;function v(){return(0,o.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:j})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42548:(e,t,i)=>{"use strict";i.d(t,{_:()=>a});var r=i(98467);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not defined in environment variables");let a=new r.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil",typescript:!0})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85514:(e,t,i)=>{"use strict";let r;i.d(t,{z:()=>s});let a=require("@prisma/client");try{r=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let s=r},89536:()=>{},89909:(e,t,i)=>{"use strict";i.d(t,{HU:()=>c,L1:()=>l,ib:()=>Y,ie:()=>p,k:()=>d,yo:()=>j,yz:()=>o});var r=i(61412);let a=r.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),s=r.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);r.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let o=r.Ik({id:r.Yj().optional(),email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:r.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=r.Ik({firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide").optional(),street:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:r.Yj().min(2,"Pays requis"),phone:r.Yj().optional()}),c=r.Ik({id:r.Yj().optional(),name:r.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:r.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:r.ai().positive("Le prix doit \xeatre positif"),image:r.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:r.zM().default(!0),dosage:r.Yj().optional(),variants:r.YO(r.bz()).optional(),pricingTiers:r.YO(r.bz()).optional()}),d=r.Ik({id:r.Yj().optional(),productId:r.Yj(),flavor:a,color:r.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:r.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:r.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:r.YO(r.Yj().url()).default(["/img/placeholder.svg"])}),u=r.Ik({id:r.Yj().optional(),productId:r.Yj(),variantId:r.Yj(),name:r.Yj(),flavor:a,color:r.Yj(),price:r.ai().positive(),quantity:r.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:r.Yj().url()});r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive().max(10,"Maximum 10 articles par produit")});let m=r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive()}),p=r.Ik({customerId:r.Yj().optional(),items:r.YO(m).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:r.ai().positive().optional()}),l=r.Ik({orderId:r.Yj(),status:s});r.Ik({orderId:r.Yj(),amount:r.ai().positive(),currency:r.Yj().length(3).default("EUR")}),r.Ik({type:r.Yj(),data:r.Ik({object:r.bz()})}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:r.Yj(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=r.Ik({name:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide"),subject:r.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:r.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});r.Ik({email:r.Yj().email("Email invalide")}),r.Ik({productId:r.Yj(),customerId:r.Yj(),rating:r.ai().int().min(1).max(5),title:r.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:r.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),r.Ik({emailNotifications:r.zM().default(!0),smsNotifications:r.zM().default(!1),marketingEmails:r.zM().default(!0),language:r.k5(["fr","en"]).default("fr"),currency:r.k5(["EUR","USD"]).default("EUR")});let Y=r.Ik({customer:o,shippingAddress:n,paymentMethod:r.k5(["card","paypal","apple_pay","google_pay"]),items:r.YO(u).min(1,"Au moins un article requis"),promoCode:r.Yj().optional(),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[7583,5696,1412,8467],()=>i(4967));module.exports=r})();