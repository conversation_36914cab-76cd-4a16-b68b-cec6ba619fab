(()=>{var e={};e.id=6857,e.ids=[6857],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42548:(e,r,t)=>{"use strict";t.d(r,{_:()=>o});var s=t(98467);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not defined in environment variables");let o=new s.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil",typescript:!0})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},50386:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>v,routeModule:()=>d,serverHooks:()=>x,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>m});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(73194),i=t(42355),n=t(41650),a=t(85514),u=t(42548),c=t(63723);async function p(e,{params:r}){let{sessionId:t}=await r;try{let e=await u._.checkout.sessions.retrieve(t,{expand:["payment_intent"]});if(!e)return c.NextResponse.json({success:!1,error:"Session non trouv\xe9e"},{status:404});let r=null;e.metadata?.orderId&&(r=await a.z.order.findUnique({where:{id:e.metadata.orderId},include:{customer:!0,items:{include:{product:!0,variant:!0}}}}));let s={success:!0,data:{session:{id:e.id,status:e.status,payment_status:e.payment_status,customer_email:e.customer_email,amount_total:e.amount_total,currency:e.currency,created:e.created,metadata:e.metadata},order:r}};return c.NextResponse.json(s)}catch(e){return console.error("Error retrieving checkout session:",e),c.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration de la session"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/checkout/session/[sessionId]/route",pathname:"/api/checkout/session/[sessionId]",filename:"route",bundlePath:"app/api/checkout/session/[sessionId]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\[sessionId]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:m,serverHooks:x}=d;function v(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:m})}},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>i});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let i=s},89536:()=>{},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,8467],()=>t(50386));module.exports=s})();