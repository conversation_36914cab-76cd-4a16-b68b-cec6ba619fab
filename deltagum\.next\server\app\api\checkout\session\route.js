(()=>{var e={};e.id=8023,e.ids=[8023],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42548:(e,r,t)=>{"use strict";t.d(r,{_:()=>o});var s=t(98467);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not defined in environment variables");let o=new s.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil",typescript:!0})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>i});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let i=s},88624:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>x,routeModule:()=>m,serverHooks:()=>h,workAsyncStorage:()=>_,workUnitAsyncStorage:()=>v});var s={};t.r(s),t.d(s,{POST:()=>l});var o=t(73194),i=t(42355),a=t(41650),n=t(85514),c=t(42548),u=t(63723),d=t(61412);let p=d.Ik({orderId:d.Yj().min(1,"ID de commande requis")});async function l(e){try{let r=await e.json(),{orderId:t}=p.parse(r),s=await n.z.order.findUnique({where:{id:t},include:{customer:!0,items:{include:{product:!0,variant:!0}}}});if(!s)return u.NextResponse.json({success:!1,error:"Commande non trouv\xe9e"},{status:404});if("PAID"===s.status)return u.NextResponse.json({success:!1,error:"Cette commande a d\xe9j\xe0 \xe9t\xe9 pay\xe9e"},{status:400});let o=[];for(let e of s.items)o.push({price_data:{currency:"eur",product_data:{name:`${e.product.name} - ${e.variant?.flavor||"Standard"}`,description:e.product.description,images:e.product.image?[e.product.image.startsWith("http")?e.product.image:`${process.env.NEXTAUTH_URL}${e.product.image}`]:[],metadata:{productId:e.productId,variantId:e.variantId}},unit_amount:Math.round(100*Number(e.price))},quantity:e.quantity});let i=await c._.checkout.sessions.create({payment_method_types:["card"],line_items:o,mode:"payment",customer_email:s.customer.email,success_url:`${process.env.NEXTAUTH_URL}/success?session_id={CHECKOUT_SESSION_ID}`,cancel_url:`${process.env.NEXTAUTH_URL}/cancel`,metadata:{orderId:s.id,customerId:s.customerId},shipping_address_collection:{allowed_countries:["FR","BE","CH","LU","MC"]},billing_address_collection:"required",phone_number_collection:{enabled:!0},custom_text:{shipping_address:{message:"Veuillez indiquer votre adresse de livraison pour vos d\xe9licieux bonbons Deltagum !"}},customer_creation:"if_required",...s.customer.firstName&&s.customer.lastName&&{custom_fields:[{key:"customer_name",label:{type:"custom",custom:"Nom complet"},type:"text",optional:!1}]}});await n.z.order.update({where:{id:s.id},data:{stripePaymentId:i.id}});let a={success:!0,data:{sessionId:i.id,url:i.url,orderId:s.id},message:"Session de paiement cr\xe9\xe9e avec succ\xe8s"};return u.NextResponse.json(a)}catch(r){console.error("Error creating checkout session:",r);let e={success:!1,error:r instanceof Error?r.message:"Erreur lors de la cr\xe9ation de la session de paiement"};return u.NextResponse.json(e,{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/checkout/session/route",pathname:"/api/checkout/session",filename:"route",bundlePath:"app/api/checkout/session/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\checkout\\session\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:_,workUnitAsyncStorage:v,serverHooks:h}=m;function x(){return(0,a.patchFetch)({workAsyncStorage:_,workUnitAsyncStorage:v})}},89536:()=>{},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,1412,8467],()=>t(88624));module.exports=s})();