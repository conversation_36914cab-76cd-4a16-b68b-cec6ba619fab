"use strict";(()=>{var e={};e.id=8746,e.ids=[8746],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26696:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>c,serverHooks:()=>x,workAsyncStorage:()=>g,workUnitAsyncStorage:()=>u});var o={};t.r(o),t.d(o,{POST:()=>l});var s=t(73194),a=t(42355),n=t(41650),i=t(34702),d=t(89909),p=t(63723);async function l(e){try{let r=await e.json(),t=d.yo.parse(r);return await i.o6.emails.send({from:"Acme <<EMAIL>>",to:["<EMAIL>"],subject:`Nouveau message de contact - ${t.subject}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background-color: #f8f9fa;">
          <div style="background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px;">🍭 Deltagum</h1>
            <p style="margin: 10px 0 0 0;">Nouveau message de contact</p>
          </div>

          <div style="padding: 30px; background-color: #fff;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h3 style="color: #333; margin-top: 0; border-bottom: 2px solid #ff6b6b; padding-bottom: 10px;">
                👤 Informations du contact
              </h3>
              <p style="margin: 10px 0;"><strong>Nom :</strong> ${t.name}</p>
              <p style="margin: 10px 0;"><strong>Email :</strong> <a href="mailto:${t.email}" style="color: #ff6b6b;">${t.email}</a></p>
              <p style="margin: 10px 0;"><strong>Sujet :</strong> ${t.subject}</p>
            </div>

            <div style="background-color: #fff; padding: 20px; border: 1px solid #e9ecef; border-left: 4px solid #ff6b6b; border-radius: 8px;">
              <h3 style="color: #333; margin-top: 0;">💬 Message</h3>
              <div style="line-height: 1.6; color: #666; background-color: #f8f9fa; padding: 15px; border-radius: 6px;">
                ${t.message.replace(/\n/g,"<br>")}
              </div>
            </div>

            <div style="margin-top: 30px; padding: 20px; background-color: #e3f2fd; border-radius: 8px;">
              <h4 style="color: #1976d2; margin-top: 0;">📋 Actions recommand\xe9es</h4>
              <ul style="color: #666; margin: 0; padding-left: 20px;">
                <li>R\xe9pondre dans les 24h</li>
                <li>V\xe9rifier si c'est un client existant</li>
                <li>Ajouter \xe0 la liste de contacts si n\xe9cessaire</li>
              </ul>
            </div>
          </div>

          <div style="text-align: center; padding: 20px; background-color: #f8f9fa; color: #666; font-size: 12px;">
            <p style="margin: 0;">
              📅 Message re\xe7u le ${new Date().toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})}
            </p>
            <p style="margin: 5px 0 0 0;">\xa9 2024 Deltagum - Syst\xe8me de contact automatis\xe9</p>
          </div>
        </div>
      `,replyTo:t.email}),await i.o6.emails.send({from:"Acme <<EMAIL>>",to:t.email,subject:"Confirmation de r\xe9ception - Deltagum",html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="text-align: center; padding: 20px; background: linear-gradient(135deg, #ff6b6b, #ffa500); color: white; border-radius: 8px 8px 0 0;">
            <h1 style="margin: 0; font-size: 28px;">🍭 Deltagum</h1>
            <p style="margin: 10px 0 0 0; font-size: 16px;">Merci pour votre message !</p>
          </div>
          
          <div style="padding: 30px; background-color: #fff; border-radius: 0 0 8px 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
            <h2 style="color: #333; margin-top: 0;">Bonjour ${t.name} !</h2>
            
            <p style="color: #666; line-height: 1.6;">
              Nous avons bien re\xe7u votre message concernant "<strong>${t.subject}</strong>" 
              et nous vous remercions de nous avoir contact\xe9s.
            </p>
            
            <p style="color: #666; line-height: 1.6;">
              Notre \xe9quipe examine votre demande et vous r\xe9pondra dans les plus brefs d\xe9lais, 
              g\xe9n\xe9ralement sous 24 heures.
            </p>
            
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
              <h3 style="color: #555; margin-top: 0;">R\xe9capitulatif de votre message</h3>
              <p style="margin: 5px 0;"><strong>Sujet :</strong> ${t.subject}</p>
              <p style="margin: 5px 0;"><strong>Message :</strong></p>
              <p style="color: #666; font-style: italic; margin: 10px 0;">
                "${t.message}"
              </p>
            </div>
            
            <p style="color: #666; line-height: 1.6;">
              En attendant, n'h\xe9sitez pas \xe0 d\xe9couvrir nos d\xe9licieux bonbons aux saveurs 
              fraise, myrtille et pomme sur notre site !
            </p>
            
            <div style="text-align: center; margin: 30px 0;">
              <a href="${process.env.NEXTAUTH_URL}" 
                 style="background: linear-gradient(135deg, #ff6b6b, #ffa500); 
                        color: white; 
                        padding: 12px 30px; 
                        text-decoration: none; 
                        border-radius: 25px; 
                        font-weight: bold;
                        display: inline-block;">
                D\xe9couvrir nos bonbons
              </a>
            </div>
          </div>
          
          <div style="text-align: center; padding: 20px; color: #999; font-size: 12px;">
            <p>\xa9 2024 Deltagum - Tous droits r\xe9serv\xe9s</p>
            <p>Des bonbons qui font sourire ! 🍭✨</p>
          </div>
        </div>
      `}),p.NextResponse.json({success:!0,message:"Message envoy\xe9 avec succ\xe8s"})}catch(r){console.error("Error sending contact message:",r);let e={success:!1,error:r instanceof Error?r.message:"Erreur lors de l'envoi du message"};return p.NextResponse.json(e,{status:500})}}let c=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/contact/route",pathname:"/api/contact",filename:"route",bundlePath:"app/api/contact/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\contact\\route.ts",nextConfigOutput:"",userland:o}),{workAsyncStorage:g,workUnitAsyncStorage:u,serverHooks:x}=c;function m(){return(0,n.patchFetch)({workAsyncStorage:g,workUnitAsyncStorage:u})}},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},84297:e=>{e.exports=require("async_hooks")}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),o=r.X(0,[7583,5696,1412,176],()=>t(26696));module.exports=o})();