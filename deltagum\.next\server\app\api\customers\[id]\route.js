(()=>{var e={};e.id=2077,e.ids=[2077],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31628:(e,t,i)=>{"use strict";i.d(t,{To:()=>a,mQ:()=>s}),process.env.NEXTAUTH_URL;let r={levels:{BRONZE:{name:"Bronze",color:"#CD7F32",minPoints:0,benefits:["Livraison gratuite d\xe8s 25€"]},SILVER:{name:"Argent",color:"#C0C0C0",minPoints:100,benefits:["Livraison gratuite d\xe8s 20€","5% de r\xe9duction"]},GOLD:{name:"Or",color:"#FFD700",minPoints:500,benefits:["Livraison gratuite","10% de r\xe9duction","Acc\xe8s prioritaire aux nouveaut\xe9s"]},PLATINUM:{name:"Platine",color:"#E5E4E2",minPoints:1e3,benefits:["Livraison gratuite","15% de r\xe9duction","Acc\xe8s prioritaire","Cadeaux exclusifs"]}},pointsPerEuro:10};function a(e){return e>=r.levels.PLATINUM.minPoints?"PLATINUM":e>=r.levels.GOLD.minPoints?"GOLD":e>=r.levels.SILVER.minPoints?"SILVER":"BRONZE"}function s(e){return Math.floor(e*r.pointsPerEuro)}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},69808:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>g,routeModule:()=>j,serverHooks:()=>f,workAsyncStorage:()=>Y,workUnitAsyncStorage:()=>v});var r={};i.r(r),i.d(r,{DELETE:()=>p,GET:()=>d,PATCH:()=>m});var a=i(73194),s=i(42355),o=i(41650),n=i(85514),c=i(31628),u=i(89909),l=i(63723);async function d(e,{params:t}){let{id:i}=await t;try{let e=await n.z.customer.findUnique({where:{id:i},include:{orders:{include:{items:{include:{product:!0,variant:!0}}},orderBy:{createdAt:"desc"}}}});if(!e)return l.NextResponse.json({success:!1,error:"Client non trouv\xe9"},{status:404});return l.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching customer:",e),l.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration du client"},{status:500})}}async function m(e,{params:t}){let{id:i}=await t;try{let t=await e.json(),r=u.yz.partial().parse(t),a=await n.z.$transaction(async e=>{let t=await e.customer.update({where:{id:i},data:r,include:{loyalty:!0}});if(t.loyalty){let r=(0,c.To)(t.loyalty.points);r!==t.loyalty.level&&await e.loyaltyProgram.update({where:{customerId:i},data:{level:r}})}return await e.customer.findUnique({where:{id:i},include:{loyalty:!0,orders:{orderBy:{createdAt:"desc"},take:5}}})});return l.NextResponse.json({success:!0,data:a,message:"Client mis \xe0 jour avec succ\xe8s"})}catch(t){console.error("Error updating customer:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la mise \xe0 jour du client"};return l.NextResponse.json(e,{status:400})}}async function p(e,{params:t}){let{id:i}=await t;try{return await n.z.customer.delete({where:{id:i}}),l.NextResponse.json({success:!0,message:"Client supprim\xe9 avec succ\xe8s"})}catch(e){return console.error("Error deleting customer:",e),l.NextResponse.json({success:!1,error:"Erreur lors de la suppression du client"},{status:500})}}let j=new a.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/customers/[id]/route",pathname:"/api/customers/[id]",filename:"route",bundlePath:"app/api/customers/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:Y,workUnitAsyncStorage:v,serverHooks:f}=j;function g(){return(0,o.patchFetch)({workAsyncStorage:Y,workUnitAsyncStorage:v})}},85514:(e,t,i)=>{"use strict";let r;i.d(t,{z:()=>s});let a=require("@prisma/client");try{r=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let s=r},89536:()=>{},89909:(e,t,i)=>{"use strict";i.d(t,{HU:()=>c,L1:()=>p,ib:()=>Y,ie:()=>m,k:()=>u,yo:()=>j,yz:()=>o});var r=i(61412);let a=r.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),s=r.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);r.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let o=r.Ik({id:r.Yj().optional(),email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:r.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=r.Ik({firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide").optional(),street:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:r.Yj().min(2,"Pays requis"),phone:r.Yj().optional()}),c=r.Ik({id:r.Yj().optional(),name:r.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:r.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:r.ai().positive("Le prix doit \xeatre positif"),image:r.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:r.zM().default(!0),dosage:r.Yj().optional(),variants:r.YO(r.bz()).optional(),pricingTiers:r.YO(r.bz()).optional()}),u=r.Ik({id:r.Yj().optional(),productId:r.Yj(),flavor:a,color:r.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:r.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:r.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:r.YO(r.Yj().url()).default(["/img/placeholder.svg"])}),l=r.Ik({id:r.Yj().optional(),productId:r.Yj(),variantId:r.Yj(),name:r.Yj(),flavor:a,color:r.Yj(),price:r.ai().positive(),quantity:r.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:r.Yj().url()});r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive().max(10,"Maximum 10 articles par produit")});let d=r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive()}),m=r.Ik({customerId:r.Yj().optional(),items:r.YO(d).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:r.ai().positive().optional()}),p=r.Ik({orderId:r.Yj(),status:s});r.Ik({orderId:r.Yj(),amount:r.ai().positive(),currency:r.Yj().length(3).default("EUR")}),r.Ik({type:r.Yj(),data:r.Ik({object:r.bz()})}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:r.Yj(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=r.Ik({name:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide"),subject:r.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:r.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});r.Ik({email:r.Yj().email("Email invalide")}),r.Ik({productId:r.Yj(),customerId:r.Yj(),rating:r.ai().int().min(1).max(5),title:r.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:r.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),r.Ik({emailNotifications:r.zM().default(!0),smsNotifications:r.zM().default(!1),marketingEmails:r.zM().default(!0),language:r.k5(["fr","en"]).default("fr"),currency:r.k5(["EUR","USD"]).default("EUR")});let Y=r.Ik({customer:o,shippingAddress:n,paymentMethod:r.k5(["card","paypal","apple_pay","google_pay"]),items:r.YO(l).min(1,"Au moins un article requis"),promoCode:r.Yj().optional(),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[7583,5696,1412],()=>i(69808));module.exports=r})();