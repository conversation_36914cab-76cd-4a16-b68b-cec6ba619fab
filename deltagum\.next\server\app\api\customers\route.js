"use strict";(()=>{var e={};e.id=1941,e.ids=[1941],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67815:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>y,routeModule:()=>m,serverHooks:()=>w,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>p});var a=t(73194),o=t(42355),n=t(41650),i=t(34702),c=t(85514),u=t(89909),l=t(63723);async function d(e){try{let{searchParams:r}=new URL(e.url),t=r.get("email"),s=r.get("limit"),a=r.get("offset"),o=t?{email:t}:{},n=s?parseInt(s):void 0,i=a?parseInt(a):void 0,[u,d]=await Promise.all([c.z.customer.findMany({where:o,include:{orders:{orderBy:{createdAt:"desc"},take:5}},orderBy:{createdAt:"desc"},take:n,skip:i}),c.z.customer.count({where:o})]);return l.NextResponse.json({success:!0,data:{customers:u,total:d}})}catch(e){return console.error("Error fetching customers:",e),l.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration des clients"},{status:500})}}async function p(e){try{let r=await e.json(),s=u.yz.parse(r);if(await c.z.customer.findUnique({where:{email:s.email}}))return l.NextResponse.json({success:!1,error:"Un client avec cet email existe d\xe9j\xe0"},{status:409});let a=await c.z.customer.create({data:{email:s.email,password:s.password||"",firstName:s.firstName,lastName:s.lastName,phone:s.phone||"",address:s.address||"",postalCode:s.postalCode||"",city:s.city||"",id:t(55511).randomUUID(),updatedAt:new Date}});try{await (0,i.v4)(a.email,`${a.firstName} ${a.lastName}`)}catch(e){console.error("Error sending welcome email:",e)}return l.NextResponse.json({success:!0,data:a,message:"Client cr\xe9\xe9 avec succ\xe8s"},{status:201})}catch(r){console.error("Error creating customer:",r);let e={success:!1,error:r instanceof Error?r.message:"Erreur lors de la cr\xe9ation du client"};return l.NextResponse.json(e,{status:400})}}let m=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/customers/route",pathname:"/api/customers",filename:"route",bundlePath:"app/api/customers/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\customers\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:w}=m;function y(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},84297:e=>{e.exports=require("async_hooks")},85514:(e,r,t)=>{let s;t.d(r,{z:()=>o});let a=require("@prisma/client");try{s=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=s}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,1412,176],()=>t(67815));module.exports=s})();