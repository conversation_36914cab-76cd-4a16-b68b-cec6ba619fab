"use strict";(()=>{var e={};e.id=7413,e.ids=[7413],e.modules={3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7483:(e,r,t)=>{t.r(r),t.d(r,{patchFetch:()=>P,routeModule:()=>v,serverHooks:()=>g,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>f});var s={};t.r(s),t.d(s,{GET:()=>m,PATCH:()=>p});var o=t(73194),a=t(42355),n=t(41650),i=t(34702),u=t(85514),c=t(31628),d=t(89909),l=t(63723);async function m(e,{params:r}){let{id:t}=await r;try{let e=await u.z.order.findUnique({where:{id:t},include:{customer:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},items:{include:{product:!0,variant:!0}}}});if(!e)return l.NextResponse.json({success:!1,error:"Commande non trouv\xe9e"},{status:404});return l.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching order:",e),l.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration de la commande"},{status:500})}}async function p(e,{params:r}){let{id:t}=await r;try{let r=await e.json(),s=d.L1.parse({orderId:t,...r}),o=await u.z.$transaction(async e=>{let r=await e.order.update({where:{id:t},data:{status:s.status},include:{customer:{include:{loyalty:!0}},items:{include:{product:!0,variant:!0}}}});if("PAID"===s.status&&r.customer.loyalty){let t=(0,c.mQ)(Number(r.totalAmount));await e.loyaltyProgram.update({where:{customerId:r.customerId},data:{points:{increment:t}}});try{await (0,i.k9)(r.customer.email,{orderId:r.id,customerName:`${r.customer.firstName} ${r.customer.lastName}`,totalAmount:Number(r.totalAmount),items:r.items.map(e=>({name:e.product.name,flavor:e.variant.flavor,quantity:e.quantity,price:Number(e.price)}))})}catch(e){console.error("Error sending confirmation email:",e)}}return r});return l.NextResponse.json({success:!0,data:o,message:"Statut de la commande mis \xe0 jour avec succ\xe8s"})}catch(r){console.error("Error updating order status:",r);let e={success:!1,error:r instanceof Error?r.message:"Erreur lors de la mise \xe0 jour du statut"};return l.NextResponse.json(e,{status:400})}}let v=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/[id]/route",pathname:"/api/orders/[id]",filename:"route",bundlePath:"app/api/orders/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\[id]\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:f,serverHooks:g}=v;function P(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:f})}},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{e.exports=require("stream")},28354:e=>{e.exports=require("util")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31628:(e,r,t)=>{t.d(r,{To:()=>o,mQ:()=>a}),process.env.NEXTAUTH_URL;let s={levels:{BRONZE:{name:"Bronze",color:"#CD7F32",minPoints:0,benefits:["Livraison gratuite d\xe8s 25€"]},SILVER:{name:"Argent",color:"#C0C0C0",minPoints:100,benefits:["Livraison gratuite d\xe8s 20€","5% de r\xe9duction"]},GOLD:{name:"Or",color:"#FFD700",minPoints:500,benefits:["Livraison gratuite","10% de r\xe9duction","Acc\xe8s prioritaire aux nouveaut\xe9s"]},PLATINUM:{name:"Platine",color:"#E5E4E2",minPoints:1e3,benefits:["Livraison gratuite","15% de r\xe9duction","Acc\xe8s prioritaire","Cadeaux exclusifs"]}},pointsPerEuro:10};function o(e){return e>=s.levels.PLATINUM.minPoints?"PLATINUM":e>=s.levels.GOLD.minPoints?"GOLD":e>=s.levels.SILVER.minPoints?"SILVER":"BRONZE"}function a(e){return Math.floor(e*s.pointsPerEuro)}},44870:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{e.exports=require("crypto")},57075:e=>{e.exports=require("node:stream")},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},84297:e=>{e.exports=require("async_hooks")},85514:(e,r,t)=>{let s;t.d(r,{z:()=>a});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let a=s}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,1412,176],()=>t(7483));module.exports=s})();