(()=>{var e={};e.id=9789,e.ids=[9789],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42805:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>Y,routeModule:()=>l,serverHooks:()=>g,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>j});var r={};i.r(r),i.d(r,{GET:()=>u,POST:()=>p});var s=i(73194),a=i(42355),o=i(41650),n=i(85514),d=i(89909),c=i(63723);async function u(e){try{let{searchParams:t}=new URL(e.url),i=t.get("customerId"),r=t.get("status"),s=t.get("limit"),a=t.get("offset"),o={};i&&(o.customerId=i),r&&(o.status=r);let d=s?parseInt(s):void 0,u=a?parseInt(a):void 0,[p,l]=await Promise.all([n.z.order.findMany({where:o,include:{customer:!0,items:{include:{product:!0,variant:!0}}},orderBy:{createdAt:"desc"},take:d,skip:u}),n.z.order.count({where:o})]);return c.NextResponse.json({success:!0,data:{orders:p,total:l}})}catch(e){return console.error("Error fetching orders:",e),c.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration des commandes"},{status:500})}}async function p(e){try{let t,r=e.headers.get("content-type");if(!r||!r.includes("application/json"))return c.NextResponse.json({success:!1,error:"Content-Type doit \xeatre application/json"},{status:400});try{t=await e.json()}catch(e){return console.error("Erreur de parsing JSON:",e),c.NextResponse.json({success:!1,error:"Corps de requ\xeate JSON invalide"},{status:400})}if(!t||"object"!=typeof t)return c.NextResponse.json({success:!1,error:"Corps de requ\xeate manquant ou invalide"},{status:400});if(console.log("\uD83D\uDCE6 Donn\xe9es re\xe7ues pour cr\xe9ation de commande:",t),console.log("\uD83D\uDD0D Prisma client:",n.z?"✅ Disponible":"❌ Undefined"),console.log("\uD83D\uDD0D Prisma product:",n.z?.product?"✅ Disponible":"❌ Undefined"),!n.z)throw Error("Prisma client non initialis\xe9");let s=d.ie.parse(t),a=0,o=[];for(let e of s.items){let t=await n.z.product.findUnique({where:{id:e.productId}});if(!t)throw console.error(`Produit ${e.productId} non trouv\xe9. Articles disponibles:`,await n.z.product.findMany({select:{id:!0,name:!0}})),Error(`Produit ${e.productId} non trouv\xe9. V\xe9rifiez que les produits existent en base de donn\xe9es.`);let i=Number(t.basePrice),r=i*e.quantity;a+=r,o.push({productId:e.productId,variantId:e.variantId,quantity:e.quantity,price:i})}let u=s.customerId;if(u)await n.z.customer.findUnique({where:{id:u}})||(console.error(`Client ${u} non trouv\xe9. Cr\xe9ation d'un client temporaire.`),u=(await n.z.customer.create({data:{id:i(55511).randomUUID(),email:s.shippingAddress.email||`guest-${Date.now()}@deltagum.com`,password:"",firstName:s.shippingAddress.firstName,lastName:s.shippingAddress.lastName,phone:s.shippingAddress.phone||"",address:s.shippingAddress.street||"",postalCode:s.shippingAddress.postalCode,city:s.shippingAddress.city,updatedAt:new Date}})).id);else{let e=s.shippingAddress.email||`guest-${Date.now()}@deltagum.com`,t=await n.z.customer.findUnique({where:{email:e}});t?(console.log(`Client existant trouv\xe9 avec email ${e}, utilisation du client existant`),u=t.id):(u=(await n.z.customer.create({data:{id:i(55511).randomUUID(),email:e,password:"",firstName:s.shippingAddress.firstName,lastName:s.shippingAddress.lastName,phone:s.shippingAddress.phone||"",address:s.shippingAddress.street||"",postalCode:s.shippingAddress.postalCode,city:s.shippingAddress.city,updatedAt:new Date}})).id,console.log(`Nouveau client temporaire cr\xe9\xe9 avec email ${e}`))}let p=await n.z.$transaction(async e=>{let t=i(55511).randomUUID(),r=await e.order.create({data:{id:t,customerId:u,status:"PENDING",totalAmount:s.totalAmount||a,shippingAddress:s.shippingAddress,updatedAt:new Date,items:{create:o}},include:{customer:!0,items:{include:{product:!0,variant:!0}}}});for(let t of s.items)await e.productVariant.update({where:{id:t.variantId},data:{stock:{decrement:t.quantity}}});return r});return c.NextResponse.json({success:!0,data:p,message:"Commande cr\xe9\xe9e avec succ\xe8s"},{status:201})}catch(t){console.error("Error creating order:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la cr\xe9ation de la commande"};return c.NextResponse.json(e,{status:400})}}let l=new s.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/orders/route",pathname:"/api/orders",filename:"route",bundlePath:"app/api/orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:j,serverHooks:g}=l;function Y(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:j})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,t,i)=>{"use strict";let r;i.d(t,{z:()=>a});let s=require("@prisma/client");try{r=new s.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let a=r},89536:()=>{},89909:(e,t,i)=>{"use strict";i.d(t,{HU:()=>d,L1:()=>m,ib:()=>g,ie:()=>l,k:()=>c,yo:()=>j,yz:()=>o});var r=i(61412);let s=r.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),a=r.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);r.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let o=r.Ik({id:r.Yj().optional(),email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:r.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=r.Ik({firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide").optional(),street:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:r.Yj().min(2,"Pays requis"),phone:r.Yj().optional()}),d=r.Ik({id:r.Yj().optional(),name:r.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:r.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:r.ai().positive("Le prix doit \xeatre positif"),image:r.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:r.zM().default(!0),dosage:r.Yj().optional(),variants:r.YO(r.bz()).optional(),pricingTiers:r.YO(r.bz()).optional()}),c=r.Ik({id:r.Yj().optional(),productId:r.Yj(),flavor:s,color:r.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:r.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:r.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:r.YO(r.Yj().url()).default(["/img/placeholder.svg"])}),u=r.Ik({id:r.Yj().optional(),productId:r.Yj(),variantId:r.Yj(),name:r.Yj(),flavor:s,color:r.Yj(),price:r.ai().positive(),quantity:r.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:r.Yj().url()});r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive().max(10,"Maximum 10 articles par produit")});let p=r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive()}),l=r.Ik({customerId:r.Yj().optional(),items:r.YO(p).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:r.ai().positive().optional()}),m=r.Ik({orderId:r.Yj(),status:a});r.Ik({orderId:r.Yj(),amount:r.ai().positive(),currency:r.Yj().length(3).default("EUR")}),r.Ik({type:r.Yj(),data:r.Ik({object:r.bz()})}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:r.Yj(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=r.Ik({name:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide"),subject:r.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:r.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});r.Ik({email:r.Yj().email("Email invalide")}),r.Ik({productId:r.Yj(),customerId:r.Yj(),rating:r.ai().int().min(1).max(5),title:r.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:r.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),r.Ik({emailNotifications:r.zM().default(!0),smsNotifications:r.zM().default(!1),marketingEmails:r.zM().default(!0),language:r.k5(["fr","en"]).default("fr"),currency:r.k5(["EUR","USD"]).default("EUR")});let g=r.Ik({customer:o,shippingAddress:n,paymentMethod:r.k5(["card","paypal","apple_pay","google_pay"]),items:r.YO(u).min(1,"Au moins un article requis"),promoCode:r.Yj().optional(),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[7583,5696,1412],()=>i(42805));module.exports=r})();