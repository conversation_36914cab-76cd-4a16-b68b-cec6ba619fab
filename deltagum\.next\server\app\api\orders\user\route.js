(()=>{var e={};e.id=1593,e.ids=[1593],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},13499:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>f,routeModule:()=>d,serverHooks:()=>v,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>x});var s={};t.r(s),t.d(s,{GET:()=>p});var o=t(73194),u=t(42355),a=t(41650),n=t(85514),i=t(2783),c=t.n(i),l=t(63723);async function p(e){try{let r=function(e){let r=e.cookies.get("auth-token")?.value;if(!r)return null;try{return c().verify(r,process.env.JWT_SECRET||"fallback-secret")}catch{return null}}(e);if(!r)return l.NextResponse.json({success:!1,error:"Non authentifi\xe9"},{status:401});let t=await n.z.order.findMany({where:{customerId:r.userId},include:{customer:{select:{firstName:!0,lastName:!0,email:!0}},items:{include:{product:{select:{name:!0,image:!0}},variant:{select:{flavor:!0,color:!0}}}}},orderBy:{createdAt:"desc"}}),s=t.length,o=t.filter(e=>"DELIVERED"===e.status).length,u=t.filter(e=>"DELIVERED"===e.status).reduce((e,r)=>e+Number(r.totalAmount),0);return l.NextResponse.json({success:!0,data:{orders:t,stats:{totalOrders:s,completedOrders:o,totalSpent:u,averageOrderValue:o>0?u/o:0}}})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des commandes:",e),l.NextResponse.json({success:!1,error:"Erreur serveur"},{status:500})}}let d=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/orders/user/route",pathname:"/api/orders/user",filename:"route",bundlePath:"app/api/orders/user/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\orders\\user\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:x,serverHooks:v}=d;function f(){return(0,a.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:x})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>u});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let u=s},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696,2783],()=>t(13499));module.exports=s})();