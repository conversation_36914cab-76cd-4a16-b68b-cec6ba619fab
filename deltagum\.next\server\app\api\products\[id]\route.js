(()=>{var e={};e.id=3856,e.ids=[3856],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,t,i)=>{"use strict";let r;i.d(t,{z:()=>o});let a=require("@prisma/client");try{r=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=r},89536:()=>{},89909:(e,t,i)=>{"use strict";i.d(t,{HU:()=>c,L1:()=>l,ib:()=>Y,ie:()=>p,k:()=>d,yo:()=>j,yz:()=>s});var r=i(61412);let a=r.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),o=r.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);r.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let s=r.Ik({id:r.Yj().optional(),email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:r.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=r.Ik({firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide").optional(),street:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:r.Yj().min(2,"Pays requis"),phone:r.Yj().optional()}),c=r.Ik({id:r.Yj().optional(),name:r.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:r.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:r.ai().positive("Le prix doit \xeatre positif"),image:r.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:r.zM().default(!0),dosage:r.Yj().optional(),variants:r.YO(r.bz()).optional(),pricingTiers:r.YO(r.bz()).optional()}),d=r.Ik({id:r.Yj().optional(),productId:r.Yj(),flavor:a,color:r.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:r.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:r.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:r.YO(r.Yj().url()).default(["/img/placeholder.svg"])}),u=r.Ik({id:r.Yj().optional(),productId:r.Yj(),variantId:r.Yj(),name:r.Yj(),flavor:a,color:r.Yj(),price:r.ai().positive(),quantity:r.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:r.Yj().url()});r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive().max(10,"Maximum 10 articles par produit")});let m=r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive()}),p=r.Ik({customerId:r.Yj().optional(),items:r.YO(m).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:r.ai().positive().optional()}),l=r.Ik({orderId:r.Yj(),status:o});r.Ik({orderId:r.Yj(),amount:r.ai().positive(),currency:r.Yj().length(3).default("EUR")}),r.Ik({type:r.Yj(),data:r.Ik({object:r.bz()})}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:r.Yj(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=r.Ik({name:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide"),subject:r.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:r.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});r.Ik({email:r.Yj().email("Email invalide")}),r.Ik({productId:r.Yj(),customerId:r.Yj(),rating:r.ai().int().min(1).max(5),title:r.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:r.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),r.Ik({emailNotifications:r.zM().default(!0),smsNotifications:r.zM().default(!1),marketingEmails:r.zM().default(!0),language:r.k5(["fr","en"]).default("fr"),currency:r.k5(["EUR","USD"]).default("EUR")});let Y=r.Ik({customer:s,shippingAddress:n,paymentMethod:r.k5(["card","paypal","apple_pay","google_pay"]),items:r.YO(u).min(1,"Au moins un article requis"),promoCode:r.Yj().optional(),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})},98223:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>g,routeModule:()=>l,serverHooks:()=>v,workAsyncStorage:()=>j,workUnitAsyncStorage:()=>Y});var r={};i.r(r),i.d(r,{DELETE:()=>p,GET:()=>u,PUT:()=>m});var a=i(73194),o=i(42355),s=i(41650),n=i(85514),c=i(89909),d=i(63723);async function u(e,{params:t}){let{id:i}=await t;try{let e=await n.z.product.findUnique({where:{id:i},include:{variants:{orderBy:{flavor:"asc"}}}});if(!e)return d.NextResponse.json({success:!1,error:"Produit non trouv\xe9"},{status:404});return d.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching product:",e),d.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration du produit"},{status:500})}}async function m(e,{params:t}){let{id:i}=await t;try{let t=await e.json(),{variants:r,pricingTiers:a,...o}=c.HU.parse(t),s=await n.z.product.update({where:{id:i},data:o,include:{variants:!0}});return d.NextResponse.json({success:!0,data:s,message:"Produit mis \xe0 jour avec succ\xe8s"})}catch(t){console.error("Error updating product:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la mise \xe0 jour du produit"};return d.NextResponse.json(e,{status:400})}}async function p(e,{params:t}){let{id:i}=await t;try{return await n.z.product.delete({where:{id:i}}),d.NextResponse.json({success:!0,message:"Produit supprim\xe9 avec succ\xe8s"})}catch(e){return console.error("Error deleting product:",e),d.NextResponse.json({success:!1,error:"Erreur lors de la suppression du produit"},{status:500})}}let l=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/products/[id]/route",pathname:"/api/products/[id]",filename:"route",bundlePath:"app/api/products/[id]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:j,workUnitAsyncStorage:Y,serverHooks:v}=l;function g(){return(0,s.patchFetch)({workAsyncStorage:j,workUnitAsyncStorage:Y})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),r=t.X(0,[7583,5696,1412],()=>i(98223));module.exports=r})();