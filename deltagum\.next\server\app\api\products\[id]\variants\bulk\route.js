(()=>{var e={};e.id=9396,e.ids=[9396],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79850:(e,t,a)=>{"use strict";a.r(t),a.d(t,{patchFetch:()=>k,routeModule:()=>p,serverHooks:()=>Y,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>j});var r={};a.r(r),a.d(r,{DELETE:()=>l,POST:()=>u});var i=a(73194),s=a(42355),o=a(41650),n=a(85514),c=a(89909),d=a(63723);async function u(e,{params:t}){let{id:r}=await t;try{let t,{variants:i,action:s}=await e.json();if(!Array.isArray(i))return d.NextResponse.json({success:!1,error:"Format de donn\xe9es invalide"},{status:400});let o=await n.z.product.findUnique({where:{id:r}});if(!o)return d.NextResponse.json({success:!1,error:"Produit non trouv\xe9"},{status:404});switch(s){case"replace":t=await n.z.$transaction(async e=>{await e.productVariant.deleteMany({where:{productId:r}});let t=[];for(let s of i){let i=c.k.parse({...s,productId:r});if(!i.sku){let e=`${o.name.substring(0,3).toUpperCase()}-${i.flavor.toUpperCase()}`;i.sku=`${e}-${Date.now()}-${Math.random().toString(36).substring(2,5)}`}let n=await e.productVariant.create({data:{...i,id:a(55511).randomUUID(),sku:i.sku||`VAR-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,updatedAt:new Date}});t.push(n)}return t});break;case"update_stock":t=await n.z.$transaction(async e=>{let t=[];for(let a of i)if(a.id){let r=await e.productVariant.update({where:{id:a.id},data:{stock:a.stock}});t.push(r)}return t});break;case"create_multiple":t=await n.z.$transaction(async e=>{let t=[];for(let s of i){let i=c.k.parse({...s,productId:r});if(!i.sku){let e=`${o.name.substring(0,3).toUpperCase()}-${i.flavor.toUpperCase()}`;i.sku=`${e}-${Date.now()}-${Math.random().toString(36).substring(2,5)}`}let n=await e.productVariant.create({data:{...i,id:a(55511).randomUUID(),sku:i.sku||`VAR-${Date.now()}-${Math.random().toString(36).substring(2,11)}`,updatedAt:new Date}});t.push(n)}return t});break;default:return d.NextResponse.json({success:!1,error:"Action non support\xe9e"},{status:400})}let u={success:!0,data:t,message:`Variantes ${"replace"===s?"remplac\xe9es":"update_stock"===s?"mises \xe0 jour":"cr\xe9\xe9es"} avec succ\xe8s`};return d.NextResponse.json(u)}catch(t){console.error("Error in bulk variant operation:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de l'op\xe9ration en lot"};return d.NextResponse.json(e,{status:400})}}async function l(e,{params:t}){let{id:a}=await t;try{let{variantIds:t}=await e.json();if(!Array.isArray(t))return d.NextResponse.json({success:!1,error:"Format de donn\xe9es invalide"},{status:400});if((await n.z.orderItem.findMany({where:{variantId:{in:t}}})).length>0)return d.NextResponse.json({success:!1,error:"Impossible de supprimer ces variantes car elles sont li\xe9es \xe0 des commandes"},{status:400});let r=await n.z.productVariant.deleteMany({where:{id:{in:t},productId:a}}),i={success:!0,data:{deletedCount:r.count},message:`${r.count} variante(s) supprim\xe9e(s) avec succ\xe8s`};return d.NextResponse.json(i)}catch(e){return console.error("Error in bulk variant deletion:",e),d.NextResponse.json({success:!1,error:"Erreur lors de la suppression en lot"},{status:500})}}let p=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/products/[id]/variants/bulk/route",pathname:"/api/products/[id]/variants/bulk",filename:"route",bundlePath:"app/api/products/[id]/variants/bulk/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\bulk\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:m,workUnitAsyncStorage:j,serverHooks:Y}=p;function k(){return(0,o.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:j})}},85514:(e,t,a)=>{"use strict";let r;a.d(t,{z:()=>s});let i=require("@prisma/client");try{r=new i.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let s=r},89536:()=>{},89909:(e,t,a)=>{"use strict";a.d(t,{HU:()=>c,L1:()=>m,ib:()=>Y,ie:()=>p,k:()=>d,yo:()=>j,yz:()=>o});var r=a(61412);let i=r.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),s=r.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);r.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let o=r.Ik({id:r.Yj().optional(),email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:r.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=r.Ik({firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide").optional(),street:r.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:r.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:r.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:r.Yj().min(2,"Pays requis"),phone:r.Yj().optional()}),c=r.Ik({id:r.Yj().optional(),name:r.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:r.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:r.ai().positive("Le prix doit \xeatre positif"),image:r.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:r.zM().default(!0),dosage:r.Yj().optional(),variants:r.YO(r.bz()).optional(),pricingTiers:r.YO(r.bz()).optional()}),d=r.Ik({id:r.Yj().optional(),productId:r.Yj(),flavor:i,color:r.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:r.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:r.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:r.YO(r.Yj().url()).default(["/img/placeholder.svg"])}),u=r.Ik({id:r.Yj().optional(),productId:r.Yj(),variantId:r.Yj(),name:r.Yj(),flavor:i,color:r.Yj(),price:r.ai().positive(),quantity:r.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:r.Yj().url()});r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive().max(10,"Maximum 10 articles par produit")});let l=r.Ik({productId:r.Yj(),variantId:r.Yj(),quantity:r.ai().int().positive()}),p=r.Ik({customerId:r.Yj().optional(),items:r.YO(l).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:r.ai().positive().optional()}),m=r.Ik({orderId:r.Yj(),status:s});r.Ik({orderId:r.Yj(),amount:r.ai().positive(),currency:r.Yj().length(3).default("EUR")}),r.Ik({type:r.Yj(),data:r.Ik({object:r.bz()})}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),r.Ik({email:r.Yj().email("Email invalide"),password:r.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:r.Yj(),firstName:r.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=r.Ik({name:r.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:r.Yj().email("Email invalide"),subject:r.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:r.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});r.Ik({email:r.Yj().email("Email invalide")}),r.Ik({productId:r.Yj(),customerId:r.Yj(),rating:r.ai().int().min(1).max(5),title:r.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:r.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),r.Ik({emailNotifications:r.zM().default(!0),smsNotifications:r.zM().default(!1),marketingEmails:r.zM().default(!0),language:r.k5(["fr","en"]).default("fr"),currency:r.k5(["EUR","USD"]).default("EUR")});let Y=r.Ik({customer:o,shippingAddress:n,paymentMethod:r.k5(["card","paypal","apple_pay","google_pay"]),items:r.YO(u).min(1,"Au moins un article requis"),promoCode:r.Yj().optional(),acceptTerms:r.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};var t=require("../../../../../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[7583,5696,1412],()=>a(79850));module.exports=r})();