(()=>{var e={};e.id=5893,e.ids=[5893],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85479:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>j,serverHooks:()=>f,workAsyncStorage:()=>v,workUnitAsyncStorage:()=>Y});var a={};r.r(a),r.d(a,{DELETE:()=>m,GET:()=>d,POST:()=>p,PUT:()=>l});var i=r(73194),s=r(42355),o=r(41650),n=r(85514),c=r(89909),u=r(63723);async function d(e,{params:t}){let{id:r}=await t;try{let e=await n.z.productVariant.findMany({where:{productId:r},orderBy:{flavor:"asc"}});return u.NextResponse.json({success:!0,data:e})}catch(e){return console.error("Error fetching variants:",e),u.NextResponse.json({success:!1,error:"Erreur lors de la r\xe9cup\xe9ration des variantes"},{status:500})}}async function p(e,{params:t}){let{id:a}=await t;try{let t=await e.json(),i=c.k.parse({...t,productId:a}),s=await n.z.product.findUnique({where:{id:a}});if(!s)return u.NextResponse.json({success:!1,error:"Produit non trouv\xe9"},{status:404});if(!i.sku){let e=`${s.name.substring(0,3).toUpperCase()}-${i.flavor.toUpperCase()}`,t=await n.z.productVariant.findMany({where:{sku:{startsWith:e}}});i.sku=`${e}-${t.length+1}`}let o=await n.z.productVariant.create({data:{...i,id:r(55511).randomUUID(),sku:i.sku||`VAR-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,updatedAt:new Date}});return u.NextResponse.json({success:!0,data:o,message:"Variante cr\xe9\xe9e avec succ\xe8s"},{status:201})}catch(t){console.error("Error creating variant:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la cr\xe9ation de la variante"};return u.NextResponse.json(e,{status:400})}}async function l(e,{params:t}){let{id:a}=await t;try{let{variants:t}=await e.json();if(!Array.isArray(t))return u.NextResponse.json({success:!1,error:"Format de donn\xe9es invalide"},{status:400});let i=await n.z.product.findUnique({where:{id:a}});if(!i)return u.NextResponse.json({success:!1,error:"Produit non trouv\xe9"},{status:404});let s=await n.z.$transaction(async e=>{await e.productVariant.deleteMany({where:{productId:a}});let s=[];for(let o of t){let t=c.k.parse({...o,productId:a});if(!t.sku){let e=`${i.name.substring(0,3).toUpperCase()}-${t.flavor.toUpperCase()}`;t.sku=`${e}-${Date.now()}`}let n=await e.productVariant.create({data:{...t,id:r(55511).randomUUID(),sku:t.sku||`VAR-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,updatedAt:new Date}});s.push(n)}return s});return u.NextResponse.json({success:!0,data:s,message:"Variantes mises \xe0 jour avec succ\xe8s"})}catch(t){console.error("Error updating variants:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la mise \xe0 jour des variantes"};return u.NextResponse.json(e,{status:400})}}async function m(e,{params:t}){let{id:r}=await t;try{return await n.z.productVariant.deleteMany({where:{productId:r}}),u.NextResponse.json({success:!0,message:"Toutes les variantes ont \xe9t\xe9 supprim\xe9es"})}catch(e){return console.error("Error deleting variants:",e),u.NextResponse.json({success:!1,error:"Erreur lors de la suppression des variantes"},{status:500})}}let j=new i.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/products/[id]/variants/route",pathname:"/api/products/[id]/variants",filename:"route",bundlePath:"app/api/products/[id]/variants/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\[id]\\variants\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:v,workUnitAsyncStorage:Y,serverHooks:f}=j;function g(){return(0,o.patchFetch)({workAsyncStorage:v,workUnitAsyncStorage:Y})}},85514:(e,t,r)=>{"use strict";let a;r.d(t,{z:()=>s});let i=require("@prisma/client");try{a=new i.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let s=a},89536:()=>{},89909:(e,t,r)=>{"use strict";r.d(t,{HU:()=>c,L1:()=>m,ib:()=>v,ie:()=>l,k:()=>u,yo:()=>j,yz:()=>o});var a=r(61412);let i=a.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),s=a.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);a.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let o=a.Ik({id:a.Yj().optional(),email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:a.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:a.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:a.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:a.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),n=a.Ik({firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:a.Yj().email("Email invalide").optional(),street:a.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:a.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:a.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:a.Yj().min(2,"Pays requis"),phone:a.Yj().optional()}),c=a.Ik({id:a.Yj().optional(),name:a.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:a.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:a.ai().positive("Le prix doit \xeatre positif"),image:a.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:a.zM().default(!0),dosage:a.Yj().optional(),variants:a.YO(a.bz()).optional(),pricingTiers:a.YO(a.bz()).optional()}),u=a.Ik({id:a.Yj().optional(),productId:a.Yj(),flavor:i,color:a.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:a.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:a.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:a.YO(a.Yj().url()).default(["/img/placeholder.svg"])}),d=a.Ik({id:a.Yj().optional(),productId:a.Yj(),variantId:a.Yj(),name:a.Yj(),flavor:i,color:a.Yj(),price:a.ai().positive(),quantity:a.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:a.Yj().url()});a.Ik({productId:a.Yj(),variantId:a.Yj(),quantity:a.ai().int().positive().max(10,"Maximum 10 articles par produit")});let p=a.Ik({productId:a.Yj(),variantId:a.Yj(),quantity:a.ai().int().positive()}),l=a.Ik({customerId:a.Yj().optional(),items:a.YO(p).min(1,"Au moins un article requis"),shippingAddress:n,totalAmount:a.ai().positive().optional()}),m=a.Ik({orderId:a.Yj(),status:s});a.Ik({orderId:a.Yj(),amount:a.ai().positive(),currency:a.Yj().length(3).default("EUR")}),a.Ik({type:a.Yj(),data:a.Ik({object:a.bz()})}),a.Ik({email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),a.Ik({email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:a.Yj(),firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:a.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let j=a.Ik({name:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:a.Yj().email("Email invalide"),subject:a.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:a.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});a.Ik({email:a.Yj().email("Email invalide")}),a.Ik({productId:a.Yj(),customerId:a.Yj(),rating:a.ai().int().min(1).max(5),title:a.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:a.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),a.Ik({emailNotifications:a.zM().default(!0),smsNotifications:a.zM().default(!1),marketingEmails:a.zM().default(!0),language:a.k5(["fr","en"]).default("fr"),currency:a.k5(["EUR","USD"]).default("EUR")});let v=a.Ik({customer:o,shippingAddress:n,paymentMethod:a.k5(["card","paypal","apple_pay","google_pay"]),items:a.YO(d).min(1,"Au moins un article requis"),promoCode:a.Yj().optional(),acceptTerms:a.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7583,5696,1412],()=>r(85479));module.exports=a})();