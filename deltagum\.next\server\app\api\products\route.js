(()=>{var e={};e.id=3146,e.ids=[3146],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8062:(e,t,i)=>{"use strict";i.r(t),i.d(t,{patchFetch:()=>I,routeModule:()=>m,serverHooks:()=>j,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>g});var a={};i.r(a),i.d(a,{GET:()=>u,POST:()=>p});var r=i(73194),o=i(42355),n=i(41650),s=i(85514),c=i(89909),d=i(63723);async function u(e){try{let{searchParams:t}=new URL(e.url),i=t.get("active"),a=t.get("limit"),r=t.get("offset"),o="true"===i?{active:!0}:{},n=a?parseInt(a):void 0,c=r?parseInt(r):void 0,[u,p]=await Promise.all([s.z.product.findMany({where:o,include:{variants:{orderBy:{flavor:"asc"}},priceTiers:{orderBy:{quantity:"asc"}}},orderBy:{createdAt:"desc"},take:n,skip:c}),s.z.product.count({where:o})]);return d.NextResponse.json({success:!0,data:{products:u,total:p}})}catch(t){console.error("Error fetching products:",t),console.log("Utilisation des donn\xe9es de d\xe9monstration...");let e={success:!0,data:{products:[{id:"1",name:"Bonbons Delta-9",description:"Nos d\xe9licieux bonbons Delta-9 aux saveurs naturelles. Parfait pour une exp\xe9rience relaxante et savoureuse.",image:"/img/product/packaging-group-deltagum.jpg",basePrice:12,dosage:"10mg",active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),priceTiers:[{id:"1",productId:"1",quantity:1,price:12,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",productId:"1",quantity:3,price:30,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"3",productId:"1",quantity:5,price:45,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],variants:[{id:"1",productId:"1",flavor:"fraise",color:"#ff6b6b",stock:50,images:["/img/product/deltagum-fraise-main1.png","/img/product/deltagum-fraise-main2.png"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"2",productId:"1",flavor:"myrtille",color:"#4ecdc4",stock:45,images:["/img/product/deltagum-myrtille-main1.png","/img/product/deltagum-myrtille-main2.png"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"3",productId:"1",flavor:"pomme",color:"#95e1d3",stock:40,images:["/img/product/deltagum-apple-main1.png","/img/product/deltagum-apple-main2.png"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}]},{id:"2",name:"Cookies Delta-9",description:"D\xe9licieux cookies Delta-9 pour une exp\xe9rience gourmande unique. Parfait pour accompagner votre pause d\xe9tente.",image:"/img/product/packaging-group-cookie.png",basePrice:15,dosage:"15mg",active:!0,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString(),priceTiers:[{id:"4",productId:"2",quantity:1,price:15,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()},{id:"5",productId:"2",quantity:3,price:40,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}],variants:[{id:"4",productId:"2",flavor:"chocolat",color:"#8b4513",stock:30,images:["/img/product/cookie.png"],createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}]}],total:2}};return d.NextResponse.json(e)}}async function p(e){try{let t=await e.json(),{variants:a,pricingTiers:r,...o}=c.HU.parse(t),n={...o,id:i(55511).randomUUID(),updatedAt:new Date},u=await s.z.$transaction(async e=>{let t=await e.product.create({data:n});if(a&&Array.isArray(a)&&a.length>0)for(let i of a){if(!i.sku){let e=`${t.name.substring(0,3).toUpperCase()}-${i.flavor.toUpperCase()}`;i.sku=`${e}-${Date.now()}`}await e.productVariant.create({data:{...i,productId:t.id}})}if(r&&Array.isArray(r)&&r.length>0)for(let i of r)await e.priceTier.create({data:{...i,productId:t.id}});return await e.product.findUnique({where:{id:t.id},include:{variants:!0,priceTiers:!0}})});return d.NextResponse.json({success:!0,data:u,message:"Produit cr\xe9\xe9 avec succ\xe8s"},{status:201})}catch(t){console.error("Error creating product:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de la cr\xe9ation du produit"};return d.NextResponse.json(e,{status:400})}}let m=new r.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/products/route",pathname:"/api/products",filename:"route",bundlePath:"app/api/products/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\products\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:l,workUnitAsyncStorage:g,serverHooks:j}=m;function I(){return(0,n.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:g})}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,t,i)=>{"use strict";let a;i.d(t,{z:()=>o});let r=require("@prisma/client");try{a=new r.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=a},89536:()=>{},89909:(e,t,i)=>{"use strict";i.d(t,{HU:()=>c,L1:()=>l,ib:()=>j,ie:()=>m,k:()=>d,yo:()=>g,yz:()=>n});var a=i(61412);let r=a.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),o=a.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);a.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let n=a.Ik({id:a.Yj().optional(),email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:a.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:a.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:a.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:a.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),s=a.Ik({firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:a.Yj().email("Email invalide").optional(),street:a.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:a.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:a.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:a.Yj().min(2,"Pays requis"),phone:a.Yj().optional()}),c=a.Ik({id:a.Yj().optional(),name:a.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:a.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:a.ai().positive("Le prix doit \xeatre positif"),image:a.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:a.zM().default(!0),dosage:a.Yj().optional(),variants:a.YO(a.bz()).optional(),pricingTiers:a.YO(a.bz()).optional()}),d=a.Ik({id:a.Yj().optional(),productId:a.Yj(),flavor:r,color:a.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:a.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:a.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:a.YO(a.Yj().url()).default(["/img/placeholder.svg"])}),u=a.Ik({id:a.Yj().optional(),productId:a.Yj(),variantId:a.Yj(),name:a.Yj(),flavor:r,color:a.Yj(),price:a.ai().positive(),quantity:a.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:a.Yj().url()});a.Ik({productId:a.Yj(),variantId:a.Yj(),quantity:a.ai().int().positive().max(10,"Maximum 10 articles par produit")});let p=a.Ik({productId:a.Yj(),variantId:a.Yj(),quantity:a.ai().int().positive()}),m=a.Ik({customerId:a.Yj().optional(),items:a.YO(p).min(1,"Au moins un article requis"),shippingAddress:s,totalAmount:a.ai().positive().optional()}),l=a.Ik({orderId:a.Yj(),status:o});a.Ik({orderId:a.Yj(),amount:a.ai().positive(),currency:a.Yj().length(3).default("EUR")}),a.Ik({type:a.Yj(),data:a.Ik({object:a.bz()})}),a.Ik({email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),a.Ik({email:a.Yj().email("Email invalide"),password:a.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:a.Yj(),firstName:a.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:a.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let g=a.Ik({name:a.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:a.Yj().email("Email invalide"),subject:a.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:a.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});a.Ik({email:a.Yj().email("Email invalide")}),a.Ik({productId:a.Yj(),customerId:a.Yj(),rating:a.ai().int().min(1).max(5),title:a.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:a.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),a.Ik({emailNotifications:a.zM().default(!0),smsNotifications:a.zM().default(!1),marketingEmails:a.zM().default(!0),language:a.k5(["fr","en"]).default("fr"),currency:a.k5(["EUR","USD"]).default("EUR")});let j=a.Ik({customer:n,shippingAddress:s,paymentMethod:a.k5(["card","paypal","apple_pay","google_pay"]),items:a.YO(u).min(1,"Au moins un article requis"),promoCode:a.Yj().optional(),acceptTerms:a.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};var t=require("../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[7583,5696,1412],()=>i(8062));module.exports=a})();