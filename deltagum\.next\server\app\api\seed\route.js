(()=>{var e={};e.id=1617,e.ids=[1617],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},74323:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>y,routeModule:()=>l,serverHooks:()=>D,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>g});var a={};r.r(a),r.d(a,{DELETE:()=>p,GET:()=>c,POST:()=>u});var o=r(73194),s=r(42355),i=r(41650),d=r(85514),n=r(63723);async function u(e){try{if(await d.z.product.count()>0)return n.NextResponse.json({success:!1,error:"Des produits existent d\xe9j\xe0 dans la base de donn\xe9es"},{status:400});let e=await d.z.$transaction(async e=>{let t=await e.product.create({data:{id:globalThis.crypto.randomUUID(),name:"Bonbons Delta-9",description:"Nos d\xe9licieux bonbons Delta-9 aux saveurs naturelles. Parfait pour une exp\xe9rience relaxante et savoureuse.",image:"/img/product/packaging-group-deltagum.jpg",basePrice:12,dosage:"10mg",active:!0,updatedAt:new Date}});await e.productVariant.createMany({data:[{id:globalThis.crypto.randomUUID(),productId:t.id,flavor:"STRAWBERRY",color:"#ff6b9d",stock:50,sku:`BON-FRAISE-${Date.now()}`,images:["/img/product/deltagum-fraise-main1.jpg","/img/product/deltagum-fraise-main2.jpg"],updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:t.id,flavor:"BLUEBERRY",color:"#6b73ff",stock:45,sku:`BON-MYRTILLE-${Date.now()}`,images:["/img/product/deltagum-myrtille-main1.jpg","/img/product/deltagum-myrtille-main2.jpg"],updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:t.id,flavor:"APPLE",color:"#9dff6b",stock:40,sku:`BON-POMME-${Date.now()}`,images:["/img/product/deltagum-pomme-main1.jpg","/img/product/deltagum-pomme-main2.jpg"],updatedAt:new Date}]}),await e.priceTier.createMany({data:[{id:globalThis.crypto.randomUUID(),productId:t.id,quantity:1,price:12,updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:t.id,quantity:3,price:30,updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:t.id,quantity:5,price:45,updatedAt:new Date}]});let r=await e.product.create({data:{id:globalThis.crypto.randomUUID(),name:"Cookies Delta-9",description:"Nos cookies artisanaux infus\xe9s au Delta-9. Une texture moelleuse et un go\xfbt authentique pour une exp\xe9rience unique.",image:"/img/product/packaging-group-deltagum.jpg",basePrice:15,dosage:"15mg",active:!0,updatedAt:new Date}});return await e.productVariant.createMany({data:[{id:globalThis.crypto.randomUUID(),productId:r.id,flavor:"CHOCOLATE",color:"#8b4513",stock:30,sku:`COO-CHOCOLAT-${Date.now()}`,images:["/img/product/cookie.png"],updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:r.id,flavor:"VANILLA",color:"#f5deb3",stock:25,sku:`COO-VANILLE-${Date.now()}`,images:["/img/product/cookie.png"],updatedAt:new Date}]}),await e.priceTier.createMany({data:[{id:globalThis.crypto.randomUUID(),productId:r.id,quantity:1,price:15,updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:r.id,quantity:2,price:25,updatedAt:new Date},{id:globalThis.crypto.randomUUID(),productId:r.id,quantity:4,price:45,updatedAt:new Date}]}),[t,r]}),t=await d.z.customer.create({data:{id:globalThis.crypto.randomUUID(),email:"<EMAIL>",password:"test123",firstName:"Test",lastName:"User",phone:"0123456789",address:"123 Rue de Test",postalCode:"75001",city:"Paris",role:"USER",updatedAt:new Date}}),r={success:!0,data:{products:e.length,customer:t.id},message:`${e.length} produits et 1 client de test cr\xe9\xe9s avec succ\xe8s`};return n.NextResponse.json(r,{status:201})}catch(t){console.error("Error seeding database:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur lors de l'initialisation"};return n.NextResponse.json(e,{status:500})}}async function c(){try{let[e,t,r]=await Promise.all([d.z.product.count(),d.z.customer.count(),d.z.order.count()]);return n.NextResponse.json({success:!0,data:{products:e,customers:t,orders:r,isEmpty:0===e&&0===t&&0===r}})}catch(e){return console.error("Error checking database:",e),n.NextResponse.json({success:!1,error:"Erreur lors de la v\xe9rification de la base de donn\xe9es"},{status:500})}}async function p(){return n.NextResponse.json({success:!1,error:"Cette action n'est disponible qu'en mode d\xe9veloppement"},{status:403})}let l=new o.AppRouteRouteModule({definition:{kind:s.RouteKind.APP_ROUTE,page:"/api/seed/route",pathname:"/api/seed",filename:"route",bundlePath:"app/api/seed/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\seed\\route.ts",nextConfigOutput:"",userland:a}),{workAsyncStorage:m,workUnitAsyncStorage:g,serverHooks:D}=l;function y(){return(0,i.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:g})}},85514:(e,t,r)=>{"use strict";let a;r.d(t,{z:()=>s});let o=require("@prisma/client");try{a=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let s=a},89536:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[7583,5696],()=>r(74323));module.exports=a})();