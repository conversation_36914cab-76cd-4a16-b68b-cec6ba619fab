(()=>{var e={};e.id=5681,e.ids=[5681],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14007:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>m,routeModule:()=>p,serverHooks:()=>x,workAsyncStorage:()=>d,workUnitAsyncStorage:()=>l});var s={};t.r(s),t.d(s,{GET:()=>c});var n=t(73194),o=t(42355),a=t(41650),i=t(85514),u=t(63723);async function c(){try{let e=await i.z.$queryRaw`SELECT 1 as test`;return u.NextResponse.json({success:!0,message:"Connexion \xe0 la base de donn\xe9es r\xe9ussie",result:e})}catch(e){return console.error("Erreur de connexion \xe0 la base de donn\xe9es:",e),u.NextResponse.json({success:!1,message:"Erreur de connexion \xe0 la base de donn\xe9es",error:e instanceof Error?e.message:"Erreur inconnue"},{status:500})}}let p=new n.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test-db/route",pathname:"/api/test-db",filename:"route",bundlePath:"app/api/test-db/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-db\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:d,workUnitAsyncStorage:l,serverHooks:x}=p;function m(){return(0,a.patchFetch)({workAsyncStorage:d,workUnitAsyncStorage:l})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>o});let n=require("@prisma/client");try{s=new n.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=s},89536:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696],()=>t(14007));module.exports=s})();