(()=>{var e={};e.id=1744,e.ids=[1744],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26493:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>g,routeModule:()=>u,serverHooks:()=>p,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>d});var s={};r.r(s),r.d(s,{GET:()=>l});var o=r(73194),a=r(42355),n=r(41650),c=r(85514),i=r(63723);async function l(){try{console.log("\uD83E\uDDEA Test Commandes - D\xe9but"),console.log("\uD83D\uDD0D Test comptage commandes...");let e=await c.z.order.count();console.log("\uD83D\uDCE6 Nombre total de commandes:",e),console.log("\uD83D\uDD0D Test r\xe9cup\xe9ration commandes...");let t=await c.z.order.findMany({include:{customer:{select:{id:!0,email:!0,firstName:!0,lastName:!0}},items:{include:{product:{select:{id:!0,name:!0,image:!0}},variant:{select:{id:!0,flavor:!0}}}}},orderBy:{createdAt:"desc"}});console.log("\uD83D\uDCCB Commandes trouv\xe9es:",t.length),t.forEach((e,t)=>{console.log(`📦 Commande ${t+1}:`,{id:e.id,customerId:e.customerId,customerEmail:e.customer.email,status:e.status,totalAmount:e.totalAmount,itemsCount:e.items.length,createdAt:e.createdAt})}),console.log("\uD83D\uDD0D Test comptage clients...");let r=await c.z.customer.count();console.log("\uD83D\uDC65 Nombre total de clients:",r);let s=await c.z.customer.findMany({select:{id:!0,email:!0,firstName:!0,lastName:!0,role:!0}});console.log("\uD83D\uDC65 Clients trouv\xe9s:"),s.forEach((e,t)=>{console.log(`👤 Client ${t+1}:`,{id:e.id,email:e.email,name:`${e.firstName} ${e.lastName}`,role:e.role})});let o={success:!0,data:{totalOrders:e,totalCustomers:r,orders:t.map(e=>({id:e.id,customerId:e.customerId,customerEmail:e.customer.email,status:e.status,totalAmount:e.totalAmount,itemsCount:e.items.length,createdAt:e.createdAt})),customers:s},message:"Test commandes r\xe9ussi"};return i.NextResponse.json(o)}catch(t){console.error("❌ Erreur test commandes:",t);let e={success:!1,error:t instanceof Error?t.message:"Erreur inconnue"};return i.NextResponse.json(e,{status:500})}}let u=new o.AppRouteRouteModule({definition:{kind:a.RouteKind.APP_ROUTE,page:"/api/test-orders/route",pathname:"/api/test-orders",filename:"route",bundlePath:"app/api/test-orders/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-orders\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:m,workUnitAsyncStorage:d,serverHooks:p}=u;function g(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:d})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,t,r)=>{"use strict";let s;r.d(t,{z:()=>a});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let a=s},89536:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7583,5696],()=>r(26493));module.exports=s})();