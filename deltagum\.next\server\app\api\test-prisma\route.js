(()=>{var e={};e.id=4707,e.ids=[4707],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},40518:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>g,routeModule:()=>p,serverHooks:()=>m,workAsyncStorage:()=>l,workUnitAsyncStorage:()=>d});var s={};t.r(s),t.d(s,{GET:()=>c});var o=t(73194),n=t(42355),i=t(41650),a=t(85514),u=t(63723);async function c(){try{if(console.log("\uD83E\uDDEA Test Prisma - D\xe9but"),console.log("\uD83D\uDD0D Prisma client:",a.z?"✅ Disponible":"❌ Undefined"),!a.z)return u.NextResponse.json({success:!1,error:"Prisma client non initialis\xe9"},{status:500});console.log("\uD83D\uDD0D Test de connexion...");let e=await a.z.$queryRaw`SELECT 1 as test`;console.log("✅ Connexion r\xe9ussie:",e),console.log("\uD83D\uDD0D Test comptage produits...");let r=await a.z.product.count();return console.log("\uD83D\uDCE6 Nombre de produits:",r),u.NextResponse.json({success:!0,data:{prismaAvailable:!0,connectionTest:e,productCount:r},message:"Test Prisma r\xe9ussi"})}catch(r){console.error("❌ Erreur test Prisma:",r);let e={success:!1,error:r instanceof Error?r.message:"Erreur inconnue"};return u.NextResponse.json(e,{status:500})}}let p=new o.AppRouteRouteModule({definition:{kind:n.RouteKind.APP_ROUTE,page:"/api/test-prisma/route",pathname:"/api/test-prisma",filename:"route",bundlePath:"app/api/test-prisma/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-prisma\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:l,workUnitAsyncStorage:d,serverHooks:m}=p;function g(){return(0,i.patchFetch)({workAsyncStorage:l,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},85514:(e,r,t)=>{"use strict";let s;t.d(r,{z:()=>n});let o=require("@prisma/client");try{s=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let n=s},89536:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696],()=>t(40518));module.exports=s})();