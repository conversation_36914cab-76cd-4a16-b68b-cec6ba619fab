(()=>{var e={};e.id=5132,e.ids=[5132],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42598:(e,t,s)=>{"use strict";s.r(t),s.d(t,{patchFetch:()=>x,routeModule:()=>p,serverHooks:()=>l,workAsyncStorage:()=>c,workUnitAsyncStorage:()=>d});var r={};s.r(r),s.d(r,{GET:()=>u});var a=s(73194),o=s(42355),n=s(41650),i=s(63723);async function u(){try{console.log("\uD83E\uDDEA Test de l'API des statistiques...");let e=await fetch("http://localhost:3000/api/admin/stats"),t=await e.json();return console.log("\uD83D\uDCCA R\xe9ponse API stats:",t),i.NextResponse.json({success:!0,message:"Test des statistiques r\xe9ussi",data:t})}catch(e){return console.error("❌ Erreur test stats:",e),i.NextResponse.json({success:!1,error:"Erreur lors du test des statistiques",details:e instanceof Error?e.message:String(e)},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test-stats/route",pathname:"/api/test-stats",filename:"route",bundlePath:"app/api/test-stats/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test-stats\\route.ts",nextConfigOutput:"",userland:r}),{workAsyncStorage:c,workUnitAsyncStorage:d,serverHooks:l}=p;function x(){return(0,n.patchFetch)({workAsyncStorage:c,workUnitAsyncStorage:d})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},89536:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7583,5696],()=>s(42598));module.exports=r})();