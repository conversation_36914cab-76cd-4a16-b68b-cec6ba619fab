(()=>{var e={};e.id=5154,e.ids=[5154],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73299:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>x,routeModule:()=>d,serverHooks:()=>m,workAsyncStorage:()=>p,workUnitAsyncStorage:()=>l});var s={};r.r(s),r.d(s,{GET:()=>c});var a=r(73194),o=r(42355),n=r(41650),i=r(85514),u=r(63723);async function c(){try{let e=Date.now();await i.z.$queryRaw`SELECT 1 as test`;let t=Date.now()-e,[r,s,a]=await Promise.all([i.z.product.count(),i.z.customer.count(),i.z.order.count()]);return u.NextResponse.json({success:!0,message:"Connexion \xe0 la base de donn\xe9es r\xe9ussie",data:{connectionTime:t,tables:{products:r,customers:s,orders:a},timestamp:new Date().toISOString()}})}catch(e){return console.error("Erreur de test de base de donn\xe9es:",e),u.NextResponse.json({success:!1,error:"Erreur de connexion \xe0 la base de donn\xe9es",details:e instanceof Error?e.message:String(e)},{status:500})}}let d=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/test/database/route",pathname:"/api/test/database",filename:"route",bundlePath:"app/api/test/database/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\test\\database\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:p,workUnitAsyncStorage:l,serverHooks:m}=d;function x(){return(0,n.patchFetch)({workAsyncStorage:p,workUnitAsyncStorage:l})}},85514:(e,t,r)=>{"use strict";let s;r.d(t,{z:()=>o});let a=require("@prisma/client");try{s=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=s},89536:()=>{}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7583,5696],()=>r(73299));module.exports=s})();