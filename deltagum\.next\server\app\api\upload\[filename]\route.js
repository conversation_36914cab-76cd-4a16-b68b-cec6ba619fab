(()=>{var e={};e.id=9157,e.ids=[9157],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39989:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>j,routeModule:()=>x,serverHooks:()=>h,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>f});var t={};s.r(t),s.d(t,{DELETE:()=>d,GET:()=>l});var i=s(73194),o=s(42355),n=s(41650),u=s(63723),a=s(79748),p=s(33873),c=s(29021);async function d(e,{params:r}){try{let{filename:e}=await r;if(!e||e.includes("..")||e.includes("/")||e.includes("\\"))return u.NextResponse.json({error:"Nom de fichier invalide"},{status:400});let s=(0,p.join)(process.cwd(),"public","uploads",e);if(!(0,c.existsSync)(s))return u.NextResponse.json({error:"Fichier non trouv\xe9"},{status:404});return await (0,a.unlink)(s),u.NextResponse.json({success:!0,message:"Fichier supprim\xe9 avec succ\xe8s"})}catch(e){return console.error("Erreur lors de la suppression:",e),u.NextResponse.json({error:"Erreur lors de la suppression du fichier"},{status:500})}}async function l(e,{params:r}){try{let{filename:e}=await r;if(!e||e.includes("..")||e.includes("/")||e.includes("\\"))return u.NextResponse.json({error:"Nom de fichier invalide"},{status:400});let t=(0,p.join)(process.cwd(),"public","uploads",e);if(!(0,c.existsSync)(t))return u.NextResponse.json({error:"Fichier non trouv\xe9"},{status:404});let i=s(29021).statSync(t);return u.NextResponse.json({success:!0,file:{name:e,url:`/uploads/${e}`,size:i.size,createdAt:i.birthtime,modifiedAt:i.mtime}})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des informations:",e),u.NextResponse.json({error:"Erreur lors de la r\xe9cup\xe9ration des informations du fichier"},{status:500})}}let x=new i.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/upload/[filename]/route",pathname:"/api/upload/[filename]",filename:"route",bundlePath:"app/api/upload/[filename]/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\[filename]\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:m,workUnitAsyncStorage:f,serverHooks:h}=x;function j(){return(0,n.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:f})}},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79748:e=>{"use strict";e.exports=require("fs/promises")},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[7583,5696],()=>s(39989));module.exports=t})();