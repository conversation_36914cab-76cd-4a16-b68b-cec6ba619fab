(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19899:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>d,POST:()=>l});var a=t(73194),i=t(42355),o=t(41650),n=t(29021),u=t(79748),p=t(63723),c=t(33873);async function l(e){try{let r=(await e.formData()).get("file");if(!r)return p.NextResponse.json({error:"Aucun fichier fourni"},{status:400});if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(r.type))return p.NextResponse.json({error:"Type de fichier non autoris\xe9. Utilisez JPG, PNG ou WebP"},{status:400});if(r.size>5242880)return p.NextResponse.json({error:"Fichier trop volumineux. Maximum 5MB"},{status:400});let t=await r.arrayBuffer(),s=Buffer.from(t),a=Date.now(),i=r.name.replace(/[^a-zA-Z0-9.-]/g,"_"),o=`${a}_${i}`,l=(0,c.join)(process.cwd(),"public","uploads");(0,n.existsSync)(l)||(0,n.mkdirSync)(l,{recursive:!0});let d=(0,c.join)(l,o);await (0,u.writeFile)(d,s);let m=`/uploads/${o}`;return p.NextResponse.json({success:!0,url:m,fileName:o,originalName:r.name,size:r.size,type:r.type})}catch(e){return console.error("Erreur lors de l'upload:",e),p.NextResponse.json({error:"Erreur lors de l'upload du fichier"},{status:500})}}async function d(){try{let e=(0,c.join)(process.cwd(),"public","uploads");if(!(0,n.existsSync)(e))return p.NextResponse.json({success:!0,images:[]});let r=await (0,u.readdir)(e),t=await Promise.all(r.filter(e=>{let r=e.toLowerCase();return r.endsWith(".jpg")||r.endsWith(".jpeg")||r.endsWith(".png")||r.endsWith(".webp")}).map(async r=>{let t=await (0,u.stat)((0,c.join)(e,r));return{name:r,url:`/uploads/${r}`,size:t.size,createdAt:t.birthtime}}).sort((e,r)=>new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime()));return p.NextResponse.json({success:!0,images:t})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des images:",e),p.NextResponse.json({error:"Erreur lors de la r\xe9cup\xe9ration des images"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:f}=m;function j(){return(0,o.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79748:e=>{"use strict";e.exports=require("fs/promises")},89536:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696],()=>t(19899));module.exports=s})();