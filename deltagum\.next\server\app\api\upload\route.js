(()=>{var e={};e.id=5413,e.ids=[5413],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19899:(e,r,t)=>{"use strict";t.r(r),t.d(r,{patchFetch:()=>j,routeModule:()=>m,serverHooks:()=>f,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>g});var s={};t.r(s),t.d(s,{GET:()=>l,POST:()=>d});var o=t(73194),i=t(42355),a=t(41650),n=t(63723),u=t(79748),p=t(33873),c=t(29021);async function d(e){try{let r=(await e.formData()).get("file");if(!r)return n.NextResponse.json({error:"Aucun fichier fourni"},{status:400});if(!["image/jpeg","image/jpg","image/png","image/webp"].includes(r.type))return n.NextResponse.json({error:"Type de fichier non autoris\xe9. Utilisez JPG, PNG ou WebP"},{status:400});if(r.size>5242880)return n.NextResponse.json({error:"Fichier trop volumineux. Maximum 5MB"},{status:400});let t=await r.arrayBuffer(),s=Buffer.from(t),o=Date.now(),i=r.name.replace(/[^a-zA-Z0-9.-]/g,"_"),a=`${o}_${i}`,d=(0,p.join)(process.cwd(),"public","uploads");(0,c.existsSync)(d)||(0,c.mkdirSync)(d,{recursive:!0});let l=(0,p.join)(d,a);await (0,u.writeFile)(l,s);let m=`/uploads/${a}`;return n.NextResponse.json({success:!0,url:m,fileName:a,originalName:r.name,size:r.size,type:r.type})}catch(e){return console.error("Erreur lors de l'upload:",e),n.NextResponse.json({error:"Erreur lors de l'upload du fichier"},{status:500})}}async function l(){try{let e=(0,p.join)(process.cwd(),"public","uploads");if(!(0,c.existsSync)(e))return n.NextResponse.json({success:!0,images:[]});let r=t(29021),s=r.readdirSync(e).filter(e=>{let r=e.toLowerCase();return r.endsWith(".jpg")||r.endsWith(".jpeg")||r.endsWith(".png")||r.endsWith(".webp")}).map(t=>{let s=r.statSync((0,p.join)(e,t));return{name:t,url:`/uploads/${t}`,size:s.size,createdAt:s.birthtime}}).sort((e,r)=>new Date(r.createdAt).getTime()-new Date(e.createdAt).getTime());return n.NextResponse.json({success:!0,images:s})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration des images:",e),n.NextResponse.json({error:"Erreur lors de la r\xe9cup\xe9ration des images"},{status:500})}}let m=new o.AppRouteRouteModule({definition:{kind:i.RouteKind.APP_ROUTE,page:"/api/upload/route",pathname:"/api/upload",filename:"route",bundlePath:"app/api/upload/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\upload\\route.ts",nextConfigOutput:"",userland:s}),{workAsyncStorage:x,workUnitAsyncStorage:g,serverHooks:f}=m;function j(){return(0,a.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:g})}},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79748:e=>{"use strict";e.exports=require("fs/promises")},89536:()=>{}};var r=require("../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[7583,5696],()=>t(19899));module.exports=s})();