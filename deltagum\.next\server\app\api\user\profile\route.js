(()=>{var e={};e.id=397,e.ids=[397],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79428:e=>{"use strict";e.exports=require("buffer")},81051:(e,r,s)=>{"use strict";s.r(r),s.d(r,{patchFetch:()=>v,routeModule:()=>f,serverHooks:()=>j,workAsyncStorage:()=>x,workUnitAsyncStorage:()=>h});var t={};s.r(t),s.d(t,{GET:()=>d,PUT:()=>m});var o=s(73194),u=s(42355),n=s(41650),i=s(85514),a=s(63723),c=s(2783),l=s.n(c);function p(e){let r=e.cookies.get("auth-token")?.value;if(!r)return null;try{return l().verify(r,process.env.JWT_SECRET||"fallback-secret")}catch{return null}}async function d(e){try{let r=p(e);if(!r)return a.NextResponse.json({success:!1,error:"Non authentifi\xe9"},{status:401});let s=await i.z.customer.findUnique({where:{id:r.userId},select:{id:!0,firstName:!0,lastName:!0,email:!0,phone:!0,address:!0,postalCode:!0,city:!0,createdAt:!0,role:!0}});if(!s)return a.NextResponse.json({success:!1,error:"Utilisateur non trouv\xe9"},{status:404});return a.NextResponse.json({success:!0,data:{user:s}})}catch(e){return console.error("Erreur lors de la r\xe9cup\xe9ration du profil:",e),a.NextResponse.json({success:!1,error:"Erreur serveur"},{status:500})}}async function m(e){try{let r=p(e);if(!r)return a.NextResponse.json({success:!1,error:"Non authentifi\xe9"},{status:401});let{firstName:s,lastName:t,email:o,phone:u,address:n,postalCode:c,city:l}=await e.json();if(!s||!t||!o)return a.NextResponse.json({success:!1,error:"Pr\xe9nom, nom et email sont requis"},{status:400});if(o!==r.email&&await i.z.customer.findFirst({where:{email:o,id:{not:r.userId}}}))return a.NextResponse.json({success:!1,error:"Cet email est d\xe9j\xe0 utilis\xe9"},{status:400});let d=await i.z.customer.update({where:{id:r.userId},data:{firstName:s,lastName:t,email:o,phone:u||null,address:n||null,postalCode:c||null,city:l||null},select:{id:!0,firstName:!0,lastName:!0,email:!0,phone:!0,address:!0,postalCode:!0,city:!0,createdAt:!0,role:!0}});return a.NextResponse.json({success:!0,message:"Profil mis \xe0 jour avec succ\xe8s",data:{user:d}})}catch(e){return console.error("Erreur lors de la mise \xe0 jour du profil:",e),a.NextResponse.json({success:!1,error:"Erreur serveur"},{status:500})}}let f=new o.AppRouteRouteModule({definition:{kind:u.RouteKind.APP_ROUTE,page:"/api/user/profile/route",pathname:"/api/user/profile",filename:"route",bundlePath:"app/api/user/profile/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\user\\profile\\route.ts",nextConfigOutput:"",userland:t}),{workAsyncStorage:x,workUnitAsyncStorage:h,serverHooks:j}=f;function v(){return(0,n.patchFetch)({workAsyncStorage:x,workUnitAsyncStorage:h})}},85514:(e,r,s)=>{"use strict";let t;s.d(r,{z:()=>u});let o=require("@prisma/client");try{t=new o.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let u=t},89536:()=>{}};var r=require("../../../../webpack-runtime.js");r.C(e);var s=e=>r(r.s=e),t=r.X(0,[7583,5696,2783],()=>s(81051));module.exports=t})();