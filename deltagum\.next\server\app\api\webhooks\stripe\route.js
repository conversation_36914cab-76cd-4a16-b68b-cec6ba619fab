(()=>{var e={};e.id=4024,e.ids=[4024],e.modules={2560:()=>{},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5505:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{HeadersAdapter:function(){return o},ReadonlyHeadersError:function(){return a}});let n=r(59118);class a extends Error{constructor(){super("Headers cannot be modified. Read more: https://nextjs.org/docs/app/api-reference/functions/headers")}static callable(){throw new a}}class o extends Headers{constructor(e){super(),this.headers=new Proxy(e,{get(t,r,a){if("symbol"==typeof r)return n.ReflectAdapter.get(t,r,a);let o=r.toLowerCase(),s=Object.keys(e).find(e=>e.toLowerCase()===o);if(void 0!==s)return n.ReflectAdapter.get(t,s,a)},set(t,r,a,o){if("symbol"==typeof r)return n.ReflectAdapter.set(t,r,a,o);let s=r.toLowerCase(),i=Object.keys(e).find(e=>e.toLowerCase()===s);return n.ReflectAdapter.set(t,i??r,a,o)},has(t,r){if("symbol"==typeof r)return n.ReflectAdapter.has(t,r);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0!==o&&n.ReflectAdapter.has(t,o)},deleteProperty(t,r){if("symbol"==typeof r)return n.ReflectAdapter.deleteProperty(t,r);let a=r.toLowerCase(),o=Object.keys(e).find(e=>e.toLowerCase()===a);return void 0===o||n.ReflectAdapter.deleteProperty(t,o)}})}static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"append":case"delete":case"set":return a.callable;default:return n.ReflectAdapter.get(e,t,r)}}})}merge(e){return Array.isArray(e)?e.join(", "):e}static from(e){return e instanceof Headers?e:new o(e)}append(e,t){let r=this.headers[e];"string"==typeof r?this.headers[e]=[r,t]:Array.isArray(r)?r.push(t):this.headers[e]=t}delete(e){delete this.headers[e]}get(e){let t=this.headers[e];return void 0!==t?this.merge(t):null}has(e){return void 0!==this.headers[e]}set(e,t){this.headers[e]=t}forEach(e,t){for(let[r,n]of this.entries())e.call(t,n,r,this)}*entries(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase(),r=this.get(t);yield[t,r]}}*keys(){for(let e of Object.keys(this.headers)){let t=e.toLowerCase();yield t}}*values(){for(let e of Object.keys(this.headers)){let t=this.get(e);yield t}}[Symbol.iterator](){return this.entries()}}},8074:(e,t,r)=>{"use strict";r.r(t),r.d(t,{patchFetch:()=>E,routeModule:()=>g,serverHooks:()=>w,workAsyncStorage:()=>m,workUnitAsyncStorage:()=>v});var n={};r.r(n),r.d(n,{POST:()=>f});var a=r(73194),o=r(42355),s=r(41650),i=r(85514),c=r(42548);r(8552);var u=r(90313);r(94402);var d=r(63723);let l=process.env.STRIPE_WEBHOOK_SECRET;async function f(e){let t,r=await e.text(),n=(await (0,u.b)()).get("stripe-signature");try{t=c._.webhooks.constructEvent(r,n,l)}catch(e){return console.error("Webhook signature verification failed:",e),d.NextResponse.json({error:"Webhook signature verification failed"},{status:400})}try{switch(t.type){case"checkout.session.completed":await p(t.data.object);break;case"payment_intent.succeeded":await h(t.data.object);break;case"payment_intent.payment_failed":await b(t.data.object);break;case"invoice.payment_succeeded":await y(t.data.object);break;default:console.log(`Unhandled event type: ${t.type}`)}return d.NextResponse.json({received:!0})}catch(e){return console.error("Error processing webhook:",e),d.NextResponse.json({error:"Error processing webhook"},{status:500})}}async function p(e){console.log("Checkout session completed:",e.id),e.metadata?.orderId&&(await i.z.order.update({where:{id:e.metadata.orderId},data:{status:"PAID",stripePaymentId:e.payment_intent}}),console.log(`Order ${e.metadata.orderId} marked as PAID`))}async function h(e){console.log("Payment intent succeeded:",e.id),e.metadata?.orderId&&(await i.z.order.update({where:{id:e.metadata.orderId},data:{status:"PAID",stripePaymentId:e.id}}),console.log(`Order ${e.metadata.orderId} payment confirmed`))}async function b(e){console.log("Payment intent failed:",e.id),e.metadata?.orderId&&(await i.z.order.update({where:{id:e.metadata.orderId},data:{status:"CANCELLED",stripePaymentId:e.id}}),console.log(`Order ${e.metadata.orderId} marked as FAILED`))}async function y(e){console.log("Invoice payment succeeded:",e.id)}let g=new a.AppRouteRouteModule({definition:{kind:o.RouteKind.APP_ROUTE,page:"/api/webhooks/stripe/route",pathname:"/api/webhooks/stripe",filename:"route",bundlePath:"app/api/webhooks/stripe/route"},resolvedPagePath:"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\api\\webhooks\\stripe\\route.ts",nextConfigOutput:"",userland:n}),{workAsyncStorage:m,workUnitAsyncStorage:v,serverHooks:w}=g;function E(){return(0,s.patchFetch)({workAsyncStorage:m,workUnitAsyncStorage:v})}},8552:(e,t,r)=>{"use strict";let n=r(8676),a=r(37813),o=r(29294),s=r(63033),i=r(67244),c=r(7952),u=r(3937),d=r(26653),l=(r(52316),r(60686)),f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{[Symbol.iterator]:{value:e[Symbol.iterator]?e[Symbol.iterator].bind(e):g.bind(e)},size:{get:()=>e.size},get:{value:e.get.bind(e)},getAll:{value:e.getAll.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},delete:{value:e.delete.bind(e)},clear:{value:"function"==typeof e.clear?e.clear.bind(e):m.bind(e,r)},toString:{value:e.toString.bind(e)}}),r}function h(e){return"object"==typeof e&&null!==e&&"string"==typeof e.name?`'${e.name}'`:"string"==typeof e?`'${e}'`:"..."}let b=(0,d.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`cookies()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E223",enumerable:!1,configurable:!0})}function g(){return this.getAll().map(e=>[e.name,e]).values()}function m(e){for(let e of this.getAll())this.delete(e.name);return e}},8676:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{MutableRequestCookiesAdapter:function(){return f},ReadonlyRequestCookiesError:function(){return i},RequestCookiesAdapter:function(){return c},appendMutableCookies:function(){return l},areCookiesMutableInCurrentPhase:function(){return h},getModifiedCookieValues:function(){return d},responseCookiesToRequestCookies:function(){return y},wrapWithMutableAccessCheck:function(){return p}});let n=r(37813),a=r(59118),o=r(29294),s=r(63033);class i extends Error{constructor(){super("Cookies can only be modified in a Server Action or Route Handler. Read more: https://nextjs.org/docs/app/api-reference/functions/cookies#options")}static callable(){throw new i}}class c{static seal(e){return new Proxy(e,{get(e,t,r){switch(t){case"clear":case"delete":case"set":return i.callable;default:return a.ReflectAdapter.get(e,t,r)}}})}}let u=Symbol.for("next.mutated.cookies");function d(e){let t=e[u];return t&&Array.isArray(t)&&0!==t.length?t:[]}function l(e,t){let r=d(t);if(0===r.length)return!1;let a=new n.ResponseCookies(e),o=a.getAll();for(let e of r)a.set(e);for(let e of o)a.set(e);return!0}class f{static wrap(e,t){let r=new n.ResponseCookies(new Headers);for(let t of e.getAll())r.set(t);let s=[],i=new Set,c=()=>{let e=o.workAsyncStorage.getStore();if(e&&(e.pathWasRevalidated=!0),s=r.getAll().filter(e=>i.has(e.name)),t){let e=[];for(let t of s){let r=new n.ResponseCookies(new Headers);r.set(t),e.push(r.toString())}t(e)}},d=new Proxy(r,{get(e,t,r){switch(t){case u:return s;case"delete":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.delete(...t),d}finally{c()}};case"set":return function(...t){i.add("string"==typeof t[0]?t[0]:t[0].name);try{return e.set(...t),d}finally{c()}};default:return a.ReflectAdapter.get(e,t,r)}}});return d}}function p(e){let t=new Proxy(e,{get(e,r,n){switch(r){case"delete":return function(...r){return b("cookies().delete"),e.delete(...r),t};case"set":return function(...r){return b("cookies().set"),e.set(...r),t};default:return a.ReflectAdapter.get(e,r,n)}}});return t}function h(e){return"action"===e.phase}function b(e){if(!h((0,s.getExpectedRequestStore)(e)))throw new i}function y(e){let t=new n.RequestCookies(new Headers);for(let r of e.getAll())t.set(r);return t}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},26653:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createDedupedByCallsiteServerErrorLoggerDev",{enumerable:!0,get:function(){return c}});let n=function(e,t){if(e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var r=a(t);if(r&&r.has(e))return r.get(e);var n={__proto__:null},o=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in e)if("default"!==s&&Object.prototype.hasOwnProperty.call(e,s)){var i=o?Object.getOwnPropertyDescriptor(e,s):null;i&&(i.get||i.set)?Object.defineProperty(n,s,i):n[s]=e[s]}return n.default=e,r&&r.set(e,n),n}(r(26101));function a(e){if("function"!=typeof WeakMap)return null;var t=new WeakMap,r=new WeakMap;return(a=function(e){return e?r:t})(e)}let o={current:null},s="function"==typeof n.cache?n.cache:e=>e,i=console.warn;function c(e){return function(...t){i(e(...t))}}s(e=>{try{i(o.current)}finally{o.current=null}})},28354:e=>{"use strict";e.exports=require("util")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},42548:(e,t,r)=>{"use strict";r.d(t,{_:()=>a});var n=r(98467);if(!process.env.STRIPE_SECRET_KEY)throw Error("STRIPE_SECRET_KEY is not defined in environment variables");let a=new n.A(process.env.STRIPE_SECRET_KEY,{apiVersion:"2025-06-30.basil",typescript:!0})},44870:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},79646:e=>{"use strict";e.exports=require("child_process")},81630:e=>{"use strict";e.exports=require("http")},85514:(e,t,r)=>{"use strict";let n;r.d(t,{z:()=>o});let a=require("@prisma/client");try{n=new a.PrismaClient({log:["error"]}),console.log("✅ Prisma client cr\xe9\xe9 avec succ\xe8s")}catch(e){throw console.error("❌ Erreur cr\xe9ation Prisma client:",e),e}let o=n},89536:()=>{},90313:(e,t,r)=>{"use strict";Object.defineProperty(t,"b",{enumerable:!0,get:function(){return l}});let n=r(5505),a=r(29294),o=r(63033),s=r(67244),i=r(7952),c=r(3937),u=r(26653),d=(r(52316),r(60686));function l(){let e=a.workAsyncStorage.getStore(),t=o.workUnitAsyncStorage.getStore();if(e){if(t&&"after"===t.phase&&!(0,d.isRequestAPICallableInsideAfter)())throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "after(...)". This is not supported. If you need this data inside an "after" callback, use "headers" outside of the callback. See more info here: https://nextjs.org/docs/canary/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E367",enumerable:!1,configurable:!0});if(e.forceStatic)return p(n.HeadersAdapter.seal(new Headers({})));if(t){if("cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside "use cache". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E304",enumerable:!1,configurable:!0});else if("unstable-cache"===t.type)throw Object.defineProperty(Error(`Route ${e.route} used "headers" inside a function cached with "unstable_cache(...)". Accessing Dynamic data sources inside a cache scope is not supported. If you need this data inside a cached function use "headers" outside of the cached function and pass the required dynamic data in as an argument. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E127",enumerable:!1,configurable:!0})}if(e.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${e.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`headers\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E525",enumerable:!1,configurable:!0});if(t)if("prerender"===t.type){var r=e.route,u=t;let n=f.get(u);if(n)return n;let a=(0,c.makeHangingPromise)(u.renderSignal,"`headers()`");return f.set(u,a),Object.defineProperties(a,{append:{value:function(){let e=`\`headers().append(${h(arguments[0])}, ...)\``,t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},delete:{value:function(){let e=`\`headers().delete(${h(arguments[0])})\``,t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},get:{value:function(){let e=`\`headers().get(${h(arguments[0])})\``,t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},has:{value:function(){let e=`\`headers().has(${h(arguments[0])})\``,t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},set:{value:function(){let e=`\`headers().set(${h(arguments[0])}, ...)\``,t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},getSetCookie:{value:function(){let e="`headers().getSetCookie()`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},forEach:{value:function(){let e="`headers().forEach(...)`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},keys:{value:function(){let e="`headers().keys()`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},values:{value:function(){let e="`headers().values()`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},entries:{value:function(){let e="`headers().entries()`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}},[Symbol.iterator]:{value:function(){let e="`headers()[Symbol.iterator]()`",t=y(r,e);(0,s.abortAndThrowOnSynchronousRequestDataAccess)(r,e,t,u)}}}),a}else"prerender-ppr"===t.type?(0,s.postponeWithTracking)(e.route,"headers",t.dynamicTracking):"prerender-legacy"===t.type&&(0,s.throwToInterruptStaticGeneration)("headers",e,t);(0,s.trackDynamicDataInDynamicRender)(e,t)}return p((0,o.getExpectedRequestStore)("headers").headers)}let f=new WeakMap;function p(e){let t=f.get(e);if(t)return t;let r=Promise.resolve(e);return f.set(e,r),Object.defineProperties(r,{append:{value:e.append.bind(e)},delete:{value:e.delete.bind(e)},get:{value:e.get.bind(e)},has:{value:e.has.bind(e)},set:{value:e.set.bind(e)},getSetCookie:{value:e.getSetCookie.bind(e)},forEach:{value:e.forEach.bind(e)},keys:{value:e.keys.bind(e)},values:{value:e.values.bind(e)},entries:{value:e.entries.bind(e)},[Symbol.iterator]:{value:e[Symbol.iterator].bind(e)}}),r}function h(e){return"string"==typeof e?`'${e}'`:"..."}let b=(0,u.createDedupedByCallsiteServerErrorLoggerDev)(y);function y(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`headers()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E277",enumerable:!1,configurable:!0})}},94402:(e,t,r)=>{"use strict";let n=r(63033),a=r(29294),o=r(67244),s=r(26653),i=r(7952),c=r(92578);function u(){let e=a.workAsyncStorage.getStore(),t=n.workUnitAsyncStorage.getStore();switch((!e||!t)&&(0,n.throwForMissingRequestStore)("draftMode"),t.type){case"request":return d(t.draftMode,e);case"cache":case"unstable-cache":let r=(0,n.getDraftModeProviderForCacheScope)(e,t);if(r)return d(r,e);case"prerender":case"prerender-ppr":case"prerender-legacy":return f(null);default:return t}}function d(e,t){let r,n=l.get(u);return n||(r=f(e),l.set(e,r),r)}let l=new WeakMap;function f(e){let t=new p(e),r=Promise.resolve(t);return Object.defineProperty(r,"isEnabled",{get:()=>t.isEnabled,set(e){Object.defineProperty(r,"isEnabled",{value:e,writable:!0,enumerable:!0})},enumerable:!0,configurable:!0}),r.enable=t.enable.bind(t),r.disable=t.disable.bind(t),r}class p{constructor(e){this._provider=e}get isEnabled(){return null!==this._provider&&this._provider.isEnabled}enable(){b("draftMode().enable()"),null!==this._provider&&this._provider.enable()}disable(){b("draftMode().disable()"),null!==this._provider&&this._provider.disable()}}let h=(0,s.createDedupedByCallsiteServerErrorLoggerDev)(function(e,t){let r=e?`Route "${e}" `:"This route ";return Object.defineProperty(Error(`${r}used ${t}. \`draftMode()\` should be awaited before using its value. Learn more: https://nextjs.org/docs/messages/sync-dynamic-apis`),"__NEXT_ERROR_CODE",{value:"E377",enumerable:!1,configurable:!0})});function b(e){let t=a.workAsyncStorage.getStore(),r=n.workUnitAsyncStorage.getStore();if(t){if(r){if("cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside "use cache". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/messages/next-request-in-use-cache`),"__NEXT_ERROR_CODE",{value:"E246",enumerable:!1,configurable:!0});else if("unstable-cache"===r.type)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside a function cached with "unstable_cache(...)". The enabled status of draftMode can be read in caches but you must not enable or disable draftMode inside a cache. See more info here: https://nextjs.org/docs/app/api-reference/functions/unstable_cache`),"__NEXT_ERROR_CODE",{value:"E259",enumerable:!1,configurable:!0});else if("after"===r.phase)throw Object.defineProperty(Error(`Route ${t.route} used "${e}" inside \`after\`. The enabled status of draftMode can be read inside \`after\` but you cannot enable or disable draftMode. See more info here: https://nextjs.org/docs/app/api-reference/functions/after`),"__NEXT_ERROR_CODE",{value:"E348",enumerable:!1,configurable:!0})}if(t.dynamicShouldError)throw Object.defineProperty(new i.StaticGenBailoutError(`Route ${t.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(r){if("prerender"===r.type){let n=Object.defineProperty(Error(`Route ${t.route} used ${e} without first calling \`await connection()\`. See more info here: https://nextjs.org/docs/messages/next-prerender-sync-headers`),"__NEXT_ERROR_CODE",{value:"E126",enumerable:!1,configurable:!0});(0,o.abortAndThrowOnSynchronousRequestDataAccess)(t.route,e,n,r)}else if("prerender-ppr"===r.type)(0,o.postponeWithTracking)(t.route,e,r.dynamicTracking);else if("prerender-legacy"===r.type){r.revalidate=0;let n=Object.defineProperty(new c.DynamicServerError(`Route ${t.route} couldn't be rendered statically because it used \`${e}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw t.dynamicUsageDescription=e,t.dynamicUsageStack=n.stack,n}}}}},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[7583,5696,8467],()=>r(8074));module.exports=n})();