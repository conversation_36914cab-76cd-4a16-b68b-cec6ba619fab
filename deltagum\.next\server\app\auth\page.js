(()=>{var e={};e.id=8365,e.ids=[8365],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},23689:(e,t,r)=>{Promise.resolve().then(r.bind(r,25464))},25464:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>g});var s=r(166),a=r(23705),l=r(93666),o=r(66212),n=r(39690),i=r(2926),d=r(55050);let c=(0,d.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),m=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var u=r(53340),p=r(11325),x=r.n(p),h=r(81040),b=r(14791);let g=()=>{let[e,t]=(0,b.useState)(!0),[r,d]=(0,b.useState)(!1),[p,g]=(0,b.useState)({email:"",password:"",firstName:"",lastName:"",phone:"",address:"",postalCode:"",city:""}),[f,v]=(0,b.useState)({}),{login:y,register:j,isAuthenticated:k,isLoading:N}=(0,l.useAuth)(),w=(0,h.useRouter)();(0,b.useEffect)(()=>{k&&w.push("/")},[k,w]);let C=async t=>{t.preventDefault(),v({});try{e?await y(p.email,p.password):await j(p),w.push("/")}catch(e){v({general:e.message||"Une erreur est survenue"})}},P=e=>{g({...p,[e.target.name]:e.target.value})};return(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8",children:(0,s.jsxs)(o.P.div,{className:"max-w-2xl w-full space-y-8 bg-white p-10 rounded-3xl shadow-2xl border border-gray-200",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,s.jsxs)("div",{children:[(0,s.jsx)(x(),{href:"/",className:"flex justify-center",children:(0,s.jsx)("img",{className:"h-12 w-auto",src:"/img/logo.png",alt:"Deltagum",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})}),(0,s.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-black",children:e?"Connexion":"Cr\xe9er un compte"}),(0,s.jsx)("p",{className:"mt-2 text-center text-sm text-black",children:e?(0,s.jsxs)(s.Fragment,{children:["Pas encore de compte ?"," ",(0,s.jsx)("button",{onClick:()=>t(!1),className:"font-medium text-pink-600 hover:text-pink-500",children:"S'inscrire"})]}):(0,s.jsxs)(s.Fragment,{children:["D\xe9j\xe0 un compte ?"," ",(0,s.jsx)("button",{onClick:()=>t(!0),className:"font-medium text-pink-600 hover:text-pink-500",children:"Se connecter"})]})})]}),(0,s.jsxs)("form",{className:"mt-10 space-y-8",onSubmit:C,children:[(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-black mb-3",children:"Adresse email"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(n.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5 z-10"}),(0,s.jsx)(a.pd,{id:"email",name:"email",type:"email",required:!0,className:"w-full pl-12 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"<EMAIL>",value:p.email,onChange:P})]})]}),!e&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-black mb-2",children:"Pr\xe9nom"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5"}),(0,s.jsx)(a.pd,{id:"firstName",name:"firstName",type:"text",required:!0,className:"w-full pl-12 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre pr\xe9nom",value:p.firstName,onChange:P})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-black mb-2",children:"Nom"}),(0,s.jsx)(a.pd,{id:"lastName",name:"lastName",type:"text",required:!0,className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre nom",value:p.lastName,onChange:P})]})]}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-black mb-3",children:"T\xe9l\xe9phone (optionnel)"}),(0,s.jsx)(a.pd,{id:"phone",name:"phone",type:"tel",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"06 12 34 56 78",value:p.phone,onChange:P})]}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-black mb-3",children:"Adresse (optionnel)"}),(0,s.jsx)(a.pd,{id:"address",name:"address",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"123 rue de la Paix",value:p.address,onChange:P})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-black mb-2",children:"Code postal"}),(0,s.jsx)(a.pd,{id:"postalCode",name:"postalCode",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"75001",value:p.postalCode,onChange:P})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-black mb-2",children:"Ville"}),(0,s.jsx)(a.pd,{id:"city",name:"city",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Paris",value:p.city,onChange:P})]})]})]}),(0,s.jsxs)("div",{className:"w-full",children:[(0,s.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-black mb-3",children:"Mot de passe"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(c,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5 z-10"}),(0,s.jsx)(a.pd,{id:"password",name:"password",type:r?"text":"password",required:!0,className:"w-full pl-12 pr-14 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre mot de passe",value:p.password,onChange:P}),(0,s.jsx)("button",{type:"button",className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-700 hover:text-black transition-colors z-10 p-1",onClick:()=>d(!r),children:r?(0,s.jsx)(m,{className:"w-5 h-5"}):(0,s.jsx)(u.A,{className:"w-5 h-5"})})]}),!e&&(0,s.jsx)("p",{className:"mt-1 text-xs text-black",children:"Minimum 6 caract\xe8res"})]})]}),f.general&&(0,s.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 text-red-700 text-sm text-center",children:f.general}),(0,s.jsx)("div",{className:"pt-4",children:(0,s.jsx)(a.$n,{type:"submit",variant:"primary",size:"lg",className:"w-full h-16 text-lg font-semibold bg-pink-600 hover:bg-pink-700 focus:ring-pink-500 focus:ring-4 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl",disabled:N,children:N?"Chargement...":e?"Se connecter":"Cr\xe9er mon compte"})}),(0,s.jsx)("div",{className:"text-center pt-4 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-3 text-amber-800",children:[(0,s.jsx)("p",{className:"text-sm font-medium",children:"\uD83D\uDD1E R\xe9serv\xe9 aux personnes majeures (18+)"}),(0,s.jsx)("p",{className:"text-xs mt-1",children:"En vous inscrivant, vous confirmez avoir 18 ans ou plus"})]})})]})]})})}},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33417:(e,t,r)=>{Promise.resolve().then(r.bind(r,89231))},33873:e=>{"use strict";e.exports=require("path")},39690:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55050).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},53340:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});let s=(0,r(55050).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73515:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(67269);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81040:(e,t,r)=>{"use strict";var s=r(59076);r.o(s,"useParams")&&r.d(t,{useParams:function(){return s.useParams}}),r.o(s,"useRouter")&&r.d(t,{useRouter:function(){return s.useRouter}}),r.o(s,"useSearchParams")&&r.d(t,{useSearchParams:function(){return s.useSearchParams}})},89231:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx","default")},96163:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,pages:()=>c,routeModule:()=>u,tree:()=>d});var s=r(87628),a=r(42355),l=r(87979),o=r.n(l),n=r(15140),i={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(i[e]=()=>n[e]);r.d(t,i);let d={children:["",{children:["auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,89231)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\auth\\page.tsx"],m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/auth/page",pathname:"/auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[7583,8500,7269,6964],()=>r(96163));module.exports=s})();