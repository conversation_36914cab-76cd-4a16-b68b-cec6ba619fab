(()=>{var e={};e.id=9577,e.ids=[9577],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11967:(e,t,a)=>{"use strict";a.r(t),a.d(t,{GlobalError:()=>i.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c});var s=a(87628),r=a(42355),n=a(87979),i=a.n(n),l=a(15140),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);a.d(t,o);let c={children:["",{children:["cancel",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(a.bind(a,51515)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(a.bind(a,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(a.t.bind(a,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(a.t.bind(a,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(a.t.bind(a,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(a.bind(a,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx"],m={require:a,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/cancel/page",pathname:"/cancel",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},48317:(e,t,a)=>{Promise.resolve().then(a.bind(a,51515))},51515:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>s});let s=(0,a(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\cancel\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\cancel\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66877:(e,t,a)=>{Promise.resolve().then(a.bind(a,68921))},68921:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>c});var s=a(166),r=a(23705),n=a(93666),i=a(66212),l=a(11325),o=a.n(l);function c(){let{addItem:e}=(0,n._$)();return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-red-50",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsxs)(i.P.div,{className:"max-w-2xl mx-auto text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsx)(i.P.div,{className:"text-8xl mb-6",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:260,damping:20,delay:.2},children:"❌"}),(0,s.jsx)(i.P.h1,{className:"text-4xl font-bold text-gray-800 mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:"Paiement annul\xe9"}),(0,s.jsx)(i.P.p,{className:"text-xl text-gray-600 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:"Votre paiement a \xe9t\xe9 annul\xe9. Aucun montant n'a \xe9t\xe9 d\xe9bit\xe9 de votre compte."}),(0,s.jsxs)(i.P.div,{className:"bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)("span",{className:"text-4xl",children:"\uD83D\uDED2"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Vos articles sont toujours dans votre panier"}),(0,s.jsx)("p",{className:"text-orange-700",children:"Vous pouvez reprendre votre commande \xe0 tout moment. Vos d\xe9licieux bonbons Deltagum vous attendent !"})]}),(0,s.jsxs)(i.P.div,{className:"bg-white rounded-lg shadow-lg p-6 mb-8 text-left",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4 text-center",children:"Pourquoi reprendre votre commande ?"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDF53"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Saveurs naturelles de fraise, myrtille et pomme"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDE9A"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Livraison gratuite d\xe8s 25€ d'achat"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDD12"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Paiement 100% s\xe9curis\xe9 par Stripe"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"⭐"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Satisfaction garantie ou rembours\xe9"})]})]})]}),(0,s.jsxs)(i.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2},children:[(0,s.jsx)(o(),{href:"/cart",children:(0,s.jsx)(r.$n,{variant:"primary",size:"lg",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDED2"}),"Reprendre ma commande"]})})}),(0,s.jsx)(o(),{href:"/",children:(0,s.jsx)(r.$n,{variant:"outline",size:"lg",children:"Continuer mes achats"})})]}),(0,s.jsxs)(i.P.div,{className:"mt-12 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.4},children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Besoin d'aide ? Notre \xe9quipe est l\xe0 pour vous accompagner."}),(0,s.jsx)(o(),{href:"/#contact",children:(0,s.jsx)(r.$n,{variant:"ghost",size:"sm",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDCAC"}),"Nous contacter"]})})})]})]})})})}a(14791)},73515:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>r});var s=a(67269);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),s=t.X(0,[7583,8500,7269,6964],()=>a(11967));module.exports=s})();