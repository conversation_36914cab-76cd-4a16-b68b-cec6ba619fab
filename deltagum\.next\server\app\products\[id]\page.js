(()=>{var e={};e.id=3403,e.ids=[3403],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},7878:()=>{},10322:(e,t,s)=>{Promise.resolve().then(s.bind(s,17420))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},15580:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});let i=(0,s(55050).A)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},17420:(e,t,s)=>{"use strict";let i,r,a;s.r(t),s.d(t,{default:()=>e$});var l=s(166),n=s(23705),o=s(45276);let d={STRAWBERRY:"Fraise",BLUEBERRY:"Myrtille",APPLE:"Pomme"};var c=s(66212),p=s(14791),u=s(26367),h=s(55050);let m=(0,h.A)("chevron-left",[["path",{d:"m15 18-6-6 6-6",key:"1wnfg3"}]]),f=(0,h.A)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]);function g(e){return null!==e&&"object"==typeof e&&"constructor"in e&&e.constructor===Object}function v(e,t){void 0===e&&(e={}),void 0===t&&(t={});let s=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>s.indexOf(e)).forEach(s=>{void 0===e[s]?e[s]=t[s]:g(t[s])&&g(e[s])&&Object.keys(t[s]).length>0&&v(e[s],t[s])})}let x={body:{},addEventListener(){},removeEventListener(){},activeElement:{blur(){},nodeName:""},querySelector:()=>null,querySelectorAll:()=>[],getElementById:()=>null,createEvent:()=>({initEvent(){}}),createElement:()=>({children:[],childNodes:[],style:{},setAttribute(){},getElementsByTagName:()=>[]}),createElementNS:()=>({}),importNode:()=>null,location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""}};function b(){let e="undefined"!=typeof document?document:{};return v(e,x),e}let w={document:x,navigator:{userAgent:""},location:{hash:"",host:"",hostname:"",href:"",origin:"",pathname:"",protocol:"",search:""},history:{replaceState(){},pushState(){},go(){},back(){}},CustomEvent:function(){return this},addEventListener(){},removeEventListener(){},getComputedStyle:()=>({getPropertyValue:()=>""}),Image(){},Date(){},screen:{},setTimeout(){},clearTimeout(){},matchMedia:()=>({}),requestAnimationFrame:e=>"undefined"==typeof setTimeout?(e(),null):setTimeout(e,0),cancelAnimationFrame(e){"undefined"!=typeof setTimeout&&clearTimeout(e)}};function y(){let e="undefined"!=typeof window?window:{};return v(e,w),e}function S(e,t){return void 0===t&&(t=0),setTimeout(e,t)}function E(){return Date.now()}function T(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)}function C(){let e=Object(arguments.length<=0?void 0:arguments[0]),t=["__proto__","constructor","prototype"];for(let s=1;s<arguments.length;s+=1){let i=s<0||arguments.length<=s?void 0:arguments[s];if(null!=i&&("undefined"!=typeof window&&void 0!==window.HTMLElement?!(i instanceof HTMLElement):!i||1!==i.nodeType&&11!==i.nodeType)){let s=Object.keys(Object(i)).filter(e=>0>t.indexOf(e));for(let t=0,r=s.length;t<r;t+=1){let r=s[t],a=Object.getOwnPropertyDescriptor(i,r);void 0!==a&&a.enumerable&&(T(e[r])&&T(i[r])?i[r].__swiper__?e[r]=i[r]:C(e[r],i[r]):!T(e[r])&&T(i[r])?(e[r]={},i[r].__swiper__?e[r]=i[r]:C(e[r],i[r])):e[r]=i[r])}}}return e}function P(e,t,s){e.style.setProperty(t,s)}function j(e){let t,{swiper:s,targetPosition:i,side:r}=e,a=y(),l=-s.translate,n=null,o=s.params.speed;s.wrapperEl.style.scrollSnapType="none",a.cancelAnimationFrame(s.cssModeFrameID);let d=i>l?"next":"prev",c=(e,t)=>"next"===d&&e>=t||"prev"===d&&e<=t,p=()=>{t=new Date().getTime(),null===n&&(n=t);let e=l+(.5-Math.cos(Math.max(Math.min((t-n)/o,1),0)*Math.PI)/2)*(i-l);if(c(e,i)&&(e=i),s.wrapperEl.scrollTo({[r]:e}),c(e,i)){s.wrapperEl.style.overflow="hidden",s.wrapperEl.style.scrollSnapType="",setTimeout(()=>{s.wrapperEl.style.overflow="",s.wrapperEl.scrollTo({[r]:e})}),a.cancelAnimationFrame(s.cssModeFrameID);return}s.cssModeFrameID=a.requestAnimationFrame(p)};p()}function k(e,t){void 0===t&&(t="");let s=y(),i=[...e.children];return(s.HTMLSlotElement&&e instanceof HTMLSlotElement&&i.push(...e.assignedElements()),t)?i.filter(e=>e.matches(t)):i}function M(e){try{console.warn(e);return}catch(e){}}function N(e,t){var s;void 0===t&&(t=[]);let i=document.createElement(e);return i.classList.add(...Array.isArray(t)?t:(void 0===(s=t)&&(s=""),s.trim().split(" ").filter(e=>!!e.trim()))),i}function L(e,t){return y().getComputedStyle(e,null).getPropertyValue(t)}function O(e){let t,s=e;if(s){for(t=0;null!==(s=s.previousSibling);)1===s.nodeType&&(t+=1);return t}}function A(e,t){let s=[],i=e.parentElement;for(;i;)t?i.matches(t)&&s.push(i):s.push(i),i=i.parentElement;return s}function _(e,t,s){let i=y();return s?e["width"===t?"offsetWidth":"offsetHeight"]+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-right":"margin-top"))+parseFloat(i.getComputedStyle(e,null).getPropertyValue("width"===t?"margin-left":"margin-bottom")):e.offsetWidth}function z(e){return(Array.isArray(e)?e:[e]).filter(e=>!!e)}function I(e,t){void 0===t&&(t=""),"undefined"!=typeof trustedTypes?e.innerHTML=trustedTypes.createPolicy("html",{createHTML:e=>e}).createHTML(t):e.innerHTML=t}function $(e,t,s,i){return e.params.createElements&&Object.keys(i).forEach(r=>{if(!s[r]&&!0===s.auto){let a=k(e.el,`.${i[r]}`)[0];a||((a=N("div",i[r])).className=i[r],e.el.append(a)),s[r]=a,t[r]=a}}),s}function D(e){let{swiper:t,extendParams:s,on:i,emit:r}=e;function a(e){let s;return e&&"string"==typeof e&&t.isElement&&(s=t.el.querySelector(e)||t.hostEl.querySelector(e))?s:(e&&("string"==typeof e&&(s=[...document.querySelectorAll(e)]),t.params.uniqueNavElements&&"string"==typeof e&&s&&s.length>1&&1===t.el.querySelectorAll(e).length?s=t.el.querySelector(e):s&&1===s.length&&(s=s[0])),e&&!s)?e:s}function l(e,s){let i=t.params.navigation;(e=z(e)).forEach(e=>{e&&(e.classList[s?"add":"remove"](...i.disabledClass.split(" ")),"BUTTON"===e.tagName&&(e.disabled=s),t.params.watchOverflow&&t.enabled&&e.classList[t.isLocked?"add":"remove"](i.lockClass))})}function n(){let{nextEl:e,prevEl:s}=t.navigation;if(t.params.loop){l(s,!1),l(e,!1);return}l(s,t.isBeginning&&!t.params.rewind),l(e,t.isEnd&&!t.params.rewind)}function o(e){e.preventDefault(),(!t.isBeginning||t.params.loop||t.params.rewind)&&(t.slidePrev(),r("navigationPrev"))}function d(e){e.preventDefault(),(!t.isEnd||t.params.loop||t.params.rewind)&&(t.slideNext(),r("navigationNext"))}function c(){let e=t.params.navigation;if(t.params.navigation=$(t,t.originalParams.navigation,t.params.navigation,{nextEl:"swiper-button-next",prevEl:"swiper-button-prev"}),!(e.nextEl||e.prevEl))return;let s=a(e.nextEl),i=a(e.prevEl);Object.assign(t.navigation,{nextEl:s,prevEl:i}),s=z(s),i=z(i);let r=(s,i)=>{s&&s.addEventListener("click","next"===i?d:o),!t.enabled&&s&&s.classList.add(...e.lockClass.split(" "))};s.forEach(e=>r(e,"next")),i.forEach(e=>r(e,"prev"))}function p(){let{nextEl:e,prevEl:s}=t.navigation;e=z(e),s=z(s);let i=(e,s)=>{e.removeEventListener("click","next"===s?d:o),e.classList.remove(...t.params.navigation.disabledClass.split(" "))};e.forEach(e=>i(e,"next")),s.forEach(e=>i(e,"prev"))}s({navigation:{nextEl:null,prevEl:null,hideOnClick:!1,disabledClass:"swiper-button-disabled",hiddenClass:"swiper-button-hidden",lockClass:"swiper-button-lock",navigationDisabledClass:"swiper-navigation-disabled"}}),t.navigation={nextEl:null,prevEl:null},i("init",()=>{!1===t.params.navigation.enabled?u():(c(),n())}),i("toEdge fromEdge lock unlock",()=>{n()}),i("destroy",()=>{p()}),i("enable disable",()=>{let{nextEl:e,prevEl:s}=t.navigation;if(e=z(e),s=z(s),t.enabled)return void n();[...e,...s].filter(e=>!!e).forEach(e=>e.classList.add(t.params.navigation.lockClass))}),i("click",(e,s)=>{let{nextEl:i,prevEl:a}=t.navigation;i=z(i),a=z(a);let l=s.target,n=a.includes(l)||i.includes(l);if(t.isElement&&!n){let e=s.path||s.composedPath&&s.composedPath();e&&(n=e.find(e=>i.includes(e)||a.includes(e)))}if(t.params.navigation.hideOnClick&&!n){let e;if(t.pagination&&t.params.pagination&&t.params.pagination.clickable&&(t.pagination.el===l||t.pagination.el.contains(l)))return;i.length?e=i[0].classList.contains(t.params.navigation.hiddenClass):a.length&&(e=a[0].classList.contains(t.params.navigation.hiddenClass)),!0===e?r("navigationShow"):r("navigationHide"),[...i,...a].filter(e=>!!e).forEach(e=>e.classList.toggle(t.params.navigation.hiddenClass))}});let u=()=>{t.el.classList.add(...t.params.navigation.navigationDisabledClass.split(" ")),p()};Object.assign(t.navigation,{enable:()=>{t.el.classList.remove(...t.params.navigation.navigationDisabledClass.split(" ")),c(),n()},disable:u,update:n,init:c,destroy:p})}function B(e){return void 0===e&&(e=""),`.${e.trim().replace(/([\.:!+\/()[\]])/g,"\\$1").replace(/ /g,".")}`}function G(e){let t,{swiper:s,extendParams:i,on:r,emit:a}=e,l="swiper-pagination";i({pagination:{el:null,bulletElement:"span",clickable:!1,hideOnClick:!1,renderBullet:null,renderProgressbar:null,renderFraction:null,renderCustom:null,progressbarOpposite:!1,type:"bullets",dynamicBullets:!1,dynamicMainBullets:1,formatFractionCurrent:e=>e,formatFractionTotal:e=>e,bulletClass:`${l}-bullet`,bulletActiveClass:`${l}-bullet-active`,modifierClass:`${l}-`,currentClass:`${l}-current`,totalClass:`${l}-total`,hiddenClass:`${l}-hidden`,progressbarFillClass:`${l}-progressbar-fill`,progressbarOppositeClass:`${l}-progressbar-opposite`,clickableClass:`${l}-clickable`,lockClass:`${l}-lock`,horizontalClass:`${l}-horizontal`,verticalClass:`${l}-vertical`,paginationDisabledClass:`${l}-disabled`}}),s.pagination={el:null,bullets:[]};let n=0;function o(){return!s.params.pagination.el||!s.pagination.el||Array.isArray(s.pagination.el)&&0===s.pagination.el.length}function d(e,t){let{bulletActiveClass:i}=s.params.pagination;e&&(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&(e.classList.add(`${i}-${t}`),(e=e[`${"prev"===t?"previous":"next"}ElementSibling`])&&e.classList.add(`${i}-${t}-${t}`))}function c(e){let t=e.target.closest(B(s.params.pagination.bulletClass));if(!t)return;e.preventDefault();let i=O(t)*s.params.slidesPerGroup;if(s.params.loop){var r,a,l;if(s.realIndex===i)return;let e=(r=s.realIndex,a=i,(r%=l=s.slides.length,(a%=l)===r+1)?"next":a===r-1?"previous":void 0);"next"===e?s.slideNext():"previous"===e?s.slidePrev():s.slideToLoop(i)}else s.slideTo(i)}function p(){let e,i,r=s.rtl,l=s.params.pagination;if(o())return;let c=s.pagination.el;c=z(c);let p=s.virtual&&s.params.virtual.enabled?s.virtual.slides.length:s.slides.length,u=s.params.loop?Math.ceil(p/s.params.slidesPerGroup):s.snapGrid.length;if(s.params.loop?(i=s.previousRealIndex||0,e=s.params.slidesPerGroup>1?Math.floor(s.realIndex/s.params.slidesPerGroup):s.realIndex):void 0!==s.snapIndex?(e=s.snapIndex,i=s.previousSnapIndex):(i=s.previousIndex||0,e=s.activeIndex||0),"bullets"===l.type&&s.pagination.bullets&&s.pagination.bullets.length>0){let a,o,p,u=s.pagination.bullets;if(l.dynamicBullets&&(t=_(u[0],s.isHorizontal()?"width":"height",!0),c.forEach(e=>{e.style[s.isHorizontal()?"width":"height"]=`${t*(l.dynamicMainBullets+4)}px`}),l.dynamicMainBullets>1&&void 0!==i&&((n+=e-(i||0))>l.dynamicMainBullets-1?n=l.dynamicMainBullets-1:n<0&&(n=0)),p=((o=(a=Math.max(e-n,0))+(Math.min(u.length,l.dynamicMainBullets)-1))+a)/2),u.forEach(e=>{let t=[...["","-next","-next-next","-prev","-prev-prev","-main"].map(e=>`${l.bulletActiveClass}${e}`)].map(e=>"string"==typeof e&&e.includes(" ")?e.split(" "):e).flat();e.classList.remove(...t)}),c.length>1)u.forEach(t=>{let i=O(t);i===e?t.classList.add(...l.bulletActiveClass.split(" ")):s.isElement&&t.setAttribute("part","bullet"),l.dynamicBullets&&(i>=a&&i<=o&&t.classList.add(...`${l.bulletActiveClass}-main`.split(" ")),i===a&&d(t,"prev"),i===o&&d(t,"next"))});else{let t=u[e];if(t&&t.classList.add(...l.bulletActiveClass.split(" ")),s.isElement&&u.forEach((t,s)=>{t.setAttribute("part",s===e?"bullet-active":"bullet")}),l.dynamicBullets){let e=u[a],t=u[o];for(let e=a;e<=o;e+=1)u[e]&&u[e].classList.add(...`${l.bulletActiveClass}-main`.split(" "));d(e,"prev"),d(t,"next")}}if(l.dynamicBullets){let e=Math.min(u.length,l.dynamicMainBullets+4),i=(t*e-t)/2-p*t,a=r?"right":"left";u.forEach(e=>{e.style[s.isHorizontal()?a:"top"]=`${i}px`})}}c.forEach((t,i)=>{if("fraction"===l.type&&(t.querySelectorAll(B(l.currentClass)).forEach(t=>{t.textContent=l.formatFractionCurrent(e+1)}),t.querySelectorAll(B(l.totalClass)).forEach(e=>{e.textContent=l.formatFractionTotal(u)})),"progressbar"===l.type){let i;i=l.progressbarOpposite?s.isHorizontal()?"vertical":"horizontal":s.isHorizontal()?"horizontal":"vertical";let r=(e+1)/u,a=1,n=1;"horizontal"===i?a=r:n=r,t.querySelectorAll(B(l.progressbarFillClass)).forEach(e=>{e.style.transform=`translate3d(0,0,0) scaleX(${a}) scaleY(${n})`,e.style.transitionDuration=`${s.params.speed}ms`})}"custom"===l.type&&l.renderCustom?(I(t,l.renderCustom(s,e+1,u)),0===i&&a("paginationRender",t)):(0===i&&a("paginationRender",t),a("paginationUpdate",t)),s.params.watchOverflow&&s.enabled&&t.classList[s.isLocked?"add":"remove"](l.lockClass)})}function u(){let e=s.params.pagination;if(o())return;let t=s.virtual&&s.params.virtual.enabled?s.virtual.slides.length:s.grid&&s.params.grid.rows>1?s.slides.length/Math.ceil(s.params.grid.rows):s.slides.length,i=s.pagination.el;i=z(i);let r="";if("bullets"===e.type){let i=s.params.loop?Math.ceil(t/s.params.slidesPerGroup):s.snapGrid.length;s.params.freeMode&&s.params.freeMode.enabled&&i>t&&(i=t);for(let t=0;t<i;t+=1)e.renderBullet?r+=e.renderBullet.call(s,t,e.bulletClass):r+=`<${e.bulletElement} ${s.isElement?'part="bullet"':""} class="${e.bulletClass}"></${e.bulletElement}>`}"fraction"===e.type&&(r=e.renderFraction?e.renderFraction.call(s,e.currentClass,e.totalClass):`<span class="${e.currentClass}"></span> / <span class="${e.totalClass}"></span>`),"progressbar"===e.type&&(r=e.renderProgressbar?e.renderProgressbar.call(s,e.progressbarFillClass):`<span class="${e.progressbarFillClass}"></span>`),s.pagination.bullets=[],i.forEach(t=>{"custom"!==e.type&&I(t,r||""),"bullets"===e.type&&s.pagination.bullets.push(...t.querySelectorAll(B(e.bulletClass)))}),"custom"!==e.type&&a("paginationRender",i[0])}function h(){let e;s.params.pagination=$(s,s.originalParams.pagination,s.params.pagination,{el:"swiper-pagination"});let t=s.params.pagination;t.el&&("string"==typeof t.el&&s.isElement&&(e=s.el.querySelector(t.el)),e||"string"!=typeof t.el||(e=[...document.querySelectorAll(t.el)]),e||(e=t.el),e&&0!==e.length&&(s.params.uniqueNavElements&&"string"==typeof t.el&&Array.isArray(e)&&e.length>1&&(e=[...s.el.querySelectorAll(t.el)]).length>1&&(e=e.find(e=>A(e,".swiper")[0]===s.el)),Array.isArray(e)&&1===e.length&&(e=e[0]),Object.assign(s.pagination,{el:e}),(e=z(e)).forEach(e=>{"bullets"===t.type&&t.clickable&&e.classList.add(...(t.clickableClass||"").split(" ")),e.classList.add(t.modifierClass+t.type),e.classList.add(s.isHorizontal()?t.horizontalClass:t.verticalClass),"bullets"===t.type&&t.dynamicBullets&&(e.classList.add(`${t.modifierClass}${t.type}-dynamic`),n=0,t.dynamicMainBullets<1&&(t.dynamicMainBullets=1)),"progressbar"===t.type&&t.progressbarOpposite&&e.classList.add(t.progressbarOppositeClass),t.clickable&&e.addEventListener("click",c),s.enabled||e.classList.add(t.lockClass)})))}function m(){let e=s.params.pagination;if(o())return;let t=s.pagination.el;t&&(t=z(t)).forEach(t=>{t.classList.remove(e.hiddenClass),t.classList.remove(e.modifierClass+e.type),t.classList.remove(s.isHorizontal()?e.horizontalClass:e.verticalClass),e.clickable&&(t.classList.remove(...(e.clickableClass||"").split(" ")),t.removeEventListener("click",c))}),s.pagination.bullets&&s.pagination.bullets.forEach(t=>t.classList.remove(...e.bulletActiveClass.split(" ")))}r("changeDirection",()=>{if(!s.pagination||!s.pagination.el)return;let e=s.params.pagination,{el:t}=s.pagination;(t=z(t)).forEach(t=>{t.classList.remove(e.horizontalClass,e.verticalClass),t.classList.add(s.isHorizontal()?e.horizontalClass:e.verticalClass)})}),r("init",()=>{!1===s.params.pagination.enabled?f():(h(),u(),p())}),r("activeIndexChange",()=>{void 0===s.snapIndex&&p()}),r("snapIndexChange",()=>{p()}),r("snapGridLengthChange",()=>{u(),p()}),r("destroy",()=>{m()}),r("enable disable",()=>{let{el:e}=s.pagination;e&&(e=z(e)).forEach(e=>e.classList[s.enabled?"remove":"add"](s.params.pagination.lockClass))}),r("lock unlock",()=>{p()}),r("click",(e,t)=>{let i=t.target,r=z(s.pagination.el);if(s.params.pagination.el&&s.params.pagination.hideOnClick&&r&&r.length>0&&!i.classList.contains(s.params.pagination.bulletClass)){if(s.navigation&&(s.navigation.nextEl&&i===s.navigation.nextEl||s.navigation.prevEl&&i===s.navigation.prevEl))return;!0===r[0].classList.contains(s.params.pagination.hiddenClass)?a("paginationShow"):a("paginationHide"),r.forEach(e=>e.classList.toggle(s.params.pagination.hiddenClass))}});let f=()=>{s.el.classList.add(s.params.pagination.paginationDisabledClass);let{el:e}=s.pagination;e&&(e=z(e)).forEach(e=>e.classList.add(s.params.pagination.paginationDisabledClass)),m()};Object.assign(s.pagination,{enable:()=>{s.el.classList.remove(s.params.pagination.paginationDisabledClass);let{el:e}=s.pagination;e&&(e=z(e)).forEach(e=>e.classList.remove(s.params.pagination.paginationDisabledClass)),h(),u(),p()},disable:f,render:u,update:p,init:h,destroy:m})}function R(){return i||(i=function(){let e=y(),t=b();return{smoothScroll:t.documentElement&&t.documentElement.style&&"scrollBehavior"in t.documentElement.style,touch:!!("ontouchstart"in e||e.DocumentTouch&&t instanceof e.DocumentTouch)}}()),i}function V(e){return void 0===e&&(e={}),r||(r=function(e){let{userAgent:t}=void 0===e?{}:e,s=R(),i=y(),r=i.navigator.platform,a=t||i.navigator.userAgent,l={ios:!1,android:!1},n=i.screen.width,o=i.screen.height,d=a.match(/(Android);?[\s\/]+([\d.]+)?/),c=a.match(/(iPad).*OS\s([\d_]+)/),p=a.match(/(iPod)(.*OS\s([\d_]+))?/),u=!c&&a.match(/(iPhone\sOS|iOS)\s([\d_]+)/),h="MacIntel"===r;return!c&&h&&s.touch&&["1024x1366","1366x1024","834x1194","1194x834","834x1112","1112x834","768x1024","1024x768","820x1180","1180x820","810x1080","1080x810"].indexOf(`${n}x${o}`)>=0&&((c=a.match(/(Version)\/([\d.]+)/))||(c=[0,1,"13_0_0"]),h=!1),d&&"Win32"!==r&&(l.os="android",l.android=!0),(c||u||p)&&(l.os="ios",l.ios=!0),l}(e)),r}function F(){return a||(a=function(){let e=y(),t=V(),s=!1;function i(){let t=e.navigator.userAgent.toLowerCase();return t.indexOf("safari")>=0&&0>t.indexOf("chrome")&&0>t.indexOf("android")}if(i()){let t=String(e.navigator.userAgent);if(t.includes("Version/")){let[e,i]=t.split("Version/")[1].split(" ")[0].split(".").map(e=>Number(e));s=e<16||16===e&&i<2}}let r=/(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(e.navigator.userAgent),a=i(),l=a||r&&t.ios;return{isSafari:s||a,needPerspectiveFix:s,need3dFix:l,isWebView:r}}()),a}let q=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)},H=(e,t,s)=>{t&&!e.classList.contains(s)?e.classList.add(s):!t&&e.classList.contains(s)&&e.classList.remove(s)},W=(e,t)=>{if(!e||e.destroyed||!e.params)return;let s=t.closest(e.isElement?"swiper-slide":`.${e.params.slideClass}`);if(s){let t=s.querySelector(`.${e.params.lazyPreloaderClass}`);!t&&e.isElement&&(s.shadowRoot?t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`):requestAnimationFrame(()=>{s.shadowRoot&&(t=s.shadowRoot.querySelector(`.${e.params.lazyPreloaderClass}`))&&t.remove()})),t&&t.remove()}},Y=(e,t)=>{if(!e.slides[t])return;let s=e.slides[t].querySelector('[loading="lazy"]');s&&s.removeAttribute("loading")},X=e=>{if(!e||e.destroyed||!e.params)return;let t=e.params.lazyPreloadPrevNext,s=e.slides.length;if(!s||!t||t<0)return;t=Math.min(t,s);let i="auto"===e.params.slidesPerView?e.slidesPerViewDynamic():Math.ceil(e.params.slidesPerView),r=e.activeIndex;if(e.params.grid&&e.params.grid.rows>1){let s=[r-t];s.push(...Array.from({length:t}).map((e,t)=>r+i+t)),e.slides.forEach((t,i)=>{s.includes(t.column)&&Y(e,i)});return}let a=r+i-1;if(e.params.rewind||e.params.loop)for(let i=r-t;i<=a+t;i+=1){let t=(i%s+s)%s;(t<r||t>a)&&Y(e,t)}else for(let i=Math.max(r-t,0);i<=Math.min(a+t,s-1);i+=1)i!==r&&(i>a||i<r)&&Y(e,i)};function U(e){let{swiper:t,runCallbacks:s,direction:i,step:r}=e,{activeIndex:a,previousIndex:l}=t,n=i;n||(n=a>l?"next":a<l?"prev":"reset"),t.emit(`transition${r}`),s&&"reset"===n?t.emit(`slideResetTransition${r}`):s&&a!==l&&(t.emit(`slideChangeTransition${r}`),"next"===n?t.emit(`slideNextTransition${r}`):t.emit(`slidePrevTransition${r}`))}function K(e,t,s){let i=y(),{params:r}=e,a=r.edgeSwipeDetection,l=r.edgeSwipeThreshold;return!a||!(s<=l)&&!(s>=i.innerWidth-l)||"prevent"===a&&(t.preventDefault(),!0)}function Q(e){let t=b(),s=e;s.originalEvent&&(s=s.originalEvent);let i=this.touchEventsData;if("pointerdown"===s.type){if(null!==i.pointerId&&i.pointerId!==s.pointerId)return;i.pointerId=s.pointerId}else"touchstart"===s.type&&1===s.targetTouches.length&&(i.touchId=s.targetTouches[0].identifier);if("touchstart"===s.type)return void K(this,s,s.targetTouches[0].pageX);let{params:r,touches:a,enabled:l}=this;if(!l||!r.simulateTouch&&"mouse"===s.pointerType||this.animating&&r.preventInteractionOnTransition)return;!this.animating&&r.cssMode&&r.loop&&this.loopFix();let n=s.target;if("wrapper"===r.touchEventsTarget&&!function(e,t){let s=y(),i=t.contains(e);return!i&&s.HTMLSlotElement&&t instanceof HTMLSlotElement&&((i=[...t.assignedElements()].includes(e))||(i=function(e,t){let s=[t];for(;s.length>0;){let t=s.shift();if(e===t)return!0;s.push(...t.children,...t.shadowRoot?t.shadowRoot.children:[],...t.assignedElements?t.assignedElements():[])}}(e,t))),i}(n,this.wrapperEl)||"which"in s&&3===s.which||"button"in s&&s.button>0||i.isTouched&&i.isMoved)return;let o=!!r.noSwipingClass&&""!==r.noSwipingClass,d=s.composedPath?s.composedPath():s.path;o&&s.target&&s.target.shadowRoot&&d&&(n=d[0]);let c=r.noSwipingSelector?r.noSwipingSelector:`.${r.noSwipingClass}`,p=!!(s.target&&s.target.shadowRoot);if(r.noSwiping&&(p?function(e,t){return void 0===t&&(t=this),function t(s){if(!s||s===b()||s===y())return null;s.assignedSlot&&(s=s.assignedSlot);let i=s.closest(e);return i||s.getRootNode?i||t(s.getRootNode().host):null}(t)}(c,n):n.closest(c))){this.allowClick=!0;return}if(r.swipeHandler&&!n.closest(r.swipeHandler))return;a.currentX=s.pageX,a.currentY=s.pageY;let u=a.currentX,h=a.currentY;if(!K(this,s,u))return;Object.assign(i,{isTouched:!0,isMoved:!1,allowTouchCallbacks:!0,isScrolling:void 0,startMoving:void 0}),a.startX=u,a.startY=h,i.touchStartTime=E(),this.allowClick=!0,this.updateSize(),this.swipeDirection=void 0,r.threshold>0&&(i.allowThresholdMove=!1);let m=!0;n.matches(i.focusableElements)&&(m=!1,"SELECT"===n.nodeName&&(i.isTouched=!1)),t.activeElement&&t.activeElement.matches(i.focusableElements)&&t.activeElement!==n&&("mouse"===s.pointerType||"mouse"!==s.pointerType&&!n.matches(i.focusableElements))&&t.activeElement.blur();let f=m&&this.allowTouchMove&&r.touchStartPreventDefault;(r.touchStartForcePreventDefault||f)&&!n.isContentEditable&&s.preventDefault(),r.freeMode&&r.freeMode.enabled&&this.freeMode&&this.animating&&!r.cssMode&&this.freeMode.onTouchStart(),this.emit("touchStart",s)}function Z(e){let t,s,i=b(),r=this.touchEventsData,{params:a,touches:l,rtlTranslate:n,enabled:o}=this;if(!o||!a.simulateTouch&&"mouse"===e.pointerType)return;let d=e;if(d.originalEvent&&(d=d.originalEvent),"pointermove"===d.type&&(null!==r.touchId||d.pointerId!==r.pointerId))return;if("touchmove"===d.type){if(!(t=[...d.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else t=d;if(!r.isTouched){r.startMoving&&r.isScrolling&&this.emit("touchMoveOpposite",d);return}let c=t.pageX,p=t.pageY;if(d.preventedByNestedSwiper){l.startX=c,l.startY=p;return}if(!this.allowTouchMove){d.target.matches(r.focusableElements)||(this.allowClick=!1),r.isTouched&&(Object.assign(l,{startX:c,startY:p,currentX:c,currentY:p}),r.touchStartTime=E());return}if(a.touchReleaseOnEdges&&!a.loop){if(this.isVertical()){if(p<l.startY&&this.translate<=this.maxTranslate()||p>l.startY&&this.translate>=this.minTranslate()){r.isTouched=!1,r.isMoved=!1;return}}else if(n&&(c>l.startX&&-this.translate<=this.maxTranslate()||c<l.startX&&-this.translate>=this.minTranslate()))return;else if(!n&&(c<l.startX&&this.translate<=this.maxTranslate()||c>l.startX&&this.translate>=this.minTranslate()))return}if(i.activeElement&&i.activeElement.matches(r.focusableElements)&&i.activeElement!==d.target&&"mouse"!==d.pointerType&&i.activeElement.blur(),i.activeElement&&d.target===i.activeElement&&d.target.matches(r.focusableElements)){r.isMoved=!0,this.allowClick=!1;return}r.allowTouchCallbacks&&this.emit("touchMove",d),l.previousX=l.currentX,l.previousY=l.currentY,l.currentX=c,l.currentY=p;let u=l.currentX-l.startX,h=l.currentY-l.startY;if(this.params.threshold&&Math.sqrt(u**2+h**2)<this.params.threshold)return;if(void 0===r.isScrolling){let e;this.isHorizontal()&&l.currentY===l.startY||this.isVertical()&&l.currentX===l.startX?r.isScrolling=!1:u*u+h*h>=25&&(e=180*Math.atan2(Math.abs(h),Math.abs(u))/Math.PI,r.isScrolling=this.isHorizontal()?e>a.touchAngle:90-e>a.touchAngle)}if(r.isScrolling&&this.emit("touchMoveOpposite",d),void 0===r.startMoving&&(l.currentX!==l.startX||l.currentY!==l.startY)&&(r.startMoving=!0),r.isScrolling||"touchmove"===d.type&&r.preventTouchMoveFromPointerMove){r.isTouched=!1;return}if(!r.startMoving)return;this.allowClick=!1,!a.cssMode&&d.cancelable&&d.preventDefault(),a.touchMoveStopPropagation&&!a.nested&&d.stopPropagation();let m=this.isHorizontal()?u:h,f=this.isHorizontal()?l.currentX-l.previousX:l.currentY-l.previousY;a.oneWayMovement&&(m=Math.abs(m)*(n?1:-1),f=Math.abs(f)*(n?1:-1)),l.diff=m,m*=a.touchRatio,n&&(m=-m,f=-f);let g=this.touchesDirection;this.swipeDirection=m>0?"prev":"next",this.touchesDirection=f>0?"prev":"next";let v=this.params.loop&&!a.cssMode,x="next"===this.touchesDirection&&this.allowSlideNext||"prev"===this.touchesDirection&&this.allowSlidePrev;if(!r.isMoved){if(v&&x&&this.loopFix({direction:this.swipeDirection}),r.startTranslate=this.getTranslate(),this.setTransition(0),this.animating){let e=new window.CustomEvent("transitionend",{bubbles:!0,cancelable:!0,detail:{bySwiperTouchMove:!0}});this.wrapperEl.dispatchEvent(e)}r.allowMomentumBounce=!1,a.grabCursor&&(!0===this.allowSlideNext||!0===this.allowSlidePrev)&&this.setGrabCursor(!0),this.emit("sliderFirstMove",d)}if(new Date().getTime(),!1!==a._loopSwapReset&&r.isMoved&&r.allowThresholdMove&&g!==this.touchesDirection&&v&&x&&Math.abs(m)>=1){Object.assign(l,{startX:c,startY:p,currentX:c,currentY:p,startTranslate:r.currentTranslate}),r.loopSwapReset=!0,r.startTranslate=r.currentTranslate;return}this.emit("sliderMove",d),r.isMoved=!0,r.currentTranslate=m+r.startTranslate;let w=!0,y=a.resistanceRatio;if(a.touchReleaseOnEdges&&(y=0),m>0?(v&&x&&!s&&r.allowThresholdMove&&r.currentTranslate>(a.centeredSlides?this.minTranslate()-this.slidesSizesGrid[this.activeIndex+1]-("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.activeIndex+1]+this.params.spaceBetween:0)-this.params.spaceBetween:this.minTranslate())&&this.loopFix({direction:"prev",setTranslate:!0,activeSlideIndex:0}),r.currentTranslate>this.minTranslate()&&(w=!1,a.resistance&&(r.currentTranslate=this.minTranslate()-1+(-this.minTranslate()+r.startTranslate+m)**y))):m<0&&(v&&x&&!s&&r.allowThresholdMove&&r.currentTranslate<(a.centeredSlides?this.maxTranslate()+this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween+("auto"!==a.slidesPerView&&this.slides.length-a.slidesPerView>=2?this.slidesSizesGrid[this.slidesSizesGrid.length-1]+this.params.spaceBetween:0):this.maxTranslate())&&this.loopFix({direction:"next",setTranslate:!0,activeSlideIndex:this.slides.length-("auto"===a.slidesPerView?this.slidesPerViewDynamic():Math.ceil(parseFloat(a.slidesPerView,10)))}),r.currentTranslate<this.maxTranslate()&&(w=!1,a.resistance&&(r.currentTranslate=this.maxTranslate()+1-(this.maxTranslate()-r.startTranslate-m)**y))),w&&(d.preventedByNestedSwiper=!0),!this.allowSlideNext&&"next"===this.swipeDirection&&r.currentTranslate<r.startTranslate&&(r.currentTranslate=r.startTranslate),!this.allowSlidePrev&&"prev"===this.swipeDirection&&r.currentTranslate>r.startTranslate&&(r.currentTranslate=r.startTranslate),this.allowSlidePrev||this.allowSlideNext||(r.currentTranslate=r.startTranslate),a.threshold>0)if(Math.abs(m)>a.threshold||r.allowThresholdMove){if(!r.allowThresholdMove){r.allowThresholdMove=!0,l.startX=l.currentX,l.startY=l.currentY,r.currentTranslate=r.startTranslate,l.diff=this.isHorizontal()?l.currentX-l.startX:l.currentY-l.startY;return}}else{r.currentTranslate=r.startTranslate;return}a.followFinger&&!a.cssMode&&((a.freeMode&&a.freeMode.enabled&&this.freeMode||a.watchSlidesProgress)&&(this.updateActiveIndex(),this.updateSlidesClasses()),a.freeMode&&a.freeMode.enabled&&this.freeMode&&this.freeMode.onTouchMove(),this.updateProgress(r.currentTranslate),this.setTranslate(r.currentTranslate))}function J(e){let t,s,i=this,r=i.touchEventsData,a=e;if(a.originalEvent&&(a=a.originalEvent),"touchend"===a.type||"touchcancel"===a.type){if(!(t=[...a.changedTouches].find(e=>e.identifier===r.touchId))||t.identifier!==r.touchId)return}else{if(null!==r.touchId||a.pointerId!==r.pointerId)return;t=a}if(["pointercancel","pointerout","pointerleave","contextmenu"].includes(a.type)&&!(["pointercancel","contextmenu"].includes(a.type)&&(i.browser.isSafari||i.browser.isWebView)))return;r.pointerId=null,r.touchId=null;let{params:l,touches:n,rtlTranslate:o,slidesGrid:d,enabled:c}=i;if(!c||!l.simulateTouch&&"mouse"===a.pointerType)return;if(r.allowTouchCallbacks&&i.emit("touchEnd",a),r.allowTouchCallbacks=!1,!r.isTouched){r.isMoved&&l.grabCursor&&i.setGrabCursor(!1),r.isMoved=!1,r.startMoving=!1;return}l.grabCursor&&r.isMoved&&r.isTouched&&(!0===i.allowSlideNext||!0===i.allowSlidePrev)&&i.setGrabCursor(!1);let p=E(),u=p-r.touchStartTime;if(i.allowClick){let e=a.path||a.composedPath&&a.composedPath();i.updateClickedSlide(e&&e[0]||a.target,e),i.emit("tap click",a),u<300&&p-r.lastClickTime<300&&i.emit("doubleTap doubleClick",a)}if(r.lastClickTime=E(),S(()=>{i.destroyed||(i.allowClick=!0)}),!r.isTouched||!r.isMoved||!i.swipeDirection||0===n.diff&&!r.loopSwapReset||r.currentTranslate===r.startTranslate&&!r.loopSwapReset){r.isTouched=!1,r.isMoved=!1,r.startMoving=!1;return}if(r.isTouched=!1,r.isMoved=!1,r.startMoving=!1,s=l.followFinger?o?i.translate:-i.translate:-r.currentTranslate,l.cssMode)return;if(l.freeMode&&l.freeMode.enabled)return void i.freeMode.onTouchEnd({currentPos:s});let h=s>=-i.maxTranslate()&&!i.params.loop,m=0,f=i.slidesSizesGrid[0];for(let e=0;e<d.length;e+=e<l.slidesPerGroupSkip?1:l.slidesPerGroup){let t=e<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;void 0!==d[e+t]?(h||s>=d[e]&&s<d[e+t])&&(m=e,f=d[e+t]-d[e]):(h||s>=d[e])&&(m=e,f=d[d.length-1]-d[d.length-2])}let g=null,v=null;l.rewind&&(i.isBeginning?v=l.virtual&&l.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1:i.isEnd&&(g=0));let x=(s-d[m])/f,b=m<l.slidesPerGroupSkip-1?1:l.slidesPerGroup;if(u>l.longSwipesMs){if(!l.longSwipes)return void i.slideTo(i.activeIndex);"next"===i.swipeDirection&&(x>=l.longSwipesRatio?i.slideTo(l.rewind&&i.isEnd?g:m+b):i.slideTo(m)),"prev"===i.swipeDirection&&(x>1-l.longSwipesRatio?i.slideTo(m+b):null!==v&&x<0&&Math.abs(x)>l.longSwipesRatio?i.slideTo(v):i.slideTo(m))}else{if(!l.shortSwipes)return void i.slideTo(i.activeIndex);i.navigation&&(a.target===i.navigation.nextEl||a.target===i.navigation.prevEl)?a.target===i.navigation.nextEl?i.slideTo(m+b):i.slideTo(m):("next"===i.swipeDirection&&i.slideTo(null!==g?g:m+b),"prev"===i.swipeDirection&&i.slideTo(null!==v?v:m))}}function ee(){let e=this,{params:t,el:s}=e;if(s&&0===s.offsetWidth)return;t.breakpoints&&e.setBreakpoint();let{allowSlideNext:i,allowSlidePrev:r,snapGrid:a}=e,l=e.virtual&&e.params.virtual.enabled;e.allowSlideNext=!0,e.allowSlidePrev=!0,e.updateSize(),e.updateSlides(),e.updateSlidesClasses();let n=l&&t.loop;"auto"!==t.slidesPerView&&!(t.slidesPerView>1)||!e.isEnd||e.isBeginning||e.params.centeredSlides||n?e.params.loop&&!l?e.slideToLoop(e.realIndex,0,!1,!0):e.slideTo(e.activeIndex,0,!1,!0):e.slideTo(e.slides.length-1,0,!1,!0),e.autoplay&&e.autoplay.running&&e.autoplay.paused&&(clearTimeout(e.autoplay.resizeTimeout),e.autoplay.resizeTimeout=setTimeout(()=>{e.autoplay&&e.autoplay.running&&e.autoplay.paused&&e.autoplay.resume()},500)),e.allowSlidePrev=r,e.allowSlideNext=i,e.params.watchOverflow&&a!==e.snapGrid&&e.checkOverflow()}function et(e){this.enabled&&!this.allowClick&&(this.params.preventClicks&&e.preventDefault(),this.params.preventClicksPropagation&&this.animating&&(e.stopPropagation(),e.stopImmediatePropagation()))}function es(){let e,{wrapperEl:t,rtlTranslate:s,enabled:i}=this;if(!i)return;this.previousTranslate=this.translate,this.isHorizontal()?this.translate=-t.scrollLeft:this.translate=-t.scrollTop,0===this.translate&&(this.translate=0),this.updateActiveIndex(),this.updateSlidesClasses();let r=this.maxTranslate()-this.minTranslate();(0===r?0:(this.translate-this.minTranslate())/r)!==this.progress&&this.updateProgress(s?-this.translate:this.translate),this.emit("setTranslate",this.translate,!1)}function ei(e){W(this,e.target),!this.params.cssMode&&("auto"===this.params.slidesPerView||this.params.autoHeight)&&this.update()}function er(){!this.documentTouchHandlerProceeded&&(this.documentTouchHandlerProceeded=!0,this.params.touchReleaseOnEdges&&(this.el.style.touchAction="auto"))}let ea=(e,t)=>{let s=b(),{params:i,el:r,wrapperEl:a,device:l}=e,n=!!i.nested,o="on"===t?"addEventListener":"removeEventListener";r&&"string"!=typeof r&&(s[o]("touchstart",e.onDocumentTouchStart,{passive:!1,capture:n}),r[o]("touchstart",e.onTouchStart,{passive:!1}),r[o]("pointerdown",e.onTouchStart,{passive:!1}),s[o]("touchmove",e.onTouchMove,{passive:!1,capture:n}),s[o]("pointermove",e.onTouchMove,{passive:!1,capture:n}),s[o]("touchend",e.onTouchEnd,{passive:!0}),s[o]("pointerup",e.onTouchEnd,{passive:!0}),s[o]("pointercancel",e.onTouchEnd,{passive:!0}),s[o]("touchcancel",e.onTouchEnd,{passive:!0}),s[o]("pointerout",e.onTouchEnd,{passive:!0}),s[o]("pointerleave",e.onTouchEnd,{passive:!0}),s[o]("contextmenu",e.onTouchEnd,{passive:!0}),(i.preventClicks||i.preventClicksPropagation)&&r[o]("click",e.onClick,!0),i.cssMode&&a[o]("scroll",e.onScroll),i.updateOnWindowResize?e[t](l.ios||l.android?"resize orientationchange observerUpdate":"resize observerUpdate",ee,!0):e[t]("observerUpdate",ee,!0),r[o]("load",e.onLoad,{capture:!0}))},el=(e,t)=>e.grid&&t.grid&&t.grid.rows>1;var en={init:!0,direction:"horizontal",oneWayMovement:!1,swiperElementNodeName:"SWIPER-CONTAINER",touchEventsTarget:"wrapper",initialSlide:0,speed:300,cssMode:!1,updateOnWindowResize:!0,resizeObserver:!0,nested:!1,createElements:!1,eventsPrefix:"swiper",enabled:!0,focusableElements:"input, select, option, textarea, button, video, label",width:null,height:null,preventInteractionOnTransition:!1,userAgent:null,url:null,edgeSwipeDetection:!1,edgeSwipeThreshold:20,autoHeight:!1,setWrapperSize:!1,virtualTranslate:!1,effect:"slide",breakpoints:void 0,breakpointsBase:"window",spaceBetween:0,slidesPerView:1,slidesPerGroup:1,slidesPerGroupSkip:0,slidesPerGroupAuto:!1,centeredSlides:!1,centeredSlidesBounds:!1,slidesOffsetBefore:0,slidesOffsetAfter:0,normalizeSlideIndex:!0,centerInsufficientSlides:!1,watchOverflow:!0,roundLengths:!1,touchRatio:1,touchAngle:45,simulateTouch:!0,shortSwipes:!0,longSwipes:!0,longSwipesRatio:.5,longSwipesMs:300,followFinger:!0,allowTouchMove:!0,threshold:5,touchMoveStopPropagation:!1,touchStartPreventDefault:!0,touchStartForcePreventDefault:!1,touchReleaseOnEdges:!1,uniqueNavElements:!0,resistance:!0,resistanceRatio:.85,watchSlidesProgress:!1,grabCursor:!1,preventClicks:!0,preventClicksPropagation:!0,slideToClickedSlide:!1,loop:!1,loopAddBlankSlides:!0,loopAdditionalSlides:0,loopPreventsSliding:!0,rewind:!1,allowSlidePrev:!0,allowSlideNext:!0,swipeHandler:null,noSwiping:!0,noSwipingClass:"swiper-no-swiping",noSwipingSelector:null,passiveListeners:!0,maxBackfaceHiddenSlides:10,containerModifierClass:"swiper-",slideClass:"swiper-slide",slideBlankClass:"swiper-slide-blank",slideActiveClass:"swiper-slide-active",slideVisibleClass:"swiper-slide-visible",slideFullyVisibleClass:"swiper-slide-fully-visible",slideNextClass:"swiper-slide-next",slidePrevClass:"swiper-slide-prev",wrapperClass:"swiper-wrapper",lazyPreloaderClass:"swiper-lazy-preloader",lazyPreloadPrevNext:0,runCallbacksOnInit:!0,_emitClasses:!1};let eo={eventsEmitter:{on(e,t,s){let i=this;if(!i.eventsListeners||i.destroyed||"function"!=typeof t)return i;let r=s?"unshift":"push";return e.split(" ").forEach(e=>{i.eventsListeners[e]||(i.eventsListeners[e]=[]),i.eventsListeners[e][r](t)}),i},once(e,t,s){let i=this;if(!i.eventsListeners||i.destroyed||"function"!=typeof t)return i;function r(){i.off(e,r),r.__emitterProxy&&delete r.__emitterProxy;for(var s=arguments.length,a=Array(s),l=0;l<s;l++)a[l]=arguments[l];t.apply(i,a)}return r.__emitterProxy=t,i.on(e,r,s)},onAny(e,t){return!this.eventsListeners||this.destroyed||"function"!=typeof e||0>this.eventsAnyListeners.indexOf(e)&&this.eventsAnyListeners[t?"unshift":"push"](e),this},offAny(e){if(!this.eventsListeners||this.destroyed||!this.eventsAnyListeners)return this;let t=this.eventsAnyListeners.indexOf(e);return t>=0&&this.eventsAnyListeners.splice(t,1),this},off(e,t){let s=this;return s.eventsListeners&&!s.destroyed&&s.eventsListeners&&e.split(" ").forEach(e=>{void 0===t?s.eventsListeners[e]=[]:s.eventsListeners[e]&&s.eventsListeners[e].forEach((i,r)=>{(i===t||i.__emitterProxy&&i.__emitterProxy===t)&&s.eventsListeners[e].splice(r,1)})}),s},emit(){let e,t,s,i=this;if(!i.eventsListeners||i.destroyed||!i.eventsListeners)return i;for(var r=arguments.length,a=Array(r),l=0;l<r;l++)a[l]=arguments[l];return"string"==typeof a[0]||Array.isArray(a[0])?(e=a[0],t=a.slice(1,a.length),s=i):(e=a[0].events,t=a[0].data,s=a[0].context||i),t.unshift(s),(Array.isArray(e)?e:e.split(" ")).forEach(e=>{i.eventsAnyListeners&&i.eventsAnyListeners.length&&i.eventsAnyListeners.forEach(i=>{i.apply(s,[e,...t])}),i.eventsListeners&&i.eventsListeners[e]&&i.eventsListeners[e].forEach(e=>{e.apply(s,t)})}),i}},update:{updateSize:function(){let e,t,s=this.el;e=void 0!==this.params.width&&null!==this.params.width?this.params.width:s.clientWidth,t=void 0!==this.params.height&&null!==this.params.height?this.params.height:s.clientHeight,0===e&&this.isHorizontal()||0===t&&this.isVertical()||(e=e-parseInt(L(s,"padding-left")||0,10)-parseInt(L(s,"padding-right")||0,10),t=t-parseInt(L(s,"padding-top")||0,10)-parseInt(L(s,"padding-bottom")||0,10),Number.isNaN(e)&&(e=0),Number.isNaN(t)&&(t=0),Object.assign(this,{width:e,height:t,size:this.isHorizontal()?e:t}))},updateSlides:function(){let e,t=this;function s(e,s){return parseFloat(e.getPropertyValue(t.getDirectionLabel(s))||0)}let i=t.params,{wrapperEl:r,slidesEl:a,size:l,rtlTranslate:n,wrongRTL:o}=t,d=t.virtual&&i.virtual.enabled,c=d?t.virtual.slides.length:t.slides.length,p=k(a,`.${t.params.slideClass}, swiper-slide`),u=d?t.virtual.slides.length:p.length,h=[],m=[],f=[],g=i.slidesOffsetBefore;"function"==typeof g&&(g=i.slidesOffsetBefore.call(t));let v=i.slidesOffsetAfter;"function"==typeof v&&(v=i.slidesOffsetAfter.call(t));let x=t.snapGrid.length,b=t.slidesGrid.length,w=i.spaceBetween,y=-g,S=0,E=0;if(void 0===l)return;"string"==typeof w&&w.indexOf("%")>=0?w=parseFloat(w.replace("%",""))/100*l:"string"==typeof w&&(w=parseFloat(w)),t.virtualSize=-w,p.forEach(e=>{n?e.style.marginLeft="":e.style.marginRight="",e.style.marginBottom="",e.style.marginTop=""}),i.centeredSlides&&i.cssMode&&(P(r,"--swiper-centered-offset-before",""),P(r,"--swiper-centered-offset-after",""));let T=i.grid&&i.grid.rows>1&&t.grid;T?t.grid.initSlides(p):t.grid&&t.grid.unsetSlides();let C="auto"===i.slidesPerView&&i.breakpoints&&Object.keys(i.breakpoints).filter(e=>void 0!==i.breakpoints[e].slidesPerView).length>0;for(let r=0;r<u;r+=1){let a;if(e=0,p[r]&&(a=p[r]),T&&t.grid.updateSlide(r,a,p),!p[r]||"none"!==L(a,"display")){if("auto"===i.slidesPerView){C&&(p[r].style[t.getDirectionLabel("width")]="");let l=getComputedStyle(a),n=a.style.transform,o=a.style.webkitTransform;if(n&&(a.style.transform="none"),o&&(a.style.webkitTransform="none"),i.roundLengths)e=t.isHorizontal()?_(a,"width",!0):_(a,"height",!0);else{let t=s(l,"width"),i=s(l,"padding-left"),r=s(l,"padding-right"),n=s(l,"margin-left"),o=s(l,"margin-right"),d=l.getPropertyValue("box-sizing");if(d&&"border-box"===d)e=t+n+o;else{let{clientWidth:s,offsetWidth:l}=a;e=t+i+r+n+o+(l-s)}}n&&(a.style.transform=n),o&&(a.style.webkitTransform=o),i.roundLengths&&(e=Math.floor(e))}else e=(l-(i.slidesPerView-1)*w)/i.slidesPerView,i.roundLengths&&(e=Math.floor(e)),p[r]&&(p[r].style[t.getDirectionLabel("width")]=`${e}px`);p[r]&&(p[r].swiperSlideSize=e),f.push(e),i.centeredSlides?(y=y+e/2+S/2+w,0===S&&0!==r&&(y=y-l/2-w),0===r&&(y=y-l/2-w),.001>Math.abs(y)&&(y=0),i.roundLengths&&(y=Math.floor(y)),E%i.slidesPerGroup==0&&h.push(y),m.push(y)):(i.roundLengths&&(y=Math.floor(y)),(E-Math.min(t.params.slidesPerGroupSkip,E))%t.params.slidesPerGroup==0&&h.push(y),m.push(y),y=y+e+w),t.virtualSize+=e+w,S=e,E+=1}}if(t.virtualSize=Math.max(t.virtualSize,l)+v,n&&o&&("slide"===i.effect||"coverflow"===i.effect)&&(r.style.width=`${t.virtualSize+w}px`),i.setWrapperSize&&(r.style[t.getDirectionLabel("width")]=`${t.virtualSize+w}px`),T&&t.grid.updateWrapperSize(e,h),!i.centeredSlides){let e=[];for(let s=0;s<h.length;s+=1){let r=h[s];i.roundLengths&&(r=Math.floor(r)),h[s]<=t.virtualSize-l&&e.push(r)}h=e,Math.floor(t.virtualSize-l)-Math.floor(h[h.length-1])>1&&h.push(t.virtualSize-l)}if(d&&i.loop){let e=f[0]+w;if(i.slidesPerGroup>1){let s=Math.ceil((t.virtual.slidesBefore+t.virtual.slidesAfter)/i.slidesPerGroup),r=e*i.slidesPerGroup;for(let e=0;e<s;e+=1)h.push(h[h.length-1]+r)}for(let s=0;s<t.virtual.slidesBefore+t.virtual.slidesAfter;s+=1)1===i.slidesPerGroup&&h.push(h[h.length-1]+e),m.push(m[m.length-1]+e),t.virtualSize+=e}if(0===h.length&&(h=[0]),0!==w){let e=t.isHorizontal()&&n?"marginLeft":t.getDirectionLabel("marginRight");p.filter((e,t)=>!i.cssMode||!!i.loop||t!==p.length-1).forEach(t=>{t.style[e]=`${w}px`})}if(i.centeredSlides&&i.centeredSlidesBounds){let e=0;f.forEach(t=>{e+=t+(w||0)});let t=(e-=w)>l?e-l:0;h=h.map(e=>e<=0?-g:e>t?t+v:e)}if(i.centerInsufficientSlides){let e=0;f.forEach(t=>{e+=t+(w||0)}),e-=w;let t=(i.slidesOffsetBefore||0)+(i.slidesOffsetAfter||0);if(e+t<l){let s=(l-e-t)/2;h.forEach((e,t)=>{h[t]=e-s}),m.forEach((e,t)=>{m[t]=e+s})}}if(Object.assign(t,{slides:p,snapGrid:h,slidesGrid:m,slidesSizesGrid:f}),i.centeredSlides&&i.cssMode&&!i.centeredSlidesBounds){P(r,"--swiper-centered-offset-before",`${-h[0]}px`),P(r,"--swiper-centered-offset-after",`${t.size/2-f[f.length-1]/2}px`);let e=-t.snapGrid[0],s=-t.slidesGrid[0];t.snapGrid=t.snapGrid.map(t=>t+e),t.slidesGrid=t.slidesGrid.map(e=>e+s)}if(u!==c&&t.emit("slidesLengthChange"),h.length!==x&&(t.params.watchOverflow&&t.checkOverflow(),t.emit("snapGridLengthChange")),m.length!==b&&t.emit("slidesGridLengthChange"),i.watchSlidesProgress&&t.updateSlidesOffset(),t.emit("slidesUpdated"),!d&&!i.cssMode&&("slide"===i.effect||"fade"===i.effect)){let e=`${i.containerModifierClass}backface-hidden`,s=t.el.classList.contains(e);u<=i.maxBackfaceHiddenSlides?s||t.el.classList.add(e):s&&t.el.classList.remove(e)}},updateAutoHeight:function(e){let t,s=this,i=[],r=s.virtual&&s.params.virtual.enabled,a=0;"number"==typeof e?s.setTransition(e):!0===e&&s.setTransition(s.params.speed);let l=e=>r?s.slides[s.getSlideIndexByData(e)]:s.slides[e];if("auto"!==s.params.slidesPerView&&s.params.slidesPerView>1)if(s.params.centeredSlides)(s.visibleSlides||[]).forEach(e=>{i.push(e)});else for(t=0;t<Math.ceil(s.params.slidesPerView);t+=1){let e=s.activeIndex+t;if(e>s.slides.length&&!r)break;i.push(l(e))}else i.push(l(s.activeIndex));for(t=0;t<i.length;t+=1)if(void 0!==i[t]){let e=i[t].offsetHeight;a=e>a?e:a}(a||0===a)&&(s.wrapperEl.style.height=`${a}px`)},updateSlidesOffset:function(){let e=this.slides,t=this.isElement?this.isHorizontal()?this.wrapperEl.offsetLeft:this.wrapperEl.offsetTop:0;for(let s=0;s<e.length;s+=1)e[s].swiperSlideOffset=(this.isHorizontal()?e[s].offsetLeft:e[s].offsetTop)-t-this.cssOverflowAdjustment()},updateSlidesProgress:function(e){void 0===e&&(e=this&&this.translate||0);let t=this.params,{slides:s,rtlTranslate:i,snapGrid:r}=this;if(0===s.length)return;void 0===s[0].swiperSlideOffset&&this.updateSlidesOffset();let a=-e;i&&(a=e),this.visibleSlidesIndexes=[],this.visibleSlides=[];let l=t.spaceBetween;"string"==typeof l&&l.indexOf("%")>=0?l=parseFloat(l.replace("%",""))/100*this.size:"string"==typeof l&&(l=parseFloat(l));for(let e=0;e<s.length;e+=1){let n=s[e],o=n.swiperSlideOffset;t.cssMode&&t.centeredSlides&&(o-=s[0].swiperSlideOffset);let d=(a+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+l),c=(a-r[0]+(t.centeredSlides?this.minTranslate():0)-o)/(n.swiperSlideSize+l),p=-(a-o),u=p+this.slidesSizesGrid[e],h=p>=0&&p<=this.size-this.slidesSizesGrid[e],m=p>=0&&p<this.size-1||u>1&&u<=this.size||p<=0&&u>=this.size;m&&(this.visibleSlides.push(n),this.visibleSlidesIndexes.push(e)),q(n,m,t.slideVisibleClass),q(n,h,t.slideFullyVisibleClass),n.progress=i?-d:d,n.originalProgress=i?-c:c}},updateProgress:function(e){if(void 0===e){let t=this.rtlTranslate?-1:1;e=this&&this.translate&&this.translate*t||0}let t=this.params,s=this.maxTranslate()-this.minTranslate(),{progress:i,isBeginning:r,isEnd:a,progressLoop:l}=this,n=r,o=a;if(0===s)i=0,r=!0,a=!0;else{i=(e-this.minTranslate())/s;let t=1>Math.abs(e-this.minTranslate()),l=1>Math.abs(e-this.maxTranslate());r=t||i<=0,a=l||i>=1,t&&(i=0),l&&(i=1)}if(t.loop){let t=this.getSlideIndexByData(0),s=this.getSlideIndexByData(this.slides.length-1),i=this.slidesGrid[t],r=this.slidesGrid[s],a=this.slidesGrid[this.slidesGrid.length-1],n=Math.abs(e);(l=n>=i?(n-i)/a:(n+a-r)/a)>1&&(l-=1)}Object.assign(this,{progress:i,progressLoop:l,isBeginning:r,isEnd:a}),(t.watchSlidesProgress||t.centeredSlides&&t.autoHeight)&&this.updateSlidesProgress(e),r&&!n&&this.emit("reachBeginning toEdge"),a&&!o&&this.emit("reachEnd toEdge"),(n&&!r||o&&!a)&&this.emit("fromEdge"),this.emit("progress",i)},updateSlidesClasses:function(){let e,t,s,{slides:i,params:r,slidesEl:a,activeIndex:l}=this,n=this.virtual&&r.virtual.enabled,o=this.grid&&r.grid&&r.grid.rows>1,d=e=>k(a,`.${r.slideClass}${e}, swiper-slide${e}`)[0];if(n)if(r.loop){let t=l-this.virtual.slidesBefore;t<0&&(t=this.virtual.slides.length+t),t>=this.virtual.slides.length&&(t-=this.virtual.slides.length),e=d(`[data-swiper-slide-index="${t}"]`)}else e=d(`[data-swiper-slide-index="${l}"]`);else o?(e=i.find(e=>e.column===l),s=i.find(e=>e.column===l+1),t=i.find(e=>e.column===l-1)):e=i[l];e&&!o&&(s=function(e,t){let s=[];for(;e.nextElementSibling;){let i=e.nextElementSibling;t?i.matches(t)&&s.push(i):s.push(i),e=i}return s}(e,`.${r.slideClass}, swiper-slide`)[0],r.loop&&!s&&(s=i[0]),t=function(e,t){let s=[];for(;e.previousElementSibling;){let i=e.previousElementSibling;t?i.matches(t)&&s.push(i):s.push(i),e=i}return s}(e,`.${r.slideClass}, swiper-slide`)[0],r.loop),i.forEach(i=>{H(i,i===e,r.slideActiveClass),H(i,i===s,r.slideNextClass),H(i,i===t,r.slidePrevClass)}),this.emitSlidesClasses()},updateActiveIndex:function(e){let t,s,i=this,r=i.rtlTranslate?i.translate:-i.translate,{snapGrid:a,params:l,activeIndex:n,realIndex:o,snapIndex:d}=i,c=e,p=e=>{let t=e-i.virtual.slidesBefore;return t<0&&(t=i.virtual.slides.length+t),t>=i.virtual.slides.length&&(t-=i.virtual.slides.length),t};if(void 0===c&&(c=function(e){let t,{slidesGrid:s,params:i}=e,r=e.rtlTranslate?e.translate:-e.translate;for(let e=0;e<s.length;e+=1)void 0!==s[e+1]?r>=s[e]&&r<s[e+1]-(s[e+1]-s[e])/2?t=e:r>=s[e]&&r<s[e+1]&&(t=e+1):r>=s[e]&&(t=e);return i.normalizeSlideIndex&&(t<0||void 0===t)&&(t=0),t}(i)),a.indexOf(r)>=0)t=a.indexOf(r);else{let e=Math.min(l.slidesPerGroupSkip,c);t=e+Math.floor((c-e)/l.slidesPerGroup)}if(t>=a.length&&(t=a.length-1),c===n&&!i.params.loop){t!==d&&(i.snapIndex=t,i.emit("snapIndexChange"));return}if(c===n&&i.params.loop&&i.virtual&&i.params.virtual.enabled){i.realIndex=p(c);return}let u=i.grid&&l.grid&&l.grid.rows>1;if(i.virtual&&l.virtual.enabled&&l.loop)s=p(c);else if(u){let e=i.slides.find(e=>e.column===c),t=parseInt(e.getAttribute("data-swiper-slide-index"),10);Number.isNaN(t)&&(t=Math.max(i.slides.indexOf(e),0)),s=Math.floor(t/l.grid.rows)}else if(i.slides[c]){let e=i.slides[c].getAttribute("data-swiper-slide-index");s=e?parseInt(e,10):c}else s=c;Object.assign(i,{previousSnapIndex:d,snapIndex:t,previousRealIndex:o,realIndex:s,previousIndex:n,activeIndex:c}),i.initialized&&X(i),i.emit("activeIndexChange"),i.emit("snapIndexChange"),(i.initialized||i.params.runCallbacksOnInit)&&(o!==s&&i.emit("realIndexChange"),i.emit("slideChange"))},updateClickedSlide:function(e,t){let s,i=this.params,r=e.closest(`.${i.slideClass}, swiper-slide`);!r&&this.isElement&&t&&t.length>1&&t.includes(e)&&[...t.slice(t.indexOf(e)+1,t.length)].forEach(e=>{!r&&e.matches&&e.matches(`.${i.slideClass}, swiper-slide`)&&(r=e)});let a=!1;if(r){for(let e=0;e<this.slides.length;e+=1)if(this.slides[e]===r){a=!0,s=e;break}}if(r&&a)this.clickedSlide=r,this.virtual&&this.params.virtual.enabled?this.clickedIndex=parseInt(r.getAttribute("data-swiper-slide-index"),10):this.clickedIndex=s;else{this.clickedSlide=void 0,this.clickedIndex=void 0;return}i.slideToClickedSlide&&void 0!==this.clickedIndex&&this.clickedIndex!==this.activeIndex&&this.slideToClickedSlide()}},translate:{getTranslate:function(e){void 0===e&&(e=this.isHorizontal()?"x":"y");let{params:t,rtlTranslate:s,translate:i,wrapperEl:r}=this;if(t.virtualTranslate)return s?-i:i;if(t.cssMode)return i;let a=function(e,t){let s,i,r;void 0===t&&(t="x");let a=y(),l=function(e){let t,s=y();return s.getComputedStyle&&(t=s.getComputedStyle(e,null)),!t&&e.currentStyle&&(t=e.currentStyle),t||(t=e.style),t}(e);return a.WebKitCSSMatrix?((i=l.transform||l.webkitTransform).split(",").length>6&&(i=i.split(", ").map(e=>e.replace(",",".")).join(", ")),r=new a.WebKitCSSMatrix("none"===i?"":i)):s=(r=l.MozTransform||l.OTransform||l.MsTransform||l.msTransform||l.transform||l.getPropertyValue("transform").replace("translate(","matrix(1, 0, 0, 1,")).toString().split(","),"x"===t&&(i=a.WebKitCSSMatrix?r.m41:16===s.length?parseFloat(s[12]):parseFloat(s[4])),"y"===t&&(i=a.WebKitCSSMatrix?r.m42:16===s.length?parseFloat(s[13]):parseFloat(s[5])),i||0}(r,e);return a+=this.cssOverflowAdjustment(),s&&(a=-a),a||0},setTranslate:function(e,t){let s,{rtlTranslate:i,params:r,wrapperEl:a,progress:l}=this,n=0,o=0;this.isHorizontal()?n=i?-e:e:o=e,r.roundLengths&&(n=Math.floor(n),o=Math.floor(o)),this.previousTranslate=this.translate,this.translate=this.isHorizontal()?n:o,r.cssMode?a[this.isHorizontal()?"scrollLeft":"scrollTop"]=this.isHorizontal()?-n:-o:r.virtualTranslate||(this.isHorizontal()?n-=this.cssOverflowAdjustment():o-=this.cssOverflowAdjustment(),a.style.transform=`translate3d(${n}px, ${o}px, 0px)`);let d=this.maxTranslate()-this.minTranslate();(0===d?0:(e-this.minTranslate())/d)!==l&&this.updateProgress(e),this.emit("setTranslate",this.translate,t)},minTranslate:function(){return-this.snapGrid[0]},maxTranslate:function(){return-this.snapGrid[this.snapGrid.length-1]},translateTo:function(e,t,s,i,r){let a;void 0===e&&(e=0),void 0===t&&(t=this.params.speed),void 0===s&&(s=!0),void 0===i&&(i=!0);let l=this,{params:n,wrapperEl:o}=l;if(l.animating&&n.preventInteractionOnTransition)return!1;let d=l.minTranslate(),c=l.maxTranslate();if(a=i&&e>d?d:i&&e<c?c:e,l.updateProgress(a),n.cssMode){let e=l.isHorizontal();if(0===t)o[e?"scrollLeft":"scrollTop"]=-a;else{if(!l.support.smoothScroll)return j({swiper:l,targetPosition:-a,side:e?"left":"top"}),!0;o.scrollTo({[e?"left":"top"]:-a,behavior:"smooth"})}return!0}return 0===t?(l.setTransition(0),l.setTranslate(a),s&&(l.emit("beforeTransitionStart",t,r),l.emit("transitionEnd"))):(l.setTransition(t),l.setTranslate(a),s&&(l.emit("beforeTransitionStart",t,r),l.emit("transitionStart")),l.animating||(l.animating=!0,l.onTranslateToWrapperTransitionEnd||(l.onTranslateToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onTranslateToWrapperTransitionEnd),l.onTranslateToWrapperTransitionEnd=null,delete l.onTranslateToWrapperTransitionEnd,l.animating=!1,s&&l.emit("transitionEnd"))}),l.wrapperEl.addEventListener("transitionend",l.onTranslateToWrapperTransitionEnd))),!0}},transition:{setTransition:function(e,t){this.params.cssMode||(this.wrapperEl.style.transitionDuration=`${e}ms`,this.wrapperEl.style.transitionDelay=0===e?"0ms":""),this.emit("setTransition",e,t)},transitionStart:function(e,t){void 0===e&&(e=!0);let{params:s}=this;s.cssMode||(s.autoHeight&&this.updateAutoHeight(),U({swiper:this,runCallbacks:e,direction:t,step:"Start"}))},transitionEnd:function(e,t){void 0===e&&(e=!0);let{params:s}=this;this.animating=!1,s.cssMode||(this.setTransition(0),U({swiper:this,runCallbacks:e,direction:t,step:"End"}))}},slide:{slideTo:function(e,t,s,i,r){let a;void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e&&(e=parseInt(e,10));let l=this,n=e;n<0&&(n=0);let{params:o,snapGrid:d,slidesGrid:c,previousIndex:p,activeIndex:u,rtlTranslate:h,wrapperEl:m,enabled:f}=l;if(!f&&!i&&!r||l.destroyed||l.animating&&o.preventInteractionOnTransition)return!1;void 0===t&&(t=l.params.speed);let g=Math.min(l.params.slidesPerGroupSkip,n),v=g+Math.floor((n-g)/l.params.slidesPerGroup);v>=d.length&&(v=d.length-1);let x=-d[v];if(o.normalizeSlideIndex)for(let e=0;e<c.length;e+=1){let t=-Math.floor(100*x),s=Math.floor(100*c[e]),i=Math.floor(100*c[e+1]);void 0!==c[e+1]?t>=s&&t<i-(i-s)/2?n=e:t>=s&&t<i&&(n=e+1):t>=s&&(n=e)}if(l.initialized&&n!==u&&(!l.allowSlideNext&&(h?x>l.translate&&x>l.minTranslate():x<l.translate&&x<l.minTranslate())||!l.allowSlidePrev&&x>l.translate&&x>l.maxTranslate()&&(u||0)!==n))return!1;n!==(p||0)&&s&&l.emit("beforeSlideChangeStart"),l.updateProgress(x),a=n>u?"next":n<u?"prev":"reset";let b=l.virtual&&l.params.virtual.enabled;if(!(b&&r)&&(h&&-x===l.translate||!h&&x===l.translate))return l.updateActiveIndex(n),o.autoHeight&&l.updateAutoHeight(),l.updateSlidesClasses(),"slide"!==o.effect&&l.setTranslate(x),"reset"!==a&&(l.transitionStart(s,a),l.transitionEnd(s,a)),!1;if(o.cssMode){let e=l.isHorizontal(),s=h?x:-x;if(0===t)b&&(l.wrapperEl.style.scrollSnapType="none",l._immediateVirtual=!0),b&&!l._cssModeVirtualInitialSet&&l.params.initialSlide>0?(l._cssModeVirtualInitialSet=!0,requestAnimationFrame(()=>{m[e?"scrollLeft":"scrollTop"]=s})):m[e?"scrollLeft":"scrollTop"]=s,b&&requestAnimationFrame(()=>{l.wrapperEl.style.scrollSnapType="",l._immediateVirtual=!1});else{if(!l.support.smoothScroll)return j({swiper:l,targetPosition:s,side:e?"left":"top"}),!0;m.scrollTo({[e?"left":"top"]:s,behavior:"smooth"})}return!0}let w=F().isSafari;return b&&!r&&w&&l.isElement&&l.virtual.update(!1,!1,n),l.setTransition(t),l.setTranslate(x),l.updateActiveIndex(n),l.updateSlidesClasses(),l.emit("beforeTransitionStart",t,i),l.transitionStart(s,a),0===t?l.transitionEnd(s,a):l.animating||(l.animating=!0,l.onSlideToWrapperTransitionEnd||(l.onSlideToWrapperTransitionEnd=function(e){l&&!l.destroyed&&e.target===this&&(l.wrapperEl.removeEventListener("transitionend",l.onSlideToWrapperTransitionEnd),l.onSlideToWrapperTransitionEnd=null,delete l.onSlideToWrapperTransitionEnd,l.transitionEnd(s,a))}),l.wrapperEl.addEventListener("transitionend",l.onSlideToWrapperTransitionEnd)),!0},slideToLoop:function(e,t,s,i){void 0===e&&(e=0),void 0===s&&(s=!0),"string"==typeof e&&(e=parseInt(e,10));let r=this;if(r.destroyed)return;void 0===t&&(t=r.params.speed);let a=r.grid&&r.params.grid&&r.params.grid.rows>1,l=e;if(r.params.loop)if(r.virtual&&r.params.virtual.enabled)l+=r.virtual.slidesBefore;else{let e;if(a){let t=l*r.params.grid.rows;e=r.slides.find(e=>+e.getAttribute("data-swiper-slide-index")===t).column}else e=r.getSlideIndexByData(l);let t=a?Math.ceil(r.slides.length/r.params.grid.rows):r.slides.length,{centeredSlides:s}=r.params,n=r.params.slidesPerView;"auto"===n?n=r.slidesPerViewDynamic():(n=Math.ceil(parseFloat(r.params.slidesPerView,10)),s&&n%2==0&&(n+=1));let o=t-e<n;if(s&&(o=o||e<Math.ceil(n/2)),i&&s&&"auto"!==r.params.slidesPerView&&!a&&(o=!1),o){let i=s?e<r.activeIndex?"prev":"next":e-r.activeIndex-1<r.params.slidesPerView?"next":"prev";r.loopFix({direction:i,slideTo:!0,activeSlideIndex:"next"===i?e+1:e-t+1,slideRealIndex:"next"===i?r.realIndex:void 0})}if(a){let e=l*r.params.grid.rows;l=r.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e).column}else l=r.getSlideIndexByData(l)}return requestAnimationFrame(()=>{r.slideTo(l,t,s,i)}),r},slideNext:function(e,t,s){void 0===t&&(t=!0);let i=this,{enabled:r,params:a,animating:l}=i;if(!r||i.destroyed)return i;void 0===e&&(e=i.params.speed);let n=a.slidesPerGroup;"auto"===a.slidesPerView&&1===a.slidesPerGroup&&a.slidesPerGroupAuto&&(n=Math.max(i.slidesPerViewDynamic("current",!0),1));let o=i.activeIndex<a.slidesPerGroupSkip?1:n,d=i.virtual&&a.virtual.enabled;if(a.loop){if(l&&!d&&a.loopPreventsSliding)return!1;if(i.loopFix({direction:"next"}),i._clientLeft=i.wrapperEl.clientLeft,i.activeIndex===i.slides.length-1&&a.cssMode)return requestAnimationFrame(()=>{i.slideTo(i.activeIndex+o,e,t,s)}),!0}return a.rewind&&i.isEnd?i.slideTo(0,e,t,s):i.slideTo(i.activeIndex+o,e,t,s)},slidePrev:function(e,t,s){void 0===t&&(t=!0);let i=this,{params:r,snapGrid:a,slidesGrid:l,rtlTranslate:n,enabled:o,animating:d}=i;if(!o||i.destroyed)return i;void 0===e&&(e=i.params.speed);let c=i.virtual&&r.virtual.enabled;if(r.loop){if(d&&!c&&r.loopPreventsSliding)return!1;i.loopFix({direction:"prev"}),i._clientLeft=i.wrapperEl.clientLeft}function p(e){return e<0?-Math.floor(Math.abs(e)):Math.floor(e)}let u=p(n?i.translate:-i.translate),h=a.map(e=>p(e)),m=r.freeMode&&r.freeMode.enabled,f=a[h.indexOf(u)-1];if(void 0===f&&(r.cssMode||m)){let e;a.forEach((t,s)=>{u>=t&&(e=s)}),void 0!==e&&(f=m?a[e]:a[e>0?e-1:e])}let g=0;if(void 0!==f&&((g=l.indexOf(f))<0&&(g=i.activeIndex-1),"auto"===r.slidesPerView&&1===r.slidesPerGroup&&r.slidesPerGroupAuto&&(g=Math.max(g=g-i.slidesPerViewDynamic("previous",!0)+1,0))),r.rewind&&i.isBeginning){let r=i.params.virtual&&i.params.virtual.enabled&&i.virtual?i.virtual.slides.length-1:i.slides.length-1;return i.slideTo(r,e,t,s)}return r.loop&&0===i.activeIndex&&r.cssMode?(requestAnimationFrame(()=>{i.slideTo(g,e,t,s)}),!0):i.slideTo(g,e,t,s)},slideReset:function(e,t,s){if(void 0===t&&(t=!0),!this.destroyed)return void 0===e&&(e=this.params.speed),this.slideTo(this.activeIndex,e,t,s)},slideToClosest:function(e,t,s,i){if(void 0===t&&(t=!0),void 0===i&&(i=.5),this.destroyed)return;void 0===e&&(e=this.params.speed);let r=this.activeIndex,a=Math.min(this.params.slidesPerGroupSkip,r),l=a+Math.floor((r-a)/this.params.slidesPerGroup),n=this.rtlTranslate?this.translate:-this.translate;if(n>=this.snapGrid[l]){let e=this.snapGrid[l];n-e>(this.snapGrid[l+1]-e)*i&&(r+=this.params.slidesPerGroup)}else{let e=this.snapGrid[l-1];n-e<=(this.snapGrid[l]-e)*i&&(r-=this.params.slidesPerGroup)}return r=Math.min(r=Math.max(r,0),this.slidesGrid.length-1),this.slideTo(r,e,t,s)},slideToClickedSlide:function(){let e,t=this;if(t.destroyed)return;let{params:s,slidesEl:i}=t,r="auto"===s.slidesPerView?t.slidesPerViewDynamic():s.slidesPerView,a=t.getSlideIndexWhenGrid(t.clickedIndex),l=t.isElement?"swiper-slide":`.${s.slideClass}`,n=t.grid&&t.params.grid&&t.params.grid.rows>1;if(s.loop){if(t.animating)return;e=parseInt(t.clickedSlide.getAttribute("data-swiper-slide-index"),10),s.centeredSlides?t.slideToLoop(e):a>(n?(t.slides.length-r)/2-(t.params.grid.rows-1):t.slides.length-r)?(t.loopFix(),a=t.getSlideIndex(k(i,`${l}[data-swiper-slide-index="${e}"]`)[0]),S(()=>{t.slideTo(a)})):t.slideTo(a)}else t.slideTo(a)}},loop:{loopCreate:function(e,t){let s=this,{params:i,slidesEl:r}=s;if(!i.loop||s.virtual&&s.params.virtual.enabled)return;let a=s.grid&&i.grid&&i.grid.rows>1;i.loopAddBlankSlides&&(i.slidesPerGroup>1||a)&&(()=>{let e=k(r,`.${i.slideBlankClass}`);e.forEach(e=>{e.remove()}),e.length>0&&(s.recalcSlides(),s.updateSlides())})();let l=i.slidesPerGroup*(a?i.grid.rows:1),n=s.slides.length%l!=0,o=a&&s.slides.length%i.grid.rows!=0,d=e=>{for(let t=0;t<e;t+=1){let e=s.isElement?N("swiper-slide",[i.slideBlankClass]):N("div",[i.slideClass,i.slideBlankClass]);s.slidesEl.append(e)}};n?i.loopAddBlankSlides?(d(l-s.slides.length%l),s.recalcSlides(),s.updateSlides()):M("Swiper Loop Warning: The number of slides is not even to slidesPerGroup, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)"):o&&(i.loopAddBlankSlides?(d(i.grid.rows-s.slides.length%i.grid.rows),s.recalcSlides(),s.updateSlides()):M("Swiper Loop Warning: The number of slides is not even to grid.rows, loop mode may not function properly. You need to add more slides (or make duplicates, or empty slides)")),k(r,`.${i.slideClass}, swiper-slide`).forEach((e,t)=>{e.setAttribute("data-swiper-slide-index",t)}),s.loopFix({slideRealIndex:e,direction:i.centeredSlides?void 0:"next",initial:t})},loopFix:function(e){let{slideRealIndex:t,slideTo:s=!0,direction:i,setTranslate:r,activeSlideIndex:a,initial:l,byController:n,byMousewheel:o}=void 0===e?{}:e,d=this;if(!d.params.loop)return;d.emit("beforeLoopFix");let{slides:c,allowSlidePrev:p,allowSlideNext:u,slidesEl:h,params:m}=d,{centeredSlides:f,initialSlide:g}=m;if(d.allowSlidePrev=!0,d.allowSlideNext=!0,d.virtual&&m.virtual.enabled){s&&(m.centeredSlides||0!==d.snapIndex?m.centeredSlides&&d.snapIndex<m.slidesPerView?d.slideTo(d.virtual.slides.length+d.snapIndex,0,!1,!0):d.snapIndex===d.snapGrid.length-1&&d.slideTo(d.virtual.slidesBefore,0,!1,!0):d.slideTo(d.virtual.slides.length,0,!1,!0)),d.allowSlidePrev=p,d.allowSlideNext=u,d.emit("loopFix");return}let v=m.slidesPerView;"auto"===v?v=d.slidesPerViewDynamic():(v=Math.ceil(parseFloat(m.slidesPerView,10)),f&&v%2==0&&(v+=1));let x=m.slidesPerGroupAuto?v:m.slidesPerGroup,b=f?Math.max(x,Math.ceil(v/2)):x;b%x!=0&&(b+=x-b%x),d.loopedSlides=b+=m.loopAdditionalSlides;let w=d.grid&&m.grid&&m.grid.rows>1;c.length<v+b||"cards"===d.params.effect&&c.length<v+2*b?M("Swiper Loop Warning: The number of slides is not enough for loop mode, it will be disabled or not function properly. You need to add more slides (or make duplicates) or lower the values of slidesPerView and slidesPerGroup parameters"):w&&"row"===m.grid.fill&&M("Swiper Loop Warning: Loop mode is not compatible with grid.fill = `row`");let y=[],S=[],E=w?Math.ceil(c.length/m.grid.rows):c.length,T=l&&E-g<v&&!f,C=T?g:d.activeIndex;void 0===a?a=d.getSlideIndex(c.find(e=>e.classList.contains(m.slideActiveClass))):C=a;let P="next"===i||!i,j="prev"===i||!i,k=0,N=0,L=(w?c[a].column:a)+(f&&void 0===r?-v/2+.5:0);if(L<b){k=Math.max(b-L,x);for(let e=0;e<b-L;e+=1){let t=e-Math.floor(e/E)*E;if(w){let e=E-t-1;for(let t=c.length-1;t>=0;t-=1)c[t].column===e&&y.push(t)}else y.push(E-t-1)}}else if(L+v>E-b){N=Math.max(L-(E-2*b),x),T&&(N=Math.max(N,v-E+g+1));for(let e=0;e<N;e+=1){let t=e-Math.floor(e/E)*E;w?c.forEach((e,s)=>{e.column===t&&S.push(s)}):S.push(t)}}if(d.__preventObserver__=!0,requestAnimationFrame(()=>{d.__preventObserver__=!1}),"cards"===d.params.effect&&c.length<v+2*b&&(S.includes(a)&&S.splice(S.indexOf(a),1),y.includes(a)&&y.splice(y.indexOf(a),1)),j&&y.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.prepend(c[e]),c[e].swiperLoopMoveDOM=!1}),P&&S.forEach(e=>{c[e].swiperLoopMoveDOM=!0,h.append(c[e]),c[e].swiperLoopMoveDOM=!1}),d.recalcSlides(),"auto"===m.slidesPerView?d.updateSlides():w&&(y.length>0&&j||S.length>0&&P)&&d.slides.forEach((e,t)=>{d.grid.updateSlide(t,e,d.slides)}),m.watchSlidesProgress&&d.updateSlidesOffset(),s){if(y.length>0&&j){if(void 0===t){let e=d.slidesGrid[C],t=d.slidesGrid[C+k]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C+Math.ceil(k),0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else if(r){let e=w?y.length/m.grid.rows:y.length;d.slideTo(d.activeIndex+e,0,!1,!0),d.touchEventsData.currentTranslate=d.translate}}else if(S.length>0&&P)if(void 0===t){let e=d.slidesGrid[C],t=d.slidesGrid[C-N]-e;o?d.setTranslate(d.translate-t):(d.slideTo(C-N,0,!1,!0),r&&(d.touchEventsData.startTranslate=d.touchEventsData.startTranslate-t,d.touchEventsData.currentTranslate=d.touchEventsData.currentTranslate-t))}else{let e=w?S.length/m.grid.rows:S.length;d.slideTo(d.activeIndex-e,0,!1,!0)}}if(d.allowSlidePrev=p,d.allowSlideNext=u,d.controller&&d.controller.control&&!n){let e={slideRealIndex:t,direction:i,setTranslate:r,activeSlideIndex:a,byController:!0};Array.isArray(d.controller.control)?d.controller.control.forEach(t=>{!t.destroyed&&t.params.loop&&t.loopFix({...e,slideTo:t.params.slidesPerView===m.slidesPerView&&s})}):d.controller.control instanceof d.constructor&&d.controller.control.params.loop&&d.controller.control.loopFix({...e,slideTo:d.controller.control.params.slidesPerView===m.slidesPerView&&s})}d.emit("loopFix")},loopDestroy:function(){let{params:e,slidesEl:t}=this;if(!e.loop||!t||this.virtual&&this.params.virtual.enabled)return;this.recalcSlides();let s=[];this.slides.forEach(e=>{s[void 0===e.swiperSlideIndex?+e.getAttribute("data-swiper-slide-index"):e.swiperSlideIndex]=e}),this.slides.forEach(e=>{e.removeAttribute("data-swiper-slide-index")}),s.forEach(e=>{t.append(e)}),this.recalcSlides(),this.slideTo(this.realIndex,0)}},grabCursor:{setGrabCursor:function(e){let t=this;if(!t.params.simulateTouch||t.params.watchOverflow&&t.isLocked||t.params.cssMode)return;let s="container"===t.params.touchEventsTarget?t.el:t.wrapperEl;t.isElement&&(t.__preventObserver__=!0),s.style.cursor="move",s.style.cursor=e?"grabbing":"grab",t.isElement&&requestAnimationFrame(()=>{t.__preventObserver__=!1})},unsetGrabCursor:function(){let e=this;e.params.watchOverflow&&e.isLocked||e.params.cssMode||(e.isElement&&(e.__preventObserver__=!0),e["container"===e.params.touchEventsTarget?"el":"wrapperEl"].style.cursor="",e.isElement&&requestAnimationFrame(()=>{e.__preventObserver__=!1}))}},events:{attachEvents:function(){let{params:e}=this;this.onTouchStart=Q.bind(this),this.onTouchMove=Z.bind(this),this.onTouchEnd=J.bind(this),this.onDocumentTouchStart=er.bind(this),e.cssMode&&(this.onScroll=es.bind(this)),this.onClick=et.bind(this),this.onLoad=ei.bind(this),ea(this,"on")},detachEvents:function(){ea(this,"off")}},breakpoints:{setBreakpoint:function(){let e=this,{realIndex:t,initialized:s,params:i,el:r}=e,a=i.breakpoints;if(!a||a&&0===Object.keys(a).length)return;let l=b(),n="window"!==i.breakpointsBase&&i.breakpointsBase?"container":i.breakpointsBase,o=["window","container"].includes(i.breakpointsBase)||!i.breakpointsBase?e.el:l.querySelector(i.breakpointsBase),d=e.getBreakpoint(a,n,o);if(!d||e.currentBreakpoint===d)return;let c=(d in a?a[d]:void 0)||e.originalParams,p=el(e,i),u=el(e,c),h=e.params.grabCursor,m=c.grabCursor,f=i.enabled;p&&!u?(r.classList.remove(`${i.containerModifierClass}grid`,`${i.containerModifierClass}grid-column`),e.emitContainerClasses()):!p&&u&&(r.classList.add(`${i.containerModifierClass}grid`),(c.grid.fill&&"column"===c.grid.fill||!c.grid.fill&&"column"===i.grid.fill)&&r.classList.add(`${i.containerModifierClass}grid-column`),e.emitContainerClasses()),h&&!m?e.unsetGrabCursor():!h&&m&&e.setGrabCursor(),["navigation","pagination","scrollbar"].forEach(t=>{if(void 0===c[t])return;let s=i[t]&&i[t].enabled,r=c[t]&&c[t].enabled;s&&!r&&e[t].disable(),!s&&r&&e[t].enable()});let g=c.direction&&c.direction!==i.direction,v=i.loop&&(c.slidesPerView!==i.slidesPerView||g),x=i.loop;g&&s&&e.changeDirection(),C(e.params,c);let w=e.params.enabled,y=e.params.loop;Object.assign(e,{allowTouchMove:e.params.allowTouchMove,allowSlideNext:e.params.allowSlideNext,allowSlidePrev:e.params.allowSlidePrev}),f&&!w?e.disable():!f&&w&&e.enable(),e.currentBreakpoint=d,e.emit("_beforeBreakpoint",c),s&&(v?(e.loopDestroy(),e.loopCreate(t),e.updateSlides()):!x&&y?(e.loopCreate(t),e.updateSlides()):x&&!y&&e.loopDestroy()),e.emit("breakpoint",c)},getBreakpoint:function(e,t,s){if(void 0===t&&(t="window"),!e||"container"===t&&!s)return;let i=!1,r=y(),a="window"===t?r.innerHeight:s.clientHeight,l=Object.keys(e).map(e=>"string"==typeof e&&0===e.indexOf("@")?{value:a*parseFloat(e.substr(1)),point:e}:{value:e,point:e});l.sort((e,t)=>parseInt(e.value,10)-parseInt(t.value,10));for(let e=0;e<l.length;e+=1){let{point:a,value:n}=l[e];"window"===t?r.matchMedia(`(min-width: ${n}px)`).matches&&(i=a):n<=s.clientWidth&&(i=a)}return i||"max"}},checkOverflow:{checkOverflow:function(){let{isLocked:e,params:t}=this,{slidesOffsetBefore:s}=t;if(s){let e=this.slides.length-1,t=this.slidesGrid[e]+this.slidesSizesGrid[e]+2*s;this.isLocked=this.size>t}else this.isLocked=1===this.snapGrid.length;!0===t.allowSlideNext&&(this.allowSlideNext=!this.isLocked),!0===t.allowSlidePrev&&(this.allowSlidePrev=!this.isLocked),e&&e!==this.isLocked&&(this.isEnd=!1),e!==this.isLocked&&this.emit(this.isLocked?"lock":"unlock")}},classes:{addClasses:function(){let{classNames:e,params:t,rtl:s,el:i,device:r}=this,a=function(e,t){let s=[];return e.forEach(e=>{"object"==typeof e?Object.keys(e).forEach(i=>{e[i]&&s.push(t+i)}):"string"==typeof e&&s.push(t+e)}),s}(["initialized",t.direction,{"free-mode":this.params.freeMode&&t.freeMode.enabled},{autoheight:t.autoHeight},{rtl:s},{grid:t.grid&&t.grid.rows>1},{"grid-column":t.grid&&t.grid.rows>1&&"column"===t.grid.fill},{android:r.android},{ios:r.ios},{"css-mode":t.cssMode},{centered:t.cssMode&&t.centeredSlides},{"watch-progress":t.watchSlidesProgress}],t.containerModifierClass);e.push(...a),i.classList.add(...e),this.emitContainerClasses()},removeClasses:function(){let{el:e,classNames:t}=this;e&&"string"!=typeof e&&(e.classList.remove(...t),this.emitContainerClasses())}}},ed={};class ec{constructor(){let e,t;for(var s=arguments.length,i=Array(s),r=0;r<s;r++)i[r]=arguments[r];1===i.length&&i[0].constructor&&"Object"===Object.prototype.toString.call(i[0]).slice(8,-1)?t=i[0]:[e,t]=i,t||(t={}),t=C({},t),e&&!t.el&&(t.el=e);let a=b();if(t.el&&"string"==typeof t.el&&a.querySelectorAll(t.el).length>1){let e=[];return a.querySelectorAll(t.el).forEach(s=>{let i=C({},t,{el:s});e.push(new ec(i))}),e}let l=this;l.__swiper__=!0,l.support=R(),l.device=V({userAgent:t.userAgent}),l.browser=F(),l.eventsListeners={},l.eventsAnyListeners=[],l.modules=[...l.__modules__],t.modules&&Array.isArray(t.modules)&&l.modules.push(...t.modules);let n={};l.modules.forEach(e=>{e({params:t,swiper:l,extendParams:function(e,t){return function(s){void 0===s&&(s={});let i=Object.keys(s)[0],r=s[i];return"object"!=typeof r||null===r?void C(t,s):(!0===e[i]&&(e[i]={enabled:!0}),"navigation"===i&&e[i]&&e[i].enabled&&!e[i].prevEl&&!e[i].nextEl&&(e[i].auto=!0),["pagination","scrollbar"].indexOf(i)>=0&&e[i]&&e[i].enabled&&!e[i].el&&(e[i].auto=!0),i in e&&"enabled"in r)?void("object"==typeof e[i]&&!("enabled"in e[i])&&(e[i].enabled=!0),!e[i]&&(e[i]={enabled:!1}),C(t,s)):void C(t,s)}}(t,n),on:l.on.bind(l),once:l.once.bind(l),off:l.off.bind(l),emit:l.emit.bind(l)})});let o=C({},en,n);return l.params=C({},o,ed,t),l.originalParams=C({},l.params),l.passedParams=C({},t),l.params&&l.params.on&&Object.keys(l.params.on).forEach(e=>{l.on(e,l.params.on[e])}),l.params&&l.params.onAny&&l.onAny(l.params.onAny),Object.assign(l,{enabled:l.params.enabled,el:e,classNames:[],slides:[],slidesGrid:[],snapGrid:[],slidesSizesGrid:[],isHorizontal:()=>"horizontal"===l.params.direction,isVertical:()=>"vertical"===l.params.direction,activeIndex:0,realIndex:0,isBeginning:!0,isEnd:!1,translate:0,previousTranslate:0,progress:0,velocity:0,animating:!1,cssOverflowAdjustment(){return 8388608*Math.trunc(this.translate/8388608)},allowSlideNext:l.params.allowSlideNext,allowSlidePrev:l.params.allowSlidePrev,touchEventsData:{isTouched:void 0,isMoved:void 0,allowTouchCallbacks:void 0,touchStartTime:void 0,isScrolling:void 0,currentTranslate:void 0,startTranslate:void 0,allowThresholdMove:void 0,focusableElements:l.params.focusableElements,lastClickTime:0,clickTimeout:void 0,velocities:[],allowMomentumBounce:void 0,startMoving:void 0,pointerId:null,touchId:null},allowClick:!0,allowTouchMove:l.params.allowTouchMove,touches:{startX:0,startY:0,currentX:0,currentY:0,diff:0},imagesToLoad:[],imagesLoaded:0}),l.emit("_swiper"),l.params.init&&l.init(),l}getDirectionLabel(e){return this.isHorizontal()?e:({width:"height","margin-top":"margin-left","margin-bottom ":"margin-right","margin-left":"margin-top","margin-right":"margin-bottom","padding-left":"padding-top","padding-right":"padding-bottom",marginRight:"marginBottom"})[e]}getSlideIndex(e){let{slidesEl:t,params:s}=this,i=O(k(t,`.${s.slideClass}, swiper-slide`)[0]);return O(e)-i}getSlideIndexByData(e){return this.getSlideIndex(this.slides.find(t=>+t.getAttribute("data-swiper-slide-index")===e))}getSlideIndexWhenGrid(e){return this.grid&&this.params.grid&&this.params.grid.rows>1&&("column"===this.params.grid.fill?e=Math.floor(e/this.params.grid.rows):"row"===this.params.grid.fill&&(e%=Math.ceil(this.slides.length/this.params.grid.rows))),e}recalcSlides(){let{slidesEl:e,params:t}=this;this.slides=k(e,`.${t.slideClass}, swiper-slide`)}enable(){this.enabled||(this.enabled=!0,this.params.grabCursor&&this.setGrabCursor(),this.emit("enable"))}disable(){this.enabled&&(this.enabled=!1,this.params.grabCursor&&this.unsetGrabCursor(),this.emit("disable"))}setProgress(e,t){e=Math.min(Math.max(e,0),1);let s=this.minTranslate(),i=(this.maxTranslate()-s)*e+s;this.translateTo(i,void 0===t?0:t),this.updateActiveIndex(),this.updateSlidesClasses()}emitContainerClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=e.el.className.split(" ").filter(t=>0===t.indexOf("swiper")||0===t.indexOf(e.params.containerModifierClass));e.emit("_containerClasses",t.join(" "))}getSlideClasses(e){let t=this;return t.destroyed?"":e.className.split(" ").filter(e=>0===e.indexOf("swiper-slide")||0===e.indexOf(t.params.slideClass)).join(" ")}emitSlidesClasses(){let e=this;if(!e.params._emitClasses||!e.el)return;let t=[];e.slides.forEach(s=>{let i=e.getSlideClasses(s);t.push({slideEl:s,classNames:i}),e.emit("_slideClass",s,i)}),e.emit("_slideClasses",t)}slidesPerViewDynamic(e,t){void 0===e&&(e="current"),void 0===t&&(t=!1);let{params:s,slides:i,slidesGrid:r,slidesSizesGrid:a,size:l,activeIndex:n}=this,o=1;if("number"==typeof s.slidesPerView)return s.slidesPerView;if(s.centeredSlides){let e,t=i[n]?Math.ceil(i[n].swiperSlideSize):0;for(let s=n+1;s<i.length;s+=1)i[s]&&!e&&(t+=Math.ceil(i[s].swiperSlideSize),o+=1,t>l&&(e=!0));for(let s=n-1;s>=0;s-=1)i[s]&&!e&&(t+=i[s].swiperSlideSize,o+=1,t>l&&(e=!0))}else if("current"===e)for(let e=n+1;e<i.length;e+=1)(t?r[e]+a[e]-r[n]<l:r[e]-r[n]<l)&&(o+=1);else for(let e=n-1;e>=0;e-=1)r[n]-r[e]<l&&(o+=1);return o}update(){let e,t=this;if(!t||t.destroyed)return;let{snapGrid:s,params:i}=t;function r(){let e=Math.min(Math.max(t.rtlTranslate?-1*t.translate:t.translate,t.maxTranslate()),t.minTranslate());t.setTranslate(e),t.updateActiveIndex(),t.updateSlidesClasses()}if(i.breakpoints&&t.setBreakpoint(),[...t.el.querySelectorAll('[loading="lazy"]')].forEach(e=>{e.complete&&W(t,e)}),t.updateSize(),t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),i.freeMode&&i.freeMode.enabled&&!i.cssMode)r(),i.autoHeight&&t.updateAutoHeight();else{if(("auto"===i.slidesPerView||i.slidesPerView>1)&&t.isEnd&&!i.centeredSlides){let s=t.virtual&&i.virtual.enabled?t.virtual.slides:t.slides;e=t.slideTo(s.length-1,0,!1,!0)}else e=t.slideTo(t.activeIndex,0,!1,!0);e||r()}i.watchOverflow&&s!==t.snapGrid&&t.checkOverflow(),t.emit("update")}changeDirection(e,t){void 0===t&&(t=!0);let s=this.params.direction;return e||(e="horizontal"===s?"vertical":"horizontal"),e===s||"horizontal"!==e&&"vertical"!==e||(this.el.classList.remove(`${this.params.containerModifierClass}${s}`),this.el.classList.add(`${this.params.containerModifierClass}${e}`),this.emitContainerClasses(),this.params.direction=e,this.slides.forEach(t=>{"vertical"===e?t.style.width="":t.style.height=""}),this.emit("changeDirection"),t&&this.update()),this}changeLanguageDirection(e){(!this.rtl||"rtl"!==e)&&(this.rtl||"ltr"!==e)&&(this.rtl="rtl"===e,this.rtlTranslate="horizontal"===this.params.direction&&this.rtl,this.rtl?(this.el.classList.add(`${this.params.containerModifierClass}rtl`),this.el.dir="rtl"):(this.el.classList.remove(`${this.params.containerModifierClass}rtl`),this.el.dir="ltr"),this.update())}mount(e){let t=this;if(t.mounted)return!0;let s=e||t.params.el;if("string"==typeof s&&(s=document.querySelector(s)),!s)return!1;s.swiper=t,s.parentNode&&s.parentNode.host&&s.parentNode.host.nodeName===t.params.swiperElementNodeName.toUpperCase()&&(t.isElement=!0);let i=()=>`.${(t.params.wrapperClass||"").trim().split(" ").join(".")}`,r=s&&s.shadowRoot&&s.shadowRoot.querySelector?s.shadowRoot.querySelector(i()):k(s,i())[0];return!r&&t.params.createElements&&(r=N("div",t.params.wrapperClass),s.append(r),k(s,`.${t.params.slideClass}`).forEach(e=>{r.append(e)})),Object.assign(t,{el:s,wrapperEl:r,slidesEl:t.isElement&&!s.parentNode.host.slideSlots?s.parentNode.host:r,hostEl:t.isElement?s.parentNode.host:s,mounted:!0,rtl:"rtl"===s.dir.toLowerCase()||"rtl"===L(s,"direction"),rtlTranslate:"horizontal"===t.params.direction&&("rtl"===s.dir.toLowerCase()||"rtl"===L(s,"direction")),wrongRTL:"-webkit-box"===L(r,"display")}),!0}init(e){let t=this;if(t.initialized||!1===t.mount(e))return t;t.emit("beforeInit"),t.params.breakpoints&&t.setBreakpoint(),t.addClasses(),t.updateSize(),t.updateSlides(),t.params.watchOverflow&&t.checkOverflow(),t.params.grabCursor&&t.enabled&&t.setGrabCursor(),t.params.loop&&t.virtual&&t.params.virtual.enabled?t.slideTo(t.params.initialSlide+t.virtual.slidesBefore,0,t.params.runCallbacksOnInit,!1,!0):t.slideTo(t.params.initialSlide,0,t.params.runCallbacksOnInit,!1,!0),t.params.loop&&t.loopCreate(void 0,!0),t.attachEvents();let s=[...t.el.querySelectorAll('[loading="lazy"]')];return t.isElement&&s.push(...t.hostEl.querySelectorAll('[loading="lazy"]')),s.forEach(e=>{e.complete?W(t,e):e.addEventListener("load",e=>{W(t,e.target)})}),X(t),t.initialized=!0,X(t),t.emit("init"),t.emit("afterInit"),t}destroy(e,t){void 0===e&&(e=!0),void 0===t&&(t=!0);let s=this,{params:i,el:r,wrapperEl:a,slides:l}=s;if(void 0===s.params||s.destroyed)return null;(s.emit("beforeDestroy"),s.initialized=!1,s.detachEvents(),i.loop&&s.loopDestroy(),t&&(s.removeClasses(),r&&"string"!=typeof r&&r.removeAttribute("style"),a&&a.removeAttribute("style"),l&&l.length&&l.forEach(e=>{e.classList.remove(i.slideVisibleClass,i.slideFullyVisibleClass,i.slideActiveClass,i.slideNextClass,i.slidePrevClass),e.removeAttribute("style"),e.removeAttribute("data-swiper-slide-index")})),s.emit("destroy"),Object.keys(s.eventsListeners).forEach(e=>{s.off(e)}),!1!==e)&&(s.el&&"string"!=typeof s.el&&(s.el.swiper=null),Object.keys(s).forEach(e=>{try{s[e]=null}catch(e){}try{delete s[e]}catch(e){}}));return s.destroyed=!0,null}static extendDefaults(e){C(ed,e)}static get extendedDefaults(){return ed}static get defaults(){return en}static installModule(e){ec.prototype.__modules__||(ec.prototype.__modules__=[]);let t=ec.prototype.__modules__;"function"==typeof e&&0>t.indexOf(e)&&t.push(e)}static use(e){return Array.isArray(e)?e.forEach(e=>ec.installModule(e)):ec.installModule(e),ec}}Object.keys(eo).forEach(e=>{Object.keys(eo[e]).forEach(t=>{ec.prototype[t]=eo[e][t]})}),ec.use([function(e){let{swiper:t,on:s,emit:i}=e,r=y(),a=null,l=null,n=()=>{t&&!t.destroyed&&t.initialized&&(i("beforeResize"),i("resize"))},o=()=>{t&&!t.destroyed&&t.initialized&&(a=new ResizeObserver(e=>{l=r.requestAnimationFrame(()=>{let{width:s,height:i}=t,r=s,a=i;e.forEach(e=>{let{contentBoxSize:s,contentRect:i,target:l}=e;l&&l!==t.el||(r=i?i.width:(s[0]||s).inlineSize,a=i?i.height:(s[0]||s).blockSize)}),(r!==s||a!==i)&&n()})})).observe(t.el)},d=()=>{l&&r.cancelAnimationFrame(l),a&&a.unobserve&&t.el&&(a.unobserve(t.el),a=null)},c=()=>{t&&!t.destroyed&&t.initialized&&i("orientationchange")};s("init",()=>{if(t.params.resizeObserver&&void 0!==r.ResizeObserver)return void o();r.addEventListener("resize",n),r.addEventListener("orientationchange",c)}),s("destroy",()=>{d(),r.removeEventListener("resize",n),r.removeEventListener("orientationchange",c)})},function(e){let{swiper:t,extendParams:s,on:i,emit:r}=e,a=[],l=y(),n=function(e,s){void 0===s&&(s={});let i=new(l.MutationObserver||l.WebkitMutationObserver)(e=>{if(t.__preventObserver__)return;if(1===e.length)return void r("observerUpdate",e[0]);let s=function(){r("observerUpdate",e[0])};l.requestAnimationFrame?l.requestAnimationFrame(s):l.setTimeout(s,0)});i.observe(e,{attributes:void 0===s.attributes||s.attributes,childList:t.isElement||(void 0===s.childList||s).childList,characterData:void 0===s.characterData||s.characterData}),a.push(i)};s({observer:!1,observeParents:!1,observeSlideChildren:!1}),i("init",()=>{if(t.params.observer){if(t.params.observeParents){let e=A(t.hostEl);for(let t=0;t<e.length;t+=1)n(e[t])}n(t.hostEl,{childList:t.params.observeSlideChildren}),n(t.wrapperEl,{attributes:!1})}}),i("destroy",()=>{a.forEach(e=>{e.disconnect()}),a.splice(0,a.length)})}]);let ep=["eventsPrefix","injectStyles","injectStylesUrls","modules","init","_direction","oneWayMovement","swiperElementNodeName","touchEventsTarget","initialSlide","_speed","cssMode","updateOnWindowResize","resizeObserver","nested","focusableElements","_enabled","_width","_height","preventInteractionOnTransition","userAgent","url","_edgeSwipeDetection","_edgeSwipeThreshold","_freeMode","_autoHeight","setWrapperSize","virtualTranslate","_effect","breakpoints","breakpointsBase","_spaceBetween","_slidesPerView","maxBackfaceHiddenSlides","_grid","_slidesPerGroup","_slidesPerGroupSkip","_slidesPerGroupAuto","_centeredSlides","_centeredSlidesBounds","_slidesOffsetBefore","_slidesOffsetAfter","normalizeSlideIndex","_centerInsufficientSlides","_watchOverflow","roundLengths","touchRatio","touchAngle","simulateTouch","_shortSwipes","_longSwipes","longSwipesRatio","longSwipesMs","_followFinger","allowTouchMove","_threshold","touchMoveStopPropagation","touchStartPreventDefault","touchStartForcePreventDefault","touchReleaseOnEdges","uniqueNavElements","_resistance","_resistanceRatio","_watchSlidesProgress","_grabCursor","preventClicks","preventClicksPropagation","_slideToClickedSlide","_loop","loopAdditionalSlides","loopAddBlankSlides","loopPreventsSliding","_rewind","_allowSlidePrev","_allowSlideNext","_swipeHandler","_noSwiping","noSwipingClass","noSwipingSelector","passiveListeners","containerModifierClass","slideClass","slideActiveClass","slideVisibleClass","slideFullyVisibleClass","slideNextClass","slidePrevClass","slideBlankClass","wrapperClass","lazyPreloaderClass","lazyPreloadPrevNext","runCallbacksOnInit","observer","observeParents","observeSlideChildren","a11y","_autoplay","_controller","coverflowEffect","cubeEffect","fadeEffect","flipEffect","creativeEffect","cardsEffect","hashNavigation","history","keyboard","mousewheel","_navigation","_pagination","parallax","_scrollbar","_thumbs","virtual","zoom","control"];function eu(e){return"object"==typeof e&&null!==e&&e.constructor&&"Object"===Object.prototype.toString.call(e).slice(8,-1)&&!e.__swiper__}function eh(e,t){let s=["__proto__","constructor","prototype"];Object.keys(t).filter(e=>0>s.indexOf(e)).forEach(s=>{void 0===e[s]?e[s]=t[s]:eu(t[s])&&eu(e[s])&&Object.keys(t[s]).length>0?t[s].__swiper__?e[s]=t[s]:eh(e[s],t[s]):e[s]=t[s]})}function em(e){return void 0===e&&(e={}),e.navigation&&void 0===e.navigation.nextEl&&void 0===e.navigation.prevEl}function ef(e){return void 0===e&&(e={}),e.pagination&&void 0===e.pagination.el}function eg(e){return void 0===e&&(e={}),e.scrollbar&&void 0===e.scrollbar.el}function ev(e){void 0===e&&(e="");let t=e.split(" ").map(e=>e.trim()).filter(e=>!!e),s=[];return t.forEach(e=>{0>s.indexOf(e)&&s.push(e)}),s.join(" ")}let ex=e=>{e&&!e.destroyed&&e.params.virtual&&(!e.params.virtual||e.params.virtual.enabled)&&(e.updateSlides(),e.updateProgress(),e.updateSlidesClasses(),e.emit("_virtualUpdated"),e.parallax&&e.params.parallax&&e.params.parallax.enabled&&e.parallax.setTranslate())};function eb(){return(eb=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var s=arguments[t];for(var i in s)Object.prototype.hasOwnProperty.call(s,i)&&(e[i]=s[i])}return e}).apply(this,arguments)}function ew(e){return e.type&&e.type.displayName&&e.type.displayName.includes("SwiperSlide")}function ey(e,t){return"undefined"==typeof window?(0,p.useEffect)(e,t):(0,p.useLayoutEffect)(e,t)}let eS=(0,p.createContext)(null),eE=(0,p.createContext)(null),eT=(0,p.forwardRef)(function(e,t){var s;let{className:i,tag:r="div",wrapperTag:a="div",children:l,onSwiper:n,...o}=void 0===e?{}:e,d=!1,[c,u]=(0,p.useState)("swiper"),[h,m]=(0,p.useState)(null),[f,g]=(0,p.useState)(!1),v=(0,p.useRef)(!1),x=(0,p.useRef)(null),b=(0,p.useRef)(null),w=(0,p.useRef)(null),y=(0,p.useRef)(null),S=(0,p.useRef)(null),E=(0,p.useRef)(null),T=(0,p.useRef)(null),C=(0,p.useRef)(null),{params:P,passedParams:j,rest:k,events:M}=function(e,t){void 0===e&&(e={}),void 0===t&&(t=!0);let s={on:{}},i={},r={};eh(s,en),s._emitClasses=!0,s.init=!1;let a={},l=ep.map(e=>e.replace(/_/,""));return Object.keys(Object.assign({},e)).forEach(n=>{void 0!==e[n]&&(l.indexOf(n)>=0?eu(e[n])?(s[n]={},r[n]={},eh(s[n],e[n]),eh(r[n],e[n])):(s[n]=e[n],r[n]=e[n]):0===n.search(/on[A-Z]/)&&"function"==typeof e[n]?t?i[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:s.on[`${n[2].toLowerCase()}${n.substr(3)}`]=e[n]:a[n]=e[n])}),["navigation","pagination","scrollbar"].forEach(e=>{!0===s[e]&&(s[e]={}),!1===s[e]&&delete s[e]}),{params:s,passedParams:r,rest:a,events:i}}(o),{slides:N,slots:L}=function(e){let t=[],s={"container-start":[],"container-end":[],"wrapper-start":[],"wrapper-end":[]};return p.Children.toArray(e).forEach(e=>{if(ew(e))t.push(e);else if(e.props&&e.props.slot&&s[e.props.slot])s[e.props.slot].push(e);else if(e.props&&e.props.children){let i=function e(t){let s=[];return p.Children.toArray(t).forEach(t=>{ew(t)?s.push(t):t.props&&t.props.children&&e(t.props.children).forEach(e=>s.push(e))}),s}(e.props.children);i.length>0?i.forEach(e=>t.push(e)):s["container-end"].push(e)}else s["container-end"].push(e)}),{slides:t,slots:s}}(l),O=()=>{g(!f)};Object.assign(P.on,{_containerClasses(e,t){u(t)}});let A=()=>{Object.assign(P.on,M),d=!0;let e={...P};if(delete e.wrapperClass,b.current=new ec(e),b.current.virtual&&b.current.params.virtual.enabled){b.current.virtual.slides=N;let e={cache:!1,slides:N,renderExternal:m,renderExternalUpdate:!1};eh(b.current.params.virtual,e),eh(b.current.originalParams.virtual,e)}};x.current||A(),b.current&&b.current.on("_beforeBreakpoint",O);let _=()=>{!d&&M&&b.current&&Object.keys(M).forEach(e=>{b.current.on(e,M[e])})},z=()=>{M&&b.current&&Object.keys(M).forEach(e=>{b.current.off(e,M[e])})};return(0,p.useEffect)(()=>()=>{b.current&&b.current.off("_beforeBreakpoint",O)}),(0,p.useEffect)(()=>{!v.current&&b.current&&(b.current.emitSlidesClasses(),v.current=!0)}),ey(()=>{if(t&&(t.current=x.current),x.current)return b.current.destroyed&&A(),!function(e,t){let{el:s,nextEl:i,prevEl:r,paginationEl:a,scrollbarEl:l,swiper:n}=e;em(t)&&i&&r&&(n.params.navigation.nextEl=i,n.originalParams.navigation.nextEl=i,n.params.navigation.prevEl=r,n.originalParams.navigation.prevEl=r),ef(t)&&a&&(n.params.pagination.el=a,n.originalParams.pagination.el=a),eg(t)&&l&&(n.params.scrollbar.el=l,n.originalParams.scrollbar.el=l),n.init(s)}({el:x.current,nextEl:S.current,prevEl:E.current,paginationEl:T.current,scrollbarEl:C.current,swiper:b.current},P),n&&!b.current.destroyed&&n(b.current),()=>{b.current&&!b.current.destroyed&&b.current.destroy(!0,!1)}},[]),ey(()=>{_();let e=function(e,t,s,i,r){let a=[];if(!t)return a;let l=e=>{0>a.indexOf(e)&&a.push(e)};if(s&&i){let e=i.map(r),t=s.map(r);e.join("")!==t.join("")&&l("children"),i.length!==s.length&&l("children")}return ep.filter(e=>"_"===e[0]).map(e=>e.replace(/_/,"")).forEach(s=>{if(s in e&&s in t)if(eu(e[s])&&eu(t[s])){let i=Object.keys(e[s]),r=Object.keys(t[s]);i.length!==r.length?l(s):(i.forEach(i=>{e[s][i]!==t[s][i]&&l(s)}),r.forEach(i=>{e[s][i]!==t[s][i]&&l(s)}))}else e[s]!==t[s]&&l(s)}),a}(j,w.current,N,y.current,e=>e.key);return w.current=j,y.current=N,e.length&&b.current&&!b.current.destroyed&&function(e){let t,s,i,r,a,l,n,o,{swiper:d,slides:c,passedParams:p,changedParams:u,nextEl:h,prevEl:m,scrollbarEl:f,paginationEl:g}=e,v=u.filter(e=>"children"!==e&&"direction"!==e&&"wrapperClass"!==e),{params:x,pagination:b,navigation:w,scrollbar:y,virtual:S,thumbs:E}=d;u.includes("thumbs")&&p.thumbs&&p.thumbs.swiper&&!p.thumbs.swiper.destroyed&&x.thumbs&&(!x.thumbs.swiper||x.thumbs.swiper.destroyed)&&(t=!0),u.includes("controller")&&p.controller&&p.controller.control&&x.controller&&!x.controller.control&&(s=!0),u.includes("pagination")&&p.pagination&&(p.pagination.el||g)&&(x.pagination||!1===x.pagination)&&b&&!b.el&&(i=!0),u.includes("scrollbar")&&p.scrollbar&&(p.scrollbar.el||f)&&(x.scrollbar||!1===x.scrollbar)&&y&&!y.el&&(r=!0),u.includes("navigation")&&p.navigation&&(p.navigation.prevEl||m)&&(p.navigation.nextEl||h)&&(x.navigation||!1===x.navigation)&&w&&!w.prevEl&&!w.nextEl&&(a=!0);let T=e=>{d[e]&&(d[e].destroy(),"navigation"===e?(d.isElement&&(d[e].prevEl.remove(),d[e].nextEl.remove()),x[e].prevEl=void 0,x[e].nextEl=void 0,d[e].prevEl=void 0,d[e].nextEl=void 0):(d.isElement&&d[e].el.remove(),x[e].el=void 0,d[e].el=void 0))};u.includes("loop")&&d.isElement&&(x.loop&&!p.loop?l=!0:!x.loop&&p.loop?n=!0:o=!0),v.forEach(e=>{if(eu(x[e])&&eu(p[e]))Object.assign(x[e],p[e]),("navigation"===e||"pagination"===e||"scrollbar"===e)&&"enabled"in p[e]&&!p[e].enabled&&T(e);else{let t=p[e];(!0===t||!1===t)&&("navigation"===e||"pagination"===e||"scrollbar"===e)?!1===t&&T(e):x[e]=p[e]}}),v.includes("controller")&&!s&&d.controller&&d.controller.control&&x.controller&&x.controller.control&&(d.controller.control=x.controller.control),u.includes("children")&&c&&S&&x.virtual.enabled?(S.slides=c,S.update(!0)):u.includes("virtual")&&S&&x.virtual.enabled&&(c&&(S.slides=c),S.update(!0)),u.includes("children")&&c&&x.loop&&(o=!0),t&&E.init()&&E.update(!0),s&&(d.controller.control=x.controller.control),i&&(d.isElement&&(!g||"string"==typeof g)&&((g=document.createElement("div")).classList.add("swiper-pagination"),g.part.add("pagination"),d.el.appendChild(g)),g&&(x.pagination.el=g),b.init(),b.render(),b.update()),r&&(d.isElement&&(!f||"string"==typeof f)&&((f=document.createElement("div")).classList.add("swiper-scrollbar"),f.part.add("scrollbar"),d.el.appendChild(f)),f&&(x.scrollbar.el=f),y.init(),y.updateSize(),y.setTranslate()),a&&(d.isElement&&(h&&"string"!=typeof h||((h=document.createElement("div")).classList.add("swiper-button-next"),I(h,d.hostEl.constructor.nextButtonSvg),h.part.add("button-next"),d.el.appendChild(h)),m&&"string"!=typeof m||((m=document.createElement("div")).classList.add("swiper-button-prev"),I(m,d.hostEl.constructor.prevButtonSvg),m.part.add("button-prev"),d.el.appendChild(m))),h&&(x.navigation.nextEl=h),m&&(x.navigation.prevEl=m),w.init(),w.update()),u.includes("allowSlideNext")&&(d.allowSlideNext=p.allowSlideNext),u.includes("allowSlidePrev")&&(d.allowSlidePrev=p.allowSlidePrev),u.includes("direction")&&d.changeDirection(p.direction,!1),(l||o)&&d.loopDestroy(),(n||o)&&d.loopCreate(),d.update()}({swiper:b.current,slides:N,passedParams:j,changedParams:e,nextEl:S.current,prevEl:E.current,scrollbarEl:C.current,paginationEl:T.current}),()=>{z()}}),ey(()=>{ex(b.current)},[h]),p.createElement(r,eb({ref:x,className:ev(`${c}${i?` ${i}`:""}`)},k),p.createElement(eE.Provider,{value:b.current},L["container-start"],p.createElement(a,{className:(void 0===(s=P.wrapperClass)&&(s=""),s)?s.includes("swiper-wrapper")?s:`swiper-wrapper ${s}`:"swiper-wrapper"},L["wrapper-start"],P.virtual?function(e,t,s){if(!s)return null;let i=e=>{let s=e;return e<0?s=t.length+e:s>=t.length&&(s-=t.length),s},r=e.isHorizontal()?{[e.rtlTranslate?"right":"left"]:`${s.offset}px`}:{top:`${s.offset}px`},{from:a,to:l}=s,n=e.params.loop?-t.length:0,o=e.params.loop?2*t.length:t.length,d=[];for(let e=n;e<o;e+=1)e>=a&&e<=l&&d.push(t[i(e)]);return d.map((t,s)=>p.cloneElement(t,{swiper:e,style:r,key:t.props.virtualIndex||t.key||`slide-${s}`}))}(b.current,N,h):N.map((e,t)=>p.cloneElement(e,{swiper:b.current,swiperSlideIndex:t})),L["wrapper-end"]),em(P)&&p.createElement(p.Fragment,null,p.createElement("div",{ref:E,className:"swiper-button-prev"}),p.createElement("div",{ref:S,className:"swiper-button-next"})),eg(P)&&p.createElement("div",{ref:C,className:"swiper-scrollbar"}),ef(P)&&p.createElement("div",{ref:T,className:"swiper-pagination"}),L["container-end"]))});eT.displayName="Swiper";let eC=(0,p.forwardRef)(function(e,t){let{tag:s="div",children:i,className:r="",swiper:a,zoom:l,lazy:n,virtualIndex:o,swiperSlideIndex:d,...c}=void 0===e?{}:e,u=(0,p.useRef)(null),[h,m]=(0,p.useState)("swiper-slide"),[f,g]=(0,p.useState)(!1);function v(e,t,s){t===u.current&&m(s)}ey(()=>{if(void 0!==d&&(u.current.swiperSlideIndex=d),t&&(t.current=u.current),u.current&&a){if(a.destroyed){"swiper-slide"!==h&&m("swiper-slide");return}return a.on("_slideClass",v),()=>{a&&a.off("_slideClass",v)}}}),ey(()=>{a&&u.current&&!a.destroyed&&m(a.getSlideClasses(u.current))},[a]);let x={isActive:h.indexOf("swiper-slide-active")>=0,isVisible:h.indexOf("swiper-slide-visible")>=0,isPrev:h.indexOf("swiper-slide-prev")>=0,isNext:h.indexOf("swiper-slide-next")>=0},b=()=>"function"==typeof i?i(x):i;return p.createElement(s,eb({ref:u,className:ev(`${h}${r?` ${r}`:""}`),"data-swiper-slide-index":o,onLoad:()=>{g(!0)}},c),l&&p.createElement(eS.Provider,{value:x},p.createElement("div",{className:"swiper-zoom-container","data-swiper-zoom":"number"==typeof l?l:void 0},b(),n&&!f&&p.createElement("div",{className:"swiper-lazy-preloader"}))),!l&&p.createElement(eS.Provider,{value:x},b(),n&&!f&&p.createElement("div",{className:"swiper-lazy-preloader"})))});eC.displayName="SwiperSlide",s(71513),s(44234),s(7878),s(95836);let eP={STRAWBERRY:{name:"Fraise",emoji:"\uD83C\uDF53",color:"from-pink-400 to-red-500",bgColor:"bg-pink-50",borderColor:"border-pink-300",selectedBorder:"border-pink-500",textColor:"text-pink-700"},BLUEBERRY:{name:"Myrtille",emoji:"\uD83E\uDED0",color:"from-blue-400 to-purple-500",bgColor:"bg-blue-50",borderColor:"border-blue-300",selectedBorder:"border-blue-500",textColor:"text-blue-700"},APPLE:{name:"Pomme",emoji:"\uD83C\uDF4F",color:"from-green-400 to-emerald-500",bgColor:"bg-green-50",borderColor:"border-green-300",selectedBorder:"border-green-500",textColor:"text-green-700"}},ej=({variants:e,selectedVariant:t,onVariantSelect:s})=>{let i=(0,p.useRef)(null),r=e=>eP[e]||{name:function(e){return d[e]||e}(e),emoji:"\uD83C\uDF6D",color:"from-gray-400 to-gray-500",bgColor:"bg-gray-50",borderColor:"border-gray-300",selectedBorder:"border-gray-500",textColor:"text-gray-700"};return(0,l.jsxs)("div",{className:"space-y-6",children:[t&&(0,l.jsx)(c.P.div,{className:"text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,l.jsxs)("div",{className:"relative w-48 h-48 mx-auto rounded-2xl overflow-hidden shadow-xl",children:[(0,l.jsx)("img",{src:t.images?.[0]||"/img/placeholder.svg",alt:`D\xe9lices Deltagum saveur ${r(t.flavor).name}`,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,l.jsx)("div",{className:`absolute inset-0 bg-gradient-to-br ${r(t.flavor).color} opacity-20`}),(0,l.jsx)("div",{className:"absolute bottom-3 left-3 right-3",children:(0,l.jsx)("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg",children:(0,l.jsxs)("p",{className:"text-base font-bold text-gray-800 text-center",children:[r(t.flavor).emoji," ",r(t.flavor).name]})})})]})},t.id),(0,l.jsxs)("div",{className:"relative",children:[(0,l.jsx)("button",{onClick:()=>i.current?.slidePrev(),className:"flavor-nav-button prev","aria-label":"Saveur pr\xe9c\xe9dente",children:(0,l.jsx)(m,{size:20})}),(0,l.jsx)("button",{onClick:()=>i.current?.slideNext(),className:"flavor-nav-button next","aria-label":"Saveur suivante",children:(0,l.jsx)(f,{size:20})}),(0,l.jsx)(eT,{ref:i,modules:[D,G],spaceBetween:16,slidesPerView:1,centeredSlides:!0,pagination:{clickable:!0,bulletClass:"swiper-pagination-bullet !bg-pink-300",bulletActiveClass:"swiper-pagination-bullet-active !bg-pink-500"},breakpoints:{640:{slidesPerView:2,spaceBetween:20},768:{slidesPerView:3,spaceBetween:24}},className:"!pb-12",children:e.map(e=>{let i=r(e.flavor),a=t?.id===e.id,o=0===e.stock;return(0,l.jsx)(eC,{children:(0,l.jsx)(c.P.div,{whileHover:{scale:o?1:1.02},whileTap:{scale:o?1:.98},className:"h-full",children:(0,l.jsxs)("div",{className:`
                      flavor-card relative p-6 rounded-2xl border-2 cursor-pointer text-center h-full
                      ${a?`${i.selectedBorder} ${i.bgColor} shadow-lg selected`:`${i.borderColor} bg-white hover:${i.bgColor} shadow-md`}
                      ${o?"opacity-50 cursor-not-allowed":""}
                    `,onClick:()=>!o&&s(e),children:[a&&(0,l.jsx)(c.P.div,{className:"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:30},children:(0,l.jsx)("span",{className:"text-white text-xs",children:"✓"})}),o&&(0,l.jsx)("div",{className:"absolute -top-2 -left-2",children:(0,l.jsx)(n.Ex,{variant:"danger",size:"sm",children:"\xc9puis\xe9"})}),(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"text-5xl",children:i.emoji}),(0,l.jsx)("h5",{className:`font-bold text-xl ${i.textColor}`,children:i.name}),(0,l.jsx)("div",{className:"text-xl font-semibold text-gray-800",children:(0,u.$g)(Number(e.product?.price||0))}),(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,l.jsx)("div",{className:`w-2 h-2 rounded-full ${e.stock>10?"bg-green-500":e.stock>0?"bg-yellow-500":"bg-red-500"}`}),(0,l.jsx)("span",{className:"text-sm text-gray-500",children:e.stock>10?"En stock":e.stock>0?`${e.stock} restant${e.stock>1?"s":""}`:"\xc9puis\xe9"})]})]}),!o&&(0,l.jsx)(c.P.div,{className:`absolute inset-0 rounded-2xl bg-gradient-to-r ${i.color} opacity-0 pointer-events-none`,whileHover:{opacity:.1},transition:{duration:.2}})]})})},e.id)})})]}),0===e.length&&(0,l.jsxs)(c.P.div,{className:"text-center py-8",initial:{opacity:0},animate:{opacity:1},children:[(0,l.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF6D"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Aucune saveur disponible pour ce produit"})]})]})},ek={STRAWBERRY:{name:"Fraise",emoji:"\uD83C\uDF53",color:"from-pink-400 to-red-500",bgColor:"bg-pink-50",borderColor:"border-pink-300",selectedBorder:"border-pink-500",textColor:"text-pink-700"},BLUEBERRY:{name:"Myrtille",emoji:"\uD83E\uDED0",color:"from-blue-400 to-purple-500",bgColor:"bg-blue-50",borderColor:"border-blue-300",selectedBorder:"border-blue-500",textColor:"text-blue-700"},APPLE:{name:"Pomme",emoji:"\uD83C\uDF4F",color:"from-green-400 to-emerald-500",bgColor:"bg-green-50",borderColor:"border-green-300",selectedBorder:"border-green-500",textColor:"text-green-700"}},eM=({variants:e,selectedVariant:t,onVariantSelect:s})=>{let[i,r]=(0,p.useState)("desktop");(0,p.useEffect)(()=>{let e=()=>{let e=window.innerWidth;e<640?r("mobile"):e<1024?r("tablet"):r("desktop")};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let a=e=>ek[e]||{name:function(e){return d[e]||e}(e),emoji:"\uD83C\uDF6D",color:"from-gray-400 to-gray-500",bgColor:"bg-gray-50",borderColor:"border-gray-300",selectedBorder:"border-gray-500",textColor:"text-gray-700"};return"mobile"===i||"tablet"===i?(0,l.jsx)(ej,{variants:e,selectedVariant:t,onVariantSelect:s}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsx)(c.P.div,{className:"grid grid-cols-3 gap-4",variants:o.bK,initial:"initial",animate:"animate",children:e.map(e=>{let i=a(e.flavor),r=t?.id===e.id,d=0===e.stock;return(0,l.jsx)(c.P.div,{variants:o.Rf,whileHover:{scale:d?1:1.05},whileTap:{scale:d?1:.95},children:(0,l.jsxs)("div",{className:`
                  relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 text-center
                  ${r?`${i.selectedBorder} ${i.bgColor} shadow-lg`:`${i.borderColor} bg-white hover:${i.bgColor}`}
                  ${d?"opacity-50 cursor-not-allowed":""}
                `,onClick:()=>!d&&s(e),children:[r&&(0,l.jsx)(c.P.div,{className:"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:30},children:(0,l.jsx)("span",{className:"text-white text-xs",children:"✓"})}),d&&(0,l.jsx)("div",{className:"absolute -top-2 -left-2",children:(0,l.jsx)(n.Ex,{variant:"danger",size:"sm",children:"\xc9puis\xe9"})}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("h5",{className:`font-bold text-lg ${i.textColor}`,children:i.name}),(0,l.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,l.jsx)("div",{className:`w-2 h-2 rounded-full ${e.stock>10?"bg-green-500":e.stock>0?"bg-yellow-500":"bg-red-500"}`}),(0,l.jsx)("span",{className:"text-xs text-gray-500",children:e.stock>10?"En stock":e.stock>0?`${e.stock} restant${e.stock>1?"s":""}`:"\xc9puis\xe9"})]})]}),!d&&(0,l.jsx)(c.P.div,{className:`absolute inset-0 rounded-xl bg-gradient-to-r ${i.color} opacity-0 pointer-events-none`,whileHover:{opacity:.1},transition:{duration:.2}})]})},e.id)})}),0===e.length&&(0,l.jsxs)(c.P.div,{className:"text-center py-8",initial:{opacity:0},animate:{opacity:1},children:[(0,l.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF6D"}),(0,l.jsx)("p",{className:"text-gray-500",children:"Aucune saveur disponible pour ce produit"})]})]})};function eN({priceTiers:e,selectedQuantity:t,onQuantityChange:s,className:i=""}){let r=[...e].sort((e,t)=>e.quantity-t.quantity),a=r[0]?.price||0,o=e=>{let t=a*e.quantity,s=t-e.price,i=Math.round(s/t*100);return{savings:s,savingsPercent:i}};return(0,l.jsxs)("div",{className:`space-y-4 ${i}`,children:[(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Choisissez votre quantit\xe9"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Plus vous achetez, plus vous \xe9conomisez !"})]}),(0,l.jsx)("div",{className:"flex justify-center",children:(0,l.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-4 max-w-md",children:r.map(e=>{let{savings:i,savingsPercent:r}=o(e),a=t===e.quantity,d=6===e.quantity||5===e.quantity;return(0,l.jsxs)(c.P.div,{className:"relative",whileHover:{scale:1.02},whileTap:{scale:.98},children:[d&&(0,l.jsx)(n.Ex,{variant:"primary",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 z-10 text-xs",children:"Populaire"}),(0,l.jsxs)(n.$n,{variant:a?"primary":"outline",size:"lg",onClick:()=>s(e.quantity),className:`w-full h-32 p-4 flex flex-col items-center justify-center space-y-2 ${a?"ring-2 ring-pink-500 ring-offset-2":"hover:border-pink-300"}`,children:[(0,l.jsx)("div",{className:"text-2xl font-bold",children:e.quantity}),(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:`text-lg font-semibold ${a?"text-white":"text-gray-900"}`,children:(0,u.$g)(e.price)}),e.quantity>1&&(0,l.jsxs)("div",{className:`text-xs ${a?"text-pink-100":"text-gray-500"}`,children:[(0,u.$g)(e.price/e.quantity)," / unit\xe9"]})]}),i>0&&(0,l.jsxs)("div",{className:`text-xs font-medium ${a?"text-pink-100":"text-green-600"}`,children:["\xc9conomisez ",r,"%"]})]})]},e.id)})})}),(0,l.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,l.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Prix total"}),(0,l.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,u.$g)(r.find(e=>e.quantity===t)?.price||0)}),t>1&&(0,l.jsxs)("div",{className:"text-sm text-gray-500",children:["Soit"," ",(0,u.$g)((r.find(e=>e.quantity===t)?.price||0)/t)," ","par unit\xe9"]})]})]})}var eL=s(93666),eO=s(15580),eA=s(39628),e_=s(11325),ez=s.n(e_),eI=s(81040);function e$(){(0,eI.useParams)().id;let{products:e,fetchProducts:t}=(0,eL.useProduct)(),{addItem:s}=(0,eL.useCart)(),{addNotification:i}=(0,eL.useNotifications)(),[r,a]=(0,p.useState)(null),[d,h]=(0,p.useState)(null),[m,f]=(0,p.useState)(1),[g,v]=(0,p.useState)(0),x=(e,t)=>{if(!e.priceTiers||0===e.priceTiers.length)return Number(e.basePrice)*t;let s=e.priceTiers.find(e=>e.quantity===t);return s?Number(s.price):Number(e.basePrice)*t};return r?(0,l.jsx)("main",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-orange-50 pt-20",children:(0,l.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,l.jsx)(c.P.div,{className:"mb-8",initial:o.qG.initial,animate:o.qG.animate,children:(0,l.jsxs)(ez(),{href:"/",className:"inline-flex items-center text-gray-600 hover:text-pink-500 transition-colors",children:[(0,l.jsx)(eO.A,{className:"w-4 h-4 mr-2"}),"Retour \xe0 l'accueil"]})}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,l.jsx)(c.P.div,{className:"space-y-6",initial:{opacity:0,y:50},animate:{opacity:1,y:0},children:d&&d.images&&d.images.length>0?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsxs)("div",{className:"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl",children:[(0,l.jsx)("img",{src:d.images[g],alt:`${d.name} - Image ${g+1}`,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,l.jsxs)("div",{className:"absolute top-4 right-4 space-y-2",children:[(0,l.jsx)("div",{className:"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"\uD83C\uDF3F THC"}),(0,l.jsx)("div",{className:"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"18+"})]})]}),d.images.length>1&&(0,l.jsx)("div",{className:"flex justify-center space-x-3",children:d.images.map((e,t)=>(0,l.jsx)("button",{onClick:()=>v(t),className:`relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${g===t?"border-pink-500 ring-2 ring-pink-200":"border-gray-200 hover:border-gray-300"}`,children:(0,l.jsx)("img",{src:e,alt:`${d.name} - Miniature ${t+1}`,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},t))}),(0,l.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,l.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4",children:[(0,l.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Caract\xe9ristiques"]}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"THC"}),(0,l.jsx)("span",{className:"font-medium text-green-600",children:"< 0,3%"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Delta-9 THC"}),(0,l.jsx)("span",{className:"font-medium text-blue-600",children:"< 0,3%"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Origine"}),(0,l.jsx)("span",{className:"font-medium",children:"UE"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Certification"}),(0,l.jsx)("span",{className:"font-medium text-green-600",children:"Bio"})]})]})]}),(0,l.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-4",children:[(0,l.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-pink-500 rounded-full mr-2"}),"Avantages Deltagum"]}),(0,l.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Go\xfbt naturel et authentique"]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Dosage pr\xe9cis et constant"]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Texture fondante unique"]}),(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Emballage discret et pratique"]})]})]})]})]}):(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl",children:[(0,l.jsx)("img",{src:r.image||"/img/placeholder.svg",alt:r.name,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,l.jsxs)("div",{className:"absolute top-4 right-4 space-y-2",children:[(0,l.jsx)("div",{className:"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"\uD83C\uDF3F CBD"}),(0,l.jsx)("div",{className:"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"18+"})]})]}),(0,l.jsx)("div",{className:"space-y-4",children:(0,l.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4",children:[(0,l.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,l.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Caract\xe9ristiques"]}),(0,l.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"THC"}),(0,l.jsx)("span",{className:"font-medium text-green-600",children:"< 0,3%"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"CBD"}),(0,l.jsx)("span",{className:"font-medium text-blue-600",children:"Premium"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-600",children:"Origine"}),(0,l.jsx)("span",{className:"font-medium",children:"UE"})]})]})]})})]})}),(0,l.jsxs)(c.P.div,{className:"space-y-8",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:r.name}),(0,l.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:r.description}),r.dosage&&(0,l.jsxs)("div",{className:"mt-4 inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:["\uD83D\uDC8A ",r.dosage," par unit\xe9"]})]}),(0,l.jsx)("div",{className:"text-center lg:text-left",children:(0,l.jsx)("div",{className:"flex items-center justify-center lg:justify-start space-x-3",children:(0,l.jsx)("span",{className:"text-4xl font-bold text-pink-600",children:(0,u.$g)(x(r,m))})})}),r.variants&&r.variants.length>0&&(0,l.jsx)(eM,{variants:r.variants.map(e=>({...e,product:r})),selectedVariant:d,onVariantSelect:h}),r.priceTiers&&r.priceTiers.length>0&&(0,l.jsx)(eN,{priceTiers:r.priceTiers,selectedQuantity:m,onQuantityChange:e=>{f(e)},className:"mb-6"}),(0,l.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,l.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,l.jsx)("span",{className:"text-sm text-gray-600",children:"En stock"})]}),(0,l.jsxs)(n.$n,{onClick:()=>{r&&d&&(s({productId:r.id,variantId:d.id,quantity:m,name:r.name,price:x(r,m),image:r.image,flavor:d.flavor,color:d.color}),i({type:"success",title:"Produit ajout\xe9 !",message:`${m} ${r.name} ajout\xe9 au panier !`}),r.priceTiers&&r.priceTiers.length>0&&f([...r.priceTiers].sort((e,t)=>e.quantity-t.quantity)[0].quantity))},disabled:!d,size:"lg",className:"w-full text-lg py-4",children:[(0,l.jsx)(eA.A,{className:"w-5 h-5 mr-2"}),"Ajouter au panier -"," ",(0,u.$g)(x(r,m))]}),(0,l.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Informations importantes"}),(0,l.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,l.jsx)("li",{children:"• R\xe9serv\xe9 aux adultes (18+)"}),(0,l.jsx)("li",{children:"• Ne pas conduire apr\xe8s consommation"}),(0,l.jsx)("li",{children:"• Interdit sous traitement m\xe9dical"}),(0,l.jsx)("li",{children:"• THC < 0,3% (conforme UE)"})]})]})]})]})]})}):(0,l.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Chargement du produit..."})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},44234:()=>{},47274:(e,t,s)=>{Promise.resolve().then(s.bind(s,83787))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71513:()=>{},73515:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var i=s(67269);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81005:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>d});var i=s(87628),r=s(42355),a=s(87979),l=s.n(a),n=s(15140),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["products",{children:["[id]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,83787)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx"],p={require:s,loadChunk:()=>Promise.resolve()},u=new i.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/products/[id]/page",pathname:"/products/[id]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},81040:(e,t,s)=>{"use strict";var i=s(59076);s.o(i,"useParams")&&s.d(t,{useParams:function(){return i.useParams}}),s.o(i,"useRouter")&&s.d(t,{useRouter:function(){return i.useRouter}}),s.o(i,"useSearchParams")&&s.d(t,{useSearchParams:function(){return i.useSearchParams}})},83787:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});let i=(0,s(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\products\\\\[id]\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\products\\[id]\\page.tsx","default")},95836:()=>{}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),i=t.X(0,[7583,8500,7269,6964],()=>s(81005));module.exports=i})();