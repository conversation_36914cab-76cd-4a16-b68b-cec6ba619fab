(()=>{var e={};e.id=6636,e.ids=[6636],e.modules={2104:(e,s,t)=>{Promise.resolve().then(t.bind(t,50719))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},8292:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},24057:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},24961:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},35896:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},39690:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},50719:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>g});var a=t(166),r=t(23705),l=t(93666),n=t(39690),i=t(2926),c=t(24961),d=t(35896),o=t(65480),m=t(28805),x=t(24057),p=t(92865),h=t(91475);let u=(0,t(55050).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var j=t(81040),b=t(14791);function g(){(0,j.useRouter)();let{user:e,isAuthenticated:s,logout:t,isLoading:g,checkAuth:f}=(0,l.useAuth)(),[N,v]=(0,b.useState)(!1),[y,k]=(0,b.useState)([]),[w,A]=(0,b.useState)(!1),[C,P]=(0,b.useState)("profile"),[M,q]=(0,b.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",postalCode:"",city:""}),D=async()=>{try{let e=await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(M)});if(e.ok)alert("Profil mis \xe0 jour avec succ\xe8s !"),v(!1),await f();else{let s=await e.json();alert(s.error||"Erreur lors de la mise \xe0 jour")}}catch(e){alert("Erreur de connexion")}};return g||!e?(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"w-16 h-16 border-4 border-pink-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Chargement..."})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-8 mb-8",children:(0,a.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,a.jsxs)("div",{className:"w-20 h-20 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:[e.firstName?.charAt(0),e.lastName?.charAt(0)]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-black",children:[e.firstName," ",e.lastName]}),(0,a.jsxs)("p",{className:"text-black flex items-center mt-2",children:[(0,a.jsx)(n.A,{className:"w-4 h-4 mr-2"}),e.email]}),"ADMIN"===e.role&&(0,a.jsx)("div",{className:"mt-2 inline-block bg-gradient-to-r from-pink-500 to-orange-500 text-white text-xs px-2 py-1 rounded-full",children:"Administrateur"})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-8",children:(0,a.jsxs)("div",{className:"flex border-b",children:[(0,a.jsxs)("button",{onClick:()=>P("profile"),className:`flex-1 py-4 px-6 text-center font-medium transition-colors ${"profile"===C?"text-pink-600 border-b-2 border-pink-600 bg-pink-50":"text-gray-600 hover:text-pink-600"}`,children:[(0,a.jsx)(i.A,{className:"w-5 h-5 inline-block mr-2"}),"Mon Profil"]}),(0,a.jsxs)("button",{onClick:()=>P("orders"),className:`flex-1 py-4 px-6 text-center font-medium transition-colors ${"orders"===C?"text-pink-600 border-b-2 border-pink-600 bg-pink-50":"text-gray-600 hover:text-pink-600"}`,children:[(0,a.jsx)(c.A,{className:"w-5 h-5 inline-block mr-2"}),"Mes Commandes"]})]})}),"profile"===C&&(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-black",children:"Mes informations"}),!N&&(0,a.jsxs)(r.$n,{onClick:()=>v(!0),variant:"outline",className:"flex items-center space-x-2",children:[(0,a.jsx)(d.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Modifier"})]})]}),N?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Pr\xe9nom *"}),(0,a.jsx)(r.pd,{value:M.firstName,onChange:e=>q(s=>({...s,firstName:e.target.value})),placeholder:"Votre pr\xe9nom",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Nom *"}),(0,a.jsx)(r.pd,{value:M.lastName,onChange:e=>q(s=>({...s,lastName:e.target.value})),placeholder:"Votre nom",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Email *"}),(0,a.jsx)(r.pd,{type:"email",value:M.email,onChange:e=>q(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"T\xe9l\xe9phone"}),(0,a.jsx)(r.pd,{value:M.phone,onChange:e=>q(s=>({...s,phone:e.target.value})),placeholder:"06 12 34 56 78"})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Adresse"}),(0,a.jsx)(r.pd,{value:M.address,onChange:e=>q(s=>({...s,address:e.target.value})),placeholder:"123 Rue de la Paix"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Code postal"}),(0,a.jsx)(r.pd,{value:M.postalCode,onChange:e=>q(s=>({...s,postalCode:e.target.value})),placeholder:"75000"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Ville"}),(0,a.jsx)(r.pd,{value:M.city,onChange:e=>q(s=>({...s,city:e.target.value})),placeholder:"Paris"})]})]}),(0,a.jsxs)("div",{className:"flex space-x-4",children:[(0,a.jsxs)(r.$n,{onClick:D,className:"flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,a.jsx)(o.A,{className:"w-4 h-4"}),(0,a.jsx)("span",{children:"Sauvegarder"})]}),(0,a.jsxs)(r.$n,{onClick:()=>v(!1),variant:"outline",children:[(0,a.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(r.Zp,{children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"Pr\xe9nom"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:e.firstName||"Non renseign\xe9"})]})}),(0,a.jsx)(r.Zp,{children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(i.A,{className:"w-5 h-5 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"Nom"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:e.lastName||"Non renseign\xe9"})]})}),(0,a.jsx)(r.Zp,{children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(n.A,{className:"w-5 h-5 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"Email"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:e.email})]})}),(0,a.jsx)(r.Zp,{children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(x.A,{className:"w-5 h-5 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"T\xe9l\xe9phone"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:e.phone||"Non renseign\xe9"})]})}),(0,a.jsx)(r.Zp,{className:"md:col-span-2",children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,a.jsx)(p.A,{className:"w-5 h-5 text-gray-700"}),(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"Adresse"})]}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:e.address?(0,a.jsxs)(a.Fragment,{children:[e.address,", ",e.postalCode," ",e.city]}):"Non renseign\xe9e"})]})}),e.createdAt&&(0,a.jsx)(r.Zp,{className:"md:col-span-2",children:(0,a.jsxs)(r.Wu,{className:"p-4",children:[(0,a.jsx)("div",{className:"flex items-center space-x-3 mb-2",children:(0,a.jsx)("span",{className:"text-sm font-medium text-black",children:"Membre depuis"})}),(0,a.jsx)("p",{className:"text-lg font-semibold text-black",children:new Date(e.createdAt).toLocaleDateString("fr-FR",{day:"numeric",month:"long",year:"numeric"})})]})})]})]}),"orders"===C&&(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-black mb-6",children:"Mes Commandes"}),w?(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"w-8 h-8 border-4 border-pink-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,a.jsx)("p",{children:"Chargement des commandes..."})]}):0===y.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)(c.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Aucune commande"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas encore pass\xe9 de commande."})]}):(0,a.jsx)("div",{className:"space-y-6",children:y.map(e=>(0,a.jsx)(r.Zp,{className:"border border-gray-200",children:(0,a.jsxs)(r.Wu,{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-lg text-black",children:["Commande #",e.id.slice(-8)]}),(0,a.jsxs)("div",{className:"flex items-center text-gray-600 mt-1",children:[(0,a.jsx)(h.A,{className:"w-4 h-4 mr-2"}),new Date(e.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"flex items-center text-gray-600 mb-2",children:[(0,a.jsx)(u,{className:"w-4 h-4 mr-2"}),(Number(e.totalAmount)||0).toFixed(2),"€"]}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${"PAID"===e.status?"bg-green-100 text-green-800":"PENDING"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:"PAID"===e.status?"Pay\xe9e":"PENDING"===e.status?"En attente":e.status})]})]}),(0,a.jsxs)("div",{className:"border-t pt-4",children:[(0,a.jsx)("h4",{className:"font-medium text-black mb-3",children:"Articles command\xe9s :"}),(0,a.jsx)("div",{className:"space-y-2",children:e.items.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("img",{src:e.product.image,alt:e.product.name,className:"w-12 h-12 rounded-lg object-cover"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"font-medium text-black",children:e.product.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Saveur: ",e.variant.flavor," • Quantit\xe9:"," ",e.quantity]})]})]}),(0,a.jsxs)("p",{className:"font-medium text-black",children:[(Number(e.price)*e.quantity).toFixed(2),"€"]})]},e.id))})]})]})},e.id))})]})]})})})}},55317:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>o,routeModule:()=>x,tree:()=>d});var a=t(87628),r=t(42355),l=t(87979),n=t.n(l),i=t(15140),c={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>i[e]);t.d(s,c);let d={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,8292)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,o=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\profile\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65480:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},73515:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(67269);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79551:e=>{"use strict";e.exports=require("url")},81040:(e,s,t)=>{"use strict";var a=t(59076);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},91475:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},92376:(e,s,t)=>{Promise.resolve().then(t.bind(t,8292))},92865:(e,s,t)=>{"use strict";t.d(s,{A:()=>a});let a=(0,t(55050).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[7583,8500,7269,6964],()=>t(55317));module.exports=a})();