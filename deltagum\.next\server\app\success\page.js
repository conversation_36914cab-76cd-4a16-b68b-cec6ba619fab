(()=>{var e={};e.id=5582,e.ids=[5582],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},6398:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>a});let a=(0,t(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\success\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx","default")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},71122:(e,s,t)=>{Promise.resolve().then(t.bind(t,79456))},73515:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>r});var a=t(67269);let r=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,a.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},79456:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>m});var a=t(166),r=t(23705),i=t(93666),n=t(66212),l=t(11325),o=t.n(l),c=t(81040),d=t(14791);function m(){(0,c.useRouter)(),(0,c.useSearchParams)().get("session_id");let{clearCart:e}=(0,i.useCart)(),[s,t]=(0,d.useState)(null),[l,m]=(0,d.useState)(!0),[u,x]=(0,d.useState)(null);return l?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(n.P.div,{className:"text-6xl mb-4",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:"\uD83C\uDF6D"}),(0,a.jsx)("p",{className:"text-lg text-gray-600",children:"V\xe9rification de votre commande..."})]})}):u?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,a.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Erreur"}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:u}),(0,a.jsx)(o(),{href:"/",children:(0,a.jsx)(r.$n,{variant:"primary",children:"Retour \xe0 l'accueil"})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,a.jsxs)(n.P.div,{className:"max-w-2xl mx-auto text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,a.jsx)(n.P.div,{className:"text-8xl mb-6",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:260,damping:20,delay:.2},children:"✅"}),(0,a.jsx)(n.P.h1,{className:"text-4xl font-bold text-gray-800 mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:"Paiement r\xe9ussi !"}),(0,a.jsx)(n.P.p,{className:"text-xl text-gray-600 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:"Merci pour votre commande ! Vos d\xe9licieux bonbons Deltagum seront bient\xf4t en route."}),s&&(0,a.jsxs)(n.P.div,{className:"bg-white rounded-lg shadow-lg p-6 mb-8 text-left",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:[(0,a.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:["Commande #",s.id.slice(-8)]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Client"}),(0,a.jsxs)("p",{className:"text-gray-600",children:[s.customer.firstName," ",s.customer.lastName]}),(0,a.jsx)("p",{className:"text-gray-600",children:s.customer.email})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Statut"}),(0,a.jsx)("span",{className:"inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:"PAID"===s.status?"Pay\xe9":s.status})]})]}),(0,a.jsxs)("div",{className:"mt-6",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-700 mb-3",children:"Articles command\xe9s"}),(0,a.jsxs)("div",{className:"space-y-2",children:[s.items.map((e,s)=>(0,a.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,a.jsxs)("span",{className:"text-gray-600",children:[e.productName," - ",e.variantFlavor," x",e.quantity]}),(0,a.jsxs)("span",{className:"font-medium",children:[(e.price*e.quantity).toFixed(2),"€"]})]},s)),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-3 font-bold text-lg",children:[(0,a.jsx)("span",{children:"Total"}),(0,a.jsxs)("span",{children:[Number(s.totalAmount).toFixed(2),"€"]})]})]})]})]}),(0,a.jsxs)(n.P.div,{className:"bg-blue-50 rounded-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},children:[(0,a.jsx)("h3",{className:"text-xl font-bold text-blue-800 mb-4",children:"Que se passe-t-il maintenant ?"}),(0,a.jsxs)("div",{className:"space-y-3 text-left",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCE7"}),(0,a.jsx)("span",{className:"text-blue-700",children:"Vous recevrez un email de confirmation sous peu"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCE6"}),(0,a.jsx)("span",{className:"text-blue-700",children:"Votre commande sera pr\xe9par\xe9e dans les 24h"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDE9A"}),(0,a.jsx)("span",{className:"text-blue-700",children:"Livraison sous 3-5 jours ouvr\xe9s"})]})]})]}),(0,a.jsxs)(n.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2},children:[(0,a.jsx)(o(),{href:"/",children:(0,a.jsx)(r.$n,{variant:"primary",size:"lg",children:"Continuer mes achats"})}),(0,a.jsx)(o(),{href:"/profile",children:(0,a.jsx)(r.$n,{variant:"outline",size:"lg",children:"Voir mes commandes"})})]})]})})})}},79551:e=>{"use strict";e.exports=require("url")},81040:(e,s,t)=>{"use strict";var a=t(59076);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},85693:(e,s,t)=>{Promise.resolve().then(t.bind(t,6398))},92473:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(87628),r=t(42355),i=t(87979),n=t.n(i),l=t(15140),o={};for(let e in l)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);t.d(s,o);let c={children:["",{children:["success",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,6398)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\success\\page.tsx"],m={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.RouteKind.APP_PAGE,page:"/success/page",pathname:"/success",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var s=require("../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[7583,8500,7269,6964],()=>t(92473));module.exports=a})();