(()=>{var e={};e.id=2580,e.ids=[2580],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},39302:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o});var r=s(166),a=s(23705),n=s(14791);function o(){let[e,t]=(0,n.useState)(null),[s,o]=(0,n.useState)(!1),i=async()=>{o(!0);try{let e=await fetch("/api/auth/me",{credentials:"include"}),s=await e.json();t({status:e.status,ok:e.ok,data:s})}catch(e){t({error:e instanceof Error?e.message:String(e)})}finally{o(!1)}},l=async()=>{o(!0);try{let e=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:"<EMAIL>",password:"admin123"})}),s=await e.json();t({status:e.status,ok:e.ok,data:s})}catch(e){t({error:e instanceof Error?e.message:String(e)})}finally{o(!1)}};return(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 py-12",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-8",children:"Test d'Authentification"}),(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow p-6 space-y-4",children:[(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)(a.$n,{onClick:l,disabled:s,children:"Test Login Admin"}),(0,r.jsx)(a.$n,{onClick:i,disabled:s,children:"Test API /auth/me"}),(0,r.jsx)(a.$n,{onClick:()=>{t({cookies:document.cookie,localStorage:localStorage.getItem("auth-storage")})},disabled:s,children:"V\xe9rifier Cookies"})]}),s&&(0,r.jsx)("div",{className:"text-center py-4",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500 mx-auto"})}),e&&(0,r.jsxs)("div",{className:"mt-6",children:[(0,r.jsx)("h3",{className:"text-lg font-semibold mb-2",children:"R\xe9sultat :"}),(0,r.jsx)("pre",{className:"bg-gray-100 p-4 rounded-lg overflow-auto text-sm",children:JSON.stringify(e,null,2)})]})]}),(0,r.jsx)("div",{className:"mt-8",children:(0,r.jsx)("a",{href:"/admin/dashboard",className:"text-pink-600 hover:text-pink-700 underline",children:"→ Aller au Dashboard Admin"})})]})})}},50708:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});let r=(0,s(54560).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\Deltagum\\\\deltagum\\\\src\\\\app\\\\test-auth\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx","default")},54229:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>p,tree:()=>d});var r=s(87628),a=s(42355),n=s(87979),o=s.n(n),i=s(15140),l={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);s.d(t,l);let d={children:["",{children:["test-auth",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,50708)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,86684)),"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,16857,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,93620,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,36501,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,73515))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\app\\test-auth\\page.tsx"],u={require:s,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/test-auth/page",pathname:"/test-auth",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},62440:(e,t,s)=>{Promise.resolve().then(s.bind(s,39302))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73515:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(67269);let a=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},75592:(e,t,s)=>{Promise.resolve().then(s.bind(s,50708))},79551:e=>{"use strict";e.exports=require("url")}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[7583,8500,7269,6964],()=>s(54229));module.exports=r})();