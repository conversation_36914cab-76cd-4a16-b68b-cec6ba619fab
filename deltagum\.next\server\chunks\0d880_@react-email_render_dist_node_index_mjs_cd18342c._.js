module.exports = {

"[project]/node_modules/.pnpm/@react-email+render@1.1.2_r_dc87bafdebf72a670d0126d1d290625a/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/68053_next_dist_compiled_react-dom_server_b428f96d.js",
  "server/chunks/node_modules__pnpm_852a30e3._.js",
  "server/chunks/[externals]_node:stream_b8071f2c._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/@react-email+render@1.1.2_r_dc87bafdebf72a670d0126d1d290625a/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript)");
    });
});
}}),

};