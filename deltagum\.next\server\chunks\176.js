exports.id=176,exports.ids=[176],exports.modules={2560:()=>{},34702:(e,t,r)=>{"use strict";r.d(t,{o6:()=>I,k9:()=>k,v4:()=>w});var i=Object.defineProperty,n=Object.defineProperties,s=Object.getOwnPropertyDescriptors,a=Object.getOwnPropertySymbols,o=Object.prototype.hasOwnProperty,c=Object.prototype.propertyIsEnumerable,d=(e,t,r)=>t in e?i(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,l=(e,t)=>{for(var r in t||(t={}))o.call(t,r)&&d(e,r,t[r]);if(a)for(var r of a(t))c.call(t,r)&&d(e,r,t[r]);return e},u=(e,t)=>n(e,s(t)),m=(e,t,r)=>new Promise((i,n)=>{var s=e=>{try{o(r.next(e))}catch(e){n(e)}},a=e=>{try{o(r.throw(e))}catch(e){n(e)}},o=e=>e.done?i(e.value):Promise.resolve(e.value).then(s,a);o((r=r.apply(e,t)).next())}),h=class{constructor(e){this.resend=e}create(e){return m(this,arguments,function*(e,t={}){return yield this.resend.post("/api-keys",e,t)})}list(){return m(this,null,function*(){return yield this.resend.get("/api-keys")})}remove(e){return m(this,null,function*(){return yield this.resend.delete(`/api-keys/${e}`)})}},p=class{constructor(e){this.resend=e}create(e){return m(this,arguments,function*(e,t={}){return yield this.resend.post("/audiences",e,t)})}list(){return m(this,null,function*(){return yield this.resend.get("/audiences")})}get(e){return m(this,null,function*(){return yield this.resend.get(`/audiences/${e}`)})}remove(e){return m(this,null,function*(){return yield this.resend.delete(`/audiences/${e}`)})}};function f(e){return{attachments:e.attachments,bcc:e.bcc,cc:e.cc,from:e.from,headers:e.headers,html:e.html,reply_to:e.replyTo,scheduled_at:e.scheduledAt,subject:e.subject,tags:e.tags,text:e.text,to:e.to}}var y=class{constructor(e){this.resend=e}send(e){return m(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return m(this,arguments,function*(e,t={}){let i=[];for(let t of e){if(t.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(2421).then(r.bind(r,72421));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}t.html=yield this.renderAsync(t.react),t.react=void 0}i.push(f(t))}return yield this.resend.post("/emails/batch",i,t)})}},g=class{constructor(e){this.resend=e}create(e){return m(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(2421).then(r.bind(r,72421));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/broadcasts",{name:e.name,audience_id:e.audienceId,preview_text:e.previewText,from:e.from,html:e.html,reply_to:e.replyTo,subject:e.subject,text:e.text},t)})}send(e,t){return m(this,null,function*(){return yield this.resend.post(`/broadcasts/${e}/send`,{scheduled_at:null==t?void 0:t.scheduledAt})})}list(){return m(this,null,function*(){return yield this.resend.get("/broadcasts")})}get(e){return m(this,null,function*(){return yield this.resend.get(`/broadcasts/${e}`)})}remove(e){return m(this,null,function*(){return yield this.resend.delete(`/broadcasts/${e}`)})}update(e,t){return m(this,null,function*(){return yield this.resend.patch(`/broadcasts/${e}`,{name:t.name,audience_id:t.audienceId,from:t.from,html:t.html,text:t.text,subject:t.subject,reply_to:t.replyTo,preview_text:t.previewText})})}},v=class{constructor(e){this.resend=e}create(e){return m(this,arguments,function*(e,t={}){return yield this.resend.post(`/audiences/${e.audienceId}/contacts`,{unsubscribed:e.unsubscribed,email:e.email,first_name:e.firstName,last_name:e.lastName},t)})}list(e){return m(this,null,function*(){return yield this.resend.get(`/audiences/${e.audienceId}/contacts`)})}get(e){return m(this,null,function*(){return e.id||e.email?yield this.resend.get(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}update(e){return m(this,null,function*(){return e.id||e.email?yield this.resend.patch(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`,{unsubscribed:e.unsubscribed,first_name:e.firstName,last_name:e.lastName}):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}remove(e){return m(this,null,function*(){return e.id||e.email?yield this.resend.delete(`/audiences/${e.audienceId}/contacts/${(null==e?void 0:e.email)?null==e?void 0:e.email:null==e?void 0:e.id}`):{data:null,error:{message:"Missing `id` or `email` field.",name:"missing_required_field"}}})}},j=class{constructor(e){this.resend=e}create(e){return m(this,arguments,function*(e,t={}){return yield this.resend.post("/domains",{name:e.name,region:e.region,custom_return_path:e.customReturnPath},t)})}list(){return m(this,null,function*(){return yield this.resend.get("/domains")})}get(e){return m(this,null,function*(){return yield this.resend.get(`/domains/${e}`)})}update(e){return m(this,null,function*(){return yield this.resend.patch(`/domains/${e.id}`,{click_tracking:e.clickTracking,open_tracking:e.openTracking,tls:e.tls})})}remove(e){return m(this,null,function*(){return yield this.resend.delete(`/domains/${e}`)})}verify(e){return m(this,null,function*(){return yield this.resend.post(`/domains/${e}/verify`)})}},Y=class{constructor(e){this.resend=e}send(e){return m(this,arguments,function*(e,t={}){return this.create(e,t)})}create(e){return m(this,arguments,function*(e,t={}){if(e.react){if(!this.renderAsync)try{let{renderAsync:e}=yield r.e(2421).then(r.bind(r,72421));this.renderAsync=e}catch(e){throw Error("Failed to render React component. Make sure to install `@react-email/render`")}e.html=yield this.renderAsync(e.react)}return yield this.resend.post("/emails",f(e),t)})}get(e){return m(this,null,function*(){return yield this.resend.get(`/emails/${e}`)})}update(e){return m(this,null,function*(){return yield this.resend.patch(`/emails/${e.id}`,{scheduled_at:e.scheduledAt})})}cancel(e){return m(this,null,function*(){return yield this.resend.post(`/emails/${e}/cancel`)})}},b="undefined"!=typeof process&&process.env&&process.env.RESEND_BASE_URL||"https://api.resend.com",E="undefined"!=typeof process&&process.env&&process.env.RESEND_USER_AGENT||"resend-node:4.6.0",x=class{constructor(e){if(this.key=e,this.apiKeys=new h(this),this.audiences=new p(this),this.batch=new y(this),this.broadcasts=new g(this),this.contacts=new v(this),this.domains=new j(this),this.emails=new Y(this),!e&&("undefined"!=typeof process&&process.env&&(this.key=process.env.RESEND_API_KEY),!this.key))throw Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');this.headers=new Headers({Authorization:`Bearer ${this.key}`,"User-Agent":E,"Content-Type":"application/json"})}fetchRequest(e){return m(this,arguments,function*(e,t={}){try{let r=yield fetch(`${b}${e}`,t);if(!r.ok)try{let e=yield r.text();return{data:null,error:JSON.parse(e)}}catch(t){if(t instanceof SyntaxError)return{data:null,error:{name:"application_error",message:"Internal server error. We are unable to process your request right now, please try again later."}};let e={message:r.statusText,name:"application_error"};if(t instanceof Error)return{data:null,error:u(l({},e),{message:t.message})};return{data:null,error:e}}return{data:yield r.json(),error:null}}catch(e){return{data:null,error:{name:"application_error",message:"Unable to fetch data. The request could not be resolved."}}}})}post(e,t){return m(this,arguments,function*(e,t,r={}){let i=new Headers(this.headers);r.idempotencyKey&&i.set("Idempotency-Key",r.idempotencyKey);let n=l({method:"POST",headers:i,body:JSON.stringify(t)},r);return this.fetchRequest(e,n)})}get(e){return m(this,arguments,function*(e,t={}){let r=l({method:"GET",headers:this.headers},t);return this.fetchRequest(e,r)})}put(e,t){return m(this,arguments,function*(e,t,r={}){let i=l({method:"PUT",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,i)})}patch(e,t){return m(this,arguments,function*(e,t,r={}){let i=l({method:"PATCH",headers:this.headers,body:JSON.stringify(t)},r);return this.fetchRequest(e,i)})}delete(e,t){return m(this,null,function*(){let r={method:"DELETE",headers:this.headers,body:JSON.stringify(t)};return this.fetchRequest(e,r)})}};if(!process.env.RESEND_API_KEY)throw Error("RESEND_API_KEY is not defined in environment variables");let I=new x(process.env.RESEND_API_KEY),k=async(e,t)=>{try{let{data:r,error:i}=await I.emails.send({from:"Deltagum <<EMAIL>>",to:[e],subject:`Confirmation de commande #${t.orderId}`,html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #FF6B9D;">Merci pour votre commande !</h1>
          <p>Bonjour ${t.customerName},</p>
          <p>Votre commande #${t.orderId} a \xe9t\xe9 confirm\xe9e.</p>
          
          <h2>D\xe9tails de la commande :</h2>
          <ul>
            ${t.items.map(e=>`
              <li>${e.name} - ${e.flavor} x${e.quantity} - ${e.price.toFixed(2)}€</li>
            `).join("")}
          </ul>
          
          <p><strong>Total : ${t.totalAmount.toFixed(2)}€</strong></p>
          
          <p>Votre commande sera exp\xe9di\xe9e sous 24-48h.</p>
          
          <p>Merci de votre confiance !</p>
          <p>L'\xe9quipe Deltagum</p>
        </div>
      `});if(i)return console.error("Error sending email:",i),{success:!1,error:i};return{success:!0,data:r}}catch(e){return console.error("Error sending email:",e),{success:!1,error:e}}},w=async(e,t)=>{try{let{data:r,error:i}=await I.emails.send({from:"Deltagum <<EMAIL>>",to:[e],subject:"Bienvenue chez Deltagum !",html:`
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #FF6B9D;">Bienvenue chez Deltagum !</h1>
          <p>Bonjour ${t},</p>
          <p>Merci de vous \xeatre inscrit chez Deltagum !</p>
          <p>D\xe9couvrez nos d\xe9licieux chewing-gums aux saveurs naturelles de fruits.</p>
          <p>Votre programme de fid\xe9lit\xe9 a \xe9t\xe9 activ\xe9 avec 50 points de bienvenue !</p>
          <p>\xc0 bient\xf4t,</p>
          <p>L'\xe9quipe Deltagum</p>
        </div>
      `});if(i)return console.error("Error sending welcome email:",i),{success:!1,error:i};return{success:!0,data:r}}catch(e){return console.error("Error sending welcome email:",e),{success:!1,error:e}}}},89536:()=>{},89909:(e,t,r)=>{"use strict";r.d(t,{HU:()=>c,L1:()=>h,ib:()=>f,ie:()=>m,k:()=>d,yo:()=>p,yz:()=>a});var i=r(61412);let n=i.k5(["STRAWBERRY","BLUEBERRY","APPLE"]),s=i.k5(["PENDING","PAID","SHIPPED","DELIVERED","CANCELLED"]);i.k5(["BRONZE","SILVER","GOLD","PLATINUM"]);let a=i.Ik({id:i.Yj().optional(),email:i.Yj().email("Email invalide"),password:i.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res").optional(),firstName:i.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:i.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),phone:i.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide").optional(),address:i.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res").optional(),postalCode:i.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)").optional(),city:i.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res").optional()}),o=i.Ik({firstName:i.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:i.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:i.Yj().email("Email invalide").optional(),street:i.Yj().min(5,"L'adresse doit contenir au moins 5 caract\xe8res"),city:i.Yj().min(2,"La ville doit contenir au moins 2 caract\xe8res"),postalCode:i.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:i.Yj().min(2,"Pays requis"),phone:i.Yj().optional()}),c=i.Ik({id:i.Yj().optional(),name:i.Yj().min(2,"Le nom du produit doit contenir au moins 2 caract\xe8res"),description:i.Yj().min(10,"La description doit contenir au moins 10 caract\xe8res"),basePrice:i.ai().positive("Le prix doit \xeatre positif"),image:i.Yj().min(1,"Une image est requise").refine(e=>e.startsWith("http")||e.startsWith("/")||e.startsWith("./")||e.includes("/uploads/")||e.includes("/img/"),"URL d'image invalide"),active:i.zM().default(!0),dosage:i.Yj().optional(),variants:i.YO(i.bz()).optional(),pricingTiers:i.YO(i.bz()).optional()}),d=i.Ik({id:i.Yj().optional(),productId:i.Yj(),flavor:n,color:i.Yj().regex(/^#[0-9A-F]{6}$/i,"Couleur hexad\xe9cimale invalide"),stock:i.ai().int().min(0,"Le stock ne peut pas \xeatre n\xe9gatif"),sku:i.Yj().min(3,"Le SKU doit contenir au moins 3 caract\xe8res").optional(),images:i.YO(i.Yj().url()).default(["/img/placeholder.svg"])}),l=i.Ik({id:i.Yj().optional(),productId:i.Yj(),variantId:i.Yj(),name:i.Yj(),flavor:n,color:i.Yj(),price:i.ai().positive(),quantity:i.ai().int().positive("La quantit\xe9 doit \xeatre positive"),image:i.Yj().url()});i.Ik({productId:i.Yj(),variantId:i.Yj(),quantity:i.ai().int().positive().max(10,"Maximum 10 articles par produit")});let u=i.Ik({productId:i.Yj(),variantId:i.Yj(),quantity:i.ai().int().positive()}),m=i.Ik({customerId:i.Yj().optional(),items:i.YO(u).min(1,"Au moins un article requis"),shippingAddress:o,totalAmount:i.ai().positive().optional()}),h=i.Ik({orderId:i.Yj(),status:s});i.Ik({orderId:i.Yj(),amount:i.ai().positive(),currency:i.Yj().length(3).default("EUR")}),i.Ik({type:i.Yj(),data:i.Ik({object:i.bz()})}),i.Ik({email:i.Yj().email("Email invalide"),password:i.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res")}),i.Ik({email:i.Yj().email("Email invalide"),password:i.Yj().min(6,"Le mot de passe doit contenir au moins 6 caract\xe8res"),confirmPassword:i.Yj(),firstName:i.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:i.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),acceptTerms:i.zM().refine(e=>!0===e,"Vous devez accepter les conditions")}).refine(e=>e.password===e.confirmPassword,{message:"Les mots de passe ne correspondent pas",path:["confirmPassword"]});let p=i.Ik({name:i.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:i.Yj().email("Email invalide"),subject:i.Yj().min(5,"Le sujet doit contenir au moins 5 caract\xe8res"),message:i.Yj().min(10,"Le message doit contenir au moins 10 caract\xe8res")});i.Ik({email:i.Yj().email("Email invalide")}),i.Ik({productId:i.Yj(),customerId:i.Yj(),rating:i.ai().int().min(1).max(5),title:i.Yj().min(5,"Le titre doit contenir au moins 5 caract\xe8res"),comment:i.Yj().min(10,"Le commentaire doit contenir au moins 10 caract\xe8res")}),i.Ik({emailNotifications:i.zM().default(!0),smsNotifications:i.zM().default(!1),marketingEmails:i.zM().default(!0),language:i.k5(["fr","en"]).default("fr"),currency:i.k5(["EUR","USD"]).default("EUR")});let f=i.Ik({customer:a,shippingAddress:o,paymentMethod:i.k5(["card","paypal","apple_pay","google_pay"]),items:i.YO(l).min(1,"Au moins un article requis"),promoCode:i.Yj().optional(),acceptTerms:i.zM().refine(e=>!0===e,"Vous devez accepter les conditions")})}};