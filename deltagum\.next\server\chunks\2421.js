"use strict";exports.id=2421,exports.ids=[2421],exports.modules={72421:(e,t,r)=>{r.r(t),r.d(t,{plainTextSelectors:()=>cv,pretty:()=>ck,render:()=>cF,renderAsync:()=>c_});var n,i={};r.r(i),r.d(i,{default:()=>sU,languages:()=>af,options:()=>aD,parsers:()=>ay,printers:()=>sV}),function(e){e.Root="root",e.Text="text",e.Directive="directive",e.Comment="comment",e.Script="script",e.Style="style",e.Tag="tag",e.CDATA="cdata",e.Doctype="doctype"}(r3||(r3={}));let a=r3.Root,s=r3.Text,o=r3.Directive,l=r3.Comment,u=r3.Script,c=r3.Style,h=r3.Tag,d=r3.CDATA,p=r3.Doctype;class f{constructor(){this.parent=null,this.prev=null,this.next=null,this.startIndex=null,this.endIndex=null}get parentNode(){return this.parent}set parentNode(e){this.parent=e}get previousSibling(){return this.prev}set previousSibling(e){this.prev=e}get nextSibling(){return this.next}set nextSibling(e){this.next=e}cloneNode(e=!1){return E(this,e)}}class m extends f{constructor(e){super(),this.data=e}get nodeValue(){return this.data}set nodeValue(e){this.data=e}}class g extends m{constructor(){super(...arguments),this.type=r3.Text}get nodeType(){return 3}}class D extends m{constructor(){super(...arguments),this.type=r3.Comment}get nodeType(){return 8}}class y extends m{constructor(e,t){super(t),this.name=e,this.type=r3.Directive}get nodeType(){return 1}}class b extends f{constructor(e){super(),this.children=e}get firstChild(){var e;return null!=(e=this.children[0])?e:null}get lastChild(){return this.children.length>0?this.children[this.children.length-1]:null}get childNodes(){return this.children}set childNodes(e){this.children=e}}class C extends b{constructor(){super(...arguments),this.type=r3.CDATA}get nodeType(){return 4}}class v extends b{constructor(){super(...arguments),this.type=r3.Root}get nodeType(){return 9}}class S extends b{constructor(e,t,r=[],n="script"===e?r3.Script:"style"===e?r3.Style:r3.Tag){super(r),this.name=e,this.attribs=t,this.type=n}get nodeType(){return 1}get tagName(){return this.name}set tagName(e){this.name=e}get attributes(){return Object.keys(this.attribs).map(e=>{var t,r;return{name:e,value:this.attribs[e],namespace:null==(t=this["x-attribsNamespace"])?void 0:t[e],prefix:null==(r=this["x-attribsPrefix"])?void 0:r[e]}})}}function w(e){return e.type===r3.Tag||e.type===r3.Script||e.type===r3.Style}function k(e){return e.type===r3.Text}function E(e,t=!1){let r;if(k(e))r=new g(e.data);else if(e.type===r3.Comment)r=new D(e.data);else if(w(e)){let n=t?x(e.children):[],i=new S(e.name,{...e.attribs},n);n.forEach(e=>e.parent=i),null!=e.namespace&&(i.namespace=e.namespace),e["x-attribsNamespace"]&&(i["x-attribsNamespace"]={...e["x-attribsNamespace"]}),e["x-attribsPrefix"]&&(i["x-attribsPrefix"]={...e["x-attribsPrefix"]}),r=i}else if(e.type===r3.CDATA){let n=t?x(e.children):[],i=new C(n);n.forEach(e=>e.parent=i),r=i}else if(e.type===r3.Root){let n=t?x(e.children):[],i=new v(n);n.forEach(e=>e.parent=i),e["x-mode"]&&(i["x-mode"]=e["x-mode"]),r=i}else if(e.type===r3.Directive){let t=new y(e.name,e.data);null!=e["x-name"]&&(t["x-name"]=e["x-name"],t["x-publicId"]=e["x-publicId"],t["x-systemId"]=e["x-systemId"]),r=t}else throw Error(`Not implemented yet: ${e.type}`);return r.startIndex=e.startIndex,r.endIndex=e.endIndex,null!=e.sourceCodeLocation&&(r.sourceCodeLocation=e.sourceCodeLocation),r}function x(e){let t=e.map(e=>E(e,!0));for(let e=1;e<t.length;e++)t[e].prev=t[e-1],t[e-1].next=t[e];return t}let F={withStartIndices:!1,withEndIndices:!1,xmlMode:!1};class _{constructor(e,t,r){this.dom=[],this.root=new v(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null,"function"==typeof t&&(r=t,t=F),"object"==typeof e&&(t=e,e=void 0),this.callback=null!=e?e:null,this.options=null!=t?t:F,this.elementCB=null!=r?r:null}onparserinit(e){this.parser=e}onreset(){this.dom=[],this.root=new v(this.dom),this.done=!1,this.tagStack=[this.root],this.lastNode=null,this.parser=null}onend(){this.done||(this.done=!0,this.parser=null,this.handleCallback(null))}onerror(e){this.handleCallback(e)}onclosetag(){this.lastNode=null;let e=this.tagStack.pop();this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),this.elementCB&&this.elementCB(e)}onopentag(e,t){let r=new S(e,t,void 0,this.options.xmlMode?r3.Tag:void 0);this.addNode(r),this.tagStack.push(r)}ontext(e){let{lastNode:t}=this;if(t&&t.type===r3.Text)t.data+=e,this.options.withEndIndices&&(t.endIndex=this.parser.endIndex);else{let t=new g(e);this.addNode(t),this.lastNode=t}}oncomment(e){if(this.lastNode&&this.lastNode.type===r3.Comment){this.lastNode.data+=e;return}let t=new D(e);this.addNode(t),this.lastNode=t}oncommentend(){this.lastNode=null}oncdatastart(){let e=new g(""),t=new C([e]);this.addNode(t),e.parent=t,this.lastNode=e}oncdataend(){this.lastNode=null}onprocessinginstruction(e,t){let r=new y(e,t);this.addNode(r)}handleCallback(e){if("function"==typeof this.callback)this.callback(e,this.dom);else if(e)throw e}addNode(e){let t=this.tagStack[this.tagStack.length-1],r=t.children[t.children.length-1];this.options.withStartIndices&&(e.startIndex=this.parser.startIndex),this.options.withEndIndices&&(e.endIndex=this.parser.endIndex),t.children.push(e),r&&(e.prev=r,r.next=e),e.parent=t,this.lastNode=null}}let T=/\n/g;function A(e,t="",r={}){let n="string"==typeof t?t:"",i=e.map(L),a=!!("string"!=typeof t?t:r).lineNumbers;return function(e,t=0){let r=a?function(e){let t=[...e.matchAll(T)].map(e=>e.index||0);t.unshift(-1);let r=function e(t,r,n){if(n-r==1)return{offset:t[r],index:r+1};let i=Math.ceil((r+n)/2),a=e(t,r,i),s=e(t,i,n);return{offset:a.offset,low:a,high:s}}(t,0,t.length);return e=>(function e(t,r){return Object.prototype.hasOwnProperty.call(t,"index")?{line:t.index,column:r-t.offset}:e(t.high.offset<r?t.high:t.low,r)})(r,e)}(e):()=>({line:0,column:0}),s=t,o=[];e:for(;s<e.length;){let t=!1;for(let a of i){a.regex.lastIndex=s;let i=a.regex.exec(e);if(i&&i[0].length>0){if(!a.discard){let e=r(s),t="string"==typeof a.replace?i[0].replace(new RegExp(a.regex.source,a.regex.flags),a.replace):i[0];o.push({state:n,name:a.name,text:t,offset:s,len:i[0].length,line:e.line,column:e.column})}if(s=a.regex.lastIndex,t=!0,a.push){let t=a.push(e,s);o.push(...t.tokens),s=t.offset}if(a.pop)break e;break}}if(!t)break}return{tokens:o,offset:s,complete:e.length<=s}}}function L(e,t){return{...e,regex:function(e,t){if(0===e.name.length)throw Error(`Rule #${t} has empty name, which is not allowed.`);if(Object.prototype.hasOwnProperty.call(e,"regex")){var r=e.regex;if(r.global)throw Error(`Regular expression /${r.source}/${r.flags} contains the global flag, which is not allowed.`);return r.sticky?r:RegExp(r.source,r.flags+"y")}if(Object.prototype.hasOwnProperty.call(e,"str")){if(0===e.str.length)throw Error(`Rule #${t} ("${e.name}") has empty "str" property, which is not allowed.`);return RegExp(B(e.str),"y")}return RegExp(B(e.name),"y")}(e,t)}}function B(e){return e.replace(/[-[\]{}()*+!<=:?./\\^$|#\s,]/g,"\\$&")}function N(e,t){return(r,n)=>{let i,a=n;return n<r.tokens.length?void 0!==(i=e(r.tokens[n],r,n))&&a++:t?.(r,n),void 0===i?{matched:!1}:{matched:!0,position:a,value:i}}}function I(e,t){return e.matched?{matched:!0,position:e.position,value:t(e.value,e.position)}:e}function q(e,t){return e.matched?t(e):e}function P(e,t){return(r,n)=>I(e(r,n),(e,i)=>t(e,r,n,i))}function O(e,t){return(r,n)=>{let i=e(r,n);return i.matched?i:{matched:!0,position:n,value:t}}}function R(...e){return(t,r)=>{for(let n of e){let e=n(t,r);if(e.matched)return e}return{matched:!1}}}function M(e,t){return(r,n)=>{let i=e(r,n);return i.matched?i:t(r,n)}}function W(e){var t,r;return t=e,r=()=>!0,(e,n)=>{let i=[],a=!0;do{let s=t(e,n);s.matched&&r(s.value,i.length+1,e,n,s.position)?(i.push(s.value),n=s.position):a=!1}while(a);return{matched:!0,position:n,value:i}}}function $(e,t,r){return(n,i)=>q(e(n,i),e=>I(t(n,e.position),(t,a)=>r(e.value,t,n,i,a)))}function j(e,t){return $(e,t,(e,t)=>t)}function H(e,t,r,n){return(i,a)=>q(e(i,a),e=>q(t(i,e.position),t=>I(r(i,t.position),(r,s)=>n(e.value,t.value,r,i,a,s))))}function V(e,t,r){return H(e,t,r,(e,t)=>t)}function U(e,t,r){return function(e,t){return(r,n)=>q(e(r,n),e=>t(e.value,r,n,e.position)(r,e.position))}(e,e=>{var n,i,a,s;return n=e,i=$(t,r,(e,t)=>[e,t]),a=(e,[t,r])=>t(e,r),s=e=>P(i,(t,r,n,i)=>a(e,t,r,n,i)),(e,t)=>{let r=!0,i=n,a=t;do{let t=s(i,e,a)(e,a);t.matched?(i=t.value,a=t.position):r=!1}while(r);return{matched:!0,position:a,value:i}}})}let z=`(?:\\n|\\r\\n|\\r|\\f)`,G=`[^\\x00-\\x7F]`,J=`(?:\\\\[0-9a-f]{1,6}(?:\\r\\n|[ \\n\\r\\t\\f])?)`,K=`(?:\\\\[^\\n\\r\\f0-9a-f])`,Q=`(?:[_a-z]|${G}|${J}|${K})`,Z=`(?:[_a-z0-9-]|${G}|${J}|${K})`,X=`(?:${Z}+)`,Y=`(?:[-]?${Q}${Z}*)`,ee=`'([^\\n\\r\\f\\\\']|\\\\${z}|${G}|${J}|${K})*'`,et=`"([^\\n\\r\\f\\\\"]|\\\\${z}|${G}|${J}|${K})*"`,er=A([{name:"ws",regex:RegExp(`(?:[ \\t\\r\\n\\f]*)`)},{name:"hash",regex:RegExp(`#${X}`,"i")},{name:"ident",regex:RegExp(Y,"i")},{name:"str1",regex:RegExp(ee,"i")},{name:"str2",regex:RegExp(et,"i")},{name:"*"},{name:"."},{name:","},{name:"["},{name:"]"},{name:"="},{name:">"},{name:"|"},{name:"+"},{name:"~"},{name:"^"},{name:"$"}]),en=A([{name:"unicode",regex:RegExp(J,"i")},{name:"escape",regex:RegExp(K,"i")},{name:"any",regex:RegExp("[\\s\\S]","i")}]);function ei([e,t,r],[n,i,a]){return[e+n,t+i,r+a]}let ea=N(e=>"unicode"===e.name?String.fromCodePoint(parseInt(e.text.slice(1),16)):void 0),es=P(W(R(ea,N(e=>"escape"===e.name?e.text.slice(1):void 0),N(e=>"any"===e.name?e.text:void 0))),e=>e.join(""));function eo(e){return es({tokens:en(e).tokens,options:void 0},0).value}function el(e){return N(t=>t.name===e||void 0)}let eu=N(e=>"ws"===e.name?null:void 0),ec=O(eu,null);function eh(e){return V(ec,e,ec)}let ed=N(e=>"ident"===e.name?eo(e.text):void 0),ep=N(e=>"hash"===e.name?eo(e.text.slice(1)):void 0),ef=N(e=>e.name.startsWith("str")?eo(e.text.slice(1,-1)):void 0),em=$(O(ed,""),el("|"),e=>e),eg=M($(em,ed,(e,t)=>({name:t,namespace:e})),P(ed,e=>({name:e,namespace:null}))),eD=M($(em,el("*"),e=>({type:"universal",namespace:e,specificity:[0,0,0]})),P(el("*"),()=>({type:"universal",namespace:null,specificity:[0,0,0]}))),ey=P(eg,({name:e,namespace:t})=>({type:"tag",name:e,namespace:t,specificity:[0,0,1]})),eb=$(el("."),ed,(e,t)=>({type:"class",name:t,specificity:[0,1,0]})),eC=P(ep,e=>({type:"id",name:e,specificity:[1,0,0]})),ev=N(e=>{if("ident"===e.name){if("i"===e.text||"I"===e.text)return"i";if("s"===e.text||"S"===e.text)return"s"}}),eS=M($(ef,O(j(ec,ev),null),(e,t)=>({value:e,modifier:t})),$(ed,O(j(eu,ev),null),(e,t)=>({value:e,modifier:t}))),ew=R(P(el("="),()=>"="),$(el("~"),el("="),()=>"~="),$(el("|"),el("="),()=>"|="),$(el("^"),el("="),()=>"^="),$(el("$"),el("="),()=>"$="),$(el("*"),el("="),()=>"*=")),ek=M(H(el("["),eh(eg),el("]"),(e,{name:t,namespace:r})=>({type:"attrPresence",name:t,namespace:r,specificity:[0,1,0]})),V(el("["),H(eh(eg),ew,eh(eS),({name:e,namespace:t},r,{value:n,modifier:i})=>({type:"attrValue",name:e,namespace:t,matcher:r,value:n,modifier:i,specificity:[0,1,0]})),el("]"))),eE=M(eD,ey),ex=R(eC,eb,ek),eF=P(M(function(...e){return P(function(...e){return(t,r)=>{let n=[],i=r;for(let r of e){let e=r(t,i);if(!e.matched)return{matched:!1};n.push(e.value),i=e.position}return{matched:!0,position:i,value:n}}}(...e),e=>e.flatMap(e=>e))}(eE,W(ex)),function(e){return $(e,W(e),(e,t)=>[e,...t])}(ex)),e=>({type:"compound",list:e,specificity:e.map(e=>e.specificity).reduce(ei,[0,0,0])})),e_=M(eh(R(P(el(">"),()=>">"),P(el("+"),()=>"+"),P(el("~"),()=>"~"),$(el("|"),el("|"),()=>"||"))),P(eu,()=>" ")),eT=U(eF,P(e_,e=>(t,r)=>({type:"compound",list:[...r.list,{type:"combinator",combinator:e,left:t,specificity:t.specificity}],specificity:ei(t.specificity,r.specificity)})),eF);U(P(eT,e=>({type:"list",list:[e]})),P(eh(el(",")),()=>(e,t)=>({type:"list",list:[...e.list,t]})),eT);function eA(e,t,r=1){return`${e.replace(/(\t)|(\r)|(\n)/g,(e,t,r)=>t?"␉":r?"␍":"␊")}
${"".padEnd(t)}${"^".repeat(r)}`}function eL(e){if(!e.type)throw Error("This is not an AST node.");switch(e.type){case"universal":return eB(e.namespace)+"*";case"tag":return eB(e.namespace)+eI(e.name);case"class":return"."+eI(e.name);case"id":return"#"+eI(e.name);case"attrPresence":return`[${eB(e.namespace)}${eI(e.name)}]`;case"attrValue":return`[${eB(e.namespace)}${eI(e.name)}${e.matcher}"${e.value.replace(/(")|(\\)|(\x00)|([\x01-\x1f]|\x7f)/g,(e,t,r,n,i)=>t?'\\"':r?"\\\\":n?"�":eN(i))}"${e.modifier?e.modifier:""}]`;case"combinator":return eL(e.left)+e.combinator;case"compound":return e.list.reduce((e,t)=>"combinator"===t.type?eL(t)+e:e+eL(t),"");case"list":return e.list.map(eL).join(",")}}function eB(e){return e||""===e?eI(e)+"|":""}function eN(e){return`\\${e.codePointAt(0).toString(16)} `}function eI(e){return e.replace(/(^[0-9])|(^-[0-9])|(^-$)|([-0-9a-zA-Z_]|[^\x00-\x7F])|(\x00)|([\x01-\x1f]|\x7f)|([\s\S])/g,(e,t,r,n,i,a,s,o)=>t?eN(t):r?"-"+eN(r.slice(1)):n?"\\-":i||(a?"�":s?eN(s):"\\"+o))}function eq(e){switch(e.type){case"universal":case"tag":return[1];case"id":return[2];case"class":return[3,e.name];case"attrPresence":return[4,eL(e)];case"attrValue":return[5,eL(e)];case"combinator":return[15,eL(e)]}}function eP(e,t){if(!Array.isArray(e)||!Array.isArray(t))throw Error("Arguments must be arrays.");let r=e.length<t.length?e.length:t.length;for(let n=0;n<r;n++)if(e[n]!==t[n])return e[n]<t[n]?-1:1;return e.length-t.length}let eO=[["├─","│ "],["└─","  "]],eR=[["┠─","┃ "],["┖─","  "]],eM=[["╟─","║ "],["╙─","  "]];class eW{constructor(e){this.branches=e$(function(e){let t=e.length,r=Array(t);for(let i=0;i<t;i++){var n;let[t,a]=e[i],s=(function e(t){let r=[];t.list.forEach(t=>{switch(t.type){case"class":r.push({matcher:"~=",modifier:null,name:"class",namespace:null,specificity:t.specificity,type:"attrValue",value:t.name});break;case"id":r.push({matcher:"=",modifier:null,name:"id",namespace:null,specificity:t.specificity,type:"attrValue",value:t.name});break;case"combinator":e(t.left),r.push(t);break;case"universal":break;default:r.push(t)}}),t.list=r}(n=function(e,t){if(!("string"==typeof t||t instanceof String))throw Error("Expected a selector string. Actual input is not a string!");let r=er(t);if(!r.complete)throw Error(`The input "${t}" was only partially tokenized, stopped at offset ${r.offset}!
`+eA(t,r.offset));let n=eh(e)({tokens:r.tokens,options:void 0},0);if(!n.matched)throw Error(`No match for "${t}" input!`);if(n.position<r.tokens.length){let e=r.tokens[n.position];throw Error(`The input "${t}" was only partially parsed, stopped at offset ${e.offset}!
`+eA(t,e.offset,e.len))}return n.value}(eT,t)),!function e(t){if(!t.type)throw Error("This is not an AST node.");switch(t.type){case"compound":t.list.forEach(e),t.list.sort((e,t)=>eP(eq(e),eq(t)));break;case"combinator":e(t.left);break;case"list":t.list.forEach(e),t.list.sort((e,t)=>eL(e)<eL(t)?-1:1)}return t}(n),n);r[i]={ast:s,terminal:{type:"terminal",valueContainer:{index:i,value:a,specificity:s.specificity}}}}return r}(e))}build(e){return e(this.branches)}}function e$(e){let t=[];for(;e.length;){let r=ez(e,e=>!0,ej),{matches:n,nonmatches:i,empty:a}=function(e,t){let r=[],n=[],i=[];for(let a of e){let e=a.ast.list;e.length?(e.some(e=>ej(e)===t)?r:n).push(a):i.push(a)}return{matches:r,nonmatches:n,empty:i}}(e,r);e=i,n.length&&t.push(function(e,t){if("tag"===e)return{type:"tagName",variants:Object.entries(eV(t,e=>"tag"===e.type,e=>e.name)).map(([e,t])=>({type:"variant",value:e,cont:e$(t.items)}))};if(e.startsWith("attrValue "))return function(e,t){let r=eV(t,t=>"attrValue"===t.type&&t.name===e,e=>`${e.matcher} ${e.modifier||""} ${e.value}`),n=[];for(let e of Object.values(r)){let t=e.oneSimpleSelector,r=function(e){if("i"===e.modifier){let t=e.value.toLowerCase();switch(e.matcher){case"=":return e=>t===e.toLowerCase();case"~=":return e=>e.toLowerCase().split(/[ \t]+/).includes(t);case"^=":return e=>e.toLowerCase().startsWith(t);case"$=":return e=>e.toLowerCase().endsWith(t);case"*=":return e=>e.toLowerCase().includes(t);case"|=":return e=>{let r=e.toLowerCase();return t===r||r.startsWith(t)&&"-"===r[t.length]}}}else{let t=e.value;switch(e.matcher){case"=":return e=>t===e;case"~=":return e=>e.split(/[ \t]+/).includes(t);case"^=":return e=>e.startsWith(t);case"$=":return e=>e.endsWith(t);case"*=":return e=>e.includes(t);case"|=":return e=>t===e||e.startsWith(t)&&"-"===e[t.length]}}}(t),i=e$(e.items);n.push({type:"matcher",matcher:t.matcher,modifier:t.modifier,value:t.value,predicate:r,cont:i})}return{type:"attrValue",name:e,matchers:n}}(e.substring(10),t);if(e.startsWith("attrPresence "))return function(e,t){for(let r of t)eU(r,t=>"attrPresence"===t.type&&t.name===e);return{type:"attrPresence",name:e,cont:e$(t)}}(e.substring(13),t);if("combinator >"===e)return eH(">",t);if("combinator +"===e)return eH("+",t);throw Error(`Unsupported selector kind: ${e}`)}(r,n)),a.length&&t.push(...function(e){let t=[];for(let r of e){let e=r.terminal;if("terminal"===e.type)t.push(e);else{let{matches:r,rest:n}=function(e,t){let r=[],n=[];for(let i of e)t(i)?r.push(i):n.push(i);return{matches:r,rest:n}}(e.cont,e=>"terminal"===e.type);r.forEach(e=>t.push(e)),n.length&&(e.cont=n,t.push(e))}}return t}(a))}return t}function ej(e){switch(e.type){case"attrPresence":return`attrPresence ${e.name}`;case"attrValue":return`attrValue ${e.name}`;case"combinator":return`combinator ${e.combinator}`;default:return e.type}}function eH(e,t){let r=eV(t,t=>"combinator"===t.type&&t.combinator===e,e=>eL(e.left)),n=[];for(let e of Object.values(r)){let t=e$(e.items),r=e.oneSimpleSelector.left;n.push({ast:r,terminal:{type:"popElement",cont:t}})}return{type:"pushElement",combinator:e,cont:e$(n)}}function eV(e,t,r){let n={};for(;e.length;){let i=ez(e,t,r),a=e=>t(e)&&r(e)===i,{matches:s,rest:o}=function(e,t){let r=[],n=[];for(let i of e)t(i)?r.push(i):n.push(i);return{matches:r,rest:n}}(e,e=>e.ast.list.some(a)),l=null;for(let e of s){let t=eU(e,a);l||(l=t)}if(null==l)throw Error("No simple selector is found.");n[i]={oneSimpleSelector:l,items:s},e=o}return n}function eU(e,t){let r=e.ast.list,n=Array(r.length),i=-1;for(let e=r.length;e-- >0;)t(r[e])&&(n[e]=!0,i=e);if(-1==i)throw Error("Couldn't find the required simple selector.");let a=r[i];return e.ast.list=r.filter((e,t)=>!n[t]),a}function ez(e,t,r){let n={};for(let i of e){let e={};for(let n of i.ast.list.filter(t))e[r(n)]=!0;for(let t of Object.keys(e))n[t]?n[t]++:n[t]=1}let i="",a=0;for(let e of Object.entries(n))e[1]>a&&(i=e[0],a=e[1]);return i}class eG{constructor(e){this.f=e}pickAll(e){return this.f(e)}pick1(e,t=!1){let r=this.f(e),n=r.length;if(0===n)return null;if(1===n)return r[0].value;let i=t?eJ:eK,a=r[0];for(let e=1;e<n;e++){let t=r[e];i(a,t)&&(a=t)}return a.value}}function eJ(e,t){let r=eP(t.specificity,e.specificity);return r>0||0===r&&t.index<e.index}function eK(e,t){let r=eP(t.specificity,e.specificity);return r>0||0===r&&t.index>e.index}function eQ(e){return new eG(eZ(e))}function eZ(e){let t=e.map(eX);return(e,...r)=>t.flatMap(t=>t(e,...r))}function eX(e){switch(e.type){case"terminal":{let t=[e.valueContainer];return(e,...r)=>t}case"tagName":var t=e;let r={};for(let e of t.variants)r[e.value]=eZ(e.cont);return(e,...t)=>{let n=r[e.name];return n?n(e,...t):[]};case"attrValue":var n=e;let i=[];for(let e of n.matchers){let t=e.predicate,r=eZ(e.cont);i.push((e,n,...i)=>t(e)?r(n,...i):[])}let a=n.name;return(e,...t)=>{let r=e.attribs[a];return r||""===r?i.flatMap(n=>n(r,e,...t)):[]};case"attrPresence":var s=e;let o=s.name,l=eZ(s.cont);return(e,...t)=>Object.prototype.hasOwnProperty.call(e.attribs,o)?l(e,...t):[];case"pushElement":var u=e;let c=eZ(u.cont),h="+"===u.combinator?eY:e0;return(e,...t)=>{let r=h(e);return null===r?[]:c(r,e,...t)};case"popElement":var d=e;let p=eZ(d.cont);return(e,t,...r)=>p(t,...r)}}let eY=e=>{let t=e.prev;return null===t?null:w(t)?t:eY(t)},e0=e=>{let t=e.parent;return t&&w(t)?t:null},e1=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),e2=new Uint16Array("Ȁaglq	\x15\x18\x1bɭ\x0f\0\0\x12p;䀦os;䀧t;䀾t;䀼uot;䀢".split("").map(e=>e.charCodeAt(0))),e3=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]),e8=null!=(r8=String.fromCodePoint)?r8:function(e){let t="";return e>65535&&(e-=65536,t+=String.fromCharCode(e>>>10&1023|55296),e=56320|1023&e),t+=String.fromCharCode(e)};function e6(e){var t;return e>=55296&&e<=57343||e>1114111?65533:null!=(t=e3.get(e))?t:e}function e5(e){return e>=r6.ZERO&&e<=r6.NINE}!function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(r6||(r6={})),!function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(r5||(r5={})),!function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(r7||(r7={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(r9||(r9={}));class e7{constructor(e,t,r){this.decodeTree=e,this.emitCodePoint=t,this.errors=r,this.state=r7.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=r9.Strict}startEntity(e){this.decodeMode=e,this.state=r7.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case r7.EntityStart:if(e.charCodeAt(t)===r6.NUM)return this.state=r7.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1);return this.state=r7.NamedEntity,this.stateNamedEntity(e,t);case r7.NumericStart:return this.stateNumericStart(e,t);case r7.NumericDecimal:return this.stateNumericDecimal(e,t);case r7.NumericHex:return this.stateNumericHex(e,t);case r7.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===r6.LOWER_X?(this.state=r7.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=r7.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,r,n){if(t!==r){let i=r-t;this.result=this.result*Math.pow(n,i)+parseInt(e.substr(t,i),n),this.consumed+=i}}stateNumericHex(e,t){let r=t;for(;t<e.length;){var n;let i=e.charCodeAt(t);if(!e5(i)&&(!((n=i)>=r6.UPPER_A)||!(n<=r6.UPPER_F))&&(!(n>=r6.LOWER_A)||!(n<=r6.LOWER_F)))return this.addToNumericResult(e,r,t,16),this.emitNumericEntity(i,3);t+=1}return this.addToNumericResult(e,r,t,16),-1}stateNumericDecimal(e,t){let r=t;for(;t<e.length;){let n=e.charCodeAt(t);if(!e5(n))return this.addToNumericResult(e,r,t,10),this.emitNumericEntity(n,2);t+=1}return this.addToNumericResult(e,r,t,10),-1}emitNumericEntity(e,t){var r;if(this.consumed<=t)return null==(r=this.errors)||r.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===r6.SEMI)this.consumed+=1;else if(this.decodeMode===r9.Strict)return 0;return this.emitCodePoint(e6(this.result),this.consumed),this.errors&&(e!==r6.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:r}=this,n=r[this.treeIndex],i=(n&r5.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let a=e.charCodeAt(t);if(this.treeIndex=e4(r,n,this.treeIndex+Math.max(1,i),a),this.treeIndex<0)return 0===this.result||this.decodeMode===r9.Attribute&&(0===i||function(e){var t;return e===r6.EQUALS||(t=e)>=r6.UPPER_A&&t<=r6.UPPER_Z||t>=r6.LOWER_A&&t<=r6.LOWER_Z||e5(t)}(a))?0:this.emitNotTerminatedNamedEntity();if(0!=(i=((n=r[this.treeIndex])&r5.VALUE_LENGTH)>>14)){if(a===r6.SEMI)return this.emitNamedEntityData(this.treeIndex,i,this.consumed+this.excess);this.decodeMode!==r9.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:r}=this,n=(r[t]&r5.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,n,this.consumed),null==(e=this.errors)||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,r){let{decodeTree:n}=this;return this.emitCodePoint(1===t?n[e]&~r5.VALUE_LENGTH:n[e+1],r),3===t&&this.emitCodePoint(n[e+2],r),r}end(){var e;switch(this.state){case r7.NamedEntity:return 0!==this.result&&(this.decodeMode!==r9.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case r7.NumericDecimal:return this.emitNumericEntity(0,2);case r7.NumericHex:return this.emitNumericEntity(0,3);case r7.NumericStart:return null==(e=this.errors)||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case r7.EntityStart:return 0}}}function e9(e){let t="",r=new e7(e,e=>t+=e8(e));return function(e,n){let i=0,a=0;for(;(a=e.indexOf("&",a))>=0;){t+=e.slice(i,a),r.startEntity(n);let s=r.write(e,a+1);if(s<0){i=a+r.end();break}i=a+s,a=0===s?i+1:i}let s=t+e.slice(i);return t="",s}}function e4(e,t,r,n){let i=(t&r5.BRANCH_LENGTH)>>7,a=t&r5.JUMP_TABLE;if(0===i)return 0!==a&&n===a?r:-1;if(a){let t=n-a;return t<0||t>=i?-1:e[r+t]-1}let s=r,o=s+i-1;for(;s<=o;){let t=s+o>>>1,r=e[t];if(r<n)s=t+1;else{if(!(r>n))return e[t+i];o=t-1}}return -1}function te(e){return e===r4.Space||e===r4.NewLine||e===r4.Tab||e===r4.FormFeed||e===r4.CarriageReturn}function tt(e){return e===r4.Slash||e===r4.Gt||te(e)}function tr(e){return e>=r4.Zero&&e<=r4.Nine}e9(e1),e9(e2),!function(e){e[e.Tab=9]="Tab",e[e.NewLine=10]="NewLine",e[e.FormFeed=12]="FormFeed",e[e.CarriageReturn=13]="CarriageReturn",e[e.Space=32]="Space",e[e.ExclamationMark=33]="ExclamationMark",e[e.Number=35]="Number",e[e.Amp=38]="Amp",e[e.SingleQuote=39]="SingleQuote",e[e.DoubleQuote=34]="DoubleQuote",e[e.Dash=45]="Dash",e[e.Slash=47]="Slash",e[e.Zero=48]="Zero",e[e.Nine=57]="Nine",e[e.Semi=59]="Semi",e[e.Lt=60]="Lt",e[e.Eq=61]="Eq",e[e.Gt=62]="Gt",e[e.Questionmark=63]="Questionmark",e[e.UpperA=65]="UpperA",e[e.LowerA=97]="LowerA",e[e.UpperF=70]="UpperF",e[e.LowerF=102]="LowerF",e[e.UpperZ=90]="UpperZ",e[e.LowerZ=122]="LowerZ",e[e.LowerX=120]="LowerX",e[e.OpeningSquareBracket=91]="OpeningSquareBracket"}(r4||(r4={})),function(e){e[e.Text=1]="Text",e[e.BeforeTagName=2]="BeforeTagName",e[e.InTagName=3]="InTagName",e[e.InSelfClosingTag=4]="InSelfClosingTag",e[e.BeforeClosingTagName=5]="BeforeClosingTagName",e[e.InClosingTagName=6]="InClosingTagName",e[e.AfterClosingTagName=7]="AfterClosingTagName",e[e.BeforeAttributeName=8]="BeforeAttributeName",e[e.InAttributeName=9]="InAttributeName",e[e.AfterAttributeName=10]="AfterAttributeName",e[e.BeforeAttributeValue=11]="BeforeAttributeValue",e[e.InAttributeValueDq=12]="InAttributeValueDq",e[e.InAttributeValueSq=13]="InAttributeValueSq",e[e.InAttributeValueNq=14]="InAttributeValueNq",e[e.BeforeDeclaration=15]="BeforeDeclaration",e[e.InDeclaration=16]="InDeclaration",e[e.InProcessingInstruction=17]="InProcessingInstruction",e[e.BeforeComment=18]="BeforeComment",e[e.CDATASequence=19]="CDATASequence",e[e.InSpecialComment=20]="InSpecialComment",e[e.InCommentLike=21]="InCommentLike",e[e.BeforeSpecialS=22]="BeforeSpecialS",e[e.SpecialStartSequence=23]="SpecialStartSequence",e[e.InSpecialTag=24]="InSpecialTag",e[e.BeforeEntity=25]="BeforeEntity",e[e.BeforeNumericEntity=26]="BeforeNumericEntity",e[e.InNamedEntity=27]="InNamedEntity",e[e.InNumericEntity=28]="InNumericEntity",e[e.InHexEntity=29]="InHexEntity"}(ne||(ne={})),!function(e){e[e.NoValue=0]="NoValue",e[e.Unquoted=1]="Unquoted",e[e.Single=2]="Single",e[e.Double=3]="Double"}(nt||(nt={}));let tn={Cdata:new Uint8Array([67,68,65,84,65,91]),CdataEnd:new Uint8Array([93,93,62]),CommentEnd:new Uint8Array([45,45,62]),ScriptEnd:new Uint8Array([60,47,115,99,114,105,112,116]),StyleEnd:new Uint8Array([60,47,115,116,121,108,101]),TitleEnd:new Uint8Array([60,47,116,105,116,108,101])};class ti{constructor({xmlMode:e=!1,decodeEntities:t=!0},r){this.cbs=r,this.state=ne.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=ne.Text,this.isSpecial=!1,this.running=!0,this.offset=0,this.currentSequence=void 0,this.sequenceIndex=0,this.trieIndex=0,this.trieCurrent=0,this.entityResult=0,this.entityExcess=0,this.xmlMode=e,this.decodeEntities=t,this.entityTrie=e?e2:e1}reset(){this.state=ne.Text,this.buffer="",this.sectionStart=0,this.index=0,this.baseState=ne.Text,this.currentSequence=void 0,this.running=!0,this.offset=0}write(e){this.offset+=this.buffer.length,this.buffer=e,this.parse()}end(){this.running&&this.finish()}pause(){this.running=!1}resume(){this.running=!0,this.index<this.buffer.length+this.offset&&this.parse()}getIndex(){return this.index}getSectionStart(){return this.sectionStart}stateText(e){e===r4.Lt||!this.decodeEntities&&this.fastForwardTo(r4.Lt)?(this.index>this.sectionStart&&this.cbs.ontext(this.sectionStart,this.index),this.state=ne.BeforeTagName,this.sectionStart=this.index):this.decodeEntities&&e===r4.Amp&&(this.state=ne.BeforeEntity)}stateSpecialStartSequence(e){let t=this.sequenceIndex===this.currentSequence.length;if(t?tt(e):(32|e)===this.currentSequence[this.sequenceIndex]){if(!t)return void this.sequenceIndex++}else this.isSpecial=!1;this.sequenceIndex=0,this.state=ne.InTagName,this.stateInTagName(e)}stateInSpecialTag(e){if(this.sequenceIndex===this.currentSequence.length){if(e===r4.Gt||te(e)){let t=this.index-this.currentSequence.length;if(this.sectionStart<t){let e=this.index;this.index=t,this.cbs.ontext(this.sectionStart,t),this.index=e}this.isSpecial=!1,this.sectionStart=t+2,this.stateInClosingTagName(e);return}this.sequenceIndex=0}(32|e)===this.currentSequence[this.sequenceIndex]?this.sequenceIndex+=1:0===this.sequenceIndex?this.currentSequence===tn.TitleEnd?this.decodeEntities&&e===r4.Amp&&(this.state=ne.BeforeEntity):this.fastForwardTo(r4.Lt)&&(this.sequenceIndex=1):this.sequenceIndex=Number(e===r4.Lt)}stateCDATASequence(e){e===tn.Cdata[this.sequenceIndex]?++this.sequenceIndex===tn.Cdata.length&&(this.state=ne.InCommentLike,this.currentSequence=tn.CdataEnd,this.sequenceIndex=0,this.sectionStart=this.index+1):(this.sequenceIndex=0,this.state=ne.InDeclaration,this.stateInDeclaration(e))}fastForwardTo(e){for(;++this.index<this.buffer.length+this.offset;)if(this.buffer.charCodeAt(this.index-this.offset)===e)return!0;return this.index=this.buffer.length+this.offset-1,!1}stateInCommentLike(e){e===this.currentSequence[this.sequenceIndex]?++this.sequenceIndex===this.currentSequence.length&&(this.currentSequence===tn.CdataEnd?this.cbs.oncdata(this.sectionStart,this.index,2):this.cbs.oncomment(this.sectionStart,this.index,2),this.sequenceIndex=0,this.sectionStart=this.index+1,this.state=ne.Text):0===this.sequenceIndex?this.fastForwardTo(this.currentSequence[0])&&(this.sequenceIndex=1):e!==this.currentSequence[this.sequenceIndex-1]&&(this.sequenceIndex=0)}isTagStartChar(e){return this.xmlMode?!tt(e):e>=r4.LowerA&&e<=r4.LowerZ||e>=r4.UpperA&&e<=r4.UpperZ}startSpecial(e,t){this.isSpecial=!0,this.currentSequence=e,this.sequenceIndex=t,this.state=ne.SpecialStartSequence}stateBeforeTagName(e){if(e===r4.ExclamationMark)this.state=ne.BeforeDeclaration,this.sectionStart=this.index+1;else if(e===r4.Questionmark)this.state=ne.InProcessingInstruction,this.sectionStart=this.index+1;else if(this.isTagStartChar(e)){let t=32|e;this.sectionStart=this.index,this.xmlMode||t!==tn.TitleEnd[2]?this.state=this.xmlMode||t!==tn.ScriptEnd[2]?ne.InTagName:ne.BeforeSpecialS:this.startSpecial(tn.TitleEnd,3)}else e===r4.Slash?this.state=ne.BeforeClosingTagName:(this.state=ne.Text,this.stateText(e))}stateInTagName(e){tt(e)&&(this.cbs.onopentagname(this.sectionStart,this.index),this.sectionStart=-1,this.state=ne.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateBeforeClosingTagName(e){te(e)||(e===r4.Gt?this.state=ne.Text:(this.state=this.isTagStartChar(e)?ne.InClosingTagName:ne.InSpecialComment,this.sectionStart=this.index))}stateInClosingTagName(e){(e===r4.Gt||te(e))&&(this.cbs.onclosetag(this.sectionStart,this.index),this.sectionStart=-1,this.state=ne.AfterClosingTagName,this.stateAfterClosingTagName(e))}stateAfterClosingTagName(e){(e===r4.Gt||this.fastForwardTo(r4.Gt))&&(this.state=ne.Text,this.baseState=ne.Text,this.sectionStart=this.index+1)}stateBeforeAttributeName(e){e===r4.Gt?(this.cbs.onopentagend(this.index),this.isSpecial?(this.state=ne.InSpecialTag,this.sequenceIndex=0):this.state=ne.Text,this.baseState=this.state,this.sectionStart=this.index+1):e===r4.Slash?this.state=ne.InSelfClosingTag:te(e)||(this.state=ne.InAttributeName,this.sectionStart=this.index)}stateInSelfClosingTag(e){e===r4.Gt?(this.cbs.onselfclosingtag(this.index),this.state=ne.Text,this.baseState=ne.Text,this.sectionStart=this.index+1,this.isSpecial=!1):te(e)||(this.state=ne.BeforeAttributeName,this.stateBeforeAttributeName(e))}stateInAttributeName(e){(e===r4.Eq||tt(e))&&(this.cbs.onattribname(this.sectionStart,this.index),this.sectionStart=-1,this.state=ne.AfterAttributeName,this.stateAfterAttributeName(e))}stateAfterAttributeName(e){e===r4.Eq?this.state=ne.BeforeAttributeValue:e===r4.Slash||e===r4.Gt?(this.cbs.onattribend(nt.NoValue,this.index),this.state=ne.BeforeAttributeName,this.stateBeforeAttributeName(e)):te(e)||(this.cbs.onattribend(nt.NoValue,this.index),this.state=ne.InAttributeName,this.sectionStart=this.index)}stateBeforeAttributeValue(e){e===r4.DoubleQuote?(this.state=ne.InAttributeValueDq,this.sectionStart=this.index+1):e===r4.SingleQuote?(this.state=ne.InAttributeValueSq,this.sectionStart=this.index+1):te(e)||(this.sectionStart=this.index,this.state=ne.InAttributeValueNq,this.stateInAttributeValueNoQuotes(e))}handleInAttributeValue(e,t){e===t||!this.decodeEntities&&this.fastForwardTo(t)?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(t===r4.DoubleQuote?nt.Double:nt.Single,this.index),this.state=ne.BeforeAttributeName):this.decodeEntities&&e===r4.Amp&&(this.baseState=this.state,this.state=ne.BeforeEntity)}stateInAttributeValueDoubleQuotes(e){this.handleInAttributeValue(e,r4.DoubleQuote)}stateInAttributeValueSingleQuotes(e){this.handleInAttributeValue(e,r4.SingleQuote)}stateInAttributeValueNoQuotes(e){te(e)||e===r4.Gt?(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=-1,this.cbs.onattribend(nt.Unquoted,this.index),this.state=ne.BeforeAttributeName,this.stateBeforeAttributeName(e)):this.decodeEntities&&e===r4.Amp&&(this.baseState=this.state,this.state=ne.BeforeEntity)}stateBeforeDeclaration(e){e===r4.OpeningSquareBracket?(this.state=ne.CDATASequence,this.sequenceIndex=0):this.state=e===r4.Dash?ne.BeforeComment:ne.InDeclaration}stateInDeclaration(e){(e===r4.Gt||this.fastForwardTo(r4.Gt))&&(this.cbs.ondeclaration(this.sectionStart,this.index),this.state=ne.Text,this.sectionStart=this.index+1)}stateInProcessingInstruction(e){(e===r4.Gt||this.fastForwardTo(r4.Gt))&&(this.cbs.onprocessinginstruction(this.sectionStart,this.index),this.state=ne.Text,this.sectionStart=this.index+1)}stateBeforeComment(e){e===r4.Dash?(this.state=ne.InCommentLike,this.currentSequence=tn.CommentEnd,this.sequenceIndex=2,this.sectionStart=this.index+1):this.state=ne.InDeclaration}stateInSpecialComment(e){(e===r4.Gt||this.fastForwardTo(r4.Gt))&&(this.cbs.oncomment(this.sectionStart,this.index,0),this.state=ne.Text,this.sectionStart=this.index+1)}stateBeforeSpecialS(e){let t=32|e;t===tn.ScriptEnd[3]?this.startSpecial(tn.ScriptEnd,4):t===tn.StyleEnd[3]?this.startSpecial(tn.StyleEnd,4):(this.state=ne.InTagName,this.stateInTagName(e))}stateBeforeEntity(e){this.entityExcess=1,this.entityResult=0,e===r4.Number?this.state=ne.BeforeNumericEntity:e===r4.Amp||(this.trieIndex=0,this.trieCurrent=this.entityTrie[0],this.state=ne.InNamedEntity,this.stateInNamedEntity(e))}stateInNamedEntity(e){if(this.entityExcess+=1,this.trieIndex=e4(this.entityTrie,this.trieCurrent,this.trieIndex+1,e),this.trieIndex<0){this.emitNamedEntity(),this.index--;return}this.trieCurrent=this.entityTrie[this.trieIndex];let t=this.trieCurrent&r5.VALUE_LENGTH;if(t){let r=(t>>14)-1;if(this.allowLegacyEntity()||e===r4.Semi){let e=this.index-this.entityExcess+1;e>this.sectionStart&&this.emitPartial(this.sectionStart,e),this.entityResult=this.trieIndex,this.trieIndex+=r,this.entityExcess=0,this.sectionStart=this.index+1,0===r&&this.emitNamedEntity()}else this.trieIndex+=r}}emitNamedEntity(){if(this.state=this.baseState,0!==this.entityResult)switch((this.entityTrie[this.entityResult]&r5.VALUE_LENGTH)>>14){case 1:this.emitCodePoint(this.entityTrie[this.entityResult]&~r5.VALUE_LENGTH);break;case 2:this.emitCodePoint(this.entityTrie[this.entityResult+1]);break;case 3:this.emitCodePoint(this.entityTrie[this.entityResult+1]),this.emitCodePoint(this.entityTrie[this.entityResult+2])}}stateBeforeNumericEntity(e){(32|e)===r4.LowerX?(this.entityExcess++,this.state=ne.InHexEntity):(this.state=ne.InNumericEntity,this.stateInNumericEntity(e))}emitNumericEntity(e){let t=this.index-this.entityExcess-1;t+2+Number(this.state===ne.InHexEntity)!==this.index&&(t>this.sectionStart&&this.emitPartial(this.sectionStart,t),this.sectionStart=this.index+Number(e),this.emitCodePoint(e6(this.entityResult))),this.state=this.baseState}stateInNumericEntity(e){e===r4.Semi?this.emitNumericEntity(!0):tr(e)?(this.entityResult=10*this.entityResult+(e-r4.Zero),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}stateInHexEntity(e){if(e===r4.Semi)this.emitNumericEntity(!0);else if(tr(e))this.entityResult=16*this.entityResult+(e-r4.Zero),this.entityExcess++;else e>=r4.UpperA&&e<=r4.UpperF||e>=r4.LowerA&&e<=r4.LowerF?(this.entityResult=16*this.entityResult+((32|e)-r4.LowerA+10),this.entityExcess++):(this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state=this.baseState,this.index--)}allowLegacyEntity(){return!this.xmlMode&&(this.baseState===ne.Text||this.baseState===ne.InSpecialTag)}cleanup(){this.running&&this.sectionStart!==this.index&&(this.state===ne.Text||this.state===ne.InSpecialTag&&0===this.sequenceIndex?(this.cbs.ontext(this.sectionStart,this.index),this.sectionStart=this.index):(this.state===ne.InAttributeValueDq||this.state===ne.InAttributeValueSq||this.state===ne.InAttributeValueNq)&&(this.cbs.onattribdata(this.sectionStart,this.index),this.sectionStart=this.index))}shouldContinue(){return this.index<this.buffer.length+this.offset&&this.running}parse(){for(;this.shouldContinue();){let e=this.buffer.charCodeAt(this.index-this.offset);switch(this.state){case ne.Text:this.stateText(e);break;case ne.SpecialStartSequence:this.stateSpecialStartSequence(e);break;case ne.InSpecialTag:this.stateInSpecialTag(e);break;case ne.CDATASequence:this.stateCDATASequence(e);break;case ne.InAttributeValueDq:this.stateInAttributeValueDoubleQuotes(e);break;case ne.InAttributeName:this.stateInAttributeName(e);break;case ne.InCommentLike:this.stateInCommentLike(e);break;case ne.InSpecialComment:this.stateInSpecialComment(e);break;case ne.BeforeAttributeName:this.stateBeforeAttributeName(e);break;case ne.InTagName:this.stateInTagName(e);break;case ne.InClosingTagName:this.stateInClosingTagName(e);break;case ne.BeforeTagName:this.stateBeforeTagName(e);break;case ne.AfterAttributeName:this.stateAfterAttributeName(e);break;case ne.InAttributeValueSq:this.stateInAttributeValueSingleQuotes(e);break;case ne.BeforeAttributeValue:this.stateBeforeAttributeValue(e);break;case ne.BeforeClosingTagName:this.stateBeforeClosingTagName(e);break;case ne.AfterClosingTagName:this.stateAfterClosingTagName(e);break;case ne.BeforeSpecialS:this.stateBeforeSpecialS(e);break;case ne.InAttributeValueNq:this.stateInAttributeValueNoQuotes(e);break;case ne.InSelfClosingTag:this.stateInSelfClosingTag(e);break;case ne.InDeclaration:this.stateInDeclaration(e);break;case ne.BeforeDeclaration:this.stateBeforeDeclaration(e);break;case ne.BeforeComment:this.stateBeforeComment(e);break;case ne.InProcessingInstruction:this.stateInProcessingInstruction(e);break;case ne.InNamedEntity:this.stateInNamedEntity(e);break;case ne.BeforeEntity:this.stateBeforeEntity(e);break;case ne.InHexEntity:this.stateInHexEntity(e);break;case ne.InNumericEntity:this.stateInNumericEntity(e);break;default:this.stateBeforeNumericEntity(e)}this.index++}this.cleanup()}finish(){this.state===ne.InNamedEntity&&this.emitNamedEntity(),this.sectionStart<this.index&&this.handleTrailingData(),this.cbs.onend()}handleTrailingData(){let e=this.buffer.length+this.offset;this.state===ne.InCommentLike?this.currentSequence===tn.CdataEnd?this.cbs.oncdata(this.sectionStart,e,0):this.cbs.oncomment(this.sectionStart,e,0):this.state===ne.InNumericEntity&&this.allowLegacyEntity()||this.state===ne.InHexEntity&&this.allowLegacyEntity()?this.emitNumericEntity(!1):this.state===ne.InTagName||this.state===ne.BeforeAttributeName||this.state===ne.BeforeAttributeValue||this.state===ne.AfterAttributeName||this.state===ne.InAttributeName||this.state===ne.InAttributeValueSq||this.state===ne.InAttributeValueDq||this.state===ne.InAttributeValueNq||this.state===ne.InClosingTagName||this.cbs.ontext(this.sectionStart,e)}emitPartial(e,t){this.baseState!==ne.Text&&this.baseState!==ne.InSpecialTag?this.cbs.onattribdata(e,t):this.cbs.ontext(e,t)}emitCodePoint(e){this.baseState!==ne.Text&&this.baseState!==ne.InSpecialTag?this.cbs.onattribentity(e):this.cbs.ontextentity(e)}}let ta=new Set(["input","option","optgroup","select","button","datalist","textarea"]),ts=new Set(["p"]),to=new Set(["thead","tbody"]),tl=new Set(["dd","dt"]),tu=new Set(["rt","rp"]),tc=new Map([["tr",new Set(["tr","th","td"])],["th",new Set(["th"])],["td",new Set(["thead","th","td"])],["body",new Set(["head","link","script"])],["li",new Set(["li"])],["p",ts],["h1",ts],["h2",ts],["h3",ts],["h4",ts],["h5",ts],["h6",ts],["select",ta],["input",ta],["output",ta],["button",ta],["datalist",ta],["textarea",ta],["option",new Set(["option"])],["optgroup",new Set(["optgroup","option"])],["dd",tl],["dt",tl],["address",ts],["article",ts],["aside",ts],["blockquote",ts],["details",ts],["div",ts],["dl",ts],["fieldset",ts],["figcaption",ts],["figure",ts],["footer",ts],["form",ts],["header",ts],["hr",ts],["main",ts],["nav",ts],["ol",ts],["pre",ts],["section",ts],["table",ts],["ul",ts],["rt",tu],["rp",tu],["tbody",to],["tfoot",to]]),th=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]),td=new Set(["math","svg"]),tp=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignobject","desc","title"]),tf=/\s|\//;class tm{constructor(e,t={}){var r,n,i,a,s;this.options=t,this.startIndex=0,this.endIndex=0,this.openTagStart=0,this.tagname="",this.attribname="",this.attribvalue="",this.attribs=null,this.stack=[],this.foreignContext=[],this.buffers=[],this.bufferOffset=0,this.writeIndex=0,this.ended=!1,this.cbs=null!=e?e:{},this.lowerCaseTagNames=null!=(r=t.lowerCaseTags)?r:!t.xmlMode,this.lowerCaseAttributeNames=null!=(n=t.lowerCaseAttributeNames)?n:!t.xmlMode,this.tokenizer=new(null!=(i=t.Tokenizer)?i:ti)(this.options,this),null==(s=(a=this.cbs).onparserinit)||s.call(a,this)}ontext(e,t){var r,n;let i=this.getSlice(e,t);this.endIndex=t-1,null==(n=(r=this.cbs).ontext)||n.call(r,i),this.startIndex=t}ontextentity(e){var t,r;let n=this.tokenizer.getSectionStart();this.endIndex=n-1,null==(r=(t=this.cbs).ontext)||r.call(t,e8(e)),this.startIndex=n}isVoidElement(e){return!this.options.xmlMode&&th.has(e)}onopentagname(e,t){this.endIndex=t;let r=this.getSlice(e,t);this.lowerCaseTagNames&&(r=r.toLowerCase()),this.emitOpenTag(r)}emitOpenTag(e){var t,r,n,i;this.openTagStart=this.startIndex,this.tagname=e;let a=!this.options.xmlMode&&tc.get(e);if(a)for(;this.stack.length>0&&a.has(this.stack[this.stack.length-1]);){let e=this.stack.pop();null==(r=(t=this.cbs).onclosetag)||r.call(t,e,!0)}!this.isVoidElement(e)&&(this.stack.push(e),td.has(e)?this.foreignContext.push(!0):tp.has(e)&&this.foreignContext.push(!1)),null==(i=(n=this.cbs).onopentagname)||i.call(n,e),this.cbs.onopentag&&(this.attribs={})}endOpenTag(e){var t,r;this.startIndex=this.openTagStart,this.attribs&&(null==(r=(t=this.cbs).onopentag)||r.call(t,this.tagname,this.attribs,e),this.attribs=null),this.cbs.onclosetag&&this.isVoidElement(this.tagname)&&this.cbs.onclosetag(this.tagname,!0),this.tagname=""}onopentagend(e){this.endIndex=e,this.endOpenTag(!1),this.startIndex=e+1}onclosetag(e,t){var r,n,i,a,s,o;this.endIndex=t;let l=this.getSlice(e,t);if(this.lowerCaseTagNames&&(l=l.toLowerCase()),(td.has(l)||tp.has(l))&&this.foreignContext.pop(),this.isVoidElement(l))this.options.xmlMode||"br"!==l||(null==(n=(r=this.cbs).onopentagname)||n.call(r,"br"),null==(a=(i=this.cbs).onopentag)||a.call(i,"br",{},!0),null==(o=(s=this.cbs).onclosetag)||o.call(s,"br",!1));else{let e=this.stack.lastIndexOf(l);if(-1!==e)if(this.cbs.onclosetag){let t=this.stack.length-e;for(;t--;)this.cbs.onclosetag(this.stack.pop(),0!==t)}else this.stack.length=e;else this.options.xmlMode||"p"!==l||(this.emitOpenTag("p"),this.closeCurrentTag(!0))}this.startIndex=t+1}onselfclosingtag(e){this.endIndex=e,this.options.xmlMode||this.options.recognizeSelfClosing||this.foreignContext[this.foreignContext.length-1]?(this.closeCurrentTag(!1),this.startIndex=e+1):this.onopentagend(e)}closeCurrentTag(e){var t,r;let n=this.tagname;this.endOpenTag(e),this.stack[this.stack.length-1]===n&&(null==(r=(t=this.cbs).onclosetag)||r.call(t,n,!e),this.stack.pop())}onattribname(e,t){this.startIndex=e;let r=this.getSlice(e,t);this.attribname=this.lowerCaseAttributeNames?r.toLowerCase():r}onattribdata(e,t){this.attribvalue+=this.getSlice(e,t)}onattribentity(e){this.attribvalue+=e8(e)}onattribend(e,t){var r,n;this.endIndex=t,null==(n=(r=this.cbs).onattribute)||n.call(r,this.attribname,this.attribvalue,e===nt.Double?'"':e===nt.Single?"'":e===nt.NoValue?void 0:null),this.attribs&&!Object.prototype.hasOwnProperty.call(this.attribs,this.attribname)&&(this.attribs[this.attribname]=this.attribvalue),this.attribvalue=""}getInstructionName(e){let t=e.search(tf),r=t<0?e:e.substr(0,t);return this.lowerCaseTagNames&&(r=r.toLowerCase()),r}ondeclaration(e,t){this.endIndex=t;let r=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let e=this.getInstructionName(r);this.cbs.onprocessinginstruction(`!${e}`,`!${r}`)}this.startIndex=t+1}onprocessinginstruction(e,t){this.endIndex=t;let r=this.getSlice(e,t);if(this.cbs.onprocessinginstruction){let e=this.getInstructionName(r);this.cbs.onprocessinginstruction(`?${e}`,`?${r}`)}this.startIndex=t+1}oncomment(e,t,r){var n,i,a,s;this.endIndex=t,null==(i=(n=this.cbs).oncomment)||i.call(n,this.getSlice(e,t-r)),null==(s=(a=this.cbs).oncommentend)||s.call(a),this.startIndex=t+1}oncdata(e,t,r){var n,i,a,s,o,l,u,c,h,d;this.endIndex=t;let p=this.getSlice(e,t-r);this.options.xmlMode||this.options.recognizeCDATA?(null==(i=(n=this.cbs).oncdatastart)||i.call(n),null==(s=(a=this.cbs).ontext)||s.call(a,p),null==(l=(o=this.cbs).oncdataend)||l.call(o)):(null==(c=(u=this.cbs).oncomment)||c.call(u,`[CDATA[${p}]]`),null==(d=(h=this.cbs).oncommentend)||d.call(h)),this.startIndex=t+1}onend(){var e,t;if(this.cbs.onclosetag){this.endIndex=this.startIndex;for(let e=this.stack.length;e>0;this.cbs.onclosetag(this.stack[--e],!0));}null==(t=(e=this.cbs).onend)||t.call(e)}reset(){var e,t,r,n;null==(t=(e=this.cbs).onreset)||t.call(e),this.tokenizer.reset(),this.tagname="",this.attribname="",this.attribs=null,this.stack.length=0,this.startIndex=0,this.endIndex=0,null==(n=(r=this.cbs).onparserinit)||n.call(r,this),this.buffers.length=0,this.bufferOffset=0,this.writeIndex=0,this.ended=!1}parseComplete(e){this.reset(),this.end(e)}getSlice(e,t){for(;e-this.bufferOffset>=this.buffers[0].length;)this.shiftBuffer();let r=this.buffers[0].slice(e-this.bufferOffset,t-this.bufferOffset);for(;t-this.bufferOffset>this.buffers[0].length;)this.shiftBuffer(),r+=this.buffers[0].slice(0,t-this.bufferOffset);return r}shiftBuffer(){this.bufferOffset+=this.buffers[0].length,this.writeIndex--,this.buffers.shift()}write(e){var t,r;if(this.ended){null==(r=(t=this.cbs).onerror)||r.call(t,Error(".write() after done!"));return}this.buffers.push(e),this.tokenizer.running&&(this.tokenizer.write(e),this.writeIndex++)}end(e){var t,r;if(this.ended){null==(r=(t=this.cbs).onerror)||r.call(t,Error(".end() after done!"));return}e&&this.write(e),this.ended=!0,this.tokenizer.end()}pause(){this.tokenizer.pause()}resume(){for(this.tokenizer.resume();this.tokenizer.running&&this.writeIndex<this.buffers.length;)this.tokenizer.write(this.buffers[this.writeIndex++]);this.ended&&this.tokenizer.end()}parseChunk(e){this.write(e)}done(e){this.end(e)}}let tg=/["&'<>$\x80-\uFFFF]/g,tD=new Map([[34,"&quot;"],[38,"&amp;"],[39,"&apos;"],[60,"&lt;"],[62,"&gt;"]]),ty=null!=String.prototype.codePointAt?(e,t)=>e.codePointAt(t):(e,t)=>(64512&e.charCodeAt(t))==55296?(e.charCodeAt(t)-55296)*1024+e.charCodeAt(t+1)-56320+65536:e.charCodeAt(t);function tb(e){let t,r="",n=0;for(;null!==(t=tg.exec(e));){let i=t.index,a=e.charCodeAt(i),s=tD.get(a);void 0!==s?(r+=e.substring(n,i)+s,n=i+1):(r+=`${e.substring(n,i)}&#x${ty(e,i).toString(16)};`,n=tg.lastIndex+=Number((64512&a)==55296))}return r+e.substr(n)}function tC(e,t){return function(r){let n,i=0,a="";for(;n=e.exec(r);)i!==n.index&&(a+=r.substring(i,n.index)),a+=t.get(n[0].charCodeAt(0)),i=n.index+1;return a+r.substring(i)}}tC(/[&<>'"]/g,tD);let tv=tC(/["&\u00A0]/g,new Map([[34,"&quot;"],[38,"&amp;"],[160,"&nbsp;"]])),tS=tC(/[&<>\u00A0]/g,new Map([[38,"&amp;"],[60,"&lt;"],[62,"&gt;"],[160,"&nbsp;"]]));!function(e){e[e.XML=0]="XML",e[e.HTML=1]="HTML"}(nr||(nr={})),function(e){e[e.UTF8=0]="UTF8",e[e.ASCII=1]="ASCII",e[e.Extensive=2]="Extensive",e[e.Attribute=3]="Attribute",e[e.Text=4]="Text"}(nn||(nn={}));let tw=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),tk=new Map(["definitionURL","attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),tE=new Set(["style","script","xmp","iframe","noembed","noframes","plaintext","noscript"]);function tx(e){return e.replace(/"/g,"&quot;")}let tF=new Set(["area","base","basefont","br","col","command","embed","frame","hr","img","input","isindex","keygen","link","meta","param","source","track","wbr"]);function t_(e,t={}){let r="length"in e?e:[e],n="";for(let e=0;e<r.length;e++)n+=function(e,t){var r,n,i;switch(e.type){case a:return t_(e.children,t);case p:case o:return r=e,`<${r.data}>`;case l:return n=e,`<!--${n.data}-->`;case d:return i=e,`<![CDATA[${i.children[0].data}]]>`;case u:case c:case h:return function(e,t){var r;"foreign"===t.xmlMode&&(e.name=null!=(r=tw.get(e.name))?r:e.name,e.parent&&tT.has(e.parent.name)&&(t={...t,xmlMode:!1})),!t.xmlMode&&tA.has(e.name)&&(t={...t,xmlMode:"foreign"});let n=`<${e.name}`,i=function(e,t){var r;if(!e)return;let n=(null!=(r=t.encodeEntities)?r:t.decodeEntities)===!1?tx:t.xmlMode||"utf8"!==t.encodeEntities?tb:tv;return Object.keys(e).map(r=>{var i,a;let s=null!=(i=e[r])?i:"";return("foreign"===t.xmlMode&&(r=null!=(a=tk.get(r))?a:r),t.emptyAttrs||t.xmlMode||""!==s)?`${r}="${n(s)}"`:r}).join(" ")}(e.attribs,t);return i&&(n+=` ${i}`),0===e.children.length&&(t.xmlMode?!1!==t.selfClosingTags:t.selfClosingTags&&tF.has(e.name))?(t.xmlMode||(n+=" "),n+="/>"):(n+=">",e.children.length>0&&(n+=t_(e.children,t)),(t.xmlMode||!tF.has(e.name))&&(n+=`</${e.name}>`)),n}(e,t);case s:return function(e,t){var r;let n=e.data||"";return(null!=(r=t.encodeEntities)?r:t.decodeEntities)===!1||!t.xmlMode&&e.parent&&tE.has(e.parent.name)||(n=t.xmlMode||"utf8"!==t.encodeEntities?tb(n):tS(n)),n}(e,t)}}(r[e],t);return n}let tT=new Set(["mi","mo","mn","ms","mtext","annotation-xml","foreignObject","desc","title"]),tA=new Set(["svg","math"]);function tL(e){return Array.isArray(e)?e.map(tL).join(""):isTag(e)?"br"===e.name?"\n":tL(e.children):isCDATA(e)?tL(e.children):isText(e)?e.data:""}function tB(e){return Array.isArray(e)?e.map(tB).join(""):hasChildren(e)&&!isComment(e)?tB(e.children):isText(e)?e.data:""}function tN(e){return Array.isArray(e)?e.map(tN).join(""):hasChildren(e)&&(e.type===ElementType.Tag||isCDATA(e))?tN(e.children):isText(e)?e.data:""}let tI={tag_name:e=>"function"==typeof e?t=>w(t)&&e(t.name):"*"===e?w:t=>w(t)&&t.name===e,tag_type:e=>"function"==typeof e?t=>e(t.type):t=>t.type===e,tag_contains:e=>"function"==typeof e?t=>k(t)&&e(t.data):t=>k(t)&&t.data===e};function tq(e,t){return r=>e(r)||t(r)}!function(e){e[e.DISCONNECTED=1]="DISCONNECTED",e[e.PRECEDING=2]="PRECEDING",e[e.FOLLOWING=4]="FOLLOWING",e[e.CONTAINS=8]="CONTAINS",e[e.CONTAINED_BY=16]="CONTAINED_BY"}(ni||(ni={}));var tP=r(87487);function tO(e,t,r=()=>void 0){if(void 0===e){let e=function(...r){return t(e,...r)};return e}return e>=0?function(...n){return t(tO(e-1,t,r),...n)}:r}function tR(e,t){let r=0,n=e.length;for(;r<n&&e[r]===t;)++r;for(;n>r&&e[n-1]===t;)--n;return r>0||n<e.length?e.substring(r,n):e}function tM(e,t){let r=new Map;for(let n=e.length;n-- >0;){let i=e[n],a=t(i);r.set(a,r.has(a)?tP(i,r.get(a),{arrayMerge:tW}):i)}return[...r.values()].reverse()}let tW=(e,t,r)=>[...t];function t$(e,t){for(let r of t){if(!e)return;e=e[r]}return e}function tj(e,t="a",r=26){let n=[];do n.push((e-=1)%r),e=e/r|0;while(e>0);let i=t.charCodeAt(0);return n.reverse().map(e=>String.fromCharCode(i+e)).join("")}let tH=["I","X","C","M"],tV=["V","L","D"];function tU(e){return[...e+""].map(e=>+e).reverse().map((e,t)=>e%5<4?(e<5?"":tV[t])+tH[t].repeat(e%5):tH[t]+(e<5?tV[t]:tH[t+1])).reverse().join("")}class tz{constructor(e,t){this.lines=[],this.nextLineWords=[],this.maxLineLength=t||e.wordwrap||Number.MAX_VALUE,this.nextLineAvailableChars=this.maxLineLength,this.wrapCharacters=t$(e,["longWordSplit","wrapCharacters"])||[],this.forceWrapOnLimit=t$(e,["longWordSplit","forceWrapOnLimit"])||!1,this.stashedSpace=!1,this.wordBreakOpportunity=!1}pushWord(e,t=!1){this.nextLineAvailableChars<=0&&!t&&this.startNewLine();let r=0===this.nextLineWords.length,n=e.length+ +!r;if(n<=this.nextLineAvailableChars||t)this.nextLineWords.push(e),this.nextLineAvailableChars-=n;else{let[t,...n]=this.splitLongWord(e);for(let e of(r||this.startNewLine(),this.nextLineWords.push(t),this.nextLineAvailableChars-=t.length,n))this.startNewLine(),this.nextLineWords.push(e),this.nextLineAvailableChars-=e.length}}popWord(){let e=this.nextLineWords.pop();if(void 0!==e){let t=0===this.nextLineWords.length,r=e.length+ +!t;this.nextLineAvailableChars+=r}return e}concatWord(e,t=!1){if(this.wordBreakOpportunity&&e.length>this.nextLineAvailableChars)this.pushWord(e,t),this.wordBreakOpportunity=!1;else{let r=this.popWord();this.pushWord(r?r.concat(e):e,t)}}startNewLine(e=1){this.lines.push(this.nextLineWords),e>1&&this.lines.push(...Array.from({length:e-1},()=>[])),this.nextLineWords=[],this.nextLineAvailableChars=this.maxLineLength}isEmpty(){return 0===this.lines.length&&0===this.nextLineWords.length}clear(){this.lines.length=0,this.nextLineWords.length=0,this.nextLineAvailableChars=this.maxLineLength}toString(){return[...this.lines,this.nextLineWords].map(e=>e.join(" ")).join("\n")}splitLongWord(e){let t=[],r=0;for(;e.length>this.maxLineLength;){let n=e.substring(0,this.maxLineLength),i=e.substring(this.maxLineLength),a=n.lastIndexOf(this.wrapCharacters[r]);if(a>-1)e=n.substring(a+1)+i,t.push(n.substring(0,a+1));else if(++r<this.wrapCharacters.length)e=n+i;else{if(this.forceWrapOnLimit){if(t.push(n),(e=i).length>this.maxLineLength)continue}else e=n+i;break}}return t.push(e),t}}class tG{constructor(e=null){this.next=e}getRoot(){return this.next?this.next:this}}class tJ extends tG{constructor(e,t=null,r=1,n){super(t),this.leadingLineBreaks=r,this.inlineTextBuilder=new tz(e,n),this.rawText="",this.stashedLineBreaks=0,this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class tK extends tJ{constructor(e,t=null,{interRowLineBreaks:r=1,leadingLineBreaks:n=2,maxLineLength:i,maxPrefixLength:a=0,prefixAlign:s="left"}={}){super(e,t,n,i),this.maxPrefixLength=a,this.prefixAlign=s,this.interRowLineBreaks=r}}class tQ extends tJ{constructor(e,t=null,{leadingLineBreaks:r=1,maxLineLength:n,prefix:i=""}={}){super(e,t,r,n),this.prefix=i}}class tZ extends tG{constructor(e=null){super(e),this.rows=[],this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class tX extends tG{constructor(e=null){super(e),this.cells=[],this.isPre=e&&e.isPre,this.isNoWrap=e&&e.isNoWrap}}class tY extends tG{constructor(e,t=null,r){super(t),this.inlineTextBuilder=new tz(e,r),this.rawText="",this.stashedLineBreaks=0,this.isPre=t&&t.isPre,this.isNoWrap=t&&t.isNoWrap}}class t0 extends tG{constructor(e=null,t){super(e),this.transform=t}}class t1{constructor(e){this.whitespaceChars=e.preserveNewlines?e.whitespaceCharacters.replace(/\n/g,""):e.whitespaceCharacters;let t=[...this.whitespaceChars].map(e=>"\\u"+e.charCodeAt(0).toString(16).padStart(4,"0")).join("");if(this.leadingWhitespaceRe=RegExp(`^[${t}]`),this.trailingWhitespaceRe=RegExp(`[${t}]$`),this.allWhitespaceOrEmptyRe=RegExp(`^[${t}]*$`),this.newlineOrNonWhitespaceRe=RegExp(`(\\n|[^\\n${t}])`,"g"),this.newlineOrNonNewlineStringRe=RegExp(`(\\n|[^\\n]+)`,"g"),e.preserveNewlines){let e=RegExp(`\\n|[^\\n${t}]+`,"gm");this.shrinkWrapAdd=function(t,r,n=e=>e,i=!1){if(!t)return;let a=r.stashedSpace,s=!1,o=e.exec(t);if(o)for(s=!0,"\n"===o[0]?r.startNewLine():a||this.testLeadingWhitespace(t)?r.pushWord(n(o[0]),i):r.concatWord(n(o[0]),i);null!==(o=e.exec(t));)"\n"===o[0]?r.startNewLine():r.pushWord(n(o[0]),i);r.stashedSpace=a&&!s||this.testTrailingWhitespace(t)}}else{let e=RegExp(`[^${t}]+`,"g");this.shrinkWrapAdd=function(t,r,n=e=>e,i=!1){if(!t)return;let a=r.stashedSpace,s=!1,o=e.exec(t);if(o)for(s=!0,a||this.testLeadingWhitespace(t)?r.pushWord(n(o[0]),i):r.concatWord(n(o[0]),i);null!==(o=e.exec(t));)r.pushWord(n(o[0]),i);r.stashedSpace=a&&!s||this.testTrailingWhitespace(t)}}}addLiteral(e,t,r=!0){if(!e)return;let n=t.stashedSpace,i=!1,a=this.newlineOrNonNewlineStringRe.exec(e);if(a)for(i=!0,"\n"===a[0]?t.startNewLine():n?t.pushWord(a[0],r):t.concatWord(a[0],r);null!==(a=this.newlineOrNonNewlineStringRe.exec(e));)"\n"===a[0]?t.startNewLine():t.pushWord(a[0],r);t.stashedSpace=n&&!i}testLeadingWhitespace(e){return this.leadingWhitespaceRe.test(e)}testTrailingWhitespace(e){return this.trailingWhitespaceRe.test(e)}testContainsWords(e){return!this.allWhitespaceOrEmptyRe.test(e)}countNewlinesNoWords(e){let t;this.newlineOrNonWhitespaceRe.lastIndex=0;let r=0;for(;null!==(t=this.newlineOrNonWhitespaceRe.exec(e));)if("\n"!==t[0])return 0;else r++;return r}}class t2{constructor(e,t,r){this.options=e,this.picker=t,this.metadata=r,this.whitespaceProcessor=new t1(e),this._stackItem=new tJ(e),this._wordTransformer=void 0}pushWordTransform(e){this._wordTransformer=new t0(this._wordTransformer,e)}popWordTransform(){if(!this._wordTransformer)return;let e=this._wordTransformer.transform;return this._wordTransformer=this._wordTransformer.next,e}startNoWrap(){this._stackItem.isNoWrap=!0}stopNoWrap(){this._stackItem.isNoWrap=!1}_getCombinedWordTransformer(){let e=this._wordTransformer?e=>(function e(t,r){return r?e(r.transform(t),r.next):t})(e,this._wordTransformer):void 0,t=this.options.encodeCharacters;return e?t?r=>t(e(r)):e:t}_popStackItem(){let e=this._stackItem;return this._stackItem=e.next,e}addLineBreak(){(this._stackItem instanceof tJ||this._stackItem instanceof tQ||this._stackItem instanceof tY)&&(this._stackItem.isPre?this._stackItem.rawText+="\n":this._stackItem.inlineTextBuilder.startNewLine())}addWordBreakOpportunity(){(this._stackItem instanceof tJ||this._stackItem instanceof tQ||this._stackItem instanceof tY)&&(this._stackItem.inlineTextBuilder.wordBreakOpportunity=!0)}addInline(e,{noWordTransform:t=!1}={}){if(this._stackItem instanceof tJ||this._stackItem instanceof tQ||this._stackItem instanceof tY){if(this._stackItem.isPre){this._stackItem.rawText+=e;return}if(0!==e.length&&(!this._stackItem.stashedLineBreaks||this.whitespaceProcessor.testContainsWords(e))){if(this.options.preserveNewlines){let t=this.whitespaceProcessor.countNewlinesNoWords(e);if(t>0)return void this._stackItem.inlineTextBuilder.startNewLine(t)}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.shrinkWrapAdd(e,this._stackItem.inlineTextBuilder,t?void 0:this._getCombinedWordTransformer(),this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}}addLiteral(e){if((this._stackItem instanceof tJ||this._stackItem instanceof tQ||this._stackItem instanceof tY)&&0!==e.length){if(this._stackItem.isPre){this._stackItem.rawText+=e;return}this._stackItem.stashedLineBreaks&&this._stackItem.inlineTextBuilder.startNewLine(this._stackItem.stashedLineBreaks),this.whitespaceProcessor.addLiteral(e,this._stackItem.inlineTextBuilder,this._stackItem.isNoWrap),this._stackItem.stashedLineBreaks=0}}openBlock({leadingLineBreaks:e=1,reservedLineLength:t=0,isPre:r=!1}={}){let n=Math.max(20,this._stackItem.inlineTextBuilder.maxLineLength-t);this._stackItem=new tJ(this.options,this._stackItem,e,n),r&&(this._stackItem.isPre=!0)}closeBlock({trailingLineBreaks:e=1,blockTransform:t}={}){let r=this._popStackItem(),n=t?t(t3(r)):t3(r);t8(this._stackItem,n,r.leadingLineBreaks,Math.max(r.stashedLineBreaks,e))}openList({maxPrefixLength:e=0,prefixAlign:t="left",interRowLineBreaks:r=1,leadingLineBreaks:n=2}={}){this._stackItem=new tK(this.options,this._stackItem,{interRowLineBreaks:r,leadingLineBreaks:n,maxLineLength:this._stackItem.inlineTextBuilder.maxLineLength,maxPrefixLength:e,prefixAlign:t})}openListItem({prefix:e=""}={}){if(!(this._stackItem instanceof tK))throw Error("Can't add a list item to something that is not a list! Check the formatter.");let t=this._stackItem,r=Math.max(e.length,t.maxPrefixLength),n=Math.max(20,t.inlineTextBuilder.maxLineLength-r);this._stackItem=new tQ(this.options,t,{prefix:e,maxLineLength:n,leadingLineBreaks:t.interRowLineBreaks})}closeListItem(){let e=this._popStackItem(),t=e.next,r=Math.max(e.prefix.length,t.maxPrefixLength),n="\n"+" ".repeat(r),i=("right"===t.prefixAlign?e.prefix.padStart(r):e.prefix.padEnd(r))+t3(e).replace(/\n/g,n);t8(t,i,e.leadingLineBreaks,Math.max(e.stashedLineBreaks,t.interRowLineBreaks))}closeList({trailingLineBreaks:e=2}={}){let t=this._popStackItem(),r=t3(t);r&&t8(this._stackItem,r,t.leadingLineBreaks,e)}openTable(){this._stackItem=new tZ(this._stackItem)}openTableRow(){if(!(this._stackItem instanceof tZ))throw Error("Can't add a table row to something that is not a table! Check the formatter.");this._stackItem=new tX(this._stackItem)}openTableCell({maxColumnWidth:e}={}){if(!(this._stackItem instanceof tX))throw Error("Can't add a table cell to something that is not a table row! Check the formatter.");this._stackItem=new tY(this.options,this._stackItem,e)}closeTableCell({colspan:e=1,rowspan:t=1}={}){let r=this._popStackItem(),n=tR(t3(r),"\n");r.next.cells.push({colspan:e,rowspan:t,text:n})}closeTableRow(){let e=this._popStackItem();e.next.rows.push(e.cells)}closeTable({tableToString:e,leadingLineBreaks:t=2,trailingLineBreaks:r=2}){let n=e(this._popStackItem().rows);n&&t8(this._stackItem,n,t,r)}toString(){return t3(this._stackItem.getRoot())}}function t3(e){if(!(e instanceof tJ||e instanceof tQ||e instanceof tY))throw Error("Only blocks, list items and table cells can be requested for text contents.");return e.inlineTextBuilder.isEmpty()?e.rawText:e.rawText+e.inlineTextBuilder.toString()}function t8(e,t,r,n){if(!(e instanceof tJ||e instanceof tQ||e instanceof tY))throw Error("Only blocks, list items and table cells can contain text.");let i=t3(e),a=Math.max(e.stashedLineBreaks,r);e.inlineTextBuilder.clear(),i?e.rawText=i+"\n".repeat(a)+t:(e.rawText=t,e.leadingLineBreaks=a),e.stashedLineBreaks=n}function t6(e,t,r){if(!t)return;let n=r.options;for(let i of(t.length>n.limits.maxChildNodes&&(t=t.slice(0,n.limits.maxChildNodes)).push({data:n.limits.ellipsis,type:"text"}),t))switch(i.type){case"text":r.addInline(i.data);break;case"tag":{let t=r.picker.pick1(i);(0,n.formatters[t.format])(i,e,r,t.options||{})}}}function t5(e){let t=e.attribs&&e.attribs.length?" "+Object.entries(e.attribs).map(([e,t])=>""===t?e:`${e}=${t.replace(/"/g,"&quot;")}`).join(" "):"";return`<${e.name}${t}>`}function t7(e){return`</${e.name}>`}var t9=Object.freeze({__proto__:null,block:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),t(e.children,r),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockHtml:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),r.startNoWrap(),r.addLiteral(t_(e,{decodeEntities:r.options.decodeEntities})),r.stopNoWrap(),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockString:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),r.addLiteral(n.string||""),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},blockTag:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),r.startNoWrap(),r.addLiteral(t5(e)),r.stopNoWrap(),t(e.children,r),r.startNoWrap(),r.addLiteral(t7(e)),r.stopNoWrap(),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},inline:function(e,t,r,n){t(e.children,r)},inlineHtml:function(e,t,r,n){r.startNoWrap(),r.addLiteral(t_(e,{decodeEntities:r.options.decodeEntities})),r.stopNoWrap()},inlineString:function(e,t,r,n){r.addLiteral(n.string||"")},inlineSurround:function(e,t,r,n){r.addLiteral(n.prefix||""),t(e.children,r),r.addLiteral(n.suffix||"")},inlineTag:function(e,t,r,n){r.startNoWrap(),r.addLiteral(t5(e)),r.stopNoWrap(),t(e.children,r),r.startNoWrap(),r.addLiteral(t7(e)),r.stopNoWrap()},skip:function(e,t,r,n){}});function t4(e,t){return e[t]||(e[t]=[]),e[t]}function re(e,t){return void 0===e[t]&&(e[t]=0===t?0:1+re(e,t-1)),e[t]}function rt(e,t,r,n){e[t+r]=Math.max(re(e,t+r),re(e,t)+n)}function rr(e,t){return t?("string"==typeof t[0]?t[0]:"[")+e+("string"==typeof t[1]?t[1]:"]"):e}function rn(e,t,r,n,i){let a="function"==typeof t?t(e,n,i):e;return"/"===a[0]&&r?function(e,t){let r=e.length;for(;r>0&&"/"===e[r-1];)--r;return r<e.length?e.substring(0,r):e}(r,0)+a:a}function ri(e,t,r,n,i){let a="li"===t$(e,["parent","name"]),s=0,o=(e.children||[]).filter(e=>"text"!==e.type||!/^\s*$/.test(e.data)).map(function(e){if("li"!==e.name)return{node:e,prefix:""};let t=a?i().trimStart():i();return t.length>s&&(s=t.length),{node:e,prefix:t}});if(o.length){for(let{node:e,prefix:i}of(r.openList({interRowLineBreaks:1,leadingLineBreaks:a?1:n.leadingLineBreaks||2,maxPrefixLength:s,prefixAlign:"left"}),o))r.openListItem({prefix:i}),t([e],r),r.closeListItem();r.closeList({trailingLineBreaks:a?1:n.trailingLineBreaks||2})}}function ra(e,t,r,n){function i(e){let i=+t$(e,["attribs","colspan"])||1,a=+t$(e,["attribs","rowspan"])||1;r.openTableCell({maxColumnWidth:n.maxColumnWidth}),t(e.children,r),r.closeTableCell({colspan:i,rowspan:a})}r.openTable(),e.children.forEach(function e(t){if("tag"!==t.type)return;let a=!1!==n.uppercaseHeaderCells?e=>{r.pushWordTransform(e=>e.toUpperCase()),i(e),r.popWordTransform()}:i;switch(t.name){case"thead":case"tbody":case"tfoot":case"center":t.children.forEach(e);return;case"tr":for(let e of(r.openTableRow(),t.children))if("tag"===e.type)switch(e.name){case"th":a(e);break;case"td":i(e)}r.closeTableRow()}}),r.closeTable({tableToString:e=>(function(e,t,r){let n=[],i=0,a=e.length,s=[0];for(let r=0;r<a;r++){let a=t4(n,r),u=e[r],c=0;for(let e=0;e<u.length;e++){let i=u[e];c=function(e,t=0){for(;e[t];)t++;return t}(a,c);var o=r,l=c;for(let e=0;e<i.rowspan;e++){let t=t4(n,o+e);for(let e=0;e<i.colspan;e++)t[l+e]=i}c+=i.colspan,i.lines=i.text.split("\n");let h=i.lines.length;rt(s,r,i.rowspan,h+t)}i=a.length>i?a.length:i}!function(e,t){for(let r=0;r<t;r++){let t=t4(e,r);for(let n=0;n<r;n++){let i=t4(e,n);if(t[n]||i[r]){let e=t[n];t[n]=i[r],i[r]=e}}}}(n,a>i?a:i);let u=[],c=[0];for(let e=0;e<i;e++){let t,i=0,o=Math.min(a,n[e].length);for(;i<o;)if(t=n[e][i]){if(!t.rendered){let n=0;for(let r=0;r<t.lines.length;r++){let a=t.lines[r],o=s[i]+r;u[o]=(u[o]||"").padEnd(c[e])+a,n=a.length>n?a.length:n}rt(c,e,t.colspan,n+r),t.rendered=!0}i+=t.rowspan}else{let e=s[i];u[e]=u[e]||"",i++}}return u.join("\n")})(e,n.rowSpacing??0,n.colSpacing??3),leadingLineBreaks:n.leadingLineBreaks,trailingLineBreaks:n.trailingLineBreaks})}var rs=Object.freeze({__proto__:null,anchor:function(e,t,r,n){let i=function(){if(n.ignoreHref||!e.attribs||!e.attribs.href)return"";let t=e.attribs.href.replace(/^mailto:/,"");return n.noAnchorUrl&&"#"===t[0]?"":t=rn(t,n.pathRewrite,n.baseUrl,r.metadata,e)}();if(i){let a="";r.pushWordTransform(e=>(e&&(a+=e),e)),t(e.children,r),r.popWordTransform(),n.hideLinkHrefIfSameAsText&&i===a||r.addInline(a?" "+rr(i,n.linkBrackets):i,{noWordTransform:!0})}else t(e.children,r)},blockquote:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2,reservedLineLength:2}),t(e.children,r),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2,blockTransform:e=>(!1!==n.trimEmptyLines?tR(e,"\n"):e).split("\n").map(e=>"> "+e).join("\n")})},dataTable:ra,heading:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),!1!==n.uppercase?(r.pushWordTransform(e=>e.toUpperCase()),t(e.children,r),r.popWordTransform()):t(e.children,r),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},horizontalLine:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),r.addInline("-".repeat(n.length||r.options.wordwrap||40)),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},image:function(e,t,r,n){let i=e.attribs||{},a=i.alt?i.alt:"",s=i.src?rn(i.src,n.pathRewrite,n.baseUrl,r.metadata,e):"",o=s?a?a+" "+rr(s,n.linkBrackets):rr(s,n.linkBrackets):a;r.addInline(o,{noWordTransform:!0})},lineBreak:function(e,t,r,n){r.addLineBreak()},orderedList:function(e,t,r,n){let i=Number(e.attribs.start||"1"),a=function(e="1"){switch(e){case"a":return e=>tj(e,"a");case"A":return e=>tj(e,"A");case"i":return e=>tU(e).toLowerCase();case"I":return e=>tU(e);default:return e=>e.toString()}}(e.attribs.type);return ri(e,t,r,n,()=>" "+a(i++)+". ")},paragraph:function(e,t,r,n){r.openBlock({leadingLineBreaks:n.leadingLineBreaks||2}),t(e.children,r),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},pre:function(e,t,r,n){r.openBlock({isPre:!0,leadingLineBreaks:n.leadingLineBreaks||2}),t(e.children,r),r.closeBlock({trailingLineBreaks:n.trailingLineBreaks||2})},table:function(e,t,r,n){var i,a,s,o;return!function(e,t){if(!0===t)return!0;if(!e)return!1;let{classes:r,ids:n}=function(e){let t=[],r=[];for(let n of e)n.startsWith(".")?t.push(n.substring(1)):n.startsWith("#")&&r.push(n.substring(1));return{classes:t,ids:r}}(t),i=(e.class||"").split(" "),a=(e.id||"").split(" ");return i.some(e=>r.includes(e))||a.some(e=>n.includes(e))}(e.attribs,r.options.tables)?(i=e,a=t,s=r,o=n,void(s.openBlock({leadingLineBreaks:o.leadingLineBreaks}),a(i.children,s),s.closeBlock({trailingLineBreaks:o.trailingLineBreaks}))):ra(e,t,r,n)},unorderedList:function(e,t,r,n){let i=n.itemPrefix||" * ";return ri(e,t,r,n,()=>i)},wbr:function(e,t,r,n){r.addWordBreakOpportunity()}});let ro={baseElements:{selectors:["body"],orderBy:"selectors",returnDomByDefault:!0},decodeEntities:!0,encodeCharacters:{},formatters:{},limits:{ellipsis:"...",maxBaseElements:void 0,maxChildNodes:void 0,maxDepth:void 0,maxInputLength:0x1000000},longWordSplit:{forceWrapOnLimit:!1,wrapCharacters:[]},preserveNewlines:!1,selectors:[{selector:"*",format:"inline"},{selector:"a",format:"anchor",options:{baseUrl:null,hideLinkHrefIfSameAsText:!1,ignoreHref:!1,linkBrackets:["[","]"],noAnchorUrl:!0}},{selector:"article",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"aside",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"blockquote",format:"blockquote",options:{leadingLineBreaks:2,trailingLineBreaks:2,trimEmptyLines:!0}},{selector:"br",format:"lineBreak"},{selector:"div",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"footer",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"form",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"h1",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h2",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h3",format:"heading",options:{leadingLineBreaks:3,trailingLineBreaks:2,uppercase:!0}},{selector:"h4",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h5",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"h6",format:"heading",options:{leadingLineBreaks:2,trailingLineBreaks:2,uppercase:!0}},{selector:"header",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"hr",format:"horizontalLine",options:{leadingLineBreaks:2,length:void 0,trailingLineBreaks:2}},{selector:"img",format:"image",options:{baseUrl:null,linkBrackets:["[","]"]}},{selector:"main",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"nav",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"ol",format:"orderedList",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"p",format:"paragraph",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"pre",format:"pre",options:{leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"section",format:"block",options:{leadingLineBreaks:1,trailingLineBreaks:1}},{selector:"table",format:"table",options:{colSpacing:3,leadingLineBreaks:2,maxColumnWidth:60,rowSpacing:0,trailingLineBreaks:2,uppercaseHeaderCells:!0}},{selector:"ul",format:"unorderedList",options:{itemPrefix:" * ",leadingLineBreaks:2,trailingLineBreaks:2}},{selector:"wbr",format:"wbr"}],tables:[],whitespaceCharacters:" 	\r\n\f​",wordwrap:80},rl=(e,t,r)=>[...e,...t],ru=(e,t,r)=>[...t],rc=(e,t,r)=>e.some(e=>"object"==typeof e)?rl(e,t):ru(e,t);var rh=r(26101),rd=Object.defineProperty,rp=e=>{throw TypeError(e)},rf=(e,t,r)=>t in e?rd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,rm=(e,t)=>{for(var r in t)rd(e,r,{get:t[r],enumerable:!0})},rg=(e,t,r)=>rf(e,"symbol"!=typeof t?t+"":t,r),rD=(e,t,r)=>t.has(e)||rp("Cannot "+r),ry=(e,t,r)=>(rD(e,t,"read from private field"),r?r.call(e):t.get(e)),rb=(e,t,r)=>t.has(e)?rp("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),rC=(e,t,r,n)=>(rD(e,t,"write to private field"),n?n.call(e,r):t.set(e,r),r),rv={};rm(rv,{languages:()=>af,options:()=>aD,parsers:()=>ay,printers:()=>sV});var rS=(e,t,r,n)=>{if(!(e&&null==t))return t.replaceAll?t.replaceAll(r,n):r.global?t.replace(r,n):t.split(r).join(n)},rw="string",rk="array",rE="cursor",rx="indent",rF="align",r_="trim",rT="group",rA="fill",rL="if-break",rB="indent-if-break",rN="line-suffix",rI="line-suffix-boundary",rq="line",rP="label",rO="break-parent",rR=new Set([rE,rx,rF,r_,rT,rA,rL,rB,rN,rI,rq,rP,rO]),rM=(e,t,r)=>{if(!(e&&null==t))return Array.isArray(t)||"string"==typeof t?t[r<0?t.length+r:r]:t.at(r)},rW=function(e){if("string"==typeof e)return rw;if(Array.isArray(e))return rk;if(!e)return;let{type:t}=e;if(rR.has(t))return t},r$=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e),rj=class extends Error{name="InvalidDocError";constructor(e){super(function(e){let t=null===e?"null":typeof e;if("string"!==t&&"object"!==t)return`Unexpected doc '${t}', 
Expected it to be 'string' or 'object'.`;if(rW(e))throw Error("doc is valid.");let r=Object.prototype.toString.call(e);if("[object Object]"!==r)return`Unexpected doc '${r}'.`;let n=r$([...rR].map(e=>`'${e}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}(e)),this.doc=e}};function rH(e,t){if("string"==typeof e)return t(e);let r=new Map;return function e(n){if(r.has(n))return r.get(n);let i=function(r){switch(rW(r)){case rk:return t(r.map(e));case rA:return t({...r,parts:r.parts.map(e)});case rL:return t({...r,breakContents:e(r.breakContents),flatContents:e(r.flatContents)});case rT:{let{expandedStates:n,contents:i}=r;return i=n?(n=n.map(e))[0]:e(i),t({...r,contents:i,expandedStates:n})}case rF:case rx:case rB:case rP:case rN:return t({...r,contents:e(r.contents)});case rw:case rE:case r_:case rI:case rq:case rO:return t(r);default:throw new rj(r)}}(n);return r.set(n,i),i}(e)}function rV(e,t=r1){return rH(e,e=>"string"==typeof e?r2(t,e.split(`
`)):e)}var rU=()=>{};function rz(e){return rU(e),{type:rx,contents:e}}function rG(e,t){return rU(t),{type:rF,contents:t,n:e}}function rJ(e,t={}){return rU(e),rU(t.expandedStates,!0),{type:rT,id:t.id,contents:e,break:!!t.shouldBreak,expandedStates:t.expandedStates}}function rK(e){return rU(e),{type:rA,parts:e}}function rQ(e,t="",r={}){return rU(e),""!==t&&rU(t),{type:rL,breakContents:e,flatContents:t,groupId:r.groupId}}var rZ={type:rO},rX={type:rq},rY={type:rq,soft:!0},r0=[{type:rq,hard:!0},rZ],r1=[{type:rq,hard:!0,literal:!0},rZ];function r2(e,t){rU(e),rU(t);let r=[];for(let n=0;n<t.length;n++)0!==n&&r.push(e),r.push(t[n]);return r}var r3,r8,r6,r5,r7,r9,r4,ne,nt,nr,nn,ni,na,ns=function(e,t){let r=!0===t||"'"===t?"'":'"',n="'"===r?'"':"'",i=0,a=0;for(let t of e)t===r?i++:t===n&&a++;return i>a?n:r},no=class{constructor(e){rb(this,na),rC(this,na,new Set(e))}getLeadingWhitespaceCount(e){let t=ry(this,na),r=0;for(let n=0;n<e.length&&t.has(e.charAt(n));n++)r++;return r}getTrailingWhitespaceCount(e){let t=ry(this,na),r=0;for(let n=e.length-1;n>=0&&t.has(e.charAt(n));n--)r++;return r}getLeadingWhitespace(e){let t=this.getLeadingWhitespaceCount(e);return e.slice(0,t)}getTrailingWhitespace(e){let t=this.getTrailingWhitespaceCount(e);return e.slice(e.length-t)}hasLeadingWhitespace(e){return ry(this,na).has(e.charAt(0))}hasTrailingWhitespace(e){return ry(this,na).has(rM(!1,e,-1))}trimStart(e){let t=this.getLeadingWhitespaceCount(e);return e.slice(t)}trimEnd(e){let t=this.getTrailingWhitespaceCount(e);return e.slice(0,e.length-t)}trim(e){return this.trimEnd(this.trimStart(e))}split(e,t=!1){let r=`[${function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}([...ry(this,na)].join(""))}]+`,n=RegExp(t?`(${r})`:r,"u");return e.split(n)}hasWhitespaceCharacter(e){let t=ry(this,na);return Array.prototype.some.call(e,e=>t.has(e))}hasNonWhitespaceCharacter(e){let t=ry(this,na);return Array.prototype.some.call(e,e=>!t.has(e))}isWhitespaceOnly(e){let t=ry(this,na);return Array.prototype.every.call(e,e=>t.has(e))}};na=new WeakMap;var nl=new no(["	",`
`,"\f","\r"," "]),nu=class extends Error{name="UnexpectedNodeError";constructor(e,t,r="type"){super(`Unexpected ${t} node ${r}: ${JSON.stringify(e[r])}.`),this.node=e}},nc=function(e){return(null==e?void 0:e.type)==="front-matter"},nh=new Set(["sourceSpan","startSourceSpan","endSourceSpan","nameSpan","valueSpan","keySpan","tagDefinition","tokens","valueTokens","switchValueSourceSpan","expSourceSpan","valueSourceSpan"]),nd=new Set(["if","else if","for","switch","case"]);function np(e,t){var r;if("text"===e.type||"comment"===e.type||nc(e)||"yaml"===e.type||"toml"===e.type)return null;if("attribute"===e.type&&delete t.value,"docType"===e.type&&delete t.value,"angularControlFlowBlock"===e.type&&null!=(r=e.parameters)&&r.children)for(let r of t.parameters.children)nd.has(e.name)?delete r.expression:r.expression=r.expression.trim();"angularIcuExpression"===e.type&&(t.switchValue=e.switchValue.trim()),"angularLetDeclarationInitializer"===e.type&&delete t.value}async function nf(e,t){if("yaml"===e.language){let r=e.value.trim(),n=r?await t(r,{parser:"yaml"}):"";return rG({type:"root"},[e.startDelimiter,e.explicitLanguage,r0,n,n?r0:"",e.endDelimiter])}}function nm(e,t=!0){return[rz([rY,e]),t?rY:""]}function ng(e,t){let r="NGRoot"===e.type?"NGMicrosyntax"===e.node.type&&1===e.node.body.length&&"NGMicrosyntaxExpression"===e.node.body[0].type?e.node.body[0].expression:e.node:"JsExpressionRoot"===e.type?e.node:e;return r&&("ObjectExpression"===r.type||"ArrayExpression"===r.type||("__vue_expression"===t.parser||"__vue_ts_expression"===t.parser)&&("TemplateLiteral"===r.type||"StringLiteral"===r.type))}async function nD(e,t,r,n){r={__isInHtmlAttribute:!0,__embeddedInHtml:!0,...r};let i=!0;n&&(r.__onHtmlBindingRoot=(e,t)=>{i=n(e,t)});let a=await t(e,r,t);return i?rJ(a):nm(a)}np.ignoredProperties=nh;var ny,nb,nC,nv,nS,nw=function(e,t,r,n){let{node:i}=r,a=n.originalText.slice(i.sourceSpan.start.offset,i.sourceSpan.end.offset);return/^\s*$/u.test(a)?"":nD(a,e,{parser:"__ng_directive",__isInHtmlAttribute:!1},ng)},nk=(e,t)=>{if(!(e&&null==t))return t.toReversed||!Array.isArray(t)?t.toReversed():[...t].reverse()},nE=function(e){return Array.isArray(e)&&e.length>0},nx=(null==(ny=globalThis.Deno)?void 0:ny.build.os)==="windows"||(null==(nC=null==(nb=globalThis.navigator)?void 0:nb.platform)?void 0:nC.startsWith("Win"))||(null==(nS=null==(nv=globalThis.process)?void 0:nv.platform)?void 0:nS.startsWith("win"))||!1;function nF(e){if("file:"!==(e=e instanceof URL?e:new URL(e)).protocol)throw TypeError(`URL must be a file URL: received "${e.protocol}"`);return e}var n_=function(e){var t,r;let n;return nx?(n=decodeURIComponent((t=nF(t=e)).pathname.replace(/\//g,"\\").replace(/%(?![0-9A-Fa-f]{2})/g,"%25")).replace(/^\\*([A-Za-z]:)(\\|$)/,"$1\\"),""!==t.hostname&&(n=`\\\\${t.hostname}${n}`),n):decodeURIComponent(nF(e).pathname.replace(/%(?![0-9A-Fa-f]{2})/g,"%25"))},nT=e=>String(e).split(/[/\\]/u).pop();function nA(e,t){if(!t)return;let r=nT(t).toLowerCase();return e.find(({filenames:e})=>null==e?void 0:e.some(e=>e.toLowerCase()===r))??e.find(({extensions:e})=>null==e?void 0:e.some(e=>r.endsWith(e)))}function nL(e,t){if(t){if(String(t).startsWith("file:"))try{t=n_(t)}catch{return}if("string"==typeof t)return e.find(({isSupported:e})=>null==e?void 0:e({filepath:t}))}}var nB=function(e,t){let r=nk(!1,e.plugins).flatMap(e=>e.languages??[]),n=function(e,t){if(t)return e.find(({name:e})=>e.toLowerCase()===t)??e.find(({aliases:e})=>null==e?void 0:e.includes(t))??e.find(({extensions:e})=>null==e?void 0:e.includes(`.${t}`))}(r,t.language)??nA(r,t.physicalFile)??nA(r,t.file)??nL(r,t.physicalFile)??nL(r,t.file)??void t.physicalFile;return null==n?void 0:n.parsers[0]},nN={area:"none",base:"none",basefont:"none",datalist:"none",head:"none",link:"none",meta:"none",noembed:"none",noframes:"none",param:"block",rp:"none",script:"block",style:"none",template:"inline",title:"none",html:"block",body:"block",address:"block",blockquote:"block",center:"block",dialog:"block",div:"block",figure:"block",figcaption:"block",footer:"block",form:"block",header:"block",hr:"block",legend:"block",listing:"block",main:"block",p:"block",plaintext:"block",pre:"block",search:"block",xmp:"block",slot:"contents",ruby:"ruby",rt:"ruby-text",article:"block",aside:"block",h1:"block",h2:"block",h3:"block",h4:"block",h5:"block",h6:"block",hgroup:"block",nav:"block",section:"block",dir:"block",dd:"block",dl:"block",dt:"block",menu:"block",ol:"block",ul:"block",li:"list-item",table:"table",caption:"table-caption",colgroup:"table-column-group",col:"table-column",thead:"table-header-group",tbody:"table-row-group",tfoot:"table-footer-group",tr:"table-row",td:"table-cell",th:"table-cell",input:"inline-block",button:"inline-block",fieldset:"block",details:"block",summary:"block",marquee:"inline-block",source:"block",track:"block",meter:"inline-block",progress:"inline-block",object:"inline-block",video:"inline-block",audio:"inline-block",select:"inline-block",option:"block",optgroup:"block"},nI={listing:"pre",plaintext:"pre",pre:"pre",xmp:"pre",nobr:"nowrap",table:"initial",textarea:"pre-wrap"},nq=function(e){return"element"===e.type&&!e.hasExplicitNamespace&&!["html","svg"].includes(e.namespace)},nP=e=>rS(!1,e,/^[\t\f\r ]*\n/gu,""),nO=e=>nP(nl.trimEnd(e)),nR=e=>{let t=e,r=nl.getLeadingWhitespace(t);r&&(t=t.slice(r.length));let n=nl.getTrailingWhitespace(t);return n&&(t=t.slice(0,-n.length)),{leadingWhitespace:r,trailingWhitespace:n,text:t}};function nM(e,t){return!!("ieConditionalComment"===e.type&&e.lastChild&&!e.lastChild.isSelfClosing&&!e.lastChild.endSourceSpan||"ieConditionalComment"===e.type&&!e.complete||n0(e)&&e.children.some(e=>"text"!==e.type&&"interpolation"!==e.type)||n9(e,t)&&!nj(e,t)&&"interpolation"!==e.type)}function nW(e){var t;return"attribute"!==e.type&&!!e.parent&&!!e.prev&&"comment"===(t=e.prev).type&&"prettier-ignore"===t.value.trim()}function n$(e){return"text"===e.type||"comment"===e.type}function nj(e,t){return"element"===e.type&&("script"===e.fullName||"style"===e.fullName||"svg:style"===e.fullName||"svg:script"===e.fullName||"mj-style"===e.fullName&&"mjml"===t.parser||nq(e)&&("script"===e.name||"style"===e.name))}function nH(e){return n1(e).startsWith("pre")}function nV(e){return nc(e)||e.next&&e.sourceSpan.end&&e.sourceSpan.end.line+1<e.next.sourceSpan.start.line}function nU(e){return"element"===e.type&&e.children.length>0&&(["html","head","ul","ol","select"].includes(e.name)||e.cssDisplay.startsWith("table")&&"table-cell"!==e.cssDisplay)}function nz(e){var t,r;return nK(e)||e.prev&&(nK(t=e.prev)||"element"===t.type&&"br"===t.fullName||function(e){return nG(e)&&nJ(e)}(t))||nG(r=e)&&nJ(r)}function nG(e){return e.hasLeadingSpaces&&(e.prev?e.prev.sourceSpan.end.line<e.sourceSpan.start.line:"root"===e.parent.type||e.parent.startSourceSpan.end.line<e.sourceSpan.start.line)}function nJ(e){return e.hasTrailingSpaces&&(e.next?e.next.sourceSpan.start.line>e.sourceSpan.end.line:"root"===e.parent.type||e.parent.endSourceSpan&&e.parent.endSourceSpan.start.line>e.sourceSpan.end.line)}function nK(e){switch(e.type){case"ieConditionalComment":case"comment":case"directive":return!0;case"element":return["script","select"].includes(e.name)}return!1}function nQ(e){return e.lastChild?nQ(e.lastChild):e}function nZ(e){if(e)switch(e){case"module":case"text/javascript":case"text/babel":case"text/jsx":case"application/javascript":return"babel";case"application/x-typescript":return"typescript";case"text/markdown":return"markdown";case"text/html":return"html";case"text/x-handlebars-template":return"glimmer";default:if(e.endsWith("json")||e.endsWith("importmap")||"speculationrules"===e)return"json"}}function nX(e,t){return function(e,t){let{name:r,attrMap:n}=e;if("script"!==r||Object.prototype.hasOwnProperty.call(n,"src"))return;let{type:i,lang:a}=e.attrMap;return a||i?nB(t,{language:a})??nZ(i):"babel"}(e,t)??function(e,t){if("style"===e.name){let{lang:r}=e.attrMap;return r?nB(t,{language:r}):"css"}if("mj-style"===e.name&&"mjml"===t.parser)return"css"}(e,t)??function(e,t){if(!n9(e,t))return;let{attrMap:r}=e;if(Object.prototype.hasOwnProperty.call(r,"src"))return;let{type:n,lang:i}=r;return nB(t,{language:i})??nZ(n)}(e,t)}function nY(e){return"block"===e||"list-item"===e||e.startsWith("table")}function n0(e){return n1(e).startsWith("pre")}function n1(e){return"element"===e.type&&(!e.namespace||nq(e))&&Object.prototype.hasOwnProperty.call(nI,e.name)?nI[e.name]:"normal"}function n2(e,t=function(e){let t=Number.POSITIVE_INFINITY;for(let r of e.split(`
`)){if(0===r.length)continue;let e=nl.getLeadingWhitespaceCount(r);if(0===e)return 0;r.length!==e&&e<t&&(t=e)}return t===Number.POSITIVE_INFINITY?0:t}(e)){return 0===t?e:e.split(`
`).map(e=>e.slice(t)).join(`
`)}function n3(e){return rS(!1,rS(!1,e,"&apos;","'"),"&quot;",'"')}function n8(e){return n3(e.value)}var n6=new Set(["template","style","script"]);function n5(e,t){return n7(e,t)&&!n6.has(e.fullName)}function n7(e,t){return"vue"===t.parser&&"element"===e.type&&"root"===e.parent.type&&"html"!==e.fullName.toLowerCase()}function n9(e,t){return n7(e,t)&&(n5(e,t)||e.attrMap.lang&&"html"!==e.attrMap.lang)}function n4(e,t=e.value){return e.parent.isWhitespaceSensitive?e.parent.isIndentationSensitive?rV(t):rV(n2(nO(t)),r0):r2(rX,nl.split(t))}function ie(e,t){return n7(e,t)&&"script"===e.name}var it=/\{\{(.+?)\}\}/su;async function ir(e,t){let r=[];for(let[n,i]of e.split(it).entries())if(n%2==0)r.push(rV(i));else try{r.push(rJ(["{{",rz([rX,await nD(i,t,{parser:"__ng_interpolation",__isInHtmlInterpolation:!0})]),rX,"}}"]))}catch{r.push("{{",rV(i),"}}")}return r}function ii({parser:e}){return(t,r,n)=>nD(n8(n.node),t,{parser:e},ng)}var ia=ii({parser:"__ng_action"}),is=ii({parser:"__ng_binding"}),io=ii({parser:"__ng_directive"}),il=function(e,t){if("angular"!==t.parser)return;let{node:r}=e,n=r.fullName;if(n.startsWith("(")&&n.endsWith(")")||n.startsWith("on-"))return ia;if(n.startsWith("[")&&n.endsWith("]")||/^bind(?:on)?-/u.test(n)||/^ng-(?:if|show|hide|class|style)$/u.test(n))return is;if(n.startsWith("*"))return io;let i=n8(r);return/^i18n(?:-.+)?$/u.test(n)?()=>nm(rK(n4(r,i.trim())),!i.includes("@@")):it.test(i)?e=>ir(i,e):void 0},iu=function(e,t){let{node:r}=e,n=n8(r);if("class"===r.fullName&&!t.parentParser&&!n.includes("{{"))return()=>n.trim().split(/\s+/u).join(" ")};function ic(e){return"	"===e||e===`
`||"\f"===e||"\r"===e||" "===e}var ih=/^[ \t\n\r\u000c]+/,id=/^[, \t\n\r\u000c]+/,ip=/^[^ \t\n\r\u000c]+/,im=/[,]+$/,ig=/^\d+$/,iD=/^-?(?:[0-9]+|[0-9]*\.[0-9]+)(?:[eE][+-]?[0-9]+)?$/,iy=function(e){let t=e.length,r,n,i,a,s,o=0,l;function u(t){let r,n=t.exec(e.substring(o));if(n)return[r]=n,o+=r.length,r}let c=[];for(;;){if(u(id),o>=t){if(0===c.length)throw Error("Must contain one or more image candidate strings.");return c}l=o,r=u(ip),n=[],","===r.slice(-1)?(r=r.replace(im,""),h()):function(){for(u(ih),i="",a="in descriptor";;){if(s=e.charAt(o),"in descriptor"===a)if(ic(s))i&&(n.push(i),i="",a="after descriptor");else if(","===s){o+=1,i&&n.push(i),h();return}else if("("===s)i+=s,a="in parens";else if(""===s){i&&n.push(i),h();return}else i+=s;else if("in parens"===a)if(")"===s)i+=s,a="in descriptor";else if(""===s){n.push(i),h();return}else i+=s;else if("after descriptor"===a&&!ic(s))if(""===s)return void h();else a="in descriptor",o-=1;o+=1}}()}function h(){let t=!1,i,a,s,o,u={},h,d,p,f,m;for(o=0;o<n.length;o++)d=(h=n[o])[h.length-1],f=parseInt(p=h.substring(0,h.length-1),10),m=parseFloat(p),ig.test(p)&&"w"===d?((i||a)&&(t=!0),0===f?t=!0:i=f):iD.test(p)&&"x"===d?((i||a||s)&&(t=!0),m<0?t=!0:a=m):ig.test(p)&&"h"===d?((s||a)&&(t=!0),0===f?t=!0:s=f):t=!0;if(t)throw Error(`Invalid srcset descriptor found in "${e}" at "${h}".`);u.source={value:r,startOffset:l},i&&(u.width={value:i}),a&&(u.density={value:a}),s&&(u.height={value:s}),c.push(u)}},ib={width:"w",height:"h",density:"x"},iC=Object.keys(ib),iv=function(e){if("srcset"===e.node.fullName&&("img"===e.parent.fullName||"source"===e.parent.fullName))return()=>(function(e){let t=iy(e),r=iC.filter(e=>t.some(t=>Object.prototype.hasOwnProperty.call(t,e)));if(r.length>1)throw Error("Mixed descriptor in srcset is not supported");let[n]=r,i=ib[n],a=t.map(e=>e.source.value),s=Math.max(...a.map(e=>e.length)),o=t.map(e=>e[n]?String(e[n].value):""),l=o.map(e=>{let t=e.indexOf(".");return -1===t?e.length:t}),u=Math.max(...l);return nm(r2([",",rX],a.map((e,t)=>{let r=[e],n=o[t];if(n){let a=s-e.length+1,o=u-l[t],c=" ".repeat(a+o);r.push(rQ(c," "),n+i)}return r})))})(n8(e.node))};function iS(e,t){let{node:r}=e,n=n8(e.node).trim();if("style"===r.fullName&&!t.parentParser&&!n.includes("{{"))return async e=>nm(await e(n,{parser:"css",__isHTMLStyleAttribute:!0}))}var iw=new WeakMap,ik=function(e,t){let{root:r}=e;return iw.has(r)||iw.set(r,r.children.some(e=>ie(e,t)&&["ts","typescript"].includes(e.attrMap.lang))),iw.get(r)};function iE(e,t,r){let{node:n}=r,i=n8(n);return nD(`type T<${i}> = any`,e,{parser:"babel-ts",__isEmbeddedTypescriptGenericParameters:!0},ng)}async function ix(e,t,r,n){let{left:i,operator:a,right:s}=function(e){let t=/,([^,\]}]*)(?:,([^,\]}]*))?$/u,r=e.match(/(.*?)\s+(in|of)\s+(.*)/su);if(!r)return;let n={};if(n.for=r[3].trim(),!n.for)return;let i=rS(!1,r[1].trim(),/^\(|\)$/gu,""),a=i.match(t);a?(n.alias=i.replace(t,""),n.iterator1=a[1].trim(),a[2]&&(n.iterator2=a[2].trim())):n.alias=i;let s=[n.alias,n.iterator1,n.iterator2];if(!s.some((e,t)=>!e&&(0===t||s.slice(t+1).some(Boolean))))return{left:s.filter(Boolean).join(","),operator:r[2],right:n.for}}(n8(r.node)),o=ik(r,n);return[rJ(await nD(`function _(${i}) {}`,e,{parser:o?"babel-ts":"babel",__isVueForBindingLeft:!0}))," ",a," ",await nD(s,e,{parser:o?"__ts_expression":"__js_expression"})]}async function iF(e,t,{parseWithTs:r}){var n;try{return await i_(e,t,{parseWithTs:r})}catch(e){if((null==(n=e.cause)?void 0:n.code)!=="BABEL_PARSER_SYNTAX_ERROR")throw e}return nD(e,t,{parser:r?"__vue_ts_event_binding":"__vue_event_binding"},ng)}function i_(e,t,{parseWithTs:r}){return nD(e,t,{parser:r?"__ts_expression":"__js_expression"},ng)}var iT=function(e,t){let r;if("vue"!==t.parser)return;let{node:n}=e,i=n.fullName;if("v-for"===i)return ix;if("generic"===i&&ie(n.parent,t))return iE;let a=n8(n),s=ik(e,t);return"#"===(r=n.fullName).charAt(0)||"slot-scope"===r||"v-slot"===r||r.startsWith("v-slot:")||function(e,t){let r=e.parent;if(!n7(r,t))return!1;let n=r.fullName,i=e.fullName;return"script"===n&&"setup"===i||"style"===n&&"vars"===i}(n,t)?e=>(function(e,t,{parseWithTs:r}){return nD(`function _(${e}) {}`,t,{parser:r?"babel-ts":"babel",__isVueBindings:!0})})(a,e,{parseWithTs:s}):i.startsWith("@")||i.startsWith("v-on:")?e=>iF(a,e,{parseWithTs:s}):i.startsWith(":")||i.startsWith(".")||i.startsWith("v-bind:")?e=>(function(e,t,{parseWithTs:r}){return nD(e,t,{parser:r?"__vue_ts_expression":"__vue_expression"},ng)})(a,e,{parseWithTs:s}):i.startsWith("v-")?e=>i_(a,e,{parseWithTs:s}):void 0},iA=function(e,t){let{node:r}=e;if(r.value){if(/^PRETTIER_HTML_PLACEHOLDER_\d+_\d+_IN_JS$/u.test(t.originalText.slice(r.valueSpan.start.offset,r.valueSpan.end.offset))||"lwc"===t.parser&&r.value.startsWith("{")&&r.value.endsWith("}"))return[r.rawName,"=",r.value];for(let r of[iv,iS,iu,iT,il]){let n=r(e,t);if(n)return function(e){return async(t,r,n,i)=>{let a=await e(t,r,n,i);if(a)return a=rH(a,e=>"string"==typeof e?rS(!1,e,'"',"&quot;"):e),[n.node.rawName,'="',rJ(a),'"']}}(n)}}},iL=new Proxy(()=>{},{get:()=>iL});function iB(e){return e.sourceSpan.start.offset}function iN(e){return e.sourceSpan.end.offset}function iI(e,t){var r,n,i,a;return[e.isSelfClosing?"":(r=e,n=t,r.lastChild&&ij(r.lastChild)?"":[(i=r,a=n,i$(i)?iR(i.lastChild,a):""),iO(r,n)]),iq(e,t)]}function iq(e,t){return(e.next?iW(e.next):i$(e.parent))?"":[iR(e,t),iP(e,t)]}function iP(e,t){return ij(e)?iO(e.parent,t):iH(e)?iK(e.next,t):""}function iO(e,t){if(iM(e,t))return"";switch(e.type){case"ieConditionalComment":return"<!";case"element":if(e.hasHtmComponentClosingTag)return"<//";default:return`</${e.rawName}`}}function iR(e,t){if(iM(e,t))return"";switch(e.type){case"ieConditionalComment":case"ieConditionalEndComment":return"[endif]--\x3e";case"ieConditionalStartComment":return"]>\x3c!--\x3e";case"interpolation":return"}}";case"angularIcuExpression":return"}";case"element":if(e.isSelfClosing)return"/>";default:return">"}}function iM(e,t){return!e.isSelfClosing&&!e.endSourceSpan&&(nW(e)||nM(e.parent,t))}function iW(e){return e.prev&&"docType"!==e.prev.type&&"angularControlFlowBlock"!==e.type&&!n$(e.prev)&&e.isLeadingSpaceSensitive&&!e.hasLeadingSpaces}function i$(e){var t;return(null==(t=e.lastChild)?void 0:t.isTrailingSpaceSensitive)&&!e.lastChild.hasTrailingSpaces&&!n$(nQ(e.lastChild))&&!n0(e)}function ij(e){return!e.next&&!e.hasTrailingSpaces&&e.isTrailingSpaceSensitive&&n$(nQ(e))}function iH(e){return e.next&&!n$(e.next)&&n$(e)&&e.isTrailingSpaceSensitive&&!e.hasTrailingSpaces}function iV(e){return!e.prev&&e.isLeadingSpaceSensitive&&!e.hasLeadingSpaces}function iU(e,t,r){var n;let{node:i}=e;return[iz(i,t),function(e,t,r){var n;let i,{node:a}=e;if(!nE(a.attrs))return a.isSelfClosing?" ":"";let s=(null==(n=a.prev)?void 0:n.type)==="comment"&&!!(i=a.prev.value.trim().match(/^prettier-ignore-attribute(?:\s+(.+))?$/su))&&(!i[1]||i[1].split(/\s+/u)),o="boolean"==typeof s?()=>s:Array.isArray(s)?e=>s.includes(e.rawName):()=>!1,l=e.map(({node:e})=>o(e)?rV(t.originalText.slice(iB(e),iN(e))):r(),"attrs"),u="element"===a.type&&"script"===a.fullName&&1===a.attrs.length&&"src"===a.attrs[0].fullName&&0===a.children.length,c=t.singleAttributePerLine&&a.attrs.length>1&&!n7(a,t)?r0:rX,h=[rz([u?" ":rX,r2(c,l)])];return a.firstChild&&iV(a.firstChild)||a.isSelfClosing&&i$(a.parent)||u?h.push(a.isSelfClosing?" ":""):h.push(t.bracketSameLine?a.isSelfClosing?" ":"":a.isSelfClosing?rX:rY),h}(e,t,r),i.isSelfClosing||(n=i).firstChild&&iV(n.firstChild)?"":iQ(n)]}function iz(e,t){return e.prev&&iH(e.prev)?"":[iG(e,t),iK(e,t)]}function iG(e,t){return iV(e)?iQ(e.parent):iW(e)?iR(e.prev,t):""}var iJ="<!doctype";function iK(e,t){switch(e.type){case"ieConditionalComment":case"ieConditionalStartComment":return`<!--[if ${e.condition}`;case"ieConditionalEndComment":return"\x3c!--<!";case"interpolation":return"{{";case"docType":{if("html"===e.value){let{filepath:e}=t;if(e&&/\.html?$/u.test(e))return iJ}let r=iB(e);return t.originalText.slice(r,r+iJ.length)}case"angularIcuExpression":return"{";case"element":if(e.condition)return`<!--[if ${e.condition}]><!--><${e.rawName}`;default:return`<${e.rawName}`}}function iQ(e){switch(e.type){case"ieConditionalComment":return"]>";case"element":if(e.condition)return">\x3c!--<![endif]--\x3e";default:return">"}}var iZ=function(e,t){if(!e.endSourceSpan)return"";let r=e.startSourceSpan.end.offset;e.firstChild&&iV(e.firstChild)&&(r-=iQ(e).length);let n=e.endSourceSpan.start.offset;return e.lastChild&&ij(e.lastChild)?n+=iO(e,t).length:i$(e)&&(n-=iR(e.lastChild,t).length),t.originalText.slice(r,n)},iX=new Set(["if","else if","for","switch","case"]),iY=null;function i0(e){if(null!==iY&&(iY.property,1)){let e=iY;return iY=i0.prototype=null,e}return iY=i0.prototype=e??Object.create(null),new i0}for(let e=0;e<=10;e++)i0();var i1=function(e,t="type"){return i0(e),function(r){let n=r[t],i=e[n];if(!Array.isArray(i))throw Object.assign(Error(`Missing visitor keys for '${n}'.`),{node:r});return i}}({"front-matter":[],root:["children"],element:["attrs","children"],ieConditionalComment:["children"],ieConditionalStartComment:[],ieConditionalEndComment:[],interpolation:["children"],text:["children"],docType:[],comment:[],attribute:[],cdata:[],angularControlFlowBlock:["children","parameters"],angularControlFlowBlockParameters:["children"],angularControlFlowBlockParameter:[],angularLetDeclaration:["init"],angularLetDeclarationInitializer:[],angularIcuExpression:["cases"],angularIcuCase:["expression"]}),i2=/^\s*<!--\s*@(?:noformat|noprettier)\s*-->/u,i3=/^\s*<!--\s*@(?:format|prettier)\s*-->/u;function i8(e){return i3.test(e)}function i6(e){return i2.test(e)}var i5=new Map([["if",new Set(["else if","else"])],["else if",new Set(["else if","else"])],["for",new Set(["empty"])],["defer",new Set(["placeholder","error","loading"])],["placeholder",new Set(["placeholder","error","loading"])],["error",new Set(["placeholder","error","loading"])],["loading",new Set(["placeholder","error","loading"])]]);function i7(e,t,r){let n=e.node;if(nW(n)){let e=function e(t){let r=iN(t);return"element"===t.type&&!t.endSourceSpan&&nE(t.children)?Math.max(r,e(rM(!1,t.children,-1))):r}(n);return[iG(n,t),rV(nl.trimEnd(t.originalText.slice(iB(n)+(n.prev&&iH(n.prev)?iK(n).length:0),e-(n.next&&iW(n.next)?iR(n,t).length:0)))),iP(n,t)]}return r()}function i9(e,t){return n$(e)&&n$(t)?e.isTrailingSpaceSensitive?e.hasTrailingSpaces?nz(t)?r0:rX:"":nz(t)?r0:rY:iH(e)&&(nW(t)||t.firstChild||t.isSelfClosing||"element"===t.type&&t.attrs.length>0)||"element"===e.type&&e.isSelfClosing&&iW(t)?"":!t.isLeadingSpaceSensitive||nz(t)||iW(t)&&e.lastChild&&ij(e.lastChild)&&e.lastChild.lastChild&&ij(e.lastChild.lastChild)?r0:t.hasLeadingSpaces?rX:rY}function i4(e,t,r){let{node:n}=e;if(nU(n))return[rZ,...e.map(e=>{let n=e.node,i=n.prev?i9(n.prev,n):"";return[i?[i,nV(n.prev)?r0:""]:"",i7(e,t,r)]},"children")];let i=n.children.map(()=>Symbol(""));return e.map((e,n)=>{let a=e.node;if(n$(a)){if(a.prev&&n$(a.prev)){let n=i9(a.prev,a);if(n)return nV(a.prev)?[r0,r0,i7(e,t,r)]:[n,i7(e,t,r)]}return i7(e,t,r)}let s=[],o=[],l=[],u=[],c=a.prev?i9(a.prev,a):"",h=a.next?i9(a,a.next):"";return c&&(nV(a.prev)?s.push(r0,r0):c===r0?s.push(r0):n$(a.prev)?o.push(c):o.push(rQ("",rY,{groupId:i[n-1]}))),h&&(nV(a)?n$(a.next)&&u.push(r0,r0):h===r0?n$(a.next)&&u.push(r0):l.push(h)),[...s,rJ([...o,rJ([i7(e,t,r),...l],{id:i[n]})]),...u]},"children")}function ae(e){var t,r;return!((null==(t=e.next)?void 0:t.type)==="angularControlFlowBlock"&&null!=(r=i5.get(e.name))&&r.has(e.next.name))}function at(e){return e>=9&&e<=32||160==e}function ar(e){return 48<=e&&e<=57}function an(e){return e>=97&&e<=122||e>=65&&e<=90}function ai(e){return 10===e||13===e}function aa(e){return 48<=e&&e<=55}function as(e){return 39===e||34===e||96===e}var ao,al=/-+([a-z0-9])/g,au=class e{constructor(e,t,r,n){this.file=e,this.offset=t,this.line=r,this.col=n}toString(){return null!=this.offset?`${this.file.url}@${this.line}:${this.col}`:this.file.url}moveBy(t){let r=this.file.content,n=r.length,i=this.offset,a=this.line,s=this.col;for(;i>0&&t<0;)if(i--,t++,10==r.charCodeAt(i)){a--;let e=r.substring(0,i-1).lastIndexOf("\n");s=e>0?i-e:i}else s--;for(;i<n&&t>0;){let e=r.charCodeAt(i);i++,t--,10==e?(a++,s=0):s++}return new e(this.file,i,a,s)}getContext(e,t){let r=this.file.content,n=this.offset;if(null!=n){n>r.length-1&&(n=r.length-1);let i=n,a=0,s=0;for(;a<e&&n>0&&(n--,a++,r[n]!=`
`||++s!=t););for(a=0,s=0;a<e&&i<r.length-1&&(i++,a++,r[i]!=`
`||++s!=t););return{before:r.substring(n,this.offset),after:r.substring(this.offset,i+1)}}return null}},ac=class{constructor(e,t){this.content=e,this.url=t}},ah=class{constructor(e,t,r=e,n=null){this.start=e,this.end=t,this.fullStart=r,this.details=n}toString(){return this.start.file.content.substring(this.start.offset,this.end.offset)}};!function(e){e[e.WARNING=0]="WARNING",e[e.ERROR=1]="ERROR"}(ao||(ao={}));var ad=class{constructor(e,t,r=ao.ERROR,n){this.span=e,this.msg=t,this.level=r,this.relatedError=n}contextualMessage(){let e=this.span.start.getContext(100,3);return e?`${this.msg} ("${e.before}[${ao[this.level]} ->]${e.after}")`:this.msg}toString(){let e=this.span.details?`, ${this.span.details}`:"";return`${this.contextualMessage()}: ${this.span.start}${e}`}},ap=[function(e){e.walk(e=>{if("element"===e.type&&e.tagDefinition.ignoreFirstLf&&e.children.length>0&&"text"===e.children[0].type&&e.children[0].value[0]===`
`){let t=e.children[0];1===t.value.length?e.removeChild(t):t.value=t.value.slice(1)}})},function(e){let t=e=>{var t,r;return"element"===e.type&&(null==(t=e.prev)?void 0:t.type)==="ieConditionalStartComment"&&e.prev.sourceSpan.end.offset===e.startSourceSpan.start.offset&&(null==(r=e.firstChild)?void 0:r.type)==="ieConditionalEndComment"&&e.firstChild.sourceSpan.start.offset===e.startSourceSpan.end.offset};e.walk(e=>{if(e.children)for(let r=0;r<e.children.length;r++){let n=e.children[r];if(!t(n))continue;let i=n.prev,a=n.firstChild;e.removeChild(i),r--;let s=new ah(i.sourceSpan.start,a.sourceSpan.end),o=new ah(s.start,n.sourceSpan.end);n.condition=i.condition,n.sourceSpan=o,n.startSourceSpan=s,n.removeChild(a)}})},function(e){var t,r;return t=e=>"cdata"===e.type,r=e=>`<![CDATA[${e.value}]]>`,void e.walk(e=>{if(e.children)for(let n=0;n<e.children.length;n++){let i=e.children[n];if("text"!==i.type&&!t(i))continue;"text"!==i.type&&(i.type="text",i.value=r(i));let a=i.prev;!a||"text"!==a.type||(a.value+=i.value,a.sourceSpan=new ah(a.sourceSpan.start,i.sourceSpan.end),e.removeChild(i),n--)}})},function(e,t){if("html"===t.parser)return;let r=/\{\{(.+?)\}\}/su;e.walk(e=>{if(e.children&&!nj(e,t))for(let t of e.children){if("text"!==t.type)continue;let n=t.sourceSpan.start,i=null,a=t.value.split(r);for(let r=0;r<a.length;r++,n=i){let s=a[r];if(r%2==0){i=n.moveBy(s.length),s.length>0&&e.insertChildBefore(t,{type:"text",value:s,sourceSpan:new ah(n,i)});continue}i=n.moveBy(s.length+4),e.insertChildBefore(t,{type:"interpolation",sourceSpan:new ah(n,i),children:0===s.length?[]:[{type:"text",value:s,sourceSpan:new ah(n.moveBy(2),i.moveBy(-2))}]})}e.removeChild(t)}})},function(e,t){e.walk(e=>{let r=e.$children;if(!r)return;if(0===r.length||1===r.length&&"text"===r[0].type&&0===nl.trim(r[0].value).length){e.hasDanglingSpaces=r.length>0,e.$children=[];return}let n=nj(e,t)||"interpolation"===e.type||nH(e),i=nH(e);if(!n)for(let t=0;t<r.length;t++){let n=r[t];if("text"!==n.type)continue;let{leadingWhitespace:i,text:a,trailingWhitespace:s}=nR(n.value),o=n.prev,l=n.next;a?(n.value=a,n.sourceSpan=new ah(n.sourceSpan.start.moveBy(i.length),n.sourceSpan.end.moveBy(-s.length)),i&&(o&&(o.hasTrailingSpaces=!0),n.hasLeadingSpaces=!0),s&&(n.hasTrailingSpaces=!0,l&&(l.hasLeadingSpaces=!0))):(e.removeChild(n),t--,(i||s)&&(o&&(o.hasTrailingSpaces=!0),l&&(l.hasLeadingSpaces=!0)))}e.isWhitespaceSensitive=n,e.isIndentationSensitive=i})},function(e,t){e.walk(e=>{e.cssDisplay=function(e,t){var r;if(n7(e,t))return"block";if((null==(r=e.prev)?void 0:r.type)==="comment"){let t=e.prev.value.match(/^\s*display:\s*([a-z]+)\s*$/u);if(t)return t[1]}let n=!1;if("element"===e.type&&"svg"===e.namespace)if(!function(e,t){let r=e;for(;r;){if(t(r))return!0;r=r.parent}return!1}(e,e=>"svg:foreignObject"===e.fullName))return"svg"===e.name?"inline-block":"block";else n=!0;switch(t.htmlWhitespaceSensitivity){case"strict":break;case"ignore":return"block";default:if("element"===e.type&&(!e.namespace||n||nq(e))&&Object.prototype.hasOwnProperty.call(nN,e.name))return nN[e.name]}return"inline"}(e,t)})},function(e){e.walk(e=>{e.isSelfClosing=!e.children||"element"===e.type&&(e.tagDefinition.isVoid||e.endSourceSpan&&e.startSourceSpan.start===e.endSourceSpan.start&&e.startSourceSpan.end===e.endSourceSpan.end)})},function(e,t){e.walk(e=>{"element"===e.type&&(e.hasHtmComponentClosingTag=e.endSourceSpan&&/^<\s*\/\s*\/\s*>$/u.test(t.originalText.slice(e.endSourceSpan.start.offset,e.endSourceSpan.end.offset)))})},function(e,t){e.walk(e=>{let{children:r}=e;if(r){var n,i;if(0===r.length){e.isDanglingSpaceSensitive=!nY(n=e.cssDisplay)&&"inline-block"!==n&&!nj(e,t);return}for(let e of r){e.isLeadingSpaceSensitive=function(e,t){var r,n;let i=function(){var r;return!nc(e)&&"angularControlFlowBlock"!==e.type&&(("text"===e.type||"interpolation"===e.type)&&!!e.prev&&("text"===e.prev.type||"interpolation"===e.prev.type)||!!e.parent&&"none"!==e.parent.cssDisplay&&(!!n0(e.parent)||!(!e.prev&&("root"===e.parent.type||n0(e)&&e.parent||nj(e.parent,t)||n5(e.parent,t)||!(!nY(r=e.parent.cssDisplay)&&"inline-block"!==r))||e.prev&&nY(e.prev.cssDisplay))))}();return i&&!e.prev&&null!=(n=null==(r=e.parent)?void 0:r.tagDefinition)&&n.ignoreFirstLf?"interpolation"===e.type:i}(e,t),e.isTrailingSpaceSensitive=!nc(e)&&"angularControlFlowBlock"!==e.type&&(("text"===e.type||"interpolation"===e.type)&&!!e.next&&("text"===e.next.type||"interpolation"===e.next.type)||!!e.parent&&"none"!==e.parent.cssDisplay&&(!!n0(e.parent)||!(!e.next&&("root"===e.parent.type||n0(e)&&e.parent||nj(e.parent,t)||n5(e.parent,t)||!(!nY(i=e.parent.cssDisplay)&&"inline-block"!==i))||e.next&&nY(e.next.cssDisplay))))}for(let e=0;e<r.length;e++){let t=r[e];t.isLeadingSpaceSensitive=(0===e||t.prev.isTrailingSpaceSensitive)&&t.isLeadingSpaceSensitive,t.isTrailingSpaceSensitive=(e===r.length-1||t.next.isLeadingSpaceSensitive)&&t.isTrailingSpaceSensitive}}})},function(e){let t=e=>{var t,r;return"element"===e.type&&0===e.attrs.length&&1===e.children.length&&"text"===e.firstChild.type&&!nl.hasWhitespaceCharacter(e.children[0].value)&&!e.firstChild.hasLeadingSpaces&&!e.firstChild.hasTrailingSpaces&&e.isLeadingSpaceSensitive&&!e.hasLeadingSpaces&&e.isTrailingSpaceSensitive&&!e.hasTrailingSpaces&&(null==(t=e.prev)?void 0:t.type)==="text"&&(null==(r=e.next)?void 0:r.type)==="text"};e.walk(e=>{if(e.children)for(let r=0;r<e.children.length;r++){let n=e.children[r];if(!t(n))continue;let i=n.prev,a=n.next;i.value+=`<${n.rawName}>`+n.firstChild.value+`</${n.rawName}>`+a.value,i.sourceSpan=new ah(i.sourceSpan.start,a.sourceSpan.end),i.isTrailingSpaceSensitive=a.isTrailingSpaceSensitive,i.hasTrailingSpaces=a.hasTrailingSpaces,e.removeChild(n),r--,e.removeChild(a)}})}],af=[{name:"Angular",type:"markup",extensions:[".component.html"],tmScope:"text.html.basic",aceMode:"html",aliases:["xhtml"],codemirrorMode:"htmlmixed",codemirrorMimeType:"text/html",parsers:["angular"],vscodeLanguageIds:["html"],filenames:[],linguistLanguageId:146},{name:"HTML",type:"markup",extensions:[".html",".hta",".htm",".html.hl",".inc",".xht",".xhtml"],tmScope:"text.html.basic",aceMode:"html",aliases:["xhtml"],codemirrorMode:"htmlmixed",codemirrorMimeType:"text/html",parsers:["html"],vscodeLanguageIds:["html"],linguistLanguageId:146},{name:"Lightning Web Components",type:"markup",extensions:[],tmScope:"text.html.basic",aceMode:"html",aliases:["xhtml"],codemirrorMode:"htmlmixed",codemirrorMimeType:"text/html",parsers:["lwc"],vscodeLanguageIds:["html"],filenames:[],linguistLanguageId:146},{name:"MJML",type:"markup",extensions:[".mjml"],tmScope:"text.mjml.basic",aceMode:"html",aliases:["MJML","mjml"],codemirrorMode:"htmlmixed",codemirrorMimeType:"text/html",parsers:["mjml"],filenames:[],vscodeLanguageIds:["mjml"],linguistLanguageId:146},{name:"Vue",type:"markup",extensions:[".vue"],tmScope:"source.vue",aceMode:"html",parsers:["vue"],vscodeLanguageIds:["vue"],linguistLanguageId:391}],am={bracketSameLine:{category:"Common",type:"boolean",default:!1,description:"Put > of opening tags on the last line instead of on a new line."},singleAttributePerLine:{category:"Common",type:"boolean",default:!1,description:"Enforce single attribute per line in HTML, Vue and JSX."}},ag="HTML",aD={bracketSameLine:am.bracketSameLine,htmlWhitespaceSensitivity:{category:ag,type:"choice",default:"css",description:"How to handle whitespaces in HTML.",choices:[{value:"css",description:"Respect the default value of CSS display property."},{value:"strict",description:"Whitespaces are considered sensitive."},{value:"ignore",description:"Whitespaces are considered insensitive."}]},singleAttributePerLine:am.singleAttributePerLine,vueIndentScriptAndStyle:{category:ag,type:"boolean",default:!1,description:"Indent script and style tags in Vue files."}},ay={};rm(ay,{angular:()=>s$,html:()=>sR,lwc:()=>sH,mjml:()=>sW,vue:()=>sj}),!function(e){e[e.Emulated=0]="Emulated",e[e.None=2]="None",e[e.ShadowDom=3]="ShadowDom"}(a_||(a_={})),function(e){e[e.OnPush=0]="OnPush",e[e.Default=1]="Default"}(aT||(aT={})),function(e){e[e.None=0]="None",e[e.SignalBased=1]="SignalBased",e[e.HasDecoratorInputTransform=2]="HasDecoratorInputTransform"}(aA||(aA={}));var ab={name:"custom-elements"},aC={name:"no-errors-schema"};function av(e,t=!0){if(":"!=e[0])return[null,e];let r=e.indexOf(":",1);if(-1===r){if(t)throw Error(`Unsupported format "${e}" expecting ":namespace:name"`);return[null,e]}return[e.slice(1,r),e.slice(r+1)]}function aS(e){return"ng-container"===av(e)[1]}function aw(e){return"ng-content"===av(e)[1]}function ak(e){return null===e?null:av(e)[0]}function aE(e,t){return e?`:${e}:${t}`:t}function ax(){return aI||(aI={},aF(aL.HTML,["iframe|srcdoc","*|innerHTML","*|outerHTML"]),aF(aL.STYLE,["*|style"]),aF(aL.URL,["*|formAction","area|href","area|ping","audio|src","a|href","a|ping","blockquote|cite","body|background","del|cite","form|action","img|src","input|src","ins|cite","q|cite","source|src","track|src","video|poster","video|src"]),aF(aL.RESOURCE_URL,["applet|code","applet|codebase","base|href","embed|src","frame|src","head|profile","html|manifest","iframe|src","link|href","media|src","object|codebase","object|data","script|src"])),aI}function aF(e,t){for(let r of t)aI[r.toLowerCase()]=e}!function(e){e[e.NONE=0]="NONE",e[e.HTML=1]="HTML",e[e.STYLE=2]="STYLE",e[e.SCRIPT=3]="SCRIPT",e[e.URL=4]="URL",e[e.RESOURCE_URL=5]="RESOURCE_URL"}(aL||(aL={})),function(e){e[e.Error=0]="Error",e[e.Warning=1]="Warning",e[e.Ignore=2]="Ignore"}(aB||(aB={})),function(e){e[e.RAW_TEXT=0]="RAW_TEXT",e[e.ESCAPABLE_RAW_TEXT=1]="ESCAPABLE_RAW_TEXT",e[e.PARSABLE_DATA=2]="PARSABLE_DATA"}(aN||(aN={}));var a_,aT,aA,aL,aB,aN,aI,aq,aP,aO=class{},aR=["[Element]|textContent,%ariaAtomic,%ariaAutoComplete,%ariaBusy,%ariaChecked,%ariaColCount,%ariaColIndex,%ariaColSpan,%ariaCurrent,%ariaDescription,%ariaDisabled,%ariaExpanded,%ariaHasPopup,%ariaHidden,%ariaKeyShortcuts,%ariaLabel,%ariaLevel,%ariaLive,%ariaModal,%ariaMultiLine,%ariaMultiSelectable,%ariaOrientation,%ariaPlaceholder,%ariaPosInSet,%ariaPressed,%ariaReadOnly,%ariaRelevant,%ariaRequired,%ariaRoleDescription,%ariaRowCount,%ariaRowIndex,%ariaRowSpan,%ariaSelected,%ariaSetSize,%ariaSort,%ariaValueMax,%ariaValueMin,%ariaValueNow,%ariaValueText,%classList,className,elementTiming,id,innerHTML,*beforecopy,*beforecut,*beforepaste,*fullscreenchange,*fullscreenerror,*search,*webkitfullscreenchange,*webkitfullscreenerror,outerHTML,%part,#scrollLeft,#scrollTop,slot,*message,*mozfullscreenchange,*mozfullscreenerror,*mozpointerlockchange,*mozpointerlockerror,*webglcontextcreationerror,*webglcontextlost,*webglcontextrestored","[HTMLElement]^[Element]|accessKey,autocapitalize,!autofocus,contentEditable,dir,!draggable,enterKeyHint,!hidden,!inert,innerText,inputMode,lang,nonce,*abort,*animationend,*animationiteration,*animationstart,*auxclick,*beforexrselect,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*formdata,*gotpointercapture,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*lostpointercapture,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*paste,*pause,*play,*playing,*pointercancel,*pointerdown,*pointerenter,*pointerleave,*pointermove,*pointerout,*pointerover,*pointerrawupdate,*pointerup,*progress,*ratechange,*reset,*resize,*scroll,*securitypolicyviolation,*seeked,*seeking,*select,*selectionchange,*selectstart,*slotchange,*stalled,*submit,*suspend,*timeupdate,*toggle,*transitioncancel,*transitionend,*transitionrun,*transitionstart,*volumechange,*waiting,*webkitanimationend,*webkitanimationiteration,*webkitanimationstart,*webkittransitionend,*wheel,outerText,!spellcheck,%style,#tabIndex,title,!translate,virtualKeyboardPolicy","abbr,address,article,aside,b,bdi,bdo,cite,content,code,dd,dfn,dt,em,figcaption,figure,footer,header,hgroup,i,kbd,main,mark,nav,noscript,rb,rp,rt,rtc,ruby,s,samp,section,small,strong,sub,sup,u,var,wbr^[HTMLElement]|accessKey,autocapitalize,!autofocus,contentEditable,dir,!draggable,enterKeyHint,!hidden,innerText,inputMode,lang,nonce,*abort,*animationend,*animationiteration,*animationstart,*auxclick,*beforexrselect,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*formdata,*gotpointercapture,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*lostpointercapture,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*paste,*pause,*play,*playing,*pointercancel,*pointerdown,*pointerenter,*pointerleave,*pointermove,*pointerout,*pointerover,*pointerrawupdate,*pointerup,*progress,*ratechange,*reset,*resize,*scroll,*securitypolicyviolation,*seeked,*seeking,*select,*selectionchange,*selectstart,*slotchange,*stalled,*submit,*suspend,*timeupdate,*toggle,*transitioncancel,*transitionend,*transitionrun,*transitionstart,*volumechange,*waiting,*webkitanimationend,*webkitanimationiteration,*webkitanimationstart,*webkittransitionend,*wheel,outerText,!spellcheck,%style,#tabIndex,title,!translate,virtualKeyboardPolicy","media^[HTMLElement]|!autoplay,!controls,%controlsList,%crossOrigin,#currentTime,!defaultMuted,#defaultPlaybackRate,!disableRemotePlayback,!loop,!muted,*encrypted,*waitingforkey,#playbackRate,preload,!preservesPitch,src,%srcObject,#volume",":svg:^[HTMLElement]|!autofocus,nonce,*abort,*animationend,*animationiteration,*animationstart,*auxclick,*beforexrselect,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contextmenu,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*formdata,*gotpointercapture,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*lostpointercapture,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*paste,*pause,*play,*playing,*pointercancel,*pointerdown,*pointerenter,*pointerleave,*pointermove,*pointerout,*pointerover,*pointerrawupdate,*pointerup,*progress,*ratechange,*reset,*resize,*scroll,*securitypolicyviolation,*seeked,*seeking,*select,*selectionchange,*selectstart,*slotchange,*stalled,*submit,*suspend,*timeupdate,*toggle,*transitioncancel,*transitionend,*transitionrun,*transitionstart,*volumechange,*waiting,*webkitanimationend,*webkitanimationiteration,*webkitanimationstart,*webkittransitionend,*wheel,%style,#tabIndex",":svg:graphics^:svg:|",":svg:animation^:svg:|*begin,*end,*repeat",":svg:geometry^:svg:|",":svg:componentTransferFunction^:svg:|",":svg:gradient^:svg:|",":svg:textContent^:svg:graphics|",":svg:textPositioning^:svg:textContent|","a^[HTMLElement]|charset,coords,download,hash,host,hostname,href,hreflang,name,password,pathname,ping,port,protocol,referrerPolicy,rel,%relList,rev,search,shape,target,text,type,username","area^[HTMLElement]|alt,coords,download,hash,host,hostname,href,!noHref,password,pathname,ping,port,protocol,referrerPolicy,rel,%relList,search,shape,target,username","audio^media|","br^[HTMLElement]|clear","base^[HTMLElement]|href,target","body^[HTMLElement]|aLink,background,bgColor,link,*afterprint,*beforeprint,*beforeunload,*blur,*error,*focus,*hashchange,*languagechange,*load,*message,*messageerror,*offline,*online,*pagehide,*pageshow,*popstate,*rejectionhandled,*resize,*scroll,*storage,*unhandledrejection,*unload,text,vLink","button^[HTMLElement]|!disabled,formAction,formEnctype,formMethod,!formNoValidate,formTarget,name,type,value","canvas^[HTMLElement]|#height,#width","content^[HTMLElement]|select","dl^[HTMLElement]|!compact","data^[HTMLElement]|value","datalist^[HTMLElement]|","details^[HTMLElement]|!open","dialog^[HTMLElement]|!open,returnValue","dir^[HTMLElement]|!compact","div^[HTMLElement]|align","embed^[HTMLElement]|align,height,name,src,type,width","fieldset^[HTMLElement]|!disabled,name","font^[HTMLElement]|color,face,size","form^[HTMLElement]|acceptCharset,action,autocomplete,encoding,enctype,method,name,!noValidate,target","frame^[HTMLElement]|frameBorder,longDesc,marginHeight,marginWidth,name,!noResize,scrolling,src","frameset^[HTMLElement]|cols,*afterprint,*beforeprint,*beforeunload,*blur,*error,*focus,*hashchange,*languagechange,*load,*message,*messageerror,*offline,*online,*pagehide,*pageshow,*popstate,*rejectionhandled,*resize,*scroll,*storage,*unhandledrejection,*unload,rows","hr^[HTMLElement]|align,color,!noShade,size,width","head^[HTMLElement]|","h1,h2,h3,h4,h5,h6^[HTMLElement]|align","html^[HTMLElement]|version","iframe^[HTMLElement]|align,allow,!allowFullscreen,!allowPaymentRequest,csp,frameBorder,height,loading,longDesc,marginHeight,marginWidth,name,referrerPolicy,%sandbox,scrolling,src,srcdoc,width","img^[HTMLElement]|align,alt,border,%crossOrigin,decoding,#height,#hspace,!isMap,loading,longDesc,lowsrc,name,referrerPolicy,sizes,src,srcset,useMap,#vspace,#width","input^[HTMLElement]|accept,align,alt,autocomplete,!checked,!defaultChecked,defaultValue,dirName,!disabled,%files,formAction,formEnctype,formMethod,!formNoValidate,formTarget,#height,!incremental,!indeterminate,max,#maxLength,min,#minLength,!multiple,name,pattern,placeholder,!readOnly,!required,selectionDirection,#selectionEnd,#selectionStart,#size,src,step,type,useMap,value,%valueAsDate,#valueAsNumber,#width","li^[HTMLElement]|type,#value","label^[HTMLElement]|htmlFor","legend^[HTMLElement]|align","link^[HTMLElement]|as,charset,%crossOrigin,!disabled,href,hreflang,imageSizes,imageSrcset,integrity,media,referrerPolicy,rel,%relList,rev,%sizes,target,type","map^[HTMLElement]|name","marquee^[HTMLElement]|behavior,bgColor,direction,height,#hspace,#loop,#scrollAmount,#scrollDelay,!trueSpeed,#vspace,width","menu^[HTMLElement]|!compact","meta^[HTMLElement]|content,httpEquiv,media,name,scheme","meter^[HTMLElement]|#high,#low,#max,#min,#optimum,#value","ins,del^[HTMLElement]|cite,dateTime","ol^[HTMLElement]|!compact,!reversed,#start,type","object^[HTMLElement]|align,archive,border,code,codeBase,codeType,data,!declare,height,#hspace,name,standby,type,useMap,#vspace,width","optgroup^[HTMLElement]|!disabled,label","option^[HTMLElement]|!defaultSelected,!disabled,label,!selected,text,value","output^[HTMLElement]|defaultValue,%htmlFor,name,value","p^[HTMLElement]|align","param^[HTMLElement]|name,type,value,valueType","picture^[HTMLElement]|","pre^[HTMLElement]|#width","progress^[HTMLElement]|#max,#value","q,blockquote,cite^[HTMLElement]|","script^[HTMLElement]|!async,charset,%crossOrigin,!defer,event,htmlFor,integrity,!noModule,%referrerPolicy,src,text,type","select^[HTMLElement]|autocomplete,!disabled,#length,!multiple,name,!required,#selectedIndex,#size,value","slot^[HTMLElement]|name","source^[HTMLElement]|#height,media,sizes,src,srcset,type,#width","span^[HTMLElement]|","style^[HTMLElement]|!disabled,media,type","caption^[HTMLElement]|align","th,td^[HTMLElement]|abbr,align,axis,bgColor,ch,chOff,#colSpan,headers,height,!noWrap,#rowSpan,scope,vAlign,width","col,colgroup^[HTMLElement]|align,ch,chOff,#span,vAlign,width","table^[HTMLElement]|align,bgColor,border,%caption,cellPadding,cellSpacing,frame,rules,summary,%tFoot,%tHead,width","tr^[HTMLElement]|align,bgColor,ch,chOff,vAlign","tfoot,thead,tbody^[HTMLElement]|align,ch,chOff,vAlign","template^[HTMLElement]|","textarea^[HTMLElement]|autocomplete,#cols,defaultValue,dirName,!disabled,#maxLength,#minLength,name,placeholder,!readOnly,!required,#rows,selectionDirection,#selectionEnd,#selectionStart,value,wrap","time^[HTMLElement]|dateTime","title^[HTMLElement]|text","track^[HTMLElement]|!default,kind,label,src,srclang","ul^[HTMLElement]|!compact,type","unknown^[HTMLElement]|","video^media|!disablePictureInPicture,#height,*enterpictureinpicture,*leavepictureinpicture,!playsInline,poster,#width",":svg:a^:svg:graphics|",":svg:animate^:svg:animation|",":svg:animateMotion^:svg:animation|",":svg:animateTransform^:svg:animation|",":svg:circle^:svg:geometry|",":svg:clipPath^:svg:graphics|",":svg:defs^:svg:graphics|",":svg:desc^:svg:|",":svg:discard^:svg:|",":svg:ellipse^:svg:geometry|",":svg:feBlend^:svg:|",":svg:feColorMatrix^:svg:|",":svg:feComponentTransfer^:svg:|",":svg:feComposite^:svg:|",":svg:feConvolveMatrix^:svg:|",":svg:feDiffuseLighting^:svg:|",":svg:feDisplacementMap^:svg:|",":svg:feDistantLight^:svg:|",":svg:feDropShadow^:svg:|",":svg:feFlood^:svg:|",":svg:feFuncA^:svg:componentTransferFunction|",":svg:feFuncB^:svg:componentTransferFunction|",":svg:feFuncG^:svg:componentTransferFunction|",":svg:feFuncR^:svg:componentTransferFunction|",":svg:feGaussianBlur^:svg:|",":svg:feImage^:svg:|",":svg:feMerge^:svg:|",":svg:feMergeNode^:svg:|",":svg:feMorphology^:svg:|",":svg:feOffset^:svg:|",":svg:fePointLight^:svg:|",":svg:feSpecularLighting^:svg:|",":svg:feSpotLight^:svg:|",":svg:feTile^:svg:|",":svg:feTurbulence^:svg:|",":svg:filter^:svg:|",":svg:foreignObject^:svg:graphics|",":svg:g^:svg:graphics|",":svg:image^:svg:graphics|decoding",":svg:line^:svg:geometry|",":svg:linearGradient^:svg:gradient|",":svg:mpath^:svg:|",":svg:marker^:svg:|",":svg:mask^:svg:|",":svg:metadata^:svg:|",":svg:path^:svg:geometry|",":svg:pattern^:svg:|",":svg:polygon^:svg:geometry|",":svg:polyline^:svg:geometry|",":svg:radialGradient^:svg:gradient|",":svg:rect^:svg:geometry|",":svg:svg^:svg:graphics|#currentScale,#zoomAndPan",":svg:script^:svg:|type",":svg:set^:svg:animation|",":svg:stop^:svg:|",":svg:style^:svg:|!disabled,media,title,type",":svg:switch^:svg:graphics|",":svg:symbol^:svg:|",":svg:tspan^:svg:textPositioning|",":svg:text^:svg:textPositioning|",":svg:textPath^:svg:textContent|",":svg:title^:svg:|",":svg:use^:svg:graphics|",":svg:view^:svg:|#zoomAndPan","data^[HTMLElement]|value","keygen^[HTMLElement]|!autofocus,challenge,!disabled,form,keytype,name","menuitem^[HTMLElement]|type,label,icon,!disabled,!checked,radiogroup,!default","summary^[HTMLElement]|","time^[HTMLElement]|dateTime",":svg:cursor^:svg:|",":math:^[HTMLElement]|!autofocus,nonce,*abort,*animationend,*animationiteration,*animationstart,*auxclick,*beforeinput,*beforematch,*beforetoggle,*beforexrselect,*blur,*cancel,*canplay,*canplaythrough,*change,*click,*close,*contentvisibilityautostatechange,*contextlost,*contextmenu,*contextrestored,*copy,*cuechange,*cut,*dblclick,*drag,*dragend,*dragenter,*dragleave,*dragover,*dragstart,*drop,*durationchange,*emptied,*ended,*error,*focus,*formdata,*gotpointercapture,*input,*invalid,*keydown,*keypress,*keyup,*load,*loadeddata,*loadedmetadata,*loadstart,*lostpointercapture,*mousedown,*mouseenter,*mouseleave,*mousemove,*mouseout,*mouseover,*mouseup,*mousewheel,*paste,*pause,*play,*playing,*pointercancel,*pointerdown,*pointerenter,*pointerleave,*pointermove,*pointerout,*pointerover,*pointerrawupdate,*pointerup,*progress,*ratechange,*reset,*resize,*scroll,*scrollend,*securitypolicyviolation,*seeked,*seeking,*select,*selectionchange,*selectstart,*slotchange,*stalled,*submit,*suspend,*timeupdate,*toggle,*transitioncancel,*transitionend,*transitionrun,*transitionstart,*volumechange,*waiting,*webkitanimationend,*webkitanimationiteration,*webkitanimationstart,*webkittransitionend,*wheel,%style,#tabIndex",":math:math^:math:|",":math:maction^:math:|",":math:menclose^:math:|",":math:merror^:math:|",":math:mfenced^:math:|",":math:mfrac^:math:|",":math:mi^:math:|",":math:mmultiscripts^:math:|",":math:mn^:math:|",":math:mo^:math:|",":math:mover^:math:|",":math:mpadded^:math:|",":math:mphantom^:math:|",":math:mroot^:math:|",":math:mrow^:math:|",":math:ms^:math:|",":math:mspace^:math:|",":math:msqrt^:math:|",":math:mstyle^:math:|",":math:msub^:math:|",":math:msubsup^:math:|",":math:msup^:math:|",":math:mtable^:math:|",":math:mtd^:math:|",":math:mtext^:math:|",":math:mtr^:math:|",":math:munder^:math:|",":math:munderover^:math:|",":math:semantics^:math:|"],aM=new Map(Object.entries({class:"className",for:"htmlFor",formaction:"formAction",innerHtml:"innerHTML",readonly:"readOnly",tabindex:"tabIndex"})),aW=Array.from(aM).reduce((e,[t,r])=>(e.set(t,r),e),new Map),a$=class extends aO{constructor(){super(),this._schema=new Map,this._eventSchema=new Map,aR.forEach(e=>{let t=new Map,r=new Set,[n,i]=e.split("|"),a=i.split(","),[s,o]=n.split("^");s.split(",").forEach(e=>{this._schema.set(e.toLowerCase(),t),this._eventSchema.set(e.toLowerCase(),r)});let l=o&&this._schema.get(o.toLowerCase());if(l){for(let[e,r]of l)t.set(e,r);for(let e of this._eventSchema.get(o.toLowerCase()))r.add(e)}a.forEach(e=>{if(e.length>0)switch(e[0]){case"*":r.add(e.substring(1));break;case"!":t.set(e.substring(1),"boolean");break;case"#":t.set(e.substring(1),"number");break;case"%":t.set(e.substring(1),"object");break;default:t.set(e,"string")}})})}hasProperty(e,t,r){if(r.some(e=>e.name===aC.name))return!0;if(e.indexOf("-")>-1){if(aS(e)||aw(e))return!1;if(r.some(e=>e.name===ab.name))return!0}return(this._schema.get(e.toLowerCase())||this._schema.get("unknown")).has(t)}hasElement(e,t){return!!(t.some(e=>e.name===aC.name)||e.indexOf("-")>-1&&(aS(e)||aw(e)||t.some(e=>e.name===ab.name)))||this._schema.has(e.toLowerCase())}securityContext(e,t,r){r&&(t=this.getMappedPropName(t)),e=e.toLowerCase(),t=t.toLowerCase();let n=ax()[e+"|"+t];return n||(n=ax()["*|"+t])||aL.NONE}getMappedPropName(e){return aM.get(e)??e}getDefaultComponentElementName(){return"ng-component"}validateProperty(e){return e.toLowerCase().startsWith("on")?{error:!0,msg:`Binding to event property '${e}' is disallowed for security reasons, please use (${e.slice(2)})=...
If '${e}' is a directive input, make sure the directive is imported by the current module.`}:{error:!1}}validateAttribute(e){return e.toLowerCase().startsWith("on")?{error:!0,msg:`Binding to event attribute '${e}' is disallowed for security reasons, please use (${e.slice(2)})=...`}:{error:!1}}allKnownElementNames(){return Array.from(this._schema.keys())}allKnownAttributesOfElement(e){return Array.from((this._schema.get(e.toLowerCase())||this._schema.get("unknown")).keys()).map(e=>aW.get(e)??e)}allKnownEventsOfElement(e){return Array.from(this._eventSchema.get(e.toLowerCase())??[])}normalizeAnimationStyleProperty(e){return e.replace(al,(...e)=>e[1].toUpperCase())}normalizeAnimationStyleValue(e,t,r){let n="",i=r.toString().trim(),a=null;if(function(e){switch(e){case"width":case"height":case"minWidth":case"minHeight":case"maxWidth":case"maxHeight":case"left":case"top":case"bottom":case"right":case"fontSize":case"outlineWidth":case"outlineOffset":case"paddingTop":case"paddingLeft":case"paddingBottom":case"paddingRight":case"marginTop":case"marginLeft":case"marginBottom":case"marginRight":case"borderRadius":case"borderWidth":case"borderTopWidth":case"borderLeftWidth":case"borderRightWidth":case"borderBottomWidth":case"textIndent":return!0;default:return!1}}(e)&&0!==r&&"0"!==r)if("number"==typeof r)n="px";else{let e=r.match(/^[+-]?[\d\.]+([a-z]*)$/);e&&0==e[1].length&&(a=`Please provide a CSS unit value for ${t}:${r}`)}return{error:a,value:i+n}}},aj=class{constructor({closedByChildren:e,implicitNamespacePrefix:t,contentType:r=aN.PARSABLE_DATA,closedByParent:n=!1,isVoid:i=!1,ignoreFirstLf:a=!1,preventNamespaceInheritance:s=!1,canSelfClose:o=!1}={}){this.closedByChildren={},this.closedByParent=!1,e&&e.length>0&&e.forEach(e=>this.closedByChildren[e]=!0),this.isVoid=i,this.closedByParent=n||i,this.implicitNamespacePrefix=t||null,this.contentType=r,this.ignoreFirstLf=a,this.preventNamespaceInheritance=s,this.canSelfClose=o??i}isClosedByChild(e){return this.isVoid||e.toLowerCase()in this.closedByChildren}getContentType(e){return"object"==typeof this.contentType?(void 0===e?void 0:this.contentType[e])??this.contentType.default:this.contentType}};function aH(e){return aP||(aq=new aj({canSelfClose:!0}),aP=Object.assign(Object.create(null),{base:new aj({isVoid:!0}),meta:new aj({isVoid:!0}),area:new aj({isVoid:!0}),embed:new aj({isVoid:!0}),link:new aj({isVoid:!0}),img:new aj({isVoid:!0}),input:new aj({isVoid:!0}),param:new aj({isVoid:!0}),hr:new aj({isVoid:!0}),br:new aj({isVoid:!0}),source:new aj({isVoid:!0}),track:new aj({isVoid:!0}),wbr:new aj({isVoid:!0}),p:new aj({closedByChildren:["address","article","aside","blockquote","div","dl","fieldset","footer","form","h1","h2","h3","h4","h5","h6","header","hgroup","hr","main","nav","ol","p","pre","section","table","ul"],closedByParent:!0}),thead:new aj({closedByChildren:["tbody","tfoot"]}),tbody:new aj({closedByChildren:["tbody","tfoot"],closedByParent:!0}),tfoot:new aj({closedByChildren:["tbody"],closedByParent:!0}),tr:new aj({closedByChildren:["tr"],closedByParent:!0}),td:new aj({closedByChildren:["td","th"],closedByParent:!0}),th:new aj({closedByChildren:["td","th"],closedByParent:!0}),col:new aj({isVoid:!0}),svg:new aj({implicitNamespacePrefix:"svg"}),foreignObject:new aj({implicitNamespacePrefix:"svg",preventNamespaceInheritance:!0}),math:new aj({implicitNamespacePrefix:"math"}),li:new aj({closedByChildren:["li"],closedByParent:!0}),dt:new aj({closedByChildren:["dt","dd"]}),dd:new aj({closedByChildren:["dt","dd"],closedByParent:!0}),rb:new aj({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rt:new aj({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),rtc:new aj({closedByChildren:["rb","rtc","rp"],closedByParent:!0}),rp:new aj({closedByChildren:["rb","rt","rtc","rp"],closedByParent:!0}),optgroup:new aj({closedByChildren:["optgroup"],closedByParent:!0}),option:new aj({closedByChildren:["option","optgroup"],closedByParent:!0}),pre:new aj({ignoreFirstLf:!0}),listing:new aj({ignoreFirstLf:!0}),style:new aj({contentType:aN.RAW_TEXT}),script:new aj({contentType:aN.RAW_TEXT}),title:new aj({contentType:{default:aN.ESCAPABLE_RAW_TEXT,svg:aN.PARSABLE_DATA}}),textarea:new aj({contentType:aN.ESCAPABLE_RAW_TEXT,ignoreFirstLf:!0})}),new a$().allKnownElementNames().forEach(e=>{aP[e]||null!==ak(e)||(aP[e]=new aj({canSelfClose:!1}))})),aP[e]??aq}var aV=class{constructor(e,t){this.sourceSpan=e,this.i18n=t}},aU=class extends aV{constructor(e,t,r,n){super(t,n),this.value=e,this.tokens=r,this.type="text"}visit(e,t){return e.visitText(this,t)}},az=class extends aV{constructor(e,t,r,n){super(t,n),this.value=e,this.tokens=r,this.type="cdata"}visit(e,t){return e.visitCdata(this,t)}},aG=class extends aV{constructor(e,t,r,n,i,a){super(n,a),this.switchValue=e,this.type=t,this.cases=r,this.switchValueSourceSpan=i}visit(e,t){return e.visitExpansion(this,t)}},aJ=class{constructor(e,t,r,n,i){this.value=e,this.expression=t,this.sourceSpan=r,this.valueSourceSpan=n,this.expSourceSpan=i,this.type="expansionCase"}visit(e,t){return e.visitExpansionCase(this,t)}},aK=class extends aV{constructor(e,t,r,n,i,a,s){super(r,s),this.name=e,this.value=t,this.keySpan=n,this.valueSpan=i,this.valueTokens=a,this.type="attribute"}visit(e,t){return e.visitAttribute(this,t)}get nameSpan(){return this.keySpan}},aQ=class extends aV{constructor(e,t,r,n,i,a=null,s=null,o){super(n,o),this.name=e,this.attrs=t,this.children=r,this.startSourceSpan=i,this.endSourceSpan=a,this.nameSpan=s,this.type="element"}visit(e,t){return e.visitElement(this,t)}},aZ=class{constructor(e,t){this.value=e,this.sourceSpan=t,this.type="comment"}visit(e,t){return e.visitComment(this,t)}},aX=class{constructor(e,t){this.value=e,this.sourceSpan=t,this.type="docType"}visit(e,t){return e.visitDocType(this,t)}},aY=class extends aV{constructor(e,t,r,n,i,a,s=null,o){super(n,o),this.name=e,this.parameters=t,this.children=r,this.nameSpan=i,this.startSourceSpan=a,this.endSourceSpan=s,this.type="block"}visit(e,t){return e.visitBlock(this,t)}},a0=class{constructor(e,t){this.expression=e,this.sourceSpan=t,this.type="blockParameter",this.startSourceSpan=null,this.endSourceSpan=null}visit(e,t){return e.visitBlockParameter(this,t)}},a1=class{constructor(e,t,r,n,i){this.name=e,this.value=t,this.sourceSpan=r,this.nameSpan=n,this.valueSpan=i,this.type="letDeclaration",this.startSourceSpan=null,this.endSourceSpan=null}visit(e,t){return e.visitLetDeclaration(this,t)}};function a2(e,t,r=null){let n=[],i=e.visit?t=>e.visit(t,r)||t.visit(e,r):t=>t.visit(e,r);return t.forEach(e=>{let t=i(e);t&&n.push(t)}),n}var a3=class{constructor(){}visitElement(e,t){this.visitChildren(t,t=>{t(e.attrs),t(e.children)})}visitAttribute(e,t){}visitText(e,t){}visitCdata(e,t){}visitComment(e,t){}visitDocType(e,t){}visitExpansion(e,t){return this.visitChildren(t,t=>{t(e.cases)})}visitExpansionCase(e,t){}visitBlock(e,t){this.visitChildren(t,t=>{t(e.parameters),t(e.children)})}visitBlockParameter(e,t){}visitLetDeclaration(e,t){}visitChildren(e,t){let r=[],n=this;return t(function(t){t&&r.push(a2(n,t,e))}),Array.prototype.concat.apply([],r)}},a8={AElig:"\xc6",AMP:"&",amp:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",af:"⁡",Aring:"\xc5",angst:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",colone:"≔",coloneq:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",setminus:"∖",setmn:"∖",smallsetminus:"∖",ssetmn:"∖",Barv:"⫧",Barwed:"⌆",doublebarwedge:"⌆",Bcy:"Б",Because:"∵",becaus:"∵",because:"∵",Bernoullis:"ℬ",Bscr:"ℬ",bernou:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",breve:"˘",Bumpeq:"≎",HumpDownHump:"≎",bump:"≎",CHcy:"Ч",COPY:"\xa9",copy:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",DD:"ⅅ",Cayleys:"ℭ",Cfr:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",cedil:"\xb8",CenterDot:"\xb7",centerdot:"\xb7",middot:"\xb7",Chi:"Χ",CircleDot:"⊙",odot:"⊙",CircleMinus:"⊖",ominus:"⊖",CirclePlus:"⊕",oplus:"⊕",CircleTimes:"⊗",otimes:"⊗",ClockwiseContourIntegral:"∲",cwconint:"∲",CloseCurlyDoubleQuote:"”",rdquo:"”",rdquor:"”",CloseCurlyQuote:"’",rsquo:"’",rsquor:"’",Colon:"∷",Proportion:"∷",Colone:"⩴",Congruent:"≡",equiv:"≡",Conint:"∯",DoubleContourIntegral:"∯",ContourIntegral:"∮",conint:"∮",oint:"∮",Copf:"ℂ",complexes:"ℂ",Coproduct:"∐",coprod:"∐",CounterClockwiseContourIntegral:"∳",awconint:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",asympeq:"≍",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",ddagger:"‡",Darr:"↡",Dashv:"⫤",DoubleLeftTee:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",nabla:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",acute:"\xb4",DiacriticalDot:"˙",dot:"˙",DiacriticalDoubleAcute:"˝",dblac:"˝",DiacriticalGrave:"`",grave:"`",DiacriticalTilde:"˜",tilde:"˜",Diamond:"⋄",diam:"⋄",diamond:"⋄",DifferentialD:"ⅆ",dd:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DoubleDot:"\xa8",die:"\xa8",uml:"\xa8",DotDot:"⃜",DotEqual:"≐",doteq:"≐",esdot:"≐",DoubleDownArrow:"⇓",Downarrow:"⇓",dArr:"⇓",DoubleLeftArrow:"⇐",Leftarrow:"⇐",lArr:"⇐",DoubleLeftRightArrow:"⇔",Leftrightarrow:"⇔",hArr:"⇔",iff:"⇔",DoubleLongLeftArrow:"⟸",Longleftarrow:"⟸",xlArr:"⟸",DoubleLongLeftRightArrow:"⟺",Longleftrightarrow:"⟺",xhArr:"⟺",DoubleLongRightArrow:"⟹",Longrightarrow:"⟹",xrArr:"⟹",DoubleRightArrow:"⇒",Implies:"⇒",Rightarrow:"⇒",rArr:"⇒",DoubleRightTee:"⊨",vDash:"⊨",DoubleUpArrow:"⇑",Uparrow:"⇑",uArr:"⇑",DoubleUpDownArrow:"⇕",Updownarrow:"⇕",vArr:"⇕",DoubleVerticalBar:"∥",par:"∥",parallel:"∥",shortparallel:"∥",spar:"∥",DownArrow:"↓",ShortDownArrow:"↓",darr:"↓",downarrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",duarr:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",leftharpoondown:"↽",lhard:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",rhard:"⇁",rightharpoondown:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",top:"⊤",DownTeeArrow:"↧",mapstodown:"↧",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",in:"∈",isin:"∈",isinv:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",eqsim:"≂",esim:"≂",Equilibrium:"⇌",rightleftharpoons:"⇌",rlhar:"⇌",Escr:"ℰ",expectation:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",exist:"∃",ExponentialE:"ⅇ",ee:"ⅇ",exponentiale:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",blacksquare:"▪",squarf:"▪",squf:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",forall:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",gt:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",ggg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",ge:"≥",geq:"≥",GreaterEqualLess:"⋛",gel:"⋛",gtreqless:"⋛",GreaterFullEqual:"≧",gE:"≧",geqq:"≧",GreaterGreater:"⪢",GreaterLess:"≷",gl:"≷",gtrless:"≷",GreaterSlantEqual:"⩾",geqslant:"⩾",ges:"⩾",GreaterTilde:"≳",gsim:"≳",gtrsim:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",NestedGreaterGreater:"≫",gg:"≫",HARDcy:"Ъ",Hacek:"ˇ",caron:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",Poincareplane:"ℌ",HilbertSpace:"ℋ",Hscr:"ℋ",hamilt:"ℋ",Hopf:"ℍ",quaternions:"ℍ",HorizontalLine:"─",boxh:"─",Hstrok:"Ħ",HumpEqual:"≏",bumpe:"≏",bumpeq:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Im:"ℑ",image:"ℑ",imagpart:"ℑ",Igrave:"\xcc",Imacr:"Ī",ImaginaryI:"ⅈ",ii:"ⅈ",Int:"∬",Integral:"∫",int:"∫",Intersection:"⋂",bigcap:"⋂",xcap:"⋂",InvisibleComma:"⁣",ic:"⁣",InvisibleTimes:"⁢",it:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",imagline:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",lt:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Lscr:"ℒ",lagran:"ℒ",Larr:"↞",twoheadleftarrow:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",lang:"⟨",langle:"⟨",LeftArrow:"←",ShortLeftArrow:"←",larr:"←",leftarrow:"←",slarr:"←",LeftArrowBar:"⇤",larrb:"⇤",LeftArrowRightArrow:"⇆",leftrightarrows:"⇆",lrarr:"⇆",LeftCeiling:"⌈",lceil:"⌈",LeftDoubleBracket:"⟦",lobrk:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",dharl:"⇃",downharpoonleft:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",lfloor:"⌊",LeftRightArrow:"↔",harr:"↔",leftrightarrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",dashv:"⊣",LeftTeeArrow:"↤",mapstoleft:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",vartriangleleft:"⊲",vltri:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",ltrie:"⊴",trianglelefteq:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",uharl:"↿",upharpoonleft:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",leftharpoonup:"↼",lharu:"↼",LeftVectorBar:"⥒",LessEqualGreater:"⋚",leg:"⋚",lesseqgtr:"⋚",LessFullEqual:"≦",lE:"≦",leqq:"≦",LessGreater:"≶",lessgtr:"≶",lg:"≶",LessLess:"⪡",LessSlantEqual:"⩽",leqslant:"⩽",les:"⩽",LessTilde:"≲",lesssim:"≲",lsim:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",lAarr:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",longleftarrow:"⟵",xlarr:"⟵",LongLeftRightArrow:"⟷",longleftrightarrow:"⟷",xharr:"⟷",LongRightArrow:"⟶",longrightarrow:"⟶",xrarr:"⟶",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",swarr:"↙",swarrow:"↙",LowerRightArrow:"↘",searr:"↘",searrow:"↘",Lsh:"↰",lsh:"↰",Lstrok:"Ł",Lt:"≪",NestedLessLess:"≪",ll:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mscr:"ℳ",phmmat:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",mnplus:"∓",mp:"∓",Mopf:"\uD835\uDD44",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",ZeroWidthSpace:"​",NewLine:`
`,Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",nbsp:"\xa0",Nopf:"ℕ",naturals:"ℕ",Not:"⫬",NotCongruent:"≢",nequiv:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",npar:"∦",nparallel:"∦",nshortparallel:"∦",nspar:"∦",NotElement:"∉",notin:"∉",notinva:"∉",NotEqual:"≠",ne:"≠",NotEqualTilde:"≂̸",nesim:"≂̸",NotExists:"∄",nexist:"∄",nexists:"∄",NotGreater:"≯",ngt:"≯",ngtr:"≯",NotGreaterEqual:"≱",nge:"≱",ngeq:"≱",NotGreaterFullEqual:"≧̸",ngE:"≧̸",ngeqq:"≧̸",NotGreaterGreater:"≫̸",nGtv:"≫̸",NotGreaterLess:"≹",ntgl:"≹",NotGreaterSlantEqual:"⩾̸",ngeqslant:"⩾̸",nges:"⩾̸",NotGreaterTilde:"≵",ngsim:"≵",NotHumpDownHump:"≎̸",nbump:"≎̸",NotHumpEqual:"≏̸",nbumpe:"≏̸",NotLeftTriangle:"⋪",nltri:"⋪",ntriangleleft:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",nltrie:"⋬",ntrianglelefteq:"⋬",NotLess:"≮",nless:"≮",nlt:"≮",NotLessEqual:"≰",nle:"≰",nleq:"≰",NotLessGreater:"≸",ntlg:"≸",NotLessLess:"≪̸",nLtv:"≪̸",NotLessSlantEqual:"⩽̸",nleqslant:"⩽̸",nles:"⩽̸",NotLessTilde:"≴",nlsim:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",npr:"⊀",nprec:"⊀",NotPrecedesEqual:"⪯̸",npre:"⪯̸",npreceq:"⪯̸",NotPrecedesSlantEqual:"⋠",nprcue:"⋠",NotReverseElement:"∌",notni:"∌",notniva:"∌",NotRightTriangle:"⋫",nrtri:"⋫",ntriangleright:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",nrtrie:"⋭",ntrianglerighteq:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",nsqsube:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",nsqsupe:"⋣",NotSubset:"⊂⃒",nsubset:"⊂⃒",vnsub:"⊂⃒",NotSubsetEqual:"⊈",nsube:"⊈",nsubseteq:"⊈",NotSucceeds:"⊁",nsc:"⊁",nsucc:"⊁",NotSucceedsEqual:"⪰̸",nsce:"⪰̸",nsucceq:"⪰̸",NotSucceedsSlantEqual:"⋡",nsccue:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",nsupset:"⊃⃒",vnsup:"⊃⃒",NotSupersetEqual:"⊉",nsupe:"⊉",nsupseteq:"⊉",NotTilde:"≁",nsim:"≁",NotTildeEqual:"≄",nsime:"≄",nsimeq:"≄",NotTildeFullEqual:"≇",ncong:"≇",NotTildeTilde:"≉",nap:"≉",napprox:"≉",NotVerticalBar:"∤",nmid:"∤",nshortmid:"∤",nsmid:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",ohm:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",ldquo:"“",OpenCurlyQuote:"‘",lsquo:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",oline:"‾",OverBrace:"⏞",OverBracket:"⎴",tbrk:"⎴",OverParenthesis:"⏜",PartialD:"∂",part:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",plusmn:"\xb1",pm:"\xb1",Popf:"ℙ",primes:"ℙ",Pr:"⪻",Precedes:"≺",pr:"≺",prec:"≺",PrecedesEqual:"⪯",pre:"⪯",preceq:"⪯",PrecedesSlantEqual:"≼",prcue:"≼",preccurlyeq:"≼",PrecedesTilde:"≾",precsim:"≾",prsim:"≾",Prime:"″",Product:"∏",prod:"∏",Proportional:"∝",prop:"∝",propto:"∝",varpropto:"∝",vprop:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',quot:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",rationals:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",drbkarow:"⤐",REG:"\xae",circledR:"\xae",reg:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",twoheadrightarrow:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",Rfr:"ℜ",real:"ℜ",realpart:"ℜ",ReverseElement:"∋",SuchThat:"∋",ni:"∋",niv:"∋",ReverseEquilibrium:"⇋",leftrightharpoons:"⇋",lrhar:"⇋",ReverseUpEquilibrium:"⥯",duhar:"⥯",Rho:"Ρ",RightAngleBracket:"⟩",rang:"⟩",rangle:"⟩",RightArrow:"→",ShortRightArrow:"→",rarr:"→",rightarrow:"→",srarr:"→",RightArrowBar:"⇥",rarrb:"⇥",RightArrowLeftArrow:"⇄",rightleftarrows:"⇄",rlarr:"⇄",RightCeiling:"⌉",rceil:"⌉",RightDoubleBracket:"⟧",robrk:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",dharr:"⇂",downharpoonright:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",rfloor:"⌋",RightTee:"⊢",vdash:"⊢",RightTeeArrow:"↦",map:"↦",mapsto:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",vartriangleright:"⊳",vrtri:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",rtrie:"⊵",trianglerighteq:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",uharr:"↾",upharpoonright:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",rharu:"⇀",rightharpoonup:"⇀",RightVectorBar:"⥓",Ropf:"ℝ",reals:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",rAarr:"⇛",Rscr:"ℛ",realine:"ℛ",Rsh:"↱",rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortUpArrow:"↑",UpArrow:"↑",uarr:"↑",uparrow:"↑",Sigma:"Σ",SmallCircle:"∘",compfn:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",radic:"√",Square:"□",squ:"□",square:"□",SquareIntersection:"⊓",sqcap:"⊓",SquareSubset:"⊏",sqsub:"⊏",sqsubset:"⊏",SquareSubsetEqual:"⊑",sqsube:"⊑",sqsubseteq:"⊑",SquareSuperset:"⊐",sqsup:"⊐",sqsupset:"⊐",SquareSupersetEqual:"⊒",sqsupe:"⊒",sqsupseteq:"⊒",SquareUnion:"⊔",sqcup:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",sstarf:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",sube:"⊆",subseteq:"⊆",Succeeds:"≻",sc:"≻",succ:"≻",SucceedsEqual:"⪰",sce:"⪰",succeq:"⪰",SucceedsSlantEqual:"≽",sccue:"≽",succcurlyeq:"≽",SucceedsTilde:"≿",scsim:"≿",succsim:"≿",Sum:"∑",sum:"∑",Sup:"⋑",Supset:"⋑",Superset:"⊃",sup:"⊃",supset:"⊃",SupersetEqual:"⊇",supe:"⊇",supseteq:"⊇",THORN:"\xde",TRADE:"™",trade:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",there4:"∴",therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",thinsp:" ",Tilde:"∼",sim:"∼",thicksim:"∼",thksim:"∼",TildeEqual:"≃",sime:"≃",simeq:"≃",TildeFullEqual:"≅",cong:"≅",TildeTilde:"≈",ap:"≈",approx:"≈",asymp:"≈",thickapprox:"≈",thkap:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",tdot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",lowbar:"_",UnderBrace:"⏟",UnderBracket:"⎵",bbrk:"⎵",UnderParenthesis:"⏝",Union:"⋃",bigcup:"⋃",xcup:"⋃",UnionPlus:"⊎",uplus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",udarr:"⇅",UpDownArrow:"↕",updownarrow:"↕",varr:"↕",UpEquilibrium:"⥮",udhar:"⥮",UpTee:"⊥",bot:"⊥",bottom:"⊥",perp:"⊥",UpTeeArrow:"↥",mapstoup:"↥",UpperLeftArrow:"↖",nwarr:"↖",nwarrow:"↖",UpperRightArrow:"↗",nearr:"↗",nearrow:"↗",Upsi:"ϒ",upsih:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",bigvee:"⋁",xvee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",mid:"∣",shortmid:"∣",smid:"∣",VerticalLine:"|",verbar:"|",vert:"|",VerticalSeparator:"❘",VerticalTilde:"≀",wr:"≀",wreath:"≀",VeryThinSpace:" ",hairsp:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",bigwedge:"⋀",xwedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",Zeta:"Ζ",Zfr:"ℨ",zeetrf:"ℨ",Zopf:"ℤ",integers:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",mstpos:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acy:"а",aelig:"\xe6",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",and:"∧",wedge:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",angle:"∠",ange:"⦤",angmsd:"∡",measuredangle:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",apE:"⩰",apacir:"⩯",ape:"≊",approxeq:"≊",apid:"≋",apos:"'",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",midast:"*",atilde:"\xe3",auml:"\xe4",awint:"⨑",bNot:"⫭",backcong:"≌",bcong:"≌",backepsilon:"϶",bepsi:"϶",backprime:"‵",bprime:"‵",backsim:"∽",bsim:"∽",backsimeq:"⋍",bsime:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrktbrk:"⎶",bcy:"б",bdquo:"„",ldquor:"„",bemptyv:"⦰",beta:"β",beth:"ℶ",between:"≬",twixt:"≬",bfr:"\uD835\uDD1F",bigcirc:"◯",xcirc:"◯",bigodot:"⨀",xodot:"⨀",bigoplus:"⨁",xoplus:"⨁",bigotimes:"⨂",xotime:"⨂",bigsqcup:"⨆",xsqcup:"⨆",bigstar:"★",starf:"★",bigtriangledown:"▽",xdtri:"▽",bigtriangleup:"△",xutri:"△",biguplus:"⨄",xuplus:"⨄",bkarow:"⤍",rbarr:"⤍",blacklozenge:"⧫",lozf:"⧫",blacktriangle:"▴",utrif:"▴",blacktriangledown:"▾",dtrif:"▾",blacktriangleleft:"◂",ltrif:"◂",blacktriangleright:"▸",rtrif:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",minusb:"⊟",boxplus:"⊞",plusb:"⊞",boxtimes:"⊠",timesb:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bumpE:"⪮",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cemptyv:"⦲",cent:"\xa2",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",cire:"≗",circlearrowleft:"↺",olarr:"↺",circlearrowright:"↻",orarr:"↻",circledS:"Ⓢ",oS:"Ⓢ",circledast:"⊛",oast:"⊛",circledcirc:"⊚",ocir:"⊚",circleddash:"⊝",odash:"⊝",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",comma:",",commat:"@",comp:"∁",complement:"∁",congdot:"⩭",copf:"\uD835\uDD54",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",curlyeqprec:"⋞",cuesc:"⋟",curlyeqsucc:"⋟",cularr:"↶",curvearrowleft:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curvearrowright:"↷",curarrm:"⤼",curlyvee:"⋎",cuvee:"⋎",curlywedge:"⋏",cuwed:"⋏",curren:"\xa4",cwint:"∱",cylcty:"⌭",dHar:"⥥",dagger:"†",daleth:"ℸ",dash:"‐",hyphen:"‐",dbkarow:"⤏",rBarr:"⤏",dcaron:"ď",dcy:"д",ddarr:"⇊",downdownarrows:"⇊",ddotseq:"⩷",eDDot:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",diamondsuit:"♦",diams:"♦",digamma:"ϝ",gammad:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",llcorner:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",doteqdot:"≑",eDot:"≑",dotminus:"∸",minusd:"∸",dotplus:"∔",plusdo:"∔",dotsquare:"⊡",sdotb:"⊡",drcorn:"⌟",lrcorner:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",triangledown:"▿",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",eqcirc:"≖",ecirc:"\xea",ecolon:"≕",eqcolon:"≕",ecy:"э",edot:"ė",efDot:"≒",fallingdotseq:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",eqslantgtr:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",eqslantless:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",varnothing:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",straightepsilon:"ϵ",varepsilon:"ϵ",equals:"=",equest:"≟",questeq:"≟",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",risingdotseq:"≓",erarr:"⥱",escr:"ℯ",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",fork:"⋔",pitchfork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",half:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",sfrown:"⌢",fscr:"\uD835\uDCBB",gEl:"⪌",gtreqqless:"⪌",gacute:"ǵ",gamma:"γ",gap:"⪆",gtrapprox:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gimel:"ℷ",gjcy:"ѓ",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gneqq:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gnsim:"⋧",gopf:"\uD835\uDD58",gscr:"ℊ",gsime:"⪎",gsiml:"⪐",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtrdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrarr:"⥸",gvertneqq:"≩︀",gvnE:"≩︀",hardcy:"ъ",harrcir:"⥈",harrw:"↭",leftrightsquigarrow:"↭",hbar:"ℏ",hslash:"ℏ",planck:"ℏ",plankv:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",mldr:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",searhk:"⤥",hkswarow:"⤦",swarhk:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",larrhk:"↩",hookrightarrow:"↪",rarrhk:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hstrok:"ħ",hybull:"⁃",iacute:"\xed",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",ifr:"\uD835\uDD26",igrave:"\xec",iiiint:"⨌",qint:"⨌",iiint:"∭",tint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",imath:"ı",inodot:"ı",imof:"⊷",imped:"Ƶ",incare:"℅",infin:"∞",infintie:"⧝",intcal:"⊺",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iquest:"\xbf",iscr:"\uD835\uDCBE",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",varkappa:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAtail:"⤛",lBarr:"⤎",lEg:"⪋",lesseqqgtr:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lambda:"λ",langd:"⦑",lap:"⪅",lessapprox:"⪅",laquo:"\xab",larrbfs:"⤟",larrfs:"⤝",larrlp:"↫",looparrowleft:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",leftarrowtail:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lcub:"{",lbrack:"[",lsqb:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lcy:"л",ldca:"⤶",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leq:"≤",leftleftarrows:"⇇",llarr:"⇇",leftthreetimes:"⋋",lthree:"⋋",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessdot:"⋖",ltdot:"⋖",lfisht:"⥼",lfr:"\uD835\uDD29",lgE:"⪑",lharul:"⥪",lhblk:"▄",ljcy:"љ",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lneqq:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lnsim:"⋦",loang:"⟬",loarr:"⇽",longmapsto:"⟼",xmap:"⟼",looparrowright:"↬",rarrlp:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",loz:"◊",lozenge:"◊",lpar:"(",lparlt:"⦓",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsime:"⪍",lsimg:"⪏",lsquor:"‚",sbquo:"‚",lstrok:"ł",ltcc:"⪦",ltcir:"⩹",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",triangleleft:"◃",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",strns:"\xaf",male:"♂",malt:"✠",maltese:"✠",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",midcir:"⫰",minus:"−",minusdu:"⨪",mlcp:"⫛",models:"⊧",mopf:"\uD835\uDD5E",mscr:"\uD835\uDCC2",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nLeftarrow:"⇍",nlArr:"⇍",nLeftrightarrow:"⇎",nhArr:"⇎",nLl:"⋘̸",nLt:"≪⃒",nRightarrow:"⇏",nrArr:"⇏",nVDash:"⊯",nVdash:"⊮",nacute:"ń",nang:"∠⃒",napE:"⩰̸",napid:"≋̸",napos:"ŉ",natur:"♮",natural:"♮",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",neArr:"⇗",nearhk:"⤤",nedot:"≐̸",nesear:"⤨",toea:"⤨",nfr:"\uD835\uDD2B",nharr:"↮",nleftrightarrow:"↮",nhpar:"⫲",nis:"⋼",nisd:"⋺",njcy:"њ",nlE:"≦̸",nleqq:"≦̸",nlarr:"↚",nleftarrow:"↚",nldr:"‥",nopf:"\uD835\uDD5F",not:"\xac",notinE:"⋹̸",notindot:"⋵̸",notinvb:"⋷",notinvc:"⋶",notnivb:"⋾",notnivc:"⋽",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",nrarr:"↛",nrightarrow:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nscr:"\uD835\uDCC3",nsub:"⊄",nsubE:"⫅̸",nsubseteqq:"⫅̸",nsup:"⊅",nsupE:"⫆̸",nsupseteqq:"⫆̸",ntilde:"\xf1",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwnear:"⤧",oacute:"\xf3",ocirc:"\xf4",ocy:"о",odblac:"ő",odiv:"⨸",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",olcir:"⦾",olcross:"⦻",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",or:"∨",vee:"∨",ord:"⩝",order:"ℴ",orderof:"ℴ",oscr:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",para:"\xb6",parsim:"⫳",parsl:"⫽",pcy:"п",percnt:"%",period:".",permil:"‰",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",straightphi:"ϕ",varphi:"ϕ",phone:"☎",pi:"π",piv:"ϖ",varpi:"ϖ",planckh:"ℎ",plus:"+",plusacir:"⨣",pluscir:"⨢",plusdu:"⨥",pluse:"⩲",plussim:"⨦",plustwo:"⨧",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",prE:"⪳",prap:"⪷",precapprox:"⪷",precnapprox:"⪹",prnap:"⪹",precneqq:"⪵",prnE:"⪵",precnsim:"⋨",prnsim:"⋨",prime:"′",profalar:"⌮",profline:"⌒",profsurf:"⌓",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quatint:"⨖",quest:"?",rAtail:"⤜",rHar:"⥤",race:"∽̱",racute:"ŕ",raemptyv:"⦳",rangd:"⦒",range:"⦥",raquo:"\xbb",rarrap:"⥵",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rightarrowtail:"↣",rarrw:"↝",rightsquigarrow:"↝",ratail:"⤚",ratio:"∶",rbbrk:"❳",rbrace:"}",rcub:"}",rbrack:"]",rsqb:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdsh:"↳",rect:"▭",rfisht:"⥽",rfr:"\uD835\uDD2F",rharul:"⥬",rho:"ρ",rhov:"ϱ",varrho:"ϱ",rightrightarrows:"⇉",rrarr:"⇉",rightthreetimes:"⋌",rthree:"⋌",ring:"˚",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rsaquo:"›",rscr:"\uD835\uDCC7",rtimes:"⋊",rtri:"▹",triangleright:"▹",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",scE:"⪴",scap:"⪸",succapprox:"⪸",scaron:"š",scedil:"ş",scirc:"ŝ",scnE:"⪶",succneqq:"⪶",scnap:"⪺",succnapprox:"⪺",scnsim:"⋩",succnsim:"⋩",scpolint:"⨓",scy:"с",sdot:"⋅",sdote:"⩦",seArr:"⇘",sect:"\xa7",semi:";",seswar:"⤩",tosa:"⤩",sext:"✶",sfr:"\uD835\uDD30",sharp:"♯",shchcy:"щ",shcy:"ш",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",varsigma:"ς",simdot:"⩪",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",smashp:"⨳",smeparsl:"⧤",smile:"⌣",ssmile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",sqcaps:"⊓︀",sqcups:"⊔︀",sscr:"\uD835\uDCC8",star:"☆",sub:"⊂",subset:"⊂",subE:"⫅",subseteqq:"⫅",subdot:"⪽",subedot:"⫃",submult:"⫁",subnE:"⫋",subsetneqq:"⫋",subne:"⊊",subsetneq:"⊊",subplus:"⪿",subrarr:"⥹",subsim:"⫇",subsub:"⫕",subsup:"⫓",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",supE:"⫆",supseteqq:"⫆",supdot:"⪾",supdsub:"⫘",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supsetneqq:"⫌",supne:"⊋",supsetneq:"⊋",supplus:"⫀",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tcaron:"ť",tcedil:"ţ",tcy:"т",telrec:"⌕",tfr:"\uD835\uDD31",theta:"θ",thetasym:"ϑ",thetav:"ϑ",vartheta:"ϑ",thorn:"\xfe",times:"\xd7",timesbar:"⨱",timesd:"⨰",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tprime:"‴",triangle:"▵",utri:"▵",triangleq:"≜",trie:"≜",tridot:"◬",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",uHar:"⥣",uacute:"\xfa",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udblac:"ű",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uogon:"ų",uopf:"\uD835\uDD66",upsi:"υ",upsilon:"υ",upuparrows:"⇈",uuarr:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",uuml:"\xfc",uwangle:"⦧",vBar:"⫨",vBarv:"⫩",vangrt:"⦜",varsubsetneq:"⊊︀",vsubne:"⊊︀",varsubsetneqq:"⫋︀",vsubnE:"⫋︀",varsupsetneq:"⊋︀",vsupne:"⊋︀",varsupsetneqq:"⫌︀",vsupnE:"⫌︀",vcy:"в",veebar:"⊻",veeeq:"≚",vellip:"⋮",vfr:"\uD835\uDD33",vopf:"\uD835\uDD67",vscr:"\uD835\uDCCB",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedgeq:"≙",weierp:"℘",wp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wscr:"\uD835\uDCCC",xfr:"\uD835\uDD35",xi:"ξ",xnis:"⋻",xopf:"\uD835\uDD69",xscr:"\uD835\uDCCD",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"};a8.ngsp="";var a6=[/@/,/^\s*$/,/[<>]/,/^[{}]$/,/&(#|[a-z])/i,/^\/\//],a5=new class e{static fromArray(t){return t?(function(e,t){if(null!=t&&!(Array.isArray(t)&&2==t.length))throw Error(`Expected '${e}' to be an array, [start, end].`);if(null!=t){let e=t[0],r=t[1];a6.forEach(t=>{if(t.test(e)||t.test(r))throw Error(`['${e}', '${r}'] contains unusable interpolation symbol.`)})}}("interpolation",t),new e(t[0],t[1])):a5}constructor(e,t){this.start=e,this.end=t}}("{{","}}"),a7=class extends ad{constructor(e,t,r){super(r,e),this.tokenType=t}},a9=class{constructor(e,t,r){this.tokens=e,this.errors=t,this.nonNormalizedIcuExpressions=r}},a4=/\r\n?/g;function se(e){return`Unexpected character "${0===e?"EOF":String.fromCharCode(e)}"`}function st(e){return`Unknown entity "${e}" - use the "&#<decimal>;" or  "&#x<hex>;" syntax`}!function(e){e.HEX="hexadecimal",e.DEC="decimal"}(sk||(sk={}));var sr=class{constructor(e){this.error=e}},sn=class{constructor(e,t,r){this._getTagContentType=t,this._currentTokenStart=null,this._currentTokenType=null,this._expansionCaseStack=[],this._inInterpolation=!1,this._fullNameStack=[],this.tokens=[],this.errors=[],this.nonNormalizedIcuExpressions=[],this._tokenizeIcu=r.tokenizeExpansionForms||!1,this._interpolationConfig=r.interpolationConfig||a5,this._leadingTriviaCodePoints=r.leadingTriviaChars&&r.leadingTriviaChars.map(e=>e.codePointAt(0)||0),this._canSelfClose=r.canSelfClose||!1,this._allowHtmComponentClosingTags=r.allowHtmComponentClosingTags||!1;let n=r.range||{endPos:e.content.length,startPos:0,startLine:0,startCol:0};this._cursor=r.escapedString?new sd(e,n):new sh(e,n),this._preserveLineEndings=r.preserveLineEndings||!1,this._i18nNormalizeLineEndingsInICUs=r.i18nNormalizeLineEndingsInICUs||!1,this._tokenizeBlocks=r.tokenizeBlocks??!0,this._tokenizeLet=r.tokenizeLet??!0;try{this._cursor.init()}catch(e){this.handleError(e)}}_processCarriageReturns(e){return this._preserveLineEndings?e:e.replace(a4,`
`)}tokenize(){for(;0!==this._cursor.peek();){let e=this._cursor.clone();try{if(this._attemptCharCode(60))if(this._attemptCharCode(33))this._attemptStr("[CDATA[")?this._consumeCdata(e):this._attemptStr("--")?this._consumeComment(e):this._attemptStrCaseInsensitive("doctype")?this._consumeDocType(e):this._consumeBogusComment(e);else if(this._attemptCharCode(47))this._consumeTagClose(e);else{let t=this._cursor.clone();this._attemptCharCode(63)?(this._cursor=t,this._consumeBogusComment(e)):this._consumeTagOpen(e)}else this._tokenizeLet&&64===this._cursor.peek()&&!this._inInterpolation&&this._attemptStr("@let")?this._consumeLetDeclaration(e):this._tokenizeBlocks&&this._attemptCharCode(64)?this._consumeBlockStart(e):this._tokenizeBlocks&&!this._inInterpolation&&!this._isInExpansionCase()&&!this._isInExpansionForm()&&this._attemptCharCode(125)?this._consumeBlockEnd(e):this._tokenizeIcu&&this._tokenizeExpansionForm()||this._consumeWithInterpolation(5,8,()=>this._isTextEnd(),()=>this._isTagStart())}catch(e){this.handleError(e)}}this._beginToken(34),this._endToken([])}_getBlockName(){let e=!1,t=this._cursor.clone();return this._attemptCharCodeUntilFn(t=>at(t)?!e:!su(t)||(e=!0,!1)),this._cursor.getChars(t).trim()}_consumeBlockStart(e){this._beginToken(25,e);let t=this._endToken([this._getBlockName()]);if(40===this._cursor.peek())if(this._cursor.advance(),this._consumeBlockParameters(),this._attemptCharCodeUntilFn(si),this._attemptCharCode(41))this._attemptCharCodeUntilFn(si);else{t.type=29;return}this._attemptCharCode(123)?(this._beginToken(26),this._endToken([])):t.type=29}_consumeBlockEnd(e){this._beginToken(27,e),this._endToken([])}_consumeBlockParameters(){for(this._attemptCharCodeUntilFn(sc);41!==this._cursor.peek()&&0!==this._cursor.peek();){this._beginToken(28);let e=this._cursor.clone(),t=null,r=0;for(;59!==this._cursor.peek()&&0!==this._cursor.peek()||null!==t;){let e=this._cursor.peek();if(92===e)this._cursor.advance();else if(e===t)t=null;else if(null===t&&as(e))t=e;else if(40===e&&null===t)r++;else if(41===e&&null===t){if(0===r)break;r>0&&r--}this._cursor.advance()}this._endToken([this._cursor.getChars(e)]),this._attemptCharCodeUntilFn(sc)}}_consumeLetDeclaration(e){if(this._beginToken(30,e),at(this._cursor.peek()))this._attemptCharCodeUntilFn(si);else{this._endToken([this._cursor.getChars(e)]).type=33;return}let t=this._endToken([this._getLetDeclarationName()]);if(this._attemptCharCodeUntilFn(si),!this._attemptCharCode(61)){t.type=33;return}this._attemptCharCodeUntilFn(e=>si(e)&&!ai(e)),this._consumeLetDeclarationValue(),59===this._cursor.peek()?(this._beginToken(32),this._endToken([]),this._cursor.advance()):(t.type=33,t.sourceSpan=this._cursor.getSpan(e))}_getLetDeclarationName(){let e=this._cursor.clone(),t=!1;return this._attemptCharCodeUntilFn(e=>!(an(e)||36===e||95===e||t&&ar(e))||(t=!0,!1)),this._cursor.getChars(e).trim()}_consumeLetDeclarationValue(){let e=this._cursor.clone();for(this._beginToken(31,e);0!==this._cursor.peek();){let e=this._cursor.peek();if(59===e)break;as(e)&&(this._cursor.advance(),this._attemptCharCodeUntilFn(t=>92===t?(this._cursor.advance(),!1):t===e)),this._cursor.advance()}this._endToken([this._cursor.getChars(e)])}_tokenizeExpansionForm(){if(this.isExpansionFormStart())return this._consumeExpansionFormStart(),!0;if(125!==this._cursor.peek()&&this._isInExpansionForm())return this._consumeExpansionCaseStart(),!0;if(125===this._cursor.peek()){if(this._isInExpansionCase())return this._consumeExpansionCaseEnd(),!0;if(this._isInExpansionForm())return this._consumeExpansionFormEnd(),!0}return!1}_beginToken(e,t=this._cursor.clone()){this._currentTokenStart=t,this._currentTokenType=e}_endToken(e,t){if(null===this._currentTokenStart)throw new a7("Programming error - attempted to end a token when there was no start to the token",this._currentTokenType,this._cursor.getSpan(t));if(null===this._currentTokenType)throw new a7("Programming error - attempted to end a token which has no token type",null,this._cursor.getSpan(this._currentTokenStart));let r={type:this._currentTokenType,parts:e,sourceSpan:(t??this._cursor).getSpan(this._currentTokenStart,this._leadingTriviaCodePoints)};return this.tokens.push(r),this._currentTokenStart=null,this._currentTokenType=null,r}_createError(e,t){this._isInExpansionForm()&&(e+=' (Do you have an unescaped "{" in your template? Use "{{ \'{\' }}") to escape it.)');let r=new a7(e,this._currentTokenType,t);return this._currentTokenStart=null,this._currentTokenType=null,new sr(r)}handleError(e){if(e instanceof sp&&(e=this._createError(e.msg,this._cursor.getSpan(e.cursor))),e instanceof sr)this.errors.push(e.error);else throw e}_attemptCharCode(e){return this._cursor.peek()===e&&(this._cursor.advance(),!0)}_attemptCharCodeCaseInsensitive(e){var t,r;return t=this._cursor.peek(),r=e,sl(t)===sl(r)&&(this._cursor.advance(),!0)}_requireCharCode(e){let t=this._cursor.clone();if(!this._attemptCharCode(e))throw this._createError(se(this._cursor.peek()),this._cursor.getSpan(t))}_attemptStr(e){let t=e.length;if(this._cursor.charsLeft()<t)return!1;let r=this._cursor.clone();for(let n=0;n<t;n++)if(!this._attemptCharCode(e.charCodeAt(n)))return this._cursor=r,!1;return!0}_attemptStrCaseInsensitive(e){for(let t=0;t<e.length;t++)if(!this._attemptCharCodeCaseInsensitive(e.charCodeAt(t)))return!1;return!0}_requireStr(e){let t=this._cursor.clone();if(!this._attemptStr(e))throw this._createError(se(this._cursor.peek()),this._cursor.getSpan(t))}_requireStrCaseInsensitive(e){let t=this._cursor.clone();if(!this._attemptStrCaseInsensitive(e))throw this._createError(se(this._cursor.peek()),this._cursor.getSpan(t))}_attemptCharCodeUntilFn(e){for(;!e(this._cursor.peek());)this._cursor.advance()}_requireCharCodeUntilFn(e,t){let r=this._cursor.clone();if(this._attemptCharCodeUntilFn(e),this._cursor.diff(r)<t)throw this._createError(se(this._cursor.peek()),this._cursor.getSpan(r))}_attemptUntilChar(e){for(;this._cursor.peek()!==e;)this._cursor.advance()}_readChar(){let e=String.fromCodePoint(this._cursor.peek());return this._cursor.advance(),e}_consumeEntity(e){this._beginToken(9);let t=this._cursor.clone();if(this._cursor.advance(),this._attemptCharCode(35)){let e=this._attemptCharCode(120)||this._attemptCharCode(88),n=this._cursor.clone();if(this._attemptCharCodeUntilFn(ss),59!=this._cursor.peek()){var r;this._cursor.advance();let n=e?sk.HEX:sk.DEC;throw this._createError((r=this._cursor.getChars(t),`Unable to parse entity "${r}" - ${n} character reference entities must end with ";"`),this._cursor.getSpan())}let i=this._cursor.getChars(n);this._cursor.advance();try{let r=parseInt(i,e?16:10);this._endToken([String.fromCharCode(r),this._cursor.getChars(t)])}catch{throw this._createError(st(this._cursor.getChars(t)),this._cursor.getSpan())}}else{let r=this._cursor.clone();if(this._attemptCharCodeUntilFn(so),59!=this._cursor.peek())this._beginToken(e,t),this._cursor=r,this._endToken(["&"]);else{let e=this._cursor.getChars(r);this._cursor.advance();let n=a8[e];if(!n)throw this._createError(st(e),this._cursor.getSpan(t));this._endToken([n,`&${e};`])}}}_consumeRawText(e,t){this._beginToken(e?6:7);let r=[];for(;;){let n=this._cursor.clone(),i=t();if(this._cursor=n,i)break;e&&38===this._cursor.peek()?(this._endToken([this._processCarriageReturns(r.join(""))]),r.length=0,this._consumeEntity(6),this._beginToken(6)):r.push(this._readChar())}this._endToken([this._processCarriageReturns(r.join(""))])}_consumeComment(e){this._beginToken(10,e),this._endToken([]),this._consumeRawText(!1,()=>this._attemptStr("--\x3e")),this._beginToken(11),this._requireStr("--\x3e"),this._endToken([])}_consumeBogusComment(e){this._beginToken(10,e),this._endToken([]),this._consumeRawText(!1,()=>62===this._cursor.peek()),this._beginToken(11),this._cursor.advance(),this._endToken([])}_consumeCdata(e){this._beginToken(12,e),this._endToken([]),this._consumeRawText(!1,()=>this._attemptStr("]]>")),this._beginToken(13),this._requireStr("]]>"),this._endToken([])}_consumeDocType(e){this._beginToken(18,e),this._endToken([]),this._consumeRawText(!1,()=>62===this._cursor.peek()),this._beginToken(19),this._cursor.advance(),this._endToken([])}_consumePrefixAndName(){var e;let t,r=this._cursor.clone(),n="";for(;58!==this._cursor.peek()&&!(((e=this._cursor.peek())<97||122<e)&&(e<65||90<e)&&(e<48||e>57));)this._cursor.advance();return 58===this._cursor.peek()?(n=this._cursor.getChars(r),this._cursor.advance(),t=this._cursor.clone()):t=r,this._requireCharCodeUntilFn(sa,+(""!==n)),[n,this._cursor.getChars(t)]}_consumeTagOpen(e){let t,r,n,i=[];try{if(!an(this._cursor.peek()))throw this._createError(se(this._cursor.peek()),this._cursor.getSpan(e));for(r=(n=this._consumeTagOpenStart(e)).parts[0],t=n.parts[1],this._attemptCharCodeUntilFn(si);47!==this._cursor.peek()&&62!==this._cursor.peek()&&60!==this._cursor.peek()&&0!==this._cursor.peek();){let[e,t]=this._consumeAttributeName();if(this._attemptCharCodeUntilFn(si),this._attemptCharCode(61)){this._attemptCharCodeUntilFn(si);let r=this._consumeAttributeValue();i.push({prefix:e,name:t,value:r})}else i.push({prefix:e,name:t});this._attemptCharCodeUntilFn(si)}this._consumeTagOpenEnd()}catch(t){if(t instanceof sr)return void(n?n.type=4:(this._beginToken(5,e),this._endToken(["<"])));throw t}if(this._canSelfClose&&2===this.tokens[this.tokens.length-1].type)return;let a=this._getTagContentType(t,r,this._fullNameStack.length>0,i);this._handleFullNameStackForTagOpen(r,t),a===aN.RAW_TEXT?this._consumeRawTextWithTagClose(r,t,!1):a===aN.ESCAPABLE_RAW_TEXT&&this._consumeRawTextWithTagClose(r,t,!0)}_consumeRawTextWithTagClose(e,t,r){this._consumeRawText(r,()=>!!this._attemptCharCode(60)&&!!this._attemptCharCode(47)&&(this._attemptCharCodeUntilFn(si),!!this._attemptStrCaseInsensitive(e?`${e}:${t}`:t))&&(this._attemptCharCodeUntilFn(si),this._attemptCharCode(62))),this._beginToken(3),this._requireCharCodeUntilFn(e=>62===e,3),this._cursor.advance(),this._endToken([e,t]),this._handleFullNameStackForTagClose(e,t)}_consumeTagOpenStart(e){this._beginToken(0,e);let t=this._consumePrefixAndName();return this._endToken(t)}_consumeAttributeName(){let e=this._cursor.peek();if(39===e||34===e)throw this._createError(se(e),this._cursor.getSpan());this._beginToken(14);let t=this._consumePrefixAndName();return this._endToken(t),t}_consumeAttributeValue(){let e;if(39===this._cursor.peek()||34===this._cursor.peek()){let t=this._cursor.peek();this._consumeQuote(t);let r=()=>this._cursor.peek()===t;e=this._consumeWithInterpolation(16,17,r,r),this._consumeQuote(t)}else{let t=()=>sa(this._cursor.peek());e=this._consumeWithInterpolation(16,17,t,t)}return e}_consumeQuote(e){this._beginToken(15),this._requireCharCode(e),this._endToken([String.fromCodePoint(e)])}_consumeTagOpenEnd(){let e=this._attemptCharCode(47)?2:1;this._beginToken(e),this._requireCharCode(62),this._endToken([])}_consumeTagClose(e){if(this._beginToken(3,e),this._attemptCharCodeUntilFn(si),this._allowHtmComponentClosingTags&&this._attemptCharCode(47))this._attemptCharCodeUntilFn(si),this._requireCharCode(62),this._endToken([]);else{let[e,t]=this._consumePrefixAndName();this._attemptCharCodeUntilFn(si),this._requireCharCode(62),this._endToken([e,t]),this._handleFullNameStackForTagClose(e,t)}}_consumeExpansionFormStart(){this._beginToken(20),this._requireCharCode(123),this._endToken([]),this._expansionCaseStack.push(20),this._beginToken(7);let e=this._readUntil(44),t=this._processCarriageReturns(e);if(this._i18nNormalizeLineEndingsInICUs)this._endToken([t]);else{let r=this._endToken([e]);t!==e&&this.nonNormalizedIcuExpressions.push(r)}this._requireCharCode(44),this._attemptCharCodeUntilFn(si),this._beginToken(7);let r=this._readUntil(44);this._endToken([r]),this._requireCharCode(44),this._attemptCharCodeUntilFn(si)}_consumeExpansionCaseStart(){this._beginToken(21);let e=this._readUntil(123).trim();this._endToken([e]),this._attemptCharCodeUntilFn(si),this._beginToken(22),this._requireCharCode(123),this._endToken([]),this._attemptCharCodeUntilFn(si),this._expansionCaseStack.push(22)}_consumeExpansionCaseEnd(){this._beginToken(23),this._requireCharCode(125),this._endToken([]),this._attemptCharCodeUntilFn(si),this._expansionCaseStack.pop()}_consumeExpansionFormEnd(){this._beginToken(24),this._requireCharCode(125),this._endToken([]),this._expansionCaseStack.pop()}_consumeWithInterpolation(e,t,r,n){this._beginToken(e);let i=[];for(;!r();){let r=this._cursor.clone();this._interpolationConfig&&this._attemptStr(this._interpolationConfig.start)?(this._endToken([this._processCarriageReturns(i.join(""))],r),i.length=0,this._consumeInterpolation(t,r,n),this._beginToken(e)):38===this._cursor.peek()?(this._endToken([this._processCarriageReturns(i.join(""))]),i.length=0,this._consumeEntity(e),this._beginToken(e)):i.push(this._readChar())}this._inInterpolation=!1;let a=this._processCarriageReturns(i.join(""));return this._endToken([a]),a}_consumeInterpolation(e,t,r){let n=[];this._beginToken(e,t),n.push(this._interpolationConfig.start);let i=this._cursor.clone(),a=null,s=!1;for(;0!==this._cursor.peek()&&(null===r||!r());){let e=this._cursor.clone();if(this._isTagStart()){this._cursor=e,n.push(this._getProcessedChars(i,e)),this._endToken(n);return}if(null===a)if(this._attemptStr(this._interpolationConfig.end)){n.push(this._getProcessedChars(i,e)),n.push(this._interpolationConfig.end),this._endToken(n);return}else this._attemptStr("//")&&(s=!0);let t=this._cursor.peek();this._cursor.advance(),92===t?this._cursor.advance():t===a?a=null:!s&&null===a&&as(t)&&(a=t)}n.push(this._getProcessedChars(i,this._cursor)),this._endToken(n)}_getProcessedChars(e,t){return this._processCarriageReturns(t.getChars(e))}_isTextEnd(){return!!(this._isTagStart()||0===this._cursor.peek()||this._tokenizeIcu&&!this._inInterpolation&&(this.isExpansionFormStart()||125===this._cursor.peek()&&this._isInExpansionCase())||this._tokenizeBlocks&&!this._inInterpolation&&!this._isInExpansion()&&(this._isBlockStart()||64===this._cursor.peek()||125===this._cursor.peek()))}_isTagStart(){if(60===this._cursor.peek()){let e=this._cursor.clone();e.advance();let t=e.peek();if(97<=t&&t<=122||65<=t&&t<=90||47===t||33===t)return!0}return!1}_isBlockStart(){if(this._tokenizeBlocks&&64===this._cursor.peek()){let e=this._cursor.clone();if(e.advance(),su(e.peek()))return!0}return!1}_readUntil(e){let t=this._cursor.clone();return this._attemptUntilChar(e),this._cursor.getChars(t)}_isInExpansion(){return this._isInExpansionCase()||this._isInExpansionForm()}_isInExpansionCase(){return this._expansionCaseStack.length>0&&22===this._expansionCaseStack[this._expansionCaseStack.length-1]}_isInExpansionForm(){return this._expansionCaseStack.length>0&&20===this._expansionCaseStack[this._expansionCaseStack.length-1]}isExpansionFormStart(){if(123!==this._cursor.peek())return!1;if(this._interpolationConfig){let e=this._cursor.clone(),t=this._attemptStr(this._interpolationConfig.start);return this._cursor=e,!t}return!0}_handleFullNameStackForTagOpen(e,t){let r=aE(e,t);(0===this._fullNameStack.length||this._fullNameStack[this._fullNameStack.length-1]===r)&&this._fullNameStack.push(r)}_handleFullNameStackForTagClose(e,t){let r=aE(e,t);0!==this._fullNameStack.length&&this._fullNameStack[this._fullNameStack.length-1]===r&&this._fullNameStack.pop()}};function si(e){return!at(e)||0===e}function sa(e){return at(e)||62===e||60===e||47===e||39===e||34===e||61===e||0===e}function ss(e){return 59===e||0===e||!(e>=97&&e<=102||e>=65&&e<=70||ar(e))}function so(e){return 59===e||0===e||!an(e)}function sl(e){return e>=97&&e<=122?e-97+65:e}function su(e){return an(e)||ar(e)||95===e}function sc(e){return 59!==e&&si(e)}var sh=class e{constructor(t,r){if(t instanceof e){this.file=t.file,this.input=t.input,this.end=t.end;let e=t.state;this.state={peek:e.peek,offset:e.offset,line:e.line,column:e.column}}else{if(!r)throw Error("Programming error: the range argument must be provided with a file argument.");this.file=t,this.input=t.content,this.end=r.endPos,this.state={peek:-1,offset:r.startPos,line:r.startLine,column:r.startCol}}}clone(){return new e(this)}peek(){return this.state.peek}charsLeft(){return this.end-this.state.offset}diff(e){return this.state.offset-e.state.offset}advance(){this.advanceState(this.state)}init(){this.updatePeek(this.state)}getSpan(e,t){let r=e=e||this;if(t)for(;this.diff(e)>0&&-1!==t.indexOf(e.peek());)r===e&&(e=e.clone()),e.advance();let n=this.locationFromCursor(e),i=this.locationFromCursor(this),a=r!==e?this.locationFromCursor(r):n;return new ah(n,i,a)}getChars(e){return this.input.substring(e.state.offset,this.state.offset)}charAt(e){return this.input.charCodeAt(e)}advanceState(e){if(e.offset>=this.end)throw this.state=e,new sp('Unexpected character "EOF"',this);let t=this.charAt(e.offset);10===t?(e.line++,e.column=0):ai(t)||e.column++,e.offset++,this.updatePeek(e)}updatePeek(e){e.peek=e.offset>=this.end?0:this.charAt(e.offset)}locationFromCursor(e){return new au(e.file,e.state.offset,e.state.line,e.state.column)}},sd=class e extends sh{constructor(t,r){t instanceof e?(super(t),this.internalState={...t.internalState}):(super(t,r),this.internalState=this.state)}advance(){this.state=this.internalState,super.advance(),this.processEscapeSequence()}init(){super.init(),this.processEscapeSequence()}clone(){return new e(this)}getChars(e){let t=e.clone(),r="";for(;t.internalState.offset<this.internalState.offset;)r+=String.fromCodePoint(t.peek()),t.advance();return r}processEscapeSequence(){let e=()=>this.internalState.peek;if(92===e())if(this.internalState={...this.state},this.advanceState(this.internalState),110===e())this.state.peek=10;else if(114===e())this.state.peek=13;else if(118===e())this.state.peek=11;else if(116===e())this.state.peek=9;else if(98===e())this.state.peek=8;else if(102===e())this.state.peek=12;else if(117===e())if(this.advanceState(this.internalState),123===e()){this.advanceState(this.internalState);let t=this.clone(),r=0;for(;125!==e();)this.advanceState(this.internalState),r++;this.state.peek=this.decodeHexDigits(t,r)}else{let e=this.clone();this.advanceState(this.internalState),this.advanceState(this.internalState),this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(e,4)}else if(120===e()){this.advanceState(this.internalState);let e=this.clone();this.advanceState(this.internalState),this.state.peek=this.decodeHexDigits(e,2)}else if(aa(e())){let t="",r=0,n=this.clone();for(;aa(e())&&r<3;)n=this.clone(),t+=String.fromCodePoint(e()),this.advanceState(this.internalState),r++;this.state.peek=parseInt(t,8),this.internalState=n.internalState}else ai(this.internalState.peek)?(this.advanceState(this.internalState),this.state=this.internalState):this.state.peek=this.internalState.peek}decodeHexDigits(e,t){let r=parseInt(this.input.slice(e.internalState.offset,e.internalState.offset+t),16);if(isNaN(r))throw e.state=e.internalState,new sp("Invalid hexadecimal escape sequence",e);return r}},sp=class{constructor(e,t){this.msg=e,this.cursor=t}},sf=class e extends ad{static create(t,r,n){return new e(t,r,n)}constructor(e,t,r){super(t,r),this.elementName=e}},sm=class{constructor(e,t){this.rootNodes=e,this.errors=t}},sg=class{constructor(e){this.getTagDefinition=e}parse(e,t,r,n=!1,i){let a=e=>(t,...r)=>e(t.toLowerCase(),...r),s=n?this.getTagDefinition:a(this.getTagDefinition),o=e=>s(e).getContentType(),l=n?i:a(i),u=function(e,t,r,n={}){let i=new sn(new ac(e,t),r,n);return i.tokenize(),new a9(function(e){let t=[],r;for(let n=0;n<e.length;n++){let i=e[n];r&&5===r.type&&5===i.type||r&&16===r.type&&16===i.type?(r.parts[0]+=i.parts[0],r.sourceSpan.end=i.sourceSpan.end):(r=i,t.push(r))}return t}(i.tokens),i.errors,i.nonNormalizedIcuExpressions)}(e,t,i?(e,t,r,n)=>{let i=l(e,t,r,n);return void 0!==i?i:o(e)}:o,r),c=r&&r.canSelfClose||!1,h=r&&r.allowHtmComponentClosingTags||!1,d=new sD(u.tokens,s,c,h,n);return d.build(),new sm(d.rootNodes,u.errors.concat(d.errors))}},sD=class e{constructor(e,t,r,n,i){this.tokens=e,this.getTagDefinition=t,this.canSelfClose=r,this.allowHtmComponentClosingTags=n,this.isTagNameCaseSensitive=i,this._index=-1,this._containerStack=[],this.rootNodes=[],this.errors=[],this._advance()}build(){for(;34!==this._peek.type;)0===this._peek.type||4===this._peek.type?this._consumeStartTag(this._advance()):3===this._peek.type?(this._closeVoidElement(),this._consumeEndTag(this._advance())):12===this._peek.type?(this._closeVoidElement(),this._consumeCdata(this._advance())):10===this._peek.type?(this._closeVoidElement(),this._consumeComment(this._advance())):5===this._peek.type||7===this._peek.type||6===this._peek.type?(this._closeVoidElement(),this._consumeText(this._advance())):20===this._peek.type?this._consumeExpansion(this._advance()):25===this._peek.type?(this._closeVoidElement(),this._consumeBlockOpen(this._advance())):27===this._peek.type?(this._closeVoidElement(),this._consumeBlockClose(this._advance())):29===this._peek.type?(this._closeVoidElement(),this._consumeIncompleteBlock(this._advance())):30===this._peek.type?(this._closeVoidElement(),this._consumeLet(this._advance())):18===this._peek.type?this._consumeDocType(this._advance()):33===this._peek.type?(this._closeVoidElement(),this._consumeIncompleteLet(this._advance())):this._advance();for(let e of this._containerStack)e instanceof aY&&this.errors.push(sf.create(e.name,e.sourceSpan,`Unclosed block "${e.name}"`))}_advance(){let e=this._peek;return this._index<this.tokens.length-1&&this._index++,this._peek=this.tokens[this._index],e}_advanceIf(e){return this._peek.type===e?this._advance():null}_consumeCdata(e){let t=this._advance(),r=this._getText(t),n=this._advanceIf(13);this._addToParent(new az(r,new ah(e.sourceSpan.start,(n||t).sourceSpan.end),[t]))}_consumeComment(e){let t=this._advanceIf(7),r=this._advanceIf(11),n=null!=t?t.parts[0].trim():null,i=null==r?e.sourceSpan:new ah(e.sourceSpan.start,r.sourceSpan.end,e.sourceSpan.fullStart);this._addToParent(new aZ(n,i))}_consumeDocType(e){let t=this._advanceIf(7),r=this._advanceIf(19),n=null!=t?t.parts[0].trim():null,i=new ah(e.sourceSpan.start,(r||t||e).sourceSpan.end);this._addToParent(new aX(n,i))}_consumeExpansion(e){let t=this._advance(),r=this._advance(),n=[];for(;21===this._peek.type;){let e=this._parseExpansionCase();if(!e)return;n.push(e)}if(24!==this._peek.type)return void this.errors.push(sf.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '}'."));let i=new ah(e.sourceSpan.start,this._peek.sourceSpan.end,e.sourceSpan.fullStart);this._addToParent(new aG(t.parts[0],r.parts[0],n,i,t.sourceSpan)),this._advance()}_parseExpansionCase(){let t=this._advance();if(22!==this._peek.type)return this.errors.push(sf.create(null,this._peek.sourceSpan,"Invalid ICU message. Missing '{'.")),null;let r=this._advance(),n=this._collectExpansionExpTokens(r);if(!n)return null;let i=this._advance();n.push({type:34,parts:[],sourceSpan:i.sourceSpan});let a=new e(n,this.getTagDefinition,this.canSelfClose,this.allowHtmComponentClosingTags,this.isTagNameCaseSensitive);if(a.build(),a.errors.length>0)return this.errors=this.errors.concat(a.errors),null;let s=new ah(t.sourceSpan.start,i.sourceSpan.end,t.sourceSpan.fullStart),o=new ah(r.sourceSpan.start,i.sourceSpan.end,r.sourceSpan.fullStart);return new aJ(t.parts[0],a.rootNodes,s,t.sourceSpan,o)}_collectExpansionExpTokens(e){let t=[],r=[22];for(;;){if((20===this._peek.type||22===this._peek.type)&&r.push(this._peek.type),23===this._peek.type){if(!sy(r,22))return this.errors.push(sf.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;else if(r.pop(),0===r.length)return t}if(24===this._peek.type)if(!sy(r,20))return this.errors.push(sf.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;else r.pop();if(34===this._peek.type)return this.errors.push(sf.create(null,e.sourceSpan,"Invalid ICU message. Missing '}'.")),null;t.push(this._advance())}}_getText(e){let t=e.parts[0];if(t.length>0&&t[0]==`
`){let e=this._getClosestParentElement();null!=e&&0==e.children.length&&this.getTagDefinition(e.name).ignoreFirstLf&&(t=t.substring(1))}return t}_consumeText(e){let t=[e],r=e.sourceSpan,n=e.parts[0];if(n.length>0&&n[0]===`
`){let r=this._getContainer();null!=r&&0===r.children.length&&this.getTagDefinition(r.name).ignoreFirstLf&&(n=n.substring(1),t[0]={type:e.type,sourceSpan:e.sourceSpan,parts:[n]})}for(;8===this._peek.type||5===this._peek.type||9===this._peek.type;)e=this._advance(),t.push(e),8===e.type?n+=e.parts.join("").replace(/&([^;]+);/g,sb):9===e.type?n+=e.parts[0]:n+=e.parts.join("");if(n.length>0){let i=e.sourceSpan;this._addToParent(new aU(n,new ah(r.start,i.end,r.fullStart,r.details),t))}}_closeVoidElement(){let e=this._getContainer();e instanceof aQ&&this.getTagDefinition(e.name).isVoid&&this._containerStack.pop()}_consumeStartTag(e){let[t,r]=e.parts,n=[];for(;14===this._peek.type;)n.push(this._consumeAttr(this._advance()));let i=this._getElementFullName(t,r,this._getClosestParentElement()),a=!1;if(2===this._peek.type){this._advance(),a=!0;let t=this.getTagDefinition(i);this.canSelfClose||t.canSelfClose||null!==ak(i)||t.isVoid||this.errors.push(sf.create(i,e.sourceSpan,`Only void, custom and foreign elements can be self closed "${e.parts[1]}"`))}else 1===this._peek.type&&(this._advance(),a=!1);let s=this._peek.sourceSpan.fullStart,o=new ah(e.sourceSpan.start,s,e.sourceSpan.fullStart),l=new aQ(i,n,[],o,new ah(e.sourceSpan.start,s,e.sourceSpan.fullStart),void 0,new ah(e.sourceSpan.start.moveBy(1),e.sourceSpan.end)),u=this._getContainer();this._pushContainer(l,u instanceof aQ&&this.getTagDefinition(u.name).isClosedByChild(l.name)),a?this._popContainer(i,aQ,o):4===e.type&&(this._popContainer(i,aQ,null),this.errors.push(sf.create(i,o,`Opening tag "${i}" not terminated.`)))}_pushContainer(e,t){t&&this._containerStack.pop(),this._addToParent(e),this._containerStack.push(e)}_consumeEndTag(e){let t=this.allowHtmComponentClosingTags&&0===e.parts.length?null:this._getElementFullName(e.parts[0],e.parts[1],this._getClosestParentElement());if(t&&this.getTagDefinition(t).isVoid)this.errors.push(sf.create(t,e.sourceSpan,`Void elements do not have end tags "${e.parts[1]}"`));else if(!this._popContainer(t,aQ,e.sourceSpan)){let r=`Unexpected closing tag "${t}". It may happen when the tag has already been closed by another tag. For more info see https://www.w3.org/TR/html5/syntax.html#closing-elements-that-have-implied-end-tags`;this.errors.push(sf.create(t,e.sourceSpan,r))}}_popContainer(e,t,r){let n=!1;for(let i=this._containerStack.length-1;i>=0;i--){let a=this._containerStack[i];if(ak(a.name)?a.name===e:(null==e||a.name.toLowerCase()===e.toLowerCase())&&a instanceof t)return a.endSourceSpan=r,a.sourceSpan.end=null!==r?r.end:a.sourceSpan.end,this._containerStack.splice(i,this._containerStack.length-i),!n;(a instanceof aY||a instanceof aQ&&!this.getTagDefinition(a.name).closedByParent)&&(n=!0)}return!1}_consumeAttr(e){let t=aE(e.parts[0],e.parts[1]),r=e.sourceSpan.end,n;15===this._peek.type&&(n=this._advance());let i="",a=[],s,o;if(16===this._peek.type)for(s=this._peek.sourceSpan,o=this._peek.sourceSpan.end;16===this._peek.type||17===this._peek.type||9===this._peek.type;){let e=this._advance();a.push(e),17===e.type?i+=e.parts.join("").replace(/&([^;]+);/g,sb):9===e.type?i+=e.parts[0]:i+=e.parts.join(""),o=r=e.sourceSpan.end}15===this._peek.type&&(o=r=this._advance().sourceSpan.end);let l=s&&o&&new ah((null==n?void 0:n.sourceSpan.start)??s.start,o,(null==n?void 0:n.sourceSpan.fullStart)??s.fullStart);return new aK(t,i,new ah(e.sourceSpan.start,r,e.sourceSpan.fullStart),e.sourceSpan,l,a.length>0?a:void 0,void 0)}_consumeBlockOpen(e){let t=[];for(;28===this._peek.type;){let e=this._advance();t.push(new a0(e.parts[0],e.sourceSpan))}26===this._peek.type&&this._advance();let r=this._peek.sourceSpan.fullStart,n=new ah(e.sourceSpan.start,r,e.sourceSpan.fullStart),i=new ah(e.sourceSpan.start,r,e.sourceSpan.fullStart),a=new aY(e.parts[0],t,[],n,e.sourceSpan,i);this._pushContainer(a,!1)}_consumeBlockClose(e){this._popContainer(null,aY,e.sourceSpan)||this.errors.push(sf.create(null,e.sourceSpan,'Unexpected closing block. The block may have been closed earlier. If you meant to write the } character, you should use the "&#125;" HTML entity instead.'))}_consumeIncompleteBlock(e){let t=[];for(;28===this._peek.type;){let e=this._advance();t.push(new a0(e.parts[0],e.sourceSpan))}let r=this._peek.sourceSpan.fullStart,n=new ah(e.sourceSpan.start,r,e.sourceSpan.fullStart),i=new ah(e.sourceSpan.start,r,e.sourceSpan.fullStart),a=new aY(e.parts[0],t,[],n,e.sourceSpan,i);this._pushContainer(a,!1),this._popContainer(null,aY,null),this.errors.push(sf.create(e.parts[0],n,`Incomplete block "${e.parts[0]}". If you meant to write the @ character, you should use the "&#64;" HTML entity instead.`))}_consumeLet(e){let t=e.parts[0],r;if(31!==this._peek.type)return void this.errors.push(sf.create(e.parts[0],e.sourceSpan,`Invalid @let declaration "${t}". Declaration must have a value.`));if(r=this._advance(),32!==this._peek.type)return void this.errors.push(sf.create(e.parts[0],e.sourceSpan,`Unterminated @let declaration "${t}". Declaration must be terminated with a semicolon.`));let n=this._advance().sourceSpan.fullStart,i=new ah(e.sourceSpan.start,n,e.sourceSpan.fullStart),a=e.sourceSpan.toString().lastIndexOf(t),s=new ah(e.sourceSpan.start.moveBy(a),e.sourceSpan.end),o=new a1(t,r.parts[0],i,s,r.sourceSpan);this._addToParent(o)}_consumeIncompleteLet(e){let t=e.parts[0]??"",r=t?` "${t}"`:"";if(t.length>0){let r=e.sourceSpan.toString().lastIndexOf(t),n=new ah(e.sourceSpan.start.moveBy(r),e.sourceSpan.end),i=new ah(e.sourceSpan.start,e.sourceSpan.start.moveBy(0)),a=new a1(t,"",e.sourceSpan,n,i);this._addToParent(a)}this.errors.push(sf.create(e.parts[0],e.sourceSpan,`Incomplete @let declaration${r}. @let declarations must be written as \`@let <name> = <value>;\``))}_getContainer(){return this._containerStack.length>0?this._containerStack[this._containerStack.length-1]:null}_getClosestParentElement(){for(let e=this._containerStack.length-1;e>-1;e--)if(this._containerStack[e]instanceof aQ)return this._containerStack[e];return null}_addToParent(e){let t=this._getContainer();null===t?this.rootNodes.push(e):t.children.push(e)}_getElementFullName(e,t,r){if(""===e&&""===(e=this.getTagDefinition(t).implicitNamespacePrefix||"")&&null!=r){let t=av(r.name)[1];this.getTagDefinition(t).preventNamespaceInheritance||(e=ak(r.name))}return aE(e,t)}};function sy(e,t){return e.length>0&&e[e.length-1]===t}function sb(e,t){return void 0!==a8[t]?a8[t]||e:/^#x[a-f0-9]+$/i.test(t)?String.fromCodePoint(parseInt(t.slice(2),16)):/^#\d+$/.test(t)?String.fromCodePoint(parseInt(t.slice(1),10)):e}var sC=class extends sg{constructor(){super(aH)}parse(e,t,r,n=!1,i){return super.parse(e,t,r,n,i)}},sv=null,sS=()=>(sv||(sv=new sC),sv);function sw(e,t={}){let{canSelfClose:r=!1,allowHtmComponentClosingTags:n=!1,isTagNameCaseSensitive:i=!1,getTagContentType:a,tokenizeAngularBlocks:s=!1,tokenizeAngularLetDeclaration:o=!1}=t;return sS().parse(e,"angular-html-parser",{tokenizeExpansionForms:s,interpolationConfig:void 0,canSelfClose:r,allowHtmComponentClosingTags:n,tokenizeBlocks:s,tokenizeLet:o},i,a)}var sk,sE,sx,sF,s_=function(e){let t=function(e){let t=e.slice(0,3);if("---"!==t&&"+++"!==t)return;let r=e.indexOf(`
`,3);if(-1===r)return;let n=e.slice(3,r).trim(),i=e.indexOf(`
${t}`,r),a=n;if(a||(a="+++"===t?"toml":"yaml"),-1===i&&"---"===t&&"yaml"===a&&(i=e.indexOf(`
...`,r)),-1===i)return;let s=i+1+3,o=e.charAt(s+1);if(!/\s?/u.test(o))return;let l=e.slice(0,s);return{type:"front-matter",language:a,explicitLanguage:n,value:e.slice(r+1,i),startDelimiter:t,endDelimiter:l.slice(-3),raw:l}}(e);if(!t)return{content:e};let{raw:r}=t;return{frontMatter:t,content:rS(!1,r,/[^\n]/gu," ")+e.slice(r.length)}},sT={attrs:!0,children:!0,cases:!0,expression:!0},sA=new Set(["parent"]),sL=class e{constructor(e={}){for(let t of(rb(this,sE),rg(this,"type"),rg(this,"parent"),new Set([...sA,...Object.keys(e)])))this.setProperty(t,e[t])}setProperty(e,t){if(this[e]!==t){if(e in sT&&(t=t.map(e=>this.createChild(e))),!sA.has(e)){this[e]=t;return}Object.defineProperty(this,e,{value:t,enumerable:!1,configurable:!0})}}map(t){let r;for(let n in sT){let i=this[n];if(i){let a=function(e,t){let r=e.map(t);return r.some((t,r)=>t!==e[r])?r:e}(i,e=>e.map(t));r!==i&&(r||(r=new e({parent:this.parent})),r.setProperty(n,a))}}if(r)for(let e in this)e in sT||(r[e]=this[e]);return t(r||this)}walk(e){for(let t in sT){let r=this[t];if(r)for(let t=0;t<r.length;t++)r[t].walk(e)}e(this)}createChild(t){let r=t instanceof e?t.clone():new e(t);return r.setProperty("parent",this),r}insertChildBefore(e,t){let r=this.$children;r.splice(r.indexOf(e),0,this.createChild(t))}removeChild(e){let t=this.$children;t.splice(t.indexOf(e),1)}replaceChild(e,t){let r=this.$children;r[r.indexOf(e)]=this.createChild(t)}clone(){return new e(this)}get $children(){return this[ry(this,sE,sx)]}set $children(e){this[ry(this,sE,sx)]=e}get firstChild(){var e;return null==(e=this.$children)?void 0:e[0]}get lastChild(){return rM(!0,this.$children,-1)}get prev(){let e=ry(this,sE,sF);return e[e.indexOf(this)-1]}get next(){let e=ry(this,sE,sF);return e[e.indexOf(this)+1]}get rawName(){return this.hasExplicitNamespace?this.fullName:this.name}get fullName(){return this.namespace?this.namespace+":"+this.name:this.name}get attrMap(){return Object.fromEntries(this.attrs.map(e=>[e.fullName,e.value]))}};sE=new WeakSet,sx=function(){return"angularIcuCase"===this.type?"expression":"angularIcuExpression"===this.type?"cases":"children"},sF=function(){var e;return(null==(e=this.parent)?void 0:e.$children)??[]};var sB=[{regex:/^(\[if([^\]]*)\]>)(.*?)<!\s*\[endif\]$/su,parse:function(e,t,r){let[,n,i,a]=r,s=4+n.length,o=e.sourceSpan.start.moveBy(s),l=o.moveBy(a.length),[u,c]=(()=>{try{return[!0,t(a,o).children]}catch{return[!1,[{type:"text",value:a,sourceSpan:new ah(o,l)}]]}})();return{type:"ieConditionalComment",complete:u,children:c,condition:rS(!1,i.trim(),/\s+/gu," "),sourceSpan:e.sourceSpan,startSourceSpan:new ah(e.sourceSpan.start,o),endSourceSpan:new ah(l,e.sourceSpan.end)}}},{regex:/^\[if([^\]]*)\]><!$/u,parse:function(e,t,r){let[,n]=r;return{type:"ieConditionalStartComment",condition:rS(!1,n.trim(),/\s+/gu," "),sourceSpan:e.sourceSpan}}},{regex:/^<!\s*\[endif\]$/u,parse:function(e){return{type:"ieConditionalEndComment",sourceSpan:e.sourceSpan}}}],sN=new Map([["*",new Set(["accesskey","autocapitalize","autofocus","class","contenteditable","dir","draggable","enterkeyhint","hidden","id","inert","inputmode","is","itemid","itemprop","itemref","itemscope","itemtype","lang","nonce","popover","slot","spellcheck","style","tabindex","title","translate","writingsuggestions"])],["a",new Set(["charset","coords","download","href","hreflang","name","ping","referrerpolicy","rel","rev","shape","target","type"])],["applet",new Set(["align","alt","archive","code","codebase","height","hspace","name","object","vspace","width"])],["area",new Set(["alt","coords","download","href","hreflang","nohref","ping","referrerpolicy","rel","shape","target","type"])],["audio",new Set(["autoplay","controls","crossorigin","loop","muted","preload","src"])],["base",new Set(["href","target"])],["basefont",new Set(["color","face","size"])],["blockquote",new Set(["cite"])],["body",new Set(["alink","background","bgcolor","link","text","vlink"])],["br",new Set(["clear"])],["button",new Set(["disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","name","popovertarget","popovertargetaction","type","value"])],["canvas",new Set(["height","width"])],["caption",new Set(["align"])],["col",new Set(["align","char","charoff","span","valign","width"])],["colgroup",new Set(["align","char","charoff","span","valign","width"])],["data",new Set(["value"])],["del",new Set(["cite","datetime"])],["details",new Set(["name","open"])],["dialog",new Set(["open"])],["dir",new Set(["compact"])],["div",new Set(["align"])],["dl",new Set(["compact"])],["embed",new Set(["height","src","type","width"])],["fieldset",new Set(["disabled","form","name"])],["font",new Set(["color","face","size"])],["form",new Set(["accept","accept-charset","action","autocomplete","enctype","method","name","novalidate","target"])],["frame",new Set(["frameborder","longdesc","marginheight","marginwidth","name","noresize","scrolling","src"])],["frameset",new Set(["cols","rows"])],["h1",new Set(["align"])],["h2",new Set(["align"])],["h3",new Set(["align"])],["h4",new Set(["align"])],["h5",new Set(["align"])],["h6",new Set(["align"])],["head",new Set(["profile"])],["hr",new Set(["align","noshade","size","width"])],["html",new Set(["manifest","version"])],["iframe",new Set(["align","allow","allowfullscreen","allowpaymentrequest","allowusermedia","frameborder","height","loading","longdesc","marginheight","marginwidth","name","referrerpolicy","sandbox","scrolling","src","srcdoc","width"])],["img",new Set(["align","alt","border","crossorigin","decoding","fetchpriority","height","hspace","ismap","loading","longdesc","name","referrerpolicy","sizes","src","srcset","usemap","vspace","width"])],["input",new Set(["accept","align","alt","autocomplete","checked","dirname","disabled","form","formaction","formenctype","formmethod","formnovalidate","formtarget","height","ismap","list","max","maxlength","min","minlength","multiple","name","pattern","placeholder","popovertarget","popovertargetaction","readonly","required","size","src","step","type","usemap","value","width"])],["ins",new Set(["cite","datetime"])],["isindex",new Set(["prompt"])],["label",new Set(["for","form"])],["legend",new Set(["align"])],["li",new Set(["type","value"])],["link",new Set(["as","blocking","charset","color","crossorigin","disabled","fetchpriority","href","hreflang","imagesizes","imagesrcset","integrity","media","referrerpolicy","rel","rev","sizes","target","type"])],["map",new Set(["name"])],["menu",new Set(["compact"])],["meta",new Set(["charset","content","http-equiv","media","name","scheme"])],["meter",new Set(["high","low","max","min","optimum","value"])],["object",new Set(["align","archive","border","classid","codebase","codetype","data","declare","form","height","hspace","name","standby","type","typemustmatch","usemap","vspace","width"])],["ol",new Set(["compact","reversed","start","type"])],["optgroup",new Set(["disabled","label"])],["option",new Set(["disabled","label","selected","value"])],["output",new Set(["for","form","name"])],["p",new Set(["align"])],["param",new Set(["name","type","value","valuetype"])],["pre",new Set(["width"])],["progress",new Set(["max","value"])],["q",new Set(["cite"])],["script",new Set(["async","blocking","charset","crossorigin","defer","fetchpriority","integrity","language","nomodule","referrerpolicy","src","type"])],["select",new Set(["autocomplete","disabled","form","multiple","name","required","size"])],["slot",new Set(["name"])],["source",new Set(["height","media","sizes","src","srcset","type","width"])],["style",new Set(["blocking","media","type"])],["table",new Set(["align","bgcolor","border","cellpadding","cellspacing","frame","rules","summary","width"])],["tbody",new Set(["align","char","charoff","valign"])],["td",new Set(["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"])],["template",new Set(["shadowrootclonable","shadowrootdelegatesfocus","shadowrootmode"])],["textarea",new Set(["autocomplete","cols","dirname","disabled","form","maxlength","minlength","name","placeholder","readonly","required","rows","wrap"])],["tfoot",new Set(["align","char","charoff","valign"])],["th",new Set(["abbr","align","axis","bgcolor","char","charoff","colspan","headers","height","nowrap","rowspan","scope","valign","width"])],["thead",new Set(["align","char","charoff","valign"])],["time",new Set(["datetime"])],["tr",new Set(["align","bgcolor","char","charoff","valign"])],["track",new Set(["default","kind","label","src","srclang"])],["ul",new Set(["compact","type"])],["video",new Set(["autoplay","controls","crossorigin","height","loop","muted","playsinline","poster","preload","src","width"])]]),sI=new Set(["a","abbr","acronym","address","applet","area","article","aside","audio","b","base","basefont","bdi","bdo","bgsound","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","command","content","data","datalist","dd","del","details","dfn","dialog","dir","div","dl","dt","em","embed","fieldset","figcaption","figure","font","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","iframe","image","img","input","ins","isindex","kbd","keygen","label","legend","li","link","listing","main","map","mark","marquee","math","menu","menuitem","meta","meter","multicol","nav","nextid","nobr","noembed","noframes","noscript","object","ol","optgroup","option","output","p","param","picture","plaintext","pre","progress","q","rb","rbc","rp","rt","rtc","ruby","s","samp","script","search","section","select","shadow","slot","small","source","spacer","span","strike","strong","style","sub","summary","sup","svg","table","tbody","td","template","textarea","tfoot","th","thead","time","title","tr","track","tt","u","ul","var","video","wbr","xmp"]);function sq(e){var t;let{msg:r,span:{start:n,end:i}}=e;throw Object.assign(SyntaxError(r+" ("+(t={loc:{start:{line:n.line+1,column:n.col+1},end:{line:i.line+1,column:i.col+1}},cause:e}).loc.start.line+":"+t.loc.start.column+")"),t)}function sP(e){return{parse:(t,r)=>(function e(t,r,n={},i=!0){let{frontMatter:a,content:s}=i?s_(t):{frontMatter:null,content:t},o=new ac(t,n.filepath),l=new au(o,0,0,0),u=l.moveBy(t.length),c={type:"root",sourceSpan:new ah(l,u),children:function e(t,r,n){let{name:i,canSelfClose:a=!0,normalizeTagName:s=!1,normalizeAttributeName:o=!1,allowHtmComponentClosingTags:l=!1,isTagNameCaseSensitive:u=!1,shouldParseAsRawText:c}=r,{rootNodes:h,errors:d}=sw(t,{canSelfClose:a,allowHtmComponentClosingTags:l,isTagNameCaseSensitive:u,getTagContentType:c?(...e)=>c(...e)?aN.RAW_TEXT:void 0:void 0,tokenizeAngularBlocks:"angular"===i||void 0,tokenizeAngularLetDeclaration:"angular"===i||void 0});if("vue"===i){if(h.some(e=>"docType"===e.type&&"html"===e.value||"element"===e.type&&"html"===e.name.toLowerCase()))return e(t,sO,n);let r,i=()=>r??(r=sw(t,{canSelfClose:a,allowHtmComponentClosingTags:l,isTagNameCaseSensitive:u})),s=e=>i().rootNodes.find(({startSourceSpan:t})=>t&&t.start.offset===e.startSourceSpan.start.offset)??e;for(let[e,t]of h.entries()){let{endSourceSpan:r,startSourceSpan:a}=t;if(null===r)d=i().errors,h[e]=s(t);else if(function(e,t){var r;if("element"!==e.type||"template"!==e.name)return!1;let n=null==(r=e.attrs.find(e=>"lang"===e.name))?void 0:r.value;return!n||"html"===nB(t,{language:n})}(t,n)){let n=i().errors.find(e=>e.span.start.offset>a.start.offset&&e.span.start.offset<r.end.offset);n&&sq(n),h[e]=s(t)}}}d.length>0&&sq(d[0]);let p=e=>{let t=e.name.startsWith(":")?e.name.slice(1).split(":")[0]:null,r=e.nameSpan.toString(),n=null!==t&&r.startsWith(`${t}:`);e.name=n?r.slice(t.length+1):r,e.namespace=t,e.hasExplicitNamespace=n},f=e=>{switch(e.type){case"element":for(let t of(p(e),e.attrs))p(t),t.valueSpan?(t.value=t.valueSpan.toString(),/["']/u.test(t.value[0])&&(t.value=t.value.slice(1,-1))):t.value=null;break;case"comment":e.value=e.sourceSpan.toString().slice(4,-3);break;case"text":e.value=e.sourceSpan.toString()}},m=(e,t)=>{let r=e.toLowerCase();return t(r)?r:e},g=e=>{if("element"===e.type&&(s&&(!e.namespace||e.namespace===e.tagDefinition.implicitNamespacePrefix||nq(e))&&(e.name=m(e.name,e=>sI.has(e))),o))for(let t of e.attrs)t.namespace||(t.name=m(t.name,t=>sN.has(e.name)&&(sN.get("*").has(t)||sN.get(e.name).has(t))))},D=e=>{e.sourceSpan&&e.endSourceSpan&&(e.sourceSpan=new ah(e.sourceSpan.start,e.endSourceSpan.end))},y=e=>{if("element"===e.type){let t=aH(u?e.name:e.name.toLowerCase());!e.namespace||e.namespace===t.implicitNamespacePrefix||nq(e)?e.tagDefinition=t:e.tagDefinition=aH("")}};return a2(new class extends a3{visitExpansionCase(e,t){"angular"===i&&this.visitChildren(t,t=>{t(e.expression)})}visit(e){f(e),y(e),g(e),D(e)}},h),h}(s,r,n)};if(a){let e=new au(o,0,0,0),t=e.moveBy(a.raw.length);a.sourceSpan=new ah(e,t),c.children.unshift(a)}let h=new sL(c),d=(i,a)=>{let{offset:s}=a,o=e(rS(!1,t.slice(0,s),/[^\n\r]/gu," ")+i,r,n,!1);o.sourceSpan=new ah(a,rM(!1,o.children,-1).sourceSpan.end);let l=o.children[0];return l.length===s?o.children.shift():(l.sourceSpan=new ah(l.sourceSpan.start.moveBy(s),l.sourceSpan.end),l.value=l.value.slice(s)),o};return h.walk(e=>{if("comment"===e.type){let t=function(e,t){if(e.value)for(let{regex:r,parse:n}of sB){let i=e.value.match(r);if(i)return n(e,t,i)}return null}(e,d);t&&e.parent.replaceChild(e,t)}(function(e){if("block"===e.type){if(e.name=rS(!1,e.name.toLowerCase(),/\s+/gu," ").trim(),e.type="angularControlFlowBlock",!nE(e.parameters))return delete e.parameters;for(let t of e.parameters)t.type="angularControlFlowBlockParameter";e.parameters={type:"angularControlFlowBlockParameters",children:e.parameters,sourceSpan:new ah(e.parameters[0].sourceSpan.start,rM(!1,e.parameters,-1).sourceSpan.end)}}})(e),"letDeclaration"===e.type&&(e.type="angularLetDeclaration",e.id=e.name,e.init={type:"angularLetDeclarationInitializer",sourceSpan:new ah(e.valueSpan.start,e.valueSpan.end),value:e.value},delete e.name,delete e.value),("plural"===e.type||"select"===e.type)&&(e.clause=e.type,e.type="angularIcuExpression"),"expansionCase"===e.type&&(e.type="angularIcuCase")}),h})(t,e,r),hasPragma:i8,hasIgnorePragma:i6,astFormat:"html",locStart:iB,locEnd:iN}}var sO={name:"html",normalizeTagName:!0,normalizeAttributeName:!0,allowHtmComponentClosingTags:!0},sR=sP(sO),sM=new Set(["mj-style","mj-raw"]),sW=sP({...sO,name:"mjml",shouldParseAsRawText:e=>sM.has(e)}),s$=sP({name:"angular"}),sj=sP({name:"vue",isTagNameCaseSensitive:!0,shouldParseAsRawText:(e,t,r,n)=>"html"!==e.toLowerCase()&&!r&&("template"!==e||n.some(({name:e,value:t})=>"lang"===e&&"html"!==t&&""!==t&&void 0!==t))}),sH=sP({name:"lwc",canSelfClose:!1}),sV={html:{preprocess:function(e,t){for(let r of ap)r(e,t);return e},print:function(e,t,r){let{node:n}=e;switch(n.type){case"front-matter":return rV(n.raw);case"root":return t.__onHtmlRoot&&t.__onHtmlRoot(n),[rJ(i4(e,t,r)),r0];case"element":case"ieConditionalComment":return function(e,t,r){var n,i;let a,{node:s}=e;if(nM(s,t))return[iG(s,t),rJ(iU(e,t,r)),rV(iZ(s,t)),...iI(s,t),iP(s,t)];let o=1===s.children.length&&("interpolation"===s.firstChild.type||"angularIcuExpression"===s.firstChild.type)&&s.firstChild.isLeadingSpaceSensitive&&!s.firstChild.hasLeadingSpaces&&s.lastChild.isTrailingSpaceSensitive&&!s.lastChild.hasTrailingSpaces,l=Symbol("element-attr-group-id");return a=0===s.children.length?s.hasDanglingSpaces&&s.isDanglingSpaceSensitive?rX:"":[nU(s)||"element"===s.type&&s.children.length>0&&(["body","script","style"].includes(s.name)||s.children.some(e=>{var t;return null==(t=e.children)?void 0:t.some(e=>"text"!==e.type)}))||s.firstChild&&s.firstChild===s.lastChild&&"text"!==s.firstChild.type&&nG(s.firstChild)&&(!s.lastChild.isTrailingSpaceSensitive||nJ(s.lastChild))?rZ:"",(n=[o?rQ(rY,"",{groupId:l}):s.firstChild.hasLeadingSpaces&&s.firstChild.isLeadingSpaceSensitive?rX:"text"===s.firstChild.type&&s.isWhitespaceSensitive&&s.isIndentationSensitive?rG(Number.NEGATIVE_INFINITY,rY):rY,i4(e,t,r)],o?(i={groupId:l},rU(n),{type:rB,contents:n,groupId:i.groupId,negate:i.negate}):(nj(s,t)||n5(s,t))&&"root"===s.parent.type&&"vue"===t.parser&&!t.vueIndentScriptAndStyle?n:rz(n)),(s.next?iW(s.next):i$(s.parent))?s.lastChild.hasTrailingSpaces&&s.lastChild.isTrailingSpaceSensitive?" ":"":o?rQ(rY,"",{groupId:l}):s.lastChild.hasTrailingSpaces&&s.lastChild.isTrailingSpaceSensitive?rX:("comment"===s.lastChild.type||"text"===s.lastChild.type&&s.isWhitespaceSensitive&&s.isIndentationSensitive)&&RegExp(`\\n[\\t ]{${t.tabWidth*(e.ancestors.length-1)}}$`,"u").test(s.lastChild.value)?"":rY],rJ([rJ(iU(e,t,r),{id:l}),a,iI(s,t)])}(e,t,r);case"angularControlFlowBlock":return function(e,t,r){let{node:n}=e,i=[];(function(e){let{previous:t}=e;return(null==t?void 0:t.type)==="angularControlFlowBlock"&&!nW(t)&&!ae(t)})(e)&&i.push("} "),i.push("@",n.name),n.parameters&&i.push(" (",rJ(r("parameters")),")"),i.push(" {");let a=ae(n);return n.children.length>0?(n.firstChild.hasLeadingSpaces=!0,n.lastChild.hasTrailingSpaces=!0,i.push(rz([r0,i4(e,t,r)])),a&&i.push(r0,"}")):a&&i.push("}"),rJ(i,{shouldBreak:!0})}(e,t,r);case"angularControlFlowBlockParameters":return[rz([rY,r2([";",rX],e.map(r,"children"))]),rY];case"angularControlFlowBlockParameter":return nl.trim(n.expression);case"angularLetDeclaration":return rJ(["@let ",rJ([n.id," =",rJ(rz([rX,r("init")]))]),";"]);case"angularLetDeclarationInitializer":return n.value;case"angularIcuExpression":return function(e,t,r){let{node:n}=e;return[iz(n,t),rJ([n.switchValue.trim(),", ",n.clause,n.cases.length>0?[",",rz([rX,r2(rX,e.map(r,"cases"))])]:"",rY]),iq(n,t)]}(e,t,r);case"angularIcuCase":return function(e,t,r){let{node:n}=e;return[n.value," {",rJ([rz([rY,e.map(({node:e,isLast:t})=>{let n=[r()];return"text"===e.type&&(e.hasLeadingSpaces&&n.unshift(rX),e.hasTrailingSpaces&&!t&&n.push(rX)),n},"expression")]),rY]),"}"]}(e,0,r);case"ieConditionalStartComment":case"ieConditionalEndComment":return[iz(n),iq(n)];case"interpolation":return[iz(n,t),...e.map(r,"children"),iq(n,t)];case"text":{if("interpolation"===n.parent.type){let e=/\n[^\S\n]*$/u,t=e.test(n.value);return[rV(t?n.value.replace(e,""):n.value),t?r0:""]}let e=iG(n,t),r=n4(n),i=iP(n,t);return r[0]=[e,r[0]],r.push([r.pop(),i]),rK(r)}case"docType":return[rJ([iz(n,t)," ",rS(!1,n.value.replace(/^html\b/iu,"html"),/\s+/gu," ")]),iq(n,t)];case"comment":return[iG(n,t),rV(t.originalText.slice(iB(n),iN(n))),iP(n,t)];case"attribute":{if(null===n.value)return n.rawName;let e=n3(n.value),t=ns(e,'"');return[n.rawName,"=",t,rV('"'===t?rS(!1,e,'"',"&quot;"):rS(!1,e,"'","&apos;")),t]}default:throw new nu(n,"HTML")}},insertPragma:function(e){return`<!-- @format -->

${e}`},massageAstNode:np,embed:function(e,t){let{node:r}=e;switch(r.type){case"element":if(nj(r,t)||"interpolation"===r.type)return;if(!r.isSelfClosing&&n9(r,t)){let n=nX(r,t);return n?async(i,a)=>{let s=iZ(r,t),o=/^\s*$/u.test(s),l="";return o||(o=""===(l=await i(nO(s),{parser:n,__embeddedInHtml:!0}))),[iG(r,t),rJ(iU(e,t,a)),o?"":r0,l,o?"":r0,iI(r,t),iP(r,t)]}:void 0}break;case"text":if(nj(r.parent,t)){let e=nX(r.parent,t);if(e)return async n=>{let i="markdown"===e?n2(r.value.replace(/^[^\S\n]*\n/u,"")):r.value,a={parser:e,__embeddedInHtml:!0};if("html"===t.parser&&"babel"===e){let e="script",{attrMap:t}=r.parent;t&&("module"===t.type||("text/babel"===t.type||"text/jsx"===t.type)&&"module"===t["data-type"])&&(e="module"),a.__babelSourceType=e}return[rZ,iG(r,t),await n(i,a),iP(r,t)]}}else if("interpolation"===r.parent.type)return async n=>{let i={__isInHtmlInterpolation:!0,__embeddedInHtml:!0};return"angular"===t.parser?i.parser="__ng_interpolation":"vue"===t.parser?i.parser=ik(e,t)?"__vue_ts_expression":"__vue_expression":i.parser="__js_expression",[rz([rX,await n(r.value,i)]),r.parent.next&&iW(r.parent.next)?" ":rX]};break;case"attribute":return iA(e,t);case"front-matter":return e=>nf(r,e);case"angularControlFlowBlockParameters":return iX.has(e.parent.name)?nw:void 0;case"angularLetDeclarationInitializer":return e=>nD(r.value,e,{parser:"__ng_binding",__isInHtmlAttribute:!1})}},getVisitorKeys:i1}},sU=rv,sz=Object.create,sG=Object.defineProperty,sJ=Object.getOwnPropertyDescriptor,sK=Object.getOwnPropertyNames,sQ=Object.getPrototypeOf,sZ=Object.prototype.hasOwnProperty,sX=e=>{throw TypeError(e)},sY=(e,t)=>{for(var r in t)sG(e,r,{get:t[r],enumerable:!0})},s0=(e,t,r,n)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let i of sK(t))sZ.call(e,i)||i===r||sG(e,i,{get:()=>t[i],enumerable:!(n=sJ(t,i))||n.enumerable});return e},s1=(e,t,r)=>t.has(e)||sX("Cannot "+r),s2=(e,t,r)=>t.has(e)?sX("Cannot add the same private member more than once"):t instanceof WeakSet?t.add(e):t.set(e,r),s3=(e,t,r)=>(s1(e,t,"access private method"),r),s8=((e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports))(e=>{Object.defineProperty(e,"__esModule",{value:!0});var t=/\r\n|[\n\r\u2028\u2029]/;e.codeFrameColumns=function(e,r,n={}){let i=new Proxy({},{get:()=>e=>e}),{start:a,end:s,markerLines:o}=function(e,t,r){let n=Object.assign({column:0,line:-1},e.start),i=Object.assign({},n,e.end),{linesAbove:a=2,linesBelow:s=3}=r||{},o=n.line,l=n.column,u=i.line,c=i.column,h=Math.max(o-(a+1),0),d=Math.min(t.length,u+s);-1===o&&(h=0),-1===u&&(d=t.length);let p=u-o,f={};if(p)for(let e=0;e<=p;e++){let r=e+o;if(l)if(0===e){let e=t[r-1].length;f[r]=[l,e-l+1]}else if(e===p)f[r]=[0,c];else{let n=t[r-e].length;f[r]=[0,n]}else f[r]=!0}else l===c?l?f[o]=[l,0]:f[o]=!0:f[o]=[l,c-l];return{start:h,end:d,markerLines:f}}(r,e.split(t),n),l=r.start&&"number"==typeof r.start.column,u=String(s).length,c=e.split(t,s).slice(a,s).map((e,t)=>{let r=a+1+t,s=` ${` ${r}`.slice(-u)} |`,l=o[r],c=!o[r+1];if(!l)return` ${i.gutter(s)}${e.length>0?` ${e}`:""}`;{let t="";if(Array.isArray(l)){let r=e.slice(0,Math.max(l[0]-1,0)).replace(/[^\t]/g," "),a=l[1]||1;t=[`
 `,i.gutter(s.replace(/\d/g," "))," ",r,i.marker("^").repeat(a)].join(""),c&&n.message&&(t+=" "+i.message(n.message))}return[i.marker(">"),i.gutter(s),e.length>0?` ${e}`:"",t].join("")}}).join(`
`);return n.message&&!l&&(c=`${" ".repeat(u+1)}${n.message}
${c}`),c}});sY({},{__debug:()=>cl,check:()=>cs,doc:()=>uG,format:()=>ca,formatWithCursor:()=>ci,getSupportInfo:()=>co,util:()=>uX,version:()=>uZ});var s6=(e,t,r,n)=>{if(!(e&&null==t))return t.replaceAll?t.replaceAll(r,n):r.global?t.replace(r,n):t.split(r).join(n)},s5=class{diff(e,t,r={}){let n;"function"==typeof r?(n=r,r={}):"callback"in r&&(n=r.callback);let i=this.castInput(e,r),a=this.castInput(t,r),s=this.removeEmpty(this.tokenize(i,r)),o=this.removeEmpty(this.tokenize(a,r));return this.diffWithOptionsObj(s,o,r,n)}diffWithOptionsObj(e,t,r,n){var i;let a=e=>(e=this.postProcess(e,r),n)?void setTimeout(function(){n(e)},0):e,s=t.length,o=e.length,l=1,u=s+o;null!=r.maxEditLength&&(u=Math.min(u,r.maxEditLength));let c=null!=(i=r.timeout)?i:1/0,h=Date.now()+c,d=[{oldPos:-1,lastComponent:void 0}],p=this.extractCommon(d[0],t,e,0,r);if(d[0].oldPos+1>=o&&p+1>=s)return a(this.buildValues(d[0].lastComponent,t,e));let f=-1/0,m=1/0,g=()=>{for(let n=Math.max(f,-l);n<=Math.min(m,l);n+=2){let i,l=d[n-1],u=d[n+1];l&&(d[n-1]=void 0);let c=!1;if(u){let e=u.oldPos-n;c=u&&0<=e&&e<s}let h=l&&l.oldPos+1<o;if(!c&&!h){d[n]=void 0;continue}if(i=!h||c&&l.oldPos<u.oldPos?this.addToPath(u,!0,!1,0,r):this.addToPath(l,!1,!0,1,r),p=this.extractCommon(i,t,e,n,r),i.oldPos+1>=o&&p+1>=s)return a(this.buildValues(i.lastComponent,t,e))||!0;d[n]=i,i.oldPos+1>=o&&(m=Math.min(m,n-1)),p+1>=s&&(f=Math.max(f,n+1))}l++};if(n)!function e(){setTimeout(function(){if(l>u||Date.now()>h)return n(void 0);g()||e()},0)}();else for(;l<=u&&Date.now()<=h;){let e=g();if(e)return e}}addToPath(e,t,r,n,i){let a=e.lastComponent;return a&&!i.oneChangePerToken&&a.added===t&&a.removed===r?{oldPos:e.oldPos+n,lastComponent:{count:a.count+1,added:t,removed:r,previousComponent:a.previousComponent}}:{oldPos:e.oldPos+n,lastComponent:{count:1,added:t,removed:r,previousComponent:a}}}extractCommon(e,t,r,n,i){let a=t.length,s=r.length,o=e.oldPos,l=o-n,u=0;for(;l+1<a&&o+1<s&&this.equals(r[o+1],t[l+1],i);)l++,o++,u++,i.oneChangePerToken&&(e.lastComponent={count:1,previousComponent:e.lastComponent,added:!1,removed:!1});return u&&!i.oneChangePerToken&&(e.lastComponent={count:u,previousComponent:e.lastComponent,added:!1,removed:!1}),e.oldPos=o,l}equals(e,t,r){return r.comparator?r.comparator(e,t):e===t||!!r.ignoreCase&&e.toLowerCase()===t.toLowerCase()}removeEmpty(e){let t=[];for(let r=0;r<e.length;r++)e[r]&&t.push(e[r]);return t}castInput(e,t){return e}tokenize(e,t){return Array.from(e)}join(e){return e.join("")}postProcess(e,t){return e}get useLongestToken(){return!1}buildValues(e,t,r){let n=[],i;for(;e;)n.push(e),i=e.previousComponent,delete e.previousComponent,e=i;n.reverse();let a=n.length,s=0,o=0,l=0;for(;s<a;s++){let e=n[s];if(e.removed)e.value=this.join(r.slice(l,l+e.count)),l+=e.count;else{if(!e.added&&this.useLongestToken){let n=t.slice(o,o+e.count);n=n.map(function(e,t){let n=r[l+t];return n.length>e.length?n:e}),e.value=this.join(n)}else e.value=this.join(t.slice(o,o+e.count));o+=e.count,e.added||(l+=e.count)}}return n}},s7=new class extends s5{tokenize(e){return e.slice()}join(e){return e}removeEmpty(e){return e}};function s9(e){switch(e){case"cr":return"\r";case"crlf":return`\r
`;default:return`
`}}function s4(e,t){let r;switch(t){case`
`:r=/\n/gu;break;case"\r":r=/\r/gu;break;case`\r
`:r=/\r\n/gu;break;default:throw Error(`Unexpected "eol" ${JSON.stringify(t)}.`)}let n=e.match(r);return n?n.length:0}var oe="string",ot="array",or="cursor",on="indent",oi="align",oa="trim",os="group",oo="fill",ol="if-break",ou="indent-if-break",oc="line-suffix",oh="line-suffix-boundary",od="line",op="label",of="break-parent",om=new Set([or,on,oi,oa,os,oo,ol,ou,oc,oh,od,op,of]),og=(e,t,r)=>{if(!(e&&null==t))return Array.isArray(t)||"string"==typeof t?t[r<0?t.length+r:r]:t.at(r)},oD=function(e){if("string"==typeof e)return oe;if(Array.isArray(e))return ot;if(!e)return;let{type:t}=e;if(om.has(t))return t},oy=e=>new Intl.ListFormat("en-US",{type:"disjunction"}).format(e),ob=class extends Error{name="InvalidDocError";constructor(e){super(function(e){let t=null===e?"null":typeof e;if("string"!==t&&"object"!==t)return`Unexpected doc '${t}', 
Expected it to be 'string' or 'object'.`;if(oD(e))throw Error("doc is valid.");let r=Object.prototype.toString.call(e);if("[object Object]"!==r)return`Unexpected doc '${r}'.`;let n=oy([...om].map(e=>`'${e}'`));return`Unexpected doc.type '${e.type}'.
Expected it to be ${n}.`}(e)),this.doc=e}},oC={},ov=function(e,t,r,n){let i=[e];for(;i.length>0;){let e=i.pop();if(e===oC){r(i.pop());continue}r&&i.push(e,oC);let a=oD(e);if(!a)throw new ob(e);if((null==t?void 0:t(e))!==!1)switch(a){case ot:case oo:{let t=a===ot?e:e.parts;for(let e=t.length,r=e-1;r>=0;--r)i.push(t[r]);break}case ol:i.push(e.flatContents,e.breakContents);break;case os:if(n&&e.expandedStates)for(let t=e.expandedStates.length,r=t-1;r>=0;--r)i.push(e.expandedStates[r]);else i.push(e.contents);break;case oi:case on:case ou:case op:case oc:i.push(e.contents);break;case oe:case or:case oa:case oh:case od:case of:break;default:throw new ob(e)}}};function oS(e,t){if("string"==typeof e)return t(e);let r=new Map;return function e(n){if(r.has(n))return r.get(n);let i=function(r){switch(oD(r)){case ot:return t(r.map(e));case oo:return t({...r,parts:r.parts.map(e)});case ol:return t({...r,breakContents:e(r.breakContents),flatContents:e(r.flatContents)});case os:{let{expandedStates:n,contents:i}=r;return i=n?(n=n.map(e))[0]:e(i),t({...r,contents:i,expandedStates:n})}case oi:case on:case ou:case op:case oc:return t({...r,contents:e(r.contents)});case oe:case or:case oa:case oh:case od:case of:return t(r);default:throw new ob(r)}}(n);return r.set(n,i),i}(e)}function ow(e,t,r){let n=r,i=!1;return ov(e,function(e){if(i)return!1;let r=t(e);void 0!==r&&(i=!0,n=r)}),n}function ok(e){if(e.type===os&&e.break||e.type===od&&e.hard||e.type===of)return!0}function oE(e){if(e.length>0){let t=og(!1,e,-1);t.expandedStates||t.break||(t.break="propagated")}return null}function ox(e){return e.type!==od||e.hard?e.type===ol?e.flatContents:e:e.soft?"":" "}function oF(e){for(e=[...e];e.length>=2&&og(!1,e,-2).type===od&&og(!1,e,-1).type===of;)e.length-=2;if(e.length>0){let t=o_(og(!1,e,-1));e[e.length-1]=t}return e}function o_(e){switch(oD(e)){case on:case ou:case os:case oc:case op:{let t=o_(e.contents);return{...e,contents:t}}case ol:return{...e,breakContents:o_(e.breakContents),flatContents:o_(e.flatContents)};case oo:return{...e,parts:oF(e.parts)};case ot:return oF(e);case oe:let t=e.length;for(;t>0&&("\r"===e[t-1]||e[t-1]===`
`);)t--;return t<e.length?e.slice(0,t):e;case oi:case or:case oa:case oh:case od:case of:break;default:throw new ob(e)}return e}function oT(e){return o_(oS(e,e=>(function(e){switch(oD(e)){case oo:if(e.parts.every(e=>""===e))return"";break;case os:if(!e.contents&&!e.id&&!e.break&&!e.expandedStates)return"";if(e.contents.type===os&&e.contents.id===e.id&&e.contents.break===e.break&&e.contents.expandedStates===e.expandedStates)return e.contents;break;case oi:case on:case ou:case oc:if(!e.contents)return"";break;case ol:if(!e.flatContents&&!e.breakContents)return"";break;case ot:{let t=[];for(let r of e){if(!r)continue;let[e,...n]=Array.isArray(r)?r:[r];"string"==typeof e&&"string"==typeof og(!1,t,-1)?t[t.length-1]+=e:t.push(e),t.push(...n)}return 0===t.length?"":1===t.length?t[0]:t}case oe:case or:case oa:case oh:case od:case op:case of:break;default:throw new ob(e)}return e})(e)))}function oA(e){if(e.type===od)return!0}function oL(e,t){return e.type===op?{...e,contents:t(e.contents)}:t(e)}var oB=()=>{};function oN(e){return oB(e),{type:on,contents:e}}function oI(e,t){return oB(t),{type:oi,contents:t,n:e}}function oq(e,t={}){return oB(e),oB(t.expandedStates,!0),{type:os,id:t.id,contents:e,break:!!t.shouldBreak,expandedStates:t.expandedStates}}function oP(e){return oB(e),{type:oc,contents:e}}var oO={type:of},oR={type:od,hard:!0},oM={type:od,hard:!0,literal:!0},oW={type:od},o$=[oR,oO],oj=[oM,oO],oH={type:or};function oV(e,t){oB(e),oB(t);let r=[];for(let n=0;n<t.length;n++)0!==n&&r.push(e),r.push(t[n]);return r}function oU(e,t,r){oB(e);let n=e;if(t>0){for(let e=0;e<Math.floor(t/r);++e)n=oN(n);n=oI(t%r,n),n=oI(Number.NEGATIVE_INFINITY,n)}return n}var oz=()=>/[#*0-9]\uFE0F?\u20E3|[\xA9\xAE\u203C\u2049\u2122\u2139\u2194-\u2199\u21A9\u21AA\u231A\u231B\u2328\u23CF\u23ED-\u23EF\u23F1\u23F2\u23F8-\u23FA\u24C2\u25AA\u25AB\u25B6\u25C0\u25FB\u25FC\u25FE\u2600-\u2604\u260E\u2611\u2614\u2615\u2618\u2620\u2622\u2623\u2626\u262A\u262E\u262F\u2638-\u263A\u2640\u2642\u2648-\u2653\u265F\u2660\u2663\u2665\u2666\u2668\u267B\u267E\u267F\u2692\u2694-\u2697\u2699\u269B\u269C\u26A0\u26A7\u26AA\u26B0\u26B1\u26BD\u26BE\u26C4\u26C8\u26CF\u26D1\u26E9\u26F0-\u26F5\u26F7\u26F8\u26FA\u2702\u2708\u2709\u270F\u2712\u2714\u2716\u271D\u2721\u2733\u2734\u2744\u2747\u2757\u2763\u27A1\u2934\u2935\u2B05-\u2B07\u2B1B\u2B1C\u2B55\u3030\u303D\u3297\u3299]\uFE0F?|[\u261D\u270C\u270D](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\u270A\u270B](?:\uD83C[\uDFFB-\uDFFF])?|[\u23E9-\u23EC\u23F0\u23F3\u25FD\u2693\u26A1\u26AB\u26C5\u26CE\u26D4\u26EA\u26FD\u2705\u2728\u274C\u274E\u2753-\u2755\u2795-\u2797\u27B0\u27BF\u2B50]|\u26D3\uFE0F?(?:\u200D\uD83D\uDCA5)?|\u26F9(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\u2764\uFE0F?(?:\u200D(?:\uD83D\uDD25|\uD83E\uDE79))?|\uD83C(?:[\uDC04\uDD70\uDD71\uDD7E\uDD7F\uDE02\uDE37\uDF21\uDF24-\uDF2C\uDF36\uDF7D\uDF96\uDF97\uDF99-\uDF9B\uDF9E\uDF9F\uDFCD\uDFCE\uDFD4-\uDFDF\uDFF5\uDFF7]\uFE0F?|[\uDF85\uDFC2\uDFC7](?:\uD83C[\uDFFB-\uDFFF])?|[\uDFC4\uDFCA](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDFCB\uDFCC](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDCCF\uDD8E\uDD91-\uDD9A\uDE01\uDE1A\uDE2F\uDE32-\uDE36\uDE38-\uDE3A\uDE50\uDE51\uDF00-\uDF20\uDF2D-\uDF35\uDF37-\uDF43\uDF45-\uDF4A\uDF4C-\uDF7C\uDF7E-\uDF84\uDF86-\uDF93\uDFA0-\uDFC1\uDFC5\uDFC6\uDFC8\uDFC9\uDFCF-\uDFD3\uDFE0-\uDFF0\uDFF8-\uDFFF]|\uDDE6\uD83C[\uDDE8-\uDDEC\uDDEE\uDDF1\uDDF2\uDDF4\uDDF6-\uDDFA\uDDFC\uDDFD\uDDFF]|\uDDE7\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEF\uDDF1-\uDDF4\uDDF6-\uDDF9\uDDFB\uDDFC\uDDFE\uDDFF]|\uDDE8\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDEE\uDDF0-\uDDF7\uDDFA-\uDDFF]|\uDDE9\uD83C[\uDDEA\uDDEC\uDDEF\uDDF0\uDDF2\uDDF4\uDDFF]|\uDDEA\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDED\uDDF7-\uDDFA]|\uDDEB\uD83C[\uDDEE-\uDDF0\uDDF2\uDDF4\uDDF7]|\uDDEC\uD83C[\uDDE6\uDDE7\uDDE9-\uDDEE\uDDF1-\uDDF3\uDDF5-\uDDFA\uDDFC\uDDFE]|\uDDED\uD83C[\uDDF0\uDDF2\uDDF3\uDDF7\uDDF9\uDDFA]|\uDDEE\uD83C[\uDDE8-\uDDEA\uDDF1-\uDDF4\uDDF6-\uDDF9]|\uDDEF\uD83C[\uDDEA\uDDF2\uDDF4\uDDF5]|\uDDF0\uD83C[\uDDEA\uDDEC-\uDDEE\uDDF2\uDDF3\uDDF5\uDDF7\uDDFC\uDDFE\uDDFF]|\uDDF1\uD83C[\uDDE6-\uDDE8\uDDEE\uDDF0\uDDF7-\uDDFB\uDDFE]|\uDDF2\uD83C[\uDDE6\uDDE8-\uDDED\uDDF0-\uDDFF]|\uDDF3\uD83C[\uDDE6\uDDE8\uDDEA-\uDDEC\uDDEE\uDDF1\uDDF4\uDDF5\uDDF7\uDDFA\uDDFF]|\uDDF4\uD83C\uDDF2|\uDDF5\uD83C[\uDDE6\uDDEA-\uDDED\uDDF0-\uDDF3\uDDF7-\uDDF9\uDDFC\uDDFE]|\uDDF6\uD83C\uDDE6|\uDDF7\uD83C[\uDDEA\uDDF4\uDDF8\uDDFA\uDDFC]|\uDDF8\uD83C[\uDDE6-\uDDEA\uDDEC-\uDDF4\uDDF7-\uDDF9\uDDFB\uDDFD-\uDDFF]|\uDDF9\uD83C[\uDDE6\uDDE8\uDDE9\uDDEB-\uDDED\uDDEF-\uDDF4\uDDF7\uDDF9\uDDFB\uDDFC\uDDFF]|\uDDFA\uD83C[\uDDE6\uDDEC\uDDF2\uDDF3\uDDF8\uDDFE\uDDFF]|\uDDFB\uD83C[\uDDE6\uDDE8\uDDEA\uDDEC\uDDEE\uDDF3\uDDFA]|\uDDFC\uD83C[\uDDEB\uDDF8]|\uDDFD\uD83C\uDDF0|\uDDFE\uD83C[\uDDEA\uDDF9]|\uDDFF\uD83C[\uDDE6\uDDF2\uDDFC]|\uDF44(?:\u200D\uD83D\uDFEB)?|\uDF4B(?:\u200D\uD83D\uDFE9)?|\uDFC3(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDFF3\uFE0F?(?:\u200D(?:\u26A7\uFE0F?|\uD83C\uDF08))?|\uDFF4(?:\u200D\u2620\uFE0F?|\uDB40\uDC67\uDB40\uDC62\uDB40(?:\uDC65\uDB40\uDC6E\uDB40\uDC67|\uDC73\uDB40\uDC63\uDB40\uDC74|\uDC77\uDB40\uDC6C\uDB40\uDC73)\uDB40\uDC7F)?)|\uD83D(?:[\uDC3F\uDCFD\uDD49\uDD4A\uDD6F\uDD70\uDD73\uDD76-\uDD79\uDD87\uDD8A-\uDD8D\uDDA5\uDDA8\uDDB1\uDDB2\uDDBC\uDDC2-\uDDC4\uDDD1-\uDDD3\uDDDC-\uDDDE\uDDE1\uDDE3\uDDE8\uDDEF\uDDF3\uDDFA\uDECB\uDECD-\uDECF\uDEE0-\uDEE5\uDEE9\uDEF0\uDEF3]\uFE0F?|[\uDC42\uDC43\uDC46-\uDC50\uDC66\uDC67\uDC6B-\uDC6D\uDC72\uDC74-\uDC76\uDC78\uDC7C\uDC83\uDC85\uDC8F\uDC91\uDCAA\uDD7A\uDD95\uDD96\uDE4C\uDE4F\uDEC0\uDECC](?:\uD83C[\uDFFB-\uDFFF])?|[\uDC6E\uDC70\uDC71\uDC73\uDC77\uDC81\uDC82\uDC86\uDC87\uDE45-\uDE47\uDE4B\uDE4D\uDE4E\uDEA3\uDEB4\uDEB5](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD74\uDD90](?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?|[\uDC00-\uDC07\uDC09-\uDC14\uDC16-\uDC25\uDC27-\uDC3A\uDC3C-\uDC3E\uDC40\uDC44\uDC45\uDC51-\uDC65\uDC6A\uDC79-\uDC7B\uDC7D-\uDC80\uDC84\uDC88-\uDC8E\uDC90\uDC92-\uDCA9\uDCAB-\uDCFC\uDCFF-\uDD3D\uDD4B-\uDD4E\uDD50-\uDD67\uDDA4\uDDFB-\uDE2D\uDE2F-\uDE34\uDE37-\uDE41\uDE43\uDE44\uDE48-\uDE4A\uDE80-\uDEA2\uDEA4-\uDEB3\uDEB7-\uDEBF\uDEC1-\uDEC5\uDED0-\uDED2\uDED5-\uDED7\uDEDC-\uDEDF\uDEEB\uDEEC\uDEF4-\uDEFC\uDFE0-\uDFEB\uDFF0]|\uDC08(?:\u200D\u2B1B)?|\uDC15(?:\u200D\uD83E\uDDBA)?|\uDC26(?:\u200D(?:\u2B1B|\uD83D\uDD25))?|\uDC3B(?:\u200D\u2744\uFE0F?)?|\uDC41\uFE0F?(?:\u200D\uD83D\uDDE8\uFE0F?)?|\uDC68(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDC68\uDC69]\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?)|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?\uDC68\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D\uDC68\uD83C[\uDFFB-\uDFFE])))?))?|\uDC69(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:\uDC8B\u200D\uD83D)?[\uDC68\uDC69]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D(?:[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?|\uDC69\u200D\uD83D(?:\uDC66(?:\u200D\uD83D\uDC66)?|\uDC67(?:\u200D\uD83D[\uDC66\uDC67])?))|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFC-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFD-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFD\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D\uD83D(?:[\uDC68\uDC69]|\uDC8B\u200D\uD83D[\uDC68\uDC69])\uD83C[\uDFFB-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83D[\uDC68\uDC69]\uD83C[\uDFFB-\uDFFE])))?))?|\uDC6F(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDD75(?:\uD83C[\uDFFB-\uDFFF]|\uFE0F)?(?:\u200D[\u2640\u2642]\uFE0F?)?|\uDE2E(?:\u200D\uD83D\uDCA8)?|\uDE35(?:\u200D\uD83D\uDCAB)?|\uDE36(?:\u200D\uD83C\uDF2B\uFE0F?)?|\uDE42(?:\u200D[\u2194\u2195]\uFE0F?)?|\uDEB6(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?)|\uD83E(?:[\uDD0C\uDD0F\uDD18-\uDD1F\uDD30-\uDD34\uDD36\uDD77\uDDB5\uDDB6\uDDBB\uDDD2\uDDD3\uDDD5\uDEC3-\uDEC5\uDEF0\uDEF2-\uDEF8](?:\uD83C[\uDFFB-\uDFFF])?|[\uDD26\uDD35\uDD37-\uDD39\uDD3D\uDD3E\uDDB8\uDDB9\uDDCD\uDDCF\uDDD4\uDDD6-\uDDDD](?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDDDE\uDDDF](?:\u200D[\u2640\u2642]\uFE0F?)?|[\uDD0D\uDD0E\uDD10-\uDD17\uDD20-\uDD25\uDD27-\uDD2F\uDD3A\uDD3F-\uDD45\uDD47-\uDD76\uDD78-\uDDB4\uDDB7\uDDBA\uDDBC-\uDDCC\uDDD0\uDDE0-\uDDFF\uDE70-\uDE7C\uDE80-\uDE89\uDE8F-\uDEC2\uDEC6\uDECE-\uDEDC\uDEDF-\uDEE9]|\uDD3C(?:\u200D[\u2640\u2642]\uFE0F?|\uD83C[\uDFFB-\uDFFF])?|\uDDCE(?:\uD83C[\uDFFB-\uDFFF])?(?:\u200D(?:[\u2640\u2642]\uFE0F?(?:\u200D\u27A1\uFE0F?)?|\u27A1\uFE0F?))?|\uDDD1(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1|\uDDD1\u200D\uD83E\uDDD2(?:\u200D\uD83E\uDDD2)?|\uDDD2(?:\u200D\uD83E\uDDD2)?))|\uD83C(?:\uDFFB(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFC-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFC(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFD-\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFD(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFE(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFD\uDFFF]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?|\uDFFF(?:\u200D(?:[\u2695\u2696\u2708]\uFE0F?|\u2764\uFE0F?\u200D(?:\uD83D\uDC8B\u200D)?\uD83E\uDDD1\uD83C[\uDFFB-\uDFFE]|\uD83C[\uDF3E\uDF73\uDF7C\uDF84\uDF93\uDFA4\uDFA8\uDFEB\uDFED]|\uD83D[\uDCBB\uDCBC\uDD27\uDD2C\uDE80\uDE92]|\uD83E(?:[\uDDAF\uDDBC\uDDBD](?:\u200D\u27A1\uFE0F?)?|[\uDDB0-\uDDB3]|\uDD1D\u200D\uD83E\uDDD1\uD83C[\uDFFB-\uDFFF])))?))?|\uDEF1(?:\uD83C(?:\uDFFB(?:\u200D\uD83E\uDEF2\uD83C[\uDFFC-\uDFFF])?|\uDFFC(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFD-\uDFFF])?|\uDFFD(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB\uDFFC\uDFFE\uDFFF])?|\uDFFE(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFD\uDFFF])?|\uDFFF(?:\u200D\uD83E\uDEF2\uD83C[\uDFFB-\uDFFE])?))?)/g,oG=e=>!(function(e){return 12288===e||e>=65281&&e<=65376||e>=65504&&e<=65510}(e)||function(e){return e>=4352&&e<=4447||8986===e||8987===e||9001===e||9002===e||e>=9193&&e<=9196||9200===e||9203===e||9725===e||9726===e||9748===e||9749===e||e>=9776&&e<=9783||e>=9800&&e<=9811||9855===e||e>=9866&&e<=9871||9875===e||9889===e||9898===e||9899===e||9917===e||9918===e||9924===e||9925===e||9934===e||9940===e||9962===e||9970===e||9971===e||9973===e||9978===e||9981===e||9989===e||9994===e||9995===e||10024===e||10060===e||10062===e||e>=10067&&e<=10069||10071===e||e>=10133&&e<=10135||10160===e||10175===e||11035===e||11036===e||11088===e||11093===e||e>=11904&&e<=11929||e>=11931&&e<=12019||e>=12032&&e<=12245||e>=12272&&e<=12287||e>=12289&&e<=12350||e>=12353&&e<=12438||e>=12441&&e<=12543||e>=12549&&e<=12591||e>=12593&&e<=12686||e>=12688&&e<=12773||e>=12783&&e<=12830||e>=12832&&e<=12871||e>=12880&&e<=42124||e>=42128&&e<=42182||e>=43360&&e<=43388||e>=44032&&e<=55203||e>=63744&&e<=64255||e>=65040&&e<=65049||e>=65072&&e<=65106||e>=65108&&e<=65126||e>=65128&&e<=65131||e>=94176&&e<=94180||94192===e||94193===e||e>=94208&&e<=100343||e>=100352&&e<=101589||e>=101631&&e<=101640||e>=110576&&e<=110579||e>=110581&&e<=110587||110589===e||110590===e||e>=110592&&e<=110882||110898===e||e>=110928&&e<=110930||110933===e||e>=110948&&e<=110951||e>=110960&&e<=111355||e>=119552&&e<=119638||e>=119648&&e<=119670||126980===e||127183===e||127374===e||e>=127377&&e<=127386||e>=127488&&e<=127490||e>=127504&&e<=127547||e>=127552&&e<=127560||127568===e||127569===e||e>=127584&&e<=127589||e>=127744&&e<=127776||e>=127789&&e<=127797||e>=127799&&e<=127868||e>=127870&&e<=127891||e>=127904&&e<=127946||e>=127951&&e<=127955||e>=127968&&e<=127984||127988===e||e>=127992&&e<=128062||128064===e||e>=128066&&e<=128252||e>=128255&&e<=128317||e>=128331&&e<=128334||e>=128336&&e<=128359||128378===e||128405===e||128406===e||128420===e||e>=128507&&e<=128591||e>=128640&&e<=128709||128716===e||e>=128720&&e<=128722||e>=128725&&e<=128727||e>=128732&&e<=128735||128747===e||128748===e||e>=128756&&e<=128764||e>=128992&&e<=129003||129008===e||e>=129292&&e<=129338||e>=129340&&e<=129349||e>=129351&&e<=129535||e>=129648&&e<=129660||e>=129664&&e<=129673||e>=129679&&e<=129734||e>=129742&&e<=129756||e>=129759&&e<=129769||e>=129776&&e<=129784||e>=131072&&e<=196605||e>=196608&&e<=262141}(e)),oJ=/[^\x20-\x7F]/u,oK=function(e){if(!e)return 0;if(!oJ.test(e))return e.length;e=e.replace(oz(),"  ");let t=0;for(let r of e){let e=r.codePointAt(0);e<=31||e>=127&&e<=159||e>=768&&e<=879||(t+=oG(e)?1:2)}return t},oQ=Symbol("MODE_BREAK"),oZ=Symbol("MODE_FLAT"),oX=Symbol("cursor"),oY=Symbol("DOC_FILL_PRINTED_LENGTH");function o0(){return{value:"",length:0,queue:[]}}function o1(e,t,r){let n="dedent"===t.type?e.queue.slice(0,-1):[...e.queue,t],i="",a=0,s=0,o=0;for(let e of n)switch(e.type){case"indent":c(),r.useTabs?l(1):u(r.tabWidth);break;case"stringAlign":c(),i+=e.n,a+=e.n.length;break;case"numberAlign":s+=1,o+=e.n;break;default:throw Error(`Unexpected type '${e.type}'`)}return h(),{...e,value:i,length:a,queue:n};function l(e){i+="	".repeat(e),a+=r.tabWidth*e}function u(e){i+=" ".repeat(e),a+=e}function c(){r.useTabs?(s>0&&l(s),s=0,o=0):h()}function h(){o>0&&u(o),s=0,o=0}}function o2(e){let t=0,r=0,n=e.length;e:for(;n--;){let i=e[n];if(i===oX){r++;continue}for(let r=i.length-1;r>=0;r--){let a=i[r];if(" "===a||"	"===a)t++;else{e[n]=i.slice(0,r+1);break e}}}if(t>0||r>0)for(e.length=n+1;r-- >0;)e.push(oX);return t}function o3(e,t,r,n,i,a){if(r===Number.POSITIVE_INFINITY)return!0;let s=t.length,o=[e],l=[];for(;r>=0;){if(0===o.length){if(0===s)return!0;o.push(t[--s]);continue}let{mode:e,doc:u}=o.pop(),c=oD(u);switch(c){case oe:l.push(u),r-=oK(u);break;case ot:case oo:{let t=c===ot?u:u.parts,r=u[oY]??0;for(let n=t.length-1;n>=r;n--)o.push({mode:e,doc:t[n]});break}case on:case oi:case ou:case op:o.push({mode:e,doc:u.contents});break;case oa:r+=o2(l);break;case os:{if(a&&u.break)return!1;let t=u.break?oQ:e,r=u.expandedStates&&t===oQ?og(!1,u.expandedStates,-1):u.contents;o.push({mode:t,doc:r});break}case ol:{let t=(u.groupId?i[u.groupId]||oZ:e)===oQ?u.breakContents:u.flatContents;t&&o.push({mode:e,doc:t});break}case od:if(e===oQ||u.hard)return!0;u.soft||(l.push(" "),r--);break;case oc:n=!0;break;case oh:if(n)return!1}}return!1}function o8(e,t){var r;let n,i,a={},s=t.printWidth,o=s9(t.endOfLine),l=0,u=[{ind:o0(),mode:oQ,doc:e}],c=[],h=!1,d=[],p=0;for(n=new Set,i=[],ov(e,function(e){if(e.type===of&&oE(i),e.type===os){if(i.push(e),n.has(e))return!1;n.add(e)}},function(e){e.type===os&&i.pop().break&&oE(i)},!0);u.length>0;){let{ind:e,mode:n,doc:i}=u.pop();switch(oD(i)){case oe:{let e=o!==`
`?s6(!1,i,`
`,o):i;c.push(e),u.length>0&&(l+=oK(e));break}case ot:for(let t=i.length-1;t>=0;t--)u.push({ind:e,mode:n,doc:i[t]});break;case or:if(p>=2)throw Error("There are too many 'cursor' in doc.");c.push(oX),p++;break;case on:u.push({ind:o1(e,{type:"indent"},t),mode:n,doc:i.contents});break;case oi:u.push({ind:(r=i.n,r===Number.NEGATIVE_INFINITY?e.root||o0():r<0?o1(e,{type:"dedent"},t):r?"root"===r.type?{...e,root:e}:o1(e,{type:"string"==typeof r?"stringAlign":"numberAlign",n:r},t):e),mode:n,doc:i.contents});break;case oa:l-=o2(c);break;case os:switch(n){case oZ:if(!h){u.push({ind:e,mode:i.break?oQ:oZ,doc:i.contents});break}case oQ:{h=!1;let t={ind:e,mode:oZ,doc:i.contents},r=s-l,n=d.length>0;if(!i.break&&o3(t,u,r,n,a))u.push(t);else if(i.expandedStates){let t=og(!1,i.expandedStates,-1);if(i.break)u.push({ind:e,mode:oQ,doc:t});else for(let s=1;s<i.expandedStates.length+1;s++)if(s>=i.expandedStates.length){u.push({ind:e,mode:oQ,doc:t});break}else{let t={ind:e,mode:oZ,doc:i.expandedStates[s]};if(o3(t,u,r,n,a)){u.push(t);break}}}else u.push({ind:e,mode:oQ,doc:i.contents})}}i.id&&(a[i.id]=og(!1,u,-1).mode);break;case oo:{let t=s-l,r=i[oY]??0,{parts:o}=i,c=o.length-r;if(0===c)break;let h=o[r+0],p=o[r+1],f={ind:e,mode:oZ,doc:h},m={ind:e,mode:oQ,doc:h},g=o3(f,[],t,d.length>0,a,!0);if(1===c){g?u.push(f):u.push(m);break}let D={ind:e,mode:oZ,doc:p},y={ind:e,mode:oQ,doc:p};if(2===c){g?u.push(D,f):u.push(y,m);break}let b=o[r+2],C={ind:e,mode:n,doc:{...i,[oY]:r+2}};o3({ind:e,mode:oZ,doc:[h,p,b]},[],t,d.length>0,a,!0)?u.push(C,D,f):g?u.push(C,y,f):u.push(C,y,m);break}case ol:case ou:{let t=i.groupId?a[i.groupId]:n;if(t===oQ){let t=i.type===ol?i.breakContents:i.negate?i.contents:oN(i.contents);t&&u.push({ind:e,mode:n,doc:t})}if(t===oZ){let t=i.type===ol?i.flatContents:i.negate?oN(i.contents):i.contents;t&&u.push({ind:e,mode:n,doc:t})}break}case oc:d.push({ind:e,mode:n,doc:i.contents});break;case oh:d.length>0&&u.push({ind:e,mode:n,doc:oR});break;case od:switch(n){case oZ:if(i.hard)h=!0;else{i.soft||(c.push(" "),l+=1);break}case oQ:if(d.length>0){u.push({ind:e,mode:n,doc:i},...d.reverse()),d.length=0;break}i.literal?e.root?(c.push(o,e.root.value),l=e.root.length):(c.push(o),l=0):(l-=o2(c),c.push(o+e.value),l=e.length)}break;case op:u.push({ind:e,mode:n,doc:i.contents});break;case of:break;default:throw new ob(i)}0===u.length&&d.length>0&&(u.push(...d.reverse()),d.length=0)}let f=c.indexOf(oX);if(-1!==f){let e=c.indexOf(oX,f+1);if(-1===e)return{formatted:c.filter(e=>e!==oX).join("")};let t=c.slice(0,f).join(""),r=c.slice(f+1,e).join("");return{formatted:t+r+c.slice(e+1).join(""),cursorNodeStart:t.length,cursorNodeText:r}}return{formatted:c.join("")}}var o6,o5,o7,o9=function(e,t,r=0){let n=0;for(let i=r;i<e.length;++i)"	"===e[i]?n=n+t-n%t:n++;return n},o4=class{constructor(e){s2(this,o6),this.stack=[e]}get key(){let{stack:e,siblings:t}=this;return og(!1,e,null===t?-2:-4)??null}get index(){return null===this.siblings?null:og(!1,this.stack,-2)}get node(){return og(!1,this.stack,-1)}get parent(){return this.getNode(1)}get grandparent(){return this.getNode(2)}get isInArray(){return null!==this.siblings}get siblings(){let{stack:e}=this,t=og(!1,e,-3);return Array.isArray(t)?t:null}get next(){let{siblings:e}=this;return null===e?null:e[this.index+1]}get previous(){let{siblings:e}=this;return null===e?null:e[this.index-1]}get isFirst(){return 0===this.index}get isLast(){let{siblings:e,index:t}=this;return null!==e&&t===e.length-1}get isRoot(){return 1===this.stack.length}get root(){return this.stack[0]}get ancestors(){return[...s3(this,o6,o7).call(this)]}getName(){let{stack:e}=this,{length:t}=e;return t>1?og(!1,e,-2):null}getValue(){return og(!1,this.stack,-1)}getNode(e=0){let t=s3(this,o6,o5).call(this,e);return -1===t?null:this.stack[t]}getParentNode(e=0){return this.getNode(e+1)}call(e,...t){let{stack:r}=this,{length:n}=r,i=og(!1,r,-1);for(let e of t)i=i[e],r.push(e,i);try{return e(this)}finally{r.length=n}}callParent(e,t=0){let r=s3(this,o6,o5).call(this,t+1),n=this.stack.splice(r+1);try{return e(this)}finally{this.stack.push(...n)}}each(e,...t){let{stack:r}=this,{length:n}=r,i=og(!1,r,-1);for(let e of t)i=i[e],r.push(e,i);try{for(let t=0;t<i.length;++t)r.push(t,i[t]),e(this,t,i),r.length-=2}finally{r.length=n}}map(e,...t){let r=[];return this.each((t,n,i)=>{r[n]=e(t,n,i)},...t),r}match(...e){let t=this.stack.length-1,r=null,n=this.stack[t--];for(let i of e){if(void 0===n)return!1;let e=null;if("number"==typeof r&&(e=r,r=this.stack[t--],n=this.stack[t--]),i&&!i(n,r,e))return!1;r=this.stack[t--],n=this.stack[t--]}return!0}findAncestor(e){for(let t of s3(this,o6,o7).call(this))if(e(t))return t}hasAncestor(e){for(let t of s3(this,o6,o7).call(this))if(e(t))return!0;return!1}};o6=new WeakSet,o5=function(e){let{stack:t}=this;for(let r=t.length-1;r>=0;r-=2)if(!Array.isArray(t[r])&&--e<0)return r;return -1},o7=function*(){let{stack:e}=this;for(let t=e.length-3;t>=0;t-=2){let r=e[t];Array.isArray(r)||(yield r)}};var le=new Proxy(()=>{},{get:()=>le});function*lt(e,t){let{getVisitorKeys:r,filter:n=()=>!0}=t,i=e=>null!==e&&"object"==typeof e&&n(e);for(let t of r(e)){let r=e[t];if(Array.isArray(r))for(let e of r)i(e)&&(yield e);else i(r)&&(yield r)}}function lr(e){return(t,r,n)=>{let i=!!(null!=n&&n.backwards);if(!1===r)return!1;let{length:a}=t,s=r;for(;s>=0&&s<a;){let r=t.charAt(s);if(e instanceof RegExp){if(!e.test(r))return s}else if(!e.includes(r))return s;i?s--:s++}return(-1===s||s===a)&&s}}var ln=lr(/\s/u),li=lr(" 	"),la=lr(",; 	"),ls=lr(/[^\n\r]/u),lo=function(e,t,r){let n=!!(null!=r&&r.backwards);if(!1===t)return!1;let i=e.charAt(t);if(n){if("\r"===e.charAt(t-1)&&i===`
`)return t-2;if(i===`
`||"\r"===i||"\u2028"===i||"\u2029"===i)return t-1}else{if("\r"===i&&e.charAt(t+1)===`
`)return t+2;if(i===`
`||"\r"===i||"\u2028"===i||"\u2029"===i)return t+1}return t},ll=function(e,t,r={}){let n=li(e,r.backwards?t-1:t,r),i=lo(e,n,r);return n!==i},lu=new Set(["tokens","comments","parent","enclosingNode","precedingNode","followingNode"]),lc=e=>Object.keys(e).filter(e=>!lu.has(e)),lh=function(e){return e?t=>e(t,lu):lc};function ld(e,t){let r,n;(e.comments??(e.comments=[])).push(t),t.printed=!1,r=e.type||e.kind||"(unknown type)",(n=String(e.name||e.id&&("object"==typeof e.id?e.id.name:e.id)||e.key&&("object"==typeof e.key?e.key.name:e.key)||e.value&&("object"==typeof e.value?"":String(e.value))||e.operator||"")).length>20&&(n=n.slice(0,19)+"…"),t.nodeDescription=r+(n?" "+n:"")}function lp(e,t){t.leading=!0,t.trailing=!1,ld(e,t)}function lf(e,t,r){t.leading=!1,t.trailing=!1,r&&(t.marker=r),ld(e,t)}function lm(e,t){t.leading=!1,t.trailing=!0,ld(e,t)}var lg=new WeakMap;function lD(e,t){if(lg.has(e))return lg.get(e);let{printer:{getCommentChildNodes:r,canAttachComment:n,getVisitorKeys:i},locStart:a,locEnd:s}=t;if(!n)return[];let o=((null==r?void 0:r(e,t))??[...lt(e,{getVisitorKeys:lh(i)})]).flatMap(e=>n(e)?[e]:lD(e,t));return o.sort((e,t)=>a(e)-a(t)||s(e)-s(t)),lg.set(e,o),o}var ly=()=>!1,lb=e=>!/[\S\n\u2028\u2029]/u.test(e);function lC(e,t){var r,n;let i=e.length;if(0===i)return;let{precedingNode:a,followingNode:s}=e[0],o=t.locStart(s),l;for(l=i;l>0;--l){let{comment:i,precedingNode:u,followingNode:c}=e[l-1];le.strictEqual(u,a),le.strictEqual(c,s);let h=t.originalText.slice(t.locEnd(i),o);if((null==(n=(r=t.printer).isGap)?void 0:n.call(r,h,t))??/^[\s(]*$/u.test(h))o=t.locStart(i);else break}for(let[t,{comment:r}]of e.entries())t<l?lm(a,r):lp(s,r);for(let e of[a,s])e.comments&&e.comments.length>1&&e.comments.sort((e,r)=>t.locStart(e)-t.locStart(r));e.length=0}function lv(e,t,r){let n=r.locStart(t)-1;for(let t=1;t<e.length;++t)if(n<r.locStart(e[t]))return t-1;return 0}var lS=function(e,t){let r=t-1;r=li(e,r,{backwards:!0}),r=lo(e,r,{backwards:!0}),r=li(e,r,{backwards:!0});let n=lo(e,r,{backwards:!0});return r!==n};function lw(e,t){return e.node.printed=!0,t.printer.printComment(e,t)}var lk=class extends Error{name="ConfigError"},lE=class extends Error{name="UndefinedParserError"},lx={checkIgnorePragma:{category:"Special",type:"boolean",default:!1,description:"Check whether the file's first docblock comment contains '@noprettier' or '@noformat' to determine if it should be formatted.",cliCategory:"Other"},cursorOffset:{category:"Special",type:"int",default:-1,range:{start:-1,end:1/0,step:1},description:"Print (to stderr) where a cursor at the given position would move to after formatting.",cliCategory:"Editor"},endOfLine:{category:"Global",type:"choice",default:"lf",description:"Which end of line characters to apply.",choices:[{value:"lf",description:"Line Feed only (\\n), common on Linux and macOS as well as inside git repos"},{value:"crlf",description:"Carriage Return + Line Feed characters (\\r\\n), common on Windows"},{value:"cr",description:"Carriage Return character only (\\r), used very rarely"},{value:"auto",description:`Maintain existing
(mixed values within one file are normalised by looking at what's used after the first line)`}]},filepath:{category:"Special",type:"path",description:"Specify the input filepath. This will be used to do parser inference.",cliName:"stdin-filepath",cliCategory:"Other",cliDescription:"Path to the file to pretend that stdin comes from."},insertPragma:{category:"Special",type:"boolean",default:!1,description:"Insert @format pragma into file's first docblock comment.",cliCategory:"Other"},parser:{category:"Global",type:"choice",default:void 0,description:"Which parser to use.",exception:e=>"string"==typeof e||"function"==typeof e,choices:[{value:"flow",description:"Flow"},{value:"babel",description:"JavaScript"},{value:"babel-flow",description:"Flow"},{value:"babel-ts",description:"TypeScript"},{value:"typescript",description:"TypeScript"},{value:"acorn",description:"JavaScript"},{value:"espree",description:"JavaScript"},{value:"meriyah",description:"JavaScript"},{value:"css",description:"CSS"},{value:"less",description:"Less"},{value:"scss",description:"SCSS"},{value:"json",description:"JSON"},{value:"json5",description:"JSON5"},{value:"jsonc",description:"JSON with Comments"},{value:"json-stringify",description:"JSON.stringify"},{value:"graphql",description:"GraphQL"},{value:"markdown",description:"Markdown"},{value:"mdx",description:"MDX"},{value:"vue",description:"Vue"},{value:"yaml",description:"YAML"},{value:"glimmer",description:"Ember / Handlebars"},{value:"html",description:"HTML"},{value:"angular",description:"Angular"},{value:"lwc",description:"Lightning Web Components"},{value:"mjml",description:"MJML"}]},plugins:{type:"path",array:!0,default:[{value:[]}],category:"Global",description:"Add a plugin. Multiple plugins can be passed as separate `--plugin`s.",exception:e=>"string"==typeof e||"object"==typeof e,cliName:"plugin",cliCategory:"Config"},printWidth:{category:"Global",type:"int",default:80,description:"The line length where Prettier will try wrap.",range:{start:0,end:1/0,step:1}},rangeEnd:{category:"Special",type:"int",default:1/0,range:{start:0,end:1/0,step:1},description:`Format code ending at a given character offset (exclusive).
The range will extend forwards to the end of the selected statement.`,cliCategory:"Editor"},rangeStart:{category:"Special",type:"int",default:0,range:{start:0,end:1/0,step:1},description:`Format code starting at a given character offset.
The range will extend backwards to the start of the first line containing the selected statement.`,cliCategory:"Editor"},requirePragma:{category:"Special",type:"boolean",default:!1,description:"Require either '@prettier' or '@format' to be present in the file's first docblock comment in order for it to be formatted.",cliCategory:"Other"},tabWidth:{type:"int",category:"Global",default:2,description:"Number of spaces per indentation level.",range:{start:0,end:1/0,step:1}},useTabs:{category:"Global",type:"boolean",default:!1,description:"Indent with tabs instead of spaces."},embeddedLanguageFormatting:{category:"Global",type:"choice",default:"auto",description:"Control how Prettier formats quoted code embedded in the file.",choices:[{value:"auto",description:"Format embedded code if Prettier can automatically identify it."},{value:"off",description:"Never automatically format embedded code."}]}};function lF({plugins:e=[],showDeprecated:t=!1}={}){let r=e.flatMap(e=>e.languages??[]),n=[];for(let i of function(e){let t=[];for(let[r,n]of Object.entries(e)){let e={name:r,...n};Array.isArray(e.default)&&(e.default=og(!1,e.default,-1).value),t.push(e)}return t}(Object.assign({},...e.map(({options:e})=>e),lx)))!t&&i.deprecated||(Array.isArray(i.choices)&&(t||(i.choices=i.choices.filter(e=>!e.deprecated)),"parser"===i.name&&(i.choices=[...i.choices,...function*(e,t,r){let n=new Set(e.map(e=>e.value));for(let e of t)if(e.parsers){for(let t of e.parsers)if(!n.has(t)){n.add(t);let i=r.find(e=>e.parsers&&Object.prototype.hasOwnProperty.call(e.parsers,t)),a=e.name;null!=i&&i.name&&(a+=` (plugin: ${i.name})`),yield{value:t,description:a}}}}(i.choices,r,e)])),i.pluginDefaults=Object.fromEntries(e.filter(e=>{var t;return(null==(t=e.defaultOptions)?void 0:t[i.name])!==void 0}).map(e=>[e.name,e.defaultOptions[i.name]])),n.push(i));return{languages:r,options:n}}var l_,lT,lA,lL,lB,lN=(e,t)=>{if(!(e&&null==t))return t.toReversed||!Array.isArray(t)?t.toReversed():[...t].reverse()},lI=(null==(l_=globalThis.Deno)?void 0:l_.build.os)==="windows"||(null==(lA=null==(lT=globalThis.navigator)?void 0:lT.platform)?void 0:lA.startsWith("Win"))||(null==(lB=null==(lL=globalThis.process)?void 0:lL.platform)?void 0:lB.startsWith("win"))||!1;function lq(e){if("file:"!==(e=e instanceof URL?e:new URL(e)).protocol)throw TypeError(`URL must be a file URL: received "${e.protocol}"`);return e}var lP=function(e){var t,r;let n;return lI?(n=decodeURIComponent((t=lq(t=e)).pathname.replace(/\//g,"\\").replace(/%(?![0-9A-Fa-f]{2})/g,"%25")).replace(/^\\*([A-Za-z]:)(\\|$)/,"$1\\"),""!==t.hostname&&(n=`\\\\${t.hostname}${n}`),n):decodeURIComponent(lq(e).pathname.replace(/%(?![0-9A-Fa-f]{2})/g,"%25"))},lO=e=>String(e).split(/[/\\]/u).pop();function lR(e,t){if(!t)return;let r=lO(t).toLowerCase();return e.find(({filenames:e})=>null==e?void 0:e.some(e=>e.toLowerCase()===r))??e.find(({extensions:e})=>null==e?void 0:e.some(e=>r.endsWith(e)))}function lM(e,t){if(t){if(String(t).startsWith("file:"))try{t=lP(t)}catch{return}if("string"==typeof t)return e.find(({isSupported:e})=>null==e?void 0:e({filepath:t}))}}var lW=function(e,t){let r=lN(!1,e.plugins).flatMap(e=>e.languages??[]),n=function(e,t){if(t)return e.find(({name:e})=>e.toLowerCase()===t)??e.find(({aliases:e})=>null==e?void 0:e.includes(t))??e.find(({extensions:e})=>null==e?void 0:e.includes(`.${t}`))}(r,t.language)??lR(r,t.physicalFile)??lR(r,t.file)??lM(r,t.physicalFile)??lM(r,t.file)??void t.physicalFile;return null==n?void 0:n.parsers[0]},l$={key:e=>/^[$_a-zA-Z][$_a-zA-Z0-9]*$/.test(e)?e:JSON.stringify(e),value(e){if(null===e||"object"!=typeof e)return JSON.stringify(e);if(Array.isArray(e))return`[${e.map(e=>l$.value(e)).join(", ")}]`;let t=Object.keys(e);return 0===t.length?"{}":`{ ${t.map(t=>`${l$.key(t)}: ${l$.value(e[t])}`).join(", ")} }`},pair:({key:e,value:t})=>l$.value({[e]:t})},lj=new Proxy(String,{get:()=>lj}),lH=Symbol.for("vnopts.VALUE_NOT_EXIST"),lV=Symbol.for("vnopts.VALUE_UNCHANGED"),lU=" ".repeat(2);function lz(e,t,r,n){return`Invalid ${lj.red(n.key(e))} value. Expected ${lj.blue(r)}, but received ${t===lH?lj.gray("nothing"):lj.red(n.value(t))}.`}function lG(e,t){if(1===e.length)return e[0];let[r,n]=e,[i,a]=e.map(e=>e.split(`
`,1)[0].length);return i>t&&i>a?n:r}var lJ=[],lK=[],lQ=(e,t,{descriptor:r,logger:n,schemas:i})=>{let a=[`Ignored unknown option ${lj.yellow(r.pair({key:e,value:t}))}.`],s=Object.keys(i).sort().find(t=>3>function(e,t){if(e===t)return 0;let r=e;e.length>t.length&&(e=t,t=r);let n=e.length,i=t.length;for(;n>0&&e.charCodeAt(~-n)===t.charCodeAt(~-i);)n--,i--;let a=0;for(;a<n&&e.charCodeAt(a)===t.charCodeAt(a);)a++;if(n-=a,i-=a,0===n)return i;let s,o,l,u,c=0,h=0;for(;c<n;)lK[c]=e.charCodeAt(a+c),lJ[c]=++c;for(;h<i;)for(s=t.charCodeAt(a+h),l=h++,o=h,c=0;c<n;c++)u=s===lK[c]?l:l+1,l=lJ[c],o=lJ[c]=l>o?u>o?o+1:u:u>l?l+1:u;return o}(e,t));s&&a.push(`Did you mean ${lj.blue(r.key(s))}?`),n.warn(a.join(" "))},lZ=["default","expected","validate","deprecated","forward","redirect","overlap","preprocess","postprocess"],lX=class{static create(e){let t=new this(e),r=Object.create(t);for(let n of lZ)n in e&&(r[n]=function(e,t,r){return"function"==typeof e?(...n)=>e(...n.slice(0,r-1),t,...n.slice(r-1)):()=>e}(e[n],t,lX.prototype[n].length));return r}constructor(e){this.name=e.name}default(e){}expected(e){return"nothing"}validate(e,t){return!1}deprecated(e,t){return!1}forward(e,t){}redirect(e,t){}overlap(e,t,r){return e}preprocess(e,t){return e}postprocess(e,t){return lV}},lY=class extends lX{constructor(e){super(e),this._sourceName=e.sourceName}expected(e){return e.schemas[this._sourceName].expected(e)}validate(e,t){return t.schemas[this._sourceName].validate(e,t)}redirect(e,t){return this._sourceName}},l0=class extends lX{expected(){return"anything"}validate(){return!0}},l1=class extends lX{constructor({valueSchema:e,name:t=e.name,...r}){super({...r,name:t}),this._valueSchema=e}expected(e){let{text:t,list:r}=e.normalizeExpectedResult(this._valueSchema.expected(e));return{text:t&&`an array of ${t}`,list:r&&{title:"an array of the following values",values:[{list:r}]}}}validate(e,t){if(!Array.isArray(e))return!1;let r=[];for(let n of e){let e=t.normalizeValidateResult(this._valueSchema.validate(n,t),n);!0!==e&&r.push(e.value)}return 0===r.length||{value:r}}deprecated(e,t){let r=[];for(let n of e){let e=t.normalizeDeprecatedResult(this._valueSchema.deprecated(n,t),n);!1!==e&&r.push(...e.map(({value:e})=>({value:[e]})))}return r}forward(e,t){let r=[];for(let n of e){let e=t.normalizeForwardResult(this._valueSchema.forward(n,t),n);r.push(...e.map(l2))}return r}redirect(e,t){let r=[],n=[];for(let i of e){let e=t.normalizeRedirectResult(this._valueSchema.redirect(i,t),i);"remain"in e&&r.push(e.remain),n.push(...e.redirect.map(l2))}return 0===r.length?{redirect:n}:{redirect:n,remain:r}}overlap(e,t){return e.concat(t)}};function l2({from:e,to:t}){return{from:[e],to:t}}var l3=class extends lX{expected(){return"true or false"}validate(e){return"boolean"==typeof e}};function l8(e,t){if(e===t)return 0;let r=typeof e,n=typeof t,i=["undefined","object","boolean","number","string"];return r!==n?i.indexOf(r)-i.indexOf(n):"string"!==r?Number(e)-Number(t):e.localeCompare(t)}function l6(e){return void 0===e?{}:e}function l5(e){if("string"==typeof e)return{text:e};let{text:t,list:r}=e;return function(e,t){if(!e)throw Error(t)}((t||r)!==void 0,"Unexpected `expected` result, there should be at least one field."),r?{text:t,list:{title:r.title,values:r.values.map(l5)}}:{text:t}}function l7(e,t){return!0===e||(!1===e?{value:t}:e)}function l9(e,t,r=!1){return!1!==e&&(!0===e?!!r||[{value:t}]:"value"in e?[e]:0!==e.length&&e)}function l4(e,t){return"string"==typeof e||"key"in e?{from:t,to:e}:"from"in e?{from:e.from,to:e.to}:{from:t,to:e.to}}function ue(e,t){return void 0===e?[]:Array.isArray(e)?e.map(e=>l4(e,t)):[l4(e,t)]}function ut(e,t){let r=ue("object"==typeof e&&"redirect"in e?e.redirect:e,t);return 0===r.length?{remain:t,redirect:r}:"object"==typeof e&&"remain"in e?{remain:e.remain,redirect:r}:{redirect:r}}var ur=class extends lX{constructor(e){super(e),this._choices=function(e,t){let r=new Map;for(let n of e){let e=n[t];if(r.has(e))throw Error(`Duplicate ${t} ${JSON.stringify(e)}`);r.set(e,n)}return r}(e.choices.map(e=>e&&"object"==typeof e?e:{value:e}),"value")}expected({descriptor:e}){let t=Array.from(this._choices.keys()).map(e=>this._choices.get(e)).filter(({hidden:e})=>!e).map(e=>e.value).sort(l8).map(e.value),r=t.slice(0,-2),n=t.slice(-2);return{text:r.concat(n.join(" or ")).join(", "),list:{title:"one of the following values",values:t}}}validate(e){return this._choices.has(e)}deprecated(e){let t=this._choices.get(e);return!!t&&!!t.deprecated&&{value:e}}forward(e){let t=this._choices.get(e);return t?t.forward:void 0}redirect(e){let t=this._choices.get(e);return t?t.redirect:void 0}},un=class extends lX{expected(){return"a number"}validate(e,t){return"number"==typeof e}},ui=class extends un{expected(){return"an integer"}validate(e,t){return!0===t.normalizeValidateResult(super.validate(e,t),e)&&e===Math.floor(e)}},ua=class extends lX{expected(){return"a string"}validate(e){return"string"==typeof e}},us=(e,t,r)=>{let{text:n,list:i}=r.normalizeExpectedResult(r.schemas[e].expected(r)),a=[];return n&&a.push(lz(e,t,n,r.descriptor)),i&&a.push([lz(e,t,i.title,r.descriptor)].concat(i.values.map(e=>(function e({text:t,list:r},n){let i=[];return t&&i.push(`- ${lj.blue(t)}`),r&&i.push([`- ${lj.blue(r.title)}:`].concat(r.values.map(t=>e(t,n-lU.length).replace(/^|\n/g,`$&${lU}`))).join(`
`)),lG(i,n)})(e,r.loggerPrintWidth))).join(`
`)),lG(a,r.loggerPrintWidth)},uo=(e,t,{descriptor:r})=>{let n=[`${lj.yellow("string"==typeof e?r.key(e):r.pair(e))} is deprecated`];return t&&n.push(`we now treat it as ${lj.blue("string"==typeof t?r.key(t):r.pair(t))}`),n.join("; ")+"."},ul=class{constructor(e,t){let{logger:r=console,loggerPrintWidth:n=80,descriptor:i=l$,unknown:a=lQ,invalid:s=us,deprecated:o=uo,missing:l=()=>!1,required:u=()=>!1,preprocess:c=e=>e,postprocess:h=()=>lV}=t||{};this._utils={descriptor:i,logger:r||{warn:()=>{}},loggerPrintWidth:n,schemas:function(e,t){let r=Object.create(null);for(let n of e){let e=n[t];if(r[e])throw Error(`Duplicate ${t} ${JSON.stringify(e)}`);r[e]=n}return r}(e,"name"),normalizeDefaultResult:l6,normalizeExpectedResult:l5,normalizeDeprecatedResult:l9,normalizeForwardResult:ue,normalizeRedirectResult:ut,normalizeValidateResult:l7},this._unknownHandler=a,this._invalidHandler=(...e)=>{let t=s(...e);return"string"==typeof t?Error(t):t},this._deprecatedHandler=o,this._identifyMissing=(e,t)=>!(e in t)||l(e,t),this._identifyRequired=u,this._preprocess=c,this._postprocess=h,this.cleanHistory()}cleanHistory(){let e;this._hasDeprecationWarned=(e=Object.create(null),t=>{let r=JSON.stringify(t);return!!e[r]||(e[r]=!0,!1)})}normalize(e){let t={},r=[this._preprocess(e,this._utils)],n=()=>{for(;0!==r.length;){let e=r.shift(),n=this._applyNormalization(e,t);r.push(...n)}};for(let e of(n(),Object.keys(this._utils.schemas))){let n=this._utils.schemas[e];if(!(e in t)){let t=l6(n.default(this._utils));"value"in t&&r.push({[e]:t.value})}}for(let e of(n(),Object.keys(this._utils.schemas))){if(!(e in t))continue;let r=this._utils.schemas[e],n=t[e],i=r.postprocess(n,this._utils);i!==lV&&(this._applyValidation(i,e,r),t[e]=i)}return this._applyPostprocess(t),this._applyRequiredCheck(t),t}_applyNormalization(e,t){let r=[],{knownKeys:n,unknownKeys:i}=this._partitionOptionKeys(e);for(let i of n){let n=this._utils.schemas[i],a=n.preprocess(e[i],this._utils);this._applyValidation(a,i,n);let s=({from:e,to:t})=>{r.push("string"==typeof t?{[t]:e}:{[t.key]:t.value})},o=({value:e,redirectTo:t})=>{let r=l9(n.deprecated(e,this._utils),a,!0);if(!1!==r)if(!0===r)this._hasDeprecationWarned(i)||this._utils.logger.warn(this._deprecatedHandler(i,t,this._utils));else for(let{value:e}of r){let r={key:i,value:e};if(!this._hasDeprecationWarned(r)){let n="string"==typeof t?{key:t,value:e}:t;this._utils.logger.warn(this._deprecatedHandler(r,n,this._utils))}}};ue(n.forward(a,this._utils),a).forEach(s);let l=ut(n.redirect(a,this._utils),a);if(l.redirect.forEach(s),"remain"in l){let e=l.remain;t[i]=i in t?n.overlap(t[i],e,this._utils):e,o({value:e})}for(let{from:e,to:t}of l.redirect)o({value:e,redirectTo:t})}for(let n of i){let i=e[n];this._applyUnknownHandler(n,i,t,(e,t)=>{r.push({[e]:t})})}return r}_applyRequiredCheck(e){for(let t of Object.keys(this._utils.schemas))if(this._identifyMissing(t,e)&&this._identifyRequired(t))throw this._invalidHandler(t,lH,this._utils)}_partitionOptionKeys(e){let[t,r]=function(e,t){let r=[],n=[];for(let i of e)t(i)?r.push(i):n.push(i);return[r,n]}(Object.keys(e).filter(t=>!this._identifyMissing(t,e)),e=>e in this._utils.schemas);return{knownKeys:t,unknownKeys:r}}_applyValidation(e,t,r){let n=l7(r.validate(e,this._utils),e);if(!0!==n)throw this._invalidHandler(t,n.value,this._utils)}_applyUnknownHandler(e,t,r,n){let i=this._unknownHandler(e,t,this._utils);if(i)for(let e of Object.keys(i)){if(this._identifyMissing(e,i))continue;let t=i[e];e in this._utils.schemas?n(e,t):r[e]=t}}_applyPostprocess(e){let t=this._postprocess(e,this._utils);if(t!==lV){if(t.delete)for(let r of t.delete)delete e[r];if(t.override){let{knownKeys:r,unknownKeys:n}=this._partitionOptionKeys(t.override);for(let n of r){let r=t.override[n];this._applyValidation(r,n,this._utils.schemas[n]),e[n]=r}for(let r of n){let n=t.override[r];this._applyUnknownHandler(r,n,e,(t,r)=>{let n=this._utils.schemas[t];this._applyValidation(r,t,n),e[t]=r})}}}}},uu=function(e,t,{logger:r=!1,isCLI:i=!1,passThrough:a=!1,FlagSchema:s,descriptor:o}={}){if(i){if(!s)throw Error("'FlagSchema' option is required.");if(!o)throw Error("'descriptor' option is required.")}else o=l$;let l=a?Array.isArray(a)?(e,t)=>a.includes(e)?{[e]:t}:void 0:(e,t)=>({[e]:t}):(e,t,r)=>{let{_:n,...i}=r.schemas;return lQ(e,t,{...r,schemas:i})},u=new ul(function(e,{isCLI:t,FlagSchema:r}){let n=[];for(let i of(t&&n.push(l0.create({name:"_"})),e))n.push(function(e,{isCLI:t,optionInfos:r,FlagSchema:n}){let{name:i}=e,a={name:i},s,o={};switch(e.type){case"int":s=ui,t&&(a.preprocess=Number);break;case"string":case"path":s=ua;break;case"choice":s=ur,a.choices=e.choices.map(t=>null!=t&&t.redirect?{...t,redirect:{to:{key:e.name,value:t.redirect}}}:t);break;case"boolean":s=l3;break;case"flag":s=n,a.flags=r.flatMap(e=>[e.alias,e.description&&e.name,e.oppositeDescription&&`no-${e.name}`].filter(Boolean));break;default:throw Error(`Unexpected type ${e.type}`)}if(e.exception?a.validate=(t,r,n)=>e.exception(t)||r.validate(t,n):a.validate=(e,t,r)=>void 0===e||t.validate(e,r),e.redirect&&(o.redirect=t=>t?{to:"string"==typeof e.redirect?e.redirect:{key:e.redirect.option,value:e.redirect.value}}:void 0),e.deprecated&&(o.deprecated=!0),t&&!e.array){let e=a.preprocess||(e=>e);a.preprocess=(t,r,n)=>r.preprocess(e(Array.isArray(t)?og(!1,t,-1):t),n)}return e.array?l1.create({...t?{preprocess:e=>Array.isArray(e)?e:[e]}:{},...o,valueSchema:s.create(a)}):s.create({...a,...o})}(i,{isCLI:t,optionInfos:e,FlagSchema:r})),i.alias&&t&&n.push(lY.create({name:i.alias,sourceName:i.name}));return n}(t,{isCLI:i,FlagSchema:s}),{logger:r,unknown:l,descriptor:o}),c=!1!==r;c&&n&&(u._hasDeprecationWarned=n);let h=u.normalize(e);return c&&(n=u._hasDeprecationWarned),h},uc=(e,t,r)=>{if(!(e&&null==t)){if(t.findLast)return t.findLast(r);for(let e=t.length-1;e>=0;e--){let n=t[e];if(r(n,e,t))return n}}};function uh(e,t){if(!t)throw Error("parserName is required.");let r=uc(!1,e,e=>e.parsers&&Object.prototype.hasOwnProperty.call(e.parsers,t));if(r)return r;let n=`Couldn't resolve parser "${t}".`;throw new lk(n+=" Plugins must be explicitly added to the standalone bundle.")}function ud({plugins:e,parser:t}){return up(uh(e,t),t)}function up(e,t){let r=e.parsers[t];return"function"==typeof r?r():r}var uf={astFormat:"estree",printer:{},originalText:void 0,locStart:null,locEnd:null};async function um(e,t={}){var r,n;let i,a={...e};if(!a.parser)if(a.filepath){if(a.parser=lW(a,{physicalFile:a.filepath}),!a.parser)throw new lE(`No parser could be inferred for file "${a.filepath}".`)}else throw new lE("No parser and no file path given, couldn't infer a parser.");let s=lF({plugins:e.plugins,showDeprecated:!0}).options,o={...uf,...Object.fromEntries(s.filter(e=>void 0!==e.default).map(e=>[e.name,e.default]))},l=uh(a.plugins,a.parser),u=await up(l,a.parser);a.astFormat=u.astFormat,a.locEnd=u.locEnd,a.locStart=u.locStart;let c=null!=(r=l.printers)&&r[u.astFormat]?l:function(e,t){if(!t)throw Error("astFormat is required.");let r=uc(!1,e,e=>e.printers&&Object.prototype.hasOwnProperty.call(e.printers,t));if(r)return r;let n=`Couldn't find plugin for AST format "${t}".`;throw new lk(n+=" Plugins must be explicitly added to the standalone bundle.")}(a.plugins,u.astFormat);a.printer=await (n=u.astFormat,"function"==typeof(i=c.printers[n])?i():i);let h=c.defaultOptions?Object.fromEntries(Object.entries(c.defaultOptions).filter(([,e])=>void 0!==e)):{};for(let[e,t]of Object.entries({...o,...h}))(null===a[e]||void 0===a[e])&&(a[e]=t);return"json"===a.parser&&(a.trailingComma="none"),uu(a,s,{passThrough:Object.keys(uf),...t})}var ug=((e,t,r)=>(r=null!=e?sz(sQ(e)):{},s0(!t&&e&&e.__esModule?r:sG(r,"default",{value:e,enumerable:!0}),e)))(s8(),1);async function uD(e,t){let r,n=await ud(t),i=n.preprocess?n.preprocess(e,t):e;t.originalText=i;try{r=await n.parse(i,t,t)}catch(t){!function(e,t){let{loc:r}=e;if(r){let n=(0,ug.codeFrameColumns)(t,r,{highlightCode:!0});throw e.message+=`
`+n,e.codeFrame=n,e}throw e}(t,e)}return{text:i,ast:r}}async function uy(e,t,r,n,i){let{embeddedLanguageFormatting:a,printer:{embed:s,hasPrettierIgnore:o=()=>!1,getVisitorKeys:l}}=r;if(!s||"auto"!==a)return;if(s.length>2)throw Error("printer.embed has too many parameters. The API changed in Prettier v3. Please update your plugin. See https://prettier.io/docs/plugins#optional-embed");let u=lh(s.getVisitorKeys??l),c=[];!function t(){let{node:n}=e;if(null===n||"object"!=typeof n||o(e))return;for(let r of u(n))Array.isArray(n[r])?e.each(t,r):e.call(t,r);let a=s(e,r);if(a){if("function"==typeof a)return void c.push({print:a,node:n,pathStack:[...e.stack]});i.set(n,a)}}();let h=e.stack;for(let{print:n,node:a,pathStack:s}of c)try{e.stack=s;let o=await n(d,t,e,r);o&&i.set(a,o)}catch(e){if(globalThis.PRETTIER_DEBUG)throw e}function d(e,t){return ub(e,t,r,n)}e.stack=h}async function ub(e,t,r,n){let i=await um({...r,...t,parentParser:r.parser,originalText:e,cursorOffset:void 0,rangeStart:void 0,rangeEnd:void 0},{passThrough:!0}),{ast:a}=await uD(e,i);return oT(await n(a,i))}var uC=function(e,t){let{originalText:r,[Symbol.for("comments")]:n,locStart:i,locEnd:a,[Symbol.for("printedComments")]:s}=t,{node:o}=e,l=i(o),u=a(o);for(let e of n)i(e)>=l&&a(e)<=u&&s.add(e);return r.slice(l,u)};async function uv(e,t){({ast:e}=await uw(e,t));let r=new Map,n=new o4(e),i=()=>{},a=new Map;await uy(n,o,t,uv,a);let s=await uS(n,t,o,void 0,a);if(function(e){let{[Symbol.for("comments")]:t,[Symbol.for("printedComments")]:r}=e;for(let e of t){if(!e.printed&&!r.has(e))throw Error('Comment "'+e.value.trim()+'" was not printed. Please report this error!');delete e.printed}}(t),t.cursorOffset>=0){if(t.nodeAfterCursor&&!t.nodeBeforeCursor)return[oH,s];if(t.nodeBeforeCursor&&!t.nodeAfterCursor)return[s,oH]}return s;function o(e,t){return void 0===e||e===n?l(t):Array.isArray(e)?n.call(()=>l(t),...e):n.call(()=>l(t),e)}function l(e){i(n);let s=n.node;if(null==s)return"";let l=s&&"object"==typeof s&&void 0===e;if(l&&r.has(s))return r.get(s);let u=uS(n,t,o,e,a);return l&&r.set(s,u),u}}function uS(e,t,r,n,i){var a;let{node:s}=e,{printer:o}=t,l;switch(l=null!=(a=o.hasPrettierIgnore)&&a.call(o,e)?uC(e,t):i.has(s)?i.get(s):o.print(e,t,r,n),s){case t.cursorNode:l=oL(l,e=>[oH,e,oH]);break;case t.nodeBeforeCursor:l=oL(l,e=>[e,oH]);break;case t.nodeAfterCursor:l=oL(l,e=>[oH,e])}return!o.printComment||o.willPrintOwnComments&&o.willPrintOwnComments(e,t)||(l=function(e,t,r){let{leading:n,trailing:i}=function(e,t){let r=e.node;if(!r)return{};let n=t[Symbol.for("printedComments")];if(0===(r.comments||[]).filter(e=>!n.has(e)).length)return{leading:"",trailing:""};let i=[],a=[],s;return e.each(()=>{let r=e.node;if(null!=n&&n.has(r))return;let{leading:o,trailing:l}=r;o?i.push(function(e,t){var r;let n=e.node,i=[lw(e,t)],{printer:a,originalText:s,locStart:o,locEnd:l}=t;if(null==(r=a.isBlockComment)?void 0:r.call(a,n)){let e=ll(s,l(n))?ll(s,o(n),{backwards:!0})?o$:oW:" ";i.push(e)}else i.push(o$);let u=lo(s,li(s,l(n)));return!1!==u&&ll(s,u)&&i.push(o$),i}(e,t)):l&&(s=function(e,t,r){var n;let i=e.node,a=lw(e,t),{printer:s,originalText:o,locStart:l}=t,u=null==(n=s.isBlockComment)?void 0:n.call(s,i);return null!=r&&r.hasLineSuffix&&!(null!=r&&r.isBlock)||ll(o,l(i),{backwards:!0})?{doc:oP([o$,lS(o,l(i))?o$:"",a]),isBlock:u,hasLineSuffix:!0}:!u||null!=r&&r.hasLineSuffix?{doc:[oP([" ",a]),oO],isBlock:u,hasLineSuffix:!0}:{doc:[" ",a],isBlock:u,hasLineSuffix:!1}}(e,t,s),a.push(s.doc))},"comments"),{leading:i,trailing:a}}(e,r);return n||i?oL(t,e=>[n,e,i]):t}(e,l,t)),l}async function uw(e,t){let r=e.comments??[];t[Symbol.for("comments")]=r,t[Symbol.for("printedComments")]=new Set,function(e,t){let{comments:r}=e;if(delete e.comments,!(Array.isArray(r)&&r.length>0)||!t.printer.canAttachComment)return;let n=[],{printer:{experimentalFeatures:{avoidAstMutation:i=!1}={},handleComments:a={}},originalText:s}=t,{ownLine:o=ly,endOfLine:l=ly,remaining:u=ly}=a,c=r.map((n,i)=>({...function e(t,r,n,i){let{locStart:a,locEnd:s}=n,o=a(r),l=s(r),u=lD(t,n),c,h,d=0,p=u.length;for(;d<p;){let t=d+p>>1,i=u[t],f=a(i),m=s(i);if(f<=o&&l<=m)return e(i,r,n,i);if(m<=o){c=i,d=t+1;continue}if(l<=f){h=i,p=t;continue}throw Error("Comment location overlaps with node location")}if((null==i?void 0:i.type)==="TemplateLiteral"){let{quasis:e}=i,t=lv(e,r,n);c&&lv(e,c,n)!==t&&(c=null),h&&lv(e,h,n)!==t&&(h=null)}return{enclosingNode:i,precedingNode:c,followingNode:h}}(e,n,t),comment:n,text:s,options:t,ast:e,isLastComment:r.length-1===i}));for(let[e,t]of c.entries()){let{comment:r,precedingNode:a,enclosingNode:s,followingNode:h,text:d,options:p,ast:f,isLastComment:m}=t,g;if(i?g=[t]:(r.enclosingNode=s,r.precedingNode=a,r.followingNode=h,g=[r,d,p,f,m]),function(e,t,r,n){let{comment:i,precedingNode:a}=r[n],{locStart:s,locEnd:o}=t,l=s(i);if(a)for(let t=n-1;t>=0;t--){let{comment:n,precedingNode:i}=r[t];if(i!==a||!lb(e.slice(o(n),l)))break;l=s(n)}return ll(e,l,{backwards:!0})}(d,p,c,e))r.placement="ownLine",o(...g)||(h?lp(h,r):a?lm(a,r):s?lf(s,r):lf(f,r));else if(function(e,t,r,n){let{comment:i,followingNode:a}=r[n],{locStart:s,locEnd:o}=t,l=o(i);if(a)for(let t=n+1;t<r.length;t++){let{comment:n,followingNode:i}=r[t];if(i!==a||!lb(e.slice(l,s(n))))break;l=o(n)}return ll(e,l)}(d,p,c,e))r.placement="endOfLine",l(...g)||(a?lm(a,r):h?lp(h,r):s?lf(s,r):lf(f,r));else if(r.placement="remaining",!u(...g))if(a&&h){let e=n.length;e>0&&n[e-1].followingNode!==h&&lC(n,p),n.push(t)}else a?lm(a,r):h?lp(h,r):s?lf(s,r):lf(f,r)}if(lC(n,t),!i)for(let e of r)delete e.precedingNode,delete e.enclosingNode,delete e.followingNode}(e,t);let{printer:{preprocess:n}}=t;return{ast:e=n?await n(e,t):e,comments:r}}var uk=function(e,t){let{cursorOffset:r,locStart:n,locEnd:i}=t,a=lh(t.printer.getVisitorKeys),s=e,o=[e];for(let t of function*(e,t){let r=[e];for(let e=0;e<r.length;e++)for(let n of lt(r[e],t))yield n,r.push(n)}(e,{getVisitorKeys:a,filter:e=>n(e)<=r&&i(e)>=r}))o.push(t),s=t;if(lt(s,{getVisitorKeys:a}).next().done)return{cursorNode:s};let l,u,c=-1,h=Number.POSITIVE_INFINITY;for(;o.length>0&&(void 0===l||void 0===u);){s=o.pop();let e=void 0!==l,t=void 0!==u;for(let o of lt(s,{getVisitorKeys:a})){if(!e){let e=i(o);e<=r&&e>c&&(l=o,c=e)}if(!t){let e=n(o);e>=r&&e<h&&(u=o,h=e)}}}return{nodeBeforeCursor:l,nodeAfterCursor:u}},uE=function(e,t){let{printer:{massageAstNode:r,getVisitorKeys:n}}=t;if(!r)return e;let i=lh(n),a=r.ignoredProperties??new Set;return function e(t,n){if(null===t||"object"!=typeof t)return t;if(Array.isArray(t))return t.map(t=>e(t,n)).filter(Boolean);let s={},o=new Set(i(t));for(let r in t)!Object.prototype.hasOwnProperty.call(t,r)||a.has(r)||(o.has(r)?s[r]=e(t[r],t):s[r]=t[r]);let l=r(t,s,n);if(null!==l)return l??s}(e)},ux=(e,t,r)=>{if(!(e&&null==t)){if(t.findLastIndex)return t.findLastIndex(r);for(let e=t.length-1;e>=0;e--)if(r(t[e],e,t))return e;return -1}},uF=({parser:e})=>"json"===e||"json5"===e||"jsonc"===e||"json-stringify"===e;function u_(e){let t=ux(!1,e,e=>"Program"!==e.type&&"File"!==e.type);return -1===t?e:e.slice(0,t+1)}function uT(e,t,r,n,i=[],a){let{locStart:s,locEnd:o}=r,l=s(e),u=o(e);if(!(t>u||t<l||"rangeEnd"===a&&t===l||"rangeStart"===a&&t===u)){for(let s of lD(e,r)){let o=uT(s,t,r,n,[e,...i],a);if(o)return o}if(!n||n(e,i[0]))return{node:e,parentNodes:i}}}var uA=new Set(["JsonRoot","ObjectExpression","ArrayExpression","StringLiteral","NumericLiteral","BooleanLiteral","NullLiteral","UnaryExpression","TemplateLiteral"]),uL=new Set(["OperationDefinition","FragmentDefinition","VariableDefinition","TypeExtensionDefinition","ObjectTypeDefinition","FieldDefinition","DirectiveDefinition","EnumTypeDefinition","EnumValueDefinition","InputValueDefinition","InputObjectTypeDefinition","SchemaDefinition","OperationTypeDefinition","InterfaceTypeDefinition","UnionTypeDefinition","ScalarTypeDefinition"]);function uB(e,t,r){if(!t)return!1;switch(e.parser){case"flow":case"hermes":case"babel":case"babel-flow":case"babel-ts":case"typescript":case"acorn":case"espree":case"meriyah":case"oxc":case"oxc-ts":case"__babel_estree":var n;return n=t.type,"DeclareExportDeclaration"!==(null==r?void 0:r.type)&&"TypeParameterDeclaration"!==n&&("Directive"===n||"TypeAlias"===n||"TSExportAssignment"===n||n.startsWith("Declare")||n.startsWith("TSDeclare")||n.endsWith("Statement")||n.endsWith("Declaration"));case"json":case"json5":case"jsonc":case"json-stringify":return uA.has(t.type);case"graphql":return uL.has(t.kind);case"vue":return"root"!==t.tag}return!1}var uN=Symbol("cursor");async function uI(e,t,r=0){if(!e||0===e.trim().length)return{formatted:"",cursorOffset:-1,comments:[]};let{ast:n,text:i}=await uD(e,t);t.cursorOffset>=0&&(t={...t,...uk(n,t)});let a=await uv(n,t,r);r>0&&(a=oU([o$,a],r,t.tabWidth));let s=o8(a,t);if(r>0){let e=s.formatted.trim();void 0!==s.cursorNodeStart&&(s.cursorNodeStart-=s.formatted.indexOf(e),s.cursorNodeStart<0&&(s.cursorNodeStart=0,s.cursorNodeText=s.cursorNodeText.trimStart()),s.cursorNodeStart+s.cursorNodeText.length>e.length&&(s.cursorNodeText=s.cursorNodeText.trimEnd())),s.formatted=e+s9(t.endOfLine)}let o=t[Symbol.for("comments")];if(t.cursorOffset>=0){let e,r,n,a;if((t.cursorNode||t.nodeBeforeCursor||t.nodeAfterCursor)&&s.cursorNodeText)if(n=s.cursorNodeStart,a=s.cursorNodeText,t.cursorNode)e=t.locStart(t.cursorNode),r=i.slice(e,t.locEnd(t.cursorNode));else{if(!t.nodeBeforeCursor&&!t.nodeAfterCursor)throw Error("Cursor location must contain at least one of cursorNode, nodeBeforeCursor, nodeAfterCursor");e=t.nodeBeforeCursor?t.locEnd(t.nodeBeforeCursor):0;let n=t.nodeAfterCursor?t.locStart(t.nodeAfterCursor):i.length;r=i.slice(e,n)}else e=0,r=i,n=0,a=s.formatted;let l=t.cursorOffset-e;if(r===a)return{formatted:s.formatted,cursorOffset:n+l,comments:o};let u=r.split("");u.splice(l,0,uN);let c=a.split(""),h=s7.diff(u,c,void 0),d=n;for(let e of h)if(e.removed){if(e.value.includes(uN))break}else d+=e.count;return{formatted:s.formatted,cursorOffset:d,comments:o}}return{formatted:s.formatted,cursorOffset:-1,comments:o}}async function uq(e,t){let{ast:r,text:n}=await uD(e,t),{rangeStart:i,rangeEnd:a}=function(e,t,r){let n,i,{rangeStart:a,rangeEnd:s,locStart:o,locEnd:l}=t;le.ok(s>a);let u=e.slice(a,s).search(/\S/u),c=-1===u;if(!c)for(a+=u;s>a&&!/\S/u.test(e[s-1]);--s);let h=uT(r,a,t,(e,r)=>uB(t,e,r),[],"rangeStart"),d=c?h:uT(r,s,t,e=>uB(t,e),[],"rangeEnd");if(!h||!d)return{rangeStart:0,rangeEnd:0};if(uF(t)){let e,t,r=(e=[h.node,...h.parentNodes],t=new Set([d.node,...d.parentNodes]),e.find(e=>uA.has(e.type)&&t.has(e)));n=r,i=r}else({startNode:n,endNode:i}=function(e,t,{locStart:r,locEnd:n}){let i=e.node,a=t.node;if(i===a)return{startNode:i,endNode:a};let s=r(e.node);for(let e of u_(t.parentNodes))if(r(e)>=s)a=e;else break;let o=n(t.node);for(let t of u_(e.parentNodes)){if(n(t)<=o)i=t;else break;if(i===a)break}return{startNode:i,endNode:a}}(h,d,t));return{rangeStart:Math.min(o(n),o(i)),rangeEnd:Math.max(l(n),l(i))}}(n,t,r),s=n.slice(i,a),o=Math.min(i,n.lastIndexOf(`
`,i)+1),l=o9(n.slice(o,i).match(/^\s*/u)[0],t.tabWidth),u=await uI(s,{...t,rangeStart:0,rangeEnd:Number.POSITIVE_INFINITY,cursorOffset:t.cursorOffset>i&&t.cursorOffset<=a?t.cursorOffset-i:-1,endOfLine:"lf"},l),c=u.formatted.trimEnd(),{cursorOffset:h}=t;h>a?h+=c.length-s.length:u.cursorOffset>=0&&(h=u.cursorOffset+i);let d=n.slice(0,i)+c+n.slice(a);if("lf"!==t.endOfLine){let e=s9(t.endOfLine);h>=0&&e===`\r
`&&(h+=s4(d.slice(0,h),`
`)),d=s6(!1,d,`
`,e)}return{formatted:d,cursorOffset:h,comments:u.comments}}function uP(e,t,r){return"number"!=typeof t||Number.isNaN(t)||t<0||t>e.length?r:t}function uO(e,t){let{cursorOffset:r,rangeStart:n,rangeEnd:i}=t;return r=uP(e,r,-1),n=uP(e,n,0),i=uP(e,i,e.length),{...t,cursorOffset:r,rangeStart:n,rangeEnd:i}}function uR(e,t){var r;let n,{cursorOffset:i,rangeStart:a,rangeEnd:s,endOfLine:o}=uO(e,t),l="\uFEFF"===e.charAt(0);if(l&&(e=e.slice(1),i--,a--,s--),"auto"===o&&(o=-1!==(n=(r=e).indexOf("\r"))?r.charAt(n+1)===`
`?"crlf":"cr":"lf"),e.includes("\r")){let t=t=>s4(e.slice(0,Math.max(t,0)),`\r
`);i-=t(i),a-=t(a),s-=t(s),e=s6(!1,e,/\r\n?/gu,`
`)}return{hasBOM:l,text:e,options:uO(e,{...t,cursorOffset:i,rangeStart:a,rangeEnd:s,endOfLine:o})}}async function uM(e,t){let r=await ud(t);return!r.hasPragma||r.hasPragma(e)}async function uW(e,t){var r;let n=await ud(t);return null==(r=n.hasIgnorePragma)?void 0:r.call(n,e)}async function u$(e,t){let r,{hasBOM:n,text:i,options:a}=uR(e,await um(t));return a.rangeStart>=a.rangeEnd&&""!==i||a.requirePragma&&!await uM(i,a)||a.checkIgnorePragma&&await uW(i,a)?{formatted:e,cursorOffset:t.cursorOffset,comments:[]}:(a.rangeStart>0||a.rangeEnd<i.length?r=await uq(i,a):(!a.requirePragma&&a.insertPragma&&a.printer.insertPragma&&!await uM(i,a)&&(i=a.printer.insertPragma(i)),r=await uI(i,a)),n&&(r.formatted="\uFEFF"+r.formatted,r.cursorOffset>=0&&r.cursorOffset++),r)}async function uj(e,t,r){let{text:n,options:i}=uR(e,await um(t)),a=await uD(n,i);return r&&(r.preprocessForPrint&&(a.ast=await uw(a.ast,i)),r.massage&&(a.ast=uE(a.ast,i))),a}async function uH(e,t){return t=await um(t),o8(await uv(e,t),t)}async function uV(e,t){let r=function(e){let t=Object.create(null),r=new Set;return function e(t,r,i){var a,s;if("string"==typeof t)return JSON.stringify(t);if(Array.isArray(t)){let r=t.map(e).filter(Boolean);return 1===r.length?r[0]:`[${r.join(", ")}]`}if(t.type===od){let e=(null==(a=null==i?void 0:i[r+1])?void 0:a.type)===of;return t.literal?e?"literalline":"literallineWithoutBreakParent":t.hard?e?"hardline":"hardlineWithoutBreakParent":t.soft?"softline":"line"}if(t.type===of)return(null==(s=null==i?void 0:i[r-1])?void 0:s.type)===od&&i[r-1].hard?void 0:"breakParent";if(t.type===oa)return"trim";if(t.type===on)return"indent("+e(t.contents)+")";if(t.type===oi)return t.n===Number.NEGATIVE_INFINITY?"dedentToRoot("+e(t.contents)+")":t.n<0?"dedent("+e(t.contents)+")":"root"===t.n.type?"markAsRoot("+e(t.contents)+")":"align("+JSON.stringify(t.n)+", "+e(t.contents)+")";if(t.type===ol)return"ifBreak("+e(t.breakContents)+(t.flatContents?", "+e(t.flatContents):"")+(t.groupId?(t.flatContents?"":', ""')+`, { groupId: ${n(t.groupId)} }`:"")+")";if(t.type===ou){let r=[];t.negate&&r.push("negate: true"),t.groupId&&r.push(`groupId: ${n(t.groupId)}`);let i=r.length>0?`, { ${r.join(", ")} }`:"";return`indentIfBreak(${e(t.contents)}${i})`}if(t.type===os){let r=[];t.break&&"propagated"!==t.break&&r.push("shouldBreak: true"),t.id&&r.push(`id: ${n(t.id)}`);let i=r.length>0?`, { ${r.join(", ")} }`:"";return t.expandedStates?`conditionalGroup([${t.expandedStates.map(t=>e(t)).join(",")}]${i})`:`group(${e(t.contents)}${i})`}if(t.type===oo)return`fill([${t.parts.map(t=>e(t)).join(", ")}])`;if(t.type===oc)return"lineSuffix("+e(t.contents)+")";if(t.type===oh)return"lineSuffixBoundary";if(t.type===op)return`label(${JSON.stringify(t.label)}, ${e(t.contents)})`;if(t.type===or)return"cursor";throw Error("Unknown doc type "+t.type)}(function e(t){var r;if(!t)return"";if(Array.isArray(t)){let r=[];for(let n of t)if(Array.isArray(n))r.push(...e(n));else{let t=e(n);""!==t&&r.push(t)}return r}return t.type===ol?{...t,breakContents:e(t.breakContents),flatContents:e(t.flatContents)}:t.type===os?{...t,contents:e(t.contents),expandedStates:null==(r=t.expandedStates)?void 0:r.map(e)}:t.type===oo?{type:"fill",parts:t.parts.map(e)}:t.contents?{...t,contents:e(t.contents)}:t}(e));function n(e){if("symbol"!=typeof e)return JSON.stringify(String(e));if(e in t)return t[e];let n=e.description||"symbol";for(let i=0;;i++){let a=n+(i>0?` #${i}`:"");if(!r.has(a))return r.add(a),t[e]=`Symbol.for(${JSON.stringify(a)})`}}}(e),{formatted:n}=await u$(r,{...t,parser:"__js_expression"});return n}async function uU(e,t){t=await um(t);let{ast:r}=await uD(e,t);return t.cursorOffset>=0&&(t={...t,...uk(r,t)}),uv(r,t)}async function uz(e,t){return o8(e,await um(t))}var uG={};sY(uG,{builders:()=>uJ,printer:()=>uK,utils:()=>uQ});var uJ={join:oV,line:oW,softline:{type:od,soft:!0},hardline:o$,literalline:oj,group:oq,conditionalGroup:function(e,t){return oq(e[0],{...t,expandedStates:e})},fill:function(e){return oB(e),{type:oo,parts:e}},lineSuffix:oP,lineSuffixBoundary:{type:oh},cursor:oH,breakParent:oO,ifBreak:function(e,t="",r={}){return oB(e),""!==t&&oB(t),{type:ol,breakContents:e,flatContents:t,groupId:r.groupId}},trim:{type:oa},indent:oN,indentIfBreak:function(e,t){return oB(e),{type:ou,contents:e,groupId:t.groupId,negate:t.negate}},align:oI,addAlignmentToDoc:oU,markAsRoot:function(e){return oI({type:"root"},e)},dedentToRoot:function(e){return oI(Number.NEGATIVE_INFINITY,e)},dedent:function(e){return oI(-1,e)},hardlineWithoutBreakParent:oR,literallineWithoutBreakParent:oM,label:function(e,t){return oB(t),e?{type:op,label:e,contents:t}:t},concat:e=>e},uK={printDocToString:o8},uQ={willBreak:function(e){return ow(e,ok,!1)},traverseDoc:ov,findInDoc:ow,mapDoc:oS,removeLines:function(e){return oS(e,ox)},stripTrailingHardline:oT,replaceEndOfLine:function(e,t=oj){return oS(e,e=>"string"==typeof e?oV(t,e.split(`
`)):e)},canBreak:function(e){return ow(e,oA,!1)}},uZ="3.6.2",uX={};sY(uX,{addDanglingComment:()=>lf,addLeadingComment:()=>lp,addTrailingComment:()=>lm,getAlignmentSize:()=>o9,getIndentSize:()=>u3,getMaxContinuousCount:()=>u8,getNextNonSpaceNonCommentCharacter:()=>u6,getNextNonSpaceNonCommentCharacterIndex:()=>ce,getPreferredQuote:()=>u5,getStringWidth:()=>oK,hasNewline:()=>ll,hasNewlineInRange:()=>u7,hasSpaces:()=>u9,isNextLineEmpty:()=>cr,isNextLineEmptyAfterIndex:()=>u2,isPreviousLineEmpty:()=>ct,makeString:()=>u4,skip:()=>lr,skipEverythingButNewLine:()=>ls,skipInlineComment:()=>uY,skipNewline:()=>lo,skipSpaces:()=>li,skipToLineEnd:()=>la,skipTrailingComment:()=>u0,skipWhitespace:()=>ln});var uY=function(e,t){if(!1===t)return!1;if("/"===e.charAt(t)&&"*"===e.charAt(t+1)){for(let r=t+2;r<e.length;++r)if("*"===e.charAt(r)&&"/"===e.charAt(r+1))return r+2}return t},u0=function(e,t){return!1!==t&&("/"===e.charAt(t)&&"/"===e.charAt(t+1)?ls(e,t):t)},u1=function(e,t){let r=null,n=t;for(;n!==r;)r=n,n=li(e,n),n=uY(e,n),n=u0(e,n),n=lo(e,n);return n},u2=function(e,t){let r=null,n=t;for(;n!==r;)r=n,n=la(e,n),n=uY(e,n),n=li(e,n);return n=u0(e,n),!1!==(n=lo(e,n))&&ll(e,n)},u3=function(e,t){let r=e.lastIndexOf(`
`);return -1===r?0:o9(e.slice(r+1).match(/^[\t ]*/u)[0],t)},u8=function(e,t){let r=e.match(RegExp(`(${function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(t)})+`,"gu"));return null===r?0:r.reduce((e,r)=>Math.max(e,r.length/t.length),0)},u6=function(e,t){let r=u1(e,t);return!1===r?"":e.charAt(r)},u5=function(e,t){let r=!0===t||"'"===t?"'":'"',n="'"===r?'"':"'",i=0,a=0;for(let t of e)t===r?i++:t===n&&a++;return i>a?n:r},u7=function(e,t,r){for(let n=t;n<r;++n)if(e.charAt(n)===`
`)return!0;return!1},u9=function(e,t,r={}){return li(e,r.backwards?t-1:t,r)!==t},u4=function(e,t,r){let n='"'===t?"'":'"',i=s6(!1,e,/\\(.)|(["'])/gsu,(e,i,a)=>i===n?i:a===t?"\\"+a:a||(r&&/^[^\n\r"'0-7\\bfnrt-vx\u2028\u2029]$/u.test(i)?i:"\\"+i));return t+i+t};function ce(e,t){return 2==arguments.length||"number"==typeof t?u1(e,t):function(e,t,r){return u1(e,r(t))}(...arguments)}function ct(e,t){return 2==arguments.length||"number"==typeof t?lS(e,t):function(e,t,r){return lS(e,r(t))}(...arguments)}function cr(e,t){return 2==arguments.length||"number"==typeof t?u2(e,t):function(e,t,r){return u2(e,r(t))}(...arguments)}function cn(e,t=1){return async(...r)=>{let n=r[t]??{},i=n.plugins??[];return r[t]={...n,plugins:Array.isArray(i)?i:Object.values(i)},e(...r)}}var ci=cn(u$);async function ca(e,t){let{formatted:r}=await ci(e,{...t,cursorOffset:-1});return r}async function cs(e,t){return await ca(e,t)===e}var co=cn(lF,0),cl={parse:cn(uj),formatAST:cn(uH),formatDoc:cn(uV),printToDoc:cn(uU),printDocToString:cn(uz)},cu=r(57075),cc=r(87276),ch=Object.defineProperty,cd=Object.defineProperties,cp=Object.getOwnPropertyDescriptors,cf=Object.getOwnPropertySymbols,cm=Object.prototype.hasOwnProperty,cg=Object.prototype.propertyIsEnumerable,cD=(e,t,r)=>t in e?ch(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r,cy=(e,t)=>{for(var r in t||(t={}))cm.call(t,r)&&cD(e,r,t[r]);if(cf)for(var r of cf(t))cg.call(t,r)&&cD(e,r,t[r]);return e},cb=(e,t)=>cd(e,cp(t)),cC=(e,t,r)=>new Promise((n,i)=>{var a=e=>{try{o(r.next(e))}catch(e){i(e)}},s=e=>{try{o(r.throw(e))}catch(e){i(e)}},o=e=>e.done?n(e.value):Promise.resolve(e.value).then(a,s);o((r=r.apply(e,t)).next())}),cv=[{selector:"img",format:"skip"},{selector:"[data-skip-in-text=true]",format:"skip"},{selector:"a",options:{linkBrackets:!1}}],cS=cy({},i);if(cS.printers){let e=cS.printers.html.print;cS.printers.html.print=(t,r,n,i)=>{let a=t.getNode(),s=e(t,r,n,i);return"ieConditionalComment"===a.type?function e(t,r){if(Array.isArray(t))return t.map(t=>e(t,r));if("object"==typeof t){if("group"===t.type)return cb(cy({},t),{contents:e(t.contents,r),expandedStates:e(t.expandedStates,r)});if("contents"in t)return cb(cy({},t),{contents:e(t.contents,r)});if("parts"in t)return cb(cy({},t),{parts:e(t.parts,r)});if("if-break"===t.type)return cb(cy({},t),{breakContents:e(t.breakContents,r),flatContents:e(t.flatContents,r)})}return r(t)}(s,e=>"object"==typeof e&&"line"===e.type?e.soft?"":" ":e):s}}var cw={endOfLine:"lf",tabWidth:2,plugins:[cS],bracketSameLine:!0,parser:"html"},ck=(e,t={})=>ca(e.replaceAll("\0",""),cy(cy({},cw),t)),cE=new TextDecoder("utf-8"),cx=e=>cC(void 0,null,function*(){let t="";if("pipeTo"in e){let r=new WritableStream({write(e){t+=cE.decode(e)}});yield e.pipeTo(r)}else{let r=new cu.Writable({write(e,r,n){t+=cE.decode(e),n()}});e.pipe(r),yield new Promise((e,t)=>{r.on("error",t),r.on("close",()=>{e()})})}return t}),cF=(e,t)=>cC(void 0,null,function*(){let n,i=(0,cc.jsx)(rh.Suspense,{children:e}),a=yield r.e(9469).then(r.t.bind(r,89469,19)).then(e=>e.default);if(Object.hasOwn(a,"renderToReadableStream")?n=yield cx((yield a.renderToReadableStream(i))):yield new Promise((e,t)=>{let r=a.renderToPipeableStream(i,{onAllReady(){return cC(this,null,function*(){n=yield cx(r),e()})},onError(e){t(e)}})}),null==t?void 0:t.plainText)return function(e,t={},r){return(function(e={}){return(e=tP(ro,e,{arrayMerge:ru,customMerge:e=>"selectors"===e?rc:void 0})).formatters=Object.assign({},t9,rs,e.formatters),e.selectors=tM(e.selectors,e=>e.selector),function(e){if(e.tags){let t=Object.entries(e.tags).map(([e,t])=>({...t,selector:e||"*"}));e.selectors.push(...t),e.selectors=tM(e.selectors,e=>e.selector)}function t(e,t,r){let n=t.pop();for(let r of t){let t=e[r];t||(t={},e[r]=t),e=t}e[n]=r}if(e.baseElement){let r=e.baseElement;t(e,["baseElements","selectors"],Array.isArray(r)?r:[r])}for(let r of(void 0!==e.returnDomByDefault&&t(e,["baseElements","returnDomByDefault"],e.returnDomByDefault),e.selectors))"anchor"===r.format&&t$(r,["options","noLinkBrackets"])&&t(r,["options","linkBrackets"],!1)}(e),function(e={}){let t=e.selectors.filter(e=>!e.format);if(t.length)throw Error("Following selectors have no specified format: "+t.map(e=>`\`${e.selector}\``).join(", "));let r=new eW(e.selectors.map(e=>[e.selector,e])).build(eQ);"function"!=typeof e.encodeCharacters&&(e.encodeCharacters=function(e){if(!e||0===Object.keys(e).length)return;let t=Object.entries(e).filter(([,e])=>!1!==e),r=RegExp(t.map(([e])=>`(${[...e][0].replace(/[\s\S]/g,e=>"\\u"+e.charCodeAt().toString(16).padStart(4,"0"))})`).join("|"),"g"),n=t.map(([,e])=>e),i=(e,...t)=>n[t.findIndex(e=>e)];return e=>e.replace(r,i)}(e.encodeCharacters));let n=new eW(e.baseElements.selectors.map((e,t)=>[e,t+1])).build(eQ);function i(t){var r=t,i=e,a=n;let s=[];return tO(i.limits.maxDepth,function(e,t){for(let r of t=t.slice(0,i.limits.maxChildNodes)){if("tag"!==r.type)continue;let t=a.pick1(r);if(t>0?s.push({selectorIndex:t,element:r}):r.children&&e(r.children),s.length>=i.limits.maxBaseElements)return}})(r),"occurrence"!==i.baseElements.orderBy&&s.sort((e,t)=>e.selectorIndex-t.selectorIndex),i.baseElements.returnDomByDefault&&0===s.length?r:s.map(e=>e.element)}let a=tO(e.limits.maxDepth,t6,function(t,r){r.addInline(e.limits.ellipsis||"")});return function(t,n){var s=t,o=n,l=e,u=r,c=i,h=a;let d=l.limits.maxInputLength;d&&s&&s.length>d&&(console.warn(`Input length ${s.length} is above allowed limit of ${d}. Truncating without ellipsis.`),s=s.substring(0,d));let p=c(function(e,t){let r=new _(void 0,t);return new tm(r,t).end(e),r.root}(s,{decodeEntities:l.decodeEntities}).children),f=new t2(l,u,o);return h(p,f),f.toString()}}(e)})(t)(e,void 0)}(n,cy({selectors:cv},t.htmlToTextOptions));let s=`<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">${n.replace(/<!DOCTYPE.*?>/,"")}`;return(null==t?void 0:t.pretty)?ck(s):s}),c_=(e,t)=>cF(e,t)},87276:(e,t,r)=>{e.exports=r(87628).vendored["react-rsc"].ReactJsxRuntime},87487:e=>{var t=function(e){var t,n,i;return!!(t=e)&&"object"==typeof t&&(n=e,"[object RegExp]"!==(i=Object.prototype.toString.call(n))&&"[object Date]"!==i&&n.$$typeof!==r)},r="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?o(Array.isArray(e)?[]:{},e,t):e}function i(e,t,r){return e.concat(t).map(function(e){return n(e,r)})}function a(e){return Object.keys(e).concat(Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter(function(t){return Object.propertyIsEnumerable.call(e,t)}):[])}function s(e,t){try{return t in e}catch(e){return!1}}function o(e,r,l){(l=l||{}).arrayMerge=l.arrayMerge||i,l.isMergeableObject=l.isMergeableObject||t,l.cloneUnlessOtherwiseSpecified=n;var u,c,h=Array.isArray(r);return h!==Array.isArray(e)?n(r,l):h?l.arrayMerge(e,r,l):(c={},(u=l).isMergeableObject(e)&&a(e).forEach(function(t){c[t]=n(e[t],u)}),a(r).forEach(function(t){s(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))||(s(e,t)&&u.isMergeableObject(r[t])?c[t]=(function(e,t){if(!t.customMerge)return o;var r=t.customMerge(e);return"function"==typeof r?r:o})(t,u)(e[t],r[t],u):c[t]=n(r[t],u))}),c)}o.all=function(e,t){if(!Array.isArray(e))throw Error("first argument should be an array");return e.reduce(function(e,r){return o(e,r,t)},{})},e.exports=o}};