exports.id=2783,exports.ids=[2783],exports.modules={1016:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r,t)=>{let s=new i(e,t),n=new i(r,t);return s.compare(n)||s.compareBuild(n)}},1340:e=>{var r,t,i=Object.prototype,s=Function.prototype.toString,n=i.hasOwnProperty,o=s.call(Object),a=i.toString,l=(r=Object.getPrototypeOf,t=Object,function(e){return r(t(e))});e.exports=function(e){if(!(e&&"object"==typeof e)||"[object Object]"!=a.call(e)||function(e){var r=!1;if(null!=e&&"function"!=typeof e.toString)try{r=!!(e+"")}catch(e){}return r}(e))return!1;var r=l(e);if(null===r)return!0;var t=n.call(r,"constructor")&&r.constructor;return"function"==typeof t&&t instanceof t&&s.call(t)==o}},2527:(e,r,t)=>{"use strict";let i=t(82259),{MAX_LENGTH:s,MAX_SAFE_INTEGER:n}=t(65429),{safeRe:o,t:a}=t(97563),l=t(85444),{compareIdentifiers:u}=t(9892);class c{constructor(e,r){if(r=l(r),e instanceof c)if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;else e=e.version;else if("string"!=typeof e)throw TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>s)throw TypeError(`version is longer than ${s} characters`);i("SemVer",e,r),this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease;let t=e.trim().match(r.loose?o[a.LOOSE]:o[a.FULL]);if(!t)throw TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+t[1],this.minor=+t[2],this.patch=+t[3],this.major>n||this.major<0)throw TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw TypeError("Invalid patch version");t[4]?this.prerelease=t[4].split(".").map(e=>{if(/^[0-9]+$/.test(e)){let r=+e;if(r>=0&&r<n)return r}return e}):this.prerelease=[],this.build=t[5]?t[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(i("SemVer.compare",this.version,this.options,e),!(e instanceof c)){if("string"==typeof e&&e===this.version)return 0;e=new c(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof c||(e=new c(e,this.options)),u(this.major,e.major)||u(this.minor,e.minor)||u(this.patch,e.patch)}comparePre(e){if(e instanceof c||(e=new c(e,this.options)),this.prerelease.length&&!e.prerelease.length)return -1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let r=0;do{let t=this.prerelease[r],s=e.prerelease[r];if(i("prerelease compare",r,t,s),void 0===t&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===t)return -1;else if(t===s)continue;else return u(t,s)}while(++r)}compareBuild(e){e instanceof c||(e=new c(e,this.options));let r=0;do{let t=this.build[r],s=e.build[r];if(i("build compare",r,t,s),void 0===t&&void 0===s)return 0;if(void 0===s)return 1;if(void 0===t)return -1;else if(t===s)continue;else return u(t,s)}while(++r)}inc(e,r,t){if(e.startsWith("pre")){if(!r&&!1===t)throw Error("invalid increment argument: identifier is empty");if(r){let e=`-${r}`.match(this.options.loose?o[a.PRERELEASELOOSE]:o[a.PRERELEASE]);if(!e||e[1]!==r)throw Error(`invalid identifier: ${r}`)}}switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",r,t);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",r,t);break;case"prepatch":this.prerelease.length=0,this.inc("patch",r,t),this.inc("pre",r,t);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",r,t),this.inc("pre",r,t);break;case"release":if(0===this.prerelease.length)throw Error(`version ${this.raw} is not a prerelease`);this.prerelease.length=0;break;case"major":(0!==this.minor||0!==this.patch||0===this.prerelease.length)&&this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":(0!==this.patch||0===this.prerelease.length)&&this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{let e=+!!Number(t);if(0===this.prerelease.length)this.prerelease=[e];else{let i=this.prerelease.length;for(;--i>=0;)"number"==typeof this.prerelease[i]&&(this.prerelease[i]++,i=-2);if(-1===i){if(r===this.prerelease.join(".")&&!1===t)throw Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(r){let i=[r,e];!1===t&&(i=[r]),0===u(this.prerelease[0],r)?isNaN(this.prerelease[1])&&(this.prerelease=i):this.prerelease=i}break}default:throw Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=c},2783:(e,r,t)=>{e.exports={decode:t(94685),verify:t(34530),sign:t(62830),JsonWebTokenError:t(98874),NotBeforeError:t(91367),TokenExpiredError:t(74055)}},2820:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>i(e,r,t)>=0},3462:(e,r,t)=>{e.exports=t(5616).satisfies(process.version,">=15.7.0")},5413:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>i(r,e,t)},5616:(e,r,t)=>{"use strict";let i=t(97563),s=t(65429),n=t(2527),o=t(9892),a=t(90945),l=t(22332),u=t(82403),c=t(79118),f=t(9779),p=t(52507),h=t(53007),m=t(44596),E=t(84750),d=t(51197),y=t(5413),g=t(12902),v=t(1016),b=t(66984),S=t(18352),$=t(85635),R=t(30168),w=t(88598),I=t(65842),O=t(2820),A=t(47301),L=t(26426),T=t(51433),N=t(28791),x=t(95394),j=t(82399),P=t(25970),C=t(12025),D=t(87975),k=t(31878),M=t(53345),G=t(74224),B=t(8900),F=t(7941),U=t(47241);e.exports={parse:a,valid:l,clean:u,inc:c,diff:f,major:p,minor:h,patch:m,prerelease:E,compare:d,rcompare:y,compareLoose:g,compareBuild:v,sort:b,rsort:S,gt:$,lt:R,eq:w,neq:I,gte:O,lte:A,cmp:L,coerce:T,Comparator:N,Range:x,satisfies:j,toComparators:P,maxSatisfying:C,minSatisfying:D,minVersion:k,validRange:M,outside:G,gtr:B,ltr:F,intersects:U,simplifyRange:t(35788),subset:t(57281),SemVer:n,re:i.re,src:i.src,tokens:i.t,SEMVER_SPEC_VERSION:s.SEMVER_SPEC_VERSION,RELEASE_TYPES:s.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},7385:(e,r,t)=>{var i=t(79428).Buffer;e.exports=function(e){return"string"==typeof e?e:"number"==typeof e||i.isBuffer(e)?e.toString():JSON.stringify(e)}},7941:(e,r,t)=>{"use strict";let i=t(74224);e.exports=(e,r,t)=>i(e,r,"<",t)},8900:(e,r,t)=>{"use strict";let i=t(74224);e.exports=(e,r,t)=>i(e,r,">",t)},9779:(e,r,t)=>{"use strict";let i=t(90945);e.exports=(e,r)=>{let t=i(e,null,!0),s=i(r,null,!0),n=t.compare(s);if(0===n)return null;let o=n>0,a=o?t:s,l=o?s:t,u=!!a.prerelease.length;if(l.prerelease.length&&!u){if(!l.patch&&!l.minor)return"major";if(0===l.compareMain(a))return l.minor&&!l.patch?"minor":"patch"}let c=u?"pre":"";return t.major!==s.major?c+"major":t.minor!==s.minor?c+"minor":t.patch!==s.patch?c+"patch":"prerelease"}},9892:e=>{"use strict";let r=/^[0-9]+$/,t=(e,t)=>{let i=r.test(e),s=r.test(t);return i&&s&&(e*=1,t*=1),e===t?0:i&&!s?-1:s&&!i?1:e<t?-1:1};e.exports={compareIdentifiers:t,rcompareIdentifiers:(e,r)=>t(r,e)}},12025:(e,r,t)=>{"use strict";let i=t(2527),s=t(95394);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new s(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||-1===o.compare(e))&&(o=new i(n=e,t))}),n}},12902:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r)=>i(e,r,!0)},13603:e=>{"use strict";class r{constructor(){this.max=1e3,this.map=new Map}get(e){let r=this.map.get(e);if(void 0!==r)return this.map.delete(e),this.map.set(e,r),r}delete(e){return this.map.delete(e)}set(e,r){if(!this.delete(e)&&void 0!==r){if(this.map.size>=this.max){let e=this.map.keys().next().value;this.delete(e)}this.map.set(e,r)}return this}}e.exports=r},14682:(e,r,t)=>{"use strict";var i=t(79428).Buffer,s=t(79428).SlowBuffer;function n(e,r){if(!i.isBuffer(e)||!i.isBuffer(r)||e.length!==r.length)return!1;for(var t=0,s=0;s<e.length;s++)t|=e[s]^r[s];return 0===t}e.exports=n,n.install=function(){i.prototype.equal=s.prototype.equal=function(e){return n(this,e)}};var o=i.prototype.equal,a=s.prototype.equal;n.restore=function(){i.prototype.equal=o,s.prototype.equal=a}},14700:(e,r,t)=>{let i=t(3462),s=t(68148),n={ec:["ES256","ES384","ES512"],rsa:["RS256","PS256","RS384","PS384","RS512","PS512"],"rsa-pss":["PS256","PS384","PS512"]},o={ES256:"prime256v1",ES384:"secp384r1",ES512:"secp521r1"};e.exports=function(e,r){if(!e||!r)return;let t=r.asymmetricKeyType;if(!t)return;let a=n[t];if(!a)throw Error(`Unknown key type "${t}".`);if(!a.includes(e))throw Error(`"alg" parameter for "${t}" key type must be one of: ${a.join(", ")}.`);if(i)switch(t){case"ec":let l=r.asymmetricKeyDetails.namedCurve,u=o[e];if(l!==u)throw Error(`"alg" parameter "${e}" requires curve "${u}".`);break;case"rsa-pss":if(s){let t=parseInt(e.slice(-3),10),{hashAlgorithm:i,mgf1HashAlgorithm:s,saltLength:n}=r.asymmetricKeyDetails;if(i!==`sha${t}`||s!==i)throw Error(`Invalid key for this operation, its RSA-PSS parameters do not meet the requirements of "alg" ${e}.`);if(void 0!==n&&n>t>>3)throw Error(`Invalid key for this operation, its RSA-PSS parameter saltLength does not meet the requirements of "alg" ${e}.`)}}}},18352:(e,r,t)=>{"use strict";let i=t(1016);e.exports=(e,r)=>e.sort((e,t)=>i(t,e,r))},21018:e=>{var r=Object.prototype.toString;e.exports=function(e){return"number"==typeof e||!!e&&"object"==typeof e&&"[object Number]"==r.call(e)}},22332:(e,r,t)=>{"use strict";let i=t(90945);e.exports=(e,r)=>{let t=i(e,r);return t?t.version:null}},25258:e=>{var r=1/0,t=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){var c,f,p,h,m=2,E=e;if("function"!=typeof E)throw TypeError("Expected a function");return p=(f=(c=m)?(c=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var f=n.test(e);return f||o.test(e)?a(e.slice(2),f?2:8):s.test(e)?t:+e}(c))===r||c===-r?(c<0?-1:1)*17976931348623157e292:c==c?c:0:0===c?c:0)%1,m=f==f?p?f-p:f:0,function(){return--m>0&&(h=E.apply(this,arguments)),m<=1&&(E=void 0),h}}},25970:(e,r,t)=>{"use strict";let i=t(95394);e.exports=(e,r)=>new i(e,r).set.map(e=>e.map(e=>e.value).join(" ").trim().split(" "))},26426:(e,r,t)=>{"use strict";let i=t(88598),s=t(65842),n=t(85635),o=t(2820),a=t(30168),l=t(47301);e.exports=(e,r,t,u)=>{switch(r){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e===t;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof t&&(t=t.version),e!==t;case"":case"=":case"==":return i(e,t,u);case"!=":return s(e,t,u);case">":return n(e,t,u);case">=":return o(e,t,u);case"<":return a(e,t,u);case"<=":return l(e,t,u);default:throw TypeError(`Invalid operator: ${r}`)}}},28791:(e,r,t)=>{"use strict";let i=Symbol("SemVer ANY");class s{static get ANY(){return i}constructor(e,r){if(r=n(r),e instanceof s)if(!!r.loose===e.loose)return e;else e=e.value;u("comparator",e=e.trim().split(/\s+/).join(" "),r),this.options=r,this.loose=!!r.loose,this.parse(e),this.semver===i?this.value="":this.value=this.operator+this.semver.version,u("comp",this)}parse(e){let r=this.options.loose?o[a.COMPARATORLOOSE]:o[a.COMPARATOR],t=e.match(r);if(!t)throw TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==t[1]?t[1]:"","="===this.operator&&(this.operator=""),t[2]?this.semver=new c(t[2],this.options.loose):this.semver=i}toString(){return this.value}test(e){if(u("Comparator.test",e,this.options.loose),this.semver===i||e===i)return!0;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}return l(e,this.operator,this.semver,this.options)}intersects(e,r){if(!(e instanceof s))throw TypeError("a Comparator is required");return""===this.operator?""===this.value||new f(e.value,r).test(this.value):""===e.operator?""===e.value||new f(this.value,r).test(e.semver):!((r=n(r)).includePrerelease&&("<0.0.0-0"===this.value||"<0.0.0-0"===e.value)||!r.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&!!(this.operator.startsWith(">")&&e.operator.startsWith(">")||this.operator.startsWith("<")&&e.operator.startsWith("<")||this.semver.version===e.semver.version&&this.operator.includes("=")&&e.operator.includes("=")||l(this.semver,"<",e.semver,r)&&this.operator.startsWith(">")&&e.operator.startsWith("<")||l(this.semver,">",e.semver,r)&&this.operator.startsWith("<")&&e.operator.startsWith(">"))}}e.exports=s;let n=t(85444),{safeRe:o,t:a}=t(97563),l=t(26426),u=t(82259),c=t(2527),f=t(95394)},30168:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>0>i(e,r,t)},30177:(e,r,t)=>{var i=t(61280).Buffer,s=t(60718),n=t(57289),o=t(27910),a=t(7385),l=t(28354),u=/^[a-zA-Z0-9\-_]+?\.[a-zA-Z0-9\-_]+?\.([a-zA-Z0-9\-_]+)?$/;function c(e){var r=e.split(".",1)[0],t=i.from(r,"base64").toString("binary");if("[object Object]"===Object.prototype.toString.call(t))return t;try{return JSON.parse(t)}catch(e){return}}function f(e){return e.split(".")[2]}function p(e){return u.test(e)&&!!c(e)}function h(e,r,t){if(!r){var i=Error("Missing algorithm parameter for jws.verify");throw i.code="MISSING_ALGORITHM",i}var s=f(e=a(e)),o=e.split(".",2).join(".");return n(r).verify(o,s,t)}function m(e,r){if(r=r||{},!p(e=a(e)))return null;var t,s,n=c(e);if(!n)return null;var o=(t=t||"utf8",s=e.split(".")[1],i.from(s,"base64").toString(t));return("JWT"===n.typ||r.json)&&(o=JSON.parse(o,r.encoding)),{header:n,payload:o,signature:f(e)}}function E(e){var r=new s((e=e||{}).secret||e.publicKey||e.key);this.readable=!0,this.algorithm=e.algorithm,this.encoding=e.encoding,this.secret=this.publicKey=this.key=r,this.signature=new s(e.signature),this.secret.once("close",(function(){!this.signature.writable&&this.readable&&this.verify()}).bind(this)),this.signature.once("close",(function(){!this.secret.writable&&this.readable&&this.verify()}).bind(this))}l.inherits(E,o),E.prototype.verify=function(){try{var e=h(this.signature.buffer,this.algorithm,this.key.buffer),r=m(this.signature.buffer,this.encoding);return this.emit("done",e,r),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},E.decode=m,E.isValid=p,E.verify=h,e.exports=E},30381:e=>{var r,t,i=1/0,s=0/0,n=/^\s+|\s+$/g,o=/^[-+]0x[0-9a-f]+$/i,a=/^0b[01]+$/i,l=/^0o[0-7]+$/i,u=/^(?:0|[1-9]\d*)$/,c=parseInt;function f(e){return e!=e}var p=Object.prototype,h=p.hasOwnProperty,m=p.toString,E=p.propertyIsEnumerable,d=(r=Object.keys,t=Object,function(e){return r(t(e))}),y=Math.max,g=Array.isArray;function v(e){var r,t,i;return null!=e&&"number"==typeof(r=e.length)&&r>-1&&r%1==0&&r<=0x1fffffffffffff&&"[object Function]"!=(i=b(t=e)?m.call(t):"")&&"[object GeneratorFunction]"!=i}function b(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}function S(e){return!!e&&"object"==typeof e}e.exports=function(e,r,t,$){e=v(e)?e:function(e){return e?function(e,r){for(var t=-1,i=e?e.length:0,s=Array(i);++t<i;)s[t]=r(e[t],t,e);return s}(v(e)?function(e,r){var t,i,s,n,o=g(e)||S(i=t=e)&&v(i)&&h.call(t,"callee")&&(!E.call(t,"callee")||"[object Arguments]"==m.call(t))?function(e,r){for(var t=-1,i=Array(e);++t<e;)i[t]=r(t);return i}(e.length,String):[],a=o.length,l=!!a;for(var c in e){h.call(e,c)&&!(l&&("length"==c||(s=c,(n=null==(n=a)?0x1fffffffffffff:n)&&("number"==typeof s||u.test(s))&&s>-1&&s%1==0&&s<n)))&&o.push(c)}return o}(e):function(e){if(t=(r=e)&&r.constructor,r!==("function"==typeof t&&t.prototype||p))return d(e);var r,t,i=[];for(var s in Object(e))h.call(e,s)&&"constructor"!=s&&i.push(s);return i}(e),function(r){return e[r]}):[]}(e),t=t&&!$?(I=(w=(R=t)?(R=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||S(r)&&"[object Symbol]"==m.call(r))return s;if(b(e)){var r,t="function"==typeof e.valueOf?e.valueOf():e;e=b(t)?t+"":t}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(n,"");var i=a.test(e);return i||l.test(e)?c(e.slice(2),i?2:8):o.test(e)?s:+e}(R))===i||R===-i?(R<0?-1:1)*17976931348623157e292:R==R?R:0:0===R?R:0)%1,w==w?I?w-I:w:0):0;var R,w,I,O,A=e.length;return t<0&&(t=y(A+t,0)),"string"==typeof(O=e)||!g(O)&&S(O)&&"[object String]"==m.call(O)?t<=A&&e.indexOf(r,t)>-1:!!A&&function(e,r,t){if(r!=r){for(var i,s=e.length,n=t+-1;i?n--:++n<s;)if(f(e[n],n,e))return n;return -1}for(var o=t-1,a=e.length;++o<a;)if(e[o]===r)return o;return -1}(e,r,t)>-1}},31878:(e,r,t)=>{"use strict";let i=t(2527),s=t(95394),n=t(85635);e.exports=(e,r)=>{e=new s(e,r);let t=new i("0.0.0");if(e.test(t)||(t=new i("0.0.0-0"),e.test(t)))return t;t=null;for(let r=0;r<e.set.length;++r){let s=e.set[r],o=null;s.forEach(e=>{let r=new i(e.semver.version);switch(e.operator){case">":0===r.prerelease.length?r.patch++:r.prerelease.push(0),r.raw=r.format();case"":case">=":(!o||n(r,o))&&(o=r);break;case"<":case"<=":break;default:throw Error(`Unexpected operation: ${e.operator}`)}}),o&&(!t||n(t,o))&&(t=o)}return t&&e.test(t)?t:null}},34530:(e,r,t)=>{let i=t(98874),s=t(91367),n=t(74055),o=t(94685),a=t(77162),l=t(14700),u=t(43802),c=t(97013),{KeyObject:f,createSecretKey:p,createPublicKey:h}=t(55511),m=["RS256","RS384","RS512"],E=["ES256","ES384","ES512"],d=["RS256","RS384","RS512"],y=["HS256","HS384","HS512"];u&&(m.splice(m.length,0,"PS256","PS384","PS512"),d.splice(d.length,0,"PS256","PS384","PS512")),e.exports=function(e,r,t,u){let g,v,b;if("function"!=typeof t||u||(u=t,t={}),t||(t={}),t=Object.assign({},t),g=u||function(e,r){if(e)throw e;return r},t.clockTimestamp&&"number"!=typeof t.clockTimestamp)return g(new i("clockTimestamp must be a number"));if(void 0!==t.nonce&&("string"!=typeof t.nonce||""===t.nonce.trim()))return g(new i("nonce must be a non-empty string"));if(void 0!==t.allowInvalidAsymmetricKeyTypes&&"boolean"!=typeof t.allowInvalidAsymmetricKeyTypes)return g(new i("allowInvalidAsymmetricKeyTypes must be a boolean"));let S=t.clockTimestamp||Math.floor(Date.now()/1e3);if(!e)return g(new i("jwt must be provided"));if("string"!=typeof e)return g(new i("jwt must be a string"));let $=e.split(".");if(3!==$.length)return g(new i("jwt malformed"));try{v=o(e,{complete:!0})}catch(e){return g(e)}if(!v)return g(new i("invalid token"));let R=v.header;if("function"==typeof r){if(!u)return g(new i("verify must be called asynchronous if secret or public key is provided as a callback"));b=r}else b=function(e,t){return t(null,r)};return b(R,function(r,o){let u;if(r)return g(new i("error in secret or public key callback: "+r.message));let b=""!==$[2].trim();if(!b&&o)return g(new i("jwt signature is required"));if(b&&!o)return g(new i("secret or public key must be provided"));if(!b&&!t.algorithms)return g(new i('please specify "none" in "algorithms" to verify unsigned tokens'));if(null!=o&&!(o instanceof f))try{o=h(o)}catch(e){try{o=p("string"==typeof o?Buffer.from(o):o)}catch(e){return g(new i("secretOrPublicKey is not valid key material"))}}if(t.algorithms||("secret"===o.type?t.algorithms=y:["rsa","rsa-pss"].includes(o.asymmetricKeyType)?t.algorithms=d:"ec"===o.asymmetricKeyType?t.algorithms=E:t.algorithms=m),-1===t.algorithms.indexOf(v.header.alg))return g(new i("invalid algorithm"));if(R.alg.startsWith("HS")&&"secret"!==o.type)return g(new i(`secretOrPublicKey must be a symmetric key when using ${R.alg}`));if(/^(?:RS|PS|ES)/.test(R.alg)&&"public"!==o.type)return g(new i(`secretOrPublicKey must be an asymmetric key when using ${R.alg}`));if(!t.allowInvalidAsymmetricKeyTypes)try{l(R.alg,o)}catch(e){return g(e)}try{u=c.verify(e,v.header.alg,o)}catch(e){return g(e)}if(!u)return g(new i("invalid signature"));let w=v.payload;if(void 0!==w.nbf&&!t.ignoreNotBefore){if("number"!=typeof w.nbf)return g(new i("invalid nbf value"));if(w.nbf>S+(t.clockTolerance||0))return g(new s("jwt not active",new Date(1e3*w.nbf)))}if(void 0!==w.exp&&!t.ignoreExpiration){if("number"!=typeof w.exp)return g(new i("invalid exp value"));if(S>=w.exp+(t.clockTolerance||0))return g(new n("jwt expired",new Date(1e3*w.exp)))}if(t.audience){let e=Array.isArray(t.audience)?t.audience:[t.audience];if(!(Array.isArray(w.aud)?w.aud:[w.aud]).some(function(r){return e.some(function(e){return e instanceof RegExp?e.test(r):e===r})}))return g(new i("jwt audience invalid. expected: "+e.join(" or ")))}if(t.issuer&&("string"==typeof t.issuer&&w.iss!==t.issuer||Array.isArray(t.issuer)&&-1===t.issuer.indexOf(w.iss)))return g(new i("jwt issuer invalid. expected: "+t.issuer));if(t.subject&&w.sub!==t.subject)return g(new i("jwt subject invalid. expected: "+t.subject));if(t.jwtid&&w.jti!==t.jwtid)return g(new i("jwt jwtid invalid. expected: "+t.jwtid));if(t.nonce&&w.nonce!==t.nonce)return g(new i("jwt nonce invalid. expected: "+t.nonce));if(t.maxAge){if("number"!=typeof w.iat)return g(new i("iat required when maxAge is specified"));let e=a(t.maxAge,w.iat);if(void 0===e)return g(new i('"maxAge" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'));if(S>=e+(t.clockTolerance||0))return g(new n("maxAge exceeded",new Date(1e3*e)))}return!0===t.complete?g(null,{header:R,payload:w,signature:v.signature}):g(null,w)})}},35726:e=>{function r(e,r,t,i){return Math.round(e/t)+" "+i+(r>=1.5*t?"s":"")}e.exports=function(e,t){t=t||{};var i,s,n,o,a=typeof e;if("string"===a&&e.length>0){var l=e;if(!((l=String(l)).length>100)){var u=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(l);if(u){var c=parseFloat(u[1]);switch((u[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*c;case"weeks":case"week":case"w":return 6048e5*c;case"days":case"day":case"d":return 864e5*c;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*c;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*c;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*c;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:break}}}return}if("number"===a&&isFinite(e)){return t.long?(s=Math.abs(i=e))>=864e5?r(i,s,864e5,"day"):s>=36e5?r(i,s,36e5,"hour"):s>=6e4?r(i,s,6e4,"minute"):s>=1e3?r(i,s,1e3,"second"):i+" ms":(o=Math.abs(n=e))>=864e5?Math.round(n/864e5)+"d":o>=36e5?Math.round(n/36e5)+"h":o>=6e4?Math.round(n/6e4)+"m":o>=1e3?Math.round(n/1e3)+"s":n+"ms"}throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},35788:(e,r,t)=>{"use strict";let i=t(82399),s=t(51197);e.exports=(e,r,t)=>{let n=[],o=null,a=null,l=e.sort((e,r)=>s(e,r,t));for(let e of l)i(e,r,t)?(a=e,o||(o=e)):(a&&n.push([o,a]),a=null,o=null);o&&n.push([o,null]);let u=[];for(let[e,r]of n)e===r?u.push(e):r||e!==l[0]?r?e===l[0]?u.push(`<=${r}`):u.push(`${e} - ${r}`):u.push(`>=${e}`):u.push("*");let c=u.join(" || "),f="string"==typeof r.raw?r.raw:String(r);return c.length<f.length?c:r}},38932:e=>{"use strict";function r(e){return(e/8|0)+ +(e%8!=0)}var t={ES256:r(256),ES384:r(384),ES512:r(521)};e.exports=function(e){var r=t[e];if(r)return r;throw Error('Unknown algorithm "'+e+'"')}},43802:(e,r,t)=>{e.exports=t(5616).satisfies(process.version,"^6.12.0 || >=8.0.0")},44596:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r)=>new i(e,r).patch},47241:(e,r,t)=>{"use strict";let i=t(95394);e.exports=(e,r,t)=>(e=new i(e,t),r=new i(r,t),e.intersects(r,t))},47301:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>0>=i(e,r,t)},51197:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r,t)=>new i(e,t).compare(new i(r,t))},51433:(e,r,t)=>{"use strict";let i=t(2527),s=t(90945),{safeRe:n,t:o}=t(97563);e.exports=(e,r)=>{if(e instanceof i)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let t=null;if((r=r||{}).rtl){let i,s=r.includePrerelease?n[o.COERCERTLFULL]:n[o.COERCERTL];for(;(i=s.exec(e))&&(!t||t.index+t[0].length!==e.length);)t&&i.index+i[0].length===t.index+t[0].length||(t=i),s.lastIndex=i.index+i[1].length+i[2].length;s.lastIndex=-1}else t=e.match(r.includePrerelease?n[o.COERCEFULL]:n[o.COERCE]);if(null===t)return null;let a=t[2],l=t[3]||"0",u=t[4]||"0",c=r.includePrerelease&&t[5]?`-${t[5]}`:"",f=r.includePrerelease&&t[6]?`+${t[6]}`:"";return s(`${a}.${l}.${u}${c}${f}`,r)}},52507:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r)=>new i(e,r).major},53007:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r)=>new i(e,r).minor},53345:(e,r,t)=>{"use strict";let i=t(95394);e.exports=(e,r)=>{try{return new i(e,r).range||"*"}catch(e){return null}}},57281:(e,r,t)=>{"use strict";let i=t(95394),s=t(28791),{ANY:n}=s,o=t(82399),a=t(51197),l=[new s(">=0.0.0-0")],u=[new s(">=0.0.0")],c=(e,r,t)=>{let i,s,c,h,m,E,d;if(e===r)return!0;if(1===e.length&&e[0].semver===n)if(1===r.length&&r[0].semver===n)return!0;else e=t.includePrerelease?l:u;if(1===r.length&&r[0].semver===n)if(t.includePrerelease)return!0;else r=u;let y=new Set;for(let r of e)">"===r.operator||">="===r.operator?i=f(i,r,t):"<"===r.operator||"<="===r.operator?s=p(s,r,t):y.add(r.semver);if(y.size>1)return null;if(i&&s&&((c=a(i.semver,s.semver,t))>0||0===c&&(">="!==i.operator||"<="!==s.operator)))return null;for(let e of y){if(i&&!o(e,String(i),t)||s&&!o(e,String(s),t))return null;for(let i of r)if(!o(e,String(i),t))return!1;return!0}let g=!!s&&!t.includePrerelease&&!!s.semver.prerelease.length&&s.semver,v=!!i&&!t.includePrerelease&&!!i.semver.prerelease.length&&i.semver;for(let e of(g&&1===g.prerelease.length&&"<"===s.operator&&0===g.prerelease[0]&&(g=!1),r)){if(d=d||">"===e.operator||">="===e.operator,E=E||"<"===e.operator||"<="===e.operator,i){if(v&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===v.major&&e.semver.minor===v.minor&&e.semver.patch===v.patch&&(v=!1),">"===e.operator||">="===e.operator){if((h=f(i,e,t))===e&&h!==i)return!1}else if(">="===i.operator&&!o(i.semver,String(e),t))return!1}if(s){if(g&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===g.major&&e.semver.minor===g.minor&&e.semver.patch===g.patch&&(g=!1),"<"===e.operator||"<="===e.operator){if((m=p(s,e,t))===e&&m!==s)return!1}else if("<="===s.operator&&!o(s.semver,String(e),t))return!1}if(!e.operator&&(s||i)&&0!==c)return!1}return(!i||!E||!!s||0===c)&&(!s||!d||!!i||0===c)&&!v&&!g&&!0},f=(e,r,t)=>{if(!e)return r;let i=a(e.semver,r.semver,t);return i>0?e:i<0||">"===r.operator&&">="===e.operator?r:e},p=(e,r,t)=>{if(!e)return r;let i=a(e.semver,r.semver,t);return i<0?e:i>0||"<"===r.operator&&"<="===e.operator?r:e};e.exports=(e,r,t={})=>{if(e===r)return!0;e=new i(e,t),r=new i(r,t);let s=!1;e:for(let i of e.set){for(let e of r.set){let r=c(i,e,t);if(s=s||null!==r,r)continue e}if(s)return!1}return!0}},57289:(e,r,t)=>{var i,s=t(61280).Buffer,n=t(55511),o=t(99709),a=t(28354),l="secret must be a string or buffer",u="key must be a string or a buffer",c="function"==typeof n.createPublicKey;function f(e){if(!s.isBuffer(e)&&"string"!=typeof e&&(!c||"object"!=typeof e||"string"!=typeof e.type||"string"!=typeof e.asymmetricKeyType||"function"!=typeof e.export))throw E(u)}function p(e){if(!s.isBuffer(e)&&"string"!=typeof e&&"object"!=typeof e)throw E("key must be a string, a buffer or an object")}function h(e){return e.replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function m(e){var r=4-(e=e.toString()).length%4;if(4!==r)for(var t=0;t<r;++t)e+="=";return e.replace(/\-/g,"+").replace(/_/g,"/")}function E(e){var r=[].slice.call(arguments,1);return TypeError(a.format.bind(a,e).apply(null,r))}function d(e){var r;return r=e,s.isBuffer(r)||"string"==typeof r||(e=JSON.stringify(e)),e}function y(e){return function(r,t){!function(e){if(!s.isBuffer(e)){if("string"!=typeof e){if(!c||"object"!=typeof e||"secret"!==e.type||"function"!=typeof e.export)throw E(l)}}}(t),r=d(r);var i=n.createHmac("sha"+e,t);return h((i.update(r),i.digest("base64")))}}c&&(u+=" or a KeyObject",l+="or a KeyObject");var g="timingSafeEqual"in n?function(e,r){return e.byteLength===r.byteLength&&n.timingSafeEqual(e,r)}:function(e,r){return i||(i=t(14682)),i(e,r)};function v(e){return function(r,t,i){var n=y(e)(r,i);return g(s.from(t),s.from(n))}}function b(e){return function(r,t){p(t),r=d(r);var i=n.createSign("RSA-SHA"+e);return h((i.update(r),i.sign(t,"base64")))}}function S(e){return function(r,t,i){f(i),r=d(r),t=m(t);var s=n.createVerify("RSA-SHA"+e);return s.update(r),s.verify(i,t,"base64")}}function $(e){return function(r,t){p(t),r=d(r);var i=n.createSign("RSA-SHA"+e);return h((i.update(r),i.sign({key:t,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},"base64")))}}function R(e){return function(r,t,i){f(i),r=d(r),t=m(t);var s=n.createVerify("RSA-SHA"+e);return s.update(r),s.verify({key:i,padding:n.constants.RSA_PKCS1_PSS_PADDING,saltLength:n.constants.RSA_PSS_SALTLEN_DIGEST},t,"base64")}}function w(e){var r=b(e);return function(){var t=r.apply(null,arguments);return o.derToJose(t,"ES"+e)}}function I(e){var r=S(e);return function(t,i,s){return r(t,i=o.joseToDer(i,"ES"+e).toString("base64"),s)}}function O(){return function(){return""}}function A(){return function(e,r){return""===r}}e.exports=function(e){var r=e.match(/^(RS|PS|ES|HS)(256|384|512)$|^(none)$/i);if(!r)throw E('"%s" is not a valid algorithm.\n  Supported algorithms are:\n  "HS256", "HS384", "HS512", "RS256", "RS384", "RS512", "PS256", "PS384", "PS512", "ES256", "ES384", "ES512" and "none".',e);var t=(r[1]||r[3]).toLowerCase(),i=r[2];return{sign:({hs:y,rs:b,ps:$,es:w,none:O})[t](i),verify:({hs:v,rs:S,ps:R,es:I,none:A})[t](i)}}},57546:e=>{var r=Object.prototype.toString;e.exports=function(e){var t;return!0===e||!1===e||!!(t=e)&&"object"==typeof t&&"[object Boolean]"==r.call(e)}},60718:(e,r,t)=>{var i=t(61280).Buffer,s=t(27910);function n(e){if(this.buffer=null,this.writable=!0,this.readable=!0,!e)return this.buffer=i.alloc(0),this;if("function"==typeof e.pipe)return this.buffer=i.alloc(0),e.pipe(this),this;if(e.length||"object"==typeof e)return this.buffer=e,this.writable=!1,process.nextTick((function(){this.emit("end",e),this.readable=!1,this.emit("close")}).bind(this)),this;throw TypeError("Unexpected data type ("+typeof e+")")}t(28354).inherits(n,s),n.prototype.write=function(e){this.buffer=i.concat([this.buffer,i.from(e)]),this.emit("data",e)},n.prototype.end=function(e){e&&this.write(e),this.emit("end",e),this.emit("close"),this.writable=!1,this.readable=!1},e.exports=n},61280:(e,r,t)=>{var i=t(79428),s=i.Buffer;function n(e,r){for(var t in e)r[t]=e[t]}function o(e,r,t){return s(e,r,t)}s.from&&s.alloc&&s.allocUnsafe&&s.allocUnsafeSlow?e.exports=i:(n(i,r),r.Buffer=o),o.prototype=Object.create(s.prototype),n(s,o),o.from=function(e,r,t){if("number"==typeof e)throw TypeError("Argument must not be a number");return s(e,r,t)},o.alloc=function(e,r,t){if("number"!=typeof e)throw TypeError("Argument must be a number");var i=s(e);return void 0!==r?"string"==typeof t?i.fill(r,t):i.fill(r):i.fill(0),i},o.allocUnsafe=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return s(e)},o.allocUnsafeSlow=function(e){if("number"!=typeof e)throw TypeError("Argument must be a number");return i.SlowBuffer(e)}},62830:(e,r,t)=>{let i=t(77162),s=t(43802),n=t(14700),o=t(97013),a=t(30381),l=t(57546),u=t(91686),c=t(21018),f=t(1340),p=t(97119),h=t(25258),{KeyObject:m,createSecretKey:E,createPrivateKey:d}=t(55511),y=["RS256","RS384","RS512","ES256","ES384","ES512","HS256","HS384","HS512","none"];s&&y.splice(3,0,"PS256","PS384","PS512");let g={expiresIn:{isValid:function(e){return u(e)||p(e)&&e},message:'"expiresIn" should be a number of seconds or string representing a timespan'},notBefore:{isValid:function(e){return u(e)||p(e)&&e},message:'"notBefore" should be a number of seconds or string representing a timespan'},audience:{isValid:function(e){return p(e)||Array.isArray(e)},message:'"audience" must be a string or array'},algorithm:{isValid:a.bind(null,y),message:'"algorithm" must be a valid string enum value'},header:{isValid:f,message:'"header" must be an object'},encoding:{isValid:p,message:'"encoding" must be a string'},issuer:{isValid:p,message:'"issuer" must be a string'},subject:{isValid:p,message:'"subject" must be a string'},jwtid:{isValid:p,message:'"jwtid" must be a string'},noTimestamp:{isValid:l,message:'"noTimestamp" must be a boolean'},keyid:{isValid:p,message:'"keyid" must be a string'},mutatePayload:{isValid:l,message:'"mutatePayload" must be a boolean'},allowInsecureKeySizes:{isValid:l,message:'"allowInsecureKeySizes" must be a boolean'},allowInvalidAsymmetricKeyTypes:{isValid:l,message:'"allowInvalidAsymmetricKeyTypes" must be a boolean'}},v={iat:{isValid:c,message:'"iat" should be a number of seconds'},exp:{isValid:c,message:'"exp" should be a number of seconds'},nbf:{isValid:c,message:'"nbf" should be a number of seconds'}};function b(e,r,t,i){if(!f(t))throw Error('Expected "'+i+'" to be a plain object.');Object.keys(t).forEach(function(s){let n=e[s];if(!n){if(!r)throw Error('"'+s+'" is not allowed in "'+i+'"');return}if(!n.isValid(t[s]))throw Error(n.message)})}let S={audience:"aud",issuer:"iss",subject:"sub",jwtid:"jti"},$=["expiresIn","notBefore","noTimestamp","audience","issuer","subject","jwtid"];e.exports=function(e,r,t,s){var a,l;"function"==typeof t?(s=t,t={}):t=t||{};let u="object"==typeof e&&!Buffer.isBuffer(e),c=Object.assign({alg:t.algorithm||"HS256",typ:u?"JWT":void 0,kid:t.keyid},t.header);function f(e){if(s)return s(e);throw e}if(!r&&"none"!==t.algorithm)return f(Error("secretOrPrivateKey must have a value"));if(null!=r&&!(r instanceof m))try{r=d(r)}catch(e){try{r=E("string"==typeof r?Buffer.from(r):r)}catch(e){return f(Error("secretOrPrivateKey is not valid key material"))}}if(c.alg.startsWith("HS")&&"secret"!==r.type)return f(Error(`secretOrPrivateKey must be a symmetric key when using ${c.alg}`));if(/^(?:RS|PS|ES)/.test(c.alg)){if("private"!==r.type)return f(Error(`secretOrPrivateKey must be an asymmetric key when using ${c.alg}`));if(!t.allowInsecureKeySizes&&!c.alg.startsWith("ES")&&void 0!==r.asymmetricKeyDetails&&r.asymmetricKeyDetails.modulusLength<2048)return f(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`))}if(void 0===e)return f(Error("payload is required"));if(u){try{a=e,b(v,!0,a,"payload")}catch(e){return f(e)}t.mutatePayload||(e=Object.assign({},e))}else{let r=$.filter(function(e){return void 0!==t[e]});if(r.length>0)return f(Error("invalid "+r.join(",")+" option for "+typeof e+" payload"))}if(void 0!==e.exp&&void 0!==t.expiresIn)return f(Error('Bad "options.expiresIn" option the payload already has an "exp" property.'));if(void 0!==e.nbf&&void 0!==t.notBefore)return f(Error('Bad "options.notBefore" option the payload already has an "nbf" property.'));try{l=t,b(g,!1,l,"options")}catch(e){return f(e)}if(!t.allowInvalidAsymmetricKeyTypes)try{n(c.alg,r)}catch(e){return f(e)}let p=e.iat||Math.floor(Date.now()/1e3);if(t.noTimestamp?delete e.iat:u&&(e.iat=p),void 0!==t.notBefore){try{e.nbf=i(t.notBefore,p)}catch(e){return f(e)}if(void 0===e.nbf)return f(Error('"notBefore" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}if(void 0!==t.expiresIn&&"object"==typeof e){try{e.exp=i(t.expiresIn,p)}catch(e){return f(e)}if(void 0===e.exp)return f(Error('"expiresIn" should be a number of seconds or string representing a timespan eg: "1d", "20h", 60'))}Object.keys(S).forEach(function(r){let i=S[r];if(void 0!==t[r]){if(void 0!==e[i])return f(Error('Bad "options.'+r+'" option. The payload already has an "'+i+'" property.'));e[i]=t[r]}});let y=t.encoding||"utf8";if("function"==typeof s)s=s&&h(s),o.createSign({header:c,privateKey:r,payload:e,encoding:y}).once("error",s).once("done",function(e){if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&e.length<256)return s(Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`));s(null,e)});else{let i=o.sign({header:c,payload:e,secret:r,encoding:y});if(!t.allowInsecureKeySizes&&/^(?:RS|PS)/.test(c.alg)&&i.length<256)throw Error(`secretOrPrivateKey has a minimum key size of 2048 bits for ${c.alg}`);return i}}},65429:e=>{"use strict";e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:Number.MAX_SAFE_INTEGER||0x1fffffffffffff,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},65842:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>0!==i(e,r,t)},66984:(e,r,t)=>{"use strict";let i=t(1016);e.exports=(e,r)=>e.sort((e,t)=>i(e,t,r))},68148:(e,r,t)=>{e.exports=t(5616).satisfies(process.version,">=16.9.0")},71741:(e,r,t)=>{var i=t(61280).Buffer,s=t(60718),n=t(57289),o=t(27910),a=t(7385),l=t(28354);function u(e,r){return i.from(e,r).toString("base64").replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function c(e){var r,t,i,s=e.header,o=e.payload,c=e.secret||e.privateKey,f=e.encoding,p=n(s.alg),h=(r=(r=f)||"utf8",t=u(a(s),"binary"),i=u(a(o),r),l.format("%s.%s",t,i)),m=p.sign(h,c);return l.format("%s.%s",h,m)}function f(e){var r=new s(e.secret||e.privateKey||e.key);this.readable=!0,this.header=e.header,this.encoding=e.encoding,this.secret=this.privateKey=this.key=r,this.payload=new s(e.payload),this.secret.once("close",(function(){!this.payload.writable&&this.readable&&this.sign()}).bind(this)),this.payload.once("close",(function(){!this.secret.writable&&this.readable&&this.sign()}).bind(this))}l.inherits(f,o),f.prototype.sign=function(){try{var e=c({header:this.header,payload:this.payload.buffer,secret:this.secret.buffer,encoding:this.encoding});return this.emit("done",e),this.emit("data",e),this.emit("end"),this.readable=!1,e}catch(e){this.readable=!1,this.emit("error",e),this.emit("close")}},f.sign=c,e.exports=f},74055:(e,r,t)=>{var i=t(98874),s=function(e,r){i.call(this,e),this.name="TokenExpiredError",this.expiredAt=r};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},74224:(e,r,t)=>{"use strict";let i=t(2527),s=t(28791),{ANY:n}=s,o=t(95394),a=t(82399),l=t(85635),u=t(30168),c=t(47301),f=t(2820);e.exports=(e,r,t,p)=>{let h,m,E,d,y;switch(e=new i(e,p),r=new o(r,p),t){case">":h=l,m=c,E=u,d=">",y=">=";break;case"<":h=u,m=f,E=l,d="<",y="<=";break;default:throw TypeError('Must provide a hilo val of "<" or ">"')}if(a(e,r,p))return!1;for(let t=0;t<r.set.length;++t){let i=r.set[t],o=null,a=null;if(i.forEach(e=>{e.semver===n&&(e=new s(">=0.0.0")),o=o||e,a=a||e,h(e.semver,o.semver,p)?o=e:E(e.semver,a.semver,p)&&(a=e)}),o.operator===d||o.operator===y||(!a.operator||a.operator===d)&&m(e,a.semver)||a.operator===y&&E(e,a.semver))return!1}return!0}},77162:(e,r,t)=>{var i=t(35726);e.exports=function(e,r){var t=r||Math.floor(Date.now()/1e3);if("string"==typeof e){var s=i(e);if(void 0===s)return;return Math.floor(t+s/1e3)}if("number"==typeof e)return t+e}},79118:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r,t,s,n)=>{"string"==typeof t&&(n=s,s=t,t=void 0);try{return new i(e instanceof i?e.version:e,t).inc(r,s,n).version}catch(e){return null}}},82259:e=>{"use strict";e.exports="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{}},82399:(e,r,t)=>{"use strict";let i=t(95394);e.exports=(e,r,t)=>{try{r=new i(r,t)}catch(e){return!1}return r.test(e)}},82403:(e,r,t)=>{"use strict";let i=t(90945);e.exports=(e,r)=>{let t=i(e.trim().replace(/^[=v]+/,""),r);return t?t.version:null}},84750:(e,r,t)=>{"use strict";let i=t(90945);e.exports=(e,r)=>{let t=i(e,r);return t&&t.prerelease.length?t.prerelease:null}},85444:e=>{"use strict";let r=Object.freeze({loose:!0}),t=Object.freeze({});e.exports=e=>e?"object"!=typeof e?r:e:t},85635:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>i(e,r,t)>0},87975:(e,r,t)=>{"use strict";let i=t(2527),s=t(95394);e.exports=(e,r,t)=>{let n=null,o=null,a=null;try{a=new s(r,t)}catch(e){return null}return e.forEach(e=>{a.test(e)&&(!n||1===o.compare(e))&&(o=new i(n=e,t))}),n}},88598:(e,r,t)=>{"use strict";let i=t(51197);e.exports=(e,r,t)=>0===i(e,r,t)},90945:(e,r,t)=>{"use strict";let i=t(2527);e.exports=(e,r,t=!1)=>{if(e instanceof i)return e;try{return new i(e,r)}catch(e){if(!t)return null;throw e}}},91367:(e,r,t)=>{var i=t(98874),s=function(e,r){i.call(this,e),this.name="NotBeforeError",this.date=r};s.prototype=Object.create(i.prototype),s.prototype.constructor=s,e.exports=s},91686:e=>{var r=1/0,t=0/0,i=/^\s+|\s+$/g,s=/^[-+]0x[0-9a-f]+$/i,n=/^0b[01]+$/i,o=/^0o[0-7]+$/i,a=parseInt,l=Object.prototype.toString;function u(e){var r=typeof e;return!!e&&("object"==r||"function"==r)}e.exports=function(e){var c,f,p;return"number"==typeof e&&e==(p=(f=(c=e)?(c=function(e){if("number"==typeof e)return e;if("symbol"==typeof(r=e)||r&&"object"==typeof r&&"[object Symbol]"==l.call(r))return t;if(u(e)){var r,c="function"==typeof e.valueOf?e.valueOf():e;e=u(c)?c+"":c}if("string"!=typeof e)return 0===e?e:+e;e=e.replace(i,"");var f=n.test(e);return f||o.test(e)?a(e.slice(2),f?2:8):s.test(e)?t:+e}(c))===r||c===-r?(c<0?-1:1)*17976931348623157e292:c==c?c:0:0===c?c:0)%1,f==f?p?f-p:f:0)}},94685:(e,r,t)=>{var i=t(97013);e.exports=function(e,r){r=r||{};var t=i.decode(e,r);if(!t)return null;var s=t.payload;if("string"==typeof s)try{var n=JSON.parse(s);null!==n&&"object"==typeof n&&(s=n)}catch(e){}return!0===r.complete?{header:t.header,payload:s,signature:t.signature}:s}},95394:(e,r,t)=>{"use strict";let i=/\s+/g;class s{constructor(e,r){if(r=o(r),e instanceof s)if(!!r.loose===e.loose&&!!r.includePrerelease===e.includePrerelease)return e;else return new s(e.raw,r);if(e instanceof a)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=r,this.loose=!!r.loose,this.includePrerelease=!!r.includePrerelease,this.raw=e.trim().replace(i," "),this.set=this.raw.split("||").map(e=>this.parseRange(e.trim())).filter(e=>e.length),!this.set.length)throw TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){let e=this.set[0];if(this.set=this.set.filter(e=>!y(e[0])),0===this.set.length)this.set=[e];else if(this.set.length>1){for(let e of this.set)if(1===e.length&&g(e[0])){this.set=[e];break}}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");let r=this.set[e];for(let e=0;e<r.length;e++)e>0&&(this.formatted+=" "),this.formatted+=r[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){let r=((this.options.includePrerelease&&E)|(this.options.loose&&d))+":"+e,t=n.get(r);if(t)return t;let i=this.options.loose,s=i?c[f.HYPHENRANGELOOSE]:c[f.HYPHENRANGE];l("hyphen replace",e=e.replace(s,N(this.options.includePrerelease))),l("comparator trim",e=e.replace(c[f.COMPARATORTRIM],p)),l("tilde trim",e=e.replace(c[f.TILDETRIM],h)),l("caret trim",e=e.replace(c[f.CARETTRIM],m));let o=e.split(" ").map(e=>b(e,this.options)).join(" ").split(/\s+/).map(e=>T(e,this.options));i&&(o=o.filter(e=>(l("loose invalid filter",e,this.options),!!e.match(c[f.COMPARATORLOOSE])))),l("range list",o);let u=new Map;for(let e of o.map(e=>new a(e,this.options))){if(y(e))return[e];u.set(e.value,e)}u.size>1&&u.has("")&&u.delete("");let g=[...u.values()];return n.set(r,g),g}intersects(e,r){if(!(e instanceof s))throw TypeError("a Range is required");return this.set.some(t=>v(t,r)&&e.set.some(e=>v(e,r)&&t.every(t=>e.every(e=>t.intersects(e,r)))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new u(e,this.options)}catch(e){return!1}for(let r=0;r<this.set.length;r++)if(x(this.set[r],e,this.options))return!0;return!1}}e.exports=s;let n=new(t(13603)),o=t(85444),a=t(28791),l=t(82259),u=t(2527),{safeRe:c,t:f,comparatorTrimReplace:p,tildeTrimReplace:h,caretTrimReplace:m}=t(97563),{FLAG_INCLUDE_PRERELEASE:E,FLAG_LOOSE:d}=t(65429),y=e=>"<0.0.0-0"===e.value,g=e=>""===e.value,v=(e,r)=>{let t=!0,i=e.slice(),s=i.pop();for(;t&&i.length;)t=i.every(e=>s.intersects(e,r)),s=i.pop();return t},b=(e,r)=>(l("comp",e,r),l("caret",e=w(e,r)),l("tildes",e=$(e,r)),l("xrange",e=O(e,r)),l("stars",e=L(e,r)),e),S=e=>!e||"x"===e.toLowerCase()||"*"===e,$=(e,r)=>e.trim().split(/\s+/).map(e=>R(e,r)).join(" "),R=(e,r)=>{let t=r.loose?c[f.TILDELOOSE]:c[f.TILDE];return e.replace(t,(r,t,i,s,n)=>{let o;return l("tilde",e,r,t,i,s,n),S(t)?o="":S(i)?o=`>=${t}.0.0 <${+t+1}.0.0-0`:S(s)?o=`>=${t}.${i}.0 <${t}.${+i+1}.0-0`:n?(l("replaceTilde pr",n),o=`>=${t}.${i}.${s}-${n} <${t}.${+i+1}.0-0`):o=`>=${t}.${i}.${s} <${t}.${+i+1}.0-0`,l("tilde return",o),o})},w=(e,r)=>e.trim().split(/\s+/).map(e=>I(e,r)).join(" "),I=(e,r)=>{l("caret",e,r);let t=r.loose?c[f.CARETLOOSE]:c[f.CARET],i=r.includePrerelease?"-0":"";return e.replace(t,(r,t,s,n,o)=>{let a;return l("caret",e,r,t,s,n,o),S(t)?a="":S(s)?a=`>=${t}.0.0${i} <${+t+1}.0.0-0`:S(n)?a="0"===t?`>=${t}.${s}.0${i} <${t}.${+s+1}.0-0`:`>=${t}.${s}.0${i} <${+t+1}.0.0-0`:o?(l("replaceCaret pr",o),a="0"===t?"0"===s?`>=${t}.${s}.${n}-${o} <${t}.${s}.${+n+1}-0`:`>=${t}.${s}.${n}-${o} <${t}.${+s+1}.0-0`:`>=${t}.${s}.${n}-${o} <${+t+1}.0.0-0`):(l("no pr"),a="0"===t?"0"===s?`>=${t}.${s}.${n}${i} <${t}.${s}.${+n+1}-0`:`>=${t}.${s}.${n}${i} <${t}.${+s+1}.0-0`:`>=${t}.${s}.${n} <${+t+1}.0.0-0`),l("caret return",a),a})},O=(e,r)=>(l("replaceXRanges",e,r),e.split(/\s+/).map(e=>A(e,r)).join(" ")),A=(e,r)=>{e=e.trim();let t=r.loose?c[f.XRANGELOOSE]:c[f.XRANGE];return e.replace(t,(t,i,s,n,o,a)=>{l("xRange",e,t,i,s,n,o,a);let u=S(s),c=u||S(n),f=c||S(o);return"="===i&&f&&(i=""),a=r.includePrerelease?"-0":"",u?t=">"===i||"<"===i?"<0.0.0-0":"*":i&&f?(c&&(n=0),o=0,">"===i?(i=">=",c?(s=+s+1,n=0):n=+n+1,o=0):"<="===i&&(i="<",c?s=+s+1:n=+n+1),"<"===i&&(a="-0"),t=`${i+s}.${n}.${o}${a}`):c?t=`>=${s}.0.0${a} <${+s+1}.0.0-0`:f&&(t=`>=${s}.${n}.0${a} <${s}.${+n+1}.0-0`),l("xRange return",t),t})},L=(e,r)=>(l("replaceStars",e,r),e.trim().replace(c[f.STAR],"")),T=(e,r)=>(l("replaceGTE0",e,r),e.trim().replace(c[r.includePrerelease?f.GTE0PRE:f.GTE0],"")),N=e=>(r,t,i,s,n,o,a,l,u,c,f,p)=>(t=S(i)?"":S(s)?`>=${i}.0.0${e?"-0":""}`:S(n)?`>=${i}.${s}.0${e?"-0":""}`:o?`>=${t}`:`>=${t}${e?"-0":""}`,l=S(u)?"":S(c)?`<${+u+1}.0.0-0`:S(f)?`<${u}.${+c+1}.0-0`:p?`<=${u}.${c}.${f}-${p}`:e?`<${u}.${c}.${+f+1}-0`:`<=${l}`,`${t} ${l}`.trim()),x=(e,r,t)=>{for(let t=0;t<e.length;t++)if(!e[t].test(r))return!1;if(r.prerelease.length&&!t.includePrerelease){for(let t=0;t<e.length;t++)if(l(e[t].semver),e[t].semver!==a.ANY&&e[t].semver.prerelease.length>0){let i=e[t].semver;if(i.major===r.major&&i.minor===r.minor&&i.patch===r.patch)return!0}return!1}return!0}},97013:(e,r,t)=>{var i=t(71741),s=t(30177);r.ALGORITHMS=["HS256","HS384","HS512","RS256","RS384","RS512","PS256","PS384","PS512","ES256","ES384","ES512"],r.sign=i.sign,r.verify=s.verify,r.decode=s.decode,r.isValid=s.isValid,r.createSign=function(e){return new i(e)},r.createVerify=function(e){return new s(e)}},97119:e=>{var r=Object.prototype.toString,t=Array.isArray;e.exports=function(e){var i;return"string"==typeof e||!t(e)&&!!(i=e)&&"object"==typeof i&&"[object String]"==r.call(e)}},97563:(e,r,t)=>{"use strict";let{MAX_SAFE_COMPONENT_LENGTH:i,MAX_SAFE_BUILD_LENGTH:s,MAX_LENGTH:n}=t(65429),o=t(82259),a=(r=e.exports={}).re=[],l=r.safeRe=[],u=r.src=[],c=r.safeSrc=[],f=r.t={},p=0,h="[a-zA-Z0-9-]",m=[["\\s",1],["\\d",n],[h,s]],E=e=>{for(let[r,t]of m)e=e.split(`${r}*`).join(`${r}{0,${t}}`).split(`${r}+`).join(`${r}{1,${t}}`);return e},d=(e,r,t)=>{let i=E(r),s=p++;o(e,s,r),f[e]=s,u[s]=r,c[s]=i,a[s]=new RegExp(r,t?"g":void 0),l[s]=new RegExp(i,t?"g":void 0)};d("NUMERICIDENTIFIER","0|[1-9]\\d*"),d("NUMERICIDENTIFIERLOOSE","\\d+"),d("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${h}*`),d("MAINVERSION",`(${u[f.NUMERICIDENTIFIER]})\\.(${u[f.NUMERICIDENTIFIER]})\\.(${u[f.NUMERICIDENTIFIER]})`),d("MAINVERSIONLOOSE",`(${u[f.NUMERICIDENTIFIERLOOSE]})\\.(${u[f.NUMERICIDENTIFIERLOOSE]})\\.(${u[f.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASEIDENTIFIER",`(?:${u[f.NONNUMERICIDENTIFIER]}|${u[f.NUMERICIDENTIFIER]})`),d("PRERELEASEIDENTIFIERLOOSE",`(?:${u[f.NONNUMERICIDENTIFIER]}|${u[f.NUMERICIDENTIFIERLOOSE]})`),d("PRERELEASE",`(?:-(${u[f.PRERELEASEIDENTIFIER]}(?:\\.${u[f.PRERELEASEIDENTIFIER]})*))`),d("PRERELEASELOOSE",`(?:-?(${u[f.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${u[f.PRERELEASEIDENTIFIERLOOSE]})*))`),d("BUILDIDENTIFIER",`${h}+`),d("BUILD",`(?:\\+(${u[f.BUILDIDENTIFIER]}(?:\\.${u[f.BUILDIDENTIFIER]})*))`),d("FULLPLAIN",`v?${u[f.MAINVERSION]}${u[f.PRERELEASE]}?${u[f.BUILD]}?`),d("FULL",`^${u[f.FULLPLAIN]}$`),d("LOOSEPLAIN",`[v=\\s]*${u[f.MAINVERSIONLOOSE]}${u[f.PRERELEASELOOSE]}?${u[f.BUILD]}?`),d("LOOSE",`^${u[f.LOOSEPLAIN]}$`),d("GTLT","((?:<|>)?=?)"),d("XRANGEIDENTIFIERLOOSE",`${u[f.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),d("XRANGEIDENTIFIER",`${u[f.NUMERICIDENTIFIER]}|x|X|\\*`),d("XRANGEPLAIN",`[v=\\s]*(${u[f.XRANGEIDENTIFIER]})(?:\\.(${u[f.XRANGEIDENTIFIER]})(?:\\.(${u[f.XRANGEIDENTIFIER]})(?:${u[f.PRERELEASE]})?${u[f.BUILD]}?)?)?`),d("XRANGEPLAINLOOSE",`[v=\\s]*(${u[f.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[f.XRANGEIDENTIFIERLOOSE]})(?:\\.(${u[f.XRANGEIDENTIFIERLOOSE]})(?:${u[f.PRERELEASELOOSE]})?${u[f.BUILD]}?)?)?`),d("XRANGE",`^${u[f.GTLT]}\\s*${u[f.XRANGEPLAIN]}$`),d("XRANGELOOSE",`^${u[f.GTLT]}\\s*${u[f.XRANGEPLAINLOOSE]}$`),d("COERCEPLAIN",`(^|[^\\d])(\\d{1,${i}})(?:\\.(\\d{1,${i}}))?(?:\\.(\\d{1,${i}}))?`),d("COERCE",`${u[f.COERCEPLAIN]}(?:$|[^\\d])`),d("COERCEFULL",u[f.COERCEPLAIN]+`(?:${u[f.PRERELEASE]})?`+`(?:${u[f.BUILD]})?`+"(?:$|[^\\d])"),d("COERCERTL",u[f.COERCE],!0),d("COERCERTLFULL",u[f.COERCEFULL],!0),d("LONETILDE","(?:~>?)"),d("TILDETRIM",`(\\s*)${u[f.LONETILDE]}\\s+`,!0),r.tildeTrimReplace="$1~",d("TILDE",`^${u[f.LONETILDE]}${u[f.XRANGEPLAIN]}$`),d("TILDELOOSE",`^${u[f.LONETILDE]}${u[f.XRANGEPLAINLOOSE]}$`),d("LONECARET","(?:\\^)"),d("CARETTRIM",`(\\s*)${u[f.LONECARET]}\\s+`,!0),r.caretTrimReplace="$1^",d("CARET",`^${u[f.LONECARET]}${u[f.XRANGEPLAIN]}$`),d("CARETLOOSE",`^${u[f.LONECARET]}${u[f.XRANGEPLAINLOOSE]}$`),d("COMPARATORLOOSE",`^${u[f.GTLT]}\\s*(${u[f.LOOSEPLAIN]})$|^$`),d("COMPARATOR",`^${u[f.GTLT]}\\s*(${u[f.FULLPLAIN]})$|^$`),d("COMPARATORTRIM",`(\\s*)${u[f.GTLT]}\\s*(${u[f.LOOSEPLAIN]}|${u[f.XRANGEPLAIN]})`,!0),r.comparatorTrimReplace="$1$2$3",d("HYPHENRANGE",`^\\s*(${u[f.XRANGEPLAIN]})\\s+-\\s+(${u[f.XRANGEPLAIN]})\\s*$`),d("HYPHENRANGELOOSE",`^\\s*(${u[f.XRANGEPLAINLOOSE]})\\s+-\\s+(${u[f.XRANGEPLAINLOOSE]})\\s*$`),d("STAR","(<|>)?=?\\s*\\*"),d("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),d("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},98874:e=>{var r=function(e,r){Error.call(this,e),Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor),this.name="JsonWebTokenError",this.message=e,r&&(this.inner=r)};r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,e.exports=r},99709:(e,r,t)=>{"use strict";var i=t(61280).Buffer,s=t(38932);function n(e){if(i.isBuffer(e))return e;if("string"==typeof e)return i.from(e,"base64");throw TypeError("ECDSA signature must be a Base64 string or a Buffer")}function o(e,r,t){for(var i=0;r+i<t&&0===e[r+i];)++i;return e[r+i]>=128&&--i,i}e.exports={derToJose:function(e,r){e=n(e);var t=s(r),o=t+1,a=e.length,l=0;if(48!==e[l++])throw Error('Could not find expected "seq"');var u=e[l++];if(129===u&&(u=e[l++]),a-l<u)throw Error('"seq" specified length of "'+u+'", only "'+(a-l)+'" remaining');if(2!==e[l++])throw Error('Could not find expected "int" for "r"');var c=e[l++];if(a-l-2<c)throw Error('"r" specified length of "'+c+'", only "'+(a-l-2)+'" available');if(o<c)throw Error('"r" specified length of "'+c+'", max of "'+o+'" is acceptable');var f=l;if(l+=c,2!==e[l++])throw Error('Could not find expected "int" for "s"');var p=e[l++];if(a-l!==p)throw Error('"s" specified length of "'+p+'", expected "'+(a-l)+'"');if(o<p)throw Error('"s" specified length of "'+p+'", max of "'+o+'" is acceptable');var h=l;if((l+=p)!==a)throw Error('Expected to consume entire buffer, but "'+(a-l)+'" bytes remain');var m=t-c,E=t-p,d=i.allocUnsafe(m+c+E+p);for(l=0;l<m;++l)d[l]=0;e.copy(d,l,f+Math.max(-m,0),f+c),l=t;for(var y=l;l<y+E;++l)d[l]=0;return e.copy(d,l,h+Math.max(-E,0),h+p),d=(d=d.toString("base64")).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")},joseToDer:function(e,r){e=n(e);var t=s(r),a=e.length;if(a!==2*t)throw TypeError('"'+r+'" signatures must be "'+2*t+'" bytes, saw "'+a+'"');var l=o(e,0,t),u=o(e,t,e.length),c=t-l,f=t-u,p=2+c+1+1+f,h=p<128,m=i.allocUnsafe((h?2:3)+p),E=0;return m[E++]=48,h?m[E++]=p:(m[E++]=129,m[E++]=255&p),m[E++]=2,m[E++]=c,l<0?(m[E++]=0,E+=e.copy(m,E,0,t)):E+=e.copy(m,E,l,t),m[E++]=2,m[E++]=f,u<0?(m[E++]=0,e.copy(m,E,t)):e.copy(m,E,t+u),m}}}};