module.exports = {

"[project]/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/compiled/react-dom/server.js [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/68053_next_dist_compiled_ecc04fd6._.js",
  "server/chunks/[root-of-the-server]__365b6c9b._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/compiled/react-dom/server.js [app-route] (ecmascript)");
    });
});
}}),

};