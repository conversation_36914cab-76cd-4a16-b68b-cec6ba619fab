exports.id=6964,exports.ids=[6964],exports.modules={1550:(e,t,s)=>{"use strict";s.d(t,{A:()=>i});var r=s(61438),a=s(18658);let i=(0,r.v)()((0,a.Zr)((e,t)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async(t,s)=>{e({isLoading:!0});try{let r=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:t,password:s})}),a=await r.json();if(!r.ok)throw Error(a.error||"Erreur de connexion");e({user:a.user,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},register:async t=>{e({isLoading:!0});try{let s=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)}),r=await s.json();if(!s.ok)throw Error(r.error||"Erreur d'inscription");e({user:r.user,isAuthenticated:!0,isLoading:!1})}catch(t){throw e({isLoading:!1}),t}},logout:async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),e({user:null,isAuthenticated:!1})}catch(t){console.error("Erreur lors de la d\xe9connexion:",t),e({user:null,isAuthenticated:!1})}},checkAuth:async()=>{try{let t=await fetch("/api/auth/me",{credentials:"include"});if(t.ok){let s=await t.json();e({user:s.user,isAuthenticated:!0})}else e({user:null,isAuthenticated:!1})}catch(t){console.error("Erreur lors de la v\xe9rification de l'authentification:",t),e({user:null,isAuthenticated:!1})}},setUser:t=>{e({user:t,isAuthenticated:!!t})},setLoading:t=>{e({isLoading:t})},isAdmin:()=>{let{user:e}=t();return e?.role==="ADMIN"}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},7493:(e,t,s)=>{"use strict";s.d(t,{AdminLayout:()=>n,AuthLayout:()=>i,ErrorLayout:()=>a,Layout:()=>l});var r=s(54560);let a=(0,r.registerClientReference)(function(){throw Error("Attempted to call ErrorLayout() from the server but ErrorLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx","ErrorLayout"),i=(0,r.registerClientReference)(function(){throw Error("Attempted to call AuthLayout() from the server but AuthLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx","AuthLayout"),n=(0,r.registerClientReference)(function(){throw Error("Attempted to call AdminLayout() from the server but AdminLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx","AdminLayout"),l=(0,r.registerClientReference)(function(){throw Error("Attempted to call Layout() from the server but Layout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Layout.tsx","Layout")},13045:(e,t,s)=>{"use strict";s.d(t,{N:()=>m});var r=s(166),a=s(23705),i=s(93666),n=s(94102),l=s(66212);s(14791);var o=s(7799),c=s(40651);let d=c.Ik({firstName:c.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:c.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:c.Yj().email("Email invalide"),phone:c.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide"),address:c.Yj().min(5,"Adresse trop courte"),city:c.Yj().min(2,"Ville requise"),postalCode:c.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:c.Yj().min(2,"Pays requis"),deliveryInstructions:c.Yj().optional()}),m=({onNext:e,isProcessing:t})=>{let{customer:s,updateCustomer:c}=(0,i.cN)(),{addNotification:m}=(0,i.E$)(),{closeModal:x}=(0,i.Je)(),{register:u,handleSubmit:p,formState:{errors:h,isValid:g},setValue:f,watch:y}=(0,o.mN)({resolver:(0,n.u)(d),defaultValues:{firstName:s?.firstName||"",lastName:s?.lastName||"",email:s?.email||"",phone:s?.phone||"",address:"",city:"",postalCode:"",country:"France",deliveryInstructions:""}}),b=async t=>{try{c({firstName:t.firstName,lastName:t.lastName,email:t.email,phone:t.phone}),m({type:"success",title:"Livraison",message:"Informations de livraison sauvegard\xe9es"}),e()}catch(e){m({type:"error",title:"Erreur",message:"Erreur lors de la sauvegarde"})}};return(0,r.jsxs)(l.P.form,{onSubmit:p(b),className:"space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-blue-800",children:"Mode d\xe9monstration"}),(0,r.jsx)("p",{className:"text-sm text-blue-600",children:"Remplir avec des donn\xe9es de test"})]}),(0,r.jsx)(a.$n,{type:"button",variant:"outline",size:"sm",onClick:()=>{f("firstName","Marie"),f("lastName","Dupont"),f("email","<EMAIL>"),f("phone","0123456789"),f("address","123 Rue de la Paix"),f("city","Paris"),f("postalCode","75001"),f("country","France"),f("deliveryInstructions","Laisser devant la porte si absent")},className:"border-blue-300 text-blue-600 hover:bg-blue-100",children:"Remplir automatiquement"})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pr\xe9nom *"}),(0,r.jsx)(a.pd,{...u("firstName"),placeholder:"Votre pr\xe9nom",error:h.firstName?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom *"}),(0,r.jsx)(a.pd,{...u("lastName"),placeholder:"Votre nom",error:h.lastName?.message})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),(0,r.jsx)(a.pd,{...u("email"),type:"email",placeholder:"<EMAIL>",error:h.email?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xe9l\xe9phone *"}),(0,r.jsx)(a.pd,{...u("phone"),type:"tel",placeholder:"01 23 45 67 89",error:h.phone?.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adresse *"}),(0,r.jsx)(a.pd,{...u("address"),placeholder:"123 Rue de la Paix",error:h.address?.message})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ville *"}),(0,r.jsx)(a.pd,{...u("city"),placeholder:"Paris",error:h.city?.message})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Code postal *"}),(0,r.jsx)(a.pd,{...u("postalCode"),placeholder:"75001",error:h.postalCode?.message})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pays *"}),(0,r.jsxs)(a.l6,{...u("country"),error:h.country?.message,children:[(0,r.jsx)("option",{value:"France",children:"France"}),(0,r.jsx)("option",{value:"Belgique",children:"Belgique"}),(0,r.jsx)("option",{value:"Suisse",children:"Suisse"}),(0,r.jsx)("option",{value:"Luxembourg",children:"Luxembourg"}),(0,r.jsx)("option",{value:"Monaco",children:"Monaco"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Instructions de livraison (optionnel)"}),(0,r.jsx)(a.TM,{...u("deliveryInstructions"),placeholder:"Laisser devant la porte, sonner chez le voisin, etc.",rows:3})]}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Options de livraison"}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"deliveryOption",value:"standard",defaultChecked:!0,className:"text-pink-500 focus:ring-pink-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium text-gray-800",children:"Livraison standard"}),(0,r.jsx)("span",{className:"text-green-600 font-medium",children:"Gratuite"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"3-5 jours ouvr\xe9s"})]})]}),(0,r.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,r.jsx)("input",{type:"radio",name:"deliveryOption",value:"express",className:"text-pink-500 focus:ring-pink-500"}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"font-medium text-gray-800",children:"Livraison express"}),(0,r.jsx)("span",{className:"text-gray-800 font-medium",children:"4,99 €"})]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:"24-48h"})]})]})]})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,r.jsx)("button",{type:"button",onClick:x,className:"text-gray-600 hover:text-gray-800 transition-colors",children:"← Retour au panier"}),(0,r.jsx)(a.$n,{type:"submit",variant:"primary",size:"lg",disabled:!g||t,className:"min-w-[200px]",children:t?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(l.P.span,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:"\uD83C\uDF6D"}),"Traitement..."]}):(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-2",children:"\uD83D\uDCB3"}),"Continuer vers le paiement"]})})]})]})}},13683:(e,t,s)=>{"use strict";s.d(t,{D:()=>o});var r=s(166),a=s(23705),i=s(26367),n=s(93666),l=s(66212);s(14791);let o=()=>{let{cart:e}=(0,n._$)(),t={strawberry:{emoji:"\uD83C\uDF53",color:"text-pink-600",bg:"bg-pink-50"},blueberry:{emoji:"\uD83E\uDED0",color:"text-blue-600",bg:"bg-blue-50"},apple:{emoji:"\uD83C\uDF4F",color:"text-green-600",bg:"bg-green-50"}},s=e.totalAmount,o=.2*s,c=s>=50?0:5.99,d=s+o+c;return(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"R\xe9capitulatif de commande"})}),(0,r.jsxs)(a.Wu,{className:"space-y-6",children:[(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("h4",{className:"font-medium text-gray-800 border-b border-gray-200 pb-2",children:["Articles command\xe9s (",e.totalItems,")"]}),(0,r.jsx)("div",{className:"space-y-3 max-h-60 overflow-y-auto",children:e.items.map(e=>{let s=t[e.flavor.toLowerCase()]||{emoji:"\uD83C\uDF6D",color:"text-gray-600",bg:"bg-gray-50"};return(0,r.jsxs)(l.P.div,{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:[(0,r.jsx)("div",{className:`w-10 h-10 rounded-lg ${s.bg} flex items-center justify-center text-lg`,children:s.emoji}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h5",{className:"font-medium text-gray-800 text-sm truncate",children:e.name}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,r.jsx)(a.Ex,{variant:"secondary",size:"sm",className:`${s.color} border-current text-xs`,children:e.flavor}),(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:["Qt\xe9: ",e.quantity]})]})]}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:(0,i.$g)(e.price*e.quantity)})})]},e.id)})})]}),(0,r.jsxs)("div",{className:"space-y-3 border-t border-gray-200 pt-4",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Sous-total"}),(0,r.jsx)("span",{className:"font-medium",children:(0,i.$g)(s)})]}),!1,(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"Livraison"}),(0,r.jsx)("span",{className:"font-medium",children:0===c?(0,r.jsx)("span",{className:"text-green-600",children:"Gratuite"}):(0,i.$g)(c)})]}),o>0&&(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-600",children:"TVA (20%)"}),(0,r.jsx)("span",{className:"font-medium",children:(0,i.$g)(o)})]})]}),(0,r.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,r.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,r.jsx)("span",{children:"Total \xe0 payer"}),(0,r.jsx)(l.P.span,{className:"text-pink-600",initial:{scale:1.1},animate:{scale:1},transition:{duration:.2},children:(0,i.$g)(d)},d)]})}),(0,r.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,r.jsx)("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDE9A"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-blue-800 text-sm",children:"Livraison estim\xe9e"}),(0,r.jsx)("p",{className:"text-blue-600 text-sm",children:0===c?"3-5 jours ouvr\xe9s":"24-48h"}),(0,r.jsx)("p",{className:"text-blue-600 text-xs mt-1",children:"Suivi de commande par email"})]})]})}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-center text-xs text-gray-500",children:[(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("span",{className:"text-lg mb-1",children:"\uD83D\uDD12"}),(0,r.jsx)("span",{children:"Paiement s\xe9curis\xe9"})]}),(0,r.jsxs)("div",{className:"flex flex-col items-center",children:[(0,r.jsx)("span",{className:"text-lg mb-1",children:"↩️"}),(0,r.jsx)("span",{children:"Retour gratuit"})]})]}),(0,r.jsx)("div",{className:"bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg p-3",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("p",{className:"text-sm font-medium text-gray-800",children:"Points de fid\xe9lit\xe9"}),(0,r.jsxs)("p",{className:"text-xs text-gray-600",children:["Vous gagnerez ",Math.floor(d/100)," points"]})]}),(0,r.jsxs)(a.Ex,{variant:"info",size:"sm",children:["+",Math.floor(d/100)," \uD83C\uDF81"]})]})}),(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Une question sur votre commande ?"}),(0,r.jsx)("button",{className:"text-xs text-pink-600 hover:text-pink-700 font-medium",children:"Contacter le support \uD83D\uDCAC"})]})]})]})}},16370:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,6635,23)),Promise.resolve().then(s.t.bind(s,54663,23)),Promise.resolve().then(s.t.bind(s,87979,23)),Promise.resolve().then(s.t.bind(s,11562,23)),Promise.resolve().then(s.t.bind(s,47294,23)),Promise.resolve().then(s.t.bind(s,30002,23)),Promise.resolve().then(s.t.bind(s,39240,23)),Promise.resolve().then(s.t.bind(s,83738,23))},23705:(e,t,s)=>{"use strict";s.d(t,{Ex:()=>j,$n:()=>o,Zp:()=>d,Wu:()=>u,aR:()=>m,ZB:()=>x,pd:()=>y,aF:()=>h,cw:()=>g,jl:()=>f,l6:()=>v,TM:()=>b,N9:()=>w});var r=s(166),a=s(26367),i=s(66212),n=s(14791),l=s.n(n);let o=l().forwardRef(({className:e,variant:t="primary",size:s="md",loading:n=!1,icon:l,iconPosition:o="left",fullWidth:c=!1,rounded:d=!1,children:m,disabled:x,...u},p)=>{let h=(0,a.cn)(["inline-flex items-center justify-center font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","relative overflow-hidden"],{primary:["bg-gradient-to-r from-pink-500 to-orange-400","hover:from-pink-600 hover:to-orange-500","text-white shadow-lg hover:shadow-xl","focus:ring-pink-500"],secondary:["bg-gradient-to-r from-purple-500 to-blue-500","hover:from-purple-600 hover:to-blue-600","text-white shadow-lg hover:shadow-xl","focus:ring-purple-500"],outline:["border-2 border-pink-500 text-pink-500","hover:bg-pink-500 hover:text-white","focus:ring-pink-500"],ghost:["text-gray-700 hover:text-pink-500","hover:bg-pink-50","focus:ring-pink-500"],danger:["bg-red-500 hover:bg-red-600","text-white shadow-lg hover:shadow-xl","focus:ring-red-500"]}[t],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg",xl:"px-8 py-4 text-xl"}[s],{sm:d?"rounded-full":"rounded-md",md:d?"rounded-full":"rounded-lg",lg:d?"rounded-full":"rounded-xl",xl:d?"rounded-full":"rounded-2xl"}[s],c&&"w-full",e),g={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-7 h-7"},{onDrag:f,onDragStart:y,onDragEnd:b,onAnimationStart:v,onAnimationEnd:j,onAnimationIteration:N,...w}=u;return(0,r.jsx)(i.P.button,{ref:p,className:h,disabled:x||n,whileHover:x||n?void 0:{scale:1.02},whileTap:x||n?void 0:{scale:.98},...w,children:n?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(()=>(0,r.jsx)(i.P.div,{className:(0,a.cn)("border-2 border-current border-t-transparent rounded-full",g[s]),animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),{}),m&&(0,r.jsx)("span",{className:"ml-2",children:"Chargement..."})]}):l&&"left"===o?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("span",{className:(0,a.cn)(g[s],m&&"mr-2"),children:l}),m]}):l&&"right"===o?(0,r.jsxs)(r.Fragment,{children:[m,(0,r.jsx)("span",{className:(0,a.cn)(g[s],m&&"ml-2"),children:l})]}):m})});o.displayName="Button";var c=s(45276);let d=l().forwardRef(({className:e,variant:t="default",padding:s="md",rounded:n="lg",hover:l=!1,clickable:o=!1,gradient:d=!1,children:m,...x},u)=>{let p=(0,a.cn)(["relative overflow-hidden transition-all duration-300"],{default:["bg-white border border-gray-200","shadow-sm"],elevated:["bg-white","shadow-lg hover:shadow-xl"],outlined:["bg-white border-2 border-gray-300","hover:border-pink-300"],glass:["bg-white/80 backdrop-blur-sm","border border-white/20","shadow-lg"]}[t],{none:"",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"}[s],{none:"",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"}[n],l?["hover:shadow-lg hover:-translate-y-1","hover:scale-105"]:[],o?["cursor-pointer","hover:shadow-lg hover:-translate-y-1","active:scale-95"]:[],d?["bg-gradient-to-br from-pink-50 to-orange-50","border-gradient-to-r from-pink-200 to-orange-200"]:[],e),h=i.P.div;return(0,r.jsx)(h,{ref:u,className:p,initial:!1,animate:c.Yo.animate,whileHover:l||o?{y:-4,scale:1.02}:void 0,whileTap:o?{scale:.98}:void 0,...(({onAnimationStart:e,onAnimationEnd:t,onAnimationIteration:s,onDrag:r,onDragStart:a,onDragEnd:i,...n})=>n)(x),children:m})});d.displayName="Card";let m=l().forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex flex-col space-y-1.5 p-6 pb-0",e),...t}));m.displayName="CardHeader";let x=l().forwardRef(({className:e,...t},s)=>(0,r.jsx)("h3",{ref:s,className:(0,a.cn)("text-lg font-semibold leading-none tracking-tight",e),...t}));x.displayName="CardTitle",l().forwardRef(({className:e,...t},s)=>(0,r.jsx)("p",{ref:s,className:(0,a.cn)("text-sm text-gray-600",e),...t})).displayName="CardDescription";let u=l().forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("p-6 pt-0",e),...t}));u.displayName="CardContent",l().forwardRef(({className:e,...t},s)=>(0,r.jsx)("div",{ref:s,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter",s(86214),s(35952),s(49537);var p=s(16224);s(1486);let h=({isOpen:e,onClose:t,title:s,description:r,size:l="md",closeOnOverlayClick:o=!0,closeOnEscape:c=!0,showCloseButton:d=!0,children:m,className:x,overlayClassName:u})=>((0,n.useEffect)(()=>{if(!c)return;let s=s=>{"Escape"===s.key&&e&&t()};return document.addEventListener("keydown",s),()=>document.removeEventListener("keydown",s)},[e,t,c]),(0,n.useRef)(null),(0,n.useEffect)(()=>(e?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[e]),(0,a.cn)("fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4","bg-black/50 backdrop-blur-sm",u),(0,a.cn)("relative w-full bg-white rounded-lg sm:rounded-xl shadow-2xl","max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",{sm:"max-w-sm sm:max-w-md",md:"max-w-md sm:max-w-lg",lg:"max-w-lg sm:max-w-xl lg:max-w-2xl",xl:"max-w-xl sm:max-w-2xl lg:max-w-4xl",full:"max-w-full mx-2 sm:mx-4"}[l],x),e&&(p.N,e&&(i.P.div,i.P.div)),null),g=({children:e,className:t})=>(0,r.jsx)("div",{className:(0,a.cn)("p-6",t),children:e}),f=({children:e,className:t})=>(0,r.jsx)("div",{className:(0,a.cn)("flex items-center justify-end gap-3 p-6 border-t border-gray-200",t),children:e}),y=l().forwardRef(({className:e,type:t="text",label:s,error:n,helperText:o,leftIcon:c,rightIcon:d,variant:m="default",inputSize:x="md",fullWidth:u=!1,disabled:p,...h},g)=>{let[f,y]=l().useState(!1),b=(0,a.cn)(["transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-1","disabled:opacity-50 disabled:cursor-not-allowed"],{default:["border border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",n?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""],filled:["border-0 bg-gray-50 text-gray-900","placeholder:text-gray-700","hover:bg-gray-100","focus:bg-white focus:ring-pink-500/20 focus:shadow-md","shadow-sm",n?"bg-red-50 focus:ring-red-500/20":""],outlined:["border-2 border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",n?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""]}[m],{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-base",lg:"px-5 py-3 text-lg"}[x],{sm:"rounded-md",md:"rounded-lg",lg:"rounded-xl"}[x],c&&"pl-10",d&&"pr-10",u&&"w-full",e),v={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,r.jsxs)("div",{className:(0,a.cn)("relative",u&&"w-full"),children:[s&&(0,r.jsx)(i.P.label,{className:(0,a.cn)("block text-sm font-medium mb-2 transition-colors",n?"text-red-700":"text-gray-700",p&&"text-gray-400"),animate:{color:f?n?"#dc2626":"#ec4899":n?"#dc2626":"#374151"},children:s}),(0,r.jsxs)("div",{className:"relative",children:[c&&(0,r.jsx)("div",{className:(0,a.cn)("absolute top-1/2 transform -translate-y-1/2 text-gray-400",{sm:"left-3",md:"left-3",lg:"left-4"}[x]),children:(0,r.jsx)("span",{className:v[x],children:c})}),(0,r.jsx)(i.P.input,{ref:g,type:t,className:b,disabled:p,onFocus:e=>{y(!0),h.onFocus?.(e)},onBlur:e=>{y(!1),h.onBlur?.(e)},whileFocus:{scale:1.01},...(({onAnimationStart:e,onAnimationEnd:t,onAnimationIteration:s,onDrag:r,onDragStart:a,onDragEnd:i,...n})=>n)(h)}),d&&(0,r.jsx)("div",{className:(0,a.cn)("absolute top-1/2 transform -translate-y-1/2 text-gray-400",{sm:"right-3",md:"right-3",lg:"right-4"}[x]),children:(0,r.jsx)("span",{className:v[x],children:d})})]}),n&&(0,r.jsx)(i.P.p,{className:"mt-1 text-sm text-red-600",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:n}),o&&!n&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:o})]})});y.displayName="Input";let b=l().forwardRef(({className:e,label:t,error:s,helperText:n,variant:o="default",inputSize:c="md",fullWidth:d=!1,resize:m="vertical",disabled:x,...u},p)=>{let[h,g]=l().useState(!1),f=(0,a.cn)(["transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-1","disabled:opacity-50 disabled:cursor-not-allowed","min-h-[80px]"],{default:["border border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",s?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""],filled:["border-0 bg-gray-50 text-gray-900","placeholder:text-gray-700","hover:bg-gray-100","focus:bg-white focus:ring-pink-500/20 focus:shadow-md","shadow-sm",s?"bg-red-50 focus:ring-red-500/20":""],outlined:["border-2 border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",s?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""]}[o],{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-base",lg:"px-5 py-3 text-lg"}[c],{sm:"rounded-md",md:"rounded-lg",lg:"rounded-xl"}[c],{none:"resize-none",vertical:"resize-y",horizontal:"resize-x",both:"resize"}[m],d&&"w-full",e);return(0,r.jsxs)("div",{className:(0,a.cn)("relative",d&&"w-full"),children:[t&&(0,r.jsx)(i.P.label,{className:(0,a.cn)("block text-sm font-medium mb-2 transition-colors",s?"text-red-700":"text-gray-700",x&&"text-gray-400"),animate:{color:h?s?"#dc2626":"#ec4899":s?"#dc2626":"#374151"},children:t}),(0,r.jsx)(i.P.textarea,{ref:p,className:f,disabled:x,onFocus:e=>{g(!0),u.onFocus?.(e)},onBlur:e=>{g(!1),u.onBlur?.(e)},whileFocus:{scale:1.01},...(({onAnimationStart:e,onAnimationEnd:t,onAnimationIteration:s,onDrag:r,onDragStart:a,onDragEnd:i,...n})=>n)(u)}),s&&(0,r.jsx)(i.P.p,{className:"mt-1 text-sm text-red-600",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:s}),n&&!s&&(0,r.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:n})]})});b.displayName="Textarea";let v=(0,n.forwardRef)(({className:e,label:t,error:s,helperText:i,variant:n="default",size:l="md",children:o,...c},d)=>{let m=s?"border-red-500 focus:border-red-500 focus:ring-red-500":"";return(0,r.jsxs)("div",{className:"w-full",children:[t&&(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:t}),(0,r.jsx)("select",{ref:d,className:(0,a.cn)("w-full rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{default:"border-gray-300 bg-white focus:border-pink-500 focus:ring-pink-500",outline:"border-gray-300 bg-transparent focus:border-pink-500 focus:ring-pink-500",filled:"border-gray-200 bg-gray-50 focus:border-pink-500 focus:ring-pink-500"}[n],{sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-5 py-4 text-lg"}[l],m,e),...c,children:o}),s&&(0,r.jsx)("p",{className:"mt-2 text-sm text-red-600",children:s}),i&&!s&&(0,r.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:i})]})});v.displayName="Select";let j=l().forwardRef(({className:e,variant:t="default",size:s="md",rounded:n=!1,outline:l=!1,icon:o,iconPosition:c="left",removable:d=!1,onRemove:m,children:x,...u},p)=>{let h={sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"},g=(0,a.cn)(["inline-flex items-center font-medium transition-all duration-200","whitespace-nowrap"],{default:l?"border border-gray-300 text-gray-700 bg-transparent hover:bg-gray-50":"bg-gray-100 text-gray-800 hover:bg-gray-200",primary:l?"border border-pink-300 text-pink-700 bg-transparent hover:bg-pink-50":"bg-gradient-to-r from-pink-500 to-orange-400 text-white hover:from-pink-600 hover:to-orange-500",secondary:l?"border border-purple-300 text-purple-700 bg-transparent hover:bg-purple-50":"bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600",success:l?"border border-green-300 text-green-700 bg-transparent hover:bg-green-50":"bg-green-100 text-green-800 hover:bg-green-200",warning:l?"border border-yellow-300 text-yellow-700 bg-transparent hover:bg-yellow-50":"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",danger:l?"border border-red-300 text-red-700 bg-transparent hover:bg-red-50":"bg-red-100 text-red-800 hover:bg-red-200",info:l?"border border-blue-300 text-blue-700 bg-transparent hover:bg-blue-50":"bg-blue-100 text-blue-800 hover:bg-blue-200"}[t],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[s],{sm:n?"rounded-full":"rounded",md:n?"rounded-full":"rounded-md",lg:n?"rounded-full":"rounded-lg"}[s],e),f=e=>o&&c===e?(0,r.jsx)("span",{className:(0,a.cn)(h[s],"left"===e&&x&&"mr-1","right"===e&&x&&"ml-1"),children:o}):null,{onAnimationStart:y,onAnimationEnd:b,onAnimationIteration:v,onDrag:j,onDragStart:N,onDragEnd:w,...k}=u;return(0,r.jsxs)(i.P.div,{ref:p,className:g,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},whileHover:{scale:1.05},whileTap:{scale:.95},...k,children:[f("left"),x,f("right"),d?(0,r.jsx)("button",{onClick:m,className:(0,a.cn)("ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors",h[s]),"aria-label":"Supprimer",children:(0,r.jsx)("svg",{className:"w-full h-full",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):null]})});j.displayName="Badge";var N=s(93666);let w=()=>{let{notifications:e,removeNotification:t}=(0,N.E$)();return null};s(59093)},26367:(e,t,s)=>{"use strict";s.d(t,{wy:()=>o,te:()=>l,cn:()=>i,$g:()=>n});var r=s(62508),a=s(72828);function i(...e){return(0,a.QP)((0,r.$)(e))}function n(e,t="EUR"){return new Intl.NumberFormat("fr-FR",{style:"currency",currency:t}).format(e)}process.env.NEXTAUTH_URL;function l(e){return e.reduce((e,t)=>e+t.price*t.quantity,0)}function o(e){return e.reduce((e,t)=>e+t.quantity,0)}},33659:(e,t,s)=>{"use strict";s.d(t,{b:()=>d});var r=s(166),a=s(23705),i=s(26367),n=s(93666),l=s(66212),o=s(16224),c=s(14791);let d=({onBack:e,onSuccess:t,isProcessing:s,setIsProcessing:d})=>{let{cart:m,clearCart:x}=(0,n._$)(),{customer:u}=(0,n.cN)(),{addNotification:p}=(0,n.E$)(),[h,g]=(0,c.useState)("card"),[f,y]=(0,c.useState)({number:"",expiry:"",cvc:"",name:""}),b=(e,t)=>{let s=t;"number"===e?(s=t.replace(/\s/g,"").replace(/(.{4})/g,"$1 ").trim()).length>19&&(s=s.slice(0,19)):"expiry"===e?(s=t.replace(/\D/g,"").replace(/(\d{2})(\d)/,"$1/$2")).length>5&&(s=s.slice(0,5)):"cvc"===e&&(s=t.replace(/\D/g,"").slice(0,4)),y(t=>({...t,[e]:s}))},v=()=>{let{number:e,expiry:t,cvc:s,name:r}=f;return!e||e.replace(/\s/g,"").length<16?(p({type:"error",title:"Paiement",message:"Num\xe9ro de carte invalide"}),!1):!t||t.length<5?(p({type:"error",title:"Paiement",message:"Date d'expiration invalide"}),!1):!s||s.length<3?(p({type:"error",title:"Paiement",message:"Code CVC invalide"}),!1):!!r.trim()||(p({type:"error",title:"Paiement",message:"Nom du titulaire requis"}),!1)},j=async()=>{if("card"!==h||v()){d(!0);try{if(0===m.items.length)return void p({type:"error",title:"Panier vide",message:"Votre panier est vide. Ajoutez des produits avant de proc\xe9der au paiement."});let e=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...u?.id&&{customerId:u.id},items:m.items.map(e=>({productId:e.productId,variantId:e.variantId,quantity:e.quantity})),shippingAddress:{firstName:u?.firstName||"Client",lastName:u?.lastName||"Deltagum",email:u?.email||"<EMAIL>",phone:u?.phone||"0123456789",street:u?.address||"123 Rue de la Livraison",city:u?.city||"Paris",postalCode:u?.postalCode||"75001",country:"France"},totalAmount:m.totalAmount})});if(!e.ok)throw Error("Erreur lors de la cr\xe9ation de la commande");let{order:t}=await e.json(),s=await fetch("/api/checkout/session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:t.id})});if(!s.ok)throw Error("Erreur lors de la cr\xe9ation de la session de paiement");let{data:r}=await s.json();if(r.url)localStorage.setItem("deltagum_pending_order",JSON.stringify({orderId:t.id,cartItems:m.items,timestamp:Date.now()})),window.location.href=r.url;else throw Error("URL de paiement non re\xe7ue")}catch(e){console.error("Erreur de paiement:",e),p({type:"error",title:"Erreur de paiement",message:e instanceof Error?e.message:"Une erreur est survenue lors du paiement"}),d(!1)}}};return(0,r.jsxs)(l.P.div,{className:"space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-4",children:"Choisissez votre m\xe9thode de paiement"}),(0,r.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[{id:"card",name:"Carte bancaire",icon:"\uD83D\uDCB3",description:"Visa, Mastercard, American Express",available:!0},{id:"paypal",name:"PayPal",icon:"\uD83C\uDD7F️",description:"Paiement s\xe9curis\xe9 avec PayPal",available:!0},{id:"apple_pay",name:"Apple Pay",icon:"\uD83C\uDF4E",description:"Paiement rapide avec Touch ID",available:!1},{id:"google_pay",name:"Google Pay",icon:"\uD83D\uDD35",description:"Paiement rapide avec Google",available:!1}].map(e=>(0,r.jsx)(l.P.button,{onClick:()=>e.available&&g(e.id),disabled:!e.available,className:`
                p-4 rounded-lg border-2 text-left transition-all duration-200
                ${h===e.id?"border-pink-500 bg-pink-50":e.available?"border-gray-200 hover:border-gray-300 bg-white":"border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed"}
              `,whileHover:e.available?{scale:1.02}:{},whileTap:e.available?{scale:.98}:{},children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-2xl",children:e.icon}),(0,r.jsxs)("div",{className:"flex-1",children:[(0,r.jsx)("h5",{className:"font-medium text-gray-800",children:e.name}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),!e.available&&(0,r.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Bient\xf4t disponible"})]}),h===e.id&&(0,r.jsx)(l.P.div,{className:"w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300},children:(0,r.jsx)("span",{className:"text-white text-xs",children:"✓"})})]})},e.id))})]}),(0,r.jsxs)(o.N,{mode:"wait",children:["card"===h&&(0,r.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsx)(a.Zp,{children:(0,r.jsxs)(a.Wu,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-800",children:"Informations de carte"}),(0,r.jsx)(a.$n,{type:"button",variant:"outline",size:"sm",onClick:()=>{y({number:"4242 4242 4242 4242",expiry:"12/25",cvc:"123",name:u?.firstName&&u?.lastName?`${u.firstName} ${u.lastName}`:"Marie Dupont"})},className:"text-xs",children:"Donn\xe9es de test"})]}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Num\xe9ro de carte"}),(0,r.jsx)("input",{type:"text",value:f.number,onChange:e=>b("number",e.target.value),placeholder:"1234 5678 9012 3456",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date d'expiration"}),(0,r.jsx)("input",{type:"text",value:f.expiry,onChange:e=>b("expiry",e.target.value),placeholder:"MM/YY",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVC"}),(0,r.jsx)("input",{type:"text",value:f.cvc,onChange:e=>b("cvc",e.target.value),placeholder:"123",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom du titulaire"}),(0,r.jsx)("input",{type:"text",value:f.name,onChange:e=>b("name",e.target.value),placeholder:"Marie Dupont",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]})]})]})})},"card-form"),"paypal"===h&&(0,r.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,r.jsx)(a.Zp,{children:(0,r.jsxs)(a.Wu,{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"text-4xl mb-4",children:"\uD83C\uDD7F️"}),(0,r.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"Paiement PayPal"}),(0,r.jsx)("p",{className:"text-gray-600 text-sm",children:"Vous serez redirig\xe9 vers PayPal pour finaliser votre paiement de mani\xe8re s\xe9curis\xe9e."})]})})},"paypal-info")]}),(0,r.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"text-green-600 text-xl",children:"\uD83D\uDD12"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-green-800",children:"Paiement 100% s\xe9curis\xe9"}),(0,r.jsx)("p",{className:"text-sm text-green-600",children:"Vos donn\xe9es sont prot\xe9g\xe9es par un cryptage SSL 256-bit"})]})]})}),(0,r.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,r.jsx)(a.$n,{variant:"outline",onClick:e,disabled:s,children:"← Retour aux informations"}),(0,r.jsx)(a.$n,{variant:"primary",size:"lg",onClick:j,disabled:s,className:"min-w-[200px]",children:s?(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)(l.P.span,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:"\uD83D\uDCB3"}),"Traitement en cours..."]}):(0,r.jsxs)("span",{className:"flex items-center",children:[(0,r.jsx)("span",{className:"mr-2",children:"\uD83D\uDCB0"}),"Payer"," ",(0,i.$g)(m.totalAmount+.2*m.totalAmount+(m.totalAmount>=50?0:5.99))]})})]})]})}},35008:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>n});var r=s(166),a=s(11325),i=s.n(a);s(14791);let n=()=>{let e={navigation:[{label:"Accueil",href:"/"},{label:"\xc0 propos & L\xe9gal",href:"/about"},{label:"Professionnels & Revendeurs",href:"/professionals"}],legal:[{label:"Mentions l\xe9gales",href:"/legal"},{label:"Politique de confidentialit\xe9",href:"/privacy"},{label:"Conditions g\xe9n\xe9rales",href:"/terms"},{label:"V\xe9rification d'\xe2ge",href:"/age-verification"},{label:"Cookies",href:"/cookies"}],social:[{label:"Facebook",href:"https://facebook.com/deltagum",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{label:"Instagram",href:"https://instagram.com/deltagum",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.876-.875-1.366-2.026-1.366-3.323s.49-2.448 1.366-3.323c.875-.876 2.026-1.366 3.323-1.366s2.448.49 3.323 1.366c.876.875 1.366 2.026 1.366 3.323s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"})})},{label:"Twitter",href:"https://twitter.com/deltagum",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{label:"TikTok",href:"https://tiktok.com/@deltagum",icon:(0,r.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"})})}]};return(0,r.jsxs)("footer",{className:"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white",children:[(0,r.jsx)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8 py-8 sm:py-12 lg:py-16",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12",children:[(0,r.jsxs)("div",{className:"lg:col-span-1 text-center sm:text-left",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center sm:justify-start space-x-2 sm:space-x-3 mb-4 sm:mb-6",children:[(0,r.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10",children:(0,r.jsx)("img",{src:"/img/logo.jpg",alt:"Deltagum Logo",className:"w-full h-full object-contain rounded-lg"})}),(0,r.jsx)("span",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-pink-400 to-orange-300 bg-clip-text text-transparent",children:"Deltagum"})]}),(0,r.jsx)("p",{className:"text-gray-300 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base",children:"Des bonbons artisanaux aux saveurs uniques qui \xe9veillent vos sens. D\xe9couvrez nos cr\xe9ations gourmandes aux parfums de fraise, myrtille et pomme."})]}),(0,r.jsxs)("div",{className:"text-center sm:text-left",children:[(0,r.jsx)("h3",{className:"font-bold text-white sm:text-lg mb-3 sm:mb-4",style:{color:"#ffffff"},children:"Informations"}),(0,r.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:e.legal.map(e=>(0,r.jsx)("li",{children:(0,r.jsx)(i(),{href:e.href,className:"text-gray-300 hover:text-pink-300 transition-colors duration-200 text-sm sm:text-base",children:e.label})},e.href))})]}),(0,r.jsxs)("div",{className:"text-center sm:text-left",children:[(0,r.jsx)("h3",{className:"font-bold text-white sm:text-lg mb-3 sm:mb-4",style:{color:"#ffffff"},children:"Contact"}),(0,r.jsxs)("div",{className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6",children:[(0,r.jsxs)("p",{className:"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base",children:[(0,r.jsx)("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),"<EMAIL>"]}),(0,r.jsxs)("p",{className:"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base",children:[(0,r.jsx)("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),"+33 1 23 45 67 89"]})]}),(0,r.jsx)("h4",{className:"font-semibold mb-2 sm:mb-3 text-pink-300 text-sm sm:text-base",children:"Suivez-nous"}),(0,r.jsx)("div",{className:"flex justify-center sm:justify-start space-x-2 sm:space-x-3",children:e.social.map(e=>(0,r.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"p-1.5 sm:p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-pink-300 hover:bg-gray-700 transition-colors duration-200","aria-label":e.label,children:e.icon},e.label))})]})]})}),(0,r.jsx)("div",{className:"border-t border-gray-700 py-4 sm:py-6",children:(0,r.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8",children:[(0,r.jsx)("div",{className:"bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("p",{className:"text-yellow-300 text-xs sm:text-sm font-semibold mb-1 sm:mb-2",children:"\uD83C\uDF3F AVERTISSEMENT IMPORTANT - PRODUITS DELTA-9 THC"}),(0,r.jsxs)("p",{className:"text-yellow-200 text-xs leading-relaxed",children:["Nos produits sont strictement r\xe9serv\xe9s aux personnes majeures (18 ans et plus).",(0,r.jsx)("br",{}),"Ne pas conduire ou utiliser de machines apr\xe8s consommation. D\xe9conseill\xe9 aux femmes enceintes ou allaitantes.",(0,r.jsx)("br",{}),"Tenir hors de port\xe9e des enfants. Consommer avec mod\xe9ration."]})]})}),(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,r.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm text-center sm:text-left",children:"\xa9 2024 Deltagum . Tous droits r\xe9serv\xe9s. Fait avec ❤️ en France."}),(0,r.jsx)("div",{className:"flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-400",children:(0,r.jsx)("span",{children:"\uD83C\uDF3F D\xe9tente naturelle avec style ! ✨"})})]})]})})]})}},35952:(e,t,s)=>{"use strict";s.d(t,{c:()=>n});var r=s(61438),a=s(18658);let i=(0,r.v)()((0,a.Zr)((e,t)=>({customer:null,loading:!1,error:null,setCustomer:t=>{e({customer:t,error:null})},updateCustomer:async s=>{let r=t().customer;if(!r)return void e({error:"Aucun client connect\xe9"});e({loading:!0,error:null});try{let t=await fetch(`/api/customers/${r.id}`,{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!t.ok)throw Error(`HTTP error! status: ${t.status}`);let a=await t.json();if(a.success)e({customer:a.data,loading:!1});else throw Error(a.error||"Erreur lors de la mise \xe0 jour")}catch(t){e({error:t instanceof Error?t.message:"Erreur inconnue",loading:!1})}},clearCustomer:()=>{e({customer:null,error:null})}}),{name:"deltagum-customer",storage:(0,a.KU)(()=>localStorage),partialize:e=>({customer:e.customer})})),n=()=>{let{customer:e,loading:t,error:s,setCustomer:r,updateCustomer:a,clearCustomer:n}=i();return{customer:e,loading:t,error:s,setCustomer:r,updateCustomer:a,clearCustomer:n}}},45276:(e,t,s)=>{"use strict";s.d(t,{C9:()=>i,Rf:()=>o,Vd:()=>a,Yo:()=>n,bK:()=>l,nM:()=>c,qG:()=>r});let r={initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3}},a={initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4}},i={initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.4}},n={initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3}},l={animate:{transition:{staggerChildren:.1}}},o={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.4}}},c={initial:{opacity:0,x:300},animate:{opacity:1,x:0,transition:{duration:.3}},exit:{opacity:0,x:300,transition:{duration:.2}}}},49537:(e,t,s)=>{"use strict";s.d(t,{K:()=>r,Q:()=>a});let r=(0,s(61438).v)((e,t)=>({products:[],selectedProduct:null,selectedVariant:null,loading:!0,error:null,fetchProducts:async(s=0)=>{e({loading:!0,error:null});try{let t=new AbortController,s=setTimeout(()=>t.abort(),1e4),r=await fetch("/api/products",{cache:"no-store",headers:{"Content-Type":"application/json"},signal:t.signal});if(clearTimeout(s),!r.ok)throw Error(`Erreur HTTP: ${r.status} - ${r.statusText}`);let a=await r.json();if(a.success&&a.data&&Array.isArray(a.data.products))console.log("Produits charg\xe9s avec succ\xe8s:",a.data.products.length),e({products:a.data.products,loading:!1,error:null});else throw Error(a.error||"Format de r\xe9ponse invalide")}catch(r){if(console.error("Erreur lors du chargement des produits:",r),s<2){console.log(`Tentative de rechargement ${s+1}/2...`),setTimeout(()=>{let e=t();e.fetchProducts&&e.fetchProducts(s+1)},1e3*(s+1));return}e({error:r instanceof Error?r.message:"Erreur de connexion",loading:!1})}},selectProduct:t=>{let s=t.variants?.[0],r=s?{...s,product:t}:null;e({selectedProduct:t,selectedVariant:r})},selectVariant:t=>{e({selectedVariant:t})}})),a=()=>{let{products:e,selectedProduct:t,selectedVariant:s,loading:a,error:i,fetchProducts:n,selectProduct:l,selectVariant:o}=r();return{products:e,selectedProduct:t,selectedVariant:s,loading:a,error:i,fetchProducts:n,selectProduct:l,selectVariant:o}}},53081:(e,t,s)=>{"use strict";s.d(t,{AdminLayout:()=>L,AuthLayout:()=>E,ErrorLayout:()=>A,Layout:()=>D});var r=s(166),a=s(23705),i=s(14791),n=s.n(i);let l="deltagum_age_verified",o=()=>{let[e,t]=(0,i.useState)(null),[s,r]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(l);if(e){let{timestamp:s,verified:a}=JSON.parse(e),i=Date.now();if(a&&i-s<864e5){t(!0),r(!1);return}localStorage.removeItem(l)}t(!1),r(!0)}catch(e){console.error("Erreur lors de la v\xe9rification d'\xe2ge:",e),t(!1),r(!0)}})()},[]),{isVerified:e,showModal:s,confirmAge:()=>{try{let e={verified:!0,timestamp:Date.now()};localStorage.setItem(l,JSON.stringify(e)),t(!0),r(!1)}catch(e){console.error("Erreur lors de la sauvegarde de la v\xe9rification:",e)}},denyAge:()=>{t(!1),r(!1),window.location.href="https://www.service-public.fr/particuliers/vosdroits/F32094"},resetVerification:()=>{localStorage.removeItem(l),t(!1),r(!0)}}};var c=s(93666),d=s(26367),m=s(16224),x=s(66212),u=s(35008),p=s(81838);let h=({isOpen:e,onConfirm:t,onDeny:s})=>{let[n,l]=(0,i.useState)(!1);return e?(0,r.jsx)(m.N,{children:(0,r.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,r.jsx)(x.P.div,{className:"absolute inset-0 bg-black/80 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}}),(0,r.jsx)(x.P.div,{className:"relative z-10 w-full max-w-md mx-4",initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.3},children:(0,r.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl overflow-hidden",children:[(0,r.jsxs)("div",{className:"bg-gradient-to-r from-pink-500 to-orange-500 p-6 text-center",children:[(0,r.jsx)(x.P.div,{className:"text-6xl mb-4",animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0},children:"\uD83D\uDD1E"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-white font-candy",children:"V\xe9rification d'\xe2ge"})]}),(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-3",children:"Acc\xe8s r\xe9serv\xe9 aux adultes"}),(0,r.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["Ce site propose des produits \xe0 base de"," ",(0,r.jsx)("strong",{children:"Delta-9 THC"}),".",(0,r.jsx)("br",{}),"L'acc\xe8s est strictement r\xe9serv\xe9 aux personnes majeures."]})]}),(0,r.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,r.jsxs)("p",{className:"text-sm text-yellow-800",children:["⚠️ ",(0,r.jsx)("strong",{children:"Avertissement :"})," Ces produits contiennent du Delta-9 THC et ne sont pas destin\xe9s aux mineurs, aux femmes enceintes ou allaitantes."]})}),(0,r.jsx)("div",{className:"text-lg font-medium text-gray-800 mb-6",children:"Avez-vous 18 ans ou plus ?"}),n?(0,r.jsxs)(x.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-center",children:[(0,r.jsx)("div",{className:"text-red-600 font-semibold mb-4",children:"❌ Acc\xe8s refus\xe9"}),(0,r.jsxs)("p",{className:"text-gray-600",children:["Vous devez \xeatre majeur pour acc\xe9der \xe0 ce site.",(0,r.jsx)("br",{}),"Redirection en cours..."]})]}):(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(a.$n,{onClick:t,className:"flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3",children:"✓ Oui, j'ai 18 ans ou plus"}),(0,r.jsx)(a.$n,{onClick:()=>{l(!0),setTimeout(()=>{s()},2e3)},variant:"outline",className:"flex-1 border-red-300 text-red-600 hover:bg-red-50 font-semibold py-3",children:"✗ Non, j'ai moins de 18 ans"})]})]}),(0,r.jsx)("div",{className:"bg-gray-50 px-6 py-4 text-center",children:(0,r.jsx)("p",{className:"text-xs text-gray-500",children:"En continuant, vous confirmez \xeatre majeur et acceptez nos conditions d'utilisation."})})]})})]})}):null};var g=s(59093);let f=({isOpen:e,onClose:t})=>{let{cart:s,removeItem:i,clearCart:l}=(0,c._$)(),{openModal:o}=(0,c.Je)(),{isAuthenticated:u,user:p}=(0,c.As)(),{addNotification:h}=(0,c.E$)(),[f,y]=n().useState(!1),b=s.items,v=s.totalAmount,j=s.totalItems,N=v>=50?0:5.99,w=v+N,k=async()=>{if(!f){if(!u){h({type:"warning",title:"\uD83D\uDD12 Connexion requise",message:"Vous devez vous connecter pour passer commande. Redirection en cours..."}),t(),setTimeout(()=>{window.location.href="/auth"},1500);return}try{if(0===b.length)return void h({type:"error",title:"Panier vide",message:"Ajoutez des produits \xe0 votre panier avant de commander"});y(!0),console.log("\uD83D\uDED2 D\xe9but du processus de commande..."),console.log("\uD83D\uDCE6 Articles du panier:",b),console.log("\uD83D\uDCB0 Total:",w);let e={items:b.map(e=>({productId:e.productId,variantId:e.variantId,quantity:e.quantity})),shippingAddress:{firstName:p?.firstName||"Client",lastName:p?.lastName||"Deltagum",email:p?.email||`client-${Date.now()}@deltagum.com`,phone:p?.phone||"0123456789",street:p?.address||"123 Rue de la Livraison",city:p?.city||"Paris",postalCode:p?.postalCode||"75001",country:"France"},...p?.id&&{customerId:p.id},totalAmount:w};console.log("\uD83D\uDCE4 Donn\xe9es de commande:",e);let s=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();console.error("❌ Erreur commande:",e),h({type:"error",title:"Erreur de commande",message:"Impossible de cr\xe9er la commande. Veuillez r\xe9essayer."}),y(!1);return}let{data:r}=await s.json();console.log("✅ Commande cr\xe9\xe9e:",r.id);let a=await fetch("/api/checkout/session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:r.id})});if(!a.ok){let e=await a.json();console.error("❌ Erreur session:",e),h({type:"error",title:"Erreur de paiement",message:"Impossible de cr\xe9er la session de paiement. Veuillez r\xe9essayer."}),y(!1);return}let i=await a.json();i.success&&i.data.url?(console.log("✅ Session Stripe cr\xe9\xe9e, redirection..."),localStorage.setItem("deltagum_pending_order",JSON.stringify({orderId:r.id,cartItems:b,timestamp:Date.now()})),t(),window.location.href=i.data.url):(console.error("❌ Erreur session:",i),h({type:"error",title:"Erreur de session",message:"Probl\xe8me lors de la cr\xe9ation de la session de paiement"}),y(!1))}catch(e){console.error("❌ Erreur:",e),h({type:"error",title:"Erreur inattendue",message:"Une erreur s'est produite. Veuillez r\xe9essayer."}),y(!1)}}};return(0,r.jsxs)(a.aF,{isOpen:e,onClose:t,title:"Mon Panier",size:"lg",className:"max-h-[95vh] sm:max-h-[90vh]",children:[(0,r.jsx)(a.cw,{className:"p-0",children:0===b.length?(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"text-center py-8 sm:py-12 px-4",children:[(0,r.jsx)(x.P.div,{className:"text-4xl sm:text-5xl lg:text-6xl mb-3 sm:mb-4",animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0},children:"\uD83D\uDED2"}),(0,r.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-2",children:"Votre panier est vide"}),(0,r.jsx)("p",{className:"text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base",children:"D\xe9couvrez nos d\xe9licieux bonbons et ajoutez-les \xe0 votre panier !"}),(0,r.jsx)(a.$n,{variant:"primary",onClick:t,className:"w-full sm:w-auto",children:"Continuer mes achats"})]}),{}):(0,r.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,r.jsx)("div",{className:"max-h-80 sm:max-h-96 overflow-y-auto px-3 sm:px-6",children:(0,r.jsx)(m.N,{mode:"popLayout",children:b.map(e=>(0,r.jsxs)(x.P.div,{className:"flex items-center space-x-2 sm:space-x-4 py-3 sm:py-4 border-b border-gray-200 last:border-b-0",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},layout:!0,children:[(0,r.jsx)("div",{className:"flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-lg overflow-hidden",children:e.image?(0,r.jsx)(g.default,{src:e.image,alt:e.name,width:64,height:64,className:"w-full h-full object-cover"}):(0,r.jsx)("div",{className:"w-full h-full flex items-center justify-center text-lg sm:text-2xl",children:"\uD83C\uDF6D"})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 truncate text-sm sm:text-base",children:e.name}),(0,r.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:["Saveur: ",e.flavor]}),(0,r.jsx)("div",{className:"flex items-center space-x-2 mt-1",children:(0,r.jsx)("span",{className:"font-semibold text-pink-600 text-sm sm:text-base",children:(0,d.$g)(e.price)})})]}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full",children:["Qt\xe9: ",e.quantity]})}),(0,r.jsx)("div",{className:"text-right",children:(0,r.jsx)("p",{className:"font-semibold text-gray-900",children:(0,d.$g)(e.price*e.quantity)})}),(0,r.jsx)(a.$n,{variant:"ghost",size:"sm",onClick:()=>i(e.id),className:"text-red-500 hover:text-red-700 p-1",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]},e.id))})}),(0,r.jsx)("div",{className:"px-6 py-4 bg-gray-50 border-t",children:(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsxs)("span",{className:"text-gray-700",children:["Sous-total (",j," article",j>1?"s":"",")"]}),(0,r.jsx)("span",{className:"text-gray-900 font-medium",children:(0,d.$g)(v)})]}),(0,r.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,r.jsx)("span",{className:"text-gray-700",children:"Livraison"}),(0,r.jsx)("span",{className:(0,d.cn)("text-gray-900 font-medium",0===N&&"text-green-600 font-semibold"),children:0===N?"Gratuite":(0,d.$g)(N)})]}),(0,r.jsxs)("div",{className:"border-t pt-2 flex justify-between font-semibold text-lg",children:[(0,r.jsx)("span",{className:"text-gray-900",children:"Total"}),(0,r.jsx)("span",{className:"text-pink-600 font-bold",children:(0,d.$g)(w)})]})]})})]})}),b.length>0&&(0,r.jsxs)(a.jl,{className:"flex-col space-y-3",children:[!1,(0,r.jsxs)("div",{className:"flex space-x-3 w-full",children:[(0,r.jsx)(a.$n,{variant:"outline",onClick:()=>l(),className:"flex-1",children:"Vider le panier"}),(0,r.jsx)(a.$n,{variant:"primary",onClick:k,disabled:f,className:"flex-1",children:f?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Traitement en cours..."]}):`Commander (${(0,d.$g)(w)})`})]}),(0,r.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Paiement s\xe9curis\xe9 avec Stripe"})]})]})};var y=s(13045),b=s(66506),v=s(13683),j=s(33659);let N=({isOpen:e,onClose:t})=>{let[s,l]=(0,i.useState)("shipping"),[o,d]=(0,i.useState)(!1),u=(0,c._$)();if((0,c.cN)(),n().useEffect(()=>{e&&(l("shipping"),d(!1))},[e]),!u?.cart?.items||0===u.cart.items.length)return null;let p=e=>{l(e)},h=()=>{l("shipping"),d(!1),t()};return(0,r.jsx)(a.aF,{isOpen:e,onClose:h,title:"Finaliser votre commande",size:"full",className:"max-w-7xl",children:(0,r.jsxs)("div",{className:"min-h-[600px]",children:[(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)(b.s,{currentStep:s})}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8",children:[(0,r.jsx)("div",{className:"lg:col-span-2",children:(0,r.jsxs)(m.N,{mode:"wait",children:["shipping"===s&&(0,r.jsx)(x.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"Informations de livraison"})}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)(y.N,{onNext:()=>p("payment"),isProcessing:o})})]})},"shipping"),"payment"===s&&(0,r.jsx)(x.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,r.jsxs)(a.Zp,{children:[(0,r.jsx)(a.aR,{children:(0,r.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"M\xe9thode de paiement"})}),(0,r.jsx)(a.Wu,{children:(0,r.jsx)(j.b,{onBack:()=>p("shipping"),onSuccess:()=>{l("confirmation")},isProcessing:o,setIsProcessing:d})})]})},"payment"),"confirmation"===s&&(0,r.jsx)(x.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},children:(0,r.jsx)(a.Zp,{children:(0,r.jsxs)(a.Wu,{className:"text-center py-12",children:[(0,r.jsx)(x.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring"},className:"text-6xl mb-6",children:"\uD83C\uDF89"}),(0,r.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Commande confirm\xe9e !"}),(0,r.jsx)("p",{className:"text-gray-600 mb-8",children:"Merci pour votre commande ! Vous recevrez un email de confirmation sous peu."}),(0,r.jsx)("div",{className:"space-y-4",children:(0,r.jsx)("button",{onClick:h,className:"bg-pink-500 text-white px-8 py-3 rounded-lg hover:bg-pink-600 transition-colors mr-4",children:"Fermer"})})]})})},"confirmation")]})}),(0,r.jsx)("div",{className:"lg:col-span-1",children:(0,r.jsx)("div",{className:"sticky top-4",children:(0,r.jsx)(v.D,{})})})]})]})})},w=({isOpen:e,onClose:t})=>{let{selectedProduct:s}=(0,c.Bj)();return s?(0,r.jsxs)(a.aF,{isOpen:e,onClose:t,title:s.name,size:"lg",children:[(0,r.jsx)(a.cw,{children:(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDF6D"}),(0,r.jsx)("p",{className:"text-gray-600",children:"Modal de d\xe9tail du produit - \xc0 d\xe9velopper dans les prochaines t\xe2ches"})]})}),(0,r.jsx)(a.jl,{children:(0,r.jsx)(a.$n,{variant:"outline",onClick:t,children:"Fermer"})})]}):null};var k=s(62603);let C=(0,s(43203).c)("pk_test_51RhRu9GP36x2BKi0Zeh7E8nYyXBaJdtwnnTM7hDuFB2ZPsd3lVEUl7A3Op83HkdNQAqdTfg2619sik6NJmYPYUUl00ZWiBSORN");function P({children:e}){return(0,r.jsx)(k.S8,{stripe:C,options:{appearance:{theme:"stripe",variables:{colorPrimary:"#ff6b9d",colorBackground:"#ffffff",colorText:"#30313d",colorDanger:"#df1b41",fontFamily:"Inter, system-ui, sans-serif",spacingUnit:"4px",borderRadius:"8px"}},locale:"fr"},children:e})}let D=({children:e,className:t,showHeader:s=!0,showFooter:l=!0})=>{let{isCartOpen:g,isAuthModalOpen:y,setIsMobile:b,isLoading:v,closeAllModals:j}=(0,c.mL)(),{isOpen:k,closeModal:C}=(0,c.Je)(),{isVerified:D,showModal:A,confirmAge:E,denyAge:L}=o();!function(){let{checkAuth:e}=(0,c.As)()}();let[I,T]=n().useState(!1);return((0,i.useEffect)(()=>{T(!0)},[]),(0,i.useEffect)(()=>{let e=()=>{b(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[b]),(0,i.useEffect)(()=>(g||k||y?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[g,k,y]),null===D)?(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-pink-400 via-purple-500 to-orange-400 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-white text-center",children:[(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,r.jsx)("p",{children:"Chargement..."})]})}):(0,r.jsxs)("div",{className:"min-h-screen flex flex-col bg-white",children:[(0,r.jsx)(h,{isOpen:A,onConfirm:E,onDeny:L}),D&&(0,r.jsxs)(P,{children:[s&&(0,r.jsx)(p.Header,{}),(0,r.jsx)("main",{className:(0,d.cn)("flex-1",s&&"pt-16 lg:pt-20",t),children:e}),l&&(0,r.jsx)(u.Footer,{}),I&&(0,r.jsxs)(m.N,{mode:"wait",children:[(0,r.jsx)(f,{isOpen:g,onClose:j},"cart-modal"),(0,r.jsx)(w,{isOpen:!1,onClose:j},"product-modal"),(0,r.jsx)(N,{isOpen:k,onClose:C},"checkout-modal")]})]}),(0,r.jsx)(a.N9,{}),(0,r.jsx)(m.N,{children:v&&(0,r.jsx)(x.P.div,{className:"fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)(x.P.div,{className:"text-6xl mb-4",animate:{rotate:[0,360],scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:"\uD83C\uDF6D"}),(0,r.jsx)(x.P.p,{className:"text-lg font-medium text-gray-600",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Chargement en cours..."})]})})})]})},A=({children:e})=>(0,r.jsx)(D,{showHeader:!1,showFooter:!1,className:"flex items-center justify-center",children:e}),E=({children:e})=>(0,r.jsx)(D,{showHeader:!1,showFooter:!1,className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-pink-50 to-orange-50",children:e}),L=({children:e})=>(0,r.jsx)(D,{className:"bg-gray-50",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:e})})},63226:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,16793,23)),Promise.resolve().then(s.t.bind(s,20897,23)),Promise.resolve().then(s.t.bind(s,53609,23)),Promise.resolve().then(s.t.bind(s,82680,23)),Promise.resolve().then(s.t.bind(s,89352,23)),Promise.resolve().then(s.t.bind(s,8236,23)),Promise.resolve().then(s.t.bind(s,14114,23)),Promise.resolve().then(s.t.bind(s,20476,23))},64256:(e,t,s)=>{Promise.resolve().then(s.bind(s,92610)),Promise.resolve().then(s.bind(s,71044)),Promise.resolve().then(s.bind(s,7493))},66506:(e,t,s)=>{"use strict";s.d(t,{s:()=>i});var r=s(166),a=s(66212);s(14791);let i=({currentStep:e})=>{let t=[{id:"shipping",name:"Livraison",icon:"\uD83D\uDCE6",description:"Adresse de livraison"},{id:"payment",name:"Paiement",icon:"\uD83D\uDCB3",description:"M\xe9thode de paiement"},{id:"confirmation",name:"Confirmation",icon:"✅",description:"Commande confirm\xe9e"}],s=t.findIndex(t=>t.id===e);return(0,r.jsxs)("div",{className:"w-full",children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("div",{className:"absolute top-1/2 left-0 right-0 h-1 bg-gray-200 rounded-full transform -translate-y-1/2"}),(0,r.jsx)(a.P.div,{className:"absolute top-1/2 left-0 h-1 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full transform -translate-y-1/2",initial:{width:"0%"},animate:{width:0===s?"0%":1===s?"50%":"100%"},transition:{duration:.5,ease:"easeInOut"}}),(0,r.jsx)("div",{className:"relative flex justify-between",children:t.map((e,t)=>{let i=t<s,n=t===s;return(0,r.jsxs)(a.P.div,{className:"flex flex-col items-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*t},children:[(0,r.jsxs)(a.P.div,{className:`
                    relative w-12 h-12 rounded-full flex items-center justify-center text-lg font-semibold
                    ${i?"bg-gradient-to-r from-pink-500 to-orange-500 text-white":n?"bg-white border-2 border-pink-500 text-pink-500 shadow-lg":"bg-gray-200 text-gray-400"}
                  `,whileHover:{scale:1.05},animate:n?{boxShadow:["0 0 0 0 rgba(236, 72, 153, 0.4)","0 0 0 10px rgba(236, 72, 153, 0)","0 0 0 0 rgba(236, 72, 153, 0)"]}:{},transition:{boxShadow:{duration:2,repeat:1/0},scale:{duration:.2}},children:[i?(0,r.jsx)(a.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:"✓"}):(0,r.jsx)(a.P.span,{animate:n?{scale:[1,1.2,1],rotate:[0,5,-5,0]}:{},transition:{duration:2,repeat:1/0},children:e.icon}),n&&(0,r.jsx)(a.P.div,{className:"absolute -inset-1 rounded-full border-2 border-pink-300",animate:{scale:[1,1.2,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0}})]}),(0,r.jsxs)(a.P.div,{className:"mt-3 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.1*t+.2},children:[(0,r.jsx)("h4",{className:`
                    text-sm font-semibold
                    ${i||n?"text-gray-800":"text-gray-400"}
                  `,children:e.name}),(0,r.jsx)("p",{className:`
                    text-xs mt-1
                    ${i||n?"text-gray-600":"text-gray-400"}
                  `,children:e.description})]}),(0,r.jsx)("div",{className:"md:hidden mt-1",children:(0,r.jsxs)("span",{className:`
                    text-xs px-2 py-1 rounded-full
                    ${i?"bg-green-100 text-green-600":n?"bg-pink-100 text-pink-600":"bg-gray-100 text-gray-400"}
                  `,children:[t+1,"/3"]})})]},e.id)})})]}),(0,r.jsx)(a.P.div,{className:"md:hidden mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,r.jsxs)("h3",{className:"font-semibold text-gray-800 mb-1",children:["\xc9tape ",s+1," sur ",t.length]}),(0,r.jsx)("p",{className:"text-sm text-gray-600",children:t[s].description}),(0,r.jsxs)("div",{className:"mt-3",children:[(0,r.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,r.jsx)("span",{children:"Progression"}),(0,r.jsxs)("span",{children:[Math.round((s+1)/t.length*100),"%"]})]}),(0,r.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,r.jsx)(a.P.div,{className:"bg-gradient-to-r from-pink-500 to-orange-500 h-2 rounded-full",initial:{width:"0%"},animate:{width:`${(s+1)/t.length*100}%`},transition:{duration:.5,ease:"easeInOut"}})})]})]})}),(0,r.jsx)(a.P.div,{className:"hidden md:block mt-6 text-center",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,r.jsxs)("p",{className:"text-sm text-gray-500",children:["⏱️ Temps estim\xe9 restant :"," ","shipping"===e?"2-3 minutes":"payment"===e?"1-2 minutes":"Termin\xe9 !"]})})]})}},69308:()=>{},71044:(e,t,s)=>{"use strict";s.d(t,{Header:()=>r});let r=(0,s(54560).registerClientReference)(function(){throw Error("Attempted to call Header() from the server but Header is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Header.tsx","Header")},77280:(e,t,s)=>{Promise.resolve().then(s.bind(s,35008)),Promise.resolve().then(s.bind(s,81838)),Promise.resolve().then(s.bind(s,53081))},81838:(e,t,s)=>{"use strict";s.d(t,{Header:()=>y});var r=s(166),a=s(23705),i=s(45276),n=s(26367),l=s(93666),o=s(66212),c=s(16224),d=s(83163),m=s(2926),x=s(39628),u=s(28805),p=s(81678),h=s(11325),g=s.n(h),f=s(14791);let y=()=>{let[e,t]=(0,f.useState)(!1),{getTotalItems:s}=(0,l._$)(),{openCart:h}=(0,l.mL)(),[y,b]=(0,f.useState)(!1),[v,j]=(0,f.useState)(!1),{products:N,fetchProducts:w}=(0,l.Bj)(),{isAuthenticated:k,user:C,logout:P}=(0,l.As)();(0,f.useEffect)(()=>{let e=()=>{t(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,f.useEffect)(()=>{w()},[w]);let D=s(),A=[{label:"Accueil",href:"/",type:"link"},{label:"\xc0 propos & L\xe9gal",href:"/about",type:"link"},{label:"Professionnels & Revendeurs",href:"/professionals",type:"link"}],E=e=>{if("scroll"===e.type){let t=document.querySelector(e.href);t&&t.scrollIntoView({behavior:"smooth"})}else window.location.href=e.href;b(!1)};return(0,r.jsx)(o.P.header,{className:(0,n.cn)("fixed top-0 left-0 right-0 z-40 transition-all duration-300",e?"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200":"bg-transparent"),initial:i.C9.initial,animate:i.C9.animate,transition:i.C9.transition,children:(0,r.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between h-14 sm:h-16 lg:h-20",children:[(0,r.jsx)(o.P.div,{className:"flex items-center space-x-1 sm:space-x-2",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,r.jsxs)(g(),{href:"/",className:"flex items-center space-x-1 sm:space-x-2",children:[(0,r.jsx)(o.P.div,{className:"relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12",animate:{rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:(0,r.jsx)("img",{src:"/img/logo.jpg",alt:"Deltagum Logo",className:"w-full h-full object-contain rounded-lg"})}),(0,r.jsx)("span",{className:(0,n.cn)("text-lg sm:text-xl lg:text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-400 bg-clip-text text-transparent","hidden xs:block"),children:"Deltagum"})]})}),(0,r.jsxs)("nav",{className:"hidden lg:flex items-center space-x-8",children:[(0,r.jsx)(g(),{href:A[0].href,children:(0,r.jsxs)(o.P.button,{className:(0,n.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0},children:[A[0].label,(0,r.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]})}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)(o.P.button,{onClick:()=>j(!v),className:(0,n.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2 flex items-center space-x-1"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,r.jsx)("span",{children:"Produits"}),(0,r.jsx)(d.A,{className:(0,n.cn)("w-4 h-4 transition-transform",v&&"rotate-180")}),(0,r.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]}),(0,r.jsx)(c.N,{children:v&&(0,r.jsx)(o.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:N.map(e=>(0,r.jsx)(g(),{href:`/products/${e.id}`,className:"block px-4 py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors",onClick:()=>j(!1),children:(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("img",{src:e.image,alt:e.name,className:"w-8 h-8 rounded object-cover"}),(0,r.jsx)("div",{children:(0,r.jsx)("div",{className:"font-medium",children:e.name})})]})},e.id))})})]}),A.slice(1).map((e,t)=>(0,r.jsx)(g(),{href:e.href,children:(0,r.jsxs)(o.P.button,{className:(0,n.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:(t+2)*.1},children:[e.label,(0,r.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]})},t))]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[k?(0,r.jsxs)("div",{className:"relative group",children:[(0,r.jsx)(o.P.button,{className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(m.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-green-600"})}),(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-9999",children:[(0,r.jsxs)("div",{className:"p-3 border-b",children:[(0,r.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[C?.firstName," ",C?.lastName]}),(0,r.jsx)("p",{className:"text-xs text-gray-500",children:C?.email})]}),(0,r.jsxs)("div",{className:"p-2",children:[(0,r.jsx)(g(),{href:"/profile",children:(0,r.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1",children:"Mon profil"})}),C?.role==="ADMIN"&&(0,r.jsx)(g(),{href:"/admin/dashboard",children:(0,r.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1",children:"Dashboard Admin"})}),(0,r.jsx)("button",{onClick:P,className:"w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors",children:"Se d\xe9connecter"})]})]})]}):(0,r.jsx)(g(),{href:"/auth",children:(0,r.jsx)(o.P.button,{className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,r.jsx)(m.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-black"})})}),(0,r.jsx)(o.P.div,{className:"relative",children:(0,r.jsxs)(o.P.button,{onClick:()=>{h()},className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:[(0,r.jsx)(x.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-black"}),(0,r.jsx)(c.N,{children:D>0&&(0,r.jsx)(o.P.div,{className:"absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1",initial:{scale:0},animate:{scale:1},exit:{scale:0},children:(0,r.jsx)(a.Ex,{variant:"primary",size:"sm",rounded:!0,className:"min-w-[18px] h-4 sm:min-w-[20px] sm:h-5 text-xs",children:D})})})]})}),(0,r.jsx)("div",{className:"lg:hidden",children:(0,r.jsx)(a.$n,{variant:"ghost",size:"md",onClick:()=>b(!y),className:"p-1.5 sm:p-2",children:(0,r.jsx)(o.P.div,{animate:y?{rotate:180}:{rotate:0},transition:{duration:.2},children:y?(0,r.jsx)(u.A,{className:"w-5 h-5 sm:w-6 sm:h-6"}):(0,r.jsx)(p.A,{className:"w-5 h-5 sm:w-6 sm:h-6"})})})})]})]}),(0,r.jsx)(c.N,{children:y&&(0,r.jsx)(o.P.div,{className:"lg:hidden border-t border-gray-200 bg-white/95 backdrop-blur-md",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},children:(0,r.jsx)("nav",{className:"py-3 sm:py-4 space-y-1 sm:space-y-2",children:A.map((e,t)=>(0,r.jsx)(o.P.button,{onClick:()=>E(e),className:"block w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors text-sm sm:text-base font-medium",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.05*t},children:e.label},e.href))})})})]})})}},86214:(e,t,s)=>{"use strict";s.d(t,{x:()=>l});var r=s(26367),a=s(61438),i=s(18658);let n={items:[],totalItems:0,totalAmount:0},l=(0,a.v)()((0,i.Zr)((e,t)=>({cart:n,addItem:t=>{e(e=>{let s,a=e.cart.items.findIndex(e=>e.productId===t.productId&&e.variantId===t.variantId);if(a>=0)s=e.cart.items.map((e,s)=>s===a?{...e,quantity:e.quantity+t.quantity}:e);else{let r={...t,id:`${t.productId}-${t.variantId}-${Date.now()}`};s=[...e.cart.items,r]}let i=(0,r.wy)(s),n=(0,r.te)(s);return{cart:{items:s,totalItems:i,totalAmount:n}}})},removeItem:t=>{e(e=>{let s=e.cart.items.filter(e=>e.id!==t),a=(0,r.wy)(s),i=(0,r.te)(s);return{cart:{items:s,totalItems:a,totalAmount:i}}})},updateQuantity:(s,a)=>{if(a<=0)return void t().removeItem(s);e(e=>{let t=e.cart.items.map(e=>e.id===s?{...e,quantity:a}:e),i=(0,r.wy)(t),n=(0,r.te)(t);return{cart:{items:t,totalItems:i,totalAmount:n}}})},clearCart:()=>{e({cart:n})},getTotalItems:()=>t().cart.totalItems,getTotalAmount:()=>t().cart.totalAmount}),{name:"deltagum-cart",storage:(0,i.KU)(()=>localStorage),partialize:e=>({cart:e.cart})}))},86684:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>o,metadata:()=>l});var r=s(87276);s(71044),s(92610);var a=s(7493),i=s(40134),n=s.n(i);s(69308);let l={title:"Deltagum - Des bonbons qui \xe9veillent vos sens",description:"D\xe9couvrez nos cr\xe9ations artisanales aux saveurs uniques de fraise, myrtille et pomme. Des bonbons qui \xe9veillent vos sens !",keywords:["bonbons","artisanal","fraise","myrtille","pomme","deltagum","confiserie"],authors:[{name:"Deltagum"}],creator:"Deltagum",publisher:"Deltagum",openGraph:{title:"Deltagum - Des bonbons qui \xe9veillent vos sens",description:"D\xe9couvrez nos cr\xe9ations artisanales aux saveurs uniques de fraise, myrtille et pomme.",url:"https://deltagum.com",siteName:"Deltagum",images:[{url:"/og-image.jpg",width:1200,height:630,alt:"Deltagum - Bonbons artisanaux"}],locale:"fr_FR",type:"website"},twitter:{card:"summary_large_image",title:"Deltagum - Des bonbons qui \xe9veillent vos sens",description:"D\xe9couvrez nos cr\xe9ations artisanales aux saveurs uniques de fraise, myrtille et pomme.",images:["/og-image.jpg"],creator:"@deltagum"},robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}},verification:{google:"your-google-verification-code"}};function o({children:e}){return(0,r.jsx)("html",{lang:"fr",className:"scroll-smooth",children:(0,r.jsx)("body",{className:`${n().variable} font-sans antialiased`,children:(0,r.jsx)(a.Layout,{children:e})})})}},92610:(e,t,s)=>{"use strict";s.d(t,{Footer:()=>r});let r=(0,s(54560).registerClientReference)(function(){throw Error("Attempted to call Footer() from the server but Footer is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\Documents\\augment-projects\\Deltagum\\deltagum\\src\\components\\layout\\Footer.tsx","Footer")},93666:(e,t,s)=>{"use strict";s.d(t,{As:()=>r.A,_$:()=>a.x,Je:()=>n,cN:()=>l.c,E$:()=>c,Bj:()=>d.K,QT:()=>d.Q,mL:()=>x});var r=s(1550),a=s(86214),i=s(61438);let n=(0,i.v)(e=>({isOpen:!1,openModal:()=>e({isOpen:!0}),closeModal:()=>e({isOpen:!1})}));var l=s(35952);let o=(0,i.v)((e,t)=>({notifications:[],addNotification:s=>{let r=`notification-${Date.now()}-${Math.random().toString(36).substr(2,9)}`,a={...s,id:r,duration:s.duration||5e3};e(e=>({notifications:[...e.notifications,a]})),a.duration&&a.duration>0&&setTimeout(()=>{t().removeNotification(r)},a.duration)},removeNotification:t=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==t)}))},clearNotifications:()=>{e({notifications:[]})}})),c=()=>{let{notifications:e,addNotification:t,removeNotification:s,clearNotifications:r}=o();return{notifications:e,addNotification:t,removeNotification:s,clearNotifications:r,showSuccess:(e,s,r)=>{t({type:"success",title:e,message:s,...r})},showError:(e,s,r)=>{t({type:"error",title:e,message:s,...r})},showWarning:(e,s,r)=>{t({type:"warning",title:e,message:s,...r})},showInfo:(e,s,r)=>{t({type:"info",title:e,message:s,...r})}}};var d=s(49537);let m=(0,i.v)(e=>({isCartOpen:!1,isMenuOpen:!1,isCheckoutOpen:!1,isAuthModalOpen:!1,isLoading:!1,loadingMessage:"",isMobile:!1,scrollY:0,openCart:()=>e({isCartOpen:!0}),closeCart:()=>e({isCartOpen:!1}),toggleCart:()=>e(e=>({isCartOpen:!e.isCartOpen})),openMenu:()=>e({isMenuOpen:!0}),closeMenu:()=>e({isMenuOpen:!1}),toggleMenu:()=>e(e=>({isMenuOpen:!e.isMenuOpen})),openCheckout:()=>e({isCheckoutOpen:!0,isCartOpen:!1}),closeCheckout:()=>e({isCheckoutOpen:!1}),openAuthModal:()=>e({isAuthModalOpen:!0}),closeAuthModal:()=>e({isAuthModalOpen:!1}),setLoading:(t,s="")=>e({isLoading:t,loadingMessage:s}),setIsMobile:t=>e({isMobile:t}),setScrollY:t=>e({scrollY:t}),closeAllModals:()=>e({isCartOpen:!1,isMenuOpen:!1,isCheckoutOpen:!1,isAuthModalOpen:!1})})),x=()=>{let{isCartOpen:e,isMenuOpen:t,isCheckoutOpen:s,isAuthModalOpen:r,isLoading:a,loadingMessage:i,isMobile:n,scrollY:l,openCart:o,closeCart:c,toggleCart:d,openMenu:x,closeMenu:u,toggleMenu:p,openCheckout:h,closeCheckout:g,openAuthModal:f,closeAuthModal:y,setLoading:b,setIsMobile:v,setScrollY:j,closeAllModals:N}=m();return{isCartOpen:e,isMenuOpen:t,isCheckoutOpen:s,isAuthModalOpen:r,isLoading:a,loadingMessage:i,isMobile:n,scrollY:l,openCart:o,closeCart:c,toggleCart:d,openMenu:x,closeMenu:u,toggleMenu:p,openCheckout:h,closeCheckout:g,openAuthModal:f,closeAuthModal:y,setLoading:b,setIsMobile:v,setScrollY:j,closeAllModals:N}}}};