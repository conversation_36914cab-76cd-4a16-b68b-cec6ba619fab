{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/auth/login/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport bcrypt from \"bcryptjs\";\nimport jwt from \"jsonwebtoken\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email, password } = await request.json();\n\n    // Validation des données\n    if (!email || !password) {\n      return NextResponse.json(\n        { error: \"Email et mot de passe requis\" },\n        { status: 400 }\n      );\n    }\n\n    // Rechercher l'utilisateur\n    const user = await prisma.customer.findUnique({\n      where: { email },\n      select: {\n        id: true,\n        email: true,\n        password: true,\n        firstName: true,\n        lastName: true,\n        role: true,\n      },\n    });\n\n    if (!user) {\n      return NextResponse.json(\n        { error: \"Identifiants invalides\" },\n        { status: 401 }\n      );\n    }\n\n    // Vérifier le mot de passe\n    const isPasswordValid = await bcrypt.compare(password, user.password || \"\");\n\n    if (!isPasswordValid) {\n      return NextResponse.json(\n        { error: \"Identifiants invalides\" },\n        { status: 401 }\n      );\n    }\n\n    // Générer le token JWT\n    const token = jwt.sign(\n      {\n        userId: user.id,\n        email: user.email,\n        role: user.role,\n      },\n      process.env.JWT_SECRET || \"fallback-secret\",\n      { expiresIn: \"7d\" }\n    );\n\n    // Créer la réponse avec le cookie\n    const response = NextResponse.json({\n      message: \"Connexion réussie\",\n      user: {\n        id: user.id,\n        email: user.email,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        role: user.role,\n      },\n    });\n\n    // Définir le cookie HTTP-only\n    response.cookies.set(\"auth-token\", token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === \"production\",\n      sameSite: \"lax\",\n      maxAge: 7 * 24 * 60 * 60, // 7 jours\n      path: \"/\",\n    });\n\n    return response;\n  } catch (error) {\n    console.error(\"Erreur lors de la connexion:\", error);\n    return NextResponse.json(\n      { error: \"Erreur interne du serveur\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,yBAAyB;QACzB,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA+B,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE;YAAM;YACf,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,UAAU;gBACV,WAAW;gBACX,UAAU;gBACV,MAAM;YACR;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,2BAA2B;QAC3B,MAAM,kBAAkB,MAAM,wLAAA,CAAA,UAAM,CAAC,OAAO,CAAC,UAAU,KAAK,QAAQ,IAAI;QAExE,IAAI,CAAC,iBAAiB;YACpB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,QAAQ,gMAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;YAAE,WAAW;QAAK;QAGpB,kCAAkC;QAClC,MAAM,WAAW,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,8BAA8B;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;YACxC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK;YACtB,MAAM;QACR;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}