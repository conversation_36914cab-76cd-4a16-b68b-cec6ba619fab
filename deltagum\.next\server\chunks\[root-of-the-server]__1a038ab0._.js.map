{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 111, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/validations.ts"], "sourcesContent": ["import { z } from \"zod\";\n\n// Schémas pour les enums\nexport const flavorTypeSchema = z.enum([\n  \"STRAWBERRY\",\n  \"BLUEBERRY\",\n  \"APPLE\",\n  \"CHOCOLATE\",\n  \"VANILLA\",\n  \"MYRTILLE\",\n]);\nexport const orderStatusSchema = z.enum([\n  \"PENDING\",\n  \"PAID\",\n  \"PROCESSING\",\n  \"SHIPPED\",\n  \"DELIVERED\",\n  \"CANCELLED\",\n]);\nexport const loyaltyLevelSchema = z.enum([\n  \"BRONZE\",\n  \"SILVER\",\n  \"GOLD\",\n  \"PLATINUM\",\n]);\n\n// Schémas pour les entités de base\nexport const customerSchema = z.object({\n  id: z.string().optional(),\n  email: z.string().email(\"Email invalide\"),\n  password: z\n    .string()\n    .min(6, \"Le mot de passe doit contenir au moins 6 caractères\")\n    .optional(),\n  firstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n  lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  phone: z.string().min(10, \"Numéro de téléphone invalide\").optional(),\n  address: z\n    .string()\n    .min(5, \"L'adresse doit contenir au moins 5 caractères\")\n    .optional(),\n  postalCode: z\n    .string()\n    .regex(/^\\d{5}$/, \"Code postal invalide (5 chiffres)\")\n    .optional(),\n  city: z\n    .string()\n    .min(2, \"La ville doit contenir au moins 2 caractères\")\n    .optional(),\n});\n\nexport const shippingAddressSchema = z.object({\n  firstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n  lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\").optional(),\n  street: z.string().min(5, \"L'adresse doit contenir au moins 5 caractères\"),\n  city: z.string().min(2, \"La ville doit contenir au moins 2 caractères\"),\n  postalCode: z.string().regex(/^\\d{5}$/, \"Code postal invalide (5 chiffres)\"),\n  country: z.string().min(2, \"Pays requis\"),\n  phone: z.string().optional(),\n});\n\nexport const productSchema = z.object({\n  id: z.string().optional(),\n  name: z\n    .string()\n    .min(2, \"Le nom du produit doit contenir au moins 2 caractères\"),\n  description: z\n    .string()\n    .min(10, \"La description doit contenir au moins 10 caractères\"),\n  basePrice: z\n    .union([z.number(), z.string().transform((val) => parseFloat(val))])\n    .refine((val) => val > 0, \"Le prix doit être positif\"),\n  image: z\n    .string()\n    .min(1, \"Une image est requise\")\n    .refine((val) => {\n      // Accepter les URLs complètes, les chemins absolus et les uploads\n      return (\n        val.startsWith(\"http\") ||\n        val.startsWith(\"/\") ||\n        val.startsWith(\"./\") ||\n        val.includes(\"/uploads/\") ||\n        val.includes(\"/img/\")\n      );\n    }, \"URL d'image invalide\"),\n  active: z.boolean().default(true),\n  dosage: z.string().optional(),\n  variants: z.array(z.any()).optional(),\n  pricingTiers: z.array(z.any()).optional(),\n});\n\nexport const productVariantSchema = z.object({\n  id: z.string().optional(),\n  productId: z.string(),\n  flavor: flavorTypeSchema,\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"Couleur hexadécimale invalide\"),\n  stock: z.number().int().min(0, \"Le stock ne peut pas être négatif\"),\n  sku: z\n    .string()\n    .min(3, \"Le SKU doit contenir au moins 3 caractères\")\n    .optional(),\n  images: z\n    .array(\n      z.string().refine((val) => {\n        // Accepter les URLs complètes, les chemins absolus et les uploads\n        return (\n          val.startsWith(\"http\") ||\n          val.startsWith(\"/\") ||\n          val.startsWith(\"./\") ||\n          val.includes(\"/uploads/\") ||\n          val.includes(\"/img/\")\n        );\n      }, \"URL ou chemin d'image invalide\")\n    )\n    .default([\"/img/placeholder.svg\"]),\n});\n\n// Schémas pour le panier\nexport const cartItemSchema = z.object({\n  id: z.string().optional(),\n  productId: z.string(),\n  variantId: z.string(),\n  name: z.string(),\n  flavor: flavorTypeSchema,\n  color: z.string(),\n  price: z.number().positive(),\n  quantity: z.number().int().positive(\"La quantité doit être positive\"),\n  image: z.string().url(),\n});\n\nexport const addToCartSchema = z.object({\n  productId: z.string(),\n  variantId: z.string(),\n  quantity: z\n    .number()\n    .int()\n    .positive()\n    .max(10, \"Maximum 10 articles par produit\"),\n});\n\n// Schémas pour les commandes\nexport const orderItemSchema = z.object({\n  productId: z.string(),\n  variantId: z.string(),\n  quantity: z.number().int().positive(),\n  price: z.number().positive().optional(), // Prix unitaire du panier (optionnel, calculé si absent)\n});\n\nexport const createOrderSchema = z.object({\n  customerId: z.string().optional(), // Optionnel pour les commandes invités\n  items: z.array(orderItemSchema).min(1, \"Au moins un article requis\"),\n  shippingAddress: shippingAddressSchema,\n  totalAmount: z.number().positive().optional(), // Optionnel, sera calculé si non fourni\n});\n\nexport const updateOrderStatusSchema = z.object({\n  orderId: z.string(),\n  status: orderStatusSchema,\n});\n\n// Schémas pour les paiements\nexport const paymentIntentSchema = z.object({\n  orderId: z.string(),\n  amount: z.number().positive(),\n  currency: z.string().length(3).default(\"EUR\"),\n});\n\nexport const stripeWebhookSchema = z.object({\n  type: z.string(),\n  data: z.object({\n    object: z.any(),\n  }),\n});\n\n// Schémas pour l'authentification\nexport const signInSchema = z.object({\n  email: z.string().email(\"Email invalide\"),\n  password: z\n    .string()\n    .min(6, \"Le mot de passe doit contenir au moins 6 caractères\"),\n});\n\nexport const signUpSchema = z\n  .object({\n    email: z.string().email(\"Email invalide\"),\n    password: z\n      .string()\n      .min(6, \"Le mot de passe doit contenir au moins 6 caractères\"),\n    confirmPassword: z.string(),\n    firstName: z\n      .string()\n      .min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n    lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n    acceptTerms: z\n      .boolean()\n      .refine((val) => val === true, \"Vous devez accepter les conditions\"),\n  })\n  .refine((data) => data.password === data.confirmPassword, {\n    message: \"Les mots de passe ne correspondent pas\",\n    path: [\"confirmPassword\"],\n  });\n\n// Schémas pour les formulaires de contact\nexport const contactSchema = z.object({\n  name: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\"),\n  subject: z.string().min(5, \"Le sujet doit contenir au moins 5 caractères\"),\n  message: z\n    .string()\n    .min(10, \"Le message doit contenir au moins 10 caractères\"),\n});\n\nexport const newsletterSchema = z.object({\n  email: z.string().email(\"Email invalide\"),\n});\n\n// Schémas pour les avis clients\nexport const reviewSchema = z.object({\n  productId: z.string(),\n  customerId: z.string(),\n  rating: z.number().int().min(1).max(5),\n  title: z.string().min(5, \"Le titre doit contenir au moins 5 caractères\"),\n  comment: z\n    .string()\n    .min(10, \"Le commentaire doit contenir au moins 10 caractères\"),\n});\n\n// Schémas pour les paramètres utilisateur\nexport const userPreferencesSchema = z.object({\n  emailNotifications: z.boolean().default(true),\n  smsNotifications: z.boolean().default(false),\n  marketingEmails: z.boolean().default(true),\n  language: z.enum([\"fr\", \"en\"]).default(\"fr\"),\n  currency: z.enum([\"EUR\", \"USD\"]).default(\"EUR\"),\n});\n\n// Schéma pour le checkout\nexport const checkoutSchema = z.object({\n  customer: customerSchema,\n  shippingAddress: shippingAddressSchema,\n  paymentMethod: z.enum([\"card\", \"paypal\", \"apple_pay\", \"google_pay\"]),\n  items: z.array(cartItemSchema).min(1, \"Au moins un article requis\"),\n  promoCode: z.string().optional(),\n  acceptTerms: z\n    .boolean()\n    .refine((val) => val === true, \"Vous devez accepter les conditions\"),\n});\n\n// Types inférés des schémas\nexport type CustomerFormData = z.infer<typeof customerSchema>;\nexport type ShippingAddressFormData = z.infer<typeof shippingAddressSchema>;\nexport type ProductFormData = z.infer<typeof productSchema>;\nexport type ProductVariantFormData = z.infer<typeof productVariantSchema>;\nexport type CartItemFormData = z.infer<typeof cartItemSchema>;\nexport type AddToCartFormData = z.infer<typeof addToCartSchema>;\nexport type CreateOrderFormData = z.infer<typeof createOrderSchema>;\nexport type SignInFormData = z.infer<typeof signInSchema>;\nexport type SignUpFormData = z.infer<typeof signUpSchema>;\nexport type ContactFormData = z.infer<typeof contactSchema>;\nexport type NewsletterFormData = z.infer<typeof newsletterSchema>;\nexport type ReviewFormData = z.infer<typeof reviewSchema>;\nexport type UserPreferencesFormData = z.infer<typeof userPreferencesSchema>;\nexport type CheckoutFormData = z.infer<typeof checkoutSchema>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,mBAAmB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;CACD;AACM,MAAM,oBAAoB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA;IACA;IACA;CACD;AACM,MAAM,qBAAqB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACvC;IACA;IACA;IACA;CACD;AAGM,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,uDACP,QAAQ;IACX,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gCAAgC,QAAQ;IAClE,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,iDACP,QAAQ;IACX,YAAY,sNAAA,CAAA,IAAC,CACV,MAAM,GACN,KAAK,CAAC,WAAW,qCACjB,QAAQ;IACX,MAAM,sNAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,gDACP,QAAQ;AACb;AAEO,MAAM,wBAAwB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,QAAQ;IAClD,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW;IACxC,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAEO,MAAM,gBAAgB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,MAAM,sNAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;IACV,aAAa,sNAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,IAAI;IACX,WAAW,sNAAA,CAAA,IAAC,CACT,KAAK,CAAC;QAAC,sNAAA,CAAA,IAAC,CAAC,MAAM;QAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,WAAW;KAAM,EAClE,MAAM,CAAC,CAAC,MAAQ,MAAM,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,yBACP,MAAM,CAAC,CAAC;QACP,kEAAkE;QAClE,OACE,IAAI,UAAU,CAAC,WACf,IAAI,UAAU,CAAC,QACf,IAAI,UAAU,CAAC,SACf,IAAI,QAAQ,CAAC,gBACb,IAAI,QAAQ,CAAC;IAEjB,GAAG;IACL,QAAQ,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sNAAA,CAAA,IAAC,CAAC,GAAG,IAAI,QAAQ;IACnC,cAAc,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sNAAA,CAAA,IAAC,CAAC,GAAG,IAAI,QAAQ;AACzC;AAEO,MAAM,uBAAuB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,QAAQ;IACR,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB;IAC3C,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG;IAC/B,KAAK,sNAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG,8CACP,QAAQ;IACX,QAAQ,sNAAA,CAAA,IAAC,CACN,KAAK,CACJ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;QACjB,kEAAkE;QAClE,OACE,IAAI,UAAU,CAAC,WACf,IAAI,UAAU,CAAC,QACf,IAAI,UAAU,CAAC,SACf,IAAI,QAAQ,CAAC,gBACb,IAAI,QAAQ,CAAC;IAEjB,GAAG,mCAEJ,OAAO,CAAC;QAAC;KAAuB;AACrC;AAGO,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM;IACd,QAAQ;IACR,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM;IACf,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IACpC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;AACvB;AAEO,MAAM,kBAAkB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,GACH,QAAQ,GACR,GAAG,CAAC,IAAI;AACb;AAGO,MAAM,kBAAkB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;IACnC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AACvC;AAEO,MAAM,oBAAoB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,OAAO,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,GAAG;IACvC,iBAAiB;IACjB,aAAa,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC7C;AAEO,MAAM,0BAA0B,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM;IACjB,QAAQ;AACV;AAGO,MAAM,sBAAsB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM;IACjB,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC;AACzC;AAEO,MAAM,sBAAsB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM;IACd,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACb,QAAQ,sNAAA,CAAA,IAAC,CAAC,GAAG;IACf;AACF;AAGO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAEO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAC1B,MAAM,CAAC;IACN,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;IACV,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM;IACzB,WAAW,sNAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;IACV,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,sNAAA,CAAA,IAAC,CACX,OAAO,GACP,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;AACnC,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,gBAAgB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI;AACb;AAEO,MAAM,mBAAmB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAGO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM;IACpB,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI;AACb;AAGO,MAAM,wBAAwB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,oBAAoB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACxC,kBAAkB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,iBAAiB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACrC,UAAU,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAM;KAAK,EAAE,OAAO,CAAC;IACvC,UAAU,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAM,EAAE,OAAO,CAAC;AAC3C;AAGO,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU;IACV,iBAAiB;IACjB,eAAe,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;QAAa;KAAa;IACnE,OAAO,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,aAAa,sNAAA,CAAA,IAAC,CACX,OAAO,GACP,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;AACnC", "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/orders/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { createOrderSchema } from \"@/lib/validations\";\nimport { ApiResponse } from \"@/types\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\n// GET /api/orders - Récupérer les commandes\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const customerId = searchParams.get(\"customerId\");\n    const status = searchParams.get(\"status\");\n    const limit = searchParams.get(\"limit\");\n    const offset = searchParams.get(\"offset\");\n\n    const where: any = {};\n    if (customerId) where.customerId = customerId;\n    if (status) where.status = status;\n\n    const take = limit ? parseInt(limit) : undefined;\n    const skip = offset ? parseInt(offset) : undefined;\n\n    const [orders, total] = await Promise.all([\n      prisma.order.findMany({\n        where,\n        include: {\n          customer: true,\n          items: {\n            include: {\n              product: true,\n              variant: true,\n            },\n          },\n        },\n        orderBy: { createdAt: \"desc\" },\n        take,\n        skip,\n      }),\n      prisma.order.count({ where }),\n    ]);\n\n    const response: ApiResponse = {\n      success: true,\n      data: { orders, total },\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Error fetching orders:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error: \"Erreur lors de la récupération des commandes\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n\n// POST /api/orders - Créer une nouvelle commande\nexport async function POST(request: NextRequest) {\n  try {\n    // Vérifier que la requête a un corps\n    const contentType = request.headers.get(\"content-type\");\n    if (!contentType || !contentType.includes(\"application/json\")) {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Content-Type doit être application/json\",\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n\n    let body: any;\n    try {\n      body = await request.json();\n    } catch (jsonError) {\n      console.error(\"Erreur de parsing JSON:\", jsonError);\n      const response: ApiResponse = {\n        success: false,\n        error: \"Corps de requête JSON invalide\",\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n\n    if (!body || typeof body !== \"object\") {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Corps de requête manquant ou invalide\",\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n\n    console.log(\"📦 Données reçues pour création de commande:\", body);\n\n    // Debug: Vérifier que Prisma est disponible\n    console.log(\"🔍 Prisma client:\", prisma ? \"✅ Disponible\" : \"❌ Undefined\");\n    console.log(\n      \"🔍 Prisma product:\",\n      prisma?.product ? \"✅ Disponible\" : \"❌ Undefined\"\n    );\n\n    if (!prisma) {\n      throw new Error(\"Prisma client non initialisé\");\n    }\n\n    // Validation des données\n    const validatedData = createOrderSchema.parse(body);\n\n    // Calculer le montant total\n    let totalAmount = 0;\n    const orderItems: Array<{\n      id: string;\n      productId: string;\n      variantId: string;\n      quantity: number;\n      price: number;\n    }> = [];\n\n    console.log(\"🔍 Traitement des items de commande:\", validatedData.items);\n\n    for (const item of validatedData.items) {\n      console.log(`🔍 Recherche du produit: ${item.productId}`);\n\n      const product = await prisma.product.findUnique({\n        where: { id: item.productId },\n      });\n\n      if (!product) {\n        console.error(\n          `❌ Produit ${item.productId} non trouvé. Articles disponibles:`,\n          await prisma.product.findMany({ select: { id: true, name: true } })\n        );\n        throw new Error(\n          `Produit ${item.productId} non trouvé. Vérifiez que les produits existent en base de données.`\n        );\n      }\n\n      console.log(`✅ Produit trouvé: ${product.name}`);\n\n      // Utiliser le prix du panier s'il est fourni, sinon utiliser le basePrice\n      const itemPrice = item.price\n        ? Number(item.price)\n        : Number((product as any).basePrice);\n      const itemTotal = itemPrice * item.quantity;\n      totalAmount += itemTotal;\n\n      console.log(`📦 Création commande - Item: ${product.name}`);\n      console.log(`💰 Prix reçu du panier: ${item.price || \"non fourni\"}€`);\n      console.log(`💰 Prix de base produit: ${product.basePrice}€`);\n      console.log(`💰 Prix utilisé: ${itemPrice}€`);\n      console.log(`🔢 Quantité: ${item.quantity}`);\n      console.log(`💵 Total item: ${itemTotal}€`);\n\n      orderItems.push({\n        id: globalThis.crypto.randomUUID(),\n        productId: item.productId,\n        variantId: item.variantId,\n        quantity: item.quantity,\n        price: itemPrice,\n      });\n    }\n\n    // Gérer le client (créer un client temporaire si nécessaire)\n    let customerId = validatedData.customerId;\n\n    if (!customerId) {\n      // Vérifier si un client avec cet email existe déjà\n      const email =\n        validatedData.shippingAddress.email ||\n        `guest-${Date.now()}@deltagum.com`;\n\n      const existingCustomer = await prisma.customer.findUnique({\n        where: { email },\n      });\n\n      if (existingCustomer) {\n        console.log(\n          `Client existant trouvé avec email ${email}, utilisation du client existant`\n        );\n        customerId = existingCustomer.id;\n      } else {\n        // Créer un client temporaire pour les commandes invités\n        const tempCustomer = await prisma.customer.create({\n          data: {\n            id: globalThis.crypto.randomUUID(),\n            email,\n            password: \"\", // Mot de passe vide pour les invités\n            firstName: validatedData.shippingAddress.firstName,\n            lastName: validatedData.shippingAddress.lastName,\n            phone: validatedData.shippingAddress.phone || \"\",\n            address: validatedData.shippingAddress.street || \"\",\n            postalCode: validatedData.shippingAddress.postalCode,\n            city: validatedData.shippingAddress.city,\n            updatedAt: new Date(),\n          },\n        });\n        customerId = tempCustomer.id;\n        console.log(`Nouveau client temporaire créé avec email ${email}`);\n      }\n    } else {\n      // Vérifier que le client existe\n      const existingCustomer = await prisma.customer.findUnique({\n        where: { id: customerId },\n      });\n\n      if (!existingCustomer) {\n        console.error(\n          `Client ${customerId} non trouvé. Création d'un client temporaire.`\n        );\n        // Créer un client temporaire si le client spécifié n'existe pas\n        const tempCustomer = await prisma.customer.create({\n          data: {\n            id: globalThis.crypto.randomUUID(),\n            email:\n              validatedData.shippingAddress.email ||\n              `guest-${Date.now()}@deltagum.com`,\n            password: \"\",\n            firstName: validatedData.shippingAddress.firstName,\n            lastName: validatedData.shippingAddress.lastName,\n            phone: validatedData.shippingAddress.phone || \"\",\n            address: validatedData.shippingAddress.street || \"\",\n            postalCode: validatedData.shippingAddress.postalCode,\n            city: validatedData.shippingAddress.city,\n            updatedAt: new Date(),\n          },\n        });\n        customerId = tempCustomer.id;\n      }\n    }\n\n    // Créer la commande avec transaction\n    const order = await prisma.$transaction(async (tx: any) => {\n      // Créer la commande\n      const orderId = globalThis.crypto.randomUUID();\n      const newOrder = await tx.order.create({\n        data: {\n          id: orderId,\n          customerId,\n          status: \"PENDING\",\n          totalAmount: validatedData.totalAmount || totalAmount,\n          shippingAddress: validatedData.shippingAddress,\n          updatedAt: new Date(),\n          items: {\n            create: orderItems,\n          },\n        },\n        include: {\n          customer: true,\n          items: {\n            include: {\n              product: true,\n              variant: true,\n            },\n          },\n        },\n      });\n\n      // Mettre à jour le stock des variantes\n      for (const item of validatedData.items) {\n        await tx.productVariant.update({\n          where: { id: item.variantId },\n          data: {\n            stock: {\n              decrement: item.quantity,\n            },\n          },\n        });\n      }\n\n      return newOrder;\n    });\n\n    const response: ApiResponse = {\n      success: true,\n      data: order,\n      message: \"Commande créée avec succès\",\n    };\n\n    return NextResponse.json(response, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating order:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error:\n        error instanceof Error\n          ? error.message\n          : \"Erreur lors de la création de la commande\",\n    };\n\n    return NextResponse.json(response, { status: 400 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,aAAa,aAAa,GAAG,CAAC;QACpC,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,QAAa,CAAC;QACpB,IAAI,YAAY,MAAM,UAAU,GAAG;QACnC,IAAI,QAAQ,MAAM,MAAM,GAAG;QAE3B,MAAM,OAAO,QAAQ,SAAS,SAAS;QACvC,MAAM,OAAO,SAAS,SAAS,UAAU;QAEzC,MAAM,CAAC,QAAQ,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACxC,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACpB;gBACA,SAAS;oBACP,UAAU;oBACV,OAAO;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;oBACF;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B;gBACA;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC5B;QAED,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;gBAAE;gBAAQ;YAAM;QACxB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QAExC,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO;QACT;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,qCAAqC;QACrC,MAAM,cAAc,QAAQ,OAAO,CAAC,GAAG,CAAC;QACxC,IAAI,CAAC,eAAe,CAAC,YAAY,QAAQ,CAAC,qBAAqB;YAC7D,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,QAAQ,IAAI;QAC3B,EAAE,OAAO,WAAW;YAClB,QAAQ,KAAK,CAAC,2BAA2B;YACzC,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,IAAI,CAAC,QAAQ,OAAO,SAAS,UAAU;YACrC,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,QAAQ,GAAG,CAAC,gDAAgD;QAE5D,4CAA4C;QAC5C,QAAQ,GAAG,CAAC,qBAAqB,sHAAA,CAAA,SAAM,GAAG,iBAAiB;QAC3D,QAAQ,GAAG,CACT,sBACA,sHAAA,CAAA,SAAM,EAAE,UAAU,iBAAiB;QAGrC,IAAI,CAAC,sHAAA,CAAA,SAAM,EAAE;YACX,MAAM,IAAI,MAAM;QAClB;QAEA,yBAAyB;QACzB,MAAM,gBAAgB,2HAAA,CAAA,oBAAiB,CAAC,KAAK,CAAC;QAE9C,4BAA4B;QAC5B,IAAI,cAAc;QAClB,MAAM,aAMD,EAAE;QAEP,QAAQ,GAAG,CAAC,wCAAwC,cAAc,KAAK;QAEvE,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;YACtC,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,KAAK,SAAS,EAAE;YAExD,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;YAC9B;YAEA,IAAI,CAAC,SAAS;gBACZ,QAAQ,KAAK,CACX,CAAC,UAAU,EAAE,KAAK,SAAS,CAAC,kCAAkC,CAAC,EAC/D,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;oBAAE,QAAQ;wBAAE,IAAI;wBAAM,MAAM;oBAAK;gBAAE;gBAEnE,MAAM,IAAI,MACR,CAAC,QAAQ,EAAE,KAAK,SAAS,CAAC,mEAAmE,CAAC;YAElG;YAEA,QAAQ,GAAG,CAAC,CAAC,kBAAkB,EAAE,QAAQ,IAAI,EAAE;YAE/C,0EAA0E;YAC1E,MAAM,YAAY,KAAK,KAAK,GACxB,OAAO,KAAK,KAAK,IACjB,OAAO,AAAC,QAAgB,SAAS;YACrC,MAAM,YAAY,YAAY,KAAK,QAAQ;YAC3C,eAAe;YAEf,QAAQ,GAAG,CAAC,CAAC,6BAA6B,EAAE,QAAQ,IAAI,EAAE;YAC1D,QAAQ,GAAG,CAAC,CAAC,wBAAwB,EAAE,KAAK,KAAK,IAAI,aAAa,CAAC,CAAC;YACpE,QAAQ,GAAG,CAAC,CAAC,yBAAyB,EAAE,QAAQ,SAAS,CAAC,CAAC,CAAC;YAC5D,QAAQ,GAAG,CAAC,CAAC,iBAAiB,EAAE,UAAU,CAAC,CAAC;YAC5C,QAAQ,GAAG,CAAC,CAAC,aAAa,EAAE,KAAK,QAAQ,EAAE;YAC3C,QAAQ,GAAG,CAAC,CAAC,eAAe,EAAE,UAAU,CAAC,CAAC;YAE1C,WAAW,IAAI,CAAC;gBACd,IAAI,WAAW,MAAM,CAAC,UAAU;gBAChC,WAAW,KAAK,SAAS;gBACzB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,OAAO;YACT;QACF;QAEA,6DAA6D;QAC7D,IAAI,aAAa,cAAc,UAAU;QAEzC,IAAI,CAAC,YAAY;YACf,mDAAmD;YACnD,MAAM,QACJ,cAAc,eAAe,CAAC,KAAK,IACnC,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,aAAa,CAAC;YAEpC,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxD,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI,kBAAkB;gBACpB,QAAQ,GAAG,CACT,CAAC,kCAAkC,EAAE,MAAM,gCAAgC,CAAC;gBAE9E,aAAa,iBAAiB,EAAE;YAClC,OAAO;gBACL,wDAAwD;gBACxD,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAChD,MAAM;wBACJ,IAAI,WAAW,MAAM,CAAC,UAAU;wBAChC;wBACA,UAAU;wBACV,WAAW,cAAc,eAAe,CAAC,SAAS;wBAClD,UAAU,cAAc,eAAe,CAAC,QAAQ;wBAChD,OAAO,cAAc,eAAe,CAAC,KAAK,IAAI;wBAC9C,SAAS,cAAc,eAAe,CAAC,MAAM,IAAI;wBACjD,YAAY,cAAc,eAAe,CAAC,UAAU;wBACpD,MAAM,cAAc,eAAe,CAAC,IAAI;wBACxC,WAAW,IAAI;oBACjB;gBACF;gBACA,aAAa,aAAa,EAAE;gBAC5B,QAAQ,GAAG,CAAC,CAAC,0CAA0C,EAAE,OAAO;YAClE;QACF,OAAO;YACL,gCAAgC;YAChC,MAAM,mBAAmB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACxD,OAAO;oBAAE,IAAI;gBAAW;YAC1B;YAEA,IAAI,CAAC,kBAAkB;gBACrB,QAAQ,KAAK,CACX,CAAC,OAAO,EAAE,WAAW,6CAA6C,CAAC;gBAErE,gEAAgE;gBAChE,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAChD,MAAM;wBACJ,IAAI,WAAW,MAAM,CAAC,UAAU;wBAChC,OACE,cAAc,eAAe,CAAC,KAAK,IACnC,CAAC,MAAM,EAAE,KAAK,GAAG,GAAG,aAAa,CAAC;wBACpC,UAAU;wBACV,WAAW,cAAc,eAAe,CAAC,SAAS;wBAClD,UAAU,cAAc,eAAe,CAAC,QAAQ;wBAChD,OAAO,cAAc,eAAe,CAAC,KAAK,IAAI;wBAC9C,SAAS,cAAc,eAAe,CAAC,MAAM,IAAI;wBACjD,YAAY,cAAc,eAAe,CAAC,UAAU;wBACpD,MAAM,cAAc,eAAe,CAAC,IAAI;wBACxC,WAAW,IAAI;oBACjB;gBACF;gBACA,aAAa,aAAa,EAAE;YAC9B;QACF;QAEA,qCAAqC;QACrC,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC7C,oBAAoB;YACpB,MAAM,UAAU,WAAW,MAAM,CAAC,UAAU;YAC5C,MAAM,WAAW,MAAM,GAAG,KAAK,CAAC,MAAM,CAAC;gBACrC,MAAM;oBACJ,IAAI;oBACJ;oBACA,QAAQ;oBACR,aAAa,cAAc,WAAW,IAAI;oBAC1C,iBAAiB,cAAc,eAAe;oBAC9C,WAAW,IAAI;oBACf,OAAO;wBACL,QAAQ;oBACV;gBACF;gBACA,SAAS;oBACP,UAAU;oBACV,OAAO;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;oBACF;gBACF;YACF;YAEA,uCAAuC;YACvC,KAAK,MAAM,QAAQ,cAAc,KAAK,CAAE;gBACtC,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;oBAC7B,OAAO;wBAAE,IAAI,KAAK,SAAS;oBAAC;oBAC5B,MAAM;wBACJ,OAAO;4BACL,WAAW,KAAK,QAAQ;wBAC1B;oBACF;gBACF;YACF;YAEA,OAAO;QACT;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;YACN,SAAS;QACX;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,yBAAyB;QAEvC,MAAM,WAAwB;YAC5B,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}