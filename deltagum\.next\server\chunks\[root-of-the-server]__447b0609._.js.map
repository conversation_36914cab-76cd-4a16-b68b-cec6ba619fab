{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/email.ts"], "sourcesContent": ["import { Resend } from \"resend\";\n\nif (!process.env.RESEND_API_KEY) {\n  throw new Error(\"RESEND_API_KEY is not defined in environment variables\");\n}\n\nexport const resend = new Resend(process.env.RESEND_API_KEY);\n\n// Types pour les emails\ninterface ContactFormData {\n  name: string;\n  email: string;\n  phone?: string;\n  message: string;\n  type: \"contact\" | \"professional\";\n}\n\ninterface OrderData {\n  orderId: string;\n  customerName: string;\n  customerEmail: string;\n  totalAmount: number;\n  items: Array<{\n    name: string;\n    quantity: number;\n    price: number;\n    flavor?: string;\n  }>;\n  shippingAddress: {\n    firstName: string;\n    lastName: string;\n    street: string;\n    city: string;\n    postalCode: string;\n    phone?: string;\n  };\n}\n\ninterface PasswordResetData {\n  email: string;\n  name: string;\n  resetUrl: string;\n}\n\nexport const sendOrderConfirmation = async (\n  to: string,\n  orderData: {\n    orderId: string;\n    customerName: string;\n    totalAmount: number;\n    items: Array<{\n      name: string;\n      flavor: string;\n      quantity: number;\n      price: number;\n    }>;\n  }\n) => {\n  try {\n    const { data, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [to],\n      subject: `Confirmation de commande #${orderData.orderId}`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h1 style=\"color: #FF6B9D;\">Merci pour votre commande !</h1>\n          <p>Bonjour ${orderData.customerName},</p>\n          <p>Votre commande #${orderData.orderId} a été confirmée.</p>\n          \n          <h2>Détails de la commande :</h2>\n          <ul>\n            ${orderData.items\n              .map(\n                (item) => `\n              <li>${item.name} - ${item.flavor} x${\n                  item.quantity\n                } - ${item.price.toFixed(2)}€</li>\n            `\n              )\n              .join(\"\")}\n          </ul>\n          \n          <p><strong>Total : ${orderData.totalAmount.toFixed(2)}€</strong></p>\n          \n          <p>Votre commande sera expédiée sous 24-48h.</p>\n          \n          <p>Merci de votre confiance !</p>\n          <p>L'équipe Deltagum</p>\n        </div>\n      `,\n    });\n\n    if (error) {\n      console.error(\"Error sending email:\", error);\n      return { success: false, error };\n    }\n\n    return { success: true, data };\n  } catch (error) {\n    console.error(\"Error sending email:\", error);\n    return { success: false, error };\n  }\n};\n\nexport const sendWelcomeEmail = async (to: string, customerName: string) => {\n  try {\n    const { data, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [to],\n      subject: \"Bienvenue chez Deltagum !\",\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h1 style=\"color: #FF6B9D;\">Bienvenue chez Deltagum !</h1>\n          <p>Bonjour ${customerName},</p>\n          <p>Merci de vous être inscrit chez Deltagum !</p>\n          <p>Découvrez nos délicieux chewing-gums aux saveurs naturelles de fruits.</p>\n          <p>Votre programme de fidélité a été activé avec 50 points de bienvenue !</p>\n          <p>À bientôt,</p>\n          <p>L'équipe Deltagum</p>\n        </div>\n      `,\n    });\n\n    if (error) {\n      console.error(\"Error sending welcome email:\", error);\n      return { success: false, error };\n    }\n\n    return { success: true, data };\n  } catch (error) {\n    console.error(\"Error sending welcome email:\", error);\n    return { success: false, error };\n  }\n};\n\n// Fonction pour envoyer les emails de contact\nexport const sendContactEmail = async (data: ContactFormData) => {\n  try {\n    const isProf = data.type === \"professional\";\n\n    const { data: result, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [\"<EMAIL>\"],\n      subject: isProf\n        ? \"Nouvelle demande professionnelle - Deltagum\"\n        : \"Nouveau message de contact - Deltagum\",\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">🍬 Deltagum</h1>\n            <p style=\"color: white; margin: 10px 0 0 0; opacity: 0.9;\">\n              ${\n                isProf\n                  ? \"Nouvelle demande professionnelle\"\n                  : \"Nouveau message de contact\"\n              }\n            </p>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 20px;\">\n            <h2 style=\"color: #333; margin-top: 0;\">Informations du contact</h2>\n            <p><strong>Nom :</strong> ${data.name}</p>\n            <p><strong>Email :</strong> ${data.email}</p>\n            ${\n              data.phone\n                ? `<p><strong>Téléphone :</strong> ${data.phone}</p>`\n                : \"\"\n            }\n            <p><strong>Type :</strong> ${\n              isProf ? \"Demande professionnelle/revendeur\" : \"Contact général\"\n            }</p>\n          </div>\n\n          <div style=\"background: white; padding: 25px; border: 1px solid #e5e7eb; border-radius: 8px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Message :</h3>\n            <p style=\"line-height: 1.6; color: #555;\">${data.message.replace(\n              /\\n/g,\n              \"<br>\"\n            )}</p>\n          </div>\n\n          <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n            <p style=\"color: #666; font-size: 14px;\">\n              Email envoyé automatiquement depuis le site Deltagum<br>\n              <a href=\"mailto:${\n                data.email\n              }\" style=\"color: #ec4899;\">Répondre directement</a>\n            </p>\n          </div>\n        </div>\n      `,\n      replyTo: data.email,\n    });\n\n    if (error) {\n      console.error(\"❌ Erreur envoi email de contact:\", error);\n      return { success: false, error };\n    }\n\n    console.log(\"✅ Email de contact envoyé:\", result);\n    return { success: true, data: result };\n  } catch (error) {\n    console.error(\"❌ Erreur envoi email de contact:\", error);\n    return { success: false, error };\n  }\n};\n\n// Fonction améliorée pour les confirmations de commande\nexport const sendOrderConfirmationEmail = async (data: OrderData) => {\n  try {\n    console.log(\"📧 Envoi email de confirmation de commande...\");\n    console.log(\"Données reçues:\", JSON.stringify(data, null, 2));\n    console.log(\n      \"Adresse de livraison:\",\n      JSON.stringify(data.shippingAddress, null, 2)\n    );\n\n    const itemsHtml = data.items\n      .map(\n        (item) => `\n      <tr style=\"border-bottom: 1px solid #e5e7eb;\">\n        <td style=\"padding: 12px 0; color: #333;\">\n          ${item.name}${item.flavor ? ` - ${item.flavor}` : \"\"}\n        </td>\n        <td style=\"padding: 12px 0; text-align: center; color: #666;\">\n          ${item.quantity}\n        </td>\n        <td style=\"padding: 12px 0; text-align: right; color: #333; font-weight: 500;\">\n          ${(item.price * item.quantity).toFixed(2)}€\n        </td>\n      </tr>\n    `\n      )\n      .join(\"\");\n\n    // Email au client\n    const customerResult = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [data.customerEmail],\n      subject: `Confirmation de commande #${data.orderId} - Deltagum`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">🍬 Deltagum</h1>\n            <p style=\"color: white; margin: 10px 0 0 0; opacity: 0.9;\">\n              Merci pour votre commande !\n            </p>\n          </div>\n\n          <div style=\"background: #f0fdf4; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h2 style=\"color: #166534; margin: 0 0 10px 0; font-size: 18px;\">✅ Commande confirmée</h2>\n            <p style=\"color: #166534; margin: 0;\">\n              Votre commande <strong>#${\n                data.orderId\n              }</strong> a été confirmée et sera traitée dans les plus brefs délais.\n            </p>\n          </div>\n\n          <div style=\"background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 25px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Détails de la commande</h3>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <thead>\n                <tr style=\"background: #f8f9fa;\">\n                  <th style=\"padding: 12px 0; text-align: left; color: #666; font-weight: 600;\">Produit</th>\n                  <th style=\"padding: 12px 0; text-align: center; color: #666; font-weight: 600;\">Qté</th>\n                  <th style=\"padding: 12px 0; text-align: right; color: #666; font-weight: 600;\">Total</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${itemsHtml}\n              </tbody>\n            </table>\n\n            <div style=\"margin-top: 20px; padding-top: 20px; border-top: 2px solid #ec4899;\">\n              <div style=\"text-align: right;\">\n                <span style=\"font-size: 18px; font-weight: bold; color: #333;\">\n                  Total : ${data.totalAmount.toFixed(2)}€\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Adresse de livraison</h3>\n            <p style=\"margin: 0; line-height: 1.6; color: #555;\">\n              ${data.shippingAddress.firstName} ${\n        data.shippingAddress.lastName\n      }<br>\n              ${data.shippingAddress.street}<br>\n              ${data.shippingAddress.postalCode} ${data.shippingAddress.city}\n              ${\n                data.shippingAddress.phone\n                  ? `<br>Tél: ${data.shippingAddress.phone}`\n                  : \"\"\n              }\n            </p>\n          </div>\n\n          <div style=\"background: #fef3c7; border: 1px solid #fbbf24; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h3 style=\"color: #92400e; margin-top: 0;\">⚠️ Informations importantes</h3>\n            <ul style=\"color: #92400e; margin: 0; padding-left: 20px;\">\n              <li>Produit contenant du Delta-9 THC (< 0.3%)</li>\n              <li>Réservé aux personnes majeures (18+)</li>\n              <li>Conforme à la réglementation européenne</li>\n              <li>Consommation responsable recommandée</li>\n            </ul>\n          </div>\n\n          <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n            <p style=\"color: #666; font-size: 14px; margin: 0;\">\n              Besoin d'aide ? Contactez-nous à\n              <a href=\"mailto:<EMAIL>\" style=\"color: #ec4899;\"><EMAIL></a>\n            </p>\n            <p style=\"color: #666; font-size: 12px; margin: 10px 0 0 0;\">\n              Deltagum - Délices au Delta-9 THC\n            </p>\n          </div>\n        </div>\n      `,\n    });\n\n    // Email de notification à l'admin\n    const adminResult = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [\"<EMAIL>\"],\n      subject: `Nouvelle commande #${data.orderId} - ${data.customerName}`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h2>🛒 Nouvelle commande reçue</h2>\n          <p><strong>Commande :</strong> #${data.orderId}</p>\n          <p><strong>Client :</strong> ${data.customerName} (${\n        data.customerEmail\n      })</p>\n          <p><strong>Montant :</strong> ${data.totalAmount.toFixed(2)}€</p>\n          <p><strong>Nombre d'articles :</strong> ${data.items.reduce(\n            (sum, item) => sum + item.quantity,\n            0\n          )}</p>\n\n          <h3>Articles commandés :</h3>\n          <ul>\n            ${data.items\n              .map(\n                (item) => `\n              <li>${item.name}${item.flavor ? ` - ${item.flavor}` : \"\"} x${\n                  item.quantity\n                } = ${(item.price * item.quantity).toFixed(2)}€</li>\n            `\n              )\n              .join(\"\")}\n          </ul>\n\n          <h3>Adresse de livraison :</h3>\n          <p>\n            ${data.shippingAddress?.firstName || \"N/A\"} ${\n        data.shippingAddress?.lastName || \"N/A\"\n      }<br>\n            ${data.shippingAddress?.street || \"Adresse non spécifiée\"}<br>\n            ${data.shippingAddress?.postalCode || \"\"} ${\n        data.shippingAddress?.city || \"Ville non spécifiée\"\n      }\n            ${\n              data.shippingAddress?.phone\n                ? `<br>Tél: ${data.shippingAddress.phone}`\n                : \"\"\n            }\n          </p>\n\n          <p><a href=\"${\n            process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\"\n          }/admin/dashboard\">Voir dans le dashboard admin</a></p>\n        </div>\n      `,\n    });\n\n    console.log(\"✅ Emails de commande envoyés:\", {\n      customerResult,\n      adminResult,\n    });\n    return { success: true, data: { customerResult, adminResult } };\n  } catch (error) {\n    console.error(\"❌ Erreur envoi emails de commande:\", error);\n    return { success: false, error };\n  }\n};\n\n// Fonction pour envoyer un email de réinitialisation de mot de passe\nexport const sendPasswordResetEmail = async (data: PasswordResetData) => {\n  try {\n    console.log(\"📧 Envoi email de réinitialisation de mot de passe...\");\n\n    const emailHtml = `\n      <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #f9fafb;\">\n        <div style=\"background: white; padding: 30px; border-radius: 12px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);\">\n          <div style=\"text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: #ec4899; font-size: 28px; margin: 0;\">🔐 Réinitialisation de mot de passe</h1>\n          </div>\n\n          <h2 style=\"color: #333; font-size: 20px; margin-bottom: 20px;\">\n            Bonjour ${data.name},\n          </h2>\n\n          <p style=\"color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 25px;\">\n            Vous avez demandé la réinitialisation de votre mot de passe pour votre compte Deltagum.\n          </p>\n\n          <p style=\"color: #666; font-size: 16px; line-height: 1.6; margin-bottom: 30px;\">\n            Cliquez sur le bouton ci-dessous pour créer un nouveau mot de passe :\n          </p>\n\n          <div style=\"text-align: center; margin: 30px 0;\">\n            <a href=\"${data.resetUrl}\"\n               style=\"background: linear-gradient(135deg, #ec4899, #f97316);\n                      color: white;\n                      padding: 15px 30px;\n                      text-decoration: none;\n                      border-radius: 8px;\n                      font-weight: bold;\n                      font-size: 16px;\n                      display: inline-block;\">\n              Réinitialiser mon mot de passe\n            </a>\n          </div>\n\n          <div style=\"background: #fef3c7; border: 1px solid #fbbf24; padding: 20px; border-radius: 8px; margin: 25px 0;\">\n            <p style=\"color: #92400e; font-size: 14px; margin: 0; line-height: 1.5;\">\n              <strong>⚠️ Important :</strong><br>\n              • Ce lien est valide pendant 1 heure seulement<br>\n              • Si vous n'avez pas demandé cette réinitialisation, ignorez cet email<br>\n              • Votre mot de passe actuel reste inchangé tant que vous n'en créez pas un nouveau\n            </p>\n          </div>\n\n          <p style=\"color: #666; font-size: 14px; line-height: 1.6; margin-top: 30px;\">\n            Si le bouton ne fonctionne pas, copiez et collez ce lien dans votre navigateur :<br>\n            <a href=\"${data.resetUrl}\" style=\"color: #ec4899; word-break: break-all;\">${data.resetUrl}</a>\n          </p>\n\n          <div style=\"border-top: 1px solid #e5e7eb; margin-top: 30px; padding-top: 20px; text-align: center;\">\n            <p style=\"color: #9ca3af; font-size: 12px; margin: 0;\">\n              Cet email a été envoyé par Deltagum<br>\n              Si vous avez des questions, contactez-nous à <EMAIL>\n            </p>\n          </div>\n        </div>\n      </div>\n    `;\n\n    const { data: result, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [data.email],\n      subject: \"🔐 Réinitialisation de votre mot de passe Deltagum\",\n      html: emailHtml,\n    });\n\n    if (error) {\n      console.error(\"❌ Erreur Resend:\", error);\n      return { success: false, error };\n    }\n\n    console.log(\"✅ Email de réinitialisation envoyé:\", result?.id);\n    return { success: true, data: result };\n  } catch (error) {\n    console.error(\n      \"❌ Erreur lors de l'envoi de l'email de réinitialisation:\",\n      error\n    );\n    return { success: false, error };\n  }\n};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,0QAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAsCpD,MAAM,wBAAwB,OACnC,IACA;IAYA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC/C,MAAM;YACN,IAAI;gBAAC;aAAG;YACR,SAAS,CAAC,0BAA0B,EAAE,UAAU,OAAO,EAAE;YACzD,MAAM,CAAC;;;qBAGQ,EAAE,UAAU,YAAY,CAAC;6BACjB,EAAE,UAAU,OAAO,CAAC;;;;YAIrC,EAAE,UAAU,KAAK,CACd,GAAG,CACF,CAAC,OAAS,CAAC;kBACT,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,EAC/B,KAAK,QAAQ,CACd,GAAG,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG;YAChC,CAAC,EAEE,IAAI,CAAC,IAAI;;;6BAGK,EAAE,UAAU,WAAW,CAAC,OAAO,CAAC,GAAG;;;;;;;MAO1D,CAAC;QACH;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,mBAAmB,OAAO,IAAY;IACjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC/C,MAAM;YACN,IAAI;gBAAC;aAAG;YACR,SAAS;YACT,MAAM,CAAC;;;qBAGQ,EAAE,aAAa;;;;;;;MAO9B,CAAC;QACH;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,SAAS,KAAK,IAAI,KAAK;QAE7B,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACvD,MAAM;YACN,IAAI;gBAAC;aAAyB;YAC9B,SAAS,SACL,gDACA;YACJ,MAAM,CAAC;;;;;cAKC,EACE,SACI,qCACA,6BACL;;;;;;sCAMuB,EAAE,KAAK,IAAI,CAAC;wCACV,EAAE,KAAK,KAAK,CAAC;YACzC,EACE,KAAK,KAAK,GACN,CAAC,gCAAgC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,GACnD,GACL;uCAC0B,EACzB,SAAS,sCAAsC,kBAChD;;;;;sDAKyC,EAAE,KAAK,OAAO,CAAC,OAAO,CAC9D,OACA,QACA;;;;;;8BAMgB,EACd,KAAK,KAAK,CACX;;;;MAIT,CAAC;YACD,SAAS,KAAK,KAAK;QACrB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,QAAQ,GAAG,CAAC;QACZ,QAAQ,GAAG,CAAC,mBAAmB,KAAK,SAAS,CAAC,MAAM,MAAM;QAC1D,QAAQ,GAAG,CACT,yBACA,KAAK,SAAS,CAAC,KAAK,eAAe,EAAE,MAAM;QAG7C,MAAM,YAAY,KAAK,KAAK,CACzB,GAAG,CACF,CAAC,OAAS,CAAC;;;UAGT,EAAE,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE,GAAG,GAAG;;;UAGrD,EAAE,KAAK,QAAQ,CAAC;;;UAGhB,EAAE,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG;;;IAGhD,CAAC,EAEE,IAAI,CAAC;QAER,kBAAkB;QAClB,MAAM,iBAAiB,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC9C,MAAM;YACN,IAAI;gBAAC,KAAK,aAAa;aAAC;YACxB,SAAS,CAAC,0BAA0B,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC;YAC/D,MAAM,CAAC;;;;;;;;;;;;sCAYyB,EACtB,KAAK,OAAO,CACb;;;;;;;;;;;;;;;gBAeC,EAAE,UAAU;;;;;;;0BAOF,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG;;;;;;;;;cAS1C,EAAE,KAAK,eAAe,CAAC,SAAS,CAAC,CAAC,EACxC,KAAK,eAAe,CAAC,QAAQ,CAC9B;cACO,EAAE,KAAK,eAAe,CAAC,MAAM,CAAC;cAC9B,EAAE,KAAK,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,IAAI,CAAC;cAC/D,EACE,KAAK,eAAe,CAAC,KAAK,GACtB,CAAC,SAAS,EAAE,KAAK,eAAe,CAAC,KAAK,EAAE,GACxC,GACL;;;;;;;;;;;;;;;;;;;;;;;;MAwBT,CAAC;QACH;QAEA,kCAAkC;QAClC,MAAM,cAAc,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC3C,MAAM;YACN,IAAI;gBAAC;aAAyB;YAC9B,SAAS,CAAC,mBAAmB,EAAE,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE;YACpE,MAAM,CAAC;;;0CAG6B,EAAE,KAAK,OAAO,CAAC;uCAClB,EAAE,KAAK,YAAY,CAAC,EAAE,EACrD,KAAK,aAAa,CACnB;wCACiC,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG;kDACpB,EAAE,KAAK,KAAK,CAAC,MAAM,CACzD,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAClC,GACA;;;;YAIA,EAAE,KAAK,KAAK,CACT,GAAG,CACF,CAAC,OAAS,CAAC;kBACT,EAAE,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE,GAAG,GAAG,EAAE,EACvD,KAAK,QAAQ,CACd,GAAG,EAAE,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG;YAClD,CAAC,EAEE,IAAI,CAAC,IAAI;;;;;YAKZ,EAAE,KAAK,eAAe,EAAE,aAAa,MAAM,CAAC,EAChD,KAAK,eAAe,EAAE,YAAY,MACnC;YACK,EAAE,KAAK,eAAe,EAAE,UAAU,wBAAwB;YAC1D,EAAE,KAAK,eAAe,EAAE,cAAc,GAAG,CAAC,EAC9C,KAAK,eAAe,EAAE,QAAQ,sBAC/B;YACK,EACE,KAAK,eAAe,EAAE,QAClB,CAAC,SAAS,EAAE,KAAK,eAAe,CAAC,KAAK,EAAE,GACxC,GACL;;;sBAGS,EACV,6DAAoC,wBACrC;;MAEL,CAAC;QACH;QAEA,QAAQ,GAAG,CAAC,iCAAiC;YAC3C;YACA;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;gBAAgB;YAAY;QAAE;IAChE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,yBAAyB,OAAO;IAC3C,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,MAAM,YAAY,CAAC;;;;;;;;oBAQH,EAAE,KAAK,IAAI,CAAC;;;;;;;;;;;;qBAYX,EAAE,KAAK,QAAQ,CAAC;;;;;;;;;;;;;;;;;;;;;;;;qBAwBhB,EAAE,KAAK,QAAQ,CAAC,iDAAiD,EAAE,KAAK,QAAQ,CAAC;;;;;;;;;;;IAWlG,CAAC;QAED,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACvD,MAAM;YACN,IAAI;gBAAC,KAAK,KAAK;aAAC;YAChB,SAAS;YACT,MAAM;QACR;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oBAAoB;YAClC,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,QAAQ,GAAG,CAAC,uCAAuC,QAAQ;QAC3D,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CACX,4DACA;QAEF,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 533, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/auth/forgot-password/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/prisma\";\nimport { sendPasswordResetEmail } from \"@/lib/email\";\nimport crypto from \"crypto\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { email } = await request.json();\n\n    if (!email) {\n      return NextResponse.json(\n        { success: false, error: \"Email requis\" },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier si l'utilisateur existe\n    const user = await prisma.customer.findUnique({\n      where: { email: email.toLowerCase() },\n    });\n\n    // Toujours retourner un succès pour des raisons de sécurité\n    // (ne pas révéler si un email existe ou non)\n    if (!user) {\n      return NextResponse.json({\n        success: true,\n        message: \"Si cet email existe, un lien de réinitialisation a été envoyé\",\n      });\n    }\n\n    // Générer un token de réinitialisation\n    const resetToken = crypto.randomBytes(32).toString(\"hex\");\n    const resetTokenExpiry = new Date(Date.now() + 3600000); // 1 heure\n\n    // Sauvegarder le token en base\n    await prisma.customer.update({\n      where: { id: user.id },\n      data: {\n        resetToken,\n        resetTokenExpiry,\n      },\n    });\n\n    // Envoyer l'email de réinitialisation\n    const resetUrl = `${process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\"}/auth/reset-password?token=${resetToken}`;\n    \n    await sendPasswordResetEmail({\n      email: user.email,\n      name: `${user.firstName} ${user.lastName}`,\n      resetUrl,\n    });\n\n    console.log(`📧 Email de réinitialisation envoyé à: ${user.email}`);\n\n    return NextResponse.json({\n      success: true,\n      message: \"Si cet email existe, un lien de réinitialisation a été envoyé\",\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de la demande de réinitialisation:\", error);\n    return NextResponse.json(\n      { success: false, error: \"Erreur serveur\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEpC,IAAI,CAAC,OAAO;YACV,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAe,GACxC;gBAAE,QAAQ;YAAI;QAElB;QAEA,mCAAmC;QACnC,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE,OAAO,MAAM,WAAW;YAAG;QACtC;QAEA,4DAA4D;QAC5D,6CAA6C;QAC7C,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBACvB,SAAS;gBACT,SAAS;YACX;QACF;QAEA,uCAAuC;QACvC,MAAM,aAAa,qGAAA,CAAA,UAAM,CAAC,WAAW,CAAC,IAAI,QAAQ,CAAC;QACnD,MAAM,mBAAmB,IAAI,KAAK,KAAK,GAAG,KAAK,UAAU,UAAU;QAEnE,+BAA+B;QAC/B,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,MAAM;gBACJ;gBACA;YACF;QACF;QAEA,sCAAsC;QACtC,MAAM,WAAW,GAAG,6DAAoC,wBAAwB,2BAA2B,EAAE,YAAY;QAEzH,MAAM,CAAA,GAAA,qHAAA,CAAA,yBAAsB,AAAD,EAAE;YAC3B,OAAO,KAAK,KAAK;YACjB,MAAM,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE;YAC1C;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,uCAAuC,EAAE,KAAK,KAAK,EAAE;QAElE,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,kDAAkD;QAChE,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAiB,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}