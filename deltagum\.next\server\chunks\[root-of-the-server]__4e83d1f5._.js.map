{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 127, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/auth/reset-password/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/prisma\";\nimport bcrypt from \"bcryptjs\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { token, password } = await request.json();\n\n    if (!token || !password) {\n      return NextResponse.json(\n        { success: false, error: \"Token et mot de passe requis\" },\n        { status: 400 }\n      );\n    }\n\n    if (password.length < 6) {\n      return NextResponse.json(\n        { success: false, error: \"Le mot de passe doit contenir au moins 6 caractères\" },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier le token\n    const user = await prisma.customer.findFirst({\n      where: {\n        resetToken: token,\n        resetTokenExpiry: {\n          gt: new Date(), // Token non expiré\n        },\n      },\n    });\n\n    if (!user) {\n      return NextResponse.json(\n        { success: false, error: \"Token invalide ou expiré\" },\n        { status: 400 }\n      );\n    }\n\n    // Hasher le nouveau mot de passe\n    const hashedPassword = await bcrypt.hash(password, 12);\n\n    // Mettre à jour le mot de passe et supprimer le token\n    await prisma.customer.update({\n      where: { id: user.id },\n      data: {\n        password: hashedPassword,\n        resetToken: null,\n        resetTokenExpiry: null,\n      },\n    });\n\n    console.log(`🔐 Mot de passe réinitialisé pour: ${user.email}`);\n\n    return NextResponse.json({\n      success: true,\n      message: \"Mot de passe réinitialisé avec succès\",\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de la réinitialisation:\", error);\n    return NextResponse.json(\n      { success: false, error: \"Erreur serveur\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,QAAQ,IAAI;QAE9C,IAAI,CAAC,SAAS,CAAC,UAAU;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA+B,GACxD;gBAAE,QAAQ;YAAI;QAElB;QAEA,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAsD,GAC/E;gBAAE,QAAQ;YAAI;QAElB;QAEA,oBAAoB;QACpB,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;YAC3C,OAAO;gBACL,YAAY;gBACZ,kBAAkB;oBAChB,IAAI,IAAI;gBACV;YACF;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAA2B,GACpD;gBAAE,QAAQ;YAAI;QAElB;QAEA,iCAAiC;QACjC,MAAM,iBAAiB,MAAM,wLAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,sDAAsD;QACtD,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC3B,OAAO;gBAAE,IAAI,KAAK,EAAE;YAAC;YACrB,MAAM;gBACJ,UAAU;gBACV,YAAY;gBACZ,kBAAkB;YACpB;QACF;QAEA,QAAQ,GAAG,CAAC,CAAC,mCAAmC,EAAE,KAAK,KAAK,EAAE;QAE9D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,uCAAuC;QACrD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAiB,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}