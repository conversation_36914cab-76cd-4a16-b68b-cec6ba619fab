{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/admin/orders/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { ApiResponse } from \"@/types\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    \n    // Paramètres de pagination\n    const page = parseInt(searchParams.get(\"page\") || \"1\");\n    const limit = parseInt(searchParams.get(\"limit\") || \"10\");\n    const skip = (page - 1) * limit;\n    \n    // Paramètres de filtrage\n    const search = searchParams.get(\"search\") || \"\";\n    const status = searchParams.get(\"status\") || \"\";\n    \n    // Construction des filtres\n    const where: any = {};\n    \n    // Filtre par statut\n    if (status && status !== \"all\") {\n      where.status = status;\n    }\n    \n    // Filtre de recherche\n    if (search) {\n      where.OR = [\n        {\n          id: {\n            contains: search,\n            mode: \"insensitive\",\n          },\n        },\n        {\n          customer: {\n            firstName: {\n              contains: search,\n              mode: \"insensitive\",\n            },\n          },\n        },\n        {\n          customer: {\n            lastName: {\n              contains: search,\n              mode: \"insensitive\",\n            },\n          },\n        },\n        {\n          customer: {\n            email: {\n              contains: search,\n              mode: \"insensitive\",\n            },\n          },\n        },\n        {\n          shippingFirstName: {\n            contains: search,\n            mode: \"insensitive\",\n          },\n        },\n        {\n          shippingLastName: {\n            contains: search,\n            mode: \"insensitive\",\n          },\n        },\n      ];\n    }\n\n    // Compter le total des commandes\n    const totalOrders = await prisma.order.count({ where });\n    \n    // Récupérer les commandes avec pagination\n    const orders = await prisma.order.findMany({\n      where,\n      include: {\n        customer: {\n          select: {\n            id: true,\n            firstName: true,\n            lastName: true,\n            email: true,\n          },\n        },\n        items: {\n          include: {\n            product: {\n              select: {\n                id: true,\n                name: true,\n              },\n            },\n            variant: {\n              select: {\n                id: true,\n                flavor: true,\n              },\n            },\n          },\n        },\n      },\n      orderBy: {\n        createdAt: \"desc\",\n      },\n      skip,\n      take: limit,\n    });\n\n    // Calculer les métadonnées de pagination\n    const totalPages = Math.ceil(totalOrders / limit);\n    const hasNextPage = page < totalPages;\n    const hasPrevPage = page > 1;\n\n    const response: ApiResponse = {\n      success: true,\n      data: {\n        orders,\n        pagination: {\n          currentPage: page,\n          totalPages,\n          totalOrders,\n          ordersPerPage: limit,\n          hasNextPage,\n          hasPrevPage,\n        },\n        // Compatibilité avec l'ancien format\n        totalOrders,\n        totalPages,\n      },\n      message: `${orders.length} commande${orders.length > 1 ? 's' : ''} récupérée${orders.length > 1 ? 's' : ''}`,\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Erreur lors de la récupération des commandes:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error: error instanceof Error ? error.message : \"Erreur interne du serveur\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n\n// Mettre à jour le statut d'une commande\nexport async function PATCH(request: NextRequest) {\n  try {\n    const body = await request.json();\n    const { orderId, status } = body;\n\n    if (!orderId || !status) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: \"ID de commande et statut requis\",\n        },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier que le statut est valide\n    const validStatuses = [\"pending\", \"confirmed\", \"shipped\", \"delivered\", \"cancelled\"];\n    if (!validStatuses.includes(status)) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: \"Statut invalide\",\n        },\n        { status: 400 }\n      );\n    }\n\n    // Mettre à jour la commande\n    const updatedOrder = await prisma.order.update({\n      where: { id: orderId },\n      data: { status },\n      include: {\n        customer: {\n          select: {\n            firstName: true,\n            lastName: true,\n            email: true,\n          },\n        },\n        items: {\n          include: {\n            product: {\n              select: {\n                name: true,\n              },\n            },\n            variant: {\n              select: {\n                flavor: true,\n              },\n            },\n          },\n        },\n      },\n    });\n\n    const response: ApiResponse = {\n      success: true,\n      data: updatedOrder,\n      message: \"Statut de la commande mis à jour avec succès\",\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Erreur lors de la mise à jour de la commande:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error: error instanceof Error ? error.message : \"Erreur interne du serveur\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n\n// Supprimer une commande (admin seulement)\nexport async function DELETE(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const orderId = searchParams.get(\"id\");\n\n    if (!orderId) {\n      return NextResponse.json(\n        {\n          success: false,\n          error: \"ID de commande requis\",\n        },\n        { status: 400 }\n      );\n    }\n\n    // Supprimer la commande et ses items (cascade)\n    await prisma.order.delete({\n      where: { id: orderId },\n    });\n\n    const response: ApiResponse = {\n      success: true,\n      message: \"Commande supprimée avec succès\",\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Erreur lors de la suppression de la commande:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error: error instanceof Error ? error.message : \"Erreur interne du serveur\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;;;AAAA;AAEA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAE5C,2BAA2B;QAC3B,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,yBAAyB;QACzB,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,2BAA2B;QAC3B,MAAM,QAAa,CAAC;QAEpB,oBAAoB;QACpB,IAAI,UAAU,WAAW,OAAO;YAC9B,MAAM,MAAM,GAAG;QACjB;QAEA,sBAAsB;QACtB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBACE,IAAI;wBACF,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA;oBACE,UAAU;wBACR,WAAW;4BACT,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;gBACA;oBACE,UAAU;wBACR,UAAU;4BACR,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;gBACA;oBACE,UAAU;wBACR,OAAO;4BACL,UAAU;4BACV,MAAM;wBACR;oBACF;gBACF;gBACA;oBACE,mBAAmB;wBACjB,UAAU;wBACV,MAAM;oBACR;gBACF;gBACA;oBACE,kBAAkB;wBAChB,UAAU;wBACV,MAAM;oBACR;gBACF;aACD;QACH;QAEA,iCAAiC;QACjC,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;YAAE;QAAM;QAErD,0CAA0C;QAC1C,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;YACzC;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,IAAI;wBACJ,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;gBACA,OAAO;oBACL,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,IAAI;gCACJ,MAAM;4BACR;wBACF;wBACA,SAAS;4BACP,QAAQ;gCACN,IAAI;gCACJ,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;YACA,SAAS;gBACP,WAAW;YACb;YACA;YACA,MAAM;QACR;QAEA,yCAAyC;QACzC,MAAM,aAAa,KAAK,IAAI,CAAC,cAAc;QAC3C,MAAM,cAAc,OAAO;QAC3B,MAAM,cAAc,OAAO;QAE3B,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;gBACJ;gBACA,YAAY;oBACV,aAAa;oBACb;oBACA;oBACA,eAAe;oBACf;oBACA;gBACF;gBACA,qCAAqC;gBACrC;gBACA;YACF;YACA,SAAS,GAAG,OAAO,MAAM,CAAC,SAAS,EAAE,OAAO,MAAM,GAAG,IAAI,MAAM,GAAG,UAAU,EAAE,OAAO,MAAM,GAAG,IAAI,MAAM,IAAI;QAC9G;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAE/D,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF;AAGO,eAAe,MAAM,OAAoB;IAC9C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;QAE5B,IAAI,CAAC,WAAW,CAAC,QAAQ;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,oCAAoC;QACpC,MAAM,gBAAgB;YAAC;YAAW;YAAa;YAAW;YAAa;SAAY;QACnF,IAAI,CAAC,cAAc,QAAQ,CAAC,SAAS;YACnC,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,4BAA4B;QAC5B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YAC7C,OAAO;gBAAE,IAAI;YAAQ;YACrB,MAAM;gBAAE;YAAO;YACf,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,WAAW;wBACX,UAAU;wBACV,OAAO;oBACT;gBACF;gBACA,OAAO;oBACL,SAAS;wBACP,SAAS;4BACP,QAAQ;gCACN,MAAM;4BACR;wBACF;wBACA,SAAS;4BACP,QAAQ;gCACN,QAAQ;4BACV;wBACF;oBACF;gBACF;YACF;QACF;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;YACN,SAAS;QACX;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAE/D,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF;AAGO,eAAe,OAAO,OAAoB;IAC/C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,UAAU,aAAa,GAAG,CAAC;QAEjC,IAAI,CAAC,SAAS;YACZ,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,SAAS;gBACT,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+CAA+C;QAC/C,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI;YAAQ;QACvB;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,SAAS;QACX;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iDAAiD;QAE/D,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;QAClD;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}