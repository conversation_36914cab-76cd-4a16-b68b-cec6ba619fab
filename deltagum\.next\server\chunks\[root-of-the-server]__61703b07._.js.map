{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from \"stripe\";\n\nif (!process.env.STRIPE_SECRET_KEY) {\n  throw new Error(\"STRIPE_SECRET_KEY is not defined in environment variables\");\n}\n\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {\n  apiVersion: \"2025-06-30.basil\",\n  typescript: true,\n});\n\nexport const getStripePublishableKey = () => {\n  if (!process.env.STRIPE_PUBLISHABLE_KEY) {\n    throw new Error(\n      \"STRIPE_PUBLISHABLE_KEY is not defined in environment variables\"\n    );\n  }\n  return process.env.STRIPE_PUBLISHABLE_KEY;\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,+OAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAC9D,YAAY;IACZ,YAAY;AACd;AAEO,MAAM,0BAA0B;IACrC,IAAI,CAAC,QAAQ,GAAG,CAAC,sBAAsB,EAAE;QACvC,MAAM,IAAI,MACR;IAEJ;IACA,OAAO,QAAQ,GAAG,CAAC,sBAAsB;AAC3C", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/checkout/session/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { stripe } from \"@/lib/stripe\";\nimport { ApiResponse } from \"@/types\";\nimport { NextRequest, NextResponse } from \"next/server\";\nimport { z } from \"zod\";\n\n// Schéma simple pour créer une session à partir d'une commande existante\nconst createSessionSchema = z.object({\n  orderId: z.string().min(1, \"ID de commande requis\"),\n});\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n\n    // Validation des données\n    const { orderId } = createSessionSchema.parse(body);\n\n    // Récupérer la commande avec tous les détails\n    const order = await prisma.order.findUnique({\n      where: { id: orderId },\n      include: {\n        customer: true,\n        items: {\n          include: {\n            product: true,\n            variant: true,\n          },\n        },\n      },\n    });\n\n    if (!order) {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Commande non trouvée\",\n      };\n      return NextResponse.json(response, { status: 404 });\n    }\n\n    // Vérifier que la commande n'a pas déjà été payée\n    if (order.status === \"PAID\") {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Cette commande a déjà été payée\",\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n\n    // Créer les line items pour Stripe\n    const lineItems: any[] = [];\n\n    for (const item of order.items) {\n      lineItems.push({\n        price_data: {\n          currency: \"eur\",\n          product_data: {\n            name: `${item.product.name} - ${\n              item.variant?.flavor || \"Standard\"\n            }`,\n            description: item.product.description,\n            images: item.product.image\n              ? [\n                  item.product.image.startsWith(\"http\")\n                    ? item.product.image\n                    : `${process.env.NEXTAUTH_URL}${item.product.image}`,\n                ]\n              : [],\n            metadata: {\n              productId: item.productId,\n              variantId: item.variantId,\n            },\n          },\n          unit_amount: Math.round(Number(item.price) * 100), // Prix en centimes\n        },\n        quantity: item.quantity,\n      });\n    }\n\n    // Créer la session Stripe Checkout\n    const session = await stripe.checkout.sessions.create({\n      payment_method_types: [\"card\"],\n      line_items: lineItems,\n      mode: \"payment\",\n      customer_email: order.customer.email,\n      success_url: `${process.env.NEXTAUTH_URL}/success?session_id={CHECKOUT_SESSION_ID}&order_id=${order.id}`,\n      cancel_url: `${process.env.NEXTAUTH_URL}/cancel`,\n      metadata: {\n        orderId: order.id,\n        customerId: order.customerId,\n      },\n      shipping_address_collection: {\n        allowed_countries: [\"FR\", \"BE\", \"CH\", \"LU\", \"MC\"],\n      },\n      billing_address_collection: \"required\",\n      phone_number_collection: {\n        enabled: true,\n      },\n      custom_text: {\n        shipping_address: {\n          message:\n            \"Veuillez indiquer votre adresse de livraison pour vos délicieux bonbons Deltagum !\",\n        },\n      },\n      // Pré-remplir les informations client si disponibles\n      customer_creation: \"if_required\",\n      ...(order.customer.firstName &&\n        order.customer.lastName && {\n          custom_fields: [\n            {\n              key: \"customer_name\",\n              label: {\n                type: \"custom\",\n                custom: \"Nom complet\",\n              },\n              type: \"text\",\n              optional: false,\n            },\n          ],\n        }),\n    });\n\n    // Mettre à jour la commande avec l'ID de session (statut reste PENDING)\n    await prisma.order.update({\n      where: { id: order.id },\n      data: {\n        stripePaymentId: session.id,\n        // Le statut sera mis à jour à \"PAID\" par le webhook après confirmation\n      },\n    });\n\n    const response: ApiResponse = {\n      success: true,\n      data: {\n        sessionId: session.id,\n        url: session.url,\n        orderId: order.id,\n      },\n      message: \"Session de paiement créée avec succès\",\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Error creating checkout session:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error:\n        error instanceof Error\n          ? error.message\n          : \"Erreur lors de la création de la session de paiement\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;AACA;;;;;AAEA,yEAAyE;AACzE,MAAM,sBAAsB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AAC7B;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,yBAAyB;QACzB,MAAM,EAAE,OAAO,EAAE,GAAG,oBAAoB,KAAK,CAAC;QAE9C,8CAA8C;QAC9C,MAAM,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;YAC1C,OAAO;gBAAE,IAAI;YAAQ;YACrB,SAAS;gBACP,UAAU;gBACV,OAAO;oBACL,SAAS;wBACP,SAAS;wBACT,SAAS;oBACX;gBACF;YACF;QACF;QAEA,IAAI,CAAC,OAAO;YACV,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,kDAAkD;QAClD,IAAI,MAAM,MAAM,KAAK,QAAQ;YAC3B,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,mCAAmC;QACnC,MAAM,YAAmB,EAAE;QAE3B,KAAK,MAAM,QAAQ,MAAM,KAAK,CAAE;YAC9B,UAAU,IAAI,CAAC;gBACb,YAAY;oBACV,UAAU;oBACV,cAAc;wBACZ,MAAM,GAAG,KAAK,OAAO,CAAC,IAAI,CAAC,GAAG,EAC5B,KAAK,OAAO,EAAE,UAAU,YACxB;wBACF,aAAa,KAAK,OAAO,CAAC,WAAW;wBACrC,QAAQ,KAAK,OAAO,CAAC,KAAK,GACtB;4BACE,KAAK,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,UAC1B,KAAK,OAAO,CAAC,KAAK,GAClB,GAAG,QAAQ,GAAG,CAAC,YAAY,GAAG,KAAK,OAAO,CAAC,KAAK,EAAE;yBACvD,GACD,EAAE;wBACN,UAAU;4BACR,WAAW,KAAK,SAAS;4BACzB,WAAW,KAAK,SAAS;wBAC3B;oBACF;oBACA,aAAa,KAAK,KAAK,CAAC,OAAO,KAAK,KAAK,IAAI;gBAC/C;gBACA,UAAU,KAAK,QAAQ;YACzB;QACF;QAEA,mCAAmC;QACnC,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACpD,sBAAsB;gBAAC;aAAO;YAC9B,YAAY;YACZ,MAAM;YACN,gBAAgB,MAAM,QAAQ,CAAC,KAAK;YACpC,aAAa,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,mDAAmD,EAAE,MAAM,EAAE,EAAE;YACxG,YAAY,GAAG,QAAQ,GAAG,CAAC,YAAY,CAAC,OAAO,CAAC;YAChD,UAAU;gBACR,SAAS,MAAM,EAAE;gBACjB,YAAY,MAAM,UAAU;YAC9B;YACA,6BAA6B;gBAC3B,mBAAmB;oBAAC;oBAAM;oBAAM;oBAAM;oBAAM;iBAAK;YACnD;YACA,4BAA4B;YAC5B,yBAAyB;gBACvB,SAAS;YACX;YACA,aAAa;gBACX,kBAAkB;oBAChB,SACE;gBACJ;YACF;YACA,qDAAqD;YACrD,mBAAmB;YACnB,GAAI,MAAM,QAAQ,CAAC,SAAS,IAC1B,MAAM,QAAQ,CAAC,QAAQ,IAAI;gBACzB,eAAe;oBACb;wBACE,KAAK;wBACL,OAAO;4BACL,MAAM;4BACN,QAAQ;wBACV;wBACA,MAAM;wBACN,UAAU;oBACZ;iBACD;YACH,CAAC;QACL;QAEA,wEAAwE;QACxE,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;YACxB,OAAO;gBAAE,IAAI,MAAM,EAAE;YAAC;YACtB,MAAM;gBACJ,iBAAiB,QAAQ,EAAE;YAE7B;QACF;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;gBACJ,WAAW,QAAQ,EAAE;gBACrB,KAAK,QAAQ,GAAG;gBAChB,SAAS,MAAM,EAAE;YACnB;YACA,SAAS;QACX;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAElD,MAAM,WAAwB;YAC5B,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}