{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  var __prisma: PrismaClient | undefined;\n}\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\n// Créer le client Prisma avec gestion d'erreur\nlet prismaInstance: PrismaClient;\n\ntry {\n  prismaInstance = new PrismaClient({\n    log:\n      process.env.NODE_ENV === \"development\"\n        ? [\"query\", \"error\", \"warn\"]\n        : [\"error\"],\n  });\n  console.log(\"✅ Prisma client créé avec succès\");\n} catch (error) {\n  console.error(\"❌ Erreur création Prisma client:\", error);\n  throw error;\n}\n\nexport const prisma = prismaInstance;\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalForPrisma.prisma = prisma;\n  globalThis.__prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMA,MAAM,kBAAkB;AAIxB,+CAA+C;AAC/C,IAAI;AAEJ,IAAI;IACF,iBAAiB,IAAI,6HAAA,CAAA,eAAY,CAAC;QAChC,KACE,uCACI;YAAC;YAAS;YAAS;SAAO;IAElC;IACA,QAAQ,GAAG,CAAC;AACd,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,MAAM;AACR;AAEO,MAAM,SAAS;AAEtB,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;IACzB,WAAW,QAAQ,GAAG;AACxB", "debugId": null}}, {"offset": {"line": 92, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/validations.ts"], "sourcesContent": ["import { z } from \"zod\";\n\n// Schémas pour les enums\nexport const flavorTypeSchema = z.enum([\n  \"STRAWBERRY\",\n  \"BLUEBERRY\",\n  \"APPLE\",\n  \"CHOCOLATE\",\n  \"VANILL<PERSON>\",\n  \"MYRTILLE\",\n]);\nexport const orderStatusSchema = z.enum([\n  \"PENDING\",\n  \"PAID\",\n  \"SHIPPED\",\n  \"DELIVERED\",\n  \"CANCELLED\",\n]);\nexport const loyaltyLevelSchema = z.enum([\n  \"BRONZE\",\n  \"SILVER\",\n  \"GOLD\",\n  \"PLATINUM\",\n]);\n\n// Schémas pour les entités de base\nexport const customerSchema = z.object({\n  id: z.string().optional(),\n  email: z.string().email(\"Email invalide\"),\n  password: z\n    .string()\n    .min(6, \"Le mot de passe doit contenir au moins 6 caractères\")\n    .optional(),\n  firstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n  lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  phone: z.string().min(10, \"Numéro de téléphone invalide\").optional(),\n  address: z\n    .string()\n    .min(5, \"L'adresse doit contenir au moins 5 caractères\")\n    .optional(),\n  postalCode: z\n    .string()\n    .regex(/^\\d{5}$/, \"Code postal invalide (5 chiffres)\")\n    .optional(),\n  city: z\n    .string()\n    .min(2, \"La ville doit contenir au moins 2 caractères\")\n    .optional(),\n});\n\nexport const shippingAddressSchema = z.object({\n  firstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n  lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\").optional(),\n  street: z.string().min(5, \"L'adresse doit contenir au moins 5 caractères\"),\n  city: z.string().min(2, \"La ville doit contenir au moins 2 caractères\"),\n  postalCode: z.string().regex(/^\\d{5}$/, \"Code postal invalide (5 chiffres)\"),\n  country: z.string().min(2, \"Pays requis\"),\n  phone: z.string().optional(),\n});\n\nexport const productSchema = z.object({\n  id: z.string().optional(),\n  name: z\n    .string()\n    .min(2, \"Le nom du produit doit contenir au moins 2 caractères\"),\n  description: z\n    .string()\n    .min(10, \"La description doit contenir au moins 10 caractères\"),\n  basePrice: z\n    .union([z.number(), z.string().transform((val) => parseFloat(val))])\n    .refine((val) => val > 0, \"Le prix doit être positif\"),\n  image: z\n    .string()\n    .min(1, \"Une image est requise\")\n    .refine((val) => {\n      // Accepter les URLs complètes, les chemins absolus et les uploads\n      return (\n        val.startsWith(\"http\") ||\n        val.startsWith(\"/\") ||\n        val.startsWith(\"./\") ||\n        val.includes(\"/uploads/\") ||\n        val.includes(\"/img/\")\n      );\n    }, \"URL d'image invalide\"),\n  active: z.boolean().default(true),\n  dosage: z.string().optional(),\n  variants: z.array(z.any()).optional(),\n  pricingTiers: z.array(z.any()).optional(),\n});\n\nexport const productVariantSchema = z.object({\n  id: z.string().optional(),\n  productId: z.string(),\n  flavor: flavorTypeSchema,\n  color: z.string().regex(/^#[0-9A-F]{6}$/i, \"Couleur hexadécimale invalide\"),\n  stock: z.number().int().min(0, \"Le stock ne peut pas être négatif\"),\n  sku: z\n    .string()\n    .min(3, \"Le SKU doit contenir au moins 3 caractères\")\n    .optional(),\n  images: z\n    .array(\n      z.string().refine((val) => {\n        // Accepter les URLs complètes, les chemins absolus et les uploads\n        return (\n          val.startsWith(\"http\") ||\n          val.startsWith(\"/\") ||\n          val.startsWith(\"./\") ||\n          val.includes(\"/uploads/\") ||\n          val.includes(\"/img/\")\n        );\n      }, \"URL ou chemin d'image invalide\")\n    )\n    .default([\"/img/placeholder.svg\"]),\n});\n\n// Schémas pour le panier\nexport const cartItemSchema = z.object({\n  id: z.string().optional(),\n  productId: z.string(),\n  variantId: z.string(),\n  name: z.string(),\n  flavor: flavorTypeSchema,\n  color: z.string(),\n  price: z.number().positive(),\n  quantity: z.number().int().positive(\"La quantité doit être positive\"),\n  image: z.string().url(),\n});\n\nexport const addToCartSchema = z.object({\n  productId: z.string(),\n  variantId: z.string(),\n  quantity: z\n    .number()\n    .int()\n    .positive()\n    .max(10, \"Maximum 10 articles par produit\"),\n});\n\n// Schémas pour les commandes\nexport const orderItemSchema = z.object({\n  productId: z.string(),\n  variantId: z.string(),\n  quantity: z.number().int().positive(),\n});\n\nexport const createOrderSchema = z.object({\n  customerId: z.string().optional(), // Optionnel pour les commandes invités\n  items: z.array(orderItemSchema).min(1, \"Au moins un article requis\"),\n  shippingAddress: shippingAddressSchema,\n  totalAmount: z.number().positive().optional(), // Optionnel, sera calculé si non fourni\n});\n\nexport const updateOrderStatusSchema = z.object({\n  orderId: z.string(),\n  status: orderStatusSchema,\n});\n\n// Schémas pour les paiements\nexport const paymentIntentSchema = z.object({\n  orderId: z.string(),\n  amount: z.number().positive(),\n  currency: z.string().length(3).default(\"EUR\"),\n});\n\nexport const stripeWebhookSchema = z.object({\n  type: z.string(),\n  data: z.object({\n    object: z.any(),\n  }),\n});\n\n// Schémas pour l'authentification\nexport const signInSchema = z.object({\n  email: z.string().email(\"Email invalide\"),\n  password: z\n    .string()\n    .min(6, \"Le mot de passe doit contenir au moins 6 caractères\"),\n});\n\nexport const signUpSchema = z\n  .object({\n    email: z.string().email(\"Email invalide\"),\n    password: z\n      .string()\n      .min(6, \"Le mot de passe doit contenir au moins 6 caractères\"),\n    confirmPassword: z.string(),\n    firstName: z\n      .string()\n      .min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n    lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n    acceptTerms: z\n      .boolean()\n      .refine((val) => val === true, \"Vous devez accepter les conditions\"),\n  })\n  .refine((data) => data.password === data.confirmPassword, {\n    message: \"Les mots de passe ne correspondent pas\",\n    path: [\"confirmPassword\"],\n  });\n\n// Schémas pour les formulaires de contact\nexport const contactSchema = z.object({\n  name: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\"),\n  subject: z.string().min(5, \"Le sujet doit contenir au moins 5 caractères\"),\n  message: z\n    .string()\n    .min(10, \"Le message doit contenir au moins 10 caractères\"),\n});\n\nexport const newsletterSchema = z.object({\n  email: z.string().email(\"Email invalide\"),\n});\n\n// Schémas pour les avis clients\nexport const reviewSchema = z.object({\n  productId: z.string(),\n  customerId: z.string(),\n  rating: z.number().int().min(1).max(5),\n  title: z.string().min(5, \"Le titre doit contenir au moins 5 caractères\"),\n  comment: z\n    .string()\n    .min(10, \"Le commentaire doit contenir au moins 10 caractères\"),\n});\n\n// Schémas pour les paramètres utilisateur\nexport const userPreferencesSchema = z.object({\n  emailNotifications: z.boolean().default(true),\n  smsNotifications: z.boolean().default(false),\n  marketingEmails: z.boolean().default(true),\n  language: z.enum([\"fr\", \"en\"]).default(\"fr\"),\n  currency: z.enum([\"EUR\", \"USD\"]).default(\"EUR\"),\n});\n\n// Schéma pour le checkout\nexport const checkoutSchema = z.object({\n  customer: customerSchema,\n  shippingAddress: shippingAddressSchema,\n  paymentMethod: z.enum([\"card\", \"paypal\", \"apple_pay\", \"google_pay\"]),\n  items: z.array(cartItemSchema).min(1, \"Au moins un article requis\"),\n  promoCode: z.string().optional(),\n  acceptTerms: z\n    .boolean()\n    .refine((val) => val === true, \"Vous devez accepter les conditions\"),\n});\n\n// Types inférés des schémas\nexport type CustomerFormData = z.infer<typeof customerSchema>;\nexport type ShippingAddressFormData = z.infer<typeof shippingAddressSchema>;\nexport type ProductFormData = z.infer<typeof productSchema>;\nexport type ProductVariantFormData = z.infer<typeof productVariantSchema>;\nexport type CartItemFormData = z.infer<typeof cartItemSchema>;\nexport type AddToCartFormData = z.infer<typeof addToCartSchema>;\nexport type CreateOrderFormData = z.infer<typeof createOrderSchema>;\nexport type SignInFormData = z.infer<typeof signInSchema>;\nexport type SignUpFormData = z.infer<typeof signUpSchema>;\nexport type ContactFormData = z.infer<typeof contactSchema>;\nexport type NewsletterFormData = z.infer<typeof newsletterSchema>;\nexport type ReviewFormData = z.infer<typeof reviewSchema>;\nexport type UserPreferencesFormData = z.infer<typeof userPreferencesSchema>;\nexport type CheckoutFormData = z.infer<typeof checkoutSchema>;\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AAAA;;AAGO,MAAM,mBAAmB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACrC;IACA;IACA;IACA;IACA;IACA;CACD;AACM,MAAM,oBAAoB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACtC;IACA;IACA;IACA;IACA;CACD;AACM,MAAM,qBAAqB,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;IACvC;IACA;IACA;IACA;CACD;AAGM,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG,uDACP,QAAQ;IACX,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI,gCAAgC,QAAQ;IAClE,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,GAAG,iDACP,QAAQ;IACX,YAAY,sNAAA,CAAA,IAAC,CACV,MAAM,GACN,KAAK,CAAC,WAAW,qCACjB,QAAQ;IACX,MAAM,sNAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG,gDACP,QAAQ;AACb;AAEO,MAAM,wBAAwB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,kBAAkB,QAAQ;IAClD,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC1B,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW;IACxC,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC5B;AAEO,MAAM,gBAAgB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,MAAM,sNAAA,CAAA,IAAC,CACJ,MAAM,GACN,GAAG,CAAC,GAAG;IACV,aAAa,sNAAA,CAAA,IAAC,CACX,MAAM,GACN,GAAG,CAAC,IAAI;IACX,WAAW,sNAAA,CAAA,IAAC,CACT,KAAK,CAAC;QAAC,sNAAA,CAAA,IAAC,CAAC,MAAM;QAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,SAAS,CAAC,CAAC,MAAQ,WAAW;KAAM,EAClE,MAAM,CAAC,CAAC,MAAQ,MAAM,GAAG;IAC5B,OAAO,sNAAA,CAAA,IAAC,CACL,MAAM,GACN,GAAG,CAAC,GAAG,yBACP,MAAM,CAAC,CAAC;QACP,kEAAkE;QAClE,OACE,IAAI,UAAU,CAAC,WACf,IAAI,UAAU,CAAC,QACf,IAAI,UAAU,CAAC,SACf,IAAI,QAAQ,CAAC,gBACb,IAAI,QAAQ,CAAC;IAEjB,GAAG;IACL,QAAQ,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IAC5B,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sNAAA,CAAA,IAAC,CAAC,GAAG,IAAI,QAAQ;IACnC,cAAc,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,sNAAA,CAAA,IAAC,CAAC,GAAG,IAAI,QAAQ;AACzC;AAEO,MAAM,uBAAuB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC3C,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,QAAQ;IACR,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,mBAAmB;IAC3C,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG;IAC/B,KAAK,sNAAA,CAAA,IAAC,CACH,MAAM,GACN,GAAG,CAAC,GAAG,8CACP,QAAQ;IACX,QAAQ,sNAAA,CAAA,IAAC,CACN,KAAK,CACJ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,CAAC;QACjB,kEAAkE;QAClE,OACE,IAAI,UAAU,CAAC,WACf,IAAI,UAAU,CAAC,QACf,IAAI,UAAU,CAAC,SACf,IAAI,QAAQ,CAAC,gBACb,IAAI,QAAQ,CAAC;IAEjB,GAAG,mCAEJ,OAAO,CAAC;QAAC;KAAuB;AACrC;AAGO,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,IAAI,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IACvB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM;IACd,QAAQ;IACR,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM;IACf,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC1B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ,CAAC;IACpC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;AACvB;AAEO,MAAM,kBAAkB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,GACH,QAAQ,GACR,GAAG,CAAC,IAAI;AACb;AAGO,MAAM,kBAAkB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;AACrC;AAEO,MAAM,oBAAoB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACxC,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC/B,OAAO,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,iBAAiB,GAAG,CAAC,GAAG;IACvC,iBAAiB;IACjB,aAAa,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ,GAAG,QAAQ;AAC7C;AAEO,MAAM,0BAA0B,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9C,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM;IACjB,QAAQ;AACV;AAGO,MAAM,sBAAsB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM;IACjB,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC3B,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,MAAM,CAAC,GAAG,OAAO,CAAC;AACzC;AAEO,MAAM,sBAAsB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC1C,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM;IACd,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;QACb,QAAQ,sNAAA,CAAA,IAAC,CAAC,GAAG;IACf;AACF;AAGO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;AACZ;AAEO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAC1B,MAAM,CAAC;IACN,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,UAAU,sNAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;IACV,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM;IACzB,WAAW,sNAAA,CAAA,IAAC,CACT,MAAM,GACN,GAAG,CAAC,GAAG;IACV,UAAU,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,aAAa,sNAAA,CAAA,IAAC,CACX,OAAO,GACP,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;AACnC,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAGK,MAAM,gBAAgB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACpC,MAAM,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI;AACb;AAEO,MAAM,mBAAmB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACvC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;AAC1B;AAGO,MAAM,eAAe,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACnC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM;IACnB,YAAY,sNAAA,CAAA,IAAC,CAAC,MAAM;IACpB,QAAQ,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG,CAAC;IACpC,OAAO,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,SAAS,sNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI;AACb;AAGO,MAAM,wBAAwB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC5C,oBAAoB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACxC,kBAAkB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACtC,iBAAiB,sNAAA,CAAA,IAAC,CAAC,OAAO,GAAG,OAAO,CAAC;IACrC,UAAU,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAM;KAAK,EAAE,OAAO,CAAC;IACvC,UAAU,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAO;KAAM,EAAE,OAAO,CAAC;AAC3C;AAGO,MAAM,iBAAiB,sNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IACrC,UAAU;IACV,iBAAiB;IACjB,eAAe,sNAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAQ;QAAU;QAAa;KAAa;IACnE,OAAO,sNAAA,CAAA,IAAC,CAAC,KAAK,CAAC,gBAAgB,GAAG,CAAC,GAAG;IACtC,WAAW,sNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;IAC9B,aAAa,sNAAA,CAAA,IAAC,CACX,OAAO,GACP,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;AACnC", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/products/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { productSchema } from \"@/lib/validations\";\nimport { ApiResponse, ProductsResponse } from \"@/types\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\n// GET /api/products - Récupérer tous les produits\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url);\n    const active = searchParams.get(\"active\");\n    const limit = searchParams.get(\"limit\");\n    const offset = searchParams.get(\"offset\");\n\n    const where = active === \"true\" ? { active: true } : {};\n    const take = limit ? parseInt(limit) : undefined;\n    const skip = offset ? parseInt(offset) : undefined;\n\n    const [products, total] = await Promise.all([\n      prisma.product.findMany({\n        where,\n        include: {\n          variants: {\n            orderBy: { flavor: \"asc\" },\n          },\n          priceTiers: {\n            orderBy: { quantity: \"asc\" },\n          },\n        },\n        orderBy: { createdAt: \"desc\" },\n        take,\n        skip,\n      }),\n      prisma.product.count({ where }),\n    ]);\n\n    const response: ApiResponse<ProductsResponse> = {\n      success: true,\n      data: {\n        products: products as any,\n        total,\n      },\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Error fetching products:\", error);\n    console.log(\"Utilisation des données de démonstration...\");\n\n    // En cas d'erreur de base de données, utiliser les données mockées\n    const mockData = {\n      success: true,\n      data: {\n        products: [\n          {\n            id: \"1\",\n            name: \"Bonbons Delta-9\",\n            description:\n              \"Nos délicieux bonbons Delta-9 aux saveurs naturelles. Parfait pour une expérience relaxante et savoureuse.\",\n            image: \"/img/product/packaging-group-deltagum.jpg\",\n            basePrice: 12.0,\n            dosage: \"10mg\",\n            active: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            priceTiers: [\n              {\n                id: \"1\",\n                productId: \"1\",\n                quantity: 1,\n                price: 12.0,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n              {\n                id: \"2\",\n                productId: \"1\",\n                quantity: 3,\n                price: 30.0,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n              {\n                id: \"3\",\n                productId: \"1\",\n                quantity: 5,\n                price: 45.0,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n            ],\n            variants: [\n              {\n                id: \"1\",\n                productId: \"1\",\n                flavor: \"fraise\",\n                color: \"#ff6b6b\",\n                stock: 50,\n                images: [\n                  \"/img/product/deltagum-fraise-main1.png\",\n                  \"/img/product/deltagum-fraise-main2.png\",\n                ],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n              {\n                id: \"2\",\n                productId: \"1\",\n                flavor: \"myrtille\",\n                color: \"#4ecdc4\",\n                stock: 45,\n                images: [\n                  \"/img/product/deltagum-myrtille-main1.png\",\n                  \"/img/product/deltagum-myrtille-main2.png\",\n                ],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n              {\n                id: \"3\",\n                productId: \"1\",\n                flavor: \"pomme\",\n                color: \"#95e1d3\",\n                stock: 40,\n                images: [\n                  \"/img/product/deltagum-apple-main1.png\",\n                  \"/img/product/deltagum-apple-main2.png\",\n                ],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n            ],\n          },\n          {\n            id: \"2\",\n            name: \"Cookies Delta-9\",\n            description:\n              \"Délicieux cookies Delta-9 pour une expérience gourmande unique. Parfait pour accompagner votre pause détente.\",\n            image: \"/img/product/packaging-group-cookie.png\",\n            basePrice: 15.0,\n            dosage: \"15mg\",\n            active: true,\n            createdAt: new Date().toISOString(),\n            updatedAt: new Date().toISOString(),\n            priceTiers: [\n              {\n                id: \"4\",\n                productId: \"2\",\n                quantity: 1,\n                price: 15.0,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n              {\n                id: \"5\",\n                productId: \"2\",\n                quantity: 3,\n                price: 40.0,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n            ],\n            variants: [\n              {\n                id: \"4\",\n                productId: \"2\",\n                flavor: \"chocolat\",\n                color: \"#8b4513\",\n                stock: 30,\n                images: [\"/img/product/cookie.png\"],\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n              },\n            ],\n          },\n        ],\n        total: 2,\n      },\n    };\n\n    return NextResponse.json(mockData);\n  }\n}\n\n// POST /api/products - Créer un nouveau produit\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json();\n\n    // Validation des données (Zod convertit automatiquement les strings en numbers)\n    const validatedData = productSchema.parse(body);\n\n    // Préparer les données pour Prisma (exclure les champs non-DB)\n    const { variants, pricingTiers, ...productData } = validatedData;\n\n    // Ajouter les champs requis\n    const productDataWithRequired = {\n      ...productData,\n      id: globalThis.crypto.randomUUID(),\n      updatedAt: new Date(),\n    };\n\n    // Créer le produit avec ses variantes et prix en transaction\n    const result = await prisma.$transaction(async (tx) => {\n      // Créer le produit\n      const product = await tx.product.create({\n        data: productDataWithRequired,\n      });\n\n      // Créer les variantes si elles existent\n      if (variants && Array.isArray(variants) && variants.length > 0) {\n        // Transformer les noms de saveurs français en enum\n        const flavorMapping: Record<string, string> = {\n          Myrtille: \"BLUEBERRY\",\n          Fraise: \"STRAWBERRY\",\n          Pomme: \"APPLE\",\n          Chocolat: \"CHOCOLATE\",\n          Vanille: \"VANILLA\",\n        };\n\n        for (const variantData of variants) {\n          // Transformer la saveur si nécessaire\n          if (variantData.flavor && flavorMapping[variantData.flavor]) {\n            variantData.flavor = flavorMapping[variantData.flavor];\n          }\n          // Générer un SKU unique si non fourni\n          if (!variantData.sku) {\n            const skuBase = `${product.name\n              .substring(0, 3)\n              .toUpperCase()}-${variantData.flavor.toUpperCase()}`;\n            variantData.sku = `${skuBase}-${Date.now()}`;\n          }\n\n          await tx.productVariant.create({\n            data: {\n              id: globalThis.crypto.randomUUID(),\n              ...variantData,\n              productId: product.id,\n              updatedAt: new Date(),\n            },\n          });\n        }\n      }\n\n      // Créer les prix par palier si ils existent\n      if (\n        pricingTiers &&\n        Array.isArray(pricingTiers) &&\n        pricingTiers.length > 0\n      ) {\n        for (const tierData of pricingTiers) {\n          await tx.priceTier.create({\n            data: {\n              id: globalThis.crypto.randomUUID(),\n              ...tierData,\n              productId: product.id,\n              updatedAt: new Date(),\n            },\n          });\n        }\n      }\n\n      // Retourner le produit complet\n      return await tx.product.findUnique({\n        where: { id: product.id },\n        include: {\n          variants: true,\n          priceTiers: true,\n        },\n      });\n    });\n\n    const response: ApiResponse = {\n      success: true,\n      data: result,\n      message: \"Produit créé avec succès\",\n    };\n\n    return NextResponse.json(response, { status: 201 });\n  } catch (error) {\n    console.error(\"Error creating product:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error:\n        error instanceof Error\n          ? error.message\n          : \"Erreur lors de la création du produit\",\n    };\n\n    return NextResponse.json(response, { status: 400 });\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAEA;;;;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,SAAS,aAAa,GAAG,CAAC;QAChC,MAAM,QAAQ,aAAa,GAAG,CAAC;QAC/B,MAAM,SAAS,aAAa,GAAG,CAAC;QAEhC,MAAM,QAAQ,WAAW,SAAS;YAAE,QAAQ;QAAK,IAAI,CAAC;QACtD,MAAM,OAAO,QAAQ,SAAS,SAAS;QACvC,MAAM,OAAO,SAAS,SAAS,UAAU;QAEzC,MAAM,CAAC,UAAU,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC1C,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,QAAQ,CAAC;gBACtB;gBACA,SAAS;oBACP,UAAU;wBACR,SAAS;4BAAE,QAAQ;wBAAM;oBAC3B;oBACA,YAAY;wBACV,SAAS;4BAAE,UAAU;wBAAM;oBAC7B;gBACF;gBACA,SAAS;oBAAE,WAAW;gBAAO;gBAC7B;gBACA;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC9B;QAED,MAAM,WAA0C;YAC9C,SAAS;YACT,MAAM;gBACJ,UAAU;gBACV;YACF;QACF;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,QAAQ,GAAG,CAAC;QAEZ,mEAAmE;QACnE,MAAM,WAAW;YACf,SAAS;YACT,MAAM;gBACJ,UAAU;oBACR;wBACE,IAAI;wBACJ,MAAM;wBACN,aACE;wBACF,OAAO;wBACP,WAAW;wBACX,QAAQ;wBACR,QAAQ;wBACR,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;wBACjC,YAAY;4BACV;gCACE,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;wBACD,UAAU;4BACR;gCACE,IAAI;gCACJ,WAAW;gCACX,QAAQ;gCACR,OAAO;gCACP,OAAO;gCACP,QAAQ;oCACN;oCACA;iCACD;gCACD,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,WAAW;gCACX,QAAQ;gCACR,OAAO;gCACP,OAAO;gCACP,QAAQ;oCACN;oCACA;iCACD;gCACD,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,WAAW;gCACX,QAAQ;gCACR,OAAO;gCACP,OAAO;gCACP,QAAQ;oCACN;oCACA;iCACD;gCACD,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;oBACH;oBACA;wBACE,IAAI;wBACJ,MAAM;wBACN,aACE;wBACF,OAAO;wBACP,WAAW;wBACX,QAAQ;wBACR,QAAQ;wBACR,WAAW,IAAI,OAAO,WAAW;wBACjC,WAAW,IAAI,OAAO,WAAW;wBACjC,YAAY;4BACV;gCACE,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;4BACA;gCACE,IAAI;gCACJ,WAAW;gCACX,UAAU;gCACV,OAAO;gCACP,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;wBACD,UAAU;4BACR;gCACE,IAAI;gCACJ,WAAW;gCACX,QAAQ;gCACR,OAAO;gCACP,OAAO;gCACP,QAAQ;oCAAC;iCAA0B;gCACnC,WAAW,IAAI,OAAO,WAAW;gCACjC,WAAW,IAAI,OAAO,WAAW;4BACnC;yBACD;oBACH;iBACD;gBACD,OAAO;YACT;QACF;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B;AACF;AAGO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAE/B,gFAAgF;QAChF,MAAM,gBAAgB,2HAAA,CAAA,gBAAa,CAAC,KAAK,CAAC;QAE1C,+DAA+D;QAC/D,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,aAAa,GAAG;QAEnD,4BAA4B;QAC5B,MAAM,0BAA0B;YAC9B,GAAG,WAAW;YACd,IAAI,WAAW,MAAM,CAAC,UAAU;YAChC,WAAW,IAAI;QACjB;QAEA,6DAA6D;QAC7D,MAAM,SAAS,MAAM,sHAAA,CAAA,SAAM,CAAC,YAAY,CAAC,OAAO;YAC9C,mBAAmB;YACnB,MAAM,UAAU,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC;gBACtC,MAAM;YACR;YAEA,wCAAwC;YACxC,IAAI,YAAY,MAAM,OAAO,CAAC,aAAa,SAAS,MAAM,GAAG,GAAG;gBAC9D,mDAAmD;gBACnD,MAAM,gBAAwC;oBAC5C,UAAU;oBACV,QAAQ;oBACR,OAAO;oBACP,UAAU;oBACV,SAAS;gBACX;gBAEA,KAAK,MAAM,eAAe,SAAU;oBAClC,sCAAsC;oBACtC,IAAI,YAAY,MAAM,IAAI,aAAa,CAAC,YAAY,MAAM,CAAC,EAAE;wBAC3D,YAAY,MAAM,GAAG,aAAa,CAAC,YAAY,MAAM,CAAC;oBACxD;oBACA,sCAAsC;oBACtC,IAAI,CAAC,YAAY,GAAG,EAAE;wBACpB,MAAM,UAAU,GAAG,QAAQ,IAAI,CAC5B,SAAS,CAAC,GAAG,GACb,WAAW,GAAG,CAAC,EAAE,YAAY,MAAM,CAAC,WAAW,IAAI;wBACtD,YAAY,GAAG,GAAG,GAAG,QAAQ,CAAC,EAAE,KAAK,GAAG,IAAI;oBAC9C;oBAEA,MAAM,GAAG,cAAc,CAAC,MAAM,CAAC;wBAC7B,MAAM;4BACJ,IAAI,WAAW,MAAM,CAAC,UAAU;4BAChC,GAAG,WAAW;4BACd,WAAW,QAAQ,EAAE;4BACrB,WAAW,IAAI;wBACjB;oBACF;gBACF;YACF;YAEA,4CAA4C;YAC5C,IACE,gBACA,MAAM,OAAO,CAAC,iBACd,aAAa,MAAM,GAAG,GACtB;gBACA,KAAK,MAAM,YAAY,aAAc;oBACnC,MAAM,GAAG,SAAS,CAAC,MAAM,CAAC;wBACxB,MAAM;4BACJ,IAAI,WAAW,MAAM,CAAC,UAAU;4BAChC,GAAG,QAAQ;4BACX,WAAW,QAAQ,EAAE;4BACrB,WAAW,IAAI;wBACjB;oBACF;gBACF;YACF;YAEA,+BAA+B;YAC/B,OAAO,MAAM,GAAG,OAAO,CAAC,UAAU,CAAC;gBACjC,OAAO;oBAAE,IAAI,QAAQ,EAAE;gBAAC;gBACxB,SAAS;oBACP,UAAU;oBACV,YAAY;gBACd;YACF;QACF;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;YACN,SAAS;QACX;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QAEzC,MAAM,WAAwB;YAC5B,SAAS;YACT,OACE,iBAAiB,QACb,MAAM,OAAO,GACb;QACR;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}