{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 68, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/checkout/verify-payment/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\nimport { prisma } from \"@/lib/prisma\";\nimport Stripe from \"stripe\";\n\nconst stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {\n  apiVersion: \"2024-12-18.acacia\",\n});\n\ninterface ApiResponse {\n  success: boolean;\n  data?: any;\n  error?: string;\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const { sessionId, orderId } = await request.json();\n\n    if (!sessionId || !orderId) {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Session ID et Order ID requis\",\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n\n    // Récupérer la session Stripe\n    const session = await stripe.checkout.sessions.retrieve(sessionId);\n\n    if (!session) {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Session Stripe introuvable\",\n      };\n      return NextResponse.json(response, { status: 404 });\n    }\n\n    // Vérifier le statut du paiement\n    if (session.payment_status === \"paid\") {\n      // Mettre à jour la commande\n      const updatedOrder = await prisma.order.update({\n        where: { id: orderId },\n        data: {\n          status: \"PAID\",\n          stripeSessionId: sessionId,\n          stripePaymentIntentId: session.payment_intent as string,\n          updatedAt: new Date(),\n        },\n        include: {\n          items: {\n            include: {\n              product: true,\n              variant: true,\n            },\n          },\n          customer: true,\n        },\n      });\n\n      const response: ApiResponse = {\n        success: true,\n        data: {\n          order: updatedOrder,\n          paymentStatus: session.payment_status,\n        },\n      };\n      return NextResponse.json(response);\n    } else {\n      const response: ApiResponse = {\n        success: false,\n        error: `Paiement non confirmé. Statut: ${session.payment_status}`,\n      };\n      return NextResponse.json(response, { status: 400 });\n    }\n  } catch (error) {\n    console.error(\"Erreur vérification paiement:\", error);\n    const response: ApiResponse = {\n      success: false,\n      error: \"Erreur lors de la vérification du paiement\",\n    };\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEA,MAAM,SAAS,IAAI,+OAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAG;IACxD,YAAY;AACd;AAQO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,MAAM,QAAQ,IAAI;QAEjD,IAAI,CAAC,aAAa,CAAC,SAAS;YAC1B,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,8BAA8B;QAC9B,MAAM,UAAU,MAAM,OAAO,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC;QAExD,IAAI,CAAC,SAAS;YACZ,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,iCAAiC;QACjC,IAAI,QAAQ,cAAc,KAAK,QAAQ;YACrC,4BAA4B;YAC5B,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,MAAM,CAAC;gBAC7C,OAAO;oBAAE,IAAI;gBAAQ;gBACrB,MAAM;oBACJ,QAAQ;oBACR,iBAAiB;oBACjB,uBAAuB,QAAQ,cAAc;oBAC7C,WAAW,IAAI;gBACjB;gBACA,SAAS;oBACP,OAAO;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;oBACF;oBACA,UAAU;gBACZ;YACF;YAEA,MAAM,WAAwB;gBAC5B,SAAS;gBACT,MAAM;oBACJ,OAAO;oBACP,eAAe,QAAQ,cAAc;gBACvC;YACF;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;QAC3B,OAAO;YACL,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO,CAAC,+BAA+B,EAAE,QAAQ,cAAc,EAAE;YACnE;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO;QACT;QACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}