{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/user/profile/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { NextRequest, NextResponse } from \"next/server\";\nimport jwt from \"jsonwebtoken\";\n\n// Fonction pour récupérer l'utilisateur depuis le token\nfunction getUserFromToken(request: NextRequest) {\n  const token = request.cookies.get(\"auth-token\")?.value;\n  if (!token) return null;\n\n  try {\n    const decoded = jwt.verify(token, process.env.JWT_SECRET || \"fallback-secret\") as any;\n    return decoded;\n  } catch {\n    return null;\n  }\n}\n\n// GET - Récupérer le profil utilisateur\nexport async function GET(request: NextRequest) {\n  try {\n    const user = getUserFromToken(request);\n    if (!user) {\n      return NextResponse.json(\n        { success: false, error: \"Non authentifié\" },\n        { status: 401 }\n      );\n    }\n\n    const userProfile = await prisma.customer.findUnique({\n      where: { id: user.userId },\n      select: {\n        id: true,\n        firstName: true,\n        lastName: true,\n        email: true,\n        phone: true,\n        address: true,\n        postalCode: true,\n        city: true,\n        createdAt: true,\n        role: true,\n      },\n    });\n\n    if (!userProfile) {\n      return NextResponse.json(\n        { success: false, error: \"Utilisateur non trouvé\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json({\n      success: true,\n      data: { user: userProfile },\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de la récupération du profil:\", error);\n    return NextResponse.json(\n      { success: false, error: \"Erreur serveur\" },\n      { status: 500 }\n    );\n  }\n}\n\n// PUT - Mettre à jour le profil utilisateur\nexport async function PUT(request: NextRequest) {\n  try {\n    const user = getUserFromToken(request);\n    if (!user) {\n      return NextResponse.json(\n        { success: false, error: \"Non authentifié\" },\n        { status: 401 }\n      );\n    }\n\n    const body = await request.json();\n    const { firstName, lastName, email, phone, address, postalCode, city } = body;\n\n    // Validation basique\n    if (!firstName || !lastName || !email) {\n      return NextResponse.json(\n        { success: false, error: \"Prénom, nom et email sont requis\" },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier si l'email est déjà utilisé par un autre utilisateur\n    if (email !== user.email) {\n      const existingUser = await prisma.customer.findFirst({\n        where: {\n          email: email,\n          id: { not: user.userId },\n        },\n      });\n\n      if (existingUser) {\n        return NextResponse.json(\n          { success: false, error: \"Cet email est déjà utilisé\" },\n          { status: 400 }\n        );\n      }\n    }\n\n    // Mettre à jour le profil\n    const updatedUser = await prisma.customer.update({\n      where: { id: user.userId },\n      data: {\n        firstName,\n        lastName,\n        email,\n        phone: phone || null,\n        address: address || null,\n        postalCode: postalCode || null,\n        city: city || null,\n      },\n      select: {\n        id: true,\n        firstName: true,\n        lastName: true,\n        email: true,\n        phone: true,\n        address: true,\n        postalCode: true,\n        city: true,\n        createdAt: true,\n        role: true,\n      },\n    });\n\n    return NextResponse.json({\n      success: true,\n      message: \"Profil mis à jour avec succès\",\n      data: { user: updatedUser },\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de la mise à jour du profil:\", error);\n    return NextResponse.json(\n      { success: false, error: \"Erreur serveur\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AAEA,wDAAwD;AACxD,SAAS,iBAAiB,OAAoB;IAC5C,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;IACjD,IAAI,CAAC,OAAO,OAAO;IAEnB,IAAI;QACF,MAAM,UAAU,gMAAA,CAAA,UAAG,CAAC,MAAM,CAAC,OAAO,QAAQ,GAAG,CAAC,UAAU,IAAI;QAC5D,OAAO;IACT,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,iBAAiB;QAC9B,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkB,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YACnD,OAAO;gBAAE,IAAI,KAAK,MAAM;YAAC;YACzB,QAAQ;gBACN,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,MAAM;gBACN,WAAW;gBACX,MAAM;YACR;QACF;QAEA,IAAI,CAAC,aAAa;YAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAyB,GAClD;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;gBAAE,MAAM;YAAY;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,6CAA6C;QAC3D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAiB,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,OAAO,iBAAiB;QAC9B,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAkB,GAC3C;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,OAAO,EAAE,UAAU,EAAE,IAAI,EAAE,GAAG;QAEzE,qBAAqB;QACrB,IAAI,CAAC,aAAa,CAAC,YAAY,CAAC,OAAO;YACrC,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,SAAS;gBAAO,OAAO;YAAmC,GAC5D;gBAAE,QAAQ;YAAI;QAElB;QAEA,gEAAgE;QAChE,IAAI,UAAU,KAAK,KAAK,EAAE;YACxB,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,SAAS,CAAC;gBACnD,OAAO;oBACL,OAAO;oBACP,IAAI;wBAAE,KAAK,KAAK,MAAM;oBAAC;gBACzB;YACF;YAEA,IAAI,cAAc;gBAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,SAAS;oBAAO,OAAO;gBAA6B,GACtD;oBAAE,QAAQ;gBAAI;YAElB;QACF;QAEA,0BAA0B;QAC1B,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC/C,OAAO;gBAAE,IAAI,KAAK,MAAM;YAAC;YACzB,MAAM;gBACJ;gBACA;gBACA;gBACA,OAAO,SAAS;gBAChB,SAAS,WAAW;gBACpB,YAAY,cAAc;gBAC1B,MAAM,QAAQ;YAChB;YACA,QAAQ;gBACN,IAAI;gBACJ,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,MAAM;gBACN,WAAW;gBACX,MAAM;YACR;QACF;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,SAAS;YACT,MAAM;gBAAE,MAAM;YAAY;QAC5B;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4CAA4C;QAC1D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,SAAS;YAAO,OAAO;QAAiB,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}