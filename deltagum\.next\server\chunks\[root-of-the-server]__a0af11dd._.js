module.exports = {

"[project]/.next-internal/server/app/api/admin/stats/route/actions.js [app-rsc] (server actions loader, ecmascript)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
}}),
"[externals]/next/dist/compiled/next-server/app-route-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-route-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-route-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/@opentelemetry/api [external] (next/dist/compiled/@opentelemetry/api, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/@opentelemetry/api", () => require("next/dist/compiled/@opentelemetry/api"));

module.exports = mod;
}}),
"[externals]/next/dist/compiled/next-server/app-page-turbo.runtime.dev.js [external] (next/dist/compiled/next-server/app-page-turbo.runtime.dev.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js", () => require("next/dist/compiled/next-server/app-page-turbo.runtime.dev.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-unit-async-storage.external.js [external] (next/dist/server/app-render/work-unit-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-unit-async-storage.external.js", () => require("next/dist/server/app-render/work-unit-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/next/dist/server/app-render/work-async-storage.external.js [external] (next/dist/server/app-render/work-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/work-async-storage.external.js", () => require("next/dist/server/app-render/work-async-storage.external.js"));

module.exports = mod;
}}),
"[externals]/@prisma/client [external] (@prisma/client, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("@prisma/client", () => require("@prisma/client"));

module.exports = mod;
}}),
"[project]/src/lib/prisma.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "prisma": (()=>prisma)
});
var __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__ = __turbopack_context__.i("[externals]/@prisma/client [external] (@prisma/client, cjs)");
;
const globalForPrisma = globalThis;
// Créer le client Prisma avec gestion d'erreur
let prismaInstance;
try {
    prismaInstance = new __TURBOPACK__imported__module__$5b$externals$5d2f40$prisma$2f$client__$5b$external$5d$__$2840$prisma$2f$client$2c$__cjs$29$__["PrismaClient"]({
        log: ("TURBOPACK compile-time truthy", 1) ? [
            "query",
            "error",
            "warn"
        ] : ("TURBOPACK unreachable", undefined)
    });
    console.log("✅ Prisma client créé avec succès");
} catch (error) {
    console.error("❌ Erreur création Prisma client:", error);
    throw error;
}
const prisma = prismaInstance;
if ("TURBOPACK compile-time truthy", 1) {
    globalForPrisma.prisma = prisma;
    globalThis.__prisma = prisma;
}
}}),
"[externals]/next/dist/server/app-render/after-task-async-storage.external.js [external] (next/dist/server/app-render/after-task-async-storage.external.js, cjs)": (function(__turbopack_context__) {

var { g: global, __dirname, m: module, e: exports } = __turbopack_context__;
{
const mod = __turbopack_context__.x("next/dist/server/app-render/after-task-async-storage.external.js", () => require("next/dist/server/app-render/after-task-async-storage.external.js"));

module.exports = mod;
}}),
"[project]/src/app/api/admin/stats/route.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "GET": (()=>GET)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/prisma.ts [app-route] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_$40$babel$2b$core$40$7$2e$2_185ca0f072c7c00081c01751178945af$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/server.js [app-route] (ecmascript)");
;
;
async function GET() {
    try {
        // Récupérer les statistiques en parallèle
        const [productsCount, ordersCount, customersCount, totalRevenue, recentOrders, topProducts, monthlyStats] = await Promise.all([
            // Nombre total de produits actifs
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.count({
                where: {
                    active: true
                }
            }),
            // Nombre total de commandes
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.count(),
            // Nombre total de clients
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].customer.count({
                where: {
                    role: "USER"
                }
            }),
            // Revenus totaux (somme de tous les montants des commandes)
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.aggregate({
                _sum: {
                    totalAmount: true
                },
                where: {
                    status: "DELIVERED"
                }
            }),
            // 5 dernières commandes
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.findMany({
                take: 5,
                orderBy: {
                    createdAt: "desc"
                },
                include: {
                    customer: {
                        select: {
                            firstName: true,
                            lastName: true,
                            email: true
                        }
                    },
                    items: {
                        include: {
                            product: {
                                select: {
                                    name: true
                                }
                            },
                            variant: {
                                select: {
                                    flavor: true
                                }
                            }
                        }
                    }
                }
            }),
            // Produits les plus vendus
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].orderItem.groupBy({
                by: [
                    "productId"
                ],
                _sum: {
                    quantity: true
                },
                orderBy: {
                    _sum: {
                        quantity: "desc"
                    }
                },
                take: 5
            }),
            // Statistiques des 12 derniers mois
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].$queryRaw`
        SELECT 
          DATE_TRUNC('month', "createdAt") as month,
          COUNT(*) as orders_count,
          SUM(total) as revenue
        FROM orders 
        WHERE "createdAt" >= NOW() - INTERVAL '12 months'
        GROUP BY DATE_TRUNC('month', "createdAt")
        ORDER BY month DESC
      `
        ]);
        // Récupérer les détails des produits les plus vendus
        const topProductsWithDetails = await Promise.all(topProducts.map(async (item)=>{
            const product = await __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].product.findUnique({
                where: {
                    id: item.productId
                },
                select: {
                    name: true,
                    image: true
                }
            });
            return {
                ...product,
                totalSold: item._sum.quantity || 0
            };
        }));
        // Calculer les statistiques de croissance (comparaison avec le mois précédent)
        const currentMonth = new Date();
        const lastMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1);
        const currentMonthStart = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1);
        const [currentMonthOrders, lastMonthOrders] = await Promise.all([
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.count({
                where: {
                    createdAt: {
                        gte: currentMonthStart
                    }
                }
            }),
            __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$prisma$2e$ts__$5b$app$2d$route$5d$__$28$ecmascript$29$__["prisma"].order.count({
                where: {
                    createdAt: {
                        gte: lastMonth,
                        lt: currentMonthStart
                    }
                }
            })
        ]);
        const ordersGrowth = lastMonthOrders > 0 ? (currentMonthOrders - lastMonthOrders) / lastMonthOrders * 100 : 0;
        const stats = {
            overview: {
                products: productsCount,
                orders: ordersCount,
                customers: customersCount,
                revenue: totalRevenue._sum?.totalAmount || 0,
                ordersGrowth: Math.round(ordersGrowth * 100) / 100
            },
            recentOrders: recentOrders.map((order)=>({
                    id: order.id,
                    customer: `${order.customer.firstName} ${order.customer.lastName}`,
                    email: order.customer.email,
                    total: order.totalAmount,
                    status: order.status,
                    createdAt: order.createdAt,
                    itemsCount: order.items.length,
                    items: order.items.map((item)=>({
                            product: item.product.name,
                            variant: item.variant?.flavor,
                            quantity: item.quantity
                        }))
                })),
            topProducts: topProductsWithDetails,
            monthlyStats: monthlyStats
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_$40$babel$2b$core$40$7$2e$2_185ca0f072c7c00081c01751178945af$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error("Erreur lors de la récupération des statistiques:", error);
        // Retourner des données de fallback en cas d'erreur
        const fallbackStats = {
            overview: {
                products: 2,
                orders: 0,
                customers: 0,
                revenue: 0,
                ordersGrowth: 0
            },
            recentOrders: [],
            topProducts: [],
            monthlyStats: []
        };
        return __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$next$40$15$2e$3$2e$5_$40$babel$2b$core$40$7$2e$2_185ca0f072c7c00081c01751178945af$2f$node_modules$2f$next$2f$server$2e$js__$5b$app$2d$route$5d$__$28$ecmascript$29$__["NextResponse"].json({
            success: true,
            data: fallbackStats,
            fallback: true
        });
    }
}
}}),

};

//# sourceMappingURL=%5Broot-of-the-server%5D__a0af11dd._.js.map