{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  var __prisma: PrismaClient | undefined;\n}\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\n// Créer le client Prisma avec gestion d'erreur\nlet prismaInstance: PrismaClient;\n\ntry {\n  prismaInstance = new PrismaClient({\n    log:\n      process.env.NODE_ENV === \"development\"\n        ? [\"query\", \"error\", \"warn\"]\n        : [\"error\"],\n  });\n  console.log(\"✅ Prisma client créé avec succès\");\n} catch (error) {\n  console.error(\"❌ Erreur création Prisma client:\", error);\n  throw error;\n}\n\nexport const prisma = prismaInstance;\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalForPrisma.prisma = prisma;\n  globalThis.__prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMA,MAAM,kBAAkB;AAIxB,+CAA+C;AAC/C,IAAI;AAEJ,IAAI;IACF,iBAAiB,IAAI,6HAAA,CAAA,eAAY,CAAC;QAChC,KACE,uCACI;YAAC;YAAS;YAAS;SAAO;IAElC;IACA,QAAQ,GAAG,CAAC;AACd,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,MAAM;AACR;AAEO,MAAM,SAAS;AAEtB,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;IACzB,WAAW,QAAQ,GAAG;AACxB", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/admin/stats/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { NextResponse } from \"next/server\";\n\nexport async function GET() {\n  try {\n    // Récupérer les statistiques en parallèle\n    const [\n      productsCount,\n      ordersCount,\n      customersCount,\n      totalRevenue,\n      recentOrders,\n      topProducts,\n      monthlyStats,\n    ] = await Promise.all([\n      // Nombre total de produits actifs\n      prisma.product.count({\n        where: { active: true },\n      }),\n\n      // Nombre total de commandes\n      prisma.order.count(),\n\n      // Nombre total de clients\n      prisma.customer.count({\n        where: { role: \"USER\" },\n      }),\n\n      // Revenus totaux (somme de tous les montants des commandes)\n      prisma.order.aggregate({\n        _sum: { totalAmount: true },\n        where: { status: \"DELIVERED\" },\n      }),\n\n      // 5 dernières commandes\n      prisma.order.findMany({\n        take: 5,\n        orderBy: { createdAt: \"desc\" },\n        include: {\n          customer: {\n            select: { firstName: true, lastName: true, email: true },\n          },\n          items: {\n            include: {\n              product: { select: { name: true } },\n              variant: { select: { flavor: true } },\n            },\n          },\n        },\n      }),\n\n      // Produits les plus vendus\n      prisma.orderItem.groupBy({\n        by: [\"productId\"],\n        _sum: { quantity: true },\n        orderBy: { _sum: { quantity: \"desc\" } },\n        take: 5,\n      }),\n\n      // Statistiques des 12 derniers mois\n      prisma.$queryRaw`\n        SELECT \n          DATE_TRUNC('month', \"createdAt\") as month,\n          COUNT(*) as orders_count,\n          SUM(total) as revenue\n        FROM orders \n        WHERE \"createdAt\" >= NOW() - INTERVAL '12 months'\n        GROUP BY DATE_TRUNC('month', \"createdAt\")\n        ORDER BY month DESC\n      `,\n    ]);\n\n    // Récupérer les détails des produits les plus vendus\n    const topProductsWithDetails = await Promise.all(\n      topProducts.map(async (item) => {\n        const product = await prisma.product.findUnique({\n          where: { id: item.productId },\n          select: { name: true, image: true },\n        });\n        return {\n          ...product,\n          totalSold: item._sum.quantity || 0,\n        };\n      })\n    );\n\n    // Calculer les statistiques de croissance (comparaison avec le mois précédent)\n    const currentMonth = new Date();\n    const lastMonth = new Date(\n      currentMonth.getFullYear(),\n      currentMonth.getMonth() - 1,\n      1\n    );\n    const currentMonthStart = new Date(\n      currentMonth.getFullYear(),\n      currentMonth.getMonth(),\n      1\n    );\n\n    const [currentMonthOrders, lastMonthOrders] = await Promise.all([\n      prisma.order.count({\n        where: {\n          createdAt: { gte: currentMonthStart },\n        },\n      }),\n      prisma.order.count({\n        where: {\n          createdAt: {\n            gte: lastMonth,\n            lt: currentMonthStart,\n          },\n        },\n      }),\n    ]);\n\n    const ordersGrowth =\n      lastMonthOrders > 0\n        ? ((currentMonthOrders - lastMonthOrders) / lastMonthOrders) * 100\n        : 0;\n\n    const stats = {\n      overview: {\n        products: productsCount,\n        orders: ordersCount,\n        customers: customersCount,\n        revenue: totalRevenue._sum?.totalAmount || 0,\n        ordersGrowth: Math.round(ordersGrowth * 100) / 100,\n      },\n      recentOrders: recentOrders.map((order) => ({\n        id: order.id,\n        customer: `${order.customer.firstName} ${order.customer.lastName}`,\n        email: order.customer.email,\n        total: order.totalAmount,\n        status: order.status,\n        createdAt: order.createdAt,\n        itemsCount: order.items.length,\n        items: order.items.map((item) => ({\n          product: item.product.name,\n          variant: item.variant?.flavor,\n          quantity: item.quantity,\n        })),\n      })),\n      topProducts: topProductsWithDetails,\n      monthlyStats: monthlyStats,\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: stats,\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de la récupération des statistiques:\", error);\n\n    // Retourner des données de fallback en cas d'erreur\n    const fallbackStats = {\n      overview: {\n        products: 2,\n        orders: 0,\n        customers: 0,\n        revenue: 0,\n        ordersGrowth: 0,\n      },\n      recentOrders: [],\n      topProducts: [],\n      monthlyStats: [],\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: fallbackStats,\n      fallback: true,\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,0CAA0C;QAC1C,MAAM,CACJ,eACA,aACA,gBACA,cACA,cACA,aACA,aACD,GAAG,MAAM,QAAQ,GAAG,CAAC;YACpB,kCAAkC;YAClC,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;gBACnB,OAAO;oBAAE,QAAQ;gBAAK;YACxB;YAEA,4BAA4B;YAC5B,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;YAElB,0BAA0B;YAC1B,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBACpB,OAAO;oBAAE,MAAM;gBAAO;YACxB;YAEA,4DAA4D;YAC5D,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;gBACrB,MAAM;oBAAE,aAAa;gBAAK;gBAC1B,OAAO;oBAAE,QAAQ;gBAAY;YAC/B;YAEA,wBAAwB;YACxB,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,QAAQ,CAAC;gBACpB,MAAM;gBACN,SAAS;oBAAE,WAAW;gBAAO;gBAC7B,SAAS;oBACP,UAAU;wBACR,QAAQ;4BAAE,WAAW;4BAAM,UAAU;4BAAM,OAAO;wBAAK;oBACzD;oBACA,OAAO;wBACL,SAAS;4BACP,SAAS;gCAAE,QAAQ;oCAAE,MAAM;gCAAK;4BAAE;4BAClC,SAAS;gCAAE,QAAQ;oCAAE,QAAQ;gCAAK;4BAAE;wBACtC;oBACF;gBACF;YACF;YAEA,2BAA2B;YAC3B,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC,OAAO,CAAC;gBACvB,IAAI;oBAAC;iBAAY;gBACjB,MAAM;oBAAE,UAAU;gBAAK;gBACvB,SAAS;oBAAE,MAAM;wBAAE,UAAU;oBAAO;gBAAE;gBACtC,MAAM;YACR;YAEA,oCAAoC;YACpC,sHAAA,CAAA,SAAM,CAAC,SAAS,CAAC;;;;;;;;;MASjB,CAAC;SACF;QAED,qDAAqD;QACrD,MAAM,yBAAyB,MAAM,QAAQ,GAAG,CAC9C,YAAY,GAAG,CAAC,OAAO;YACrB,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,UAAU,CAAC;gBAC9C,OAAO;oBAAE,IAAI,KAAK,SAAS;gBAAC;gBAC5B,QAAQ;oBAAE,MAAM;oBAAM,OAAO;gBAAK;YACpC;YACA,OAAO;gBACL,GAAG,OAAO;gBACV,WAAW,KAAK,IAAI,CAAC,QAAQ,IAAI;YACnC;QACF;QAGF,+EAA+E;QAC/E,MAAM,eAAe,IAAI;QACzB,MAAM,YAAY,IAAI,KACpB,aAAa,WAAW,IACxB,aAAa,QAAQ,KAAK,GAC1B;QAEF,MAAM,oBAAoB,IAAI,KAC5B,aAAa,WAAW,IACxB,aAAa,QAAQ,IACrB;QAGF,MAAM,CAAC,oBAAoB,gBAAgB,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC9D,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACjB,OAAO;oBACL,WAAW;wBAAE,KAAK;oBAAkB;gBACtC;YACF;YACA,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK,CAAC;gBACjB,OAAO;oBACL,WAAW;wBACT,KAAK;wBACL,IAAI;oBACN;gBACF;YACF;SACD;QAED,MAAM,eACJ,kBAAkB,IACd,AAAC,CAAC,qBAAqB,eAAe,IAAI,kBAAmB,MAC7D;QAEN,MAAM,QAAQ;YACZ,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS,aAAa,IAAI,EAAE,eAAe;gBAC3C,cAAc,KAAK,KAAK,CAAC,eAAe,OAAO;YACjD;YACA,cAAc,aAAa,GAAG,CAAC,CAAC,QAAU,CAAC;oBACzC,IAAI,MAAM,EAAE;oBACZ,UAAU,GAAG,MAAM,QAAQ,CAAC,SAAS,CAAC,CAAC,EAAE,MAAM,QAAQ,CAAC,QAAQ,EAAE;oBAClE,OAAO,MAAM,QAAQ,CAAC,KAAK;oBAC3B,OAAO,MAAM,WAAW;oBACxB,QAAQ,MAAM,MAAM;oBACpB,WAAW,MAAM,SAAS;oBAC1B,YAAY,MAAM,KAAK,CAAC,MAAM;oBAC9B,OAAO,MAAM,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;4BAChC,SAAS,KAAK,OAAO,CAAC,IAAI;4BAC1B,SAAS,KAAK,OAAO,EAAE;4BACvB,UAAU,KAAK,QAAQ;wBACzB,CAAC;gBACH,CAAC;YACD,aAAa;YACb,cAAc;QAChB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAElE,oDAAoD;QACpD,MAAM,gBAAgB;YACpB,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;YACA,cAAc,EAAE;YAChB,aAAa,EAAE;YACf,cAAc,EAAE;QAClB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU;QACZ;IACF;AACF", "debugId": null}}]}