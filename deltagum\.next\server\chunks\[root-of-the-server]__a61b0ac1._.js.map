{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  var __prisma: PrismaClient | undefined;\n}\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\n// Créer le client Prisma avec gestion d'erreur\nlet prismaInstance: PrismaClient;\n\ntry {\n  prismaInstance = new PrismaClient({\n    log:\n      process.env.NODE_ENV === \"development\"\n        ? [\"query\", \"error\", \"warn\"]\n        : [\"error\"],\n  });\n  console.log(\"✅ Prisma client créé avec succès\");\n} catch (error) {\n  console.error(\"❌ Erreur création Prisma client:\", error);\n  throw error;\n}\n\nexport const prisma = prismaInstance;\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalForPrisma.prisma = prisma;\n  globalThis.__prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMA,MAAM,kBAAkB;AAIxB,+CAA+C;AAC/C,IAAI;AAEJ,IAAI;IACF,iBAAiB,IAAI,6HAAA,CAAA,eAAY,CAAC;QAChC,KACE,uCACI;YAAC;YAAS;YAAS;SAAO;IAElC;IACA,QAAQ,GAAG,CAAC;AACd,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,MAAM;AACR;AAEO,MAAM,SAAS;AAEtB,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;IACzB,WAAW,QAAQ,GAAG;AACxB", "debugId": null}}, {"offset": {"line": 100, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/admin/stats-simple/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { NextResponse } from \"next/server\";\n\nexport async function GET() {\n  try {\n    console.log(\"📊 Récupération des statistiques simplifiées...\");\n\n    // Récupérer les statistiques de base\n    const productsCount = await prisma.product.count({\n      where: { active: true },\n    });\n\n    const ordersCount = await prisma.order.count();\n\n    const customersCount = await prisma.customer.count({\n      where: { role: \"USER\" },\n    });\n\n    // Revenus totaux (commandes livrées uniquement)\n    const totalRevenueResult = await prisma.order.aggregate({\n      _sum: { totalAmount: true },\n      where: {\n        status: {\n          in: [\"PAID\", \"SHIPPED\", \"DELIVERED\"],\n        },\n      },\n    });\n\n    const totalRevenue = Number(totalRevenueResult._sum.totalAmount || 0);\n\n    const stats = {\n      overview: {\n        products: productsCount,\n        orders: ordersCount,\n        customers: customersCount,\n        revenue: totalRevenue,\n        ordersGrowth: 0,\n      },\n      recentOrders: [],\n      topProducts: [],\n      monthlyStats: [],\n    };\n\n    console.log(\"✅ Statistiques simplifiées:\", stats.overview);\n\n    return NextResponse.json({\n      success: true,\n      data: stats,\n    });\n  } catch (error) {\n    console.error(\"❌ Erreur lors de la récupération des statistiques:\", error);\n\n    // Retourner des données de fallback en cas d'erreur\n    const fallbackStats = {\n      overview: {\n        products: 0,\n        orders: 0,\n        customers: 0,\n        revenue: 0,\n        ordersGrowth: 0,\n      },\n      recentOrders: [],\n      topProducts: [],\n      monthlyStats: [],\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: fallbackStats,\n      fallback: true,\n      error: error instanceof Error ? error.message : String(error),\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,qCAAqC;QACrC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC/C,OAAO;gBAAE,QAAQ;YAAK;QACxB;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QAE5C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YACjD,OAAO;gBAAE,MAAM;YAAO;QACxB;QAEA,gDAAgD;QAChD,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACtD,MAAM;gBAAE,aAAa;YAAK;YAC1B,OAAO;gBACL,QAAQ;oBACN,IAAI;wBAAC;wBAAQ;wBAAW;qBAAY;gBACtC;YACF;QACF;QAEA,MAAM,eAAe,OAAO,mBAAmB,IAAI,CAAC,WAAW,IAAI;QAEnE,MAAM,QAAQ;YACZ,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;YACA,cAAc,EAAE;YAChB,aAAa,EAAE;YACf,cAAc,EAAE;QAClB;QAEA,QAAQ,GAAG,CAAC,+BAA+B,MAAM,QAAQ;QAEzD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QAEpE,oDAAoD;QACpD,MAAM,gBAAgB;YACpB,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;YACA,cAAc,EAAE;YAChB,aAAa,EAAE;YACf,cAAc,EAAE;QAClB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU;YACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF", "debugId": null}}]}