{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/admin/stats-simple/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { NextResponse } from \"next/server\";\n\nexport async function GET() {\n  try {\n    console.log(\"📊 Récupération des statistiques simplifiées...\");\n\n    // Récupérer les statistiques de base\n    const productsCount = await prisma.product.count({\n      where: { active: true },\n    });\n\n    const ordersCount = await prisma.order.count();\n\n    const customersCount = await prisma.customer.count({\n      where: { role: \"USER\" },\n    });\n\n    // Revenus totaux (commandes livrées uniquement)\n    const totalRevenueResult = await prisma.order.aggregate({\n      _sum: { totalAmount: true },\n      where: {\n        status: {\n          in: [\"PAID\", \"SHIPPED\", \"DELIVERED\"],\n        },\n      },\n    });\n\n    const totalRevenue = Number(totalRevenueResult._sum.totalAmount || 0);\n\n    const stats = {\n      overview: {\n        products: productsCount,\n        orders: ordersCount,\n        customers: customersCount,\n        revenue: totalRevenue,\n        ordersGrowth: 0,\n      },\n      recentOrders: [],\n      topProducts: [],\n      monthlyStats: [],\n    };\n\n    console.log(\"✅ Statistiques simplifiées:\", stats.overview);\n\n    return NextResponse.json({\n      success: true,\n      data: stats,\n    });\n  } catch (error) {\n    console.error(\"❌ Erreur lors de la récupération des statistiques:\", error);\n\n    // Retourner des données de fallback en cas d'erreur\n    const fallbackStats = {\n      overview: {\n        products: 0,\n        orders: 0,\n        customers: 0,\n        revenue: 0,\n        ordersGrowth: 0,\n      },\n      recentOrders: [],\n      topProducts: [],\n      monthlyStats: [],\n    };\n\n    return NextResponse.json({\n      success: true,\n      data: fallbackStats,\n      fallback: true,\n      error: error instanceof Error ? error.message : String(error),\n    });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,qCAAqC;QACrC,MAAM,gBAAgB,MAAM,sHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,KAAK,CAAC;YAC/C,OAAO;gBAAE,QAAQ;YAAK;QACxB;QAEA,MAAM,cAAc,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,KAAK;QAE5C,MAAM,iBAAiB,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;YACjD,OAAO;gBAAE,MAAM;YAAO;QACxB;QAEA,gDAAgD;QAChD,MAAM,qBAAqB,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,SAAS,CAAC;YACtD,MAAM;gBAAE,aAAa;YAAK;YAC1B,OAAO;gBACL,QAAQ;oBACN,IAAI;wBAAC;wBAAQ;wBAAW;qBAAY;gBACtC;YACF;QACF;QAEA,MAAM,eAAe,OAAO,mBAAmB,IAAI,CAAC,WAAW,IAAI;QAEnE,MAAM,QAAQ;YACZ,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;YACA,cAAc,EAAE;YAChB,aAAa,EAAE;YACf,cAAc,EAAE;QAClB;QAEA,QAAQ,GAAG,CAAC,+BAA+B,MAAM,QAAQ;QAEzD,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;QACR;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sDAAsD;QAEpE,oDAAoD;QACpD,MAAM,gBAAgB;YACpB,UAAU;gBACR,UAAU;gBACV,QAAQ;gBACR,WAAW;gBACX,SAAS;gBACT,cAAc;YAChB;YACA,cAAc,EAAE;YAChB,aAAa,EAAE;YACf,cAAc,EAAE;QAClB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,MAAM;YACN,UAAU;YACV,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG,OAAO;QACzD;IACF;AACF", "debugId": null}}]}