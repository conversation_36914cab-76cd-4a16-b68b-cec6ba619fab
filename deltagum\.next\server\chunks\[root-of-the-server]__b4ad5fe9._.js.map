{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 159, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/stripe.ts"], "sourcesContent": ["import Stripe from \"stripe\";\n\nif (!process.env.STRIPE_SECRET_KEY) {\n  throw new Error(\"STRIPE_SECRET_KEY is not defined in environment variables\");\n}\n\nexport const stripe = new Stripe(process.env.STRIPE_SECRET_KEY, {\n  apiVersion: \"2025-06-30.basil\",\n  typescript: true,\n});\n\nexport const getStripePublishableKey = () => {\n  if (!process.env.STRIPE_PUBLISHABLE_KEY) {\n    throw new Error(\n      \"STRIPE_PUBLISHABLE_KEY is not defined in environment variables\"\n    );\n  }\n  return process.env.STRIPE_PUBLISHABLE_KEY;\n};\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAClC,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,+OAAA,CAAA,UAAM,CAAC,QAAQ,GAAG,CAAC,iBAAiB,EAAE;IAC9D,YAAY;IACZ,YAAY;AACd;AAEO,MAAM,0BAA0B;IACrC,IAAI,CAAC,QAAQ,GAAG,CAAC,sBAAsB,EAAE;QACvC,MAAM,IAAI,MACR;IAEJ;IACA,OAAO,QAAQ,GAAG,CAAC,sBAAsB;AAC3C", "debugId": null}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/checkout/session/%5BsessionId%5D/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport { stripe } from \"@/lib/stripe\";\nimport { ApiResponse } from \"@/types\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\n// GET /api/checkout/session/[sessionId] - Vérifier le statut d'une session\nexport async function GET(\n  request: NextRequest,\n  { params }: { params: Promise<{ sessionId: string }> }\n) {\n  const { sessionId } = await params;\n  try {\n    // Récupérer la session depuis Stripe\n    const session = await stripe.checkout.sessions.retrieve(sessionId, {\n      expand: [\"payment_intent\"],\n    });\n\n    if (!session) {\n      const response: ApiResponse = {\n        success: false,\n        error: \"Session non trouvée\",\n      };\n      return NextResponse.json(response, { status: 404 });\n    }\n\n    // Récupérer la commande associée\n    let order: any = null;\n    if (session.metadata?.orderId) {\n      order = await prisma.order.findUnique({\n        where: { id: session.metadata.orderId },\n        include: {\n          customer: true,\n          items: {\n            include: {\n              product: true,\n              variant: true,\n            },\n          },\n        },\n      });\n    }\n\n    const response: ApiResponse = {\n      success: true,\n      data: {\n        session: {\n          id: session.id,\n          status: session.status,\n          payment_status: session.payment_status,\n          customer_email: session.customer_email,\n          amount_total: session.amount_total,\n          currency: session.currency,\n          created: session.created,\n          metadata: session.metadata,\n        },\n        order,\n      },\n    };\n\n    return NextResponse.json(response);\n  } catch (error) {\n    console.error(\"Error retrieving checkout session:\", error);\n\n    const response: ApiResponse = {\n      success: false,\n      error: \"Erreur lors de la récupération de la session\",\n    };\n\n    return NextResponse.json(response, { status: 500 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAEA;;;;AAGO,eAAe,IACpB,OAAoB,EACpB,EAAE,MAAM,EAA8C;IAEtD,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM;IAC5B,IAAI;QACF,qCAAqC;QACrC,MAAM,UAAU,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,WAAW;YACjE,QAAQ;gBAAC;aAAiB;QAC5B;QAEA,IAAI,CAAC,SAAS;YACZ,MAAM,WAAwB;gBAC5B,SAAS;gBACT,OAAO;YACT;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;gBAAE,QAAQ;YAAI;QACnD;QAEA,iCAAiC;QACjC,IAAI,QAAa;QACjB,IAAI,QAAQ,QAAQ,EAAE,SAAS;YAC7B,QAAQ,MAAM,sHAAA,CAAA,SAAM,CAAC,KAAK,CAAC,UAAU,CAAC;gBACpC,OAAO;oBAAE,IAAI,QAAQ,QAAQ,CAAC,OAAO;gBAAC;gBACtC,SAAS;oBACP,UAAU;oBACV,OAAO;wBACL,SAAS;4BACP,SAAS;4BACT,SAAS;wBACX;oBACF;gBACF;YACF;QACF;QAEA,MAAM,WAAwB;YAC5B,SAAS;YACT,MAAM;gBACJ,SAAS;oBACP,IAAI,QAAQ,EAAE;oBACd,QAAQ,QAAQ,MAAM;oBACtB,gBAAgB,QAAQ,cAAc;oBACtC,gBAAgB,QAAQ,cAAc;oBACtC,cAAc,QAAQ,YAAY;oBAClC,UAAU,QAAQ,QAAQ;oBAC1B,SAAS,QAAQ,OAAO;oBACxB,UAAU,QAAQ,QAAQ;gBAC5B;gBACA;YACF;QACF;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,MAAM,WAAwB;YAC5B,SAAS;YACT,OAAO;QACT;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD;AACF", "debugId": null}}]}