{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["// Déclaration globale en dehors du try/catch\ndeclare global {\n  var __prisma: any | undefined;\n}\n\n// Fallback pour le build quand Prisma n'est pas disponible\nlet prisma: any;\n\ntry {\n  // eslint-disable-next-line @typescript-eslint/no-require-imports\n  const { PrismaClient } = require(\"@prisma/client\");\n\n  const globalForPrisma = globalThis as unknown as {\n    prisma: any | undefined;\n  };\n\n  prisma =\n    globalForPrisma.prisma ||\n    new PrismaClient({\n      log: process.env.NODE_ENV === \"development\" ? [\"error\"] : [\"error\"],\n    });\n\n  if (process.env.NODE_ENV !== \"production\") {\n    globalForPrisma.prisma = prisma;\n    globalThis.__prisma = prisma;\n  }\n} catch (error) {\n  // Fallback pour le build\n  console.warn(\"Prisma client not available during build, using mock\");\n  prisma = {\n    product: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    customer: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    order: {\n      findMany: () => Promise.resolve([]),\n      create: () => Promise.resolve({}),\n      update: () => Promise.resolve({}),\n      delete: () => Promise.resolve({}),\n    },\n    $transaction: (fn: any) => fn(prisma),\n  };\n}\n\nexport { prisma };\n"], "names": [], "mappings": "AAAA,6CAA6C;;;;AAK7C,2DAA2D;AAC3D,IAAI;AAEJ,IAAI;IACF,iEAAiE;IACjE,MAAM,EAAE,YAAY,EAAE;IAEtB,MAAM,kBAAkB;IAIxB,SACE,gBAAgB,MAAM,IACtB,IAAI,aAAa;QACf,KAAK,uCAAyC;YAAC;SAAQ;IACzD;IAEF,wCAA2C;QACzC,gBAAgB,MAAM,GAAG;QACzB,WAAW,QAAQ,GAAG;IACxB;AACF,EAAE,OAAO,OAAO;IACd,yBAAyB;IACzB,QAAQ,IAAI,CAAC;IACb,SAAS;QACP,SAAS;YACP,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,UAAU;YACR,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,OAAO;YACL,UAAU,IAAM,QAAQ,OAAO,CAAC,EAAE;YAClC,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;YAC/B,QAAQ,IAAM,QAAQ,OAAO,CAAC,CAAC;QACjC;QACA,cAAc,CAAC,KAAY,GAAG;IAChC;AACF", "debugId": null}}, {"offset": {"line": 151, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/auth/register/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport bcrypt from \"bcryptjs\";\nimport jwt from \"jsonwebtoken\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const {\n      email,\n      password,\n      firstName,\n      lastName,\n      phone,\n      address,\n      postalCode,\n      city,\n    } = await request.json();\n\n    // Validation des données\n    if (\n      !email ||\n      !password ||\n      !firstName ||\n      !lastName ||\n      !phone ||\n      !address ||\n      !postalCode ||\n      !city\n    ) {\n      return NextResponse.json(\n        { error: \"Tous les champs sont requis\" },\n        { status: 400 }\n      );\n    }\n\n    // Validation de l'email\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    if (!emailRegex.test(email)) {\n      return NextResponse.json(\n        { error: \"Format d'email invalide\" },\n        { status: 400 }\n      );\n    }\n\n    // Validation du mot de passe\n    if (password.length < 6) {\n      return NextResponse.json(\n        { error: \"Le mot de passe doit contenir au moins 6 caractères\" },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier si l'utilisateur existe déjà\n    try {\n      const existingUser = await prisma.customer.findUnique({\n        where: { email },\n      });\n\n      if (existingUser) {\n        return NextResponse.json(\n          { error: \"Un compte avec cet email existe déjà\" },\n          { status: 409 }\n        );\n      }\n    } catch (dbError) {\n      console.log(\"Erreur de connexion à la base de données, mode démo activé\");\n\n      // Mode démo pour admin\n      if (email === \"<EMAIL>\") {\n        const token = jwt.sign(\n          {\n            userId: \"demo-admin-id\",\n            email: email,\n            role: \"ADMIN\",\n          },\n          process.env.JWT_SECRET || \"fallback-secret\",\n          { expiresIn: \"7d\" }\n        );\n\n        const response = NextResponse.json({\n          message: \"Inscription réussie (mode démo)\",\n          user: {\n            id: \"demo-admin-id\",\n            email: email,\n            firstName: firstName,\n            lastName: lastName,\n            role: \"ADMIN\",\n          },\n        });\n\n        response.cookies.set(\"auth-token\", token, {\n          httpOnly: true,\n          secure: process.env.NODE_ENV === \"production\",\n          sameSite: \"lax\",\n          maxAge: 60 * 60 * 24 * 7, // 7 jours\n        });\n\n        return response;\n      }\n\n      return NextResponse.json(\n        {\n          error:\n            \"Base de données temporairement indisponible. Veuillez réessayer plus tard.\",\n        },\n        { status: 503 }\n      );\n    }\n\n    // Hasher le mot de passe\n    const hashedPassword = await bcrypt.hash(password, 12);\n\n    // Créer l'utilisateur (admin si email spécifique)\n    const isAdmin = email === \"<EMAIL>\";\n\n    let user;\n    try {\n      user = await prisma.customer.create({\n        data: {\n          id: globalThis.crypto.randomUUID(),\n          email,\n          password: hashedPassword,\n          firstName,\n          lastName,\n          phone,\n          address,\n          postalCode,\n          city,\n          role: isAdmin ? \"ADMIN\" : \"USER\",\n          updatedAt: new Date(),\n        },\n      });\n    } catch (dbError) {\n      console.log(\"Erreur lors de la création de l'utilisateur:\", dbError);\n      return NextResponse.json(\n        {\n          error:\n            \"Erreur lors de la création du compte. Base de données indisponible.\",\n        },\n        { status: 503 }\n      );\n    }\n\n    // Générer le token JWT\n    const token = jwt.sign(\n      {\n        userId: user.id,\n        email: user.email,\n        role: user.role,\n      },\n      process.env.JWT_SECRET || \"fallback-secret\",\n      { expiresIn: \"7d\" }\n    );\n\n    // Créer la réponse avec le cookie\n    const response = NextResponse.json({\n      message: \"Inscription réussie\",\n      user: {\n        id: user.id,\n        email: user.email,\n        firstName: user.firstName,\n        lastName: user.lastName,\n        role: user.role,\n      },\n    });\n\n    // Définir le cookie HTTP-only\n    response.cookies.set(\"auth-token\", token, {\n      httpOnly: true,\n      secure: process.env.NODE_ENV === \"production\",\n      sameSite: \"lax\",\n      maxAge: 7 * 24 * 60 * 60, // 7 jours\n      path: \"/\",\n    });\n\n    return response;\n  } catch (error) {\n    console.error(\"Erreur lors de l'inscription:\", error);\n    return NextResponse.json(\n      { error: \"Erreur interne du serveur\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,EACJ,KAAK,EACL,QAAQ,EACR,SAAS,EACT,QAAQ,EACR,KAAK,EACL,OAAO,EACP,UAAU,EACV,IAAI,EACL,GAAG,MAAM,QAAQ,IAAI;QAEtB,yBAAyB;QACzB,IACE,CAAC,SACD,CAAC,YACD,CAAC,aACD,CAAC,YACD,CAAC,SACD,CAAC,WACD,CAAC,cACD,CAAC,MACD;YACA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA8B,GACvC;gBAAE,QAAQ;YAAI;QAElB;QAEA,wBAAwB;QACxB,MAAM,aAAa;QACnB,IAAI,CAAC,WAAW,IAAI,CAAC,QAAQ;YAC3B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAA0B,GACnC;gBAAE,QAAQ;YAAI;QAElB;QAEA,6BAA6B;QAC7B,IAAI,SAAS,MAAM,GAAG,GAAG;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAsD,GAC/D;gBAAE,QAAQ;YAAI;QAElB;QAEA,wCAAwC;QACxC,IAAI;YACF,MAAM,eAAe,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;gBACpD,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI,cAAc;gBAChB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;oBAAE,OAAO;gBAAuC,GAChD;oBAAE,QAAQ;gBAAI;YAElB;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,GAAG,CAAC;YAEZ,uBAAuB;YACvB,IAAI,UAAU,sBAAsB;gBAClC,MAAM,QAAQ,gMAAA,CAAA,UAAG,CAAC,IAAI,CACpB;oBACE,QAAQ;oBACR,OAAO;oBACP,MAAM;gBACR,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;oBAAE,WAAW;gBAAK;gBAGpB,MAAM,WAAW,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;oBACjC,SAAS;oBACT,MAAM;wBACJ,IAAI;wBACJ,OAAO;wBACP,WAAW;wBACX,UAAU;wBACV,MAAM;oBACR;gBACF;gBAEA,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;oBACxC,UAAU;oBACV,QAAQ,oDAAyB;oBACjC,UAAU;oBACV,QAAQ,KAAK,KAAK,KAAK;gBACzB;gBAEA,OAAO;YACT;YAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OACE;YACJ,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,yBAAyB;QACzB,MAAM,iBAAiB,MAAM,wLAAA,CAAA,UAAM,CAAC,IAAI,CAAC,UAAU;QAEnD,kDAAkD;QAClD,MAAM,UAAU,UAAU;QAE1B,IAAI;QACJ,IAAI;YACF,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;gBAClC,MAAM;oBACJ,IAAI,WAAW,MAAM,CAAC,UAAU;oBAChC;oBACA,UAAU;oBACV;oBACA;oBACA;oBACA;oBACA;oBACA;oBACA,MAAM,UAAU,UAAU;oBAC1B,WAAW,IAAI;gBACjB;YACF;QACF,EAAE,OAAO,SAAS;YAChB,QAAQ,GAAG,CAAC,gDAAgD;YAC5D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OACE;YACJ,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,uBAAuB;QACvB,MAAM,QAAQ,gMAAA,CAAA,UAAG,CAAC,IAAI,CACpB;YACE,QAAQ,KAAK,EAAE;YACf,OAAO,KAAK,KAAK;YACjB,MAAM,KAAK,IAAI;QACjB,GACA,QAAQ,GAAG,CAAC,UAAU,IAAI,mBAC1B;YAAE,WAAW;QAAK;QAGpB,kCAAkC;QAClC,MAAM,WAAW,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACjC,SAAS;YACT,MAAM;gBACJ,IAAI,KAAK,EAAE;gBACX,OAAO,KAAK,KAAK;gBACjB,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,MAAM,KAAK,IAAI;YACjB;QACF;QAEA,8BAA8B;QAC9B,SAAS,OAAO,CAAC,GAAG,CAAC,cAAc,OAAO;YACxC,UAAU;YACV,QAAQ,oDAAyB;YACjC,UAAU;YACV,QAAQ,IAAI,KAAK,KAAK;YACtB,MAAM;QACR;QAEA,OAAO;IACT,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,iCAAiC;QAC/C,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}