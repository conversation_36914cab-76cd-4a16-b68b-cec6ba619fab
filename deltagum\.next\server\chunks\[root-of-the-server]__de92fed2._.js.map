{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 84, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/upload/route.ts"], "sourcesContent": ["import { existsSync, mkdirSync } from \"fs\";\nimport { readdir, stat, writeFile } from \"fs/promises\";\nimport { NextRequest, NextResponse } from \"next/server\";\nimport { join } from \"path\";\n\nexport async function POST(request: NextRequest) {\n  try {\n    const data = await request.formData();\n    const file: File | null = data.get(\"file\") as unknown as File;\n\n    if (!file) {\n      return NextResponse.json(\n        { error: \"Aucun fichier fourni\" },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier le type de fichier\n    const allowedTypes = [\"image/jpeg\", \"image/jpg\", \"image/png\", \"image/webp\"];\n    if (!allowedTypes.includes(file.type)) {\n      return NextResponse.json(\n        {\n          error: \"Type de fichier non autorisé. Utilisez JPG, PNG ou WebP\",\n        },\n        { status: 400 }\n      );\n    }\n\n    // Vérifier la taille (max 5MB)\n    const maxSize = 5 * 1024 * 1024; // 5MB\n    if (file.size > maxSize) {\n      return NextResponse.json(\n        {\n          error: \"Fichier trop volumineux. Maximum 5MB\",\n        },\n        { status: 400 }\n      );\n    }\n\n    const bytes = await file.arrayBuffer();\n    const buffer = Buffer.from(bytes);\n\n    // Créer un nom de fichier unique\n    const timestamp = Date.now();\n    const originalName = file.name.replace(/[^a-zA-Z0-9.-]/g, \"_\");\n    const fileName = `${timestamp}_${originalName}`;\n\n    // Créer le dossier uploads s'il n'existe pas\n    const uploadsDir = join(process.cwd(), \"public\", \"uploads\");\n    if (!existsSync(uploadsDir)) {\n      mkdirSync(uploadsDir, { recursive: true });\n    }\n\n    // Sauvegarder le fichier\n    const filePath = join(uploadsDir, fileName);\n    await writeFile(filePath, buffer);\n\n    // Retourner l'URL du fichier\n    const fileUrl = `/uploads/${fileName}`;\n\n    return NextResponse.json({\n      success: true,\n      url: fileUrl,\n      fileName: fileName,\n      originalName: file.name,\n      size: file.size,\n      type: file.type,\n    });\n  } catch (error) {\n    console.error(\"Erreur lors de l'upload:\", error);\n    return NextResponse.json(\n      { error: \"Erreur lors de l'upload du fichier\" },\n      { status: 500 }\n    );\n  }\n}\n\n// API pour lister les images uploadées\nexport async function GET() {\n  try {\n    const uploadsDir = join(process.cwd(), \"public\", \"uploads\");\n\n    if (!existsSync(uploadsDir)) {\n      return NextResponse.json({ success: true, images: [] });\n    }\n\n    const files = await readdir(uploadsDir);\n\n    const images = await Promise.all(\n      files\n        .filter((file: string) => {\n          const ext = file.toLowerCase();\n          return (\n            ext.endsWith(\".jpg\") ||\n            ext.endsWith(\".jpeg\") ||\n            ext.endsWith(\".png\") ||\n            ext.endsWith(\".webp\")\n          );\n        })\n        .map(async (file: string) => {\n          const stats = await stat(join(uploadsDir, file));\n          return {\n            name: file,\n            url: `/uploads/${file}`,\n            size: stats.size,\n            createdAt: stats.birthtime,\n          };\n        })\n        .sort(\n          (a: any, b: any) =>\n            new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()\n        )\n    );\n\n    return NextResponse.json({ success: true, images });\n  } catch (error) {\n    console.error(\"Erreur lors de la récupération des images:\", error);\n    return NextResponse.json(\n      { error: \"Erreur lors de la récupération des images\" },\n      { status: 500 }\n    );\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;;;;;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,QAAQ;QACnC,MAAM,OAAoB,KAAK,GAAG,CAAC;QAEnC,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAuB,GAChC;gBAAE,QAAQ;YAAI;QAElB;QAEA,8BAA8B;QAC9B,MAAM,eAAe;YAAC;YAAc;YAAa;YAAa;SAAa;QAC3E,IAAI,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrC,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,+BAA+B;QAC/B,MAAM,UAAU,IAAI,OAAO,MAAM,MAAM;QACvC,IAAI,KAAK,IAAI,GAAG,SAAS;YACvB,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBACE,OAAO;YACT,GACA;gBAAE,QAAQ;YAAI;QAElB;QAEA,MAAM,QAAQ,MAAM,KAAK,WAAW;QACpC,MAAM,SAAS,OAAO,IAAI,CAAC;QAE3B,iCAAiC;QACjC,MAAM,YAAY,KAAK,GAAG;QAC1B,MAAM,eAAe,KAAK,IAAI,CAAC,OAAO,CAAC,mBAAmB;QAC1D,MAAM,WAAW,GAAG,UAAU,CAAC,EAAE,cAAc;QAE/C,6CAA6C;QAC7C,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;QACjD,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,aAAa;YAC3B,CAAA,GAAA,6FAAA,CAAA,YAAS,AAAD,EAAE,YAAY;gBAAE,WAAW;YAAK;QAC1C;QAEA,yBAAyB;QACzB,MAAM,WAAW,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;QAClC,MAAM,CAAA,GAAA,qHAAA,CAAA,YAAS,AAAD,EAAE,UAAU;QAE1B,6BAA6B;QAC7B,MAAM,UAAU,CAAC,SAAS,EAAE,UAAU;QAEtC,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB,SAAS;YACT,KAAK;YACL,UAAU;YACV,cAAc,KAAK,IAAI;YACvB,MAAM,KAAK,IAAI;YACf,MAAM,KAAK,IAAI;QACjB;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,4BAA4B;QAC1C,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAqC,GAC9C;YAAE,QAAQ;QAAI;IAElB;AACF;AAGO,eAAe;IACpB,IAAI;QACF,MAAM,aAAa,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,QAAQ,GAAG,IAAI,UAAU;QAEjD,IAAI,CAAC,CAAA,GAAA,6FAAA,CAAA,aAAU,AAAD,EAAE,aAAa;YAC3B,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,SAAS;gBAAM,QAAQ,EAAE;YAAC;QACvD;QAEA,MAAM,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,UAAO,AAAD,EAAE;QAE5B,MAAM,SAAS,MAAM,QAAQ,GAAG,CAC9B,MACG,MAAM,CAAC,CAAC;YACP,MAAM,MAAM,KAAK,WAAW;YAC5B,OACE,IAAI,QAAQ,CAAC,WACb,IAAI,QAAQ,CAAC,YACb,IAAI,QAAQ,CAAC,WACb,IAAI,QAAQ,CAAC;QAEjB,GACC,GAAG,CAAC,OAAO;YACV,MAAM,QAAQ,MAAM,CAAA,GAAA,qHAAA,CAAA,OAAI,AAAD,EAAE,CAAA,GAAA,iGAAA,CAAA,OAAI,AAAD,EAAE,YAAY;YAC1C,OAAO;gBACL,MAAM;gBACN,KAAK,CAAC,SAAS,EAAE,MAAM;gBACvB,MAAM,MAAM,IAAI;gBAChB,WAAW,MAAM,SAAS;YAC5B;QACF,GACC,IAAI,CACH,CAAC,GAAQ,IACP,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO,KAAK,IAAI,KAAK,EAAE,SAAS,EAAE,OAAO;QAIvE,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,SAAS;YAAM;QAAO;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8CAA8C;QAC5D,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4C,GACrD;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}