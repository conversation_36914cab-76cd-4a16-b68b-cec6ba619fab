{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/prisma.ts"], "sourcesContent": ["import { PrismaClient } from \"@prisma/client\";\n\ndeclare global {\n  var __prisma: PrismaClient | undefined;\n}\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined;\n};\n\n// Créer le client Prisma avec gestion d'erreur\nlet prismaInstance: PrismaClient;\n\ntry {\n  prismaInstance = new PrismaClient({\n    log:\n      process.env.NODE_ENV === \"development\"\n        ? [\"query\", \"error\", \"warn\"]\n        : [\"error\"],\n  });\n  console.log(\"✅ Prisma client créé avec succès\");\n} catch (error) {\n  console.error(\"❌ Erreur création Prisma client:\", error);\n  throw error;\n}\n\nexport const prisma = prismaInstance;\n\nif (process.env.NODE_ENV !== \"production\") {\n  globalForPrisma.prisma = prisma;\n  globalThis.__prisma = prisma;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMA,MAAM,kBAAkB;AAIxB,+CAA+C;AAC/C,IAAI;AAEJ,IAAI;IACF,iBAAiB,IAAI,6HAAA,CAAA,eAAY,CAAC;QAChC,KACE,uCACI;YAAC;YAAS;YAAS;SAAO;IAElC;IACA,QAAQ,GAAG,CAAC;AACd,EAAE,OAAO,OAAO;IACd,QAAQ,KAAK,CAAC,oCAAoC;IAClD,MAAM;AACR;AAEO,MAAM,SAAS;AAEtB,wCAA2C;IACzC,gBAAgB,MAAM,GAAG;IACzB,WAAW,QAAQ,GAAG;AACxB", "debugId": null}}, {"offset": {"line": 132, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/api/auth/me/route.ts"], "sourcesContent": ["import { prisma } from \"@/lib/prisma\";\nimport jwt from \"jsonwebtoken\";\nimport { NextRequest, NextResponse } from \"next/server\";\n\nexport async function GET(request: NextRequest) {\n  try {\n    const token = request.cookies.get(\"auth-token\")?.value;\n\n    if (!token) {\n      return NextResponse.json({ error: \"Non authentifié\" }, { status: 401 });\n    }\n\n    // Vérifier le token\n    const decoded = jwt.verify(\n      token,\n      process.env.JWT_SECRET || \"fallback-secret\"\n    ) as {\n      userId: string;\n      email: string;\n      role: string;\n    };\n\n    // Récupérer l'utilisateur\n    const user = await prisma.customer.findUnique({\n      where: { id: decoded.userId },\n      select: {\n        id: true,\n        email: true,\n        firstName: true,\n        lastName: true,\n        phone: true,\n        address: true,\n        postalCode: true,\n        city: true,\n        role: true,\n        createdAt: true,\n      },\n    });\n\n    if (!user) {\n      return NextResponse.json(\n        { error: \"Utilisateur non trouvé\" },\n        { status: 404 }\n      );\n    }\n\n    return NextResponse.json({ user });\n  } catch (error) {\n    console.error(\"Erreur lors de la vérification de l'utilisateur:\", error);\n    return NextResponse.json({ error: \"Token invalide\" }, { status: 401 });\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,QAAQ,QAAQ,OAAO,CAAC,GAAG,CAAC,eAAe;QAEjD,IAAI,CAAC,OAAO;YACV,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;gBAAE,OAAO;YAAkB,GAAG;gBAAE,QAAQ;YAAI;QACvE;QAEA,oBAAoB;QACpB,MAAM,UAAU,gMAAA,CAAA,UAAG,CAAC,MAAM,CACxB,OACA,QAAQ,GAAG,CAAC,UAAU,IAAI;QAO5B,0BAA0B;QAC1B,MAAM,OAAO,MAAM,sHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,UAAU,CAAC;YAC5C,OAAO;gBAAE,IAAI,QAAQ,MAAM;YAAC;YAC5B,QAAQ;gBACN,IAAI;gBACJ,OAAO;gBACP,WAAW;gBACX,UAAU;gBACV,OAAO;gBACP,SAAS;gBACT,YAAY;gBACZ,MAAM;gBACN,MAAM;gBACN,WAAW;YACb;QACF;QAEA,IAAI,CAAC,MAAM;YACT,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CACtB;gBAAE,OAAO;YAAyB,GAClC;gBAAE,QAAQ;YAAI;QAElB;QAEA,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE;QAAK;IAClC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oDAAoD;QAClE,OAAO,+OAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YAAE,OAAO;QAAiB,GAAG;YAAE,QAAQ;QAAI;IACtE;AACF", "debugId": null}}]}