module.exports = {

"[project]/src/lib/email.ts [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "resend": (()=>resend),
    "sendContactEmail": (()=>sendContactEmail),
    "sendOrderConfirmation": (()=>sendOrderConfirmation),
    "sendOrderConfirmationEmail": (()=>sendOrderConfirmationEmail),
    "sendWelcomeEmail": (()=>sendWelcomeEmail)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$resend$40$4$2e$6$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$resend$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/.pnpm/resend@4.6.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/resend/dist/index.mjs [app-route] (ecmascript)");
;
if (!process.env.RESEND_API_KEY) {
    throw new Error("RESEND_API_KEY is not defined in environment variables");
}
const resend = new __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f2e$pnpm$2f$resend$40$4$2e$6$2e$0_react$2d$dom$40$19$2e$1$2e$0_react$40$19$2e$1$2e$0_$5f$react$40$19$2e$1$2e$0$2f$node_modules$2f$resend$2f$dist$2f$index$2e$mjs__$5b$app$2d$route$5d$__$28$ecmascript$29$__["Resend"](process.env.RESEND_API_KEY);
const sendOrderConfirmation = async (to, orderData)=>{
    try {
        const { data, error } = await resend.emails.send({
            from: "Deltagum <<EMAIL>>",
            to: [
                to
            ],
            subject: `Confirmation de commande #${orderData.orderId}`,
            html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #FF6B9D;">Merci pour votre commande !</h1>
          <p>Bonjour ${orderData.customerName},</p>
          <p>Votre commande #${orderData.orderId} a été confirmée.</p>
          
          <h2>Détails de la commande :</h2>
          <ul>
            ${orderData.items.map((item)=>`
              <li>${item.name} - ${item.flavor} x${item.quantity} - ${item.price.toFixed(2)}€</li>
            `).join("")}
          </ul>
          
          <p><strong>Total : ${orderData.totalAmount.toFixed(2)}€</strong></p>
          
          <p>Votre commande sera expédiée sous 24-48h.</p>
          
          <p>Merci de votre confiance !</p>
          <p>L'équipe Deltagum</p>
        </div>
      `
        });
        if (error) {
            console.error("Error sending email:", error);
            return {
                success: false,
                error
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error("Error sending email:", error);
        return {
            success: false,
            error
        };
    }
};
const sendWelcomeEmail = async (to, customerName)=>{
    try {
        const { data, error } = await resend.emails.send({
            from: "Deltagum <<EMAIL>>",
            to: [
                to
            ],
            subject: "Bienvenue chez Deltagum !",
            html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <h1 style="color: #FF6B9D;">Bienvenue chez Deltagum !</h1>
          <p>Bonjour ${customerName},</p>
          <p>Merci de vous être inscrit chez Deltagum !</p>
          <p>Découvrez nos délicieux chewing-gums aux saveurs naturelles de fruits.</p>
          <p>Votre programme de fidélité a été activé avec 50 points de bienvenue !</p>
          <p>À bientôt,</p>
          <p>L'équipe Deltagum</p>
        </div>
      `
        });
        if (error) {
            console.error("Error sending welcome email:", error);
            return {
                success: false,
                error
            };
        }
        return {
            success: true,
            data
        };
    } catch (error) {
        console.error("Error sending welcome email:", error);
        return {
            success: false,
            error
        };
    }
};
const sendContactEmail = async (data)=>{
    try {
        const isProf = data.type === "professional";
        const { data: result, error } = await resend.emails.send({
            from: "Deltagum <<EMAIL>>",
            to: [
                "<EMAIL>"
            ],
            subject: isProf ? "Nouvelle demande professionnelle - Deltagum" : "Nouveau message de contact - Deltagum",
            html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🍬 Deltagum</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">
              ${isProf ? "Nouvelle demande professionnelle" : "Nouveau message de contact"}
            </p>
          </div>

          <div style="background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 20px;">
            <h2 style="color: #333; margin-top: 0;">Informations du contact</h2>
            <p><strong>Nom :</strong> ${data.name}</p>
            <p><strong>Email :</strong> ${data.email}</p>
            ${data.phone ? `<p><strong>Téléphone :</strong> ${data.phone}</p>` : ""}
            <p><strong>Type :</strong> ${isProf ? "Demande professionnelle/revendeur" : "Contact général"}</p>
          </div>

          <div style="background: white; padding: 25px; border: 1px solid #e5e7eb; border-radius: 8px;">
            <h3 style="color: #333; margin-top: 0;">Message :</h3>
            <p style="line-height: 1.6; color: #555;">${data.message.replace(/\n/g, "<br>")}</p>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #666; font-size: 14px;">
              Email envoyé automatiquement depuis le site Deltagum<br>
              <a href="mailto:${data.email}" style="color: #ec4899;">Répondre directement</a>
            </p>
          </div>
        </div>
      `,
            replyTo: data.email
        });
        if (error) {
            console.error("❌ Erreur envoi email de contact:", error);
            return {
                success: false,
                error
            };
        }
        console.log("✅ Email de contact envoyé:", result);
        return {
            success: true,
            data: result
        };
    } catch (error) {
        console.error("❌ Erreur envoi email de contact:", error);
        return {
            success: false,
            error
        };
    }
};
const sendOrderConfirmationEmail = async (data)=>{
    try {
        const itemsHtml = data.items.map((item)=>`
      <tr style="border-bottom: 1px solid #e5e7eb;">
        <td style="padding: 12px 0; color: #333;">
          ${item.name}${item.flavor ? ` - ${item.flavor}` : ""}
        </td>
        <td style="padding: 12px 0; text-align: center; color: #666;">
          ${item.quantity}
        </td>
        <td style="padding: 12px 0; text-align: right; color: #333; font-weight: 500;">
          ${(item.price * item.quantity).toFixed(2)}€
        </td>
      </tr>
    `).join("");
        // Email au client
        const customerResult = await resend.emails.send({
            from: "Deltagum <<EMAIL>>",
            to: [
                data.customerEmail
            ],
            subject: `Confirmation de commande #${data.orderId} - Deltagum`,
            html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <div style="background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;">
            <h1 style="color: white; margin: 0; font-size: 28px;">🍬 Deltagum</h1>
            <p style="color: white; margin: 10px 0 0 0; opacity: 0.9;">
              Merci pour votre commande !
            </p>
          </div>

          <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h2 style="color: #166534; margin: 0 0 10px 0; font-size: 18px;">✅ Commande confirmée</h2>
            <p style="color: #166534; margin: 0;">
              Votre commande <strong>#${data.orderId}</strong> a été confirmée et sera traitée dans les plus brefs délais.
            </p>
          </div>

          <div style="background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 25px;">
            <h3 style="color: #333; margin-top: 0;">Détails de la commande</h3>
            <table style="width: 100%; border-collapse: collapse;">
              <thead>
                <tr style="background: #f8f9fa;">
                  <th style="padding: 12px 0; text-align: left; color: #666; font-weight: 600;">Produit</th>
                  <th style="padding: 12px 0; text-align: center; color: #666; font-weight: 600;">Qté</th>
                  <th style="padding: 12px 0; text-align: right; color: #666; font-weight: 600;">Total</th>
                </tr>
              </thead>
              <tbody>
                ${itemsHtml}
              </tbody>
            </table>

            <div style="margin-top: 20px; padding-top: 20px; border-top: 2px solid #ec4899;">
              <div style="text-align: right;">
                <span style="font-size: 18px; font-weight: bold; color: #333;">
                  Total : ${data.totalAmount.toFixed(2)}€
                </span>
              </div>
            </div>
          </div>

          <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #333; margin-top: 0;">Adresse de livraison</h3>
            <p style="margin: 0; line-height: 1.6; color: #555;">
              ${data.shippingAddress.firstName} ${data.shippingAddress.lastName}<br>
              ${data.shippingAddress.street}<br>
              ${data.shippingAddress.postalCode} ${data.shippingAddress.city}
              ${data.shippingAddress.phone ? `<br>Tél: ${data.shippingAddress.phone}` : ""}
            </p>
          </div>

          <div style="background: #fef3c7; border: 1px solid #fbbf24; padding: 20px; border-radius: 8px; margin-bottom: 25px;">
            <h3 style="color: #92400e; margin-top: 0;">⚠️ Informations importantes</h3>
            <ul style="color: #92400e; margin: 0; padding-left: 20px;">
              <li>Produit contenant du Delta-9 THC (< 0.3%)</li>
              <li>Réservé aux personnes majeures (18+)</li>
              <li>Conforme à la réglementation européenne</li>
              <li>Consommation responsable recommandée</li>
            </ul>
          </div>

          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;">
            <p style="color: #666; font-size: 14px; margin: 0;">
              Besoin d'aide ? Contactez-nous à
              <a href="mailto:<EMAIL>" style="color: #ec4899;"><EMAIL></a>
            </p>
            <p style="color: #666; font-size: 12px; margin: 10px 0 0 0;">
              Deltagum - Délices au Delta-9 THC
            </p>
          </div>
        </div>
      `
        });
        // Email de notification à l'admin
        const adminResult = await resend.emails.send({
            from: "Deltagum <<EMAIL>>",
            to: [
                "<EMAIL>"
            ],
            subject: `Nouvelle commande #${data.orderId} - ${data.customerName}`,
            html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
          <h2>🛒 Nouvelle commande reçue</h2>
          <p><strong>Commande :</strong> #${data.orderId}</p>
          <p><strong>Client :</strong> ${data.customerName} (${data.customerEmail})</p>
          <p><strong>Montant :</strong> ${data.totalAmount.toFixed(2)}€</p>
          <p><strong>Nombre d'articles :</strong> ${data.items.reduce((sum, item)=>sum + item.quantity, 0)}</p>

          <h3>Articles commandés :</h3>
          <ul>
            ${data.items.map((item)=>`
              <li>${item.name}${item.flavor ? ` - ${item.flavor}` : ""} x${item.quantity} = ${(item.price * item.quantity).toFixed(2)}€</li>
            `).join("")}
          </ul>

          <h3>Adresse de livraison :</h3>
          <p>
            ${data.shippingAddress.firstName} ${data.shippingAddress.lastName}<br>
            ${data.shippingAddress.street}<br>
            ${data.shippingAddress.postalCode} ${data.shippingAddress.city}
            ${data.shippingAddress.phone ? `<br>Tél: ${data.shippingAddress.phone}` : ""}
          </p>

          <p><a href="${("TURBOPACK compile-time value", "http://localhost:3000") || "http://localhost:3000"}/admin/dashboard">Voir dans le dashboard admin</a></p>
        </div>
      `
        });
        console.log("✅ Emails de commande envoyés:", {
            customerResult,
            adminResult
        });
        return {
            success: true,
            data: {
                customerResult,
                adminResult
            }
        };
    } catch (error) {
        console.error("❌ Erreur envoi emails de commande:", error);
        return {
            success: false,
            error
        };
    }
};
}}),
"[project]/node_modules/.pnpm/resend@4.6.0_react-dom@19.1.0_react@19.1.0__react@19.1.0/node_modules/resend/dist/index.mjs [app-route] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "Resend": (()=>Resend)
});
var __defProp = Object.defineProperty;
var __defProps = Object.defineProperties;
var __getOwnPropDescs = Object.getOwnPropertyDescriptors;
var __getOwnPropSymbols = Object.getOwnPropertySymbols;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __propIsEnum = Object.prototype.propertyIsEnumerable;
var __defNormalProp = (obj, key, value)=>key in obj ? __defProp(obj, key, {
        enumerable: true,
        configurable: true,
        writable: true,
        value
    }) : obj[key] = value;
var __spreadValues = (a, b)=>{
    for(var prop in b || (b = {}))if (__hasOwnProp.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    if (__getOwnPropSymbols) for (var prop of __getOwnPropSymbols(b)){
        if (__propIsEnum.call(b, prop)) __defNormalProp(a, prop, b[prop]);
    }
    return a;
};
var __spreadProps = (a, b)=>__defProps(a, __getOwnPropDescs(b));
var __async = (__this, __arguments, generator)=>{
    return new Promise((resolve, reject)=>{
        var fulfilled = (value)=>{
            try {
                step(generator.next(value));
            } catch (e) {
                reject(e);
            }
        };
        var rejected = (value)=>{
            try {
                step(generator.throw(value));
            } catch (e) {
                reject(e);
            }
        };
        var step = (x)=>x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);
        step((generator = generator.apply(__this, __arguments)).next());
    });
};
// package.json
var version = "4.6.0";
// src/api-keys/api-keys.ts
var ApiKeys = class {
    constructor(resend){
        this.resend = resend;
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            const data = yield this.resend.post("/api-keys", payload, options);
            return data;
        });
    }
    list() {
        return __async(this, null, function*() {
            const data = yield this.resend.get("/api-keys");
            return data;
        });
    }
    remove(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.delete(`/api-keys/${id}`);
            return data;
        });
    }
};
// src/audiences/audiences.ts
var Audiences = class {
    constructor(resend){
        this.resend = resend;
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            const data = yield this.resend.post("/audiences", payload, options);
            return data;
        });
    }
    list() {
        return __async(this, null, function*() {
            const data = yield this.resend.get("/audiences");
            return data;
        });
    }
    get(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.get(`/audiences/${id}`);
            return data;
        });
    }
    remove(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.delete(`/audiences/${id}`);
            return data;
        });
    }
};
// src/common/utils/parse-email-to-api-options.ts
function parseEmailToApiOptions(email) {
    return {
        attachments: email.attachments,
        bcc: email.bcc,
        cc: email.cc,
        from: email.from,
        headers: email.headers,
        html: email.html,
        reply_to: email.replyTo,
        scheduled_at: email.scheduledAt,
        subject: email.subject,
        tags: email.tags,
        text: email.text,
        to: email.to
    };
}
// src/batch/batch.ts
var Batch = class {
    constructor(resend){
        this.resend = resend;
    }
    send(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            return this.create(payload, options);
        });
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            const emails = [];
            for (const email of payload){
                if (email.react) {
                    if (!this.renderAsync) {
                        try {
                            const { renderAsync } = yield __turbopack_context__.r("[project]/node_modules/.pnpm/@react-email+render@1.1.2_r_dc87bafdebf72a670d0126d1d290625a/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                            this.renderAsync = renderAsync;
                        } catch (error) {
                            throw new Error("Failed to render React component. Make sure to install `@react-email/render`");
                        }
                    }
                    email.html = yield this.renderAsync(email.react);
                    email.react = void 0;
                }
                emails.push(parseEmailToApiOptions(email));
            }
            const data = yield this.resend.post("/emails/batch", emails, options);
            return data;
        });
    }
};
// src/broadcasts/broadcasts.ts
var Broadcasts = class {
    constructor(resend){
        this.resend = resend;
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            if (payload.react) {
                if (!this.renderAsync) {
                    try {
                        const { renderAsync } = yield __turbopack_context__.r("[project]/node_modules/.pnpm/@react-email+render@1.1.2_r_dc87bafdebf72a670d0126d1d290625a/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                        this.renderAsync = renderAsync;
                    } catch (error) {
                        throw new Error("Failed to render React component. Make sure to install `@react-email/render`");
                    }
                }
                payload.html = yield this.renderAsync(payload.react);
            }
            const data = yield this.resend.post("/broadcasts", {
                name: payload.name,
                audience_id: payload.audienceId,
                preview_text: payload.previewText,
                from: payload.from,
                html: payload.html,
                reply_to: payload.replyTo,
                subject: payload.subject,
                text: payload.text
            }, options);
            return data;
        });
    }
    send(id, payload) {
        return __async(this, null, function*() {
            const data = yield this.resend.post(`/broadcasts/${id}/send`, {
                scheduled_at: payload == null ? void 0 : payload.scheduledAt
            });
            return data;
        });
    }
    list() {
        return __async(this, null, function*() {
            const data = yield this.resend.get("/broadcasts");
            return data;
        });
    }
    get(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.get(`/broadcasts/${id}`);
            return data;
        });
    }
    remove(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.delete(`/broadcasts/${id}`);
            return data;
        });
    }
    update(id, payload) {
        return __async(this, null, function*() {
            const data = yield this.resend.patch(`/broadcasts/${id}`, {
                name: payload.name,
                audience_id: payload.audienceId,
                from: payload.from,
                html: payload.html,
                text: payload.text,
                subject: payload.subject,
                reply_to: payload.replyTo,
                preview_text: payload.previewText
            });
            return data;
        });
    }
};
// src/contacts/contacts.ts
var Contacts = class {
    constructor(resend){
        this.resend = resend;
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            const data = yield this.resend.post(`/audiences/${payload.audienceId}/contacts`, {
                unsubscribed: payload.unsubscribed,
                email: payload.email,
                first_name: payload.firstName,
                last_name: payload.lastName
            }, options);
            return data;
        });
    }
    list(options) {
        return __async(this, null, function*() {
            const data = yield this.resend.get(`/audiences/${options.audienceId}/contacts`);
            return data;
        });
    }
    get(options) {
        return __async(this, null, function*() {
            if (!options.id && !options.email) {
                return {
                    data: null,
                    error: {
                        message: "Missing `id` or `email` field.",
                        name: "missing_required_field"
                    }
                };
            }
            const data = yield this.resend.get(`/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`);
            return data;
        });
    }
    update(payload) {
        return __async(this, null, function*() {
            if (!payload.id && !payload.email) {
                return {
                    data: null,
                    error: {
                        message: "Missing `id` or `email` field.",
                        name: "missing_required_field"
                    }
                };
            }
            const data = yield this.resend.patch(`/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`, {
                unsubscribed: payload.unsubscribed,
                first_name: payload.firstName,
                last_name: payload.lastName
            });
            return data;
        });
    }
    remove(payload) {
        return __async(this, null, function*() {
            if (!payload.id && !payload.email) {
                return {
                    data: null,
                    error: {
                        message: "Missing `id` or `email` field.",
                        name: "missing_required_field"
                    }
                };
            }
            const data = yield this.resend.delete(`/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`);
            return data;
        });
    }
};
// src/common/utils/parse-domain-to-api-options.ts
function parseDomainToApiOptions(domain) {
    return {
        name: domain.name,
        region: domain.region,
        custom_return_path: domain.customReturnPath
    };
}
// src/domains/domains.ts
var Domains = class {
    constructor(resend){
        this.resend = resend;
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            const data = yield this.resend.post("/domains", parseDomainToApiOptions(payload), options);
            return data;
        });
    }
    list() {
        return __async(this, null, function*() {
            const data = yield this.resend.get("/domains");
            return data;
        });
    }
    get(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.get(`/domains/${id}`);
            return data;
        });
    }
    update(payload) {
        return __async(this, null, function*() {
            const data = yield this.resend.patch(`/domains/${payload.id}`, {
                click_tracking: payload.clickTracking,
                open_tracking: payload.openTracking,
                tls: payload.tls
            });
            return data;
        });
    }
    remove(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.delete(`/domains/${id}`);
            return data;
        });
    }
    verify(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.post(`/domains/${id}/verify`);
            return data;
        });
    }
};
// src/emails/emails.ts
var Emails = class {
    constructor(resend){
        this.resend = resend;
    }
    send(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            return this.create(payload, options);
        });
    }
    create(_0) {
        return __async(this, arguments, function*(payload, options = {}) {
            if (payload.react) {
                if (!this.renderAsync) {
                    try {
                        const { renderAsync } = yield __turbopack_context__.r("[project]/node_modules/.pnpm/@react-email+render@1.1.2_r_dc87bafdebf72a670d0126d1d290625a/node_modules/@react-email/render/dist/node/index.mjs [app-route] (ecmascript, async loader)")(__turbopack_context__.i);
                        this.renderAsync = renderAsync;
                    } catch (error) {
                        throw new Error("Failed to render React component. Make sure to install `@react-email/render`");
                    }
                }
                payload.html = yield this.renderAsync(payload.react);
            }
            const data = yield this.resend.post("/emails", parseEmailToApiOptions(payload), options);
            return data;
        });
    }
    get(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.get(`/emails/${id}`);
            return data;
        });
    }
    update(payload) {
        return __async(this, null, function*() {
            const data = yield this.resend.patch(`/emails/${payload.id}`, {
                scheduled_at: payload.scheduledAt
            });
            return data;
        });
    }
    cancel(id) {
        return __async(this, null, function*() {
            const data = yield this.resend.post(`/emails/${id}/cancel`);
            return data;
        });
    }
};
// src/resend.ts
var defaultBaseUrl = "https://api.resend.com";
var defaultUserAgent = `resend-node:${version}`;
var baseUrl = typeof process !== "undefined" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;
var userAgent = typeof process !== "undefined" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;
var Resend = class {
    constructor(key){
        this.key = key;
        this.apiKeys = new ApiKeys(this);
        this.audiences = new Audiences(this);
        this.batch = new Batch(this);
        this.broadcasts = new Broadcasts(this);
        this.contacts = new Contacts(this);
        this.domains = new Domains(this);
        this.emails = new Emails(this);
        if (!key) {
            if (typeof process !== "undefined" && process.env) {
                this.key = process.env.RESEND_API_KEY;
            }
            if (!this.key) {
                throw new Error('Missing API key. Pass it to the constructor `new Resend("re_123")`');
            }
        }
        this.headers = new Headers({
            Authorization: `Bearer ${this.key}`,
            "User-Agent": userAgent,
            "Content-Type": "application/json"
        });
    }
    fetchRequest(_0) {
        return __async(this, arguments, function*(path, options = {}) {
            try {
                const response = yield fetch(`${baseUrl}${path}`, options);
                if (!response.ok) {
                    try {
                        const rawError = yield response.text();
                        return {
                            data: null,
                            error: JSON.parse(rawError)
                        };
                    } catch (err) {
                        if (err instanceof SyntaxError) {
                            return {
                                data: null,
                                error: {
                                    name: "application_error",
                                    message: "Internal server error. We are unable to process your request right now, please try again later."
                                }
                            };
                        }
                        const error = {
                            message: response.statusText,
                            name: "application_error"
                        };
                        if (err instanceof Error) {
                            return {
                                data: null,
                                error: __spreadProps(__spreadValues({}, error), {
                                    message: err.message
                                })
                            };
                        }
                        return {
                            data: null,
                            error
                        };
                    }
                }
                const data = yield response.json();
                return {
                    data,
                    error: null
                };
            } catch (error) {
                return {
                    data: null,
                    error: {
                        name: "application_error",
                        message: "Unable to fetch data. The request could not be resolved."
                    }
                };
            }
        });
    }
    post(_0, _1) {
        return __async(this, arguments, function*(path, entity, options = {}) {
            const headers = new Headers(this.headers);
            if (options.idempotencyKey) {
                headers.set("Idempotency-Key", options.idempotencyKey);
            }
            const requestOptions = __spreadValues({
                method: "POST",
                headers,
                body: JSON.stringify(entity)
            }, options);
            return this.fetchRequest(path, requestOptions);
        });
    }
    get(_0) {
        return __async(this, arguments, function*(path, options = {}) {
            const requestOptions = __spreadValues({
                method: "GET",
                headers: this.headers
            }, options);
            return this.fetchRequest(path, requestOptions);
        });
    }
    put(_0, _1) {
        return __async(this, arguments, function*(path, entity, options = {}) {
            const requestOptions = __spreadValues({
                method: "PUT",
                headers: this.headers,
                body: JSON.stringify(entity)
            }, options);
            return this.fetchRequest(path, requestOptions);
        });
    }
    patch(_0, _1) {
        return __async(this, arguments, function*(path, entity, options = {}) {
            const requestOptions = __spreadValues({
                method: "PATCH",
                headers: this.headers,
                body: JSON.stringify(entity)
            }, options);
            return this.fetchRequest(path, requestOptions);
        });
    }
    delete(path, query) {
        return __async(this, null, function*() {
            const requestOptions = {
                method: "DELETE",
                headers: this.headers,
                body: JSON.stringify(query)
            };
            return this.fetchRequest(path, requestOptions);
        });
    }
};
;
}}),

};

//# sourceMappingURL=_21b36791._.js.map