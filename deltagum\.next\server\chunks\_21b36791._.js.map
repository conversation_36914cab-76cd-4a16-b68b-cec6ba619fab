{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/email.ts"], "sourcesContent": ["import { Resend } from \"resend\";\n\nif (!process.env.RESEND_API_KEY) {\n  throw new Error(\"RESEND_API_KEY is not defined in environment variables\");\n}\n\nexport const resend = new Resend(process.env.RESEND_API_KEY);\n\n// Types pour les emails\ninterface ContactFormData {\n  name: string;\n  email: string;\n  phone?: string;\n  message: string;\n  type: \"contact\" | \"professional\";\n}\n\ninterface OrderData {\n  orderId: string;\n  customerName: string;\n  customerEmail: string;\n  totalAmount: number;\n  items: Array<{\n    name: string;\n    quantity: number;\n    price: number;\n    flavor?: string;\n  }>;\n  shippingAddress: {\n    firstName: string;\n    lastName: string;\n    street: string;\n    city: string;\n    postalCode: string;\n    phone?: string;\n  };\n}\n\nexport const sendOrderConfirmation = async (\n  to: string,\n  orderData: {\n    orderId: string;\n    customerName: string;\n    totalAmount: number;\n    items: Array<{\n      name: string;\n      flavor: string;\n      quantity: number;\n      price: number;\n    }>;\n  }\n) => {\n  try {\n    const { data, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [to],\n      subject: `Confirmation de commande #${orderData.orderId}`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h1 style=\"color: #FF6B9D;\">Merci pour votre commande !</h1>\n          <p>Bonjour ${orderData.customerName},</p>\n          <p>Votre commande #${orderData.orderId} a été confirmée.</p>\n          \n          <h2>Détails de la commande :</h2>\n          <ul>\n            ${orderData.items\n              .map(\n                (item) => `\n              <li>${item.name} - ${item.flavor} x${\n                  item.quantity\n                } - ${item.price.toFixed(2)}€</li>\n            `\n              )\n              .join(\"\")}\n          </ul>\n          \n          <p><strong>Total : ${orderData.totalAmount.toFixed(2)}€</strong></p>\n          \n          <p>Votre commande sera expédiée sous 24-48h.</p>\n          \n          <p>Merci de votre confiance !</p>\n          <p>L'équipe Deltagum</p>\n        </div>\n      `,\n    });\n\n    if (error) {\n      console.error(\"Error sending email:\", error);\n      return { success: false, error };\n    }\n\n    return { success: true, data };\n  } catch (error) {\n    console.error(\"Error sending email:\", error);\n    return { success: false, error };\n  }\n};\n\nexport const sendWelcomeEmail = async (to: string, customerName: string) => {\n  try {\n    const { data, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [to],\n      subject: \"Bienvenue chez Deltagum !\",\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;\">\n          <h1 style=\"color: #FF6B9D;\">Bienvenue chez Deltagum !</h1>\n          <p>Bonjour ${customerName},</p>\n          <p>Merci de vous être inscrit chez Deltagum !</p>\n          <p>Découvrez nos délicieux chewing-gums aux saveurs naturelles de fruits.</p>\n          <p>Votre programme de fidélité a été activé avec 50 points de bienvenue !</p>\n          <p>À bientôt,</p>\n          <p>L'équipe Deltagum</p>\n        </div>\n      `,\n    });\n\n    if (error) {\n      console.error(\"Error sending welcome email:\", error);\n      return { success: false, error };\n    }\n\n    return { success: true, data };\n  } catch (error) {\n    console.error(\"Error sending welcome email:\", error);\n    return { success: false, error };\n  }\n};\n\n// Fonction pour envoyer les emails de contact\nexport const sendContactEmail = async (data: ContactFormData) => {\n  try {\n    const isProf = data.type === \"professional\";\n\n    const { data: result, error } = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [\"<EMAIL>\"],\n      subject: isProf\n        ? \"Nouvelle demande professionnelle - Deltagum\"\n        : \"Nouveau message de contact - Deltagum\",\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">🍬 Deltagum</h1>\n            <p style=\"color: white; margin: 10px 0 0 0; opacity: 0.9;\">\n              ${\n                isProf\n                  ? \"Nouvelle demande professionnelle\"\n                  : \"Nouveau message de contact\"\n              }\n            </p>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 25px; border-radius: 8px; margin-bottom: 20px;\">\n            <h2 style=\"color: #333; margin-top: 0;\">Informations du contact</h2>\n            <p><strong>Nom :</strong> ${data.name}</p>\n            <p><strong>Email :</strong> ${data.email}</p>\n            ${\n              data.phone\n                ? `<p><strong>Téléphone :</strong> ${data.phone}</p>`\n                : \"\"\n            }\n            <p><strong>Type :</strong> ${\n              isProf ? \"Demande professionnelle/revendeur\" : \"Contact général\"\n            }</p>\n          </div>\n\n          <div style=\"background: white; padding: 25px; border: 1px solid #e5e7eb; border-radius: 8px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Message :</h3>\n            <p style=\"line-height: 1.6; color: #555;\">${data.message.replace(\n              /\\n/g,\n              \"<br>\"\n            )}</p>\n          </div>\n\n          <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n            <p style=\"color: #666; font-size: 14px;\">\n              Email envoyé automatiquement depuis le site Deltagum<br>\n              <a href=\"mailto:${\n                data.email\n              }\" style=\"color: #ec4899;\">Répondre directement</a>\n            </p>\n          </div>\n        </div>\n      `,\n      replyTo: data.email,\n    });\n\n    if (error) {\n      console.error(\"❌ Erreur envoi email de contact:\", error);\n      return { success: false, error };\n    }\n\n    console.log(\"✅ Email de contact envoyé:\", result);\n    return { success: true, data: result };\n  } catch (error) {\n    console.error(\"❌ Erreur envoi email de contact:\", error);\n    return { success: false, error };\n  }\n};\n\n// Fonction améliorée pour les confirmations de commande\nexport const sendOrderConfirmationEmail = async (data: OrderData) => {\n  try {\n    const itemsHtml = data.items\n      .map(\n        (item) => `\n      <tr style=\"border-bottom: 1px solid #e5e7eb;\">\n        <td style=\"padding: 12px 0; color: #333;\">\n          ${item.name}${item.flavor ? ` - ${item.flavor}` : \"\"}\n        </td>\n        <td style=\"padding: 12px 0; text-align: center; color: #666;\">\n          ${item.quantity}\n        </td>\n        <td style=\"padding: 12px 0; text-align: right; color: #333; font-weight: 500;\">\n          ${(item.price * item.quantity).toFixed(2)}€\n        </td>\n      </tr>\n    `\n      )\n      .join(\"\");\n\n    // Email au client\n    const customerResult = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [data.customerEmail],\n      subject: `Confirmation de commande #${data.orderId} - Deltagum`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <div style=\"background: linear-gradient(135deg, #ec4899, #f97316); padding: 30px; border-radius: 10px; text-align: center; margin-bottom: 30px;\">\n            <h1 style=\"color: white; margin: 0; font-size: 28px;\">🍬 Deltagum</h1>\n            <p style=\"color: white; margin: 10px 0 0 0; opacity: 0.9;\">\n              Merci pour votre commande !\n            </p>\n          </div>\n\n          <div style=\"background: #f0fdf4; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h2 style=\"color: #166534; margin: 0 0 10px 0; font-size: 18px;\">✅ Commande confirmée</h2>\n            <p style=\"color: #166534; margin: 0;\">\n              Votre commande <strong>#${\n                data.orderId\n              }</strong> a été confirmée et sera traitée dans les plus brefs délais.\n            </p>\n          </div>\n\n          <div style=\"background: white; border: 1px solid #e5e7eb; border-radius: 8px; padding: 25px; margin-bottom: 25px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Détails de la commande</h3>\n            <table style=\"width: 100%; border-collapse: collapse;\">\n              <thead>\n                <tr style=\"background: #f8f9fa;\">\n                  <th style=\"padding: 12px 0; text-align: left; color: #666; font-weight: 600;\">Produit</th>\n                  <th style=\"padding: 12px 0; text-align: center; color: #666; font-weight: 600;\">Qté</th>\n                  <th style=\"padding: 12px 0; text-align: right; color: #666; font-weight: 600;\">Total</th>\n                </tr>\n              </thead>\n              <tbody>\n                ${itemsHtml}\n              </tbody>\n            </table>\n\n            <div style=\"margin-top: 20px; padding-top: 20px; border-top: 2px solid #ec4899;\">\n              <div style=\"text-align: right;\">\n                <span style=\"font-size: 18px; font-weight: bold; color: #333;\">\n                  Total : ${data.totalAmount.toFixed(2)}€\n                </span>\n              </div>\n            </div>\n          </div>\n\n          <div style=\"background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h3 style=\"color: #333; margin-top: 0;\">Adresse de livraison</h3>\n            <p style=\"margin: 0; line-height: 1.6; color: #555;\">\n              ${data.shippingAddress.firstName} ${\n        data.shippingAddress.lastName\n      }<br>\n              ${data.shippingAddress.street}<br>\n              ${data.shippingAddress.postalCode} ${data.shippingAddress.city}\n              ${\n                data.shippingAddress.phone\n                  ? `<br>Tél: ${data.shippingAddress.phone}`\n                  : \"\"\n              }\n            </p>\n          </div>\n\n          <div style=\"background: #fef3c7; border: 1px solid #fbbf24; padding: 20px; border-radius: 8px; margin-bottom: 25px;\">\n            <h3 style=\"color: #92400e; margin-top: 0;\">⚠️ Informations importantes</h3>\n            <ul style=\"color: #92400e; margin: 0; padding-left: 20px;\">\n              <li>Produit contenant du Delta-9 THC (< 0.3%)</li>\n              <li>Réservé aux personnes majeures (18+)</li>\n              <li>Conforme à la réglementation européenne</li>\n              <li>Consommation responsable recommandée</li>\n            </ul>\n          </div>\n\n          <div style=\"text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #e5e7eb;\">\n            <p style=\"color: #666; font-size: 14px; margin: 0;\">\n              Besoin d'aide ? Contactez-nous à\n              <a href=\"mailto:<EMAIL>\" style=\"color: #ec4899;\"><EMAIL></a>\n            </p>\n            <p style=\"color: #666; font-size: 12px; margin: 10px 0 0 0;\">\n              Deltagum - Délices au Delta-9 THC\n            </p>\n          </div>\n        </div>\n      `,\n    });\n\n    // Email de notification à l'admin\n    const adminResult = await resend.emails.send({\n      from: \"Deltagum <<EMAIL>>\",\n      to: [\"<EMAIL>\"],\n      subject: `Nouvelle commande #${data.orderId} - ${data.customerName}`,\n      html: `\n        <div style=\"font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;\">\n          <h2>🛒 Nouvelle commande reçue</h2>\n          <p><strong>Commande :</strong> #${data.orderId}</p>\n          <p><strong>Client :</strong> ${data.customerName} (${\n        data.customerEmail\n      })</p>\n          <p><strong>Montant :</strong> ${data.totalAmount.toFixed(2)}€</p>\n          <p><strong>Nombre d'articles :</strong> ${data.items.reduce(\n            (sum, item) => sum + item.quantity,\n            0\n          )}</p>\n\n          <h3>Articles commandés :</h3>\n          <ul>\n            ${data.items\n              .map(\n                (item) => `\n              <li>${item.name}${item.flavor ? ` - ${item.flavor}` : \"\"} x${\n                  item.quantity\n                } = ${(item.price * item.quantity).toFixed(2)}€</li>\n            `\n              )\n              .join(\"\")}\n          </ul>\n\n          <h3>Adresse de livraison :</h3>\n          <p>\n            ${data.shippingAddress.firstName} ${\n        data.shippingAddress.lastName\n      }<br>\n            ${data.shippingAddress.street}<br>\n            ${data.shippingAddress.postalCode} ${data.shippingAddress.city}\n            ${\n              data.shippingAddress.phone\n                ? `<br>Tél: ${data.shippingAddress.phone}`\n                : \"\"\n            }\n          </p>\n\n          <p><a href=\"${\n            process.env.NEXT_PUBLIC_BASE_URL || \"http://localhost:3000\"\n          }/admin/dashboard\">Voir dans le dashboard admin</a></p>\n        </div>\n      `,\n    });\n\n    console.log(\"✅ Emails de commande envoyés:\", {\n      customerResult,\n      adminResult,\n    });\n    return { success: true, data: { customerResult, adminResult } };\n  } catch (error) {\n    console.error(\"❌ Erreur envoi emails de commande:\", error);\n    return { success: false, error };\n  }\n};\n"], "names": [], "mappings": ";;;;;;;AAAA;;AAEA,IAAI,CAAC,QAAQ,GAAG,CAAC,cAAc,EAAE;IAC/B,MAAM,IAAI,MAAM;AAClB;AAEO,MAAM,SAAS,IAAI,0QAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,CAAC,cAAc;AAgCpD,MAAM,wBAAwB,OACnC,IACA;IAYA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC/C,MAAM;YACN,IAAI;gBAAC;aAAG;YACR,SAAS,CAAC,0BAA0B,EAAE,UAAU,OAAO,EAAE;YACzD,MAAM,CAAC;;;qBAGQ,EAAE,UAAU,YAAY,CAAC;6BACjB,EAAE,UAAU,OAAO,CAAC;;;;YAIrC,EAAE,UAAU,KAAK,CACd,GAAG,CACF,CAAC,OAAS,CAAC;kBACT,EAAE,KAAK,IAAI,CAAC,GAAG,EAAE,KAAK,MAAM,CAAC,EAAE,EAC/B,KAAK,QAAQ,CACd,GAAG,EAAE,KAAK,KAAK,CAAC,OAAO,CAAC,GAAG;YAChC,CAAC,EAEE,IAAI,CAAC,IAAI;;;6BAGK,EAAE,UAAU,WAAW,CAAC,OAAO,CAAC,GAAG;;;;;;;MAO1D,CAAC;QACH;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,wBAAwB;YACtC,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAEO,MAAM,mBAAmB,OAAO,IAAY;IACjD,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC/C,MAAM;YACN,IAAI;gBAAC;aAAG;YACR,SAAS;YACT,MAAM,CAAC;;;qBAGQ,EAAE,aAAa;;;;;;;MAO9B,CAAC;QACH;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAC9C,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,OAAO;YAAE,SAAS;YAAM;QAAK;IAC/B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,gCAAgC;QAC9C,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,mBAAmB,OAAO;IACrC,IAAI;QACF,MAAM,SAAS,KAAK,IAAI,KAAK;QAE7B,MAAM,EAAE,MAAM,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YACvD,MAAM;YACN,IAAI;gBAAC;aAAyB;YAC9B,SAAS,SACL,gDACA;YACJ,MAAM,CAAC;;;;;cAKC,EACE,SACI,qCACA,6BACL;;;;;;sCAMuB,EAAE,KAAK,IAAI,CAAC;wCACV,EAAE,KAAK,KAAK,CAAC;YACzC,EACE,KAAK,KAAK,GACN,CAAC,gCAAgC,EAAE,KAAK,KAAK,CAAC,IAAI,CAAC,GACnD,GACL;uCAC0B,EACzB,SAAS,sCAAsC,kBAChD;;;;;sDAKyC,EAAE,KAAK,OAAO,CAAC,OAAO,CAC9D,OACA,QACA;;;;;;8BAMgB,EACd,KAAK,KAAK,CACX;;;;MAIT,CAAC;YACD,SAAS,KAAK,KAAK;QACrB;QAEA,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAClD,OAAO;gBAAE,SAAS;gBAAO;YAAM;QACjC;QAEA,QAAQ,GAAG,CAAC,8BAA8B;QAC1C,OAAO;YAAE,SAAS;YAAM,MAAM;QAAO;IACvC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oCAAoC;QAClD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF;AAGO,MAAM,6BAA6B,OAAO;IAC/C,IAAI;QACF,MAAM,YAAY,KAAK,KAAK,CACzB,GAAG,CACF,CAAC,OAAS,CAAC;;;UAGT,EAAE,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE,GAAG,GAAG;;;UAGrD,EAAE,KAAK,QAAQ,CAAC;;;UAGhB,EAAE,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG;;;IAGhD,CAAC,EAEE,IAAI,CAAC;QAER,kBAAkB;QAClB,MAAM,iBAAiB,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC9C,MAAM;YACN,IAAI;gBAAC,KAAK,aAAa;aAAC;YACxB,SAAS,CAAC,0BAA0B,EAAE,KAAK,OAAO,CAAC,WAAW,CAAC;YAC/D,MAAM,CAAC;;;;;;;;;;;;sCAYyB,EACtB,KAAK,OAAO,CACb;;;;;;;;;;;;;;;gBAeC,EAAE,UAAU;;;;;;;0BAOF,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG;;;;;;;;;cAS1C,EAAE,KAAK,eAAe,CAAC,SAAS,CAAC,CAAC,EACxC,KAAK,eAAe,CAAC,QAAQ,CAC9B;cACO,EAAE,KAAK,eAAe,CAAC,MAAM,CAAC;cAC9B,EAAE,KAAK,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,IAAI,CAAC;cAC/D,EACE,KAAK,eAAe,CAAC,KAAK,GACtB,CAAC,SAAS,EAAE,KAAK,eAAe,CAAC,KAAK,EAAE,GACxC,GACL;;;;;;;;;;;;;;;;;;;;;;;;MAwBT,CAAC;QACH;QAEA,kCAAkC;QAClC,MAAM,cAAc,MAAM,OAAO,MAAM,CAAC,IAAI,CAAC;YAC3C,MAAM;YACN,IAAI;gBAAC;aAAyB;YAC9B,SAAS,CAAC,mBAAmB,EAAE,KAAK,OAAO,CAAC,GAAG,EAAE,KAAK,YAAY,EAAE;YACpE,MAAM,CAAC;;;0CAG6B,EAAE,KAAK,OAAO,CAAC;uCAClB,EAAE,KAAK,YAAY,CAAC,EAAE,EACrD,KAAK,aAAa,CACnB;wCACiC,EAAE,KAAK,WAAW,CAAC,OAAO,CAAC,GAAG;kDACpB,EAAE,KAAK,KAAK,CAAC,MAAM,CACzD,CAAC,KAAK,OAAS,MAAM,KAAK,QAAQ,EAClC,GACA;;;;YAIA,EAAE,KAAK,KAAK,CACT,GAAG,CACF,CAAC,OAAS,CAAC;kBACT,EAAE,KAAK,IAAI,GAAG,KAAK,MAAM,GAAG,CAAC,GAAG,EAAE,KAAK,MAAM,EAAE,GAAG,GAAG,EAAE,EACvD,KAAK,QAAQ,CACd,GAAG,EAAE,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC,GAAG;YAClD,CAAC,EAEE,IAAI,CAAC,IAAI;;;;;YAKZ,EAAE,KAAK,eAAe,CAAC,SAAS,CAAC,CAAC,EACtC,KAAK,eAAe,CAAC,QAAQ,CAC9B;YACK,EAAE,KAAK,eAAe,CAAC,MAAM,CAAC;YAC9B,EAAE,KAAK,eAAe,CAAC,UAAU,CAAC,CAAC,EAAE,KAAK,eAAe,CAAC,IAAI,CAAC;YAC/D,EACE,KAAK,eAAe,CAAC,KAAK,GACtB,CAAC,SAAS,EAAE,KAAK,eAAe,CAAC,KAAK,EAAE,GACxC,GACL;;;sBAGS,EACV,6DAAoC,wBACrC;;MAEL,CAAC;QACH;QAEA,QAAQ,GAAG,CAAC,iCAAiC;YAC3C;YACA;QACF;QACA,OAAO;YAAE,SAAS;YAAM,MAAM;gBAAE;gBAAgB;YAAY;QAAE;IAChE,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sCAAsC;QACpD,OAAO;YAAE,SAAS;YAAO;QAAM;IACjC;AACF", "debugId": null}}, {"offset": {"line": 322, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/resend%404.6.0_react-dom%4019.1.0_react%4019.1.0__react%4019.1.0/node_modules/resend/dist/index.mjs"], "sourcesContent": ["var __defProp = Object.defineProperty;\nvar __defProps = Object.defineProperties;\nvar __getOwnPropDescs = Object.getOwnPropertyDescriptors;\nvar __getOwnPropSymbols = Object.getOwnPropertySymbols;\nvar __hasOwnProp = Object.prototype.hasOwnProperty;\nvar __propIsEnum = Object.prototype.propertyIsEnumerable;\nvar __defNormalProp = (obj, key, value) => key in obj ? __defProp(obj, key, { enumerable: true, configurable: true, writable: true, value }) : obj[key] = value;\nvar __spreadValues = (a, b) => {\n  for (var prop in b || (b = {}))\n    if (__hasOwnProp.call(b, prop))\n      __defNormalProp(a, prop, b[prop]);\n  if (__getOwnPropSymbols)\n    for (var prop of __getOwnPropSymbols(b)) {\n      if (__propIsEnum.call(b, prop))\n        __defNormalProp(a, prop, b[prop]);\n    }\n  return a;\n};\nvar __spreadProps = (a, b) => __defProps(a, __getOwnPropDescs(b));\nvar __async = (__this, __arguments, generator) => {\n  return new Promise((resolve, reject) => {\n    var fulfilled = (value) => {\n      try {\n        step(generator.next(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var rejected = (value) => {\n      try {\n        step(generator.throw(value));\n      } catch (e) {\n        reject(e);\n      }\n    };\n    var step = (x) => x.done ? resolve(x.value) : Promise.resolve(x.value).then(fulfilled, rejected);\n    step((generator = generator.apply(__this, __arguments)).next());\n  });\n};\n\n// package.json\nvar version = \"4.6.0\";\n\n// src/api-keys/api-keys.ts\nvar ApiKeys = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/api-keys\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/api-keys\");\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/api-keys/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/audiences/audiences.ts\nvar Audiences = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/audiences\",\n        payload,\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/audiences\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/audiences/${id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-email-to-api-options.ts\nfunction parseEmailToApiOptions(email) {\n  return {\n    attachments: email.attachments,\n    bcc: email.bcc,\n    cc: email.cc,\n    from: email.from,\n    headers: email.headers,\n    html: email.html,\n    reply_to: email.replyTo,\n    scheduled_at: email.scheduledAt,\n    subject: email.subject,\n    tags: email.tags,\n    text: email.text,\n    to: email.to\n  };\n}\n\n// src/batch/batch.ts\nvar Batch = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const emails = [];\n      for (const email of payload) {\n        if (email.react) {\n          if (!this.renderAsync) {\n            try {\n              const { renderAsync } = yield import(\"@react-email/render\");\n              this.renderAsync = renderAsync;\n            } catch (error) {\n              throw new Error(\n                \"Failed to render React component. Make sure to install `@react-email/render`\"\n              );\n            }\n          }\n          email.html = yield this.renderAsync(email.react);\n          email.react = void 0;\n        }\n        emails.push(parseEmailToApiOptions(email));\n      }\n      const data = yield this.resend.post(\n        \"/emails/batch\",\n        emails,\n        options\n      );\n      return data;\n    });\n  }\n};\n\n// src/broadcasts/broadcasts.ts\nvar Broadcasts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield import(\"@react-email/render\");\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/broadcasts\",\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          preview_text: payload.previewText,\n          from: payload.from,\n          html: payload.html,\n          reply_to: payload.replyTo,\n          subject: payload.subject,\n          text: payload.text\n        },\n        options\n      );\n      return data;\n    });\n  }\n  send(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/broadcasts/${id}/send`,\n        { scheduled_at: payload == null ? void 0 : payload.scheduledAt }\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/broadcasts\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/broadcasts/${id}`\n      );\n      return data;\n    });\n  }\n  update(id, payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/broadcasts/${id}`,\n        {\n          name: payload.name,\n          audience_id: payload.audienceId,\n          from: payload.from,\n          html: payload.html,\n          text: payload.text,\n          subject: payload.subject,\n          reply_to: payload.replyTo,\n          preview_text: payload.previewText\n        }\n      );\n      return data;\n    });\n  }\n};\n\n// src/contacts/contacts.ts\nvar Contacts = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        `/audiences/${payload.audienceId}/contacts`,\n        {\n          unsubscribed: payload.unsubscribed,\n          email: payload.email,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        },\n        options\n      );\n      return data;\n    });\n  }\n  list(options) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts`\n      );\n      return data;\n    });\n  }\n  get(options) {\n    return __async(this, null, function* () {\n      if (!options.id && !options.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.get(\n        `/audiences/${options.audienceId}/contacts/${(options == null ? void 0 : options.email) ? options == null ? void 0 : options.email : options == null ? void 0 : options.id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.patch(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`,\n        {\n          unsubscribed: payload.unsubscribed,\n          first_name: payload.firstName,\n          last_name: payload.lastName\n        }\n      );\n      return data;\n    });\n  }\n  remove(payload) {\n    return __async(this, null, function* () {\n      if (!payload.id && !payload.email) {\n        return {\n          data: null,\n          error: {\n            message: \"Missing `id` or `email` field.\",\n            name: \"missing_required_field\"\n          }\n        };\n      }\n      const data = yield this.resend.delete(\n        `/audiences/${payload.audienceId}/contacts/${(payload == null ? void 0 : payload.email) ? payload == null ? void 0 : payload.email : payload == null ? void 0 : payload.id}`\n      );\n      return data;\n    });\n  }\n};\n\n// src/common/utils/parse-domain-to-api-options.ts\nfunction parseDomainToApiOptions(domain) {\n  return {\n    name: domain.name,\n    region: domain.region,\n    custom_return_path: domain.customReturnPath\n  };\n}\n\n// src/domains/domains.ts\nvar Domains = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      const data = yield this.resend.post(\n        \"/domains\",\n        parseDomainToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  list() {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\"/domains\");\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/domains/${payload.id}`,\n        {\n          click_tracking: payload.clickTracking,\n          open_tracking: payload.openTracking,\n          tls: payload.tls\n        }\n      );\n      return data;\n    });\n  }\n  remove(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.delete(\n        `/domains/${id}`\n      );\n      return data;\n    });\n  }\n  verify(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/domains/${id}/verify`\n      );\n      return data;\n    });\n  }\n};\n\n// src/emails/emails.ts\nvar Emails = class {\n  constructor(resend) {\n    this.resend = resend;\n  }\n  send(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      return this.create(payload, options);\n    });\n  }\n  create(_0) {\n    return __async(this, arguments, function* (payload, options = {}) {\n      if (payload.react) {\n        if (!this.renderAsync) {\n          try {\n            const { renderAsync } = yield import(\"@react-email/render\");\n            this.renderAsync = renderAsync;\n          } catch (error) {\n            throw new Error(\n              \"Failed to render React component. Make sure to install `@react-email/render`\"\n            );\n          }\n        }\n        payload.html = yield this.renderAsync(\n          payload.react\n        );\n      }\n      const data = yield this.resend.post(\n        \"/emails\",\n        parseEmailToApiOptions(payload),\n        options\n      );\n      return data;\n    });\n  }\n  get(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.get(\n        `/emails/${id}`\n      );\n      return data;\n    });\n  }\n  update(payload) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.patch(\n        `/emails/${payload.id}`,\n        {\n          scheduled_at: payload.scheduledAt\n        }\n      );\n      return data;\n    });\n  }\n  cancel(id) {\n    return __async(this, null, function* () {\n      const data = yield this.resend.post(\n        `/emails/${id}/cancel`\n      );\n      return data;\n    });\n  }\n};\n\n// src/resend.ts\nvar defaultBaseUrl = \"https://api.resend.com\";\nvar defaultUserAgent = `resend-node:${version}`;\nvar baseUrl = typeof process !== \"undefined\" && process.env ? process.env.RESEND_BASE_URL || defaultBaseUrl : defaultBaseUrl;\nvar userAgent = typeof process !== \"undefined\" && process.env ? process.env.RESEND_USER_AGENT || defaultUserAgent : defaultUserAgent;\nvar Resend = class {\n  constructor(key) {\n    this.key = key;\n    this.apiKeys = new ApiKeys(this);\n    this.audiences = new Audiences(this);\n    this.batch = new Batch(this);\n    this.broadcasts = new Broadcasts(this);\n    this.contacts = new Contacts(this);\n    this.domains = new Domains(this);\n    this.emails = new Emails(this);\n    if (!key) {\n      if (typeof process !== \"undefined\" && process.env) {\n        this.key = process.env.RESEND_API_KEY;\n      }\n      if (!this.key) {\n        throw new Error(\n          'Missing API key. Pass it to the constructor `new Resend(\"re_123\")`'\n        );\n      }\n    }\n    this.headers = new Headers({\n      Authorization: `Bearer ${this.key}`,\n      \"User-Agent\": userAgent,\n      \"Content-Type\": \"application/json\"\n    });\n  }\n  fetchRequest(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      try {\n        const response = yield fetch(`${baseUrl}${path}`, options);\n        if (!response.ok) {\n          try {\n            const rawError = yield response.text();\n            return { data: null, error: JSON.parse(rawError) };\n          } catch (err) {\n            if (err instanceof SyntaxError) {\n              return {\n                data: null,\n                error: {\n                  name: \"application_error\",\n                  message: \"Internal server error. We are unable to process your request right now, please try again later.\"\n                }\n              };\n            }\n            const error = {\n              message: response.statusText,\n              name: \"application_error\"\n            };\n            if (err instanceof Error) {\n              return { data: null, error: __spreadProps(__spreadValues({}, error), { message: err.message }) };\n            }\n            return { data: null, error };\n          }\n        }\n        const data = yield response.json();\n        return { data, error: null };\n      } catch (error) {\n        return {\n          data: null,\n          error: {\n            name: \"application_error\",\n            message: \"Unable to fetch data. The request could not be resolved.\"\n          }\n        };\n      }\n    });\n  }\n  post(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const headers = new Headers(this.headers);\n      if (options.idempotencyKey) {\n        headers.set(\"Idempotency-Key\", options.idempotencyKey);\n      }\n      const requestOptions = __spreadValues({\n        method: \"POST\",\n        headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  get(_0) {\n    return __async(this, arguments, function* (path, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"GET\",\n        headers: this.headers\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  put(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PUT\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  patch(_0, _1) {\n    return __async(this, arguments, function* (path, entity, options = {}) {\n      const requestOptions = __spreadValues({\n        method: \"PATCH\",\n        headers: this.headers,\n        body: JSON.stringify(entity)\n      }, options);\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n  delete(path, query) {\n    return __async(this, null, function* () {\n      const requestOptions = {\n        method: \"DELETE\",\n        headers: this.headers,\n        body: JSON.stringify(query)\n      };\n      return this.fetchRequest(path, requestOptions);\n    });\n  }\n};\nexport {\n  Resend\n};\n"], "names": [], "mappings": ";;;AAAA,IAAI,YAAY,OAAO,cAAc;AACrC,IAAI,aAAa,OAAO,gBAAgB;AACxC,IAAI,oBAAoB,OAAO,yBAAyB;AACxD,IAAI,sBAAsB,OAAO,qBAAqB;AACtD,IAAI,eAAe,OAAO,SAAS,CAAC,cAAc;AAClD,IAAI,eAAe,OAAO,SAAS,CAAC,oBAAoB;AACxD,IAAI,kBAAkB,CAAC,KAAK,KAAK,QAAU,OAAO,MAAM,UAAU,KAAK,KAAK;QAAE,YAAY;QAAM,cAAc;QAAM,UAAU;QAAM;IAAM,KAAK,GAAG,CAAC,IAAI,GAAG;AAC1J,IAAI,iBAAiB,CAAC,GAAG;IACvB,IAAK,IAAI,QAAQ,KAAK,CAAC,IAAI,CAAC,CAAC,EAC3B,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC,IAAI,qBACF,KAAK,IAAI,QAAQ,oBAAoB,GAAI;QACvC,IAAI,aAAa,IAAI,CAAC,GAAG,OACvB,gBAAgB,GAAG,MAAM,CAAC,CAAC,KAAK;IACpC;IACF,OAAO;AACT;AACA,IAAI,gBAAgB,CAAC,GAAG,IAAM,WAAW,GAAG,kBAAkB;AAC9D,IAAI,UAAU,CAAC,QAAQ,aAAa;IAClC,OAAO,IAAI,QAAQ,CAAC,SAAS;QAC3B,IAAI,YAAY,CAAC;YACf,IAAI;gBACF,KAAK,UAAU,IAAI,CAAC;YACtB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,WAAW,CAAC;YACd,IAAI;gBACF,KAAK,UAAU,KAAK,CAAC;YACvB,EAAE,OAAO,GAAG;gBACV,OAAO;YACT;QACF;QACA,IAAI,OAAO,CAAC,IAAM,EAAE,IAAI,GAAG,QAAQ,EAAE,KAAK,IAAI,QAAQ,OAAO,CAAC,EAAE,KAAK,EAAE,IAAI,CAAC,WAAW;QACvF,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,QAAQ,YAAY,EAAE,IAAI;IAC9D;AACF;AAEA,eAAe;AACf,IAAI,UAAU;AAEd,2BAA2B;AAC3B,IAAI,UAAU;IACZ,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,aACA,SACA;YAEF,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC,UAAU,EAAE,IAAI;YAEnB,OAAO;QACT;IACF;AACF;AAEA,6BAA6B;AAC7B,IAAI,YAAY;IACd,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,cACA,SACA;YAEF,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC,OAAO;QACT;IACF;IACA,IAAI,EAAE,EAAE;QACN,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,WAAW,EAAE,IAAI;YAEpB,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC,WAAW,EAAE,IAAI;YAEpB,OAAO;QACT;IACF;AACF;AAEA,iDAAiD;AACjD,SAAS,uBAAuB,KAAK;IACnC,OAAO;QACL,aAAa,MAAM,WAAW;QAC9B,KAAK,MAAM,GAAG;QACd,IAAI,MAAM,EAAE;QACZ,MAAM,MAAM,IAAI;QAChB,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,IAAI;QAChB,UAAU,MAAM,OAAO;QACvB,cAAc,MAAM,WAAW;QAC/B,SAAS,MAAM,OAAO;QACtB,MAAM,MAAM,IAAI;QAChB,MAAM,MAAM,IAAI;QAChB,IAAI,MAAM,EAAE;IACd;AACF;AAEA,qBAAqB;AACrB,IAAI,QAAQ;IACV,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,KAAK,EAAE,EAAE;QACP,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;QAC9B;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,SAAS,EAAE;YACjB,KAAK,MAAM,SAAS,QAAS;gBAC3B,IAAI,MAAM,KAAK,EAAE;oBACf,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;wBACrB,IAAI;4BACF,MAAM,EAAE,WAAW,EAAE,GAAG;4BACxB,IAAI,CAAC,WAAW,GAAG;wBACrB,EAAE,OAAO,OAAO;4BACd,MAAM,IAAI,MACR;wBAEJ;oBACF;oBACA,MAAM,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CAAC,MAAM,KAAK;oBAC/C,MAAM,KAAK,GAAG,KAAK;gBACrB;gBACA,OAAO,IAAI,CAAC,uBAAuB;YACrC;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,iBACA,QACA;YAEF,OAAO;QACT;IACF;AACF;AAEA,+BAA+B;AAC/B,IAAI,aAAa;IACf,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,IAAI,QAAQ,KAAK,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrB,IAAI;wBACF,MAAM,EAAE,WAAW,EAAE,GAAG;wBACxB,IAAI,CAAC,WAAW,GAAG;oBACrB,EAAE,OAAO,OAAO;wBACd,MAAM,IAAI,MACR;oBAEJ;gBACF;gBACA,QAAQ,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CACnC,QAAQ,KAAK;YAEjB;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,eACA;gBACE,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,UAAU;gBAC/B,cAAc,QAAQ,WAAW;gBACjC,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,UAAU,QAAQ,OAAO;gBACzB,SAAS,QAAQ,OAAO;gBACxB,MAAM,QAAQ,IAAI;YACpB,GACA;YAEF,OAAO;QACT;IACF;IACA,KAAK,EAAE,EAAE,OAAO,EAAE;QAChB,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,CAAC,YAAY,EAAE,GAAG,KAAK,CAAC,EACxB;gBAAE,cAAc,WAAW,OAAO,KAAK,IAAI,QAAQ,WAAW;YAAC;YAEjE,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC,OAAO;QACT;IACF;IACA,IAAI,EAAE,EAAE;QACN,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,YAAY,EAAE,IAAI;YAErB,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC,YAAY,EAAE,IAAI;YAErB,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE,OAAO,EAAE;QAClB,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAClC,CAAC,YAAY,EAAE,IAAI,EACnB;gBACE,MAAM,QAAQ,IAAI;gBAClB,aAAa,QAAQ,UAAU;gBAC/B,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,MAAM,QAAQ,IAAI;gBAClB,SAAS,QAAQ,OAAO;gBACxB,UAAU,QAAQ,OAAO;gBACzB,cAAc,QAAQ,WAAW;YACnC;YAEF,OAAO;QACT;IACF;AACF;AAEA,2BAA2B;AAC3B,IAAI,WAAW;IACb,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,CAAC,WAAW,EAAE,QAAQ,UAAU,CAAC,SAAS,CAAC,EAC3C;gBACE,cAAc,QAAQ,YAAY;gBAClC,OAAO,QAAQ,KAAK;gBACpB,YAAY,QAAQ,SAAS;gBAC7B,WAAW,QAAQ,QAAQ;YAC7B,GACA;YAEF,OAAO;QACT;IACF;IACA,KAAK,OAAO,EAAE;QACZ,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,WAAW,EAAE,QAAQ,UAAU,CAAC,SAAS,CAAC;YAE7C,OAAO;QACT;IACF;IACA,IAAI,OAAO,EAAE;QACX,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,EAAE;gBACjC,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,WAAW,EAAE,QAAQ,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,WAAW,OAAO,KAAK,IAAI,QAAQ,EAAE,EAAE;YAE9K,OAAO;QACT;IACF;IACA,OAAO,OAAO,EAAE;QACd,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,EAAE;gBACjC,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAClC,CAAC,WAAW,EAAE,QAAQ,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,WAAW,OAAO,KAAK,IAAI,QAAQ,EAAE,EAAE,EAC5K;gBACE,cAAc,QAAQ,YAAY;gBAClC,YAAY,QAAQ,SAAS;gBAC7B,WAAW,QAAQ,QAAQ;YAC7B;YAEF,OAAO;QACT;IACF;IACA,OAAO,OAAO,EAAE;QACd,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,KAAK,EAAE;gBACjC,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,SAAS;wBACT,MAAM;oBACR;gBACF;YACF;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC,WAAW,EAAE,QAAQ,UAAU,CAAC,UAAU,EAAE,CAAC,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,IAAI,WAAW,OAAO,KAAK,IAAI,QAAQ,KAAK,GAAG,WAAW,OAAO,KAAK,IAAI,QAAQ,EAAE,EAAE;YAE9K,OAAO;QACT;IACF;AACF;AAEA,kDAAkD;AAClD,SAAS,wBAAwB,MAAM;IACrC,OAAO;QACL,MAAM,OAAO,IAAI;QACjB,QAAQ,OAAO,MAAM;QACrB,oBAAoB,OAAO,gBAAgB;IAC7C;AACF;AAEA,yBAAyB;AACzB,IAAI,UAAU;IACZ,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,YACA,wBAAwB,UACxB;YAEF,OAAO;QACT;IACF;IACA,OAAO;QACL,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;YACnC,OAAO;QACT;IACF;IACA,IAAI,EAAE,EAAE;QACN,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,SAAS,EAAE,IAAI;YAElB,OAAO;QACT;IACF;IACA,OAAO,OAAO,EAAE;QACd,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAClC,CAAC,SAAS,EAAE,QAAQ,EAAE,EAAE,EACxB;gBACE,gBAAgB,QAAQ,aAAa;gBACrC,eAAe,QAAQ,YAAY;gBACnC,KAAK,QAAQ,GAAG;YAClB;YAEF,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CACnC,CAAC,SAAS,EAAE,IAAI;YAElB,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,CAAC,SAAS,EAAE,GAAG,OAAO,CAAC;YAEzB,OAAO;QACT;IACF;AACF;AAEA,uBAAuB;AACvB,IAAI,SAAS;IACX,YAAY,MAAM,CAAE;QAClB,IAAI,CAAC,MAAM,GAAG;IAChB;IACA,KAAK,EAAE,EAAE;QACP,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,OAAO,IAAI,CAAC,MAAM,CAAC,SAAS;QAC9B;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,OAAO,EAAE,UAAU,CAAC,CAAC;YAC9D,IAAI,QAAQ,KAAK,EAAE;gBACjB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE;oBACrB,IAAI;wBACF,MAAM,EAAE,WAAW,EAAE,GAAG;wBACxB,IAAI,CAAC,WAAW,GAAG;oBACrB,EAAE,OAAO,OAAO;wBACd,MAAM,IAAI,MACR;oBAEJ;gBACF;gBACA,QAAQ,IAAI,GAAG,MAAM,IAAI,CAAC,WAAW,CACnC,QAAQ,KAAK;YAEjB;YACA,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,WACA,uBAAuB,UACvB;YAEF,OAAO;QACT;IACF;IACA,IAAI,EAAE,EAAE;QACN,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAChC,CAAC,QAAQ,EAAE,IAAI;YAEjB,OAAO;QACT;IACF;IACA,OAAO,OAAO,EAAE;QACd,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,KAAK,CAClC,CAAC,QAAQ,EAAE,QAAQ,EAAE,EAAE,EACvB;gBACE,cAAc,QAAQ,WAAW;YACnC;YAEF,OAAO;QACT;IACF;IACA,OAAO,EAAE,EAAE;QACT,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,OAAO,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACjC,CAAC,QAAQ,EAAE,GAAG,OAAO,CAAC;YAExB,OAAO;QACT;IACF;AACF;AAEA,gBAAgB;AAChB,IAAI,iBAAiB;AACrB,IAAI,mBAAmB,CAAC,YAAY,EAAE,SAAS;AAC/C,IAAI,UAAU,OAAO,YAAY,eAAe,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,eAAe,IAAI,iBAAiB;AAC9G,IAAI,YAAY,OAAO,YAAY,eAAe,QAAQ,GAAG,GAAG,QAAQ,GAAG,CAAC,iBAAiB,IAAI,mBAAmB;AACpH,IAAI,SAAS;IACX,YAAY,GAAG,CAAE;QACf,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,IAAI;QAC/B,IAAI,CAAC,SAAS,GAAG,IAAI,UAAU,IAAI;QACnC,IAAI,CAAC,KAAK,GAAG,IAAI,MAAM,IAAI;QAC3B,IAAI,CAAC,UAAU,GAAG,IAAI,WAAW,IAAI;QACrC,IAAI,CAAC,QAAQ,GAAG,IAAI,SAAS,IAAI;QACjC,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ,IAAI;QAC/B,IAAI,CAAC,MAAM,GAAG,IAAI,OAAO,IAAI;QAC7B,IAAI,CAAC,KAAK;YACR,IAAI,OAAO,YAAY,eAAe,QAAQ,GAAG,EAAE;gBACjD,IAAI,CAAC,GAAG,GAAG,QAAQ,GAAG,CAAC,cAAc;YACvC;YACA,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;gBACb,MAAM,IAAI,MACR;YAEJ;QACF;QACA,IAAI,CAAC,OAAO,GAAG,IAAI,QAAQ;YACzB,eAAe,CAAC,OAAO,EAAE,IAAI,CAAC,GAAG,EAAE;YACnC,cAAc;YACd,gBAAgB;QAClB;IACF;IACA,aAAa,EAAE,EAAE;QACf,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,IAAI,EAAE,UAAU,CAAC,CAAC;YAC3D,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,GAAG,UAAU,MAAM,EAAE;gBAClD,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,IAAI;wBACF,MAAM,WAAW,MAAM,SAAS,IAAI;wBACpC,OAAO;4BAAE,MAAM;4BAAM,OAAO,KAAK,KAAK,CAAC;wBAAU;oBACnD,EAAE,OAAO,KAAK;wBACZ,IAAI,eAAe,aAAa;4BAC9B,OAAO;gCACL,MAAM;gCACN,OAAO;oCACL,MAAM;oCACN,SAAS;gCACX;4BACF;wBACF;wBACA,MAAM,QAAQ;4BACZ,SAAS,SAAS,UAAU;4BAC5B,MAAM;wBACR;wBACA,IAAI,eAAe,OAAO;4BACxB,OAAO;gCAAE,MAAM;gCAAM,OAAO,cAAc,eAAe,CAAC,GAAG,QAAQ;oCAAE,SAAS,IAAI,OAAO;gCAAC;4BAAG;wBACjG;wBACA,OAAO;4BAAE,MAAM;4BAAM;wBAAM;oBAC7B;gBACF;gBACA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,OAAO;oBAAE;oBAAM,OAAO;gBAAK;YAC7B,EAAE,OAAO,OAAO;gBACd,OAAO;oBACL,MAAM;oBACN,OAAO;wBACL,MAAM;wBACN,SAAS;oBACX;gBACF;YACF;QACF;IACF;IACA,KAAK,EAAE,EAAE,EAAE,EAAE;QACX,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,MAAM,UAAU,IAAI,QAAQ,IAAI,CAAC,OAAO;YACxC,IAAI,QAAQ,cAAc,EAAE;gBAC1B,QAAQ,GAAG,CAAC,mBAAmB,QAAQ,cAAc;YACvD;YACA,MAAM,iBAAiB,eAAe;gBACpC,QAAQ;gBACR;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB,GAAG;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;IACA,IAAI,EAAE,EAAE;QACN,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,IAAI,EAAE,UAAU,CAAC,CAAC;YAC3D,MAAM,iBAAiB,eAAe;gBACpC,QAAQ;gBACR,SAAS,IAAI,CAAC,OAAO;YACvB,GAAG;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;IACA,IAAI,EAAE,EAAE,EAAE,EAAE;QACV,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,MAAM,iBAAiB,eAAe;gBACpC,QAAQ;gBACR,SAAS,IAAI,CAAC,OAAO;gBACrB,MAAM,KAAK,SAAS,CAAC;YACvB,GAAG;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;IACA,MAAM,EAAE,EAAE,EAAE,EAAE;QACZ,OAAO,QAAQ,IAAI,EAAE,WAAW,UAAW,IAAI,EAAE,MAAM,EAAE,UAAU,CAAC,CAAC;YACnE,MAAM,iBAAiB,eAAe;gBACpC,QAAQ;gBACR,SAAS,IAAI,CAAC,OAAO;gBACrB,MAAM,KAAK,SAAS,CAAC;YACvB,GAAG;YACH,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;IACA,OAAO,IAAI,EAAE,KAAK,EAAE;QAClB,OAAO,QAAQ,IAAI,EAAE,MAAM;YACzB,MAAM,iBAAiB;gBACrB,QAAQ;gBACR,SAAS,IAAI,CAAC,OAAO;gBACrB,MAAM,KAAK,SAAS,CAAC;YACvB;YACA,OAAO,IAAI,CAAC,YAAY,CAAC,MAAM;QACjC;IACF;AACF", "ignoreList": [0], "debugId": null}}]}