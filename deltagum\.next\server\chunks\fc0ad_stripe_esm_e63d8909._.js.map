{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/crypto/CryptoProvider.js"], "sourcesContent": ["/**\n * Interface encapsulating the various crypto computations used by the library,\n * allowing pluggable underlying crypto implementations.\n */\nexport class Crypto<PERSON>rovider {\n    /**\n     * Computes a SHA-256 HMAC given a secret and a payload (encoded in UTF-8).\n     * The output HMAC should be encoded in hexadecimal.\n     *\n     * Sample values for implementations:\n     * - computeHMACSignature('', 'test_secret') => 'f7f9bd47fb987337b5796fdc1fdb9ba221d0d5396814bfcaf9521f43fd8927fd'\n     * - computeHMACSignature('\\ud83d\\ude00', 'test_secret') => '837da296d05c4fe31f61d5d7ead035099d9585a5bcde87de952012a78f0b0c43\n     */\n    computeHMACSignature(payload, secret) {\n        throw new Error('computeHMACSignature not implemented.');\n    }\n    /**\n     * Asynchronous version of `computeHMACSignature`. Some implementations may\n     * only allow support async signature computation.\n     *\n     * Computes a SHA-256 HMAC given a secret and a payload (encoded in UTF-8).\n     * The output HMAC should be encoded in hexadecimal.\n     *\n     * Sample values for implementations:\n     * - computeHMACSignature('', 'test_secret') => 'f7f9bd47fb987337b5796fdc1fdb9ba221d0d5396814bfcaf9521f43fd8927fd'\n     * - computeHMACSignature('\\ud83d\\ude00', 'test_secret') => '837da296d05c4fe31f61d5d7ead035099d9585a5bcde87de952012a78f0b0c43\n     */\n    computeHMACSignatureAsync(payload, secret) {\n        throw new Error('computeHMACSignatureAsync not implemented.');\n    }\n    /**\n     * Computes a SHA-256 hash of the data.\n     */\n    computeSHA256Async(data) {\n        throw new Error('computeSHA256 not implemented.');\n    }\n}\n/**\n * If the crypto provider only supports asynchronous operations,\n * throw CryptoProviderOnlySupportsAsyncError instead of\n * a generic error so that the caller can choose to provide\n * a more helpful error message to direct the user to use\n * an asynchronous pathway.\n */\nexport class CryptoProviderOnlySupportsAsyncError extends Error {\n}\n"], "names": [], "mappings": "AAAA;;;CAGC;;;;AACM,MAAM;IACT;;;;;;;KAOC,GACD,qBAAqB,OAAO,EAAE,MAAM,EAAE;QAClC,MAAM,IAAI,MAAM;IACpB;IACA;;;;;;;;;;KAUC,GACD,0BAA0B,OAAO,EAAE,MAAM,EAAE;QACvC,MAAM,IAAI,MAAM;IACpB;IACA;;KAEC,GACD,mBAAmB,IAAI,EAAE;QACrB,MAAM,IAAI,MAAM;IACpB;AACJ;AAQO,MAAM,6CAA6C;AAC1D", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 52, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/crypto/NodeCryptoProvider.js"], "sourcesContent": ["import * as crypto from 'crypto';\nimport { CryptoProvider } from './CryptoProvider.js';\n/**\n * `CryptoProvider which uses the Node `crypto` package for its computations.\n */\nexport class NodeCryptoProvider extends CryptoProvider {\n    /** @override */\n    computeHMACSignature(payload, secret) {\n        return crypto\n            .createHmac('sha256', secret)\n            .update(payload, 'utf8')\n            .digest('hex');\n    }\n    /** @override */\n    async computeHMACSignatureAsync(payload, secret) {\n        const signature = await this.computeHMACSignature(payload, secret);\n        return signature;\n    }\n    /** @override */\n    async computeSHA256Async(data) {\n        return new Uint8Array(await crypto\n            .createHash('sha256')\n            .update(data)\n            .digest());\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAIO,MAAM,2BAA2B,kPAAA,CAAA,iBAAc;IAClD,cAAc,GACd,qBAAqB,OAAO,EAAE,MAAM,EAAE;QAClC,OAAO,CAAA,GAAA,qGAAA,CAAA,aACQ,AAAD,EAAE,UAAU,QACrB,MAAM,CAAC,SAAS,QAChB,MAAM,CAAC;IAChB;IACA,cAAc,GACd,MAAM,0BAA0B,OAAO,EAAE,MAAM,EAAE;QAC7C,MAAM,YAAY,MAAM,IAAI,CAAC,oBAAoB,CAAC,SAAS;QAC3D,OAAO;IACX;IACA,cAAc,GACd,MAAM,mBAAmB,IAAI,EAAE;QAC3B,OAAO,IAAI,WAAW,MAAM,CAAA,GAAA,qGAAA,CAAA,aACb,AAAD,EAAE,UACX,MAAM,CAAC,MACP,MAAM;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 77, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/net/HttpClient.js"], "sourcesContent": ["/**\n * Encapsulates the logic for issuing a request to the Stripe API.\n *\n * A custom HTTP client should should implement:\n * 1. A response class which extends HttpClientResponse and wraps around their\n *    own internal representation of a response.\n * 2. A client class which extends HttpClient and implements all methods,\n *    returning their own response class when making requests.\n */\nexport class HttpClient {\n    /** The client name used for diagnostics. */\n    getClientName() {\n        throw new Error('getClientName not implemented.');\n    }\n    makeRequest(host, port, path, method, headers, requestData, protocol, timeout) {\n        throw new Error('makeRequest not implemented.');\n    }\n    /** Helper to make a consistent timeout error across implementations. */\n    static makeTimeoutError() {\n        const timeoutErr = new TypeError(HttpClient.TIMEOUT_ERROR_CODE);\n        timeoutErr.code = HttpClient.TIMEOUT_ERROR_CODE;\n        return timeoutErr;\n    }\n}\n// Public API accessible via Stripe.HttpClient\nHttpClient.CONNECTION_CLOSED_ERROR_CODES = ['ECONNRESET', 'EPIPE'];\nHttpClient.TIMEOUT_ERROR_CODE = 'ETIMEDOUT';\nexport class HttpClientResponse {\n    constructor(statusCode, headers) {\n        this._statusCode = statusCode;\n        this._headers = headers;\n    }\n    getStatusCode() {\n        return this._statusCode;\n    }\n    getHeaders() {\n        return this._headers;\n    }\n    getRawResponse() {\n        throw new Error('getRawResponse not implemented.');\n    }\n    toStream(streamCompleteCallback) {\n        throw new Error('toStream not implemented.');\n    }\n    toJSON() {\n        throw new Error('toJSON not implemented.');\n    }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC;;;;AACM,MAAM;IACT,0CAA0C,GAC1C,gBAAgB;QACZ,MAAM,IAAI,MAAM;IACpB;IACA,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC3E,MAAM,IAAI,MAAM;IACpB;IACA,sEAAsE,GACtE,OAAO,mBAAmB;QACtB,MAAM,aAAa,IAAI,UAAU,WAAW,kBAAkB;QAC9D,WAAW,IAAI,GAAG,WAAW,kBAAkB;QAC/C,OAAO;IACX;AACJ;AACA,8CAA8C;AAC9C,WAAW,6BAA6B,GAAG;IAAC;IAAc;CAAQ;AAClE,WAAW,kBAAkB,GAAG;AACzB,MAAM;IACT,YAAY,UAAU,EAAE,OAAO,CAAE;QAC7B,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,QAAQ,GAAG;IACpB;IACA,gBAAgB;QACZ,OAAO,IAAI,CAAC,WAAW;IAC3B;IACA,aAAa;QACT,OAAO,IAAI,CAAC,QAAQ;IACxB;IACA,iBAAiB;QACb,MAAM,IAAI,MAAM;IACpB;IACA,SAAS,sBAAsB,EAAE;QAC7B,MAAM,IAAI,MAAM;IACpB;IACA,SAAS;QACL,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/net/NodeHttpClient.js"], "sourcesContent": ["import * as http_ from 'http';\nimport * as https_ from 'https';\nimport { HttpClient, HttpClientResponse, } from './HttpClient.js';\n// `import * as http_ from 'http'` creates a \"Module Namespace Exotic Object\"\n// which is immune to monkey-patching, whereas http_.default (in an ES Module context)\n// will resolve to the same thing as require('http'), which is\n// monkey-patchable. We care about this because users in their test\n// suites might be using a library like \"nock\" which relies on the ability\n// to monkey-patch and intercept calls to http.request.\nconst http = http_.default || http_;\nconst https = https_.default || https_;\nconst defaultHttpAgent = new http.Agent({ keepAlive: true });\nconst defaultHttpsAgent = new https.Agent({ keepAlive: true });\n/**\n * HTTP client which uses the Node `http` and `https` packages to issue\n * requests.`\n */\nexport class NodeHttpClient extends HttpClient {\n    constructor(agent) {\n        super();\n        this._agent = agent;\n    }\n    /** @override. */\n    getClientName() {\n        return 'node';\n    }\n    makeRequest(host, port, path, method, headers, requestData, protocol, timeout) {\n        const isInsecureConnection = protocol === 'http';\n        let agent = this._agent;\n        if (!agent) {\n            agent = isInsecureConnection ? defaultHttpAgent : defaultHttpsAgent;\n        }\n        const requestPromise = new Promise((resolve, reject) => {\n            const req = (isInsecureConnection ? http : https).request({\n                host: host,\n                port: port,\n                path,\n                method,\n                agent,\n                headers,\n                ciphers: 'DEFAULT:!aNULL:!eNULL:!LOW:!EXPORT:!SSLv2:!MD5',\n            });\n            req.setTimeout(timeout, () => {\n                req.destroy(HttpClient.makeTimeoutError());\n            });\n            req.on('response', (res) => {\n                resolve(new NodeHttpClientResponse(res));\n            });\n            req.on('error', (error) => {\n                reject(error);\n            });\n            req.once('socket', (socket) => {\n                if (socket.connecting) {\n                    socket.once(isInsecureConnection ? 'connect' : 'secureConnect', () => {\n                        // Send payload; we're safe:\n                        req.write(requestData);\n                        req.end();\n                    });\n                }\n                else {\n                    // we're already connected\n                    req.write(requestData);\n                    req.end();\n                }\n            });\n        });\n        return requestPromise;\n    }\n}\nexport class NodeHttpClientResponse extends HttpClientResponse {\n    constructor(res) {\n        // @ts-ignore\n        super(res.statusCode, res.headers || {});\n        this._res = res;\n    }\n    getRawResponse() {\n        return this._res;\n    }\n    toStream(streamCompleteCallback) {\n        // The raw response is itself the stream, so we just return that. To be\n        // backwards compatible, we should invoke the streamCompleteCallback only\n        // once the stream has been fully consumed.\n        this._res.once('end', () => streamCompleteCallback());\n        return this._res;\n    }\n    toJSON() {\n        return new Promise((resolve, reject) => {\n            let response = '';\n            this._res.setEncoding('utf8');\n            this._res.on('data', (chunk) => {\n                response += chunk;\n            });\n            this._res.once('end', () => {\n                try {\n                    resolve(JSON.parse(response));\n                }\n                catch (e) {\n                    reject(e);\n                }\n            });\n        });\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;AACA,6EAA6E;AAC7E,sFAAsF;AACtF,8DAA8D;AAC9D,mEAAmE;AACnE,0EAA0E;AAC1E,uDAAuD;AACvD,MAAM,OAAO,kGAAM,OAAO,IAAI;AAC9B,MAAM,QAAQ,oGAAO,OAAO,IAAI;AAChC,MAAM,mBAAmB,IAAI,KAAK,KAAK,CAAC;IAAE,WAAW;AAAK;AAC1D,MAAM,oBAAoB,IAAI,MAAM,KAAK,CAAC;IAAE,WAAW;AAAK;AAKrD,MAAM,uBAAuB,2OAAA,CAAA,aAAU;IAC1C,YAAY,KAAK,CAAE;QACf,KAAK;QACL,IAAI,CAAC,MAAM,GAAG;IAClB;IACA,eAAe,GACf,gBAAgB;QACZ,OAAO;IACX;IACA,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC3E,MAAM,uBAAuB,aAAa;QAC1C,IAAI,QAAQ,IAAI,CAAC,MAAM;QACvB,IAAI,CAAC,OAAO;YACR,QAAQ,uBAAuB,mBAAmB;QACtD;QACA,MAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS;YACzC,MAAM,MAAM,CAAC,uBAAuB,OAAO,KAAK,EAAE,OAAO,CAAC;gBACtD,MAAM;gBACN,MAAM;gBACN;gBACA;gBACA;gBACA;gBACA,SAAS;YACb;YACA,IAAI,UAAU,CAAC,SAAS;gBACpB,IAAI,OAAO,CAAC,2OAAA,CAAA,aAAU,CAAC,gBAAgB;YAC3C;YACA,IAAI,EAAE,CAAC,YAAY,CAAC;gBAChB,QAAQ,IAAI,uBAAuB;YACvC;YACA,IAAI,EAAE,CAAC,SAAS,CAAC;gBACb,OAAO;YACX;YACA,IAAI,IAAI,CAAC,UAAU,CAAC;gBAChB,IAAI,OAAO,UAAU,EAAE;oBACnB,OAAO,IAAI,CAAC,uBAAuB,YAAY,iBAAiB;wBAC5D,4BAA4B;wBAC5B,IAAI,KAAK,CAAC;wBACV,IAAI,GAAG;oBACX;gBACJ,OACK;oBACD,0BAA0B;oBAC1B,IAAI,KAAK,CAAC;oBACV,IAAI,GAAG;gBACX;YACJ;QACJ;QACA,OAAO;IACX;AACJ;AACO,MAAM,+BAA+B,2OAAA,CAAA,qBAAkB;IAC1D,YAAY,GAAG,CAAE;QACb,aAAa;QACb,KAAK,CAAC,IAAI,UAAU,EAAE,IAAI,OAAO,IAAI,CAAC;QACtC,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,SAAS,sBAAsB,EAAE;QAC7B,uEAAuE;QACvE,yEAAyE;QACzE,2CAA2C;QAC3C,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,IAAM;QAC5B,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,SAAS;QACL,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI,WAAW;YACf,IAAI,CAAC,IAAI,CAAC,WAAW,CAAC;YACtB,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,QAAQ,CAAC;gBAClB,YAAY;YAChB;YACA,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO;gBAClB,IAAI;oBACA,QAAQ,KAAK,KAAK,CAAC;gBACvB,EACA,OAAO,GAAG;oBACN,OAAO;gBACX;YACJ;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 248, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/utils.js"], "sourcesContent": ["import * as qs from 'qs';\nconst OPTIONS_KEYS = [\n    'apiKey',\n    'idempotencyKey',\n    'stripeAccount',\n    'apiVersion',\n    'maxNetworkRetries',\n    'timeout',\n    'host',\n    'authenticator',\n    'stripeContext',\n    'additionalHeaders',\n    'streaming',\n];\nexport function isOptionsHash(o) {\n    return (o &&\n        typeof o === 'object' &&\n        OPTIONS_KEYS.some((prop) => Object.prototype.hasOwnProperty.call(o, prop)));\n}\n/**\n * Stringifies an Object, accommodating nested objects\n * (forming the conventional key 'parent[child]=value')\n */\nexport function queryStringifyRequestData(data, apiMode) {\n    return (qs\n        .stringify(data, {\n        serializeDate: (d) => Math.floor(d.getTime() / 1000).toString(),\n        arrayFormat: apiMode == 'v2' ? 'repeat' : 'indices',\n    })\n        // Don't use strict form encoding by changing the square bracket control\n        // characters back to their literals. This is fine by the server, and\n        // makes these parameter strings easier to read.\n        .replace(/%5B/g, '[')\n        .replace(/%5D/g, ']'));\n}\n/**\n * Outputs a new function with interpolated object property values.\n * Use like so:\n *   const fn = makeURLInterpolator('some/url/{param1}/{param2}');\n *   fn({ param1: 123, param2: 456 }); // => 'some/url/123/456'\n */\nexport const makeURLInterpolator = (() => {\n    const rc = {\n        '\\n': '\\\\n',\n        '\"': '\\\\\"',\n        '\\u2028': '\\\\u2028',\n        '\\u2029': '\\\\u2029',\n    };\n    return (str) => {\n        const cleanString = str.replace(/[\"\\n\\r\\u2028\\u2029]/g, ($0) => rc[$0]);\n        return (outputs) => {\n            return cleanString.replace(/\\{([\\s\\S]+?)\\}/g, ($0, $1) => {\n                const output = outputs[$1];\n                if (isValidEncodeUriComponentType(output))\n                    return encodeURIComponent(output);\n                return '';\n            });\n        };\n    };\n})();\nfunction isValidEncodeUriComponentType(value) {\n    return ['number', 'string', 'boolean'].includes(typeof value);\n}\nexport function extractUrlParams(path) {\n    const params = path.match(/\\{\\w+\\}/g);\n    if (!params) {\n        return [];\n    }\n    return params.map((param) => param.replace(/[{}]/g, ''));\n}\n/**\n * Return the data argument from a list of arguments\n *\n * @param {object[]} args\n * @returns {object}\n */\nexport function getDataFromArgs(args) {\n    if (!Array.isArray(args) || !args[0] || typeof args[0] !== 'object') {\n        return {};\n    }\n    if (!isOptionsHash(args[0])) {\n        return args.shift();\n    }\n    const argKeys = Object.keys(args[0]);\n    const optionKeysInArgs = argKeys.filter((key) => OPTIONS_KEYS.includes(key));\n    // In some cases options may be the provided as the first argument.\n    // Here we're detecting a case where there are two distinct arguments\n    // (the first being args and the second options) and with known\n    // option keys in the first so that we can warn the user about it.\n    if (optionKeysInArgs.length > 0 &&\n        optionKeysInArgs.length !== argKeys.length) {\n        emitWarning(`Options found in arguments (${optionKeysInArgs.join(', ')}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options.`);\n    }\n    return {};\n}\n/**\n * Return the options hash from a list of arguments\n */\nexport function getOptionsFromArgs(args) {\n    const opts = {\n        host: null,\n        headers: {},\n        settings: {},\n        streaming: false,\n    };\n    if (args.length > 0) {\n        const arg = args[args.length - 1];\n        if (typeof arg === 'string') {\n            opts.authenticator = createApiKeyAuthenticator(args.pop());\n        }\n        else if (isOptionsHash(arg)) {\n            const params = Object.assign({}, args.pop());\n            const extraKeys = Object.keys(params).filter((key) => !OPTIONS_KEYS.includes(key));\n            if (extraKeys.length) {\n                emitWarning(`Invalid options found (${extraKeys.join(', ')}); ignoring.`);\n            }\n            if (params.apiKey) {\n                opts.authenticator = createApiKeyAuthenticator(params.apiKey);\n            }\n            if (params.idempotencyKey) {\n                opts.headers['Idempotency-Key'] = params.idempotencyKey;\n            }\n            if (params.stripeAccount) {\n                opts.headers['Stripe-Account'] = params.stripeAccount;\n            }\n            if (params.stripeContext) {\n                if (opts.headers['Stripe-Account']) {\n                    throw new Error(\"Can't specify both stripeAccount and stripeContext.\");\n                }\n                opts.headers['Stripe-Context'] = params.stripeContext;\n            }\n            if (params.apiVersion) {\n                opts.headers['Stripe-Version'] = params.apiVersion;\n            }\n            if (Number.isInteger(params.maxNetworkRetries)) {\n                opts.settings.maxNetworkRetries = params.maxNetworkRetries;\n            }\n            if (Number.isInteger(params.timeout)) {\n                opts.settings.timeout = params.timeout;\n            }\n            if (params.host) {\n                opts.host = params.host;\n            }\n            if (params.authenticator) {\n                if (params.apiKey) {\n                    throw new Error(\"Can't specify both apiKey and authenticator.\");\n                }\n                if (typeof params.authenticator !== 'function') {\n                    throw new Error('The authenticator must be a function ' +\n                        'receiving a request as the first parameter.');\n                }\n                opts.authenticator = params.authenticator;\n            }\n            if (params.additionalHeaders) {\n                opts.headers = params.additionalHeaders;\n            }\n            if (params.streaming) {\n                opts.streaming = true;\n            }\n        }\n    }\n    return opts;\n}\n/**\n * Provide simple \"Class\" extension mechanism.\n * <!-- Public API accessible via Stripe.StripeResource.extend -->\n */\nexport function protoExtend(sub) {\n    // eslint-disable-next-line @typescript-eslint/no-this-alias\n    const Super = this;\n    const Constructor = Object.prototype.hasOwnProperty.call(sub, 'constructor')\n        ? sub.constructor\n        : function (...args) {\n            Super.apply(this, args);\n        };\n    // This initialization logic is somewhat sensitive to be compatible with\n    // divergent JS implementations like the one found in Qt. See here for more\n    // context:\n    //\n    // https://github.com/stripe/stripe-node/pull/334\n    Object.assign(Constructor, Super);\n    Constructor.prototype = Object.create(Super.prototype);\n    Object.assign(Constructor.prototype, sub);\n    return Constructor;\n}\n/**\n * Remove empty values from an object\n */\nexport function removeNullish(obj) {\n    if (typeof obj !== 'object') {\n        throw new Error('Argument must be an object');\n    }\n    return Object.keys(obj).reduce((result, key) => {\n        if (obj[key] != null) {\n            result[key] = obj[key];\n        }\n        return result;\n    }, {});\n}\n/**\n * Normalize standard HTTP Headers:\n * {'foo-bar': 'hi'}\n * becomes\n * {'Foo-Bar': 'hi'}\n */\nexport function normalizeHeaders(obj) {\n    if (!(obj && typeof obj === 'object')) {\n        return obj;\n    }\n    return Object.keys(obj).reduce((result, header) => {\n        result[normalizeHeader(header)] = obj[header];\n        return result;\n    }, {});\n}\n/**\n * Stolen from https://github.com/marten-de-vries/header-case-normalizer/blob/master/index.js#L36-L41\n * without the exceptions which are irrelevant to us.\n */\nexport function normalizeHeader(header) {\n    return header\n        .split('-')\n        .map((text) => text.charAt(0).toUpperCase() + text.substr(1).toLowerCase())\n        .join('-');\n}\nexport function callbackifyPromiseWithTimeout(promise, callback) {\n    if (callback) {\n        // Ensure callback is called outside of promise stack.\n        return promise.then((res) => {\n            setTimeout(() => {\n                callback(null, res);\n            }, 0);\n        }, (err) => {\n            setTimeout(() => {\n                callback(err, null);\n            }, 0);\n        });\n    }\n    return promise;\n}\n/**\n * Allow for special capitalization cases (such as OAuth)\n */\nexport function pascalToCamelCase(name) {\n    if (name === 'OAuth') {\n        return 'oauth';\n    }\n    else {\n        return name[0].toLowerCase() + name.substring(1);\n    }\n}\nexport function emitWarning(warning) {\n    if (typeof process.emitWarning !== 'function') {\n        return console.warn(`Stripe: ${warning}`); /* eslint-disable-line no-console */\n    }\n    return process.emitWarning(warning, 'Stripe');\n}\nexport function isObject(obj) {\n    const type = typeof obj;\n    return (type === 'function' || type === 'object') && !!obj;\n}\n// For use in multipart requests\nexport function flattenAndStringify(data) {\n    const result = {};\n    const step = (obj, prevKey) => {\n        Object.entries(obj).forEach(([key, value]) => {\n            const newKey = prevKey ? `${prevKey}[${key}]` : key;\n            if (isObject(value)) {\n                if (!(value instanceof Uint8Array) &&\n                    !Object.prototype.hasOwnProperty.call(value, 'data')) {\n                    // Non-buffer non-file Objects are recursively flattened\n                    return step(value, newKey);\n                }\n                else {\n                    // Buffers and file objects are stored without modification\n                    result[newKey] = value;\n                }\n            }\n            else {\n                // Primitives are converted to strings\n                result[newKey] = String(value);\n            }\n        });\n    };\n    step(data, null);\n    return result;\n}\nexport function validateInteger(name, n, defaultVal) {\n    if (!Number.isInteger(n)) {\n        if (defaultVal !== undefined) {\n            return defaultVal;\n        }\n        else {\n            throw new Error(`${name} must be an integer`);\n        }\n    }\n    return n;\n}\nexport function determineProcessUserAgentProperties() {\n    return typeof process === 'undefined'\n        ? {}\n        : {\n            lang_version: process.version,\n            platform: process.platform,\n        };\n}\nexport function createApiKeyAuthenticator(apiKey) {\n    const authenticator = (request) => {\n        request.headers.Authorization = 'Bearer ' + apiKey;\n        return Promise.resolve();\n    };\n    // For testing\n    authenticator._apiKey = apiKey;\n    return authenticator;\n}\n/**\n * Joins an array of Uint8Arrays into a single Uint8Array\n */\nexport function concat(arrays) {\n    const totalLength = arrays.reduce((len, array) => len + array.length, 0);\n    const merged = new Uint8Array(totalLength);\n    let offset = 0;\n    arrays.forEach((array) => {\n        merged.set(array, offset);\n        offset += array.length;\n    });\n    return merged;\n}\n/**\n * Replaces Date objects with Unix timestamps\n */\nfunction dateTimeReplacer(key, value) {\n    if (this[key] instanceof Date) {\n        return Math.floor(this[key].getTime() / 1000).toString();\n    }\n    return value;\n}\n/**\n * JSON stringifies an Object, replacing Date objects with Unix timestamps\n */\nexport function jsonStringifyRequestData(data) {\n    return JSON.stringify(data, dateTimeReplacer);\n}\n/**\n * Inspects the given path to determine if the endpoint is for v1 or v2 API\n */\nexport function getAPIMode(path) {\n    if (!path) {\n        return 'v1';\n    }\n    return path.startsWith('/v2') ? 'v2' : 'v1';\n}\nexport function parseHttpHeaderAsString(header) {\n    if (Array.isArray(header)) {\n        return header.join(', ');\n    }\n    return String(header);\n}\nexport function parseHttpHeaderAsNumber(header) {\n    const number = Array.isArray(header) ? header[0] : header;\n    return Number(number);\n}\nexport function parseHeadersForFetch(headers) {\n    return Object.entries(headers).map(([key, value]) => {\n        return [key, parseHttpHeaderAsString(value)];\n    });\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;AACA,MAAM,eAAe;IACjB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACM,SAAS,cAAc,CAAC;IAC3B,OAAQ,KACJ,OAAO,MAAM,YACb,aAAa,IAAI,CAAC,CAAC,OAAS,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG;AAC5E;AAKO,SAAS,0BAA0B,IAAI,EAAE,OAAO;IACnD,OAAQ,CAAA,GAAA,oLAAA,CAAA,YACM,AAAD,EAAE,MAAM;QACjB,eAAe,CAAC,IAAM,KAAK,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,QAAQ;QAC7D,aAAa,WAAW,OAAO,WAAW;IAC9C,EACI,wEAAwE;IACxE,qEAAqE;IACrE,gDAAgD;KAC/C,OAAO,CAAC,QAAQ,KAChB,OAAO,CAAC,QAAQ;AACzB;AAOO,MAAM,sBAAsB,CAAC;IAChC,MAAM,KAAK;QACP,MAAM;QACN,KAAK;QACL,UAAU;QACV,UAAU;IACd;IACA,OAAO,CAAC;QACJ,MAAM,cAAc,IAAI,OAAO,CAAC,wBAAwB,CAAC,KAAO,EAAE,CAAC,GAAG;QACtE,OAAO,CAAC;YACJ,OAAO,YAAY,OAAO,CAAC,mBAAmB,CAAC,IAAI;gBAC/C,MAAM,SAAS,OAAO,CAAC,GAAG;gBAC1B,IAAI,8BAA8B,SAC9B,OAAO,mBAAmB;gBAC9B,OAAO;YACX;QACJ;IACJ;AACJ,CAAC;AACD,SAAS,8BAA8B,KAAK;IACxC,OAAO;QAAC;QAAU;QAAU;KAAU,CAAC,QAAQ,CAAC,OAAO;AAC3D;AACO,SAAS,iBAAiB,IAAI;IACjC,MAAM,SAAS,KAAK,KAAK,CAAC;IAC1B,IAAI,CAAC,QAAQ;QACT,OAAO,EAAE;IACb;IACA,OAAO,OAAO,GAAG,CAAC,CAAC,QAAU,MAAM,OAAO,CAAC,SAAS;AACxD;AAOO,SAAS,gBAAgB,IAAI;IAChC,IAAI,CAAC,MAAM,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,EAAE,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UAAU;QACjE,OAAO,CAAC;IACZ;IACA,IAAI,CAAC,cAAc,IAAI,CAAC,EAAE,GAAG;QACzB,OAAO,KAAK,KAAK;IACrB;IACA,MAAM,UAAU,OAAO,IAAI,CAAC,IAAI,CAAC,EAAE;IACnC,MAAM,mBAAmB,QAAQ,MAAM,CAAC,CAAC,MAAQ,aAAa,QAAQ,CAAC;IACvE,mEAAmE;IACnE,qEAAqE;IACrE,+DAA+D;IAC/D,kEAAkE;IAClE,IAAI,iBAAiB,MAAM,GAAG,KAC1B,iBAAiB,MAAM,KAAK,QAAQ,MAAM,EAAE;QAC5C,YAAY,CAAC,4BAA4B,EAAE,iBAAiB,IAAI,CAAC,MAAM,0GAA0G,CAAC;IACtL;IACA,OAAO,CAAC;AACZ;AAIO,SAAS,mBAAmB,IAAI;IACnC,MAAM,OAAO;QACT,MAAM;QACN,SAAS,CAAC;QACV,UAAU,CAAC;QACX,WAAW;IACf;IACA,IAAI,KAAK,MAAM,GAAG,GAAG;QACjB,MAAM,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;QACjC,IAAI,OAAO,QAAQ,UAAU;YACzB,KAAK,aAAa,GAAG,0BAA0B,KAAK,GAAG;QAC3D,OACK,IAAI,cAAc,MAAM;YACzB,MAAM,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG;YACzC,MAAM,YAAY,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,MAAQ,CAAC,aAAa,QAAQ,CAAC;YAC7E,IAAI,UAAU,MAAM,EAAE;gBAClB,YAAY,CAAC,uBAAuB,EAAE,UAAU,IAAI,CAAC,MAAM,YAAY,CAAC;YAC5E;YACA,IAAI,OAAO,MAAM,EAAE;gBACf,KAAK,aAAa,GAAG,0BAA0B,OAAO,MAAM;YAChE;YACA,IAAI,OAAO,cAAc,EAAE;gBACvB,KAAK,OAAO,CAAC,kBAAkB,GAAG,OAAO,cAAc;YAC3D;YACA,IAAI,OAAO,aAAa,EAAE;gBACtB,KAAK,OAAO,CAAC,iBAAiB,GAAG,OAAO,aAAa;YACzD;YACA,IAAI,OAAO,aAAa,EAAE;gBACtB,IAAI,KAAK,OAAO,CAAC,iBAAiB,EAAE;oBAChC,MAAM,IAAI,MAAM;gBACpB;gBACA,KAAK,OAAO,CAAC,iBAAiB,GAAG,OAAO,aAAa;YACzD;YACA,IAAI,OAAO,UAAU,EAAE;gBACnB,KAAK,OAAO,CAAC,iBAAiB,GAAG,OAAO,UAAU;YACtD;YACA,IAAI,OAAO,SAAS,CAAC,OAAO,iBAAiB,GAAG;gBAC5C,KAAK,QAAQ,CAAC,iBAAiB,GAAG,OAAO,iBAAiB;YAC9D;YACA,IAAI,OAAO,SAAS,CAAC,OAAO,OAAO,GAAG;gBAClC,KAAK,QAAQ,CAAC,OAAO,GAAG,OAAO,OAAO;YAC1C;YACA,IAAI,OAAO,IAAI,EAAE;gBACb,KAAK,IAAI,GAAG,OAAO,IAAI;YAC3B;YACA,IAAI,OAAO,aAAa,EAAE;gBACtB,IAAI,OAAO,MAAM,EAAE;oBACf,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,OAAO,OAAO,aAAa,KAAK,YAAY;oBAC5C,MAAM,IAAI,MAAM,0CACZ;gBACR;gBACA,KAAK,aAAa,GAAG,OAAO,aAAa;YAC7C;YACA,IAAI,OAAO,iBAAiB,EAAE;gBAC1B,KAAK,OAAO,GAAG,OAAO,iBAAiB;YAC3C;YACA,IAAI,OAAO,SAAS,EAAE;gBAClB,KAAK,SAAS,GAAG;YACrB;QACJ;IACJ;IACA,OAAO;AACX;AAKO,SAAS,YAAY,GAAG;IAC3B,4DAA4D;IAC5D,MAAM,QAAQ,IAAI;IAClB,MAAM,cAAc,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,KAAK,iBACxD,IAAI,WAAW,GACf,SAAU,GAAG,IAAI;QACf,MAAM,KAAK,CAAC,IAAI,EAAE;IACtB;IACJ,wEAAwE;IACxE,2EAA2E;IAC3E,WAAW;IACX,EAAE;IACF,iDAAiD;IACjD,OAAO,MAAM,CAAC,aAAa;IAC3B,YAAY,SAAS,GAAG,OAAO,MAAM,CAAC,MAAM,SAAS;IACrD,OAAO,MAAM,CAAC,YAAY,SAAS,EAAE;IACrC,OAAO;AACX;AAIO,SAAS,cAAc,GAAG;IAC7B,IAAI,OAAO,QAAQ,UAAU;QACzB,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,QAAQ;QACpC,IAAI,GAAG,CAAC,IAAI,IAAI,MAAM;YAClB,MAAM,CAAC,IAAI,GAAG,GAAG,CAAC,IAAI;QAC1B;QACA,OAAO;IACX,GAAG,CAAC;AACR;AAOO,SAAS,iBAAiB,GAAG;IAChC,IAAI,CAAC,CAAC,OAAO,OAAO,QAAQ,QAAQ,GAAG;QACnC,OAAO;IACX;IACA,OAAO,OAAO,IAAI,CAAC,KAAK,MAAM,CAAC,CAAC,QAAQ;QACpC,MAAM,CAAC,gBAAgB,QAAQ,GAAG,GAAG,CAAC,OAAO;QAC7C,OAAO;IACX,GAAG,CAAC;AACR;AAKO,SAAS,gBAAgB,MAAM;IAClC,OAAO,OACF,KAAK,CAAC,KACN,GAAG,CAAC,CAAC,OAAS,KAAK,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,GAAG,WAAW,IACvE,IAAI,CAAC;AACd;AACO,SAAS,8BAA8B,OAAO,EAAE,QAAQ;IAC3D,IAAI,UAAU;QACV,sDAAsD;QACtD,OAAO,QAAQ,IAAI,CAAC,CAAC;YACjB,WAAW;gBACP,SAAS,MAAM;YACnB,GAAG;QACP,GAAG,CAAC;YACA,WAAW;gBACP,SAAS,KAAK;YAClB,GAAG;QACP;IACJ;IACA,OAAO;AACX;AAIO,SAAS,kBAAkB,IAAI;IAClC,IAAI,SAAS,SAAS;QAClB,OAAO;IACX,OACK;QACD,OAAO,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,SAAS,CAAC;IAClD;AACJ;AACO,SAAS,YAAY,OAAO;IAC/B,IAAI,OAAO,QAAQ,WAAW,KAAK,YAAY;QAC3C,OAAO,QAAQ,IAAI,CAAC,CAAC,QAAQ,EAAE,SAAS,GAAG,kCAAkC;IACjF;IACA,OAAO,QAAQ,WAAW,CAAC,SAAS;AACxC;AACO,SAAS,SAAS,GAAG;IACxB,MAAM,OAAO,OAAO;IACpB,OAAO,CAAC,SAAS,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC;AAC3D;AAEO,SAAS,oBAAoB,IAAI;IACpC,MAAM,SAAS,CAAC;IAChB,MAAM,OAAO,CAAC,KAAK;QACf,OAAO,OAAO,CAAC,KAAK,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YACrC,MAAM,SAAS,UAAU,GAAG,QAAQ,CAAC,EAAE,IAAI,CAAC,CAAC,GAAG;YAChD,IAAI,SAAS,QAAQ;gBACjB,IAAI,CAAC,CAAC,iBAAiB,UAAU,KAC7B,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,OAAO,SAAS;oBACtD,wDAAwD;oBACxD,OAAO,KAAK,OAAO;gBACvB,OACK;oBACD,2DAA2D;oBAC3D,MAAM,CAAC,OAAO,GAAG;gBACrB;YACJ,OACK;gBACD,sCAAsC;gBACtC,MAAM,CAAC,OAAO,GAAG,OAAO;YAC5B;QACJ;IACJ;IACA,KAAK,MAAM;IACX,OAAO;AACX;AACO,SAAS,gBAAgB,IAAI,EAAE,CAAC,EAAE,UAAU;IAC/C,IAAI,CAAC,OAAO,SAAS,CAAC,IAAI;QACtB,IAAI,eAAe,WAAW;YAC1B,OAAO;QACX,OACK;YACD,MAAM,IAAI,MAAM,GAAG,KAAK,mBAAmB,CAAC;QAChD;IACJ;IACA,OAAO;AACX;AACO,SAAS;IACZ,OAAO,OAAO,YAAY,cACpB,CAAC,IACD;QACE,cAAc,QAAQ,OAAO;QAC7B,UAAU,QAAQ,QAAQ;IAC9B;AACR;AACO,SAAS,0BAA0B,MAAM;IAC5C,MAAM,gBAAgB,CAAC;QACnB,QAAQ,OAAO,CAAC,aAAa,GAAG,YAAY;QAC5C,OAAO,QAAQ,OAAO;IAC1B;IACA,cAAc;IACd,cAAc,OAAO,GAAG;IACxB,OAAO;AACX;AAIO,SAAS,OAAO,MAAM;IACzB,MAAM,cAAc,OAAO,MAAM,CAAC,CAAC,KAAK,QAAU,MAAM,MAAM,MAAM,EAAE;IACtE,MAAM,SAAS,IAAI,WAAW;IAC9B,IAAI,SAAS;IACb,OAAO,OAAO,CAAC,CAAC;QACZ,OAAO,GAAG,CAAC,OAAO;QAClB,UAAU,MAAM,MAAM;IAC1B;IACA,OAAO;AACX;AACA;;CAEC,GACD,SAAS,iBAAiB,GAAG,EAAE,KAAK;IAChC,IAAI,IAAI,CAAC,IAAI,YAAY,MAAM;QAC3B,OAAO,KAAK,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,OAAO,KAAK,MAAM,QAAQ;IAC1D;IACA,OAAO;AACX;AAIO,SAAS,yBAAyB,IAAI;IACzC,OAAO,KAAK,SAAS,CAAC,MAAM;AAChC;AAIO,SAAS,WAAW,IAAI;IAC3B,IAAI,CAAC,MAAM;QACP,OAAO;IACX;IACA,OAAO,KAAK,UAAU,CAAC,SAAS,OAAO;AAC3C;AACO,SAAS,wBAAwB,MAAM;IAC1C,IAAI,MAAM,OAAO,CAAC,SAAS;QACvB,OAAO,OAAO,IAAI,CAAC;IACvB;IACA,OAAO,OAAO;AAClB;AACO,SAAS,wBAAwB,MAAM;IAC1C,MAAM,SAAS,MAAM,OAAO,CAAC,UAAU,MAAM,CAAC,EAAE,GAAG;IACnD,OAAO,OAAO;AAClB;AACO,SAAS,qBAAqB,OAAO;IACxC,OAAO,OAAO,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,KAAK,MAAM;QAC5C,OAAO;YAAC;YAAK,wBAAwB;SAAO;IAChD;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 583, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/net/FetchHttpClient.js"], "sourcesContent": ["import { parseHeadersForFetch } from '../utils.js';\nimport { HttpClient, HttpClientResponse, } from './HttpClient.js';\n/**\n * HTTP client which uses a `fetch` function to issue requests.\n *\n * By default relies on the global `fetch` function, but an optional function\n * can be passed in. If passing in a function, it is expected to match the Web\n * Fetch API. As an example, this could be the function provided by the\n * node-fetch package (https://github.com/node-fetch/node-fetch).\n */\nexport class FetchHttpClient extends HttpClient {\n    constructor(fetchFn) {\n        super();\n        // Default to global fetch if available\n        if (!fetchFn) {\n            if (!globalThis.fetch) {\n                throw new Error('fetch() function not provided and is not defined in the global scope. ' +\n                    'You must provide a fetch implementation.');\n            }\n            fetchFn = globalThis.fetch;\n        }\n        // Both timeout behaviors differs from Node:\n        // - <PERSON><PERSON> uses a single timeout for the entire length of the request.\n        // - Node is more fine-grained and resets the timeout after each stage of the request.\n        if (globalThis.AbortController) {\n            // Utilise native AbortController if available\n            // AbortController was added in Node v15.0.0, v14.17.0\n            this._fetchFn = FetchHttpClient.makeFetchWithAbortTimeout(fetchFn);\n        }\n        else {\n            // Fall back to racing against a timeout promise if not available in the runtime\n            // This does not actually cancel the underlying fetch operation or resources\n            this._fetchFn = FetchHttpClient.makeFetchWithRaceTimeout(fetchFn);\n        }\n    }\n    static makeFetchWithRaceTimeout(fetchFn) {\n        return (url, init, timeout) => {\n            let pendingTimeoutId;\n            const timeoutPromise = new Promise((_, reject) => {\n                pendingTimeoutId = setTimeout(() => {\n                    pendingTimeoutId = null;\n                    reject(HttpClient.makeTimeoutError());\n                }, timeout);\n            });\n            const fetchPromise = fetchFn(url, init);\n            return Promise.race([fetchPromise, timeoutPromise]).finally(() => {\n                if (pendingTimeoutId) {\n                    clearTimeout(pendingTimeoutId);\n                }\n            });\n        };\n    }\n    static makeFetchWithAbortTimeout(fetchFn) {\n        return async (url, init, timeout) => {\n            // Use AbortController because AbortSignal.timeout() was added later in Node v17.3.0, v16.14.0\n            const abort = new AbortController();\n            let timeoutId = setTimeout(() => {\n                timeoutId = null;\n                abort.abort(HttpClient.makeTimeoutError());\n            }, timeout);\n            try {\n                return await fetchFn(url, Object.assign(Object.assign({}, init), { signal: abort.signal }));\n            }\n            catch (err) {\n                // Some implementations, like node-fetch, do not respect the reason passed to AbortController.abort()\n                // and instead it always throws an AbortError\n                // We catch this case to normalise all timeout errors\n                if (err.name === 'AbortError') {\n                    throw HttpClient.makeTimeoutError();\n                }\n                else {\n                    throw err;\n                }\n            }\n            finally {\n                if (timeoutId) {\n                    clearTimeout(timeoutId);\n                }\n            }\n        };\n    }\n    /** @override. */\n    getClientName() {\n        return 'fetch';\n    }\n    async makeRequest(host, port, path, method, headers, requestData, protocol, timeout) {\n        const isInsecureConnection = protocol === 'http';\n        const url = new URL(path, `${isInsecureConnection ? 'http' : 'https'}://${host}`);\n        url.port = port;\n        // For methods which expect payloads, we should always pass a body value\n        // even when it is empty. Without this, some JS runtimes (eg. Deno) will\n        // inject a second Content-Length header. See https://github.com/stripe/stripe-node/issues/1519\n        // for more details.\n        const methodHasPayload = method == 'POST' || method == 'PUT' || method == 'PATCH';\n        const body = requestData || (methodHasPayload ? '' : undefined);\n        const res = await this._fetchFn(url.toString(), {\n            method,\n            headers: parseHeadersForFetch(headers),\n            body: typeof body === 'object' ? JSON.stringify(body) : body,\n        }, timeout);\n        return new FetchHttpClientResponse(res);\n    }\n}\nexport class FetchHttpClientResponse extends HttpClientResponse {\n    constructor(res) {\n        super(res.status, FetchHttpClientResponse._transformHeadersToObject(res.headers));\n        this._res = res;\n    }\n    getRawResponse() {\n        return this._res;\n    }\n    toStream(streamCompleteCallback) {\n        // Unfortunately `fetch` does not have event handlers for when the stream is\n        // completely read. We therefore invoke the streamCompleteCallback right\n        // away. This callback emits a response event with metadata and completes\n        // metrics, so it's ok to do this without waiting for the stream to be\n        // completely read.\n        streamCompleteCallback();\n        // Fetch's `body` property is expected to be a readable stream of the body.\n        return this._res.body;\n    }\n    toJSON() {\n        return this._res.json();\n    }\n    static _transformHeadersToObject(headers) {\n        // Fetch uses a Headers instance so this must be converted to a barebones\n        // JS object to meet the HttpClient interface.\n        const headersObj = {};\n        for (const entry of headers) {\n            if (!Array.isArray(entry) || entry.length != 2) {\n                throw new Error('Response objects produced by the fetch function given to FetchHttpClient do not have an iterable headers map. Response#headers should be an iterable object.');\n            }\n            headersObj[entry[0]] = entry[1];\n        }\n        return headersObj;\n    }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AASO,MAAM,wBAAwB,2OAAA,CAAA,aAAU;IAC3C,YAAY,OAAO,CAAE;QACjB,KAAK;QACL,uCAAuC;QACvC,IAAI,CAAC,SAAS;YACV,IAAI,CAAC,WAAW,KAAK,EAAE;gBACnB,MAAM,IAAI,MAAM,2EACZ;YACR;YACA,UAAU,WAAW,KAAK;QAC9B;QACA,4CAA4C;QAC5C,sEAAsE;QACtE,sFAAsF;QACtF,IAAI,WAAW,eAAe,EAAE;YAC5B,8CAA8C;YAC9C,sDAAsD;YACtD,IAAI,CAAC,QAAQ,GAAG,gBAAgB,yBAAyB,CAAC;QAC9D,OACK;YACD,gFAAgF;YAChF,4EAA4E;YAC5E,IAAI,CAAC,QAAQ,GAAG,gBAAgB,wBAAwB,CAAC;QAC7D;IACJ;IACA,OAAO,yBAAyB,OAAO,EAAE;QACrC,OAAO,CAAC,KAAK,MAAM;YACf,IAAI;YACJ,MAAM,iBAAiB,IAAI,QAAQ,CAAC,GAAG;gBACnC,mBAAmB,WAAW;oBAC1B,mBAAmB;oBACnB,OAAO,2OAAA,CAAA,aAAU,CAAC,gBAAgB;gBACtC,GAAG;YACP;YACA,MAAM,eAAe,QAAQ,KAAK;YAClC,OAAO,QAAQ,IAAI,CAAC;gBAAC;gBAAc;aAAe,EAAE,OAAO,CAAC;gBACxD,IAAI,kBAAkB;oBAClB,aAAa;gBACjB;YACJ;QACJ;IACJ;IACA,OAAO,0BAA0B,OAAO,EAAE;QACtC,OAAO,OAAO,KAAK,MAAM;YACrB,8FAA8F;YAC9F,MAAM,QAAQ,IAAI;YAClB,IAAI,YAAY,WAAW;gBACvB,YAAY;gBACZ,MAAM,KAAK,CAAC,2OAAA,CAAA,aAAU,CAAC,gBAAgB;YAC3C,GAAG;YACH,IAAI;gBACA,OAAO,MAAM,QAAQ,KAAK,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;oBAAE,QAAQ,MAAM,MAAM;gBAAC;YAC5F,EACA,OAAO,KAAK;gBACR,qGAAqG;gBACrG,6CAA6C;gBAC7C,qDAAqD;gBACrD,IAAI,IAAI,IAAI,KAAK,cAAc;oBAC3B,MAAM,2OAAA,CAAA,aAAU,CAAC,gBAAgB;gBACrC,OACK;oBACD,MAAM;gBACV;YACJ,SACQ;gBACJ,IAAI,WAAW;oBACX,aAAa;gBACjB;YACJ;QACJ;IACJ;IACA,eAAe,GACf,gBAAgB;QACZ,OAAO;IACX;IACA,MAAM,YAAY,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,QAAQ,EAAE,OAAO,EAAE;QACjF,MAAM,uBAAuB,aAAa;QAC1C,MAAM,MAAM,IAAI,IAAI,MAAM,GAAG,uBAAuB,SAAS,QAAQ,GAAG,EAAE,MAAM;QAChF,IAAI,IAAI,GAAG;QACX,wEAAwE;QACxE,wEAAwE;QACxE,+FAA+F;QAC/F,oBAAoB;QACpB,MAAM,mBAAmB,UAAU,UAAU,UAAU,SAAS,UAAU;QAC1E,MAAM,OAAO,eAAe,CAAC,mBAAmB,KAAK,SAAS;QAC9D,MAAM,MAAM,MAAM,IAAI,CAAC,QAAQ,CAAC,IAAI,QAAQ,IAAI;YAC5C;YACA,SAAS,CAAA,GAAA,+NAAA,CAAA,uBAAoB,AAAD,EAAE;YAC9B,MAAM,OAAO,SAAS,WAAW,KAAK,SAAS,CAAC,QAAQ;QAC5D,GAAG;QACH,OAAO,IAAI,wBAAwB;IACvC;AACJ;AACO,MAAM,gCAAgC,2OAAA,CAAA,qBAAkB;IAC3D,YAAY,GAAG,CAAE;QACb,KAAK,CAAC,IAAI,MAAM,EAAE,wBAAwB,yBAAyB,CAAC,IAAI,OAAO;QAC/E,IAAI,CAAC,IAAI,GAAG;IAChB;IACA,iBAAiB;QACb,OAAO,IAAI,CAAC,IAAI;IACpB;IACA,SAAS,sBAAsB,EAAE;QAC7B,4EAA4E;QAC5E,wEAAwE;QACxE,yEAAyE;QACzE,sEAAsE;QACtE,mBAAmB;QACnB;QACA,2EAA2E;QAC3E,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACzB;IACA,SAAS;QACL,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;IACzB;IACA,OAAO,0BAA0B,OAAO,EAAE;QACtC,yEAAyE;QACzE,8CAA8C;QAC9C,MAAM,aAAa,CAAC;QACpB,KAAK,MAAM,SAAS,QAAS;YACzB,IAAI,CAAC,MAAM,OAAO,CAAC,UAAU,MAAM,MAAM,IAAI,GAAG;gBAC5C,MAAM,IAAI,MAAM;YACpB;YACA,UAAU,CAAC,KAAK,CAAC,EAAE,CAAC,GAAG,KAAK,CAAC,EAAE;QACnC;QACA,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 723, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/crypto/SubtleCryptoProvider.js"], "sourcesContent": ["import { CryptoProvider, CryptoProviderOnlySupportsAsyncError, } from './CryptoProvider.js';\n/**\n * `CryptoProvider which uses the SubtleCrypto interface of the Web Crypto API.\n *\n * This only supports asynchronous operations.\n */\nexport class SubtleCryptoProvider extends CryptoProvider {\n    constructor(subtleCrypto) {\n        super();\n        // If no subtle crypto is interface, default to the global namespace. This\n        // is to allow custom interfaces (eg. using the Node webcrypto interface in\n        // tests).\n        this.subtleCrypto = subtleCrypto || crypto.subtle;\n    }\n    /** @override */\n    computeHMACSignature(payload, secret) {\n        throw new CryptoProviderOnlySupportsAsyncError('SubtleCryptoProvider cannot be used in a synchronous context.');\n    }\n    /** @override */\n    async computeHMACSignatureAsync(payload, secret) {\n        const encoder = new TextEncoder();\n        const key = await this.subtleCrypto.importKey('raw', encoder.encode(secret), {\n            name: 'HM<PERSON>',\n            hash: { name: 'SHA-256' },\n        }, false, ['sign']);\n        const signatureBuffer = await this.subtleCrypto.sign('hmac', key, encoder.encode(payload));\n        // crypto.subtle returns the signature in base64 format. This must be\n        // encoded in hex to match the CryptoProvider contract. We map each byte in\n        // the buffer to its corresponding hex octet and then combine into a string.\n        const signatureBytes = new Uint8Array(signatureBuffer);\n        const signatureHexCodes = new Array(signatureBytes.length);\n        for (let i = 0; i < signatureBytes.length; i++) {\n            signatureHexCodes[i] = byteHexMapping[signatureBytes[i]];\n        }\n        return signatureHexCodes.join('');\n    }\n    /** @override */\n    async computeSHA256Async(data) {\n        return new Uint8Array(await this.subtleCrypto.digest('SHA-256', data));\n    }\n}\n// Cached mapping of byte to hex representation. We do this once to avoid re-\n// computing every time we need to convert the result of a signature to hex.\nconst byteHexMapping = new Array(256);\nfor (let i = 0; i < byteHexMapping.length; i++) {\n    byteHexMapping[i] = i.toString(16).padStart(2, '0');\n}\n"], "names": [], "mappings": ";;;AAAA;;AAMO,MAAM,6BAA6B,kPAAA,CAAA,iBAAc;IACpD,YAAY,YAAY,CAAE;QACtB,KAAK;QACL,0EAA0E;QAC1E,2EAA2E;QAC3E,UAAU;QACV,IAAI,CAAC,YAAY,GAAG,gBAAgB,OAAO,MAAM;IACrD;IACA,cAAc,GACd,qBAAqB,OAAO,EAAE,MAAM,EAAE;QAClC,MAAM,IAAI,kPAAA,CAAA,uCAAoC,CAAC;IACnD;IACA,cAAc,GACd,MAAM,0BAA0B,OAAO,EAAE,MAAM,EAAE;QAC7C,MAAM,UAAU,IAAI;QACpB,MAAM,MAAM,MAAM,IAAI,CAAC,YAAY,CAAC,SAAS,CAAC,OAAO,QAAQ,MAAM,CAAC,SAAS;YACzE,MAAM;YACN,MAAM;gBAAE,MAAM;YAAU;QAC5B,GAAG,OAAO;YAAC;SAAO;QAClB,MAAM,kBAAkB,MAAM,IAAI,CAAC,YAAY,CAAC,IAAI,CAAC,QAAQ,KAAK,QAAQ,MAAM,CAAC;QACjF,qEAAqE;QACrE,2EAA2E;QAC3E,4EAA4E;QAC5E,MAAM,iBAAiB,IAAI,WAAW;QACtC,MAAM,oBAAoB,IAAI,MAAM,eAAe,MAAM;QACzD,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;YAC5C,iBAAiB,CAAC,EAAE,GAAG,cAAc,CAAC,cAAc,CAAC,EAAE,CAAC;QAC5D;QACA,OAAO,kBAAkB,IAAI,CAAC;IAClC;IACA,cAAc,GACd,MAAM,mBAAmB,IAAI,EAAE;QAC3B,OAAO,IAAI,WAAW,MAAM,IAAI,CAAC,YAAY,CAAC,MAAM,CAAC,WAAW;IACpE;AACJ;AACA,6EAA6E;AAC7E,4EAA4E;AAC5E,MAAM,iBAAiB,IAAI,MAAM;AACjC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,MAAM,EAAE,IAAK;IAC5C,cAAc,CAAC,EAAE,GAAG,EAAE,QAAQ,CAAC,IAAI,QAAQ,CAAC,GAAG;AACnD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 776, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/platform/PlatformFunctions.js"], "sourcesContent": ["import { FetchHttpClient } from '../net/FetchHttpClient.js';\nimport { SubtleCryptoProvider } from '../crypto/SubtleCryptoProvider.js';\n/**\n * Interface encapsulating various utility functions whose\n * implementations depend on the platform / JS runtime.\n */\nexport class PlatformFunctions {\n    constructor() {\n        this._fetchFn = null;\n        this._agent = null;\n    }\n    /**\n     * Gets uname with Node's built-in `exec` function, if available.\n     */\n    getUname() {\n        throw new Error('getUname not implemented.');\n    }\n    /**\n     * Generates a v4 UUID. See https://stackoverflow.com/a/2117523\n     */\n    uuid4() {\n        return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, (c) => {\n            const r = (Math.random() * 16) | 0;\n            const v = c === 'x' ? r : (r & 0x3) | 0x8;\n            return v.toString(16);\n        });\n    }\n    /**\n     * Compares strings in constant time.\n     */\n    secureCompare(a, b) {\n        // return early here if buffer lengths are not equal\n        if (a.length !== b.length) {\n            return false;\n        }\n        const len = a.length;\n        let result = 0;\n        for (let i = 0; i < len; ++i) {\n            result |= a.charCodeAt(i) ^ b.charCodeAt(i);\n        }\n        return result === 0;\n    }\n    /**\n     * Creates an event emitter.\n     */\n    createEmitter() {\n        throw new Error('createEmitter not implemented.');\n    }\n    /**\n     * Checks if the request data is a stream. If so, read the entire stream\n     * to a buffer and return the buffer.\n     */\n    tryBufferData(data) {\n        throw new Error('tryBufferData not implemented.');\n    }\n    /**\n     * Creates an HTTP client which uses the Node `http` and `https` packages\n     * to issue requests.\n     */\n    createNodeHttpClient(agent) {\n        throw new Error('createNodeHttpClient not implemented.');\n    }\n    /**\n     * Creates an HTTP client for issuing Stripe API requests which uses the Web\n     * Fetch API.\n     *\n     * A fetch function can optionally be passed in as a parameter. If none is\n     * passed, will default to the default `fetch` function in the global scope.\n     */\n    createFetchHttpClient(fetchFn) {\n        return new FetchHttpClient(fetchFn);\n    }\n    /**\n     * Creates an HTTP client using runtime-specific APIs.\n     */\n    createDefaultHttpClient() {\n        throw new Error('createDefaultHttpClient not implemented.');\n    }\n    /**\n     * Creates a CryptoProvider which uses the Node `crypto` package for its computations.\n     */\n    createNodeCryptoProvider() {\n        throw new Error('createNodeCryptoProvider not implemented.');\n    }\n    /**\n     * Creates a CryptoProvider which uses the SubtleCrypto interface of the Web Crypto API.\n     */\n    createSubtleCryptoProvider(subtleCrypto) {\n        return new SubtleCryptoProvider(subtleCrypto);\n    }\n    createDefaultCryptoProvider() {\n        throw new Error('createDefaultCryptoProvider not implemented.');\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAKO,MAAM;IACT,aAAc;QACV,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;IAClB;IACA;;KAEC,GACD,WAAW;QACP,MAAM,IAAI,MAAM;IACpB;IACA;;KAEC,GACD,QAAQ;QACJ,OAAO,uCAAuC,OAAO,CAAC,SAAS,CAAC;YAC5D,MAAM,IAAI,AAAC,KAAK,MAAM,KAAK,KAAM;YACjC,MAAM,IAAI,MAAM,MAAM,IAAI,AAAC,IAAI,MAAO;YACtC,OAAO,EAAE,QAAQ,CAAC;QACtB;IACJ;IACA;;KAEC,GACD,cAAc,CAAC,EAAE,CAAC,EAAE;QAChB,oDAAoD;QACpD,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;YACvB,OAAO;QACX;QACA,MAAM,MAAM,EAAE,MAAM;QACpB,IAAI,SAAS;QACb,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,EAAE,EAAG;YAC1B,UAAU,EAAE,UAAU,CAAC,KAAK,EAAE,UAAU,CAAC;QAC7C;QACA,OAAO,WAAW;IACtB;IACA;;KAEC,GACD,gBAAgB;QACZ,MAAM,IAAI,MAAM;IACpB;IACA;;;KAGC,GACD,cAAc,IAAI,EAAE;QAChB,MAAM,IAAI,MAAM;IACpB;IACA;;;KAGC,GACD,qBAAqB,KAAK,EAAE;QACxB,MAAM,IAAI,MAAM;IACpB;IACA;;;;;;KAMC,GACD,sBAAsB,OAAO,EAAE;QAC3B,OAAO,IAAI,gPAAA,CAAA,kBAAe,CAAC;IAC/B;IACA;;KAEC,GACD,0BAA0B;QACtB,MAAM,IAAI,MAAM;IACpB;IACA;;KAEC,GACD,2BAA2B;QACvB,MAAM,IAAI,MAAM;IACpB;IACA;;KAEC,GACD,2BAA2B,YAAY,EAAE;QACrC,OAAO,IAAI,wPAAA,CAAA,uBAAoB,CAAC;IACpC;IACA,8BAA8B;QAC1B,MAAM,IAAI,MAAM;IACpB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 867, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/Error.js"], "sourcesContent": ["/* eslint-disable camelcase */\n/* eslint-disable no-warning-comments */\nexport const generateV1Error = (rawStripeError) => {\n    switch (rawStripeError.type) {\n        case 'card_error':\n            return new StripeCardError(rawStripeError);\n        case 'invalid_request_error':\n            return new StripeInvalidRequestError(rawStripeError);\n        case 'api_error':\n            return new StripeAPIError(rawStripeError);\n        case 'authentication_error':\n            return new StripeAuthenticationError(rawStripeError);\n        case 'rate_limit_error':\n            return new StripeRateLimitError(rawStripeError);\n        case 'idempotency_error':\n            return new StripeIdempotencyError(rawStripeError);\n        case 'invalid_grant':\n            return new StripeInvalidGrantError(rawStripeError);\n        default:\n            return new StripeUnknownError(rawStripeError);\n    }\n};\n// eslint-disable-next-line complexity\nexport const generateV2Error = (rawStripeError) => {\n    switch (rawStripeError.type) {\n        // switchCases: The beginning of the section generated from our OpenAPI spec\n        case 'temporary_session_expired':\n            return new TemporarySessionExpiredError(rawStripeError);\n        // switchCases: The end of the section generated from our OpenAPI spec\n    }\n    // Special handling for requests with missing required fields in V2 APIs.\n    // invalid_field response in V2 APIs returns the field 'code' instead of 'type'.\n    switch (rawStripeError.code) {\n        case 'invalid_fields':\n            return new StripeInvalidRequestError(rawStripeError);\n    }\n    return generateV1Error(rawStripeError);\n};\n/**\n * StripeError is the base error from which all other more specific Stripe errors derive.\n * Specifically for errors returned from Stripe's REST API.\n */\nexport class StripeError extends Error {\n    constructor(raw = {}, type = null) {\n        var _a;\n        super(raw.message);\n        this.type = type || this.constructor.name;\n        this.raw = raw;\n        this.rawType = raw.type;\n        this.code = raw.code;\n        this.doc_url = raw.doc_url;\n        this.param = raw.param;\n        this.detail = raw.detail;\n        this.headers = raw.headers;\n        this.requestId = raw.requestId;\n        this.statusCode = raw.statusCode;\n        this.message = (_a = raw.message) !== null && _a !== void 0 ? _a : '';\n        this.userMessage = raw.user_message;\n        this.charge = raw.charge;\n        this.decline_code = raw.decline_code;\n        this.payment_intent = raw.payment_intent;\n        this.payment_method = raw.payment_method;\n        this.payment_method_type = raw.payment_method_type;\n        this.setup_intent = raw.setup_intent;\n        this.source = raw.source;\n    }\n}\n/**\n * Helper factory which takes raw stripe errors and outputs wrapping instances\n */\nStripeError.generate = generateV1Error;\n// Specific Stripe Error types:\n/**\n * CardError is raised when a user enters a card that can't be charged for\n * some reason.\n */\nexport class StripeCardError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeCardError');\n    }\n}\n/**\n * InvalidRequestError is raised when a request is initiated with invalid\n * parameters.\n */\nexport class StripeInvalidRequestError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeInvalidRequestError');\n    }\n}\n/**\n * APIError is a generic error that may be raised in cases where none of the\n * other named errors cover the problem. It could also be raised in the case\n * that a new error has been introduced in the API, but this version of the\n * Node.JS SDK doesn't know how to handle it.\n */\nexport class StripeAPIError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeAPIError');\n    }\n}\n/**\n * AuthenticationError is raised when invalid credentials are used to connect\n * to Stripe's servers.\n */\nexport class StripeAuthenticationError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeAuthenticationError');\n    }\n}\n/**\n * PermissionError is raised in cases where access was attempted on a resource\n * that wasn't allowed.\n */\nexport class StripePermissionError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripePermissionError');\n    }\n}\n/**\n * RateLimitError is raised in cases where an account is putting too much load\n * on Stripe's API servers (usually by performing too many requests). Please\n * back off on request rate.\n */\nexport class StripeRateLimitError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeRateLimitError');\n    }\n}\n/**\n * StripeConnectionError is raised in the event that the SDK can't connect to\n * Stripe's servers. That can be for a variety of different reasons from a\n * downed network to a bad TLS certificate.\n */\nexport class StripeConnectionError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeConnectionError');\n    }\n}\n/**\n * SignatureVerificationError is raised when the signature verification for a\n * webhook fails\n */\nexport class StripeSignatureVerificationError extends StripeError {\n    constructor(header, payload, raw = {}) {\n        super(raw, 'StripeSignatureVerificationError');\n        this.header = header;\n        this.payload = payload;\n    }\n}\n/**\n * IdempotencyError is raised in cases where an idempotency key was used\n * improperly.\n */\nexport class StripeIdempotencyError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeIdempotencyError');\n    }\n}\n/**\n * InvalidGrantError is raised when a specified code doesn't exist, is\n * expired, has been used, or doesn't belong to you; a refresh token doesn't\n * exist, or doesn't belong to you; or if an API key's mode (live or test)\n * doesn't match the mode of a code or refresh token.\n */\nexport class StripeInvalidGrantError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeInvalidGrantError');\n    }\n}\n/**\n * Any other error from Stripe not specifically captured above\n */\nexport class StripeUnknownError extends StripeError {\n    constructor(raw = {}) {\n        super(raw, 'StripeUnknownError');\n    }\n}\n// classDefinitions: The beginning of the section generated from our OpenAPI spec\nexport class TemporarySessionExpiredError extends StripeError {\n    constructor(rawStripeError = {}) {\n        super(rawStripeError, 'TemporarySessionExpiredError');\n    }\n}\n// classDefinitions: The end of the section generated from our OpenAPI spec\n"], "names": [], "mappings": "AAAA,4BAA4B,GAC5B,sCAAsC;;;;;;;;;;;;;;;;;AAC/B,MAAM,kBAAkB,CAAC;IAC5B,OAAQ,eAAe,IAAI;QACvB,KAAK;YACD,OAAO,IAAI,gBAAgB;QAC/B,KAAK;YACD,OAAO,IAAI,0BAA0B;QACzC,KAAK;YACD,OAAO,IAAI,eAAe;QAC9B,KAAK;YACD,OAAO,IAAI,0BAA0B;QACzC,KAAK;YACD,OAAO,IAAI,qBAAqB;QACpC,KAAK;YACD,OAAO,IAAI,uBAAuB;QACtC,KAAK;YACD,OAAO,IAAI,wBAAwB;QACvC;YACI,OAAO,IAAI,mBAAmB;IACtC;AACJ;AAEO,MAAM,kBAAkB,CAAC;IAC5B,OAAQ,eAAe,IAAI;QACvB,4EAA4E;QAC5E,KAAK;YACD,OAAO,IAAI,6BAA6B;IAEhD;IACA,yEAAyE;IACzE,gFAAgF;IAChF,OAAQ,eAAe,IAAI;QACvB,KAAK;YACD,OAAO,IAAI,0BAA0B;IAC7C;IACA,OAAO,gBAAgB;AAC3B;AAKO,MAAM,oBAAoB;IAC7B,YAAY,MAAM,CAAC,CAAC,EAAE,OAAO,IAAI,CAAE;QAC/B,IAAI;QACJ,KAAK,CAAC,IAAI,OAAO;QACjB,IAAI,CAAC,IAAI,GAAG,QAAQ,IAAI,CAAC,WAAW,CAAC,IAAI;QACzC,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,OAAO,GAAG,IAAI,IAAI;QACvB,IAAI,CAAC,IAAI,GAAG,IAAI,IAAI;QACpB,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO;QAC1B,IAAI,CAAC,KAAK,GAAG,IAAI,KAAK;QACtB,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;QACxB,IAAI,CAAC,OAAO,GAAG,IAAI,OAAO;QAC1B,IAAI,CAAC,SAAS,GAAG,IAAI,SAAS;QAC9B,IAAI,CAAC,UAAU,GAAG,IAAI,UAAU;QAChC,IAAI,CAAC,OAAO,GAAG,CAAC,KAAK,IAAI,OAAO,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACnE,IAAI,CAAC,WAAW,GAAG,IAAI,YAAY;QACnC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;QACxB,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY;QACpC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc;QACxC,IAAI,CAAC,cAAc,GAAG,IAAI,cAAc;QACxC,IAAI,CAAC,mBAAmB,GAAG,IAAI,mBAAmB;QAClD,IAAI,CAAC,YAAY,GAAG,IAAI,YAAY;QACpC,IAAI,CAAC,MAAM,GAAG,IAAI,MAAM;IAC5B;AACJ;AACA;;CAEC,GACD,YAAY,QAAQ,GAAG;AAMhB,MAAM,wBAAwB;IACjC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAKO,MAAM,kCAAkC;IAC3C,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAOO,MAAM,uBAAuB;IAChC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAKO,MAAM,kCAAkC;IAC3C,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAKO,MAAM,8BAA8B;IACvC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAMO,MAAM,6BAA6B;IACtC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAMO,MAAM,8BAA8B;IACvC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAKO,MAAM,yCAAyC;IAClD,YAAY,MAAM,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC,CAAE;QACnC,KAAK,CAAC,KAAK;QACX,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACnB;AACJ;AAKO,MAAM,+BAA+B;IACxC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAOO,MAAM,gCAAgC;IACzC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAIO,MAAM,2BAA2B;IACpC,YAAY,MAAM,CAAC,CAAC,CAAE;QAClB,KAAK,CAAC,KAAK;IACf;AACJ;AAEO,MAAM,qCAAqC;IAC9C,YAAY,iBAAiB,CAAC,CAAC,CAAE;QAC7B,KAAK,CAAC,gBAAgB;IAC1B;AACJ,EACA,2EAA2E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1014, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/platform/NodePlatformFunctions.js"], "sourcesContent": ["import * as crypto from 'crypto';\nimport { EventEmitter } from 'events';\nimport { NodeCryptoProvider } from '../crypto/NodeCryptoProvider.js';\nimport { NodeHttpClient } from '../net/NodeHttpClient.js';\nimport { PlatformFunctions } from './PlatformFunctions.js';\nimport { StripeError } from '../Error.js';\nimport { concat } from '../utils.js';\nimport { exec } from 'child_process';\nclass StreamProcessingError extends StripeError {\n}\n/**\n * Specializes WebPlatformFunctions using APIs available in Node.js.\n */\nexport class NodePlatformFunctions extends PlatformFunctions {\n    constructor() {\n        super();\n        this._exec = exec;\n        this._UNAME_CACHE = null;\n    }\n    /** @override */\n    uuid4() {\n        // available in: v14.17.x+\n        if (crypto.randomUUID) {\n            return crypto.randomUUID();\n        }\n        return super.uuid4();\n    }\n    /**\n     * @override\n     * Node's built in `exec` function sometimes throws outright,\n     * and sometimes has a callback with an error,\n     * depending on the type of error.\n     *\n     * This unifies that interface by resolving with a null uname\n     * if an error is encountered.\n     */\n    getUname() {\n        if (!this._UNAME_CACHE) {\n            this._UNAME_CACHE = new Promise((resolve, reject) => {\n                try {\n                    this._exec('uname -a', (err, uname) => {\n                        if (err) {\n                            return resolve(null);\n                        }\n                        resolve(uname);\n                    });\n                }\n                catch (e) {\n                    resolve(null);\n                }\n            });\n        }\n        return this._UNAME_CACHE;\n    }\n    /**\n     * @override\n     * Secure compare, from https://github.com/freewil/scmp\n     */\n    secureCompare(a, b) {\n        if (!a || !b) {\n            throw new Error('secureCompare must receive two arguments');\n        }\n        // return early here if buffer lengths are not equal since timingSafeEqual\n        // will throw if buffer lengths are not equal\n        if (a.length !== b.length) {\n            return false;\n        }\n        // use crypto.timingSafeEqual if available (since Node.js v6.6.0),\n        // otherwise use our own scmp-internal function.\n        if (crypto.timingSafeEqual) {\n            const textEncoder = new TextEncoder();\n            const aEncoded = textEncoder.encode(a);\n            const bEncoded = textEncoder.encode(b);\n            return crypto.timingSafeEqual(aEncoded, bEncoded);\n        }\n        return super.secureCompare(a, b);\n    }\n    createEmitter() {\n        return new EventEmitter();\n    }\n    /** @override */\n    tryBufferData(data) {\n        if (!(data.file.data instanceof EventEmitter)) {\n            return Promise.resolve(data);\n        }\n        const bufferArray = [];\n        return new Promise((resolve, reject) => {\n            data.file.data\n                .on('data', (line) => {\n                bufferArray.push(line);\n            })\n                .once('end', () => {\n                // @ts-ignore\n                const bufferData = Object.assign({}, data);\n                bufferData.file.data = concat(bufferArray);\n                resolve(bufferData);\n            })\n                .on('error', (err) => {\n                reject(new StreamProcessingError({\n                    message: 'An error occurred while attempting to process the file for upload.',\n                    detail: err,\n                }));\n            });\n        });\n    }\n    /** @override */\n    createNodeHttpClient(agent) {\n        return new NodeHttpClient(agent);\n    }\n    /** @override */\n    createDefaultHttpClient() {\n        return new NodeHttpClient();\n    }\n    /** @override */\n    createNodeCryptoProvider() {\n        return new NodeCryptoProvider();\n    }\n    /** @override */\n    createDefaultCryptoProvider() {\n        return this.createNodeCryptoProvider();\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AACA,MAAM,8BAA8B,+NAAA,CAAA,cAAW;AAC/C;AAIO,MAAM,8BAA8B,uPAAA,CAAA,oBAAiB;IACxD,aAAc;QACV,KAAK;QACL,IAAI,CAAC,KAAK,GAAG,mHAAA,CAAA,OAAI;QACjB,IAAI,CAAC,YAAY,GAAG;IACxB;IACA,cAAc,GACd,QAAQ;QACJ,0BAA0B;QAC1B,IAAI,qGAAA,CAAA,aAAiB,EAAE;YACnB,OAAO,CAAA,GAAA,qGAAA,CAAA,aAAiB,AAAD;QAC3B;QACA,OAAO,KAAK,CAAC;IACjB;IACA;;;;;;;;KAQC,GACD,WAAW;QACP,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,IAAI,CAAC,YAAY,GAAG,IAAI,QAAQ,CAAC,SAAS;gBACtC,IAAI;oBACA,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,KAAK;wBACzB,IAAI,KAAK;4BACL,OAAO,QAAQ;wBACnB;wBACA,QAAQ;oBACZ;gBACJ,EACA,OAAO,GAAG;oBACN,QAAQ;gBACZ;YACJ;QACJ;QACA,OAAO,IAAI,CAAC,YAAY;IAC5B;IACA;;;KAGC,GACD,cAAc,CAAC,EAAE,CAAC,EAAE;QAChB,IAAI,CAAC,KAAK,CAAC,GAAG;YACV,MAAM,IAAI,MAAM;QACpB;QACA,0EAA0E;QAC1E,6CAA6C;QAC7C,IAAI,EAAE,MAAM,KAAK,EAAE,MAAM,EAAE;YACvB,OAAO;QACX;QACA,kEAAkE;QAClE,gDAAgD;QAChD,IAAI,qGAAA,CAAA,kBAAsB,EAAE;YACxB,MAAM,cAAc,IAAI;YACxB,MAAM,WAAW,YAAY,MAAM,CAAC;YACpC,MAAM,WAAW,YAAY,MAAM,CAAC;YACpC,OAAO,CAAA,GAAA,qGAAA,CAAA,kBAAsB,AAAD,EAAE,UAAU;QAC5C;QACA,OAAO,KAAK,CAAC,cAAc,GAAG;IAClC;IACA,gBAAgB;QACZ,OAAO,IAAI,qGAAA,CAAA,eAAY;IAC3B;IACA,cAAc,GACd,cAAc,IAAI,EAAE;QAChB,IAAI,CAAC,CAAC,KAAK,IAAI,CAAC,IAAI,YAAY,qGAAA,CAAA,eAAY,GAAG;YAC3C,OAAO,QAAQ,OAAO,CAAC;QAC3B;QACA,MAAM,cAAc,EAAE;QACtB,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,KAAK,IAAI,CAAC,IAAI,CACT,EAAE,CAAC,QAAQ,CAAC;gBACb,YAAY,IAAI,CAAC;YACrB,GACK,IAAI,CAAC,OAAO;gBACb,aAAa;gBACb,MAAM,aAAa,OAAO,MAAM,CAAC,CAAC,GAAG;gBACrC,WAAW,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,+NAAA,CAAA,SAAM,AAAD,EAAE;gBAC9B,QAAQ;YACZ,GACK,EAAE,CAAC,SAAS,CAAC;gBACd,OAAO,IAAI,sBAAsB;oBAC7B,SAAS;oBACT,QAAQ;gBACZ;YACJ;QACJ;IACJ;IACA,cAAc,GACd,qBAAqB,KAAK,EAAE;QACxB,OAAO,IAAI,+OAAA,CAAA,iBAAc,CAAC;IAC9B;IACA,cAAc,GACd,0BAA0B;QACtB,OAAO,IAAI,+OAAA,CAAA,iBAAc;IAC7B;IACA,cAAc,GACd,2BAA2B;QACvB,OAAO,IAAI,sPAAA,CAAA,qBAAkB;IACjC;IACA,cAAc,GACd,8BAA8B;QAC1B,OAAO,IAAI,CAAC,wBAAwB;IACxC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1138, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/RequestSender.js"], "sourcesContent": ["import { StripeAPIError, StripeAuthenticationError, StripeConnectionError, StripeError, StripePermissionError, StripeRateLimitError, generateV1Error, generateV2Error, } from './Error.js';\nimport { HttpClient } from './net/HttpClient.js';\nimport { emitWarning, jsonStringifyRequestData, normalizeHeaders, queryStringifyRequestData, removeNullish, getAPIMode, getOptionsFromArgs, getDataFromArgs, parseHttpHeaderAsString, parseHttpHeaderAsNumber, } from './utils.js';\nconst MAX_RETRY_AFTER_WAIT = 60;\nexport class RequestSender {\n    constructor(stripe, maxBufferedRequestMetric) {\n        this._stripe = stripe;\n        this._maxBufferedRequestMetric = maxBufferedRequestMetric;\n    }\n    _addHeadersDirectlyToObject(obj, headers) {\n        // For convenience, make some headers easily accessible on\n        // lastResponse.\n        // NOTE: <PERSON><PERSON> responds with lowercase header names/keys.\n        obj.requestId = headers['request-id'];\n        obj.stripeAccount = obj.stripeAccount || headers['stripe-account'];\n        obj.apiVersion = obj.apiVersion || headers['stripe-version'];\n        obj.idempotencyKey = obj.idempotencyKey || headers['idempotency-key'];\n    }\n    _makeResponseEvent(requestEvent, statusCode, headers) {\n        const requestEndTime = Date.now();\n        const requestDurationMs = requestEndTime - requestEvent.request_start_time;\n        return removeNullish({\n            api_version: headers['stripe-version'],\n            account: headers['stripe-account'],\n            idempotency_key: headers['idempotency-key'],\n            method: requestEvent.method,\n            path: requestEvent.path,\n            status: statusCode,\n            request_id: this._getRequestId(headers),\n            elapsed: requestDurationMs,\n            request_start_time: requestEvent.request_start_time,\n            request_end_time: requestEndTime,\n        });\n    }\n    _getRequestId(headers) {\n        return headers['request-id'];\n    }\n    /**\n     * Used by methods with spec.streaming === true. For these methods, we do not\n     * buffer successful responses into memory or do parse them into stripe\n     * objects, we delegate that all of that to the user and pass back the raw\n     * http.Response object to the callback.\n     *\n     * (Unsuccessful responses shouldn't make it here, they should\n     * still be buffered/parsed and handled by _jsonResponseHandler -- see\n     * makeRequest)\n     */\n    _streamingResponseHandler(requestEvent, usage, callback) {\n        return (res) => {\n            const headers = res.getHeaders();\n            const streamCompleteCallback = () => {\n                const responseEvent = this._makeResponseEvent(requestEvent, res.getStatusCode(), headers);\n                this._stripe._emitter.emit('response', responseEvent);\n                this._recordRequestMetrics(this._getRequestId(headers), responseEvent.elapsed, usage);\n            };\n            const stream = res.toStream(streamCompleteCallback);\n            // This is here for backwards compatibility, as the stream is a raw\n            // HTTP response in Node and the legacy behavior was to mutate this\n            // response.\n            this._addHeadersDirectlyToObject(stream, headers);\n            return callback(null, stream);\n        };\n    }\n    /**\n     * Default handler for Stripe responses. Buffers the response into memory,\n     * parses the JSON and returns it (i.e. passes it to the callback) if there\n     * is no \"error\" field. Otherwise constructs/passes an appropriate Error.\n     */\n    _jsonResponseHandler(requestEvent, apiMode, usage, callback) {\n        return (res) => {\n            const headers = res.getHeaders();\n            const requestId = this._getRequestId(headers);\n            const statusCode = res.getStatusCode();\n            const responseEvent = this._makeResponseEvent(requestEvent, statusCode, headers);\n            this._stripe._emitter.emit('response', responseEvent);\n            res\n                .toJSON()\n                .then((jsonResponse) => {\n                if (jsonResponse.error) {\n                    let err;\n                    // Convert OAuth error responses into a standard format\n                    // so that the rest of the error logic can be shared\n                    if (typeof jsonResponse.error === 'string') {\n                        jsonResponse.error = {\n                            type: jsonResponse.error,\n                            message: jsonResponse.error_description,\n                        };\n                    }\n                    jsonResponse.error.headers = headers;\n                    jsonResponse.error.statusCode = statusCode;\n                    jsonResponse.error.requestId = requestId;\n                    if (statusCode === 401) {\n                        err = new StripeAuthenticationError(jsonResponse.error);\n                    }\n                    else if (statusCode === 403) {\n                        err = new StripePermissionError(jsonResponse.error);\n                    }\n                    else if (statusCode === 429) {\n                        err = new StripeRateLimitError(jsonResponse.error);\n                    }\n                    else if (apiMode === 'v2') {\n                        err = generateV2Error(jsonResponse.error);\n                    }\n                    else {\n                        err = generateV1Error(jsonResponse.error);\n                    }\n                    throw err;\n                }\n                return jsonResponse;\n            }, (e) => {\n                throw new StripeAPIError({\n                    message: 'Invalid JSON received from the Stripe API',\n                    exception: e,\n                    requestId: headers['request-id'],\n                });\n            })\n                .then((jsonResponse) => {\n                this._recordRequestMetrics(requestId, responseEvent.elapsed, usage);\n                // Expose raw response object.\n                const rawResponse = res.getRawResponse();\n                this._addHeadersDirectlyToObject(rawResponse, headers);\n                Object.defineProperty(jsonResponse, 'lastResponse', {\n                    enumerable: false,\n                    writable: false,\n                    value: rawResponse,\n                });\n                callback(null, jsonResponse);\n            }, (e) => callback(e, null));\n        };\n    }\n    static _generateConnectionErrorMessage(requestRetries) {\n        return `An error occurred with our connection to Stripe.${requestRetries > 0 ? ` Request was retried ${requestRetries} times.` : ''}`;\n    }\n    // For more on when and how to retry API requests, see https://stripe.com/docs/error-handling#safely-retrying-requests-with-idempotency\n    static _shouldRetry(res, numRetries, maxRetries, error) {\n        if (error &&\n            numRetries === 0 &&\n            HttpClient.CONNECTION_CLOSED_ERROR_CODES.includes(error.code)) {\n            return true;\n        }\n        // Do not retry if we are out of retries.\n        if (numRetries >= maxRetries) {\n            return false;\n        }\n        // Retry on connection error.\n        if (!res) {\n            return true;\n        }\n        // The API may ask us not to retry (e.g., if doing so would be a no-op)\n        // or advise us to retry (e.g., in cases of lock timeouts); we defer to that.\n        if (res.getHeaders()['stripe-should-retry'] === 'false') {\n            return false;\n        }\n        if (res.getHeaders()['stripe-should-retry'] === 'true') {\n            return true;\n        }\n        // Retry on conflict errors.\n        if (res.getStatusCode() === 409) {\n            return true;\n        }\n        // Retry on 500, 503, and other internal errors.\n        //\n        // Note that we expect the stripe-should-retry header to be false\n        // in most cases when a 500 is returned, since our idempotency framework\n        // would typically replay it anyway.\n        if (res.getStatusCode() >= 500) {\n            return true;\n        }\n        return false;\n    }\n    _getSleepTimeInMS(numRetries, retryAfter = null) {\n        const initialNetworkRetryDelay = this._stripe.getInitialNetworkRetryDelay();\n        const maxNetworkRetryDelay = this._stripe.getMaxNetworkRetryDelay();\n        // Apply exponential backoff with initialNetworkRetryDelay on the\n        // number of numRetries so far as inputs. Do not allow the number to exceed\n        // maxNetworkRetryDelay.\n        let sleepSeconds = Math.min(initialNetworkRetryDelay * Math.pow(2, numRetries - 1), maxNetworkRetryDelay);\n        // Apply some jitter by randomizing the value in the range of\n        // (sleepSeconds / 2) to (sleepSeconds).\n        sleepSeconds *= 0.5 * (1 + Math.random());\n        // But never sleep less than the base sleep seconds.\n        sleepSeconds = Math.max(initialNetworkRetryDelay, sleepSeconds);\n        // And never sleep less than the time the API asks us to wait, assuming it's a reasonable ask.\n        if (Number.isInteger(retryAfter) && retryAfter <= MAX_RETRY_AFTER_WAIT) {\n            sleepSeconds = Math.max(sleepSeconds, retryAfter);\n        }\n        return sleepSeconds * 1000;\n    }\n    // Max retries can be set on a per request basis. Favor those over the global setting\n    _getMaxNetworkRetries(settings = {}) {\n        return settings.maxNetworkRetries !== undefined &&\n            Number.isInteger(settings.maxNetworkRetries)\n            ? settings.maxNetworkRetries\n            : this._stripe.getMaxNetworkRetries();\n    }\n    _defaultIdempotencyKey(method, settings, apiMode) {\n        // If this is a POST and we allow multiple retries, ensure an idempotency key.\n        const maxRetries = this._getMaxNetworkRetries(settings);\n        const genKey = () => `stripe-node-retry-${this._stripe._platformFunctions.uuid4()}`;\n        // more verbose than it needs to be, but gives clear separation between V1 and V2 behavior\n        if (apiMode === 'v2') {\n            if (method === 'POST' || method === 'DELETE') {\n                return genKey();\n            }\n        }\n        else if (apiMode === 'v1') {\n            if (method === 'POST' && maxRetries > 0) {\n                return genKey();\n            }\n        }\n        return null;\n    }\n    _makeHeaders({ contentType, contentLength, apiVersion, clientUserAgent, method, userSuppliedHeaders, userSuppliedSettings, stripeAccount, stripeContext, apiMode, }) {\n        const defaultHeaders = {\n            Accept: 'application/json',\n            'Content-Type': contentType,\n            'User-Agent': this._getUserAgentString(apiMode),\n            'X-Stripe-Client-User-Agent': clientUserAgent,\n            'X-Stripe-Client-Telemetry': this._getTelemetryHeader(),\n            'Stripe-Version': apiVersion,\n            'Stripe-Account': stripeAccount,\n            'Stripe-Context': stripeContext,\n            'Idempotency-Key': this._defaultIdempotencyKey(method, userSuppliedSettings, apiMode),\n        };\n        // As per https://datatracker.ietf.org/doc/html/rfc7230#section-3.3.2:\n        //   A user agent SHOULD send a Content-Length in a request message when\n        //   no Transfer-Encoding is sent and the request method defines a meaning\n        //   for an enclosed payload body.  For example, a Content-Length header\n        //   field is normally sent in a POST request even when the value is 0\n        //   (indicating an empty payload body).  A user agent SHOULD NOT send a\n        //   Content-Length header field when the request message does not contain\n        //   a payload body and the method semantics do not anticipate such a\n        //   body.\n        //\n        // These method types are expected to have bodies and so we should always\n        // include a Content-Length.\n        const methodHasPayload = method == 'POST' || method == 'PUT' || method == 'PATCH';\n        // If a content length was specified, we always include it regardless of\n        // whether the method semantics anticipate such a body. This keeps us\n        // consistent with historical behavior. We do however want to warn on this\n        // and fix these cases as they are semantically incorrect.\n        if (methodHasPayload || contentLength) {\n            if (!methodHasPayload) {\n                emitWarning(`${method} method had non-zero contentLength but no payload is expected for this verb`);\n            }\n            defaultHeaders['Content-Length'] = contentLength;\n        }\n        return Object.assign(removeNullish(defaultHeaders), \n        // If the user supplied, say 'idempotency-key', override instead of appending by ensuring caps are the same.\n        normalizeHeaders(userSuppliedHeaders));\n    }\n    _getUserAgentString(apiMode) {\n        const packageVersion = this._stripe.getConstant('PACKAGE_VERSION');\n        const appInfo = this._stripe._appInfo\n            ? this._stripe.getAppInfoAsString()\n            : '';\n        return `Stripe/${apiMode} NodeBindings/${packageVersion} ${appInfo}`.trim();\n    }\n    _getTelemetryHeader() {\n        if (this._stripe.getTelemetryEnabled() &&\n            this._stripe._prevRequestMetrics.length > 0) {\n            const metrics = this._stripe._prevRequestMetrics.shift();\n            return JSON.stringify({\n                last_request_metrics: metrics,\n            });\n        }\n    }\n    _recordRequestMetrics(requestId, requestDurationMs, usage) {\n        if (this._stripe.getTelemetryEnabled() && requestId) {\n            if (this._stripe._prevRequestMetrics.length > this._maxBufferedRequestMetric) {\n                emitWarning('Request metrics buffer is full, dropping telemetry message.');\n            }\n            else {\n                const m = {\n                    request_id: requestId,\n                    request_duration_ms: requestDurationMs,\n                };\n                if (usage && usage.length > 0) {\n                    m.usage = usage;\n                }\n                this._stripe._prevRequestMetrics.push(m);\n            }\n        }\n    }\n    _rawRequest(method, path, params, options) {\n        const requestPromise = new Promise((resolve, reject) => {\n            let opts;\n            try {\n                const requestMethod = method.toUpperCase();\n                if (requestMethod !== 'POST' &&\n                    params &&\n                    Object.keys(params).length !== 0) {\n                    throw new Error('rawRequest only supports params on POST requests. Please pass null and add your parameters to path.');\n                }\n                const args = [].slice.call([params, options]);\n                // Pull request data and options (headers, auth) from args.\n                const dataFromArgs = getDataFromArgs(args);\n                const data = requestMethod === 'POST' ? Object.assign({}, dataFromArgs) : null;\n                const calculatedOptions = getOptionsFromArgs(args);\n                const headers = calculatedOptions.headers;\n                // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n                const authenticator = calculatedOptions.authenticator;\n                opts = {\n                    requestMethod,\n                    requestPath: path,\n                    bodyData: data,\n                    queryData: {},\n                    authenticator,\n                    headers,\n                    host: calculatedOptions.host,\n                    streaming: !!calculatedOptions.streaming,\n                    settings: {},\n                    usage: ['raw_request'],\n                };\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            function requestCallback(err, response) {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(response);\n                }\n            }\n            const { headers, settings } = opts;\n            // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n            const authenticator = opts.authenticator;\n            this._request(opts.requestMethod, opts.host, path, opts.bodyData, authenticator, { headers, settings, streaming: opts.streaming }, opts.usage, requestCallback);\n        });\n        return requestPromise;\n    }\n    _request(method, host, path, data, authenticator, options, usage = [], callback, requestDataProcessor = null) {\n        var _a;\n        let requestData;\n        authenticator = (_a = authenticator !== null && authenticator !== void 0 ? authenticator : this._stripe._authenticator) !== null && _a !== void 0 ? _a : null;\n        const apiMode = getAPIMode(path);\n        const retryRequest = (requestFn, apiVersion, headers, requestRetries, retryAfter) => {\n            return setTimeout(requestFn, this._getSleepTimeInMS(requestRetries, retryAfter), apiVersion, headers, requestRetries + 1);\n        };\n        const makeRequest = (apiVersion, headers, numRetries) => {\n            // timeout can be set on a per-request basis. Favor that over the global setting\n            const timeout = options.settings &&\n                options.settings.timeout &&\n                Number.isInteger(options.settings.timeout) &&\n                options.settings.timeout >= 0\n                ? options.settings.timeout\n                : this._stripe.getApiField('timeout');\n            const request = {\n                host: host || this._stripe.getApiField('host'),\n                port: this._stripe.getApiField('port'),\n                path: path,\n                method: method,\n                headers: Object.assign({}, headers),\n                body: requestData,\n                protocol: this._stripe.getApiField('protocol'),\n            };\n            authenticator(request)\n                .then(() => {\n                const req = this._stripe\n                    .getApiField('httpClient')\n                    .makeRequest(request.host, request.port, request.path, request.method, request.headers, request.body, request.protocol, timeout);\n                const requestStartTime = Date.now();\n                const requestEvent = removeNullish({\n                    api_version: apiVersion,\n                    account: parseHttpHeaderAsString(headers['Stripe-Account']),\n                    idempotency_key: parseHttpHeaderAsString(headers['Idempotency-Key']),\n                    method,\n                    path,\n                    request_start_time: requestStartTime,\n                });\n                const requestRetries = numRetries || 0;\n                const maxRetries = this._getMaxNetworkRetries(options.settings || {});\n                this._stripe._emitter.emit('request', requestEvent);\n                req\n                    .then((res) => {\n                    if (RequestSender._shouldRetry(res, requestRetries, maxRetries)) {\n                        return retryRequest(makeRequest, apiVersion, headers, requestRetries, parseHttpHeaderAsNumber(res.getHeaders()['retry-after']));\n                    }\n                    else if (options.streaming && res.getStatusCode() < 400) {\n                        return this._streamingResponseHandler(requestEvent, usage, callback)(res);\n                    }\n                    else {\n                        return this._jsonResponseHandler(requestEvent, apiMode, usage, callback)(res);\n                    }\n                })\n                    .catch((error) => {\n                    if (RequestSender._shouldRetry(null, requestRetries, maxRetries, error)) {\n                        return retryRequest(makeRequest, apiVersion, headers, requestRetries, null);\n                    }\n                    else {\n                        const isTimeoutError = error.code && error.code === HttpClient.TIMEOUT_ERROR_CODE;\n                        return callback(new StripeConnectionError({\n                            message: isTimeoutError\n                                ? `Request aborted due to timeout being reached (${timeout}ms)`\n                                : RequestSender._generateConnectionErrorMessage(requestRetries),\n                            detail: error,\n                        }));\n                    }\n                });\n            })\n                .catch((e) => {\n                throw new StripeError({\n                    message: 'Unable to authenticate the request',\n                    exception: e,\n                });\n            });\n        };\n        const prepareAndMakeRequest = (error, data) => {\n            if (error) {\n                return callback(error);\n            }\n            requestData = data;\n            this._stripe.getClientUserAgent((clientUserAgent) => {\n                const apiVersion = this._stripe.getApiField('version');\n                const headers = this._makeHeaders({\n                    contentType: apiMode == 'v2'\n                        ? 'application/json'\n                        : 'application/x-www-form-urlencoded',\n                    contentLength: requestData.length,\n                    apiVersion: apiVersion,\n                    clientUserAgent,\n                    method,\n                    userSuppliedHeaders: options.headers,\n                    userSuppliedSettings: options.settings,\n                    stripeAccount: apiMode == 'v2' ? null : this._stripe.getApiField('stripeAccount'),\n                    stripeContext: apiMode == 'v2' ? this._stripe.getApiField('stripeContext') : null,\n                    apiMode: apiMode,\n                });\n                makeRequest(apiVersion, headers, 0);\n            });\n        };\n        if (requestDataProcessor) {\n            requestDataProcessor(method, data, options.headers, prepareAndMakeRequest);\n        }\n        else {\n            let stringifiedData;\n            if (apiMode == 'v2') {\n                stringifiedData = data ? jsonStringifyRequestData(data) : '';\n            }\n            else {\n                stringifiedData = queryStringifyRequestData(data || {}, apiMode);\n            }\n            prepareAndMakeRequest(null, stringifiedData);\n        }\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,MAAM,uBAAuB;AACtB,MAAM;IACT,YAAY,MAAM,EAAE,wBAAwB,CAAE;QAC1C,IAAI,CAAC,OAAO,GAAG;QACf,IAAI,CAAC,yBAAyB,GAAG;IACrC;IACA,4BAA4B,GAAG,EAAE,OAAO,EAAE;QACtC,0DAA0D;QAC1D,gBAAgB;QAChB,0DAA0D;QAC1D,IAAI,SAAS,GAAG,OAAO,CAAC,aAAa;QACrC,IAAI,aAAa,GAAG,IAAI,aAAa,IAAI,OAAO,CAAC,iBAAiB;QAClE,IAAI,UAAU,GAAG,IAAI,UAAU,IAAI,OAAO,CAAC,iBAAiB;QAC5D,IAAI,cAAc,GAAG,IAAI,cAAc,IAAI,OAAO,CAAC,kBAAkB;IACzE;IACA,mBAAmB,YAAY,EAAE,UAAU,EAAE,OAAO,EAAE;QAClD,MAAM,iBAAiB,KAAK,GAAG;QAC/B,MAAM,oBAAoB,iBAAiB,aAAa,kBAAkB;QAC1E,OAAO,CAAA,GAAA,+NAAA,CAAA,gBAAa,AAAD,EAAE;YACjB,aAAa,OAAO,CAAC,iBAAiB;YACtC,SAAS,OAAO,CAAC,iBAAiB;YAClC,iBAAiB,OAAO,CAAC,kBAAkB;YAC3C,QAAQ,aAAa,MAAM;YAC3B,MAAM,aAAa,IAAI;YACvB,QAAQ;YACR,YAAY,IAAI,CAAC,aAAa,CAAC;YAC/B,SAAS;YACT,oBAAoB,aAAa,kBAAkB;YACnD,kBAAkB;QACtB;IACJ;IACA,cAAc,OAAO,EAAE;QACnB,OAAO,OAAO,CAAC,aAAa;IAChC;IACA;;;;;;;;;KASC,GACD,0BAA0B,YAAY,EAAE,KAAK,EAAE,QAAQ,EAAE;QACrD,OAAO,CAAC;YACJ,MAAM,UAAU,IAAI,UAAU;YAC9B,MAAM,yBAAyB;gBAC3B,MAAM,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,cAAc,IAAI,aAAa,IAAI;gBACjF,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY;gBACvC,IAAI,CAAC,qBAAqB,CAAC,IAAI,CAAC,aAAa,CAAC,UAAU,cAAc,OAAO,EAAE;YACnF;YACA,MAAM,SAAS,IAAI,QAAQ,CAAC;YAC5B,mEAAmE;YACnE,mEAAmE;YACnE,YAAY;YACZ,IAAI,CAAC,2BAA2B,CAAC,QAAQ;YACzC,OAAO,SAAS,MAAM;QAC1B;IACJ;IACA;;;;KAIC,GACD,qBAAqB,YAAY,EAAE,OAAO,EAAE,KAAK,EAAE,QAAQ,EAAE;QACzD,OAAO,CAAC;YACJ,MAAM,UAAU,IAAI,UAAU;YAC9B,MAAM,YAAY,IAAI,CAAC,aAAa,CAAC;YACrC,MAAM,aAAa,IAAI,aAAa;YACpC,MAAM,gBAAgB,IAAI,CAAC,kBAAkB,CAAC,cAAc,YAAY;YACxE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,YAAY;YACvC,IACK,MAAM,GACN,IAAI,CAAC,CAAC;gBACP,IAAI,aAAa,KAAK,EAAE;oBACpB,IAAI;oBACJ,uDAAuD;oBACvD,oDAAoD;oBACpD,IAAI,OAAO,aAAa,KAAK,KAAK,UAAU;wBACxC,aAAa,KAAK,GAAG;4BACjB,MAAM,aAAa,KAAK;4BACxB,SAAS,aAAa,iBAAiB;wBAC3C;oBACJ;oBACA,aAAa,KAAK,CAAC,OAAO,GAAG;oBAC7B,aAAa,KAAK,CAAC,UAAU,GAAG;oBAChC,aAAa,KAAK,CAAC,SAAS,GAAG;oBAC/B,IAAI,eAAe,KAAK;wBACpB,MAAM,IAAI,+NAAA,CAAA,4BAAyB,CAAC,aAAa,KAAK;oBAC1D,OACK,IAAI,eAAe,KAAK;wBACzB,MAAM,IAAI,+NAAA,CAAA,wBAAqB,CAAC,aAAa,KAAK;oBACtD,OACK,IAAI,eAAe,KAAK;wBACzB,MAAM,IAAI,+NAAA,CAAA,uBAAoB,CAAC,aAAa,KAAK;oBACrD,OACK,IAAI,YAAY,MAAM;wBACvB,MAAM,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,KAAK;oBAC5C,OACK;wBACD,MAAM,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,aAAa,KAAK;oBAC5C;oBACA,MAAM;gBACV;gBACA,OAAO;YACX,GAAG,CAAC;gBACA,MAAM,IAAI,+NAAA,CAAA,iBAAc,CAAC;oBACrB,SAAS;oBACT,WAAW;oBACX,WAAW,OAAO,CAAC,aAAa;gBACpC;YACJ,GACK,IAAI,CAAC,CAAC;gBACP,IAAI,CAAC,qBAAqB,CAAC,WAAW,cAAc,OAAO,EAAE;gBAC7D,8BAA8B;gBAC9B,MAAM,cAAc,IAAI,cAAc;gBACtC,IAAI,CAAC,2BAA2B,CAAC,aAAa;gBAC9C,OAAO,cAAc,CAAC,cAAc,gBAAgB;oBAChD,YAAY;oBACZ,UAAU;oBACV,OAAO;gBACX;gBACA,SAAS,MAAM;YACnB,GAAG,CAAC,IAAM,SAAS,GAAG;QAC1B;IACJ;IACA,OAAO,gCAAgC,cAAc,EAAE;QACnD,OAAO,CAAC,gDAAgD,EAAE,iBAAiB,IAAI,CAAC,qBAAqB,EAAE,eAAe,OAAO,CAAC,GAAG,IAAI;IACzI;IACA,uIAAuI;IACvI,OAAO,aAAa,GAAG,EAAE,UAAU,EAAE,UAAU,EAAE,KAAK,EAAE;QACpD,IAAI,SACA,eAAe,KACf,2OAAA,CAAA,aAAU,CAAC,6BAA6B,CAAC,QAAQ,CAAC,MAAM,IAAI,GAAG;YAC/D,OAAO;QACX;QACA,yCAAyC;QACzC,IAAI,cAAc,YAAY;YAC1B,OAAO;QACX;QACA,6BAA6B;QAC7B,IAAI,CAAC,KAAK;YACN,OAAO;QACX;QACA,uEAAuE;QACvE,6EAA6E;QAC7E,IAAI,IAAI,UAAU,EAAE,CAAC,sBAAsB,KAAK,SAAS;YACrD,OAAO;QACX;QACA,IAAI,IAAI,UAAU,EAAE,CAAC,sBAAsB,KAAK,QAAQ;YACpD,OAAO;QACX;QACA,4BAA4B;QAC5B,IAAI,IAAI,aAAa,OAAO,KAAK;YAC7B,OAAO;QACX;QACA,gDAAgD;QAChD,EAAE;QACF,iEAAiE;QACjE,wEAAwE;QACxE,oCAAoC;QACpC,IAAI,IAAI,aAAa,MAAM,KAAK;YAC5B,OAAO;QACX;QACA,OAAO;IACX;IACA,kBAAkB,UAAU,EAAE,aAAa,IAAI,EAAE;QAC7C,MAAM,2BAA2B,IAAI,CAAC,OAAO,CAAC,2BAA2B;QACzE,MAAM,uBAAuB,IAAI,CAAC,OAAO,CAAC,uBAAuB;QACjE,iEAAiE;QACjE,2EAA2E;QAC3E,wBAAwB;QACxB,IAAI,eAAe,KAAK,GAAG,CAAC,2BAA2B,KAAK,GAAG,CAAC,GAAG,aAAa,IAAI;QACpF,6DAA6D;QAC7D,wCAAwC;QACxC,gBAAgB,MAAM,CAAC,IAAI,KAAK,MAAM,EAAE;QACxC,oDAAoD;QACpD,eAAe,KAAK,GAAG,CAAC,0BAA0B;QAClD,8FAA8F;QAC9F,IAAI,OAAO,SAAS,CAAC,eAAe,cAAc,sBAAsB;YACpE,eAAe,KAAK,GAAG,CAAC,cAAc;QAC1C;QACA,OAAO,eAAe;IAC1B;IACA,qFAAqF;IACrF,sBAAsB,WAAW,CAAC,CAAC,EAAE;QACjC,OAAO,SAAS,iBAAiB,KAAK,aAClC,OAAO,SAAS,CAAC,SAAS,iBAAiB,IACzC,SAAS,iBAAiB,GAC1B,IAAI,CAAC,OAAO,CAAC,oBAAoB;IAC3C;IACA,uBAAuB,MAAM,EAAE,QAAQ,EAAE,OAAO,EAAE;QAC9C,8EAA8E;QAC9E,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC;QAC9C,MAAM,SAAS,IAAM,CAAC,kBAAkB,EAAE,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI;QACnF,0FAA0F;QAC1F,IAAI,YAAY,MAAM;YAClB,IAAI,WAAW,UAAU,WAAW,UAAU;gBAC1C,OAAO;YACX;QACJ,OACK,IAAI,YAAY,MAAM;YACvB,IAAI,WAAW,UAAU,aAAa,GAAG;gBACrC,OAAO;YACX;QACJ;QACA,OAAO;IACX;IACA,aAAa,EAAE,WAAW,EAAE,aAAa,EAAE,UAAU,EAAE,eAAe,EAAE,MAAM,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,aAAa,EAAE,aAAa,EAAE,OAAO,EAAG,EAAE;QACjK,MAAM,iBAAiB;YACnB,QAAQ;YACR,gBAAgB;YAChB,cAAc,IAAI,CAAC,mBAAmB,CAAC;YACvC,8BAA8B;YAC9B,6BAA6B,IAAI,CAAC,mBAAmB;YACrD,kBAAkB;YAClB,kBAAkB;YAClB,kBAAkB;YAClB,mBAAmB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,sBAAsB;QACjF;QACA,sEAAsE;QACtE,wEAAwE;QACxE,0EAA0E;QAC1E,wEAAwE;QACxE,sEAAsE;QACtE,wEAAwE;QACxE,0EAA0E;QAC1E,qEAAqE;QACrE,UAAU;QACV,EAAE;QACF,yEAAyE;QACzE,4BAA4B;QAC5B,MAAM,mBAAmB,UAAU,UAAU,UAAU,SAAS,UAAU;QAC1E,wEAAwE;QACxE,qEAAqE;QACrE,0EAA0E;QAC1E,0DAA0D;QAC1D,IAAI,oBAAoB,eAAe;YACnC,IAAI,CAAC,kBAAkB;gBACnB,CAAA,GAAA,+NAAA,CAAA,cAAW,AAAD,EAAE,GAAG,OAAO,2EAA2E,CAAC;YACtG;YACA,cAAc,CAAC,iBAAiB,GAAG;QACvC;QACA,OAAO,OAAO,MAAM,CAAC,CAAA,GAAA,+NAAA,CAAA,gBAAa,AAAD,EAAE,iBACnC,4GAA4G;QAC5G,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE;IACrB;IACA,oBAAoB,OAAO,EAAE;QACzB,MAAM,iBAAiB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;QAChD,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,QAAQ,GAC/B,IAAI,CAAC,OAAO,CAAC,kBAAkB,KAC/B;QACN,OAAO,CAAC,OAAO,EAAE,QAAQ,cAAc,EAAE,eAAe,CAAC,EAAE,SAAS,CAAC,IAAI;IAC7E;IACA,sBAAsB;QAClB,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,MAChC,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,GAAG;YAC7C,MAAM,UAAU,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,KAAK;YACtD,OAAO,KAAK,SAAS,CAAC;gBAClB,sBAAsB;YAC1B;QACJ;IACJ;IACA,sBAAsB,SAAS,EAAE,iBAAiB,EAAE,KAAK,EAAE;QACvD,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,MAAM,WAAW;YACjD,IAAI,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,MAAM,GAAG,IAAI,CAAC,yBAAyB,EAAE;gBAC1E,CAAA,GAAA,+NAAA,CAAA,cAAW,AAAD,EAAE;YAChB,OACK;gBACD,MAAM,IAAI;oBACN,YAAY;oBACZ,qBAAqB;gBACzB;gBACA,IAAI,SAAS,MAAM,MAAM,GAAG,GAAG;oBAC3B,EAAE,KAAK,GAAG;gBACd;gBACA,IAAI,CAAC,OAAO,CAAC,mBAAmB,CAAC,IAAI,CAAC;YAC1C;QACJ;IACJ;IACA,YAAY,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE;QACvC,MAAM,iBAAiB,IAAI,QAAQ,CAAC,SAAS;YACzC,IAAI;YACJ,IAAI;gBACA,MAAM,gBAAgB,OAAO,WAAW;gBACxC,IAAI,kBAAkB,UAClB,UACA,OAAO,IAAI,CAAC,QAAQ,MAAM,KAAK,GAAG;oBAClC,MAAM,IAAI,MAAM;gBACpB;gBACA,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;oBAAC;oBAAQ;iBAAQ;gBAC5C,2DAA2D;gBAC3D,MAAM,eAAe,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE;gBACrC,MAAM,OAAO,kBAAkB,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG,gBAAgB;gBAC1E,MAAM,oBAAoB,CAAA,GAAA,+NAAA,CAAA,qBAAkB,AAAD,EAAE;gBAC7C,MAAM,UAAU,kBAAkB,OAAO;gBACzC,oEAAoE;gBACpE,MAAM,gBAAgB,kBAAkB,aAAa;gBACrD,OAAO;oBACH;oBACA,aAAa;oBACb,UAAU;oBACV,WAAW,CAAC;oBACZ;oBACA;oBACA,MAAM,kBAAkB,IAAI;oBAC5B,WAAW,CAAC,CAAC,kBAAkB,SAAS;oBACxC,UAAU,CAAC;oBACX,OAAO;wBAAC;qBAAc;gBAC1B;YACJ,EACA,OAAO,KAAK;gBACR,OAAO;gBACP;YACJ;YACA,SAAS,gBAAgB,GAAG,EAAE,QAAQ;gBAClC,IAAI,KAAK;oBACL,OAAO;gBACX,OACK;oBACD,QAAQ;gBACZ;YACJ;YACA,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;YAC9B,oEAAoE;YACpE,MAAM,gBAAgB,KAAK,aAAa;YACxC,IAAI,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,QAAQ,EAAE,eAAe;gBAAE;gBAAS;gBAAU,WAAW,KAAK,SAAS;YAAC,GAAG,KAAK,KAAK,EAAE;QACnJ;QACA,OAAO;IACX;IACA,SAAS,MAAM,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,aAAa,EAAE,OAAO,EAAE,QAAQ,EAAE,EAAE,QAAQ,EAAE,uBAAuB,IAAI,EAAE;QAC1G,IAAI;QACJ,IAAI;QACJ,gBAAgB,CAAC,KAAK,kBAAkB,QAAQ,kBAAkB,KAAK,IAAI,gBAAgB,IAAI,CAAC,OAAO,CAAC,cAAc,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;QACzJ,MAAM,UAAU,CAAA,GAAA,+NAAA,CAAA,aAAU,AAAD,EAAE;QAC3B,MAAM,eAAe,CAAC,WAAW,YAAY,SAAS,gBAAgB;YAClE,OAAO,WAAW,WAAW,IAAI,CAAC,iBAAiB,CAAC,gBAAgB,aAAa,YAAY,SAAS,iBAAiB;QAC3H;QACA,MAAM,cAAc,CAAC,YAAY,SAAS;YACtC,gFAAgF;YAChF,MAAM,UAAU,QAAQ,QAAQ,IAC5B,QAAQ,QAAQ,CAAC,OAAO,IACxB,OAAO,SAAS,CAAC,QAAQ,QAAQ,CAAC,OAAO,KACzC,QAAQ,QAAQ,CAAC,OAAO,IAAI,IAC1B,QAAQ,QAAQ,CAAC,OAAO,GACxB,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YAC/B,MAAM,UAAU;gBACZ,MAAM,QAAQ,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBACvC,MAAM,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC/B,MAAM;gBACN,QAAQ;gBACR,SAAS,OAAO,MAAM,CAAC,CAAC,GAAG;gBAC3B,MAAM;gBACN,UAAU,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;YACvC;YACA,cAAc,SACT,IAAI,CAAC;gBACN,MAAM,MAAM,IAAI,CAAC,OAAO,CACnB,WAAW,CAAC,cACZ,WAAW,CAAC,QAAQ,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,IAAI,EAAE,QAAQ,MAAM,EAAE,QAAQ,OAAO,EAAE,QAAQ,IAAI,EAAE,QAAQ,QAAQ,EAAE;gBAC5H,MAAM,mBAAmB,KAAK,GAAG;gBACjC,MAAM,eAAe,CAAA,GAAA,+NAAA,CAAA,gBAAa,AAAD,EAAE;oBAC/B,aAAa;oBACb,SAAS,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,CAAC,iBAAiB;oBAC1D,iBAAiB,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE,OAAO,CAAC,kBAAkB;oBACnE;oBACA;oBACA,oBAAoB;gBACxB;gBACA,MAAM,iBAAiB,cAAc;gBACrC,MAAM,aAAa,IAAI,CAAC,qBAAqB,CAAC,QAAQ,QAAQ,IAAI,CAAC;gBACnE,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,WAAW;gBACtC,IACK,IAAI,CAAC,CAAC;oBACP,IAAI,cAAc,YAAY,CAAC,KAAK,gBAAgB,aAAa;wBAC7D,OAAO,aAAa,aAAa,YAAY,SAAS,gBAAgB,CAAA,GAAA,+NAAA,CAAA,0BAAuB,AAAD,EAAE,IAAI,UAAU,EAAE,CAAC,cAAc;oBACjI,OACK,IAAI,QAAQ,SAAS,IAAI,IAAI,aAAa,KAAK,KAAK;wBACrD,OAAO,IAAI,CAAC,yBAAyB,CAAC,cAAc,OAAO,UAAU;oBACzE,OACK;wBACD,OAAO,IAAI,CAAC,oBAAoB,CAAC,cAAc,SAAS,OAAO,UAAU;oBAC7E;gBACJ,GACK,KAAK,CAAC,CAAC;oBACR,IAAI,cAAc,YAAY,CAAC,MAAM,gBAAgB,YAAY,QAAQ;wBACrE,OAAO,aAAa,aAAa,YAAY,SAAS,gBAAgB;oBAC1E,OACK;wBACD,MAAM,iBAAiB,MAAM,IAAI,IAAI,MAAM,IAAI,KAAK,2OAAA,CAAA,aAAU,CAAC,kBAAkB;wBACjF,OAAO,SAAS,IAAI,+NAAA,CAAA,wBAAqB,CAAC;4BACtC,SAAS,iBACH,CAAC,8CAA8C,EAAE,QAAQ,GAAG,CAAC,GAC7D,cAAc,+BAA+B,CAAC;4BACpD,QAAQ;wBACZ;oBACJ;gBACJ;YACJ,GACK,KAAK,CAAC,CAAC;gBACR,MAAM,IAAI,+NAAA,CAAA,cAAW,CAAC;oBAClB,SAAS;oBACT,WAAW;gBACf;YACJ;QACJ;QACA,MAAM,wBAAwB,CAAC,OAAO;YAClC,IAAI,OAAO;gBACP,OAAO,SAAS;YACpB;YACA,cAAc;YACd,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,CAAC;gBAC7B,MAAM,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;gBAC5C,MAAM,UAAU,IAAI,CAAC,YAAY,CAAC;oBAC9B,aAAa,WAAW,OAClB,qBACA;oBACN,eAAe,YAAY,MAAM;oBACjC,YAAY;oBACZ;oBACA;oBACA,qBAAqB,QAAQ,OAAO;oBACpC,sBAAsB,QAAQ,QAAQ;oBACtC,eAAe,WAAW,OAAO,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC;oBACjE,eAAe,WAAW,OAAO,IAAI,CAAC,OAAO,CAAC,WAAW,CAAC,mBAAmB;oBAC7E,SAAS;gBACb;gBACA,YAAY,YAAY,SAAS;YACrC;QACJ;QACA,IAAI,sBAAsB;YACtB,qBAAqB,QAAQ,MAAM,QAAQ,OAAO,EAAE;QACxD,OACK;YACD,IAAI;YACJ,IAAI,WAAW,MAAM;gBACjB,kBAAkB,OAAO,CAAA,GAAA,+NAAA,CAAA,2BAAwB,AAAD,EAAE,QAAQ;YAC9D,OACK;gBACD,kBAAkB,CAAA,GAAA,+NAAA,CAAA,4BAAyB,AAAD,EAAE,QAAQ,CAAC,GAAG;YAC5D;YACA,sBAAsB,MAAM;QAChC;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1564, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/autoPagination.js"], "sourcesContent": ["import { callbackifyPromiseWithTimeout, getDataFromArgs, getAPIMode, } from './utils.js';\nclass V1Iterator {\n    constructor(firstPagePromise, requestArgs, spec, stripeResource) {\n        this.index = 0;\n        this.pagePromise = firstPagePromise;\n        this.promiseCache = { currentPromise: null };\n        this.requestArgs = requestArgs;\n        this.spec = spec;\n        this.stripeResource = stripeResource;\n    }\n    async iterate(pageResult) {\n        if (!(pageResult &&\n            pageResult.data &&\n            typeof pageResult.data.length === 'number')) {\n            throw Error('Unexpected: Stripe API response does not have a well-formed `data` array.');\n        }\n        const reverseIteration = isReverseIteration(this.requestArgs);\n        if (this.index < pageResult.data.length) {\n            const idx = reverseIteration\n                ? pageResult.data.length - 1 - this.index\n                : this.index;\n            const value = pageResult.data[idx];\n            this.index += 1;\n            return { value, done: false };\n        }\n        else if (pageResult.has_more) {\n            // Reset counter, request next page, and recurse.\n            this.index = 0;\n            this.pagePromise = this.getNextPage(pageResult);\n            const nextPageResult = await this.pagePromise;\n            return this.iterate(nextPageResult);\n        }\n        return { done: true, value: undefined };\n    }\n    /** @abstract */\n    getNextPage(_pageResult) {\n        throw new Error('Unimplemented');\n    }\n    async _next() {\n        return this.iterate(await this.pagePromise);\n    }\n    next() {\n        /**\n         * If a user calls `.next()` multiple times in parallel,\n         * return the same result until something has resolved\n         * to prevent page-turning race conditions.\n         */\n        if (this.promiseCache.currentPromise) {\n            return this.promiseCache.currentPromise;\n        }\n        const nextPromise = (async () => {\n            const ret = await this._next();\n            this.promiseCache.currentPromise = null;\n            return ret;\n        })();\n        this.promiseCache.currentPromise = nextPromise;\n        return nextPromise;\n    }\n}\nclass V1ListIterator extends V1Iterator {\n    getNextPage(pageResult) {\n        const reverseIteration = isReverseIteration(this.requestArgs);\n        const lastId = getLastId(pageResult, reverseIteration);\n        return this.stripeResource._makeRequest(this.requestArgs, this.spec, {\n            [reverseIteration ? 'ending_before' : 'starting_after']: lastId,\n        });\n    }\n}\nclass V1SearchIterator extends V1Iterator {\n    getNextPage(pageResult) {\n        if (!pageResult.next_page) {\n            throw Error('Unexpected: Stripe API response does not have a well-formed `next_page` field, but `has_more` was true.');\n        }\n        return this.stripeResource._makeRequest(this.requestArgs, this.spec, {\n            page: pageResult.next_page,\n        });\n    }\n}\nclass V2ListIterator {\n    constructor(firstPagePromise, requestArgs, spec, stripeResource) {\n        this.currentPageIterator = (async () => {\n            const page = await firstPagePromise;\n            return page.data[Symbol.iterator]();\n        })();\n        this.nextPageUrl = (async () => {\n            const page = await firstPagePromise;\n            return page.next_page_url || null;\n        })();\n        this.requestArgs = requestArgs;\n        this.spec = spec;\n        this.stripeResource = stripeResource;\n    }\n    async turnPage() {\n        const nextPageUrl = await this.nextPageUrl;\n        if (!nextPageUrl)\n            return null;\n        this.spec.fullPath = nextPageUrl;\n        const page = await this.stripeResource._makeRequest([], this.spec, {});\n        this.nextPageUrl = Promise.resolve(page.next_page_url);\n        this.currentPageIterator = Promise.resolve(page.data[Symbol.iterator]());\n        return this.currentPageIterator;\n    }\n    async next() {\n        {\n            const result = (await this.currentPageIterator).next();\n            if (!result.done)\n                return { done: false, value: result.value };\n        }\n        const nextPageIterator = await this.turnPage();\n        if (!nextPageIterator) {\n            return { done: true, value: undefined };\n        }\n        const result = nextPageIterator.next();\n        if (!result.done)\n            return { done: false, value: result.value };\n        return { done: true, value: undefined };\n    }\n}\nexport const makeAutoPaginationMethods = (stripeResource, requestArgs, spec, firstPagePromise) => {\n    const apiMode = getAPIMode(spec.fullPath || spec.path);\n    if (apiMode !== 'v2' && spec.methodType === 'search') {\n        return makeAutoPaginationMethodsFromIterator(new V1SearchIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    if (apiMode !== 'v2' && spec.methodType === 'list') {\n        return makeAutoPaginationMethodsFromIterator(new V1ListIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    if (apiMode === 'v2' && spec.methodType === 'list') {\n        return makeAutoPaginationMethodsFromIterator(new V2ListIterator(firstPagePromise, requestArgs, spec, stripeResource));\n    }\n    return null;\n};\nconst makeAutoPaginationMethodsFromIterator = (iterator) => {\n    const autoPagingEach = makeAutoPagingEach((...args) => iterator.next(...args));\n    const autoPagingToArray = makeAutoPagingToArray(autoPagingEach);\n    const autoPaginationMethods = {\n        autoPagingEach,\n        autoPagingToArray,\n        // Async iterator functions:\n        next: () => iterator.next(),\n        return: () => {\n            // This is required for `break`.\n            return {};\n        },\n        [getAsyncIteratorSymbol()]: () => {\n            return autoPaginationMethods;\n        },\n    };\n    return autoPaginationMethods;\n};\n/**\n * ----------------\n * Private Helpers:\n * ----------------\n */\nfunction getAsyncIteratorSymbol() {\n    if (typeof Symbol !== 'undefined' && Symbol.asyncIterator) {\n        return Symbol.asyncIterator;\n    }\n    // Follow the convention from libraries like iterall: https://github.com/leebyron/iterall#asynciterator-1\n    return '@@asyncIterator';\n}\nfunction getDoneCallback(args) {\n    if (args.length < 2) {\n        return null;\n    }\n    const onDone = args[1];\n    if (typeof onDone !== 'function') {\n        throw Error(`The second argument to autoPagingEach, if present, must be a callback function; received ${typeof onDone}`);\n    }\n    return onDone;\n}\n/**\n * We allow four forms of the `onItem` callback (the middle two being equivalent),\n *\n *   1. `.autoPagingEach((item) => { doSomething(item); return false; });`\n *   2. `.autoPagingEach(async (item) => { await doSomething(item); return false; });`\n *   3. `.autoPagingEach((item) => doSomething(item).then(() => false));`\n *   4. `.autoPagingEach((item, next) => { doSomething(item); next(false); });`\n *\n * In addition to standard validation, this helper\n * coalesces the former forms into the latter form.\n */\nfunction getItemCallback(args) {\n    if (args.length === 0) {\n        return undefined;\n    }\n    const onItem = args[0];\n    if (typeof onItem !== 'function') {\n        throw Error(`The first argument to autoPagingEach, if present, must be a callback function; received ${typeof onItem}`);\n    }\n    // 4. `.autoPagingEach((item, next) => { doSomething(item); next(false); });`\n    if (onItem.length === 2) {\n        return onItem;\n    }\n    if (onItem.length > 2) {\n        throw Error(`The \\`onItem\\` callback function passed to autoPagingEach must accept at most two arguments; got ${onItem}`);\n    }\n    // This magically handles all three of these usecases (the latter two being functionally identical):\n    // 1. `.autoPagingEach((item) => { doSomething(item); return false; });`\n    // 2. `.autoPagingEach(async (item) => { await doSomething(item); return false; });`\n    // 3. `.autoPagingEach((item) => doSomething(item).then(() => false));`\n    return function _onItem(item, next) {\n        const shouldContinue = onItem(item);\n        next(shouldContinue);\n    };\n}\nfunction getLastId(listResult, reverseIteration) {\n    const lastIdx = reverseIteration ? 0 : listResult.data.length - 1;\n    const lastItem = listResult.data[lastIdx];\n    const lastId = lastItem && lastItem.id;\n    if (!lastId) {\n        throw Error('Unexpected: No `id` found on the last item while auto-paging a list.');\n    }\n    return lastId;\n}\nfunction makeAutoPagingEach(asyncIteratorNext) {\n    return function autoPagingEach( /* onItem?, onDone? */) {\n        const args = [].slice.call(arguments);\n        const onItem = getItemCallback(args);\n        const onDone = getDoneCallback(args);\n        if (args.length > 2) {\n            throw Error(`autoPagingEach takes up to two arguments; received ${args}`);\n        }\n        const autoPagePromise = wrapAsyncIteratorWithCallback(asyncIteratorNext, \n        // @ts-ignore we might need a null check\n        onItem);\n        return callbackifyPromiseWithTimeout(autoPagePromise, onDone);\n    };\n}\nfunction makeAutoPagingToArray(autoPagingEach) {\n    return function autoPagingToArray(opts, onDone) {\n        const limit = opts && opts.limit;\n        if (!limit) {\n            throw Error('You must pass a `limit` option to autoPagingToArray, e.g., `autoPagingToArray({limit: 1000});`.');\n        }\n        if (limit > 10000) {\n            throw Error('You cannot specify a limit of more than 10,000 items to fetch in `autoPagingToArray`; use `autoPagingEach` to iterate through longer lists.');\n        }\n        const promise = new Promise((resolve, reject) => {\n            const items = [];\n            autoPagingEach((item) => {\n                items.push(item);\n                if (items.length >= limit) {\n                    return false;\n                }\n            })\n                .then(() => {\n                resolve(items);\n            })\n                .catch(reject);\n        });\n        // @ts-ignore\n        return callbackifyPromiseWithTimeout(promise, onDone);\n    };\n}\nfunction wrapAsyncIteratorWithCallback(asyncIteratorNext, onItem) {\n    return new Promise((resolve, reject) => {\n        function handleIteration(iterResult) {\n            if (iterResult.done) {\n                resolve();\n                return;\n            }\n            const item = iterResult.value;\n            return new Promise((next) => {\n                // Bit confusing, perhaps; we pass a `resolve` fn\n                // to the user, so they can decide when and if to continue.\n                // They can return false, or a promise which resolves to false, to break.\n                onItem(item, next);\n            }).then((shouldContinue) => {\n                if (shouldContinue === false) {\n                    return handleIteration({ done: true, value: undefined });\n                }\n                else {\n                    return asyncIteratorNext().then(handleIteration);\n                }\n            });\n        }\n        asyncIteratorNext()\n            .then(handleIteration)\n            .catch(reject);\n    });\n}\nfunction isReverseIteration(requestArgs) {\n    const args = [].slice.call(requestArgs);\n    const dataFromArgs = getDataFromArgs(args);\n    return !!dataFromArgs.ending_before;\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,MAAM;IACF,YAAY,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,CAAE;QAC7D,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,YAAY,GAAG;YAAE,gBAAgB;QAAK;QAC3C,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,MAAM,QAAQ,UAAU,EAAE;QACtB,IAAI,CAAC,CAAC,cACF,WAAW,IAAI,IACf,OAAO,WAAW,IAAI,CAAC,MAAM,KAAK,QAAQ,GAAG;YAC7C,MAAM,MAAM;QAChB;QACA,MAAM,mBAAmB,mBAAmB,IAAI,CAAC,WAAW;QAC5D,IAAI,IAAI,CAAC,KAAK,GAAG,WAAW,IAAI,CAAC,MAAM,EAAE;YACrC,MAAM,MAAM,mBACN,WAAW,IAAI,CAAC,MAAM,GAAG,IAAI,IAAI,CAAC,KAAK,GACvC,IAAI,CAAC,KAAK;YAChB,MAAM,QAAQ,WAAW,IAAI,CAAC,IAAI;YAClC,IAAI,CAAC,KAAK,IAAI;YACd,OAAO;gBAAE;gBAAO,MAAM;YAAM;QAChC,OACK,IAAI,WAAW,QAAQ,EAAE;YAC1B,iDAAiD;YACjD,IAAI,CAAC,KAAK,GAAG;YACb,IAAI,CAAC,WAAW,GAAG,IAAI,CAAC,WAAW,CAAC;YACpC,MAAM,iBAAiB,MAAM,IAAI,CAAC,WAAW;YAC7C,OAAO,IAAI,CAAC,OAAO,CAAC;QACxB;QACA,OAAO;YAAE,MAAM;YAAM,OAAO;QAAU;IAC1C;IACA,cAAc,GACd,YAAY,WAAW,EAAE;QACrB,MAAM,IAAI,MAAM;IACpB;IACA,MAAM,QAAQ;QACV,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,IAAI,CAAC,WAAW;IAC9C;IACA,OAAO;QACH;;;;SAIC,GACD,IAAI,IAAI,CAAC,YAAY,CAAC,cAAc,EAAE;YAClC,OAAO,IAAI,CAAC,YAAY,CAAC,cAAc;QAC3C;QACA,MAAM,cAAc,CAAC;YACjB,MAAM,MAAM,MAAM,IAAI,CAAC,KAAK;YAC5B,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG;YACnC,OAAO;QACX,CAAC;QACD,IAAI,CAAC,YAAY,CAAC,cAAc,GAAG;QACnC,OAAO;IACX;AACJ;AACA,MAAM,uBAAuB;IACzB,YAAY,UAAU,EAAE;QACpB,MAAM,mBAAmB,mBAAmB,IAAI,CAAC,WAAW;QAC5D,MAAM,SAAS,UAAU,YAAY;QACrC,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE;YACjE,CAAC,mBAAmB,kBAAkB,iBAAiB,EAAE;QAC7D;IACJ;AACJ;AACA,MAAM,yBAAyB;IAC3B,YAAY,UAAU,EAAE;QACpB,IAAI,CAAC,WAAW,SAAS,EAAE;YACvB,MAAM,MAAM;QAChB;QACA,OAAO,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,IAAI,CAAC,WAAW,EAAE,IAAI,CAAC,IAAI,EAAE;YACjE,MAAM,WAAW,SAAS;QAC9B;IACJ;AACJ;AACA,MAAM;IACF,YAAY,gBAAgB,EAAE,WAAW,EAAE,IAAI,EAAE,cAAc,CAAE;QAC7D,IAAI,CAAC,mBAAmB,GAAG,CAAC;YACxB,MAAM,OAAO,MAAM;YACnB,OAAO,KAAK,IAAI,CAAC,OAAO,QAAQ,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG,CAAC;YAChB,MAAM,OAAO,MAAM;YACnB,OAAO,KAAK,aAAa,IAAI;QACjC,CAAC;QACD,IAAI,CAAC,WAAW,GAAG;QACnB,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,cAAc,GAAG;IAC1B;IACA,MAAM,WAAW;QACb,MAAM,cAAc,MAAM,IAAI,CAAC,WAAW;QAC1C,IAAI,CAAC,aACD,OAAO;QACX,IAAI,CAAC,IAAI,CAAC,QAAQ,GAAG;QACrB,MAAM,OAAO,MAAM,IAAI,CAAC,cAAc,CAAC,YAAY,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,EAAE,CAAC;QACpE,IAAI,CAAC,WAAW,GAAG,QAAQ,OAAO,CAAC,KAAK,aAAa;QACrD,IAAI,CAAC,mBAAmB,GAAG,QAAQ,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,QAAQ,CAAC;QACrE,OAAO,IAAI,CAAC,mBAAmB;IACnC;IACA,MAAM,OAAO;QACT;YACI,MAAM,SAAS,CAAC,MAAM,IAAI,CAAC,mBAAmB,EAAE,IAAI;YACpD,IAAI,CAAC,OAAO,IAAI,EACZ,OAAO;gBAAE,MAAM;gBAAO,OAAO,OAAO,KAAK;YAAC;QAClD;QACA,MAAM,mBAAmB,MAAM,IAAI,CAAC,QAAQ;QAC5C,IAAI,CAAC,kBAAkB;YACnB,OAAO;gBAAE,MAAM;gBAAM,OAAO;YAAU;QAC1C;QACA,MAAM,SAAS,iBAAiB,IAAI;QACpC,IAAI,CAAC,OAAO,IAAI,EACZ,OAAO;YAAE,MAAM;YAAO,OAAO,OAAO,KAAK;QAAC;QAC9C,OAAO;YAAE,MAAM;YAAM,OAAO;QAAU;IAC1C;AACJ;AACO,MAAM,4BAA4B,CAAC,gBAAgB,aAAa,MAAM;IACzE,MAAM,UAAU,CAAA,GAAA,+NAAA,CAAA,aAAU,AAAD,EAAE,KAAK,QAAQ,IAAI,KAAK,IAAI;IACrD,IAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,UAAU;QAClD,OAAO,sCAAsC,IAAI,iBAAiB,kBAAkB,aAAa,MAAM;IAC3G;IACA,IAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,QAAQ;QAChD,OAAO,sCAAsC,IAAI,eAAe,kBAAkB,aAAa,MAAM;IACzG;IACA,IAAI,YAAY,QAAQ,KAAK,UAAU,KAAK,QAAQ;QAChD,OAAO,sCAAsC,IAAI,eAAe,kBAAkB,aAAa,MAAM;IACzG;IACA,OAAO;AACX;AACA,MAAM,wCAAwC,CAAC;IAC3C,MAAM,iBAAiB,mBAAmB,CAAC,GAAG,OAAS,SAAS,IAAI,IAAI;IACxE,MAAM,oBAAoB,sBAAsB;IAChD,MAAM,wBAAwB;QAC1B;QACA;QACA,4BAA4B;QAC5B,MAAM,IAAM,SAAS,IAAI;QACzB,QAAQ;YACJ,gCAAgC;YAChC,OAAO,CAAC;QACZ;QACA,CAAC,yBAAyB,EAAE;YACxB,OAAO;QACX;IACJ;IACA,OAAO;AACX;AACA;;;;CAIC,GACD,SAAS;IACL,IAAI,OAAO,WAAW,eAAe,OAAO,aAAa,EAAE;QACvD,OAAO,OAAO,aAAa;IAC/B;IACA,yGAAyG;IACzG,OAAO;AACX;AACA,SAAS,gBAAgB,IAAI;IACzB,IAAI,KAAK,MAAM,GAAG,GAAG;QACjB,OAAO;IACX;IACA,MAAM,SAAS,IAAI,CAAC,EAAE;IACtB,IAAI,OAAO,WAAW,YAAY;QAC9B,MAAM,MAAM,CAAC,yFAAyF,EAAE,OAAO,QAAQ;IAC3H;IACA,OAAO;AACX;AACA;;;;;;;;;;CAUC,GACD,SAAS,gBAAgB,IAAI;IACzB,IAAI,KAAK,MAAM,KAAK,GAAG;QACnB,OAAO;IACX;IACA,MAAM,SAAS,IAAI,CAAC,EAAE;IACtB,IAAI,OAAO,WAAW,YAAY;QAC9B,MAAM,MAAM,CAAC,wFAAwF,EAAE,OAAO,QAAQ;IAC1H;IACA,6EAA6E;IAC7E,IAAI,OAAO,MAAM,KAAK,GAAG;QACrB,OAAO;IACX;IACA,IAAI,OAAO,MAAM,GAAG,GAAG;QACnB,MAAM,MAAM,CAAC,iGAAiG,EAAE,QAAQ;IAC5H;IACA,oGAAoG;IACpG,wEAAwE;IACxE,oFAAoF;IACpF,uEAAuE;IACvE,OAAO,SAAS,QAAQ,IAAI,EAAE,IAAI;QAC9B,MAAM,iBAAiB,OAAO;QAC9B,KAAK;IACT;AACJ;AACA,SAAS,UAAU,UAAU,EAAE,gBAAgB;IAC3C,MAAM,UAAU,mBAAmB,IAAI,WAAW,IAAI,CAAC,MAAM,GAAG;IAChE,MAAM,WAAW,WAAW,IAAI,CAAC,QAAQ;IACzC,MAAM,SAAS,YAAY,SAAS,EAAE;IACtC,IAAI,CAAC,QAAQ;QACT,MAAM,MAAM;IAChB;IACA,OAAO;AACX;AACA,SAAS,mBAAmB,iBAAiB;IACzC,OAAO,SAAS;QACZ,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,MAAM,SAAS,gBAAgB;QAC/B,MAAM,SAAS,gBAAgB;QAC/B,IAAI,KAAK,MAAM,GAAG,GAAG;YACjB,MAAM,MAAM,CAAC,mDAAmD,EAAE,MAAM;QAC5E;QACA,MAAM,kBAAkB,8BAA8B,mBACtD,wCAAwC;QACxC;QACA,OAAO,CAAA,GAAA,+NAAA,CAAA,gCAA6B,AAAD,EAAE,iBAAiB;IAC1D;AACJ;AACA,SAAS,sBAAsB,cAAc;IACzC,OAAO,SAAS,kBAAkB,IAAI,EAAE,MAAM;QAC1C,MAAM,QAAQ,QAAQ,KAAK,KAAK;QAChC,IAAI,CAAC,OAAO;YACR,MAAM,MAAM;QAChB;QACA,IAAI,QAAQ,OAAO;YACf,MAAM,MAAM;QAChB;QACA,MAAM,UAAU,IAAI,QAAQ,CAAC,SAAS;YAClC,MAAM,QAAQ,EAAE;YAChB,eAAe,CAAC;gBACZ,MAAM,IAAI,CAAC;gBACX,IAAI,MAAM,MAAM,IAAI,OAAO;oBACvB,OAAO;gBACX;YACJ,GACK,IAAI,CAAC;gBACN,QAAQ;YACZ,GACK,KAAK,CAAC;QACf;QACA,aAAa;QACb,OAAO,CAAA,GAAA,+NAAA,CAAA,gCAA6B,AAAD,EAAE,SAAS;IAClD;AACJ;AACA,SAAS,8BAA8B,iBAAiB,EAAE,MAAM;IAC5D,OAAO,IAAI,QAAQ,CAAC,SAAS;QACzB,SAAS,gBAAgB,UAAU;YAC/B,IAAI,WAAW,IAAI,EAAE;gBACjB;gBACA;YACJ;YACA,MAAM,OAAO,WAAW,KAAK;YAC7B,OAAO,IAAI,QAAQ,CAAC;gBAChB,iDAAiD;gBACjD,2DAA2D;gBAC3D,yEAAyE;gBACzE,OAAO,MAAM;YACjB,GAAG,IAAI,CAAC,CAAC;gBACL,IAAI,mBAAmB,OAAO;oBAC1B,OAAO,gBAAgB;wBAAE,MAAM;wBAAM,OAAO;oBAAU;gBAC1D,OACK;oBACD,OAAO,oBAAoB,IAAI,CAAC;gBACpC;YACJ;QACJ;QACA,oBACK,IAAI,CAAC,iBACL,KAAK,CAAC;IACf;AACJ;AACA,SAAS,mBAAmB,WAAW;IACnC,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;IAC3B,MAAM,eAAe,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE;IACrC,OAAO,CAAC,CAAC,aAAa,aAAa;AACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/StripeMethod.js"], "sourcesContent": ["import { callbackifyPromiseWithTimeout, extractUrlParams } from './utils.js';\nimport { makeAutoPaginationMethods } from './autoPagination.js';\n/**\n * Create an API method from the declared spec.\n *\n * @param [spec.method='GET'] Request Method (POST, GET, DELETE, PUT)\n * @param [spec.path=''] Path to be appended to the API BASE_PATH, joined with\n *  the instance's path (e.g. 'charges' or 'customers')\n * @param [spec.fullPath=''] Fully qualified path to the method (eg. /v1/a/b/c).\n *  If this is specified, path should not be specified.\n * @param [spec.urlParams=[]] Array of required arguments in the order that they\n *  must be passed by the consumer of the API. Subsequent optional arguments are\n *  optionally passed through a hash (Object) as the penultimate argument\n *  (preceding the also-optional callback argument\n * @param [spec.encode] Function for mutating input parameters to a method.\n *  Usefully for applying transforms to data on a per-method basis.\n * @param [spec.host] Hostname for the request.\n *\n * <!-- Public API accessible via Stripe.StripeResource.method -->\n */\nexport function stripeMethod(spec) {\n    if (spec.path !== undefined && spec.fullPath !== undefined) {\n        throw new Error(`Method spec specified both a 'path' (${spec.path}) and a 'fullPath' (${spec.fullPath}).`);\n    }\n    return function (...args) {\n        const callback = typeof args[args.length - 1] == 'function' && args.pop();\n        spec.urlParams = extractUrlParams(spec.fullPath || this.createResourcePathWithSymbols(spec.path || ''));\n        const requestPromise = callbackifyPromiseWithTimeout(this._makeRequest(args, spec, {}), callback);\n        Object.assign(requestPromise, makeAutoPaginationMethods(this, args, spec, requestPromise));\n        return requestPromise;\n    };\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAmBO,SAAS,aAAa,IAAI;IAC7B,IAAI,KAAK,IAAI,KAAK,aAAa,KAAK,QAAQ,KAAK,WAAW;QACxD,MAAM,IAAI,MAAM,CAAC,qCAAqC,EAAE,KAAK,IAAI,CAAC,oBAAoB,EAAE,KAAK,QAAQ,CAAC,EAAE,CAAC;IAC7G;IACA,OAAO,SAAU,GAAG,IAAI;QACpB,MAAM,WAAW,OAAO,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE,IAAI,cAAc,KAAK,GAAG;QACvE,KAAK,SAAS,GAAG,CAAA,GAAA,+NAAA,CAAA,mBAAgB,AAAD,EAAE,KAAK,QAAQ,IAAI,IAAI,CAAC,6BAA6B,CAAC,KAAK,IAAI,IAAI;QACnG,MAAM,iBAAiB,CAAA,GAAA,+NAAA,CAAA,gCAA6B,AAAD,EAAE,IAAI,CAAC,YAAY,CAAC,MAAM,MAAM,CAAC,IAAI;QACxF,OAAO,MAAM,CAAC,gBAAgB,CAAA,GAAA,wOAAA,CAAA,4BAAyB,AAAD,EAAE,IAAI,EAAE,MAAM,MAAM;QAC1E,OAAO;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/StripeResource.js"], "sourcesContent": ["import { getDataFromArgs, getOptionsFromArgs, makeURLInterpolator, protoExtend, queryStringifyRequestData, getAPIMode, } from './utils.js';\nimport { stripeMethod } from './StripeMethod.js';\n// Provide extension mechanism for Stripe Resource Sub-Classes\nStripeResource.extend = protoExtend;\n// Expose method-creator\nStripeResource.method = stripeMethod;\nStripeResource.MAX_BUFFERED_REQUEST_METRICS = 100;\n/**\n * Encapsulates request logic for a Stripe Resource\n */\nfunction StripeResource(stripe, deprecatedUrlData) {\n    this._stripe = stripe;\n    if (deprecatedUrlData) {\n        throw new Error('Support for curried url params was dropped in stripe-node v7.0.0. Instead, pass two ids.');\n    }\n    this.basePath = makeURLInterpolator(\n    // @ts-ignore changing type of basePath\n    this.basePath || stripe.getApiField('basePath'));\n    // @ts-ignore changing type of path\n    this.resourcePath = this.path;\n    // @ts-ignore changing type of path\n    this.path = makeURLInterpolator(this.path);\n    this.initialize(...arguments);\n}\nStripeResource.prototype = {\n    _stripe: null,\n    // @ts-ignore the type of path changes in ctor\n    path: '',\n    resourcePath: '',\n    // Methods that don't use the API's default '/v1' path can override it with this setting.\n    basePath: null,\n    initialize() { },\n    // Function to override the default data processor. This allows full control\n    // over how a StripeResource's request data will get converted into an HTTP\n    // body. This is useful for non-standard HTTP requests. The function should\n    // take method name, data, and headers as arguments.\n    requestDataProcessor: null,\n    // Function to add a validation checks before sending the request, errors should\n    // be thrown, and they will be passed to the callback/promise.\n    validateRequest: null,\n    createFullPath(commandPath, urlData) {\n        const urlParts = [this.basePath(urlData), this.path(urlData)];\n        if (typeof commandPath === 'function') {\n            const computedCommandPath = commandPath(urlData);\n            // If we have no actual command path, we just omit it to avoid adding a\n            // trailing slash. This is important for top-level listing requests, which\n            // do not have a command path.\n            if (computedCommandPath) {\n                urlParts.push(computedCommandPath);\n            }\n        }\n        else {\n            urlParts.push(commandPath);\n        }\n        return this._joinUrlParts(urlParts);\n    },\n    // Creates a relative resource path with symbols left in (unlike\n    // createFullPath which takes some data to replace them with). For example it\n    // might produce: /invoices/{id}\n    createResourcePathWithSymbols(pathWithSymbols) {\n        // If there is no path beyond the resource path, we want to produce just\n        // /<resource path> rather than /<resource path>/.\n        if (pathWithSymbols) {\n            return `/${this._joinUrlParts([this.resourcePath, pathWithSymbols])}`;\n        }\n        else {\n            return `/${this.resourcePath}`;\n        }\n    },\n    _joinUrlParts(parts) {\n        // Replace any accidentally doubled up slashes. This previously used\n        // path.join, which would do this as well. Unfortunately we need to do this\n        // as the functions for creating paths are technically part of the public\n        // interface and so we need to preserve backwards compatibility.\n        return parts.join('/').replace(/\\/{2,}/g, '/');\n    },\n    _getRequestOpts(requestArgs, spec, overrideData) {\n        var _a;\n        // Extract spec values with defaults.\n        const requestMethod = (spec.method || 'GET').toUpperCase();\n        const usage = spec.usage || [];\n        const urlParams = spec.urlParams || [];\n        const encode = spec.encode || ((data) => data);\n        const isUsingFullPath = !!spec.fullPath;\n        const commandPath = makeURLInterpolator(isUsingFullPath ? spec.fullPath : spec.path || '');\n        // When using fullPath, we ignore the resource path as it should already be\n        // fully qualified.\n        const path = isUsingFullPath\n            ? spec.fullPath\n            : this.createResourcePathWithSymbols(spec.path);\n        // Don't mutate args externally.\n        const args = [].slice.call(requestArgs);\n        // Generate and validate url params.\n        const urlData = urlParams.reduce((urlData, param) => {\n            const arg = args.shift();\n            if (typeof arg !== 'string') {\n                throw new Error(`Stripe: Argument \"${param}\" must be a string, but got: ${arg} (on API request to \\`${requestMethod} ${path}\\`)`);\n            }\n            urlData[param] = arg;\n            return urlData;\n        }, {});\n        // Pull request data and options (headers, auth) from args.\n        const dataFromArgs = getDataFromArgs(args);\n        const data = encode(Object.assign({}, dataFromArgs, overrideData));\n        const options = getOptionsFromArgs(args);\n        const host = options.host || spec.host;\n        const streaming = !!spec.streaming || !!options.streaming;\n        // Validate that there are no more args.\n        if (args.filter((x) => x != null).length) {\n            throw new Error(`Stripe: Unknown arguments (${args}). Did you mean to pass an options object? See https://github.com/stripe/stripe-node/wiki/Passing-Options. (on API request to ${requestMethod} \\`${path}\\`)`);\n        }\n        // When using full path, we can just invoke the URL interpolator directly\n        // as we don't need to use the resource to create a full path.\n        const requestPath = isUsingFullPath\n            ? commandPath(urlData)\n            : this.createFullPath(commandPath, urlData);\n        const headers = Object.assign(options.headers, spec.headers);\n        if (spec.validator) {\n            spec.validator(data, { headers });\n        }\n        const dataInQuery = spec.method === 'GET' || spec.method === 'DELETE';\n        const bodyData = dataInQuery ? null : data;\n        const queryData = dataInQuery ? data : {};\n        return {\n            requestMethod,\n            requestPath,\n            bodyData,\n            queryData,\n            authenticator: (_a = options.authenticator) !== null && _a !== void 0 ? _a : null,\n            headers,\n            host: host !== null && host !== void 0 ? host : null,\n            streaming,\n            settings: options.settings,\n            usage,\n        };\n    },\n    _makeRequest(requestArgs, spec, overrideData) {\n        return new Promise((resolve, reject) => {\n            var _a;\n            let opts;\n            try {\n                opts = this._getRequestOpts(requestArgs, spec, overrideData);\n            }\n            catch (err) {\n                reject(err);\n                return;\n            }\n            function requestCallback(err, response) {\n                if (err) {\n                    reject(err);\n                }\n                else {\n                    resolve(spec.transformResponseData\n                        ? spec.transformResponseData(response)\n                        : response);\n                }\n            }\n            const emptyQuery = Object.keys(opts.queryData).length === 0;\n            const path = [\n                opts.requestPath,\n                emptyQuery ? '' : '?',\n                queryStringifyRequestData(opts.queryData, getAPIMode(opts.requestPath)),\n            ].join('');\n            const { headers, settings } = opts;\n            this._stripe._requestSender._request(opts.requestMethod, opts.host, path, opts.bodyData, opts.authenticator, {\n                headers,\n                settings,\n                streaming: opts.streaming,\n            }, opts.usage, requestCallback, (_a = this.requestDataProcessor) === null || _a === void 0 ? void 0 : _a.bind(this));\n        });\n    },\n};\nexport { StripeResource };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,8DAA8D;AAC9D,eAAe,MAAM,GAAG,+NAAA,CAAA,cAAW;AACnC,wBAAwB;AACxB,eAAe,MAAM,GAAG,sOAAA,CAAA,eAAY;AACpC,eAAe,4BAA4B,GAAG;AAC9C;;CAEC,GACD,SAAS,eAAe,MAAM,EAAE,iBAAiB;IAC7C,IAAI,CAAC,OAAO,GAAG;IACf,IAAI,mBAAmB;QACnB,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,CAAC,QAAQ,GAAG,CAAA,GAAA,+NAAA,CAAA,sBAAmB,AAAD,EAClC,uCAAuC;IACvC,IAAI,CAAC,QAAQ,IAAI,OAAO,WAAW,CAAC;IACpC,mCAAmC;IACnC,IAAI,CAAC,YAAY,GAAG,IAAI,CAAC,IAAI;IAC7B,mCAAmC;IACnC,IAAI,CAAC,IAAI,GAAG,CAAA,GAAA,+NAAA,CAAA,sBAAmB,AAAD,EAAE,IAAI,CAAC,IAAI;IACzC,IAAI,CAAC,UAAU,IAAI;AACvB;AACA,eAAe,SAAS,GAAG;IACvB,SAAS;IACT,8CAA8C;IAC9C,MAAM;IACN,cAAc;IACd,yFAAyF;IACzF,UAAU;IACV,eAAe;IACf,4EAA4E;IAC5E,2EAA2E;IAC3E,2EAA2E;IAC3E,oDAAoD;IACpD,sBAAsB;IACtB,gFAAgF;IAChF,8DAA8D;IAC9D,iBAAiB;IACjB,gBAAe,WAAW,EAAE,OAAO;QAC/B,MAAM,WAAW;YAAC,IAAI,CAAC,QAAQ,CAAC;YAAU,IAAI,CAAC,IAAI,CAAC;SAAS;QAC7D,IAAI,OAAO,gBAAgB,YAAY;YACnC,MAAM,sBAAsB,YAAY;YACxC,uEAAuE;YACvE,0EAA0E;YAC1E,8BAA8B;YAC9B,IAAI,qBAAqB;gBACrB,SAAS,IAAI,CAAC;YAClB;QACJ,OACK;YACD,SAAS,IAAI,CAAC;QAClB;QACA,OAAO,IAAI,CAAC,aAAa,CAAC;IAC9B;IACA,gEAAgE;IAChE,6EAA6E;IAC7E,gCAAgC;IAChC,+BAA8B,eAAe;QACzC,wEAAwE;QACxE,kDAAkD;QAClD,IAAI,iBAAiB;YACjB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,aAAa,CAAC;gBAAC,IAAI,CAAC,YAAY;gBAAE;aAAgB,GAAG;QACzE,OACK;YACD,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC,YAAY,EAAE;QAClC;IACJ;IACA,eAAc,KAAK;QACf,oEAAoE;QACpE,2EAA2E;QAC3E,yEAAyE;QACzE,gEAAgE;QAChE,OAAO,MAAM,IAAI,CAAC,KAAK,OAAO,CAAC,WAAW;IAC9C;IACA,iBAAgB,WAAW,EAAE,IAAI,EAAE,YAAY;QAC3C,IAAI;QACJ,qCAAqC;QACrC,MAAM,gBAAgB,CAAC,KAAK,MAAM,IAAI,KAAK,EAAE,WAAW;QACxD,MAAM,QAAQ,KAAK,KAAK,IAAI,EAAE;QAC9B,MAAM,YAAY,KAAK,SAAS,IAAI,EAAE;QACtC,MAAM,SAAS,KAAK,MAAM,IAAI,CAAC,CAAC,OAAS,IAAI;QAC7C,MAAM,kBAAkB,CAAC,CAAC,KAAK,QAAQ;QACvC,MAAM,cAAc,CAAA,GAAA,+NAAA,CAAA,sBAAmB,AAAD,EAAE,kBAAkB,KAAK,QAAQ,GAAG,KAAK,IAAI,IAAI;QACvF,2EAA2E;QAC3E,mBAAmB;QACnB,MAAM,OAAO,kBACP,KAAK,QAAQ,GACb,IAAI,CAAC,6BAA6B,CAAC,KAAK,IAAI;QAClD,gCAAgC;QAChC,MAAM,OAAO,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC;QAC3B,oCAAoC;QACpC,MAAM,UAAU,UAAU,MAAM,CAAC,CAAC,SAAS;YACvC,MAAM,MAAM,KAAK,KAAK;YACtB,IAAI,OAAO,QAAQ,UAAU;gBACzB,MAAM,IAAI,MAAM,CAAC,kBAAkB,EAAE,MAAM,6BAA6B,EAAE,IAAI,sBAAsB,EAAE,cAAc,CAAC,EAAE,KAAK,GAAG,CAAC;YACpI;YACA,OAAO,CAAC,MAAM,GAAG;YACjB,OAAO;QACX,GAAG,CAAC;QACJ,2DAA2D;QAC3D,MAAM,eAAe,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE;QACrC,MAAM,OAAO,OAAO,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;QACpD,MAAM,UAAU,CAAA,GAAA,+NAAA,CAAA,qBAAkB,AAAD,EAAE;QACnC,MAAM,OAAO,QAAQ,IAAI,IAAI,KAAK,IAAI;QACtC,MAAM,YAAY,CAAC,CAAC,KAAK,SAAS,IAAI,CAAC,CAAC,QAAQ,SAAS;QACzD,wCAAwC;QACxC,IAAI,KAAK,MAAM,CAAC,CAAC,IAAM,KAAK,MAAM,MAAM,EAAE;YACtC,MAAM,IAAI,MAAM,CAAC,2BAA2B,EAAE,KAAK,8HAA8H,EAAE,cAAc,GAAG,EAAE,KAAK,GAAG,CAAC;QACnN;QACA,yEAAyE;QACzE,8DAA8D;QAC9D,MAAM,cAAc,kBACd,YAAY,WACZ,IAAI,CAAC,cAAc,CAAC,aAAa;QACvC,MAAM,UAAU,OAAO,MAAM,CAAC,QAAQ,OAAO,EAAE,KAAK,OAAO;QAC3D,IAAI,KAAK,SAAS,EAAE;YAChB,KAAK,SAAS,CAAC,MAAM;gBAAE;YAAQ;QACnC;QACA,MAAM,cAAc,KAAK,MAAM,KAAK,SAAS,KAAK,MAAM,KAAK;QAC7D,MAAM,WAAW,cAAc,OAAO;QACtC,MAAM,YAAY,cAAc,OAAO,CAAC;QACxC,OAAO;YACH;YACA;YACA;YACA;YACA,eAAe,CAAC,KAAK,QAAQ,aAAa,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;YAC7E;YACA,MAAM,SAAS,QAAQ,SAAS,KAAK,IAAI,OAAO;YAChD;YACA,UAAU,QAAQ,QAAQ;YAC1B;QACJ;IACJ;IACA,cAAa,WAAW,EAAE,IAAI,EAAE,YAAY;QACxC,OAAO,IAAI,QAAQ,CAAC,SAAS;YACzB,IAAI;YACJ,IAAI;YACJ,IAAI;gBACA,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,MAAM;YACnD,EACA,OAAO,KAAK;gBACR,OAAO;gBACP;YACJ;YACA,SAAS,gBAAgB,GAAG,EAAE,QAAQ;gBAClC,IAAI,KAAK;oBACL,OAAO;gBACX,OACK;oBACD,QAAQ,KAAK,qBAAqB,GAC5B,KAAK,qBAAqB,CAAC,YAC3B;gBACV;YACJ;YACA,MAAM,aAAa,OAAO,IAAI,CAAC,KAAK,SAAS,EAAE,MAAM,KAAK;YAC1D,MAAM,OAAO;gBACT,KAAK,WAAW;gBAChB,aAAa,KAAK;gBAClB,CAAA,GAAA,+NAAA,CAAA,4BAAyB,AAAD,EAAE,KAAK,SAAS,EAAE,CAAA,GAAA,+NAAA,CAAA,aAAU,AAAD,EAAE,KAAK,WAAW;aACxE,CAAC,IAAI,CAAC;YACP,MAAM,EAAE,OAAO,EAAE,QAAQ,EAAE,GAAG;YAC9B,IAAI,CAAC,OAAO,CAAC,cAAc,CAAC,QAAQ,CAAC,KAAK,aAAa,EAAE,KAAK,IAAI,EAAE,MAAM,KAAK,QAAQ,EAAE,KAAK,aAAa,EAAE;gBACzG;gBACA;gBACA,WAAW,KAAK,SAAS;YAC7B,GAAG,KAAK,KAAK,EAAE,iBAAiB,CAAC,KAAK,IAAI,CAAC,oBAAoB,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK,IAAI,GAAG,IAAI,CAAC,IAAI;QACtH;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2071, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/Webhooks.js"], "sourcesContent": ["import { StripeError, StripeSignatureVerificationError } from './Error.js';\nimport { CryptoProviderOnlySupportsAsyncError, } from './crypto/CryptoProvider.js';\nexport function createWebhooks(platformFunctions) {\n    const Webhook = {\n        DEFAULT_TOLERANCE: 300,\n        signature: null,\n        constructEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            try {\n                if (!this.signature) {\n                    throw new Error('ERR: missing signature helper, unable to verify');\n                }\n                this.signature.verifyHeader(payload, header, secret, tolerance || Webhook.DEFAULT_TOLERANCE, cryptoProvider, receivedAt);\n            }\n            catch (e) {\n                if (e instanceof CryptoProviderOnlySupportsAsyncError) {\n                    e.message +=\n                        '\\nUse `await constructEventAsync(...)` instead of `constructEvent(...)`';\n                }\n                throw e;\n            }\n            const jsonPayload = payload instanceof Uint8Array\n                ? JSON.parse(new TextDecoder('utf8').decode(payload))\n                : JSON.parse(payload);\n            return jsonPayload;\n        },\n        async constructEventAsync(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            if (!this.signature) {\n                throw new Error('ERR: missing signature helper, unable to verify');\n            }\n            await this.signature.verifyHeaderAsync(payload, header, secret, tolerance || Webhook.DEFAULT_TOLERANCE, cryptoProvider, receivedAt);\n            const jsonPayload = payload instanceof Uint8Array\n                ? JSON.parse(new TextDecoder('utf8').decode(payload))\n                : JSON.parse(payload);\n            return jsonPayload;\n        },\n        /**\n         * Generates a header to be used for webhook mocking\n         *\n         * @typedef {object} opts\n         * @property {number} timestamp - Timestamp of the header. Defaults to Date.now()\n         * @property {string} payload - JSON stringified payload object, containing the 'id' and 'object' parameters\n         * @property {string} secret - Stripe webhook secret 'whsec_...'\n         * @property {string} scheme - Version of API to hit. Defaults to 'v1'.\n         * @property {string} signature - Computed webhook signature\n         * @property {CryptoProvider} cryptoProvider - Crypto provider to use for computing the signature if none was provided. Defaults to NodeCryptoProvider.\n         */\n        generateTestHeaderString: function (opts) {\n            const preparedOpts = prepareOptions(opts);\n            const signature = preparedOpts.signature ||\n                preparedOpts.cryptoProvider.computeHMACSignature(preparedOpts.payloadString, preparedOpts.secret);\n            return preparedOpts.generateHeaderString(signature);\n        },\n        generateTestHeaderStringAsync: async function (opts) {\n            const preparedOpts = prepareOptions(opts);\n            const signature = preparedOpts.signature ||\n                (await preparedOpts.cryptoProvider.computeHMACSignatureAsync(preparedOpts.payloadString, preparedOpts.secret));\n            return preparedOpts.generateHeaderString(signature);\n        },\n    };\n    const signature = {\n        EXPECTED_SCHEME: 'v1',\n        verifyHeader(encodedPayload, encodedHeader, secret, tolerance, cryptoProvider, receivedAt) {\n            const { decodedHeader: header, decodedPayload: payload, details, suspectPayloadType, } = parseEventDetails(encodedPayload, encodedHeader, this.EXPECTED_SCHEME);\n            const secretContainsWhitespace = /\\s/.test(secret);\n            cryptoProvider = cryptoProvider || getCryptoProvider();\n            const expectedSignature = cryptoProvider.computeHMACSignature(makeHMACContent(payload, details), secret);\n            validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt);\n            return true;\n        },\n        async verifyHeaderAsync(encodedPayload, encodedHeader, secret, tolerance, cryptoProvider, receivedAt) {\n            const { decodedHeader: header, decodedPayload: payload, details, suspectPayloadType, } = parseEventDetails(encodedPayload, encodedHeader, this.EXPECTED_SCHEME);\n            const secretContainsWhitespace = /\\s/.test(secret);\n            cryptoProvider = cryptoProvider || getCryptoProvider();\n            const expectedSignature = await cryptoProvider.computeHMACSignatureAsync(makeHMACContent(payload, details), secret);\n            return validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt);\n        },\n    };\n    function makeHMACContent(payload, details) {\n        return `${details.timestamp}.${payload}`;\n    }\n    function parseEventDetails(encodedPayload, encodedHeader, expectedScheme) {\n        if (!encodedPayload) {\n            throw new StripeSignatureVerificationError(encodedHeader, encodedPayload, {\n                message: 'No webhook payload was provided.',\n            });\n        }\n        const suspectPayloadType = typeof encodedPayload != 'string' &&\n            !(encodedPayload instanceof Uint8Array);\n        const textDecoder = new TextDecoder('utf8');\n        const decodedPayload = encodedPayload instanceof Uint8Array\n            ? textDecoder.decode(encodedPayload)\n            : encodedPayload;\n        // Express's type for `Request#headers` is `string | []string`\n        // which is because the `set-cookie` header is an array,\n        // but no other headers are an array (docs: https://nodejs.org/api/http.html#http_message_headers)\n        // (Express's Request class is an extension of http.IncomingMessage, and doesn't appear to be relevantly modified: https://github.com/expressjs/express/blob/master/lib/request.js#L31)\n        if (Array.isArray(encodedHeader)) {\n            throw new Error('Unexpected: An array was passed as a header, which should not be possible for the stripe-signature header.');\n        }\n        if (encodedHeader == null || encodedHeader == '') {\n            throw new StripeSignatureVerificationError(encodedHeader, encodedPayload, {\n                message: 'No stripe-signature header value was provided.',\n            });\n        }\n        const decodedHeader = encodedHeader instanceof Uint8Array\n            ? textDecoder.decode(encodedHeader)\n            : encodedHeader;\n        const details = parseHeader(decodedHeader, expectedScheme);\n        if (!details || details.timestamp === -1) {\n            throw new StripeSignatureVerificationError(decodedHeader, decodedPayload, {\n                message: 'Unable to extract timestamp and signatures from header',\n            });\n        }\n        if (!details.signatures.length) {\n            throw new StripeSignatureVerificationError(decodedHeader, decodedPayload, {\n                message: 'No signatures found with expected scheme',\n            });\n        }\n        return {\n            decodedPayload,\n            decodedHeader,\n            details,\n            suspectPayloadType,\n        };\n    }\n    function validateComputedSignature(payload, header, details, expectedSignature, tolerance, suspectPayloadType, secretContainsWhitespace, receivedAt) {\n        const signatureFound = !!details.signatures.filter(platformFunctions.secureCompare.bind(platformFunctions, expectedSignature)).length;\n        const docsLocation = '\\nLearn more about webhook signing and explore webhook integration examples for various frameworks at ' +\n            'https://docs.stripe.com/webhooks/signature';\n        const whitespaceMessage = secretContainsWhitespace\n            ? '\\n\\nNote: The provided signing secret contains whitespace. This often indicates an extra newline or space is in the value'\n            : '';\n        if (!signatureFound) {\n            if (suspectPayloadType) {\n                throw new StripeSignatureVerificationError(header, payload, {\n                    message: 'Webhook payload must be provided as a string or a Buffer (https://nodejs.org/api/buffer.html) instance representing the _raw_ request body.' +\n                        'Payload was provided as a parsed JavaScript object instead. \\n' +\n                        'Signature verification is impossible without access to the original signed material. \\n' +\n                        docsLocation +\n                        '\\n' +\n                        whitespaceMessage,\n                });\n            }\n            throw new StripeSignatureVerificationError(header, payload, {\n                message: 'No signatures found matching the expected signature for payload.' +\n                    ' Are you passing the raw request body you received from Stripe? \\n' +\n                    ' If a webhook request is being forwarded by a third-party tool,' +\n                    ' ensure that the exact request body, including JSON formatting and new line style, is preserved.\\n' +\n                    docsLocation +\n                    '\\n' +\n                    whitespaceMessage,\n            });\n        }\n        const timestampAge = Math.floor((typeof receivedAt === 'number' ? receivedAt : Date.now()) / 1000) - details.timestamp;\n        if (tolerance > 0 && timestampAge > tolerance) {\n            throw new StripeSignatureVerificationError(header, payload, {\n                message: 'Timestamp outside the tolerance zone',\n            });\n        }\n        return true;\n    }\n    function parseHeader(header, scheme) {\n        if (typeof header !== 'string') {\n            return null;\n        }\n        return header.split(',').reduce((accum, item) => {\n            const kv = item.split('=');\n            if (kv[0] === 't') {\n                accum.timestamp = parseInt(kv[1], 10);\n            }\n            if (kv[0] === scheme) {\n                accum.signatures.push(kv[1]);\n            }\n            return accum;\n        }, {\n            timestamp: -1,\n            signatures: [],\n        });\n    }\n    let webhooksCryptoProviderInstance = null;\n    /**\n     * Lazily instantiate a CryptoProvider instance. This is a stateless object\n     * so a singleton can be used here.\n     */\n    function getCryptoProvider() {\n        if (!webhooksCryptoProviderInstance) {\n            webhooksCryptoProviderInstance = platformFunctions.createDefaultCryptoProvider();\n        }\n        return webhooksCryptoProviderInstance;\n    }\n    function prepareOptions(opts) {\n        if (!opts) {\n            throw new StripeError({\n                message: 'Options are required',\n            });\n        }\n        const timestamp = Math.floor(opts.timestamp) || Math.floor(Date.now() / 1000);\n        const scheme = opts.scheme || signature.EXPECTED_SCHEME;\n        const cryptoProvider = opts.cryptoProvider || getCryptoProvider();\n        const payloadString = `${timestamp}.${opts.payload}`;\n        const generateHeaderString = (signature) => {\n            return `t=${timestamp},${scheme}=${signature}`;\n        };\n        return Object.assign(Object.assign({}, opts), { timestamp,\n            scheme,\n            cryptoProvider,\n            payloadString,\n            generateHeaderString });\n    }\n    Webhook.signature = signature;\n    return Webhook;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS,eAAe,iBAAiB;IAC5C,MAAM,UAAU;QACZ,mBAAmB;QACnB,WAAW;QACX,gBAAe,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU;YACzE,IAAI;gBACA,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;oBACjB,MAAM,IAAI,MAAM;gBACpB;gBACA,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,SAAS,QAAQ,QAAQ,aAAa,QAAQ,iBAAiB,EAAE,gBAAgB;YACjH,EACA,OAAO,GAAG;gBACN,IAAI,aAAa,kPAAA,CAAA,uCAAoC,EAAE;oBACnD,EAAE,OAAO,IACL;gBACR;gBACA,MAAM;YACV;YACA,MAAM,cAAc,mBAAmB,aACjC,KAAK,KAAK,CAAC,IAAI,YAAY,QAAQ,MAAM,CAAC,YAC1C,KAAK,KAAK,CAAC;YACjB,OAAO;QACX;QACA,MAAM,qBAAoB,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU;YACpF,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;gBACjB,MAAM,IAAI,MAAM;YACpB;YACA,MAAM,IAAI,CAAC,SAAS,CAAC,iBAAiB,CAAC,SAAS,QAAQ,QAAQ,aAAa,QAAQ,iBAAiB,EAAE,gBAAgB;YACxH,MAAM,cAAc,mBAAmB,aACjC,KAAK,KAAK,CAAC,IAAI,YAAY,QAAQ,MAAM,CAAC,YAC1C,KAAK,KAAK,CAAC;YACjB,OAAO;QACX;QACA;;;;;;;;;;SAUC,GACD,0BAA0B,SAAU,IAAI;YACpC,MAAM,eAAe,eAAe;YACpC,MAAM,YAAY,aAAa,SAAS,IACpC,aAAa,cAAc,CAAC,oBAAoB,CAAC,aAAa,aAAa,EAAE,aAAa,MAAM;YACpG,OAAO,aAAa,oBAAoB,CAAC;QAC7C;QACA,+BAA+B,eAAgB,IAAI;YAC/C,MAAM,eAAe,eAAe;YACpC,MAAM,YAAY,aAAa,SAAS,IACnC,MAAM,aAAa,cAAc,CAAC,yBAAyB,CAAC,aAAa,aAAa,EAAE,aAAa,MAAM;YAChH,OAAO,aAAa,oBAAoB,CAAC;QAC7C;IACJ;IACA,MAAM,YAAY;QACd,iBAAiB;QACjB,cAAa,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU;YACrF,MAAM,EAAE,eAAe,MAAM,EAAE,gBAAgB,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAG,GAAG,kBAAkB,gBAAgB,eAAe,IAAI,CAAC,eAAe;YAC9J,MAAM,2BAA2B,KAAK,IAAI,CAAC;YAC3C,iBAAiB,kBAAkB;YACnC,MAAM,oBAAoB,eAAe,oBAAoB,CAAC,gBAAgB,SAAS,UAAU;YACjG,0BAA0B,SAAS,QAAQ,SAAS,mBAAmB,WAAW,oBAAoB,0BAA0B;YAChI,OAAO;QACX;QACA,MAAM,mBAAkB,cAAc,EAAE,aAAa,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU;YAChG,MAAM,EAAE,eAAe,MAAM,EAAE,gBAAgB,OAAO,EAAE,OAAO,EAAE,kBAAkB,EAAG,GAAG,kBAAkB,gBAAgB,eAAe,IAAI,CAAC,eAAe;YAC9J,MAAM,2BAA2B,KAAK,IAAI,CAAC;YAC3C,iBAAiB,kBAAkB;YACnC,MAAM,oBAAoB,MAAM,eAAe,yBAAyB,CAAC,gBAAgB,SAAS,UAAU;YAC5G,OAAO,0BAA0B,SAAS,QAAQ,SAAS,mBAAmB,WAAW,oBAAoB,0BAA0B;QAC3I;IACJ;IACA,SAAS,gBAAgB,OAAO,EAAE,OAAO;QACrC,OAAO,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,SAAS;IAC5C;IACA,SAAS,kBAAkB,cAAc,EAAE,aAAa,EAAE,cAAc;QACpE,IAAI,CAAC,gBAAgB;YACjB,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,eAAe,gBAAgB;gBACtE,SAAS;YACb;QACJ;QACA,MAAM,qBAAqB,OAAO,kBAAkB,YAChD,CAAC,CAAC,0BAA0B,UAAU;QAC1C,MAAM,cAAc,IAAI,YAAY;QACpC,MAAM,iBAAiB,0BAA0B,aAC3C,YAAY,MAAM,CAAC,kBACnB;QACN,8DAA8D;QAC9D,wDAAwD;QACxD,kGAAkG;QAClG,uLAAuL;QACvL,IAAI,MAAM,OAAO,CAAC,gBAAgB;YAC9B,MAAM,IAAI,MAAM;QACpB;QACA,IAAI,iBAAiB,QAAQ,iBAAiB,IAAI;YAC9C,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,eAAe,gBAAgB;gBACtE,SAAS;YACb;QACJ;QACA,MAAM,gBAAgB,yBAAyB,aACzC,YAAY,MAAM,CAAC,iBACnB;QACN,MAAM,UAAU,YAAY,eAAe;QAC3C,IAAI,CAAC,WAAW,QAAQ,SAAS,KAAK,CAAC,GAAG;YACtC,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,eAAe,gBAAgB;gBACtE,SAAS;YACb;QACJ;QACA,IAAI,CAAC,QAAQ,UAAU,CAAC,MAAM,EAAE;YAC5B,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,eAAe,gBAAgB;gBACtE,SAAS;YACb;QACJ;QACA,OAAO;YACH;YACA;YACA;YACA;QACJ;IACJ;IACA,SAAS,0BAA0B,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,iBAAiB,EAAE,SAAS,EAAE,kBAAkB,EAAE,wBAAwB,EAAE,UAAU;QAC/I,MAAM,iBAAiB,CAAC,CAAC,QAAQ,UAAU,CAAC,MAAM,CAAC,kBAAkB,aAAa,CAAC,IAAI,CAAC,mBAAmB,oBAAoB,MAAM;QACrI,MAAM,eAAe,2GACjB;QACJ,MAAM,oBAAoB,2BACpB,8HACA;QACN,IAAI,CAAC,gBAAgB;YACjB,IAAI,oBAAoB;gBACpB,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,QAAQ,SAAS;oBACxD,SAAS,gJACL,mEACA,4FACA,eACA,OACA;gBACR;YACJ;YACA,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,QAAQ,SAAS;gBACxD,SAAS,qEACL,uEACA,oEACA,uGACA,eACA,OACA;YACR;QACJ;QACA,MAAM,eAAe,KAAK,KAAK,CAAC,CAAC,OAAO,eAAe,WAAW,aAAa,KAAK,GAAG,EAAE,IAAI,QAAQ,QAAQ,SAAS;QACtH,IAAI,YAAY,KAAK,eAAe,WAAW;YAC3C,MAAM,IAAI,+NAAA,CAAA,mCAAgC,CAAC,QAAQ,SAAS;gBACxD,SAAS;YACb;QACJ;QACA,OAAO;IACX;IACA,SAAS,YAAY,MAAM,EAAE,MAAM;QAC/B,IAAI,OAAO,WAAW,UAAU;YAC5B,OAAO;QACX;QACA,OAAO,OAAO,KAAK,CAAC,KAAK,MAAM,CAAC,CAAC,OAAO;YACpC,MAAM,KAAK,KAAK,KAAK,CAAC;YACtB,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK;gBACf,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC,EAAE,EAAE;YACtC;YACA,IAAI,EAAE,CAAC,EAAE,KAAK,QAAQ;gBAClB,MAAM,UAAU,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE;YAC/B;YACA,OAAO;QACX,GAAG;YACC,WAAW,CAAC;YACZ,YAAY,EAAE;QAClB;IACJ;IACA,IAAI,iCAAiC;IACrC;;;KAGC,GACD,SAAS;QACL,IAAI,CAAC,gCAAgC;YACjC,iCAAiC,kBAAkB,2BAA2B;QAClF;QACA,OAAO;IACX;IACA,SAAS,eAAe,IAAI;QACxB,IAAI,CAAC,MAAM;YACP,MAAM,IAAI,+NAAA,CAAA,cAAW,CAAC;gBAClB,SAAS;YACb;QACJ;QACA,MAAM,YAAY,KAAK,KAAK,CAAC,KAAK,SAAS,KAAK,KAAK,KAAK,CAAC,KAAK,GAAG,KAAK;QACxE,MAAM,SAAS,KAAK,MAAM,IAAI,UAAU,eAAe;QACvD,MAAM,iBAAiB,KAAK,cAAc,IAAI;QAC9C,MAAM,gBAAgB,GAAG,UAAU,CAAC,EAAE,KAAK,OAAO,EAAE;QACpD,MAAM,uBAAuB,CAAC;YAC1B,OAAO,CAAC,EAAE,EAAE,UAAU,CAAC,EAAE,OAAO,CAAC,EAAE,WAAW;QAClD;QACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,OAAO;YAAE;YAC5C;YACA;YACA;YACA;QAAqB;IAC7B;IACA,QAAQ,SAAS,GAAG;IACpB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2267, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/apiVersion.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nexport const ApiVersion = '2025-06-30.basil';\nexport const ApiMajorVersion = 'basil';\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;AAChC,MAAM,aAAa;AACnB,MAAM,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2280, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/ResourceNamespace.js"], "sourcesContent": ["// ResourceNamespace allows you to create nested resources, i.e. `stripe.issuing.cards`.\n// It also works recursively, so you could do i.e. `stripe.billing.invoicing.pay`.\nfunction ResourceNamespace(stripe, resources) {\n    for (const name in resources) {\n        if (!Object.prototype.hasOwnProperty.call(resources, name)) {\n            continue;\n        }\n        const camelCaseName = name[0].toLowerCase() + name.substring(1);\n        const resource = new resources[name](stripe);\n        this[camelCaseName] = resource;\n    }\n}\nexport function resourceNamespace(namespace, resources) {\n    return function (stripe) {\n        return new ResourceNamespace(stripe, resources);\n    };\n}\n"], "names": [], "mappings": "AAAA,wFAAwF;AACxF,kFAAkF;;;;AAClF,SAAS,kBAAkB,MAAM,EAAE,SAAS;IACxC,IAAK,MAAM,QAAQ,UAAW;QAC1B,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,WAAW,OAAO;YACxD;QACJ;QACA,MAAM,gBAAgB,IAAI,CAAC,EAAE,CAAC,WAAW,KAAK,KAAK,SAAS,CAAC;QAC7D,MAAM,WAAW,IAAI,SAAS,CAAC,KAAK,CAAC;QACrC,IAAI,CAAC,cAAc,GAAG;IAC1B;AACJ;AACO,SAAS,kBAAkB,SAAS,EAAE,SAAS;IAClD,OAAO,SAAU,MAAM;QACnB,OAAO,IAAI,kBAAkB,QAAQ;IACzC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2306, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/FinancialConnections/Accounts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Accounts = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts/{account}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts',\n        methodType: 'list',\n    }),\n    disconnect: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/disconnect',\n    }),\n    listOwners: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/accounts/{account}/owners',\n        methodType: 'list',\n    }),\n    refresh: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/refresh',\n    }),\n    subscribe: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/subscribe',\n    }),\n    unsubscribe: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/accounts/{account}/unsubscribe',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,WAAW,aAAa;QACpB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2351, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Entitlements/ActiveEntitlements.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ActiveEntitlements = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/active_entitlements/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/active_entitlements',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2375, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/Alerts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Alerts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/alerts' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/alerts/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/alerts',\n        methodType: 'list',\n    }),\n    activate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/activate',\n    }),\n    archive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/archive',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/alerts/{id}/deactivate',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA0B;IAC5E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2415, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Issuing/Authorizations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Authorizations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/capture',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/expire',\n    }),\n    finalizeAmount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/finalize_amount',\n    }),\n    increment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/increment',\n    }),\n    respond: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/fraud_challenges/respond',\n    }),\n    reverse: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/authorizations/{authorization}/reverse',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,WAAW,aAAa;QACpB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2458, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Authorizations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Authorizations = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/authorizations/{authorization}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/authorizations',\n        methodType: 'list',\n    }),\n    approve: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}/approve',\n    }),\n    decline: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/authorizations/{authorization}/decline',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2494, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Tax/Calculations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Calculations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax/calculations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/calculations/{calculation}',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/calculations/{calculation}/line_items',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2522, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Cardholders.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cardholders = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cardholders' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cardholders/{cardholder}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/cardholders/{cardholder}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cardholders',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,cAAc,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA0B;IAC3E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2554, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Issuing/Cards.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cards = StripeResource.extend({\n    deliverCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/deliver',\n    }),\n    failCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/fail',\n    }),\n    returnCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/return',\n    }),\n    shipCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/ship',\n    }),\n    submitCard: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/cards/{card}/shipping/submit',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,QAAQ,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvC,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2589, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Cards.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Cards = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cards' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/issuing/cards/{card}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/cards/{card}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/cards',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,QAAQ,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;IACrE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA2B;IAC7E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;IAC5E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/BillingPortal/Configurations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Configurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing_portal/configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing_portal/configurations',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Terminal/Configurations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Configurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/configurations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/configurations/{configuration}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2689, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/ConfirmationTokens.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConfirmationTokens = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/confirmation_tokens',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2708, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Terminal/ConnectionTokens.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConnectionTokens = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/connection_tokens',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/CreditBalanceSummary.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditBalanceSummary = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_summary',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,uBAAuB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACtD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2746, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/CreditBalanceTransactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditBalanceTransactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_balance_transactions',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,4BAA4B,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3D,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2770, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/CreditGrants.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditGrants = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/credit_grants' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_grants/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/credit_grants',\n        methodType: 'list',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}/expire',\n    }),\n    voidGrant: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/credit_grants/{id}/void',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;IAC7E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,WAAW,aAAa;QACpB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2810, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/CreditReversals.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditReversals = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/credit_reversals',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/credit_reversals/{credit_reversal}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/credit_reversals',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2838, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Customers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Customers = StripeResource.extend({\n    fundCashBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/customers/{customer}/fund_cash_balance',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,iBAAiB,aAAa;QAC1B,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/DebitReversals.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const DebitReversals = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/debit_reversals',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/debit_reversals/{debit_reversal}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/debit_reversals',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2885, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Disputes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Disputes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/issuing/disputes' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/disputes/{dispute}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/disputes/{dispute}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/disputes',\n        methodType: 'list',\n    }),\n    submit: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/disputes/{dispute}/submit',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2921, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Radar/EarlyFraudWarnings.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EarlyFraudWarnings = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/early_fraud_warnings/{early_fraud_warning}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/early_fraud_warnings',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2945, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Core/EventDestinations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EventDestinations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v2/core/event_destinations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v2/core/event_destinations/{id}',\n    }),\n    disable: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/disable',\n    }),\n    enable: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/enable',\n    }),\n    ping: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/core/event_destinations/{id}/ping',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2993, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Core/Events.js"], "sourcesContent": ["// This file is manually maintained\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Events = StripeResource.extend({\n    retrieve(...args) {\n        const transformResponseData = (response) => {\n            return this.addFetchRelatedObjectIfNeeded(response);\n        };\n        return stripeMethod({\n            method: 'GET',\n            fullPath: '/v2/core/events/{id}',\n            transformResponseData,\n        }).apply(this, args);\n    },\n    list(...args) {\n        const transformResponseData = (response) => {\n            return Object.assign(Object.assign({}, response), { data: response.data.map(this.addFetchRelatedObjectIfNeeded.bind(this)) });\n        };\n        return stripeMethod({\n            method: 'GET',\n            fullPath: '/v2/core/events',\n            methodType: 'list',\n            transformResponseData,\n        }).apply(this, args);\n    },\n    /**\n     * @private\n     *\n     * For internal use in stripe-node.\n     *\n     * @param pulledEvent The retrieved event object\n     * @returns The retrieved event object with a fetchRelatedObject method,\n     * if pulledEvent.related_object is valid (non-null and has a url)\n     */\n    addFetchRelatedObjectIfNeeded(pulledEvent) {\n        if (!pulledEvent.related_object || !pulledEvent.related_object.url) {\n            return pulledEvent;\n        }\n        return Object.assign(Object.assign({}, pulledEvent), { fetchRelatedObject: () => \n            // call stripeMethod with 'this' resource to fetch\n            // the related object. 'this' is needed to construct\n            // and send the request, but the method spec controls\n            // the url endpoint and method, so it doesn't matter\n            // that 'this' is an Events resource object here\n            stripeMethod({\n                method: 'GET',\n                fullPath: pulledEvent.related_object.url,\n            }).apply(this, [\n                {\n                    stripeAccount: pulledEvent.context,\n                },\n            ]) });\n    },\n});\n"], "names": [], "mappings": "AAAA,mCAAmC;;;;AACnC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,UAAS,GAAG,IAAI;QACZ,MAAM,wBAAwB,CAAC;YAC3B,OAAO,IAAI,CAAC,6BAA6B,CAAC;QAC9C;QACA,OAAO,aAAa;YAChB,QAAQ;YACR,UAAU;YACV;QACJ,GAAG,KAAK,CAAC,IAAI,EAAE;IACnB;IACA,MAAK,GAAG,IAAI;QACR,MAAM,wBAAwB,CAAC;YAC3B,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,WAAW;gBAAE,MAAM,SAAS,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,6BAA6B,CAAC,IAAI,CAAC,IAAI;YAAG;QAC/H;QACA,OAAO,aAAa;YAChB,QAAQ;YACR,UAAU;YACV,YAAY;YACZ;QACJ,GAAG,KAAK,CAAC,IAAI,EAAE;IACnB;IACA;;;;;;;;KAQC,GACD,+BAA8B,WAAW;QACrC,IAAI,CAAC,YAAY,cAAc,IAAI,CAAC,YAAY,cAAc,CAAC,GAAG,EAAE;YAChE,OAAO;QACX;QACA,OAAO,OAAO,MAAM,CAAC,OAAO,MAAM,CAAC,CAAC,GAAG,cAAc;YAAE,oBAAoB,IACvE,kDAAkD;gBAClD,oDAAoD;gBACpD,qDAAqD;gBACrD,oDAAoD;gBACpD,gDAAgD;gBAChD,aAAa;oBACT,QAAQ;oBACR,UAAU,YAAY,cAAc,CAAC,GAAG;gBAC5C,GAAG,KAAK,CAAC,IAAI,EAAE;oBACX;wBACI,eAAe,YAAY,OAAO;oBACtC;iBACH;QAAE;IACX;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3059, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Entitlements/Features.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Features = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/entitlements/features' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/features/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/entitlements/features/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/entitlements/features',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;IAC7E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3091, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/FinancialAccounts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const FinancialAccounts = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts',\n        methodType: 'list',\n    }),\n    close: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/close',\n    }),\n    retrieveFeatures: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/features',\n    }),\n    updateFeatures: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/financial_accounts/{financial_account}/features',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,OAAO,aAAa;QAChB,QAAQ;QACR,UAAU;IACd;IACA,kBAAkB,aAAa;QAC3B,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Treasury/InboundTransfers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InboundTransfers = StripeResource.extend({\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/fail',\n    }),\n    returnInboundTransfer: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/return',\n    }),\n    succeed: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/inbound_transfers/{id}/succeed',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3162, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/InboundTransfers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InboundTransfers = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/inbound_transfers',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/inbound_transfers/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/inbound_transfers',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/inbound_transfers/{inbound_transfer}/cancel',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3194, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Terminal/Locations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Locations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/terminal/locations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/locations',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/locations/{location}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAyB;IAC1E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3230, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/MeterEventAdjustments.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventAdjustments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meter_event_adjustments',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,wBAAwB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Billing/MeterEventAdjustments.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventAdjustments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_adjustments',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,wBAAwB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3268, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Billing/MeterEventSession.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventSession = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_session',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3287, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Billing/MeterEventStream.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEventStream = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v2/billing/meter_event_stream',\n        host: 'meter-events.stripe.com',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,MAAM;IACV;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3307, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/MeterEvents.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEvents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meter_events' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,cAAc,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;AAChF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3326, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/V2/Billing/MeterEvents.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const MeterEvents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v2/billing/meter_events' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,cAAc,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;AAChF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3345, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Billing/Meters.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Meters = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/billing/meters/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/billing/meters/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/meters',\n        methodType: 'list',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meters/{id}/deactivate',\n    }),\n    listEventSummaries: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/billing/meters/{id}/event_summaries',\n        methodType: 'list',\n    }),\n    reactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing/meters/{id}/reactivate',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA0B;IAC5E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA0B;IAC3E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;IACA,oBAAoB,aAAa;QAC7B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Climate/Orders.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Orders = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/climate/orders' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/orders/{order}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/climate/orders/{order}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/orders',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/climate/orders/{order}/cancel',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3426, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Treasury/OutboundPayments.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundPayments = StripeResource.extend({\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}',\n    }),\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/fail',\n    }),\n    post: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/post',\n    }),\n    returnOutboundPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_payments/{id}/return',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3457, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/OutboundPayments.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundPayments = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_payments',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_payments/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_payments',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_payments/{id}/cancel',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3489, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Treasury/OutboundTransfers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundTransfers = StripeResource.extend({\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}',\n    }),\n    fail: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/fail',\n    }),\n    post: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/post',\n    }),\n    returnOutboundTransfer: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/outbound_transfers/{outbound_transfer}/return',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;IACd;IACA,wBAAwB,aAAa;QACjC,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/OutboundTransfers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const OutboundTransfers = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_transfers',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_transfers/{outbound_transfer}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/outbound_transfers',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/treasury/outbound_transfers/{outbound_transfer}/cancel',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Issuing/PersonalizationDesigns.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PersonalizationDesigns = StripeResource.extend({\n    activate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/activate',\n    }),\n    deactivate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/deactivate',\n    }),\n    reject: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/personalization_designs/{personalization_design}/reject',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,yBAAyB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3579, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/PersonalizationDesigns.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PersonalizationDesigns = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/personalization_designs',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/personalization_designs/{personalization_design}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/personalization_designs/{personalization_design}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/personalization_designs',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,yBAAyB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/PhysicalBundles.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PhysicalBundles = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/physical_bundles/{physical_bundle}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/physical_bundles',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3635, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Climate/Products.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Products = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/products/{product}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/products',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Terminal/Readers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Readers = StripeResource.extend({\n    presentPaymentMethod: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/present_payment_method',\n    }),\n    succeedInputCollection: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/succeed_input_collection',\n    }),\n    timeoutInputCollection: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/terminal/readers/{reader}/timeout_input_collection',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,wBAAwB,aAAa;QACjC,QAAQ;QACR,UAAU;IACd;IACA,wBAAwB,aAAa;QACjC,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3686, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Terminal/Readers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Readers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/terminal/readers' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/terminal/readers',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/terminal/readers/{reader}',\n    }),\n    cancelAction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/cancel_action',\n    }),\n    collectInputs: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/collect_inputs',\n    }),\n    collectPaymentMethod: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/collect_payment_method',\n    }),\n    confirmPaymentIntent: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/confirm_payment_intent',\n    }),\n    processPaymentIntent: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/process_payment_intent',\n    }),\n    processSetupIntent: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/process_setup_intent',\n    }),\n    refundPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/refund_payment',\n    }),\n    setReaderDisplay: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/terminal/readers/{reader}/set_reader_display',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,oBAAoB,aAAa;QAC7B,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,kBAAkB,aAAa;QAC3B,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3754, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Treasury/ReceivedCredits.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedCredits = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/received_credits',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3773, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/ReceivedCredits.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedCredits = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_credits/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_credits',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3797, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Treasury/ReceivedDebits.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedDebits = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/treasury/received_debits',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3816, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/ReceivedDebits.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReceivedDebits = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_debits/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/received_debits',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3840, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Refunds.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Refunds = StripeResource.extend({\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/refunds/{refund}/expire',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3859, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Tax/Registrations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Registrations = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax/registrations' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/registrations/{id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/registrations/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/registrations',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3891, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Reporting/ReportRuns.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReportRuns = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/reporting/report_runs' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_runs/{report_run}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_runs',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,aAAa,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC5C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;IAC7E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3919, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Reporting/ReportTypes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ReportTypes = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_types/{report_type}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reporting/report_types',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,cAAc,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3943, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Forwarding/Requests.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Requests = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/forwarding/requests' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/forwarding/requests/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/forwarding/requests',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA0B;IAC3E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Sigma/ScheduledQueryRuns.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ScheduledQueryRuns = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sigma/scheduled_query_runs/{scheduled_query_run}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sigma/scheduled_query_runs',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3995, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Apps/Secrets.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Secrets = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/apps/secrets' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apps/secrets',\n        methodType: 'list',\n    }),\n    deleteWhere: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/apps/secrets/delete',\n    }),\n    find: stripeMethod({ method: 'GET', fullPath: '/v1/apps/secrets/find' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAmB;IACpE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAwB;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4027, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/BillingPortal/Sessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/billing_portal/sessions',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4046, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Checkout/Sessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/checkout/sessions' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions/{session}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/checkout/sessions/{session}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions',\n        methodType: 'list',\n    }),\n    expire: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/checkout/sessions/{session}/expire',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/checkout/sessions/{session}/line_items',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4087, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/FinancialConnections/Sessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/financial_connections/sessions',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/sessions/{session}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4110, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Tax/Settings.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Settings = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax/settings' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/tax/settings' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAmB;IACrE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAmB;AACxE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4133, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Climate/Suppliers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Suppliers = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/suppliers/{supplier}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/climate/suppliers',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4157, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/TestClocks.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TestClocks = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/test_clocks',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/test_helpers/test_clocks',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}',\n    }),\n    advance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/test_clocks/{test_clock}/advance',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,aAAa,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC5C,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4193, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Tokens.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Tokens = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/tokens/{token}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/tokens/{token}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/tokens',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4221, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/TransactionEntries.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TransactionEntries = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transaction_entries/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transaction_entries',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4245, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TestHelpers/Issuing/Transactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    createForceCapture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/create_force_capture',\n    }),\n    createUnlinkedRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/create_unlinked_refund',\n    }),\n    refund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/test_helpers/issuing/transactions/{transaction}/refund',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,oBAAoB,aAAa;QAC7B,QAAQ;QACR,UAAU;IACd;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4272, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/FinancialConnections/Transactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/transactions/{transaction}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/financial_connections/transactions',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4296, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Issuing/Transactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/transactions/{transaction}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/issuing/transactions/{transaction}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/issuing/transactions',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4324, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Tax/Transactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/transactions/{transaction}',\n    }),\n    createFromCalculation: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/transactions/create_from_calculation',\n    }),\n    createReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/tax/transactions/create_reversal',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax/transactions/{transaction}/line_items',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Treasury/Transactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/treasury/transactions',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4380, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Radar/ValueListItems.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ValueListItems = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/radar/value_list_items',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_list_items/{item}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_list_items',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/radar/value_list_items/{item}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4412, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Radar/ValueLists.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ValueLists = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/radar/value_lists' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/radar/value_lists',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/radar/value_lists/{value_list}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,aAAa,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC5C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4448, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Identity/VerificationReports.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const VerificationReports = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_reports/{report}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_reports',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,sBAAsB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACrD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4472, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Identity/VerificationSessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const VerificationSessions = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_sessions/{session}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/identity/verification_sessions',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}/cancel',\n    }),\n    redact: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/identity/verification_sessions/{session}/redact',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,uBAAuB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACtD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4512, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Accounts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\n// Since path can either be `account` or `accounts`, support both through stripeMethod path\nexport const Accounts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/accounts' }),\n    retrieve(id, ...args) {\n        // No longer allow an api key to be passed as the first string to this function due to ambiguity between\n        // old account ids and api keys. To request the account for an api key, send null as the id\n        if (typeof id === 'string') {\n            return stripeMethod({\n                method: 'GET',\n                fullPath: '/v1/accounts/{id}',\n            }).apply(this, [id, ...args]);\n        }\n        else {\n            if (id === null || id === undefined) {\n                // Remove id as stripeMethod would complain of unexpected argument\n                [].shift.apply([id, ...args]);\n            }\n            return stripeMethod({\n                method: 'GET',\n                fullPath: '/v1/account',\n            }).apply(this, [id, ...args]);\n        }\n    },\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/accounts/{account}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/accounts/{account}' }),\n    createExternalAccount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/external_accounts',\n    }),\n    createLoginLink: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/login_links',\n    }),\n    createPerson: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/persons',\n    }),\n    deleteExternalAccount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    deletePerson: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n    listCapabilities: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/capabilities',\n        methodType: 'list',\n    }),\n    listExternalAccounts: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/external_accounts',\n        methodType: 'list',\n    }),\n    listPersons: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/persons',\n        methodType: 'list',\n    }),\n    reject: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/reject',\n    }),\n    retrieveCurrent: stripeMethod({ method: 'GET', fullPath: '/v1/account' }),\n    retrieveCapability: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/capabilities/{capability}',\n    }),\n    retrieveExternalAccount: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    retrievePerson: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n    updateCapability: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/capabilities/{capability}',\n    }),\n    updateExternalAccount: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/external_accounts/{id}',\n    }),\n    updatePerson: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/accounts/{account}/persons/{person}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AAEnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAe;IAChE,UAAS,EAAE,EAAE,GAAG,IAAI;QAChB,wGAAwG;QACxG,2FAA2F;QAC3F,IAAI,OAAO,OAAO,UAAU;YACxB,OAAO,aAAa;gBAChB,QAAQ;gBACR,UAAU;YACd,GAAG,KAAK,CAAC,IAAI,EAAE;gBAAC;mBAAO;aAAK;QAChC,OACK;YACD,IAAI,OAAO,QAAQ,OAAO,WAAW;gBACjC,kEAAkE;gBAClE,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;oBAAC;uBAAO;iBAAK;YAChC;YACA,OAAO,aAAa;gBAChB,QAAQ;gBACR,UAAU;YACd,GAAG,KAAK,CAAC,IAAI,EAAE;gBAAC;mBAAO;aAAK;QAChC;IACJ;IACA,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAyB;IAC1E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAyB;IACzE,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,iBAAiB,aAAa;QAC1B,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,kBAAkB,aAAa;QAC3B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,iBAAiB,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAc;IACvE,oBAAoB,aAAa;QAC7B,QAAQ;QACR,UAAU;IACd;IACA,yBAAyB,aAAa;QAClC,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,kBAAkB,aAAa;QAC3B,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4639, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/AccountLinks.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const AccountLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/account_links' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;AACzE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4658, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/AccountSessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const AccountSessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/account_sessions' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;AAC5E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4677, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/ApplePayDomains.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ApplePayDomains = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/apple_pay/domains' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apple_pay/domains/{domain}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/apple_pay/domains',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/apple_pay/domains/{domain}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4709, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/ApplicationFees.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ApplicationFees = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees',\n        methodType: 'list',\n    }),\n    createRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/application_fees/{id}/refunds',\n    }),\n    listRefunds: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{id}/refunds',\n        methodType: 'list',\n    }),\n    retrieveRefund: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/application_fees/{fee}/refunds/{id}',\n    }),\n    updateRefund: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/application_fees/{fee}/refunds/{id}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4750, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Balance.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Balance = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/balance' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAc;AACpE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4769, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/BalanceTransactions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const BalanceTransactions = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/balance_transactions/{id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/balance_transactions',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,sBAAsB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACrD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4793, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Charges.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Charges = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/charges' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/charges/{charge}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/charges/{charge}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/charges',\n        methodType: 'list',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/charges/{charge}/capture',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/charges/search',\n        methodType: 'search',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4834, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/ConfirmationTokens.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ConfirmationTokens = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/confirmation_tokens/{confirmation_token}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,qBAAqB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACpD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4853, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/CountrySpecs.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CountrySpecs = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/country_specs/{country}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/country_specs',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4877, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Coupons.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Coupons = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/coupons' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/coupons/{coupon}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/coupons/{coupon}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/coupons',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/coupons/{coupon}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAuB;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4913, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/CreditNotes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CreditNotes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/credit_notes' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/credit_notes/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/credit_notes/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes/{credit_note}/lines',\n        methodType: 'list',\n    }),\n    listPreviewLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/credit_notes/preview/lines',\n        methodType: 'list',\n    }),\n    preview: stripeMethod({ method: 'GET', fullPath: '/v1/credit_notes/preview' }),\n    voidCreditNote: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/credit_notes/{id}/void',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,cAAc,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAmB;IACpE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAwB;IAC1E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA2B;IAC5E,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4963, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/CustomerSessions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const CustomerSessions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/customer_sessions' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;AAC7E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4982, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Customers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Customers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/customers' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/customers/{customer}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/customers/{customer}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/customers/{customer}' }),\n    createBalanceTransaction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/balance_transactions',\n    }),\n    createFundingInstructions: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/funding_instructions',\n    }),\n    createSource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources',\n    }),\n    createTaxId: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/tax_ids',\n    }),\n    deleteDiscount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/discount',\n    }),\n    deleteSource: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    deleteTaxId: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/customers/{customer}/tax_ids/{id}',\n    }),\n    listBalanceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/balance_transactions',\n        methodType: 'list',\n    }),\n    listCashBalanceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance_transactions',\n        methodType: 'list',\n    }),\n    listPaymentMethods: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/payment_methods',\n        methodType: 'list',\n    }),\n    listSources: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/sources',\n        methodType: 'list',\n    }),\n    listTaxIds: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/tax_ids',\n        methodType: 'list',\n    }),\n    retrieveBalanceTransaction: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/balance_transactions/{transaction}',\n    }),\n    retrieveCashBalance: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance',\n    }),\n    retrieveCashBalanceTransaction: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/cash_balance_transactions/{transaction}',\n    }),\n    retrievePaymentMethod: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/payment_methods/{payment_method}',\n    }),\n    retrieveSource: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    retrieveTaxId: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/{customer}/tax_ids/{id}',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/customers/search',\n        methodType: 'search',\n    }),\n    updateBalanceTransaction: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/balance_transactions/{transaction}',\n    }),\n    updateCashBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/cash_balance',\n    }),\n    updateSource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources/{id}',\n    }),\n    verifySource: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/customers/{customer}/sources/{id}/verify',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAgB;IACjE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA2B;IAC7E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;IAC5E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAA2B;IAC3E,0BAA0B,aAAa;QACnC,QAAQ;QACR,UAAU;IACd;IACA,2BAA2B,aAAa;QACpC,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,yBAAyB,aAAa;QAClC,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,6BAA6B,aAAa;QACtC,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,oBAAoB,aAAa;QAC7B,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,YAAY,aAAa;QACrB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,4BAA4B,aAAa;QACrC,QAAQ;QACR,UAAU;IACd;IACA,qBAAqB,aAAa;QAC9B,QAAQ;QACR,UAAU;IACd;IACA,gCAAgC,aAAa;QACzC,QAAQ;QACR,UAAU;IACd;IACA,uBAAuB,aAAa;QAChC,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,0BAA0B,aAAa;QACnC,QAAQ;QACR,UAAU;IACd;IACA,mBAAmB,aAAa;QAC5B,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Disputes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Disputes = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/disputes/{dispute}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/disputes/{dispute}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/disputes',\n        methodType: 'list',\n    }),\n    close: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/disputes/{dispute}/close',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAyB;IAC3E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAyB;IAC1E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,OAAO,aAAa;QAChB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/EphemeralKeys.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const EphemeralKeys = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/ephemeral_keys',\n        validator: (data, options) => {\n            if (!options.headers || !options.headers['Stripe-Version']) {\n                throw new Error('Passing apiVersion in a separate options hash is required to create an ephemeral key. See https://stripe.com/docs/api/versioning?lang=node');\n            }\n        },\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/ephemeral_keys/{key}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,WAAW,CAAC,MAAM;YACd,IAAI,CAAC,QAAQ,OAAO,IAAI,CAAC,QAAQ,OAAO,CAAC,iBAAiB,EAAE;gBACxD,MAAM,IAAI,MAAM;YACpB;QACJ;IACJ;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAA2B;AAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5176, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Events.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Events = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/events/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/events',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAkB;IACpE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/ExchangeRates.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ExchangeRates = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/exchange_rates/{rate_id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/exchange_rates',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5224, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/FileLinks.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const FileLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/file_links' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/file_links/{link}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/file_links/{link}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/file_links',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAiB;IAClE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAwB;IAC1E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5256, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/multipart.js"], "sourcesContent": ["import { flattenAndStringify, queryStringifyRequestData } from './utils.js';\n// Method for formatting HTTP body for the multipart/form-data specification\n// Mostly taken from Fermata.js\n// https://github.com/natevw/fermata/blob/5d9732a33d776ce925013a265935facd1626cc88/fermata.js#L315-L343\nconst multipartDataGenerator = (method, data, headers) => {\n    const segno = (Math.round(Math.random() * 1e16) + Math.round(Math.random() * 1e16)).toString();\n    headers['Content-Type'] = `multipart/form-data; boundary=${segno}`;\n    const textEncoder = new TextEncoder();\n    let buffer = new Uint8Array(0);\n    const endBuffer = textEncoder.encode('\\r\\n');\n    function push(l) {\n        const prevBuffer = buffer;\n        const newBuffer = l instanceof Uint8Array ? l : new Uint8Array(textEncoder.encode(l));\n        buffer = new Uint8Array(prevBuffer.length + newBuffer.length + 2);\n        buffer.set(prevBuffer);\n        buffer.set(newBuffer, prevBuffer.length);\n        buffer.set(endBuffer, buffer.length - 2);\n    }\n    function q(s) {\n        return `\"${s.replace(/\"|\"/g, '%22').replace(/\\r\\n|\\r|\\n/g, ' ')}\"`;\n    }\n    const flattenedData = flattenAndStringify(data);\n    for (const k in flattenedData) {\n        if (!Object.prototype.hasOwnProperty.call(flattenedData, k)) {\n            continue;\n        }\n        const v = flattenedData[k];\n        push(`--${segno}`);\n        if (Object.prototype.hasOwnProperty.call(v, 'data')) {\n            const typedEntry = v;\n            push(`Content-Disposition: form-data; name=${q(k)}; filename=${q(typedEntry.name || 'blob')}`);\n            push(`Content-Type: ${typedEntry.type || 'application/octet-stream'}`);\n            push('');\n            push(typedEntry.data);\n        }\n        else {\n            push(`Content-Disposition: form-data; name=${q(k)}`);\n            push('');\n            push(v);\n        }\n    }\n    push(`--${segno}--`);\n    return buffer;\n};\nexport function multipartRequestDataProcessor(method, data, headers, callback) {\n    data = data || {};\n    if (method !== 'POST') {\n        return callback(null, queryStringifyRequestData(data));\n    }\n    this._stripe._platformFunctions\n        .tryBufferData(data)\n        .then((bufferedData) => {\n        const buffer = multipartDataGenerator(method, bufferedData, headers);\n        return callback(null, buffer);\n    })\n        .catch((err) => callback(err, null));\n}\n"], "names": [], "mappings": ";;;AAAA;;AACA,4EAA4E;AAC5E,+BAA+B;AAC/B,uGAAuG;AACvG,MAAM,yBAAyB,CAAC,QAAQ,MAAM;IAC1C,MAAM,QAAQ,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,QAAQ,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,KAAK,EAAE,QAAQ;IAC5F,OAAO,CAAC,eAAe,GAAG,CAAC,8BAA8B,EAAE,OAAO;IAClE,MAAM,cAAc,IAAI;IACxB,IAAI,SAAS,IAAI,WAAW;IAC5B,MAAM,YAAY,YAAY,MAAM,CAAC;IACrC,SAAS,KAAK,CAAC;QACX,MAAM,aAAa;QACnB,MAAM,YAAY,aAAa,aAAa,IAAI,IAAI,WAAW,YAAY,MAAM,CAAC;QAClF,SAAS,IAAI,WAAW,WAAW,MAAM,GAAG,UAAU,MAAM,GAAG;QAC/D,OAAO,GAAG,CAAC;QACX,OAAO,GAAG,CAAC,WAAW,WAAW,MAAM;QACvC,OAAO,GAAG,CAAC,WAAW,OAAO,MAAM,GAAG;IAC1C;IACA,SAAS,EAAE,CAAC;QACR,OAAO,CAAC,CAAC,EAAE,EAAE,OAAO,CAAC,QAAQ,OAAO,OAAO,CAAC,eAAe,KAAK,CAAC,CAAC;IACtE;IACA,MAAM,gBAAgB,CAAA,GAAA,+NAAA,CAAA,sBAAmB,AAAD,EAAE;IAC1C,IAAK,MAAM,KAAK,cAAe;QAC3B,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,eAAe,IAAI;YACzD;QACJ;QACA,MAAM,IAAI,aAAa,CAAC,EAAE;QAC1B,KAAK,CAAC,EAAE,EAAE,OAAO;QACjB,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,SAAS;YACjD,MAAM,aAAa;YACnB,KAAK,CAAC,qCAAqC,EAAE,EAAE,GAAG,WAAW,EAAE,EAAE,WAAW,IAAI,IAAI,SAAS;YAC7F,KAAK,CAAC,cAAc,EAAE,WAAW,IAAI,IAAI,4BAA4B;YACrE,KAAK;YACL,KAAK,WAAW,IAAI;QACxB,OACK;YACD,KAAK,CAAC,qCAAqC,EAAE,EAAE,IAAI;YACnD,KAAK;YACL,KAAK;QACT;IACJ;IACA,KAAK,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC;IACnB,OAAO;AACX;AACO,SAAS,8BAA8B,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,QAAQ;IACzE,OAAO,QAAQ,CAAC;IAChB,IAAI,WAAW,QAAQ;QACnB,OAAO,SAAS,MAAM,CAAA,GAAA,+NAAA,CAAA,4BAAyB,AAAD,EAAE;IACpD;IACA,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAC1B,aAAa,CAAC,MACd,IAAI,CAAC,CAAC;QACP,MAAM,SAAS,uBAAuB,QAAQ,cAAc;QAC5D,OAAO,SAAS,MAAM;IAC1B,GACK,KAAK,CAAC,CAAC,MAAQ,SAAS,KAAK;AACtC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Files.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { multipartRequestDataProcessor } from '../multipart.js';\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Files = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/files',\n        headers: {\n            'Content-Type': 'multipart/form-data',\n        },\n        host: 'files.stripe.com',\n    }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/files/{file}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/files',\n        methodType: 'list',\n    }),\n    requestDataProcessor: multipartRequestDataProcessor,\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;AACA;;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,QAAQ,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvC,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,SAAS;YACL,gBAAgB;QACpB;QACA,MAAM;IACV;IACA,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAmB;IACrE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,sBAAsB,mOAAA,CAAA,gCAA6B;AACvD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5354, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/InvoiceItems.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoiceItems = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/invoiceitems' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoiceitems',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/invoiceitems/{invoiceitem}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAmB;IACpE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5390, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/InvoicePayments.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoicePayments = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_payments/{invoice_payment}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_payments',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,kBAAkB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACjD,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5414, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/InvoiceRenderingTemplates.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const InvoiceRenderingTemplates = StripeResource.extend({\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_rendering_templates/{template}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoice_rendering_templates',\n        methodType: 'list',\n    }),\n    archive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoice_rendering_templates/{template}/archive',\n    }),\n    unarchive: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoice_rendering_templates/{template}/unarchive',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,4BAA4B,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3D,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,WAAW,aAAa;QACpB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5446, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Invoices.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Invoices = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/invoices' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/invoices/{invoice}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/invoices/{invoice}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/invoices/{invoice}' }),\n    addLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/add_lines',\n    }),\n    attachPayment: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/attach_payment',\n    }),\n    createPreview: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/create_preview',\n    }),\n    finalizeInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/finalize',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices/{invoice}/lines',\n        methodType: 'list',\n    }),\n    markUncollectible: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/mark_uncollectible',\n    }),\n    pay: stripeMethod({ method: 'POST', fullPath: '/v1/invoices/{invoice}/pay' }),\n    removeLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/remove_lines',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/invoices/search',\n        methodType: 'search',\n    }),\n    sendInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/send',\n    }),\n    updateLines: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/update_lines',\n    }),\n    updateLineItem: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/lines/{line_item_id}',\n    }),\n    voidInvoice: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/invoices/{invoice}/void',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAe;IAChE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAyB;IAC3E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAyB;IAC1E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAyB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,iBAAiB,aAAa;QAC1B,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,mBAAmB,aAAa;QAC5B,QAAQ;QACR,UAAU;IACd;IACA,KAAK,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA6B;IAC3E,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,aAAa,aAAa;QACtB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5536, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Mandates.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Mandates = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/mandates/{mandate}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAyB;AAC/E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5555, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/OAuth.js"], "sourcesContent": ["'use strict';\nimport { StripeResource } from '../StripeResource.js';\nimport { queryStringifyRequestData } from '../utils.js';\nconst stripeMethod = StripeResource.method;\nconst oAuthHost = 'connect.stripe.com';\nexport const OAuth = StripeResource.extend({\n    basePath: '/',\n    authorizeUrl(params, options) {\n        params = params || {};\n        options = options || {};\n        let path = 'oauth/authorize';\n        // For Express accounts, the path changes\n        if (options.express) {\n            path = `express/${path}`;\n        }\n        if (!params.response_type) {\n            params.response_type = 'code';\n        }\n        if (!params.client_id) {\n            params.client_id = this._stripe.getClientId();\n        }\n        if (!params.scope) {\n            params.scope = 'read_write';\n        }\n        return `https://${oAuthHost}/${path}?${queryStringifyRequestData(params)}`;\n    },\n    token: stripeMethod({\n        method: 'POST',\n        path: 'oauth/token',\n        host: oAuthHost,\n    }),\n    deauthorize(spec, ...args) {\n        if (!spec.client_id) {\n            spec.client_id = this._stripe.getClientId();\n        }\n        return stripeMethod({\n            method: 'POST',\n            path: 'oauth/deauthorize',\n            host: oAuthHost,\n        }).apply(this, [spec, ...args]);\n    },\n});\n"], "names": [], "mappings": ";;;AACA;AACA;AAFA;;;AAGA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AAC1C,MAAM,YAAY;AACX,MAAM,QAAQ,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvC,UAAU;IACV,cAAa,MAAM,EAAE,OAAO;QACxB,SAAS,UAAU,CAAC;QACpB,UAAU,WAAW,CAAC;QACtB,IAAI,OAAO;QACX,yCAAyC;QACzC,IAAI,QAAQ,OAAO,EAAE;YACjB,OAAO,CAAC,QAAQ,EAAE,MAAM;QAC5B;QACA,IAAI,CAAC,OAAO,aAAa,EAAE;YACvB,OAAO,aAAa,GAAG;QAC3B;QACA,IAAI,CAAC,OAAO,SAAS,EAAE;YACnB,OAAO,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QAC/C;QACA,IAAI,CAAC,OAAO,KAAK,EAAE;YACf,OAAO,KAAK,GAAG;QACnB;QACA,OAAO,CAAC,QAAQ,EAAE,UAAU,CAAC,EAAE,KAAK,CAAC,EAAE,CAAA,GAAA,+NAAA,CAAA,4BAAyB,AAAD,EAAE,SAAS;IAC9E;IACA,OAAO,aAAa;QAChB,QAAQ;QACR,MAAM;QACN,MAAM;IACV;IACA,aAAY,IAAI,EAAE,GAAG,IAAI;QACrB,IAAI,CAAC,KAAK,SAAS,EAAE;YACjB,KAAK,SAAS,GAAG,IAAI,CAAC,OAAO,CAAC,WAAW;QAC7C;QACA,OAAO,aAAa;YAChB,QAAQ;YACR,MAAM;YACN,MAAM;QACV,GAAG,KAAK,CAAC,IAAI,EAAE;YAAC;eAAS;SAAK;IAClC;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5611, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PaymentIntents.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentIntents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_intents' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents/{intent}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents',\n        methodType: 'list',\n    }),\n    applyCustomerBalance: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/apply_customer_balance',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/cancel',\n    }),\n    capture: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/capture',\n    }),\n    confirm: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/confirm',\n    }),\n    incrementAuthorization: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/increment_authorization',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_intents/search',\n        methodType: 'search',\n    }),\n    verifyMicrodeposits: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_intents/{intent}/verify_microdeposits',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAsB;IACvE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,sBAAsB,aAAa;QAC/B,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,wBAAwB,aAAa;QACjC,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,qBAAqB,aAAa;QAC9B,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5672, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PaymentLinks.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentLinks = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_links' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links/{payment_link}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_links/{payment_link}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_links/{payment_link}/line_items',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;IACrE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5709, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PaymentMethodConfigurations.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethodConfigurations = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_configurations',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_configurations/{configuration}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_configurations/{configuration}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_configurations',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,8BAA8B,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC7D,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5741, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PaymentMethodDomains.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethodDomains = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_method_domains',\n        methodType: 'list',\n    }),\n    validate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_method_domains/{payment_method_domain}/validate',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,uBAAuB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACtD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5777, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PaymentMethods.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PaymentMethods = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payment_methods' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_methods/{payment_method}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payment_methods',\n        methodType: 'list',\n    }),\n    attach: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}/attach',\n    }),\n    detach: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payment_methods/{payment_method}/detach',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAsB;IACvE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5817, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Payouts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Payouts = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/payouts' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/payouts/{payout}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/payouts/{payout}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/payouts',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payouts/{payout}/cancel',\n    }),\n    reverse: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/payouts/{payout}/reverse',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5857, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Plans.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Plans = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/plans' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/plans/{plan}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/plans/{plan}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/plans',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/plans/{plan}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,QAAQ,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAY;IAC7D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAmB;IACrE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAmB;IACpE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAmB;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5893, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Prices.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Prices = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/prices' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/prices/{price}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/prices/{price}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/prices',\n        methodType: 'list',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/prices/search',\n        methodType: 'search',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAa;IAC9D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAqB;IACvE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5930, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Products.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Products = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/products' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/products/{id}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/products/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/products/{id}' }),\n    createFeature: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/products/{product}/features',\n    }),\n    deleteFeature: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/products/{product}/features/{id}',\n    }),\n    listFeatures: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/{product}/features',\n        methodType: 'list',\n    }),\n    retrieveFeature: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/{product}/features/{id}',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/products/search',\n        methodType: 'search',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAe;IAChE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAoB;IACtE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;IACrE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAoB;IACpE,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,cAAc,aAAa;QACvB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,iBAAiB,aAAa;QAC1B,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 5988, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/PromotionCodes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const PromotionCodes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/promotion_codes' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/promotion_codes/{promotion_code}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/promotion_codes/{promotion_code}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/promotion_codes',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,iBAAiB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAChD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAsB;IACvE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6020, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Quotes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Quotes = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/quotes' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/quotes/{quote}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes',\n        methodType: 'list',\n    }),\n    accept: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}/accept' }),\n    cancel: stripeMethod({ method: 'POST', fullPath: '/v1/quotes/{quote}/cancel' }),\n    finalizeQuote: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/quotes/{quote}/finalize',\n    }),\n    listComputedUpfrontLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/computed_upfront_line_items',\n        methodType: 'list',\n    }),\n    listLineItems: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/line_items',\n        methodType: 'list',\n    }),\n    pdf: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/quotes/{quote}/pdf',\n        host: 'files.stripe.com',\n        streaming: true,\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAa;IAC9D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAqB;IACvE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;IAC7E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;IAC7E,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;IACd;IACA,8BAA8B,aAAa;QACvC,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;QACV,MAAM;QACN,WAAW;IACf;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6080, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Refunds.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Refunds = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/refunds' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/refunds/{refund}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/refunds/{refund}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/refunds',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/refunds/{refund}/cancel',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6116, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Reviews.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Reviews = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/reviews/{review}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/reviews',\n        methodType: 'list',\n    }),\n    approve: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/reviews/{review}/approve',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6144, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/SetupAttempts.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SetupAttempts = StripeResource.extend({\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_attempts',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6164, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/SetupIntents.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SetupIntents = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/setup_intents' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_intents/{intent}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/setup_intents',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/cancel',\n    }),\n    confirm: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/confirm',\n    }),\n    verifyMicrodeposits: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/setup_intents/{intent}/verify_microdeposits',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC9C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;IACrE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,qBAAqB,aAAa;QAC9B,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6208, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/ShippingRates.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const ShippingRates = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/shipping_rates' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/shipping_rates/{shipping_rate_token}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/shipping_rates/{shipping_rate_token}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/shipping_rates',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6240, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Sources.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Sources = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/sources' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/sources/{source}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/sources/{source}' }),\n    listSourceTransactions: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/sources/{source}/source_transactions',\n        methodType: 'list',\n    }),\n    verify: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/sources/{source}/verify',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,UAAU,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACzC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAuB;IACzE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAuB;IACxE,wBAAwB,aAAa;QACjC,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6276, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/SubscriptionItems.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SubscriptionItems = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/subscription_items' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_items',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscription_items/{item}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,oBAAoB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACnD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAyB;IAC1E,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/SubscriptionSchedules.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const SubscriptionSchedules = StripeResource.extend({\n    create: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules',\n    }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_schedules/{schedule}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscription_schedules',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}/cancel',\n    }),\n    release: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscription_schedules/{schedule}/release',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,wBAAwB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACvD,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6352, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Subscriptions.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Subscriptions = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/subscriptions' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}',\n    }),\n    deleteDiscount: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/subscriptions/{subscription_exposed_id}/discount',\n    }),\n    migrate: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscriptions/{subscription}/migrate',\n    }),\n    resume: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/subscriptions/{subscription}/resume',\n    }),\n    search: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/subscriptions/search',\n        methodType: 'search',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,gBAAgB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC/C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAoB;IACrE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,SAAS,aAAa;QAClB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6405, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TaxCodes.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxCodes = StripeResource.extend({\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_codes/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_codes',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAqB;IACvE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TaxIds.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxIds = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax_ids' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_ids/{id}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_ids',\n        methodType: 'list',\n    }),\n    del: stripeMethod({ method: 'DELETE', fullPath: '/v1/tax_ids/{id}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAc;IAC/D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAmB;IACrE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QAAE,QAAQ;QAAU,UAAU;IAAmB;AACvE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6461, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/TaxRates.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const TaxRates = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tax_rates' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tax_rates/{tax_rate}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/tax_rates/{tax_rate}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/tax_rates',\n        methodType: 'list',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,WAAW,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC1C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAgB;IACjE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA2B;IAC7E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;IAC5E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6493, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Tokens.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Tokens = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/tokens' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/tokens/{token}' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAa;IAC9D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAqB;AAC3E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Topups.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Topups = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/topups' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/topups/{topup}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/topups/{topup}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/topups',\n        methodType: 'list',\n    }),\n    cancel: stripeMethod({ method: 'POST', fullPath: '/v1/topups/{topup}/cancel' }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,SAAS,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IACxC,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAa;IAC9D,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAAqB;IACvE,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAqB;IACtE,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA4B;AACjF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/Transfers.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const Transfers = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/transfers' }),\n    retrieve: stripeMethod({ method: 'GET', fullPath: '/v1/transfers/{transfer}' }),\n    update: stripeMethod({ method: 'POST', fullPath: '/v1/transfers/{transfer}' }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers',\n        methodType: 'list',\n    }),\n    createReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/transfers/{id}/reversals',\n    }),\n    listReversals: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers/{id}/reversals',\n        methodType: 'list',\n    }),\n    retrieveReversal: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/transfers/{transfer}/reversals/{id}',\n    }),\n    updateReversal: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/transfers/{transfer}/reversals/{id}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,YAAY,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAC3C,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAgB;IACjE,UAAU,aAAa;QAAE,QAAQ;QAAO,UAAU;IAA2B;IAC7E,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAA2B;IAC5E,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;IACA,eAAe,aAAa;QACxB,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,kBAAkB,aAAa;QAC3B,QAAQ;QACR,UAAU;IACd;IACA,gBAAgB,aAAa;QACzB,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6601, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources/WebhookEndpoints.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { StripeResource } from '../StripeResource.js';\nconst stripeMethod = StripeResource.method;\nexport const WebhookEndpoints = StripeResource.extend({\n    create: stripeMethod({ method: 'POST', fullPath: '/v1/webhook_endpoints' }),\n    retrieve: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n    update: stripeMethod({\n        method: 'POST',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n    list: stripeMethod({\n        method: 'GET',\n        fullPath: '/v1/webhook_endpoints',\n        methodType: 'list',\n    }),\n    del: stripeMethod({\n        method: 'DELETE',\n        fullPath: '/v1/webhook_endpoints/{webhook_endpoint}',\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;AACvC;;AACA,MAAM,eAAe,wOAAA,CAAA,iBAAc,CAAC,MAAM;AACnC,MAAM,mBAAmB,wOAAA,CAAA,iBAAc,CAAC,MAAM,CAAC;IAClD,QAAQ,aAAa;QAAE,QAAQ;QAAQ,UAAU;IAAwB;IACzE,UAAU,aAAa;QACnB,QAAQ;QACR,UAAU;IACd;IACA,QAAQ,aAAa;QACjB,QAAQ;QACR,UAAU;IACd;IACA,MAAM,aAAa;QACf,QAAQ;QACR,UAAU;QACV,YAAY;IAChB;IACA,KAAK,aAAa;QACd,QAAQ;QACR,UAAU;IACd;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 6637, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/resources.js"], "sourcesContent": ["// File generated from our OpenAPI spec\nimport { resourceNamespace } from './ResourceNamespace.js';\nimport { Accounts as FinancialConnectionsAccounts } from './resources/FinancialConnections/Accounts.js';\nimport { ActiveEntitlements as EntitlementsActiveEntitlements } from './resources/Entitlements/ActiveEntitlements.js';\nimport { Alerts as BillingAlerts } from './resources/Billing/Alerts.js';\nimport { Authorizations as TestHelpersIssuingAuthorizations } from './resources/TestHelpers/Issuing/Authorizations.js';\nimport { Authorizations as IssuingAuthorizations } from './resources/Issuing/Authorizations.js';\nimport { Calculations as TaxCalculations } from './resources/Tax/Calculations.js';\nimport { Cardholders as IssuingCardholders } from './resources/Issuing/Cardholders.js';\nimport { Cards as TestHelpersIssuingCards } from './resources/TestHelpers/Issuing/Cards.js';\nimport { Cards as IssuingCards } from './resources/Issuing/Cards.js';\nimport { Configurations as BillingPortalConfigurations } from './resources/BillingPortal/Configurations.js';\nimport { Configurations as TerminalConfigurations } from './resources/Terminal/Configurations.js';\nimport { ConfirmationTokens as TestHelpersConfirmationTokens } from './resources/TestHelpers/ConfirmationTokens.js';\nimport { ConnectionTokens as TerminalConnectionTokens } from './resources/Terminal/ConnectionTokens.js';\nimport { CreditBalanceSummary as BillingCreditBalanceSummary } from './resources/Billing/CreditBalanceSummary.js';\nimport { CreditBalanceTransactions as BillingCreditBalanceTransactions } from './resources/Billing/CreditBalanceTransactions.js';\nimport { CreditGrants as BillingCreditGrants } from './resources/Billing/CreditGrants.js';\nimport { CreditReversals as TreasuryCreditReversals } from './resources/Treasury/CreditReversals.js';\nimport { Customers as TestHelpersCustomers } from './resources/TestHelpers/Customers.js';\nimport { DebitReversals as TreasuryDebitReversals } from './resources/Treasury/DebitReversals.js';\nimport { Disputes as IssuingDisputes } from './resources/Issuing/Disputes.js';\nimport { EarlyFraudWarnings as RadarEarlyFraudWarnings } from './resources/Radar/EarlyFraudWarnings.js';\nimport { EventDestinations as V2CoreEventDestinations } from './resources/V2/Core/EventDestinations.js';\nimport { Events as V2CoreEvents } from './resources/V2/Core/Events.js';\nimport { Features as EntitlementsFeatures } from './resources/Entitlements/Features.js';\nimport { FinancialAccounts as TreasuryFinancialAccounts } from './resources/Treasury/FinancialAccounts.js';\nimport { InboundTransfers as TestHelpersTreasuryInboundTransfers } from './resources/TestHelpers/Treasury/InboundTransfers.js';\nimport { InboundTransfers as TreasuryInboundTransfers } from './resources/Treasury/InboundTransfers.js';\nimport { Locations as TerminalLocations } from './resources/Terminal/Locations.js';\nimport { MeterEventAdjustments as BillingMeterEventAdjustments } from './resources/Billing/MeterEventAdjustments.js';\nimport { MeterEventAdjustments as V2BillingMeterEventAdjustments } from './resources/V2/Billing/MeterEventAdjustments.js';\nimport { MeterEventSession as V2BillingMeterEventSession } from './resources/V2/Billing/MeterEventSession.js';\nimport { MeterEventStream as V2BillingMeterEventStream } from './resources/V2/Billing/MeterEventStream.js';\nimport { MeterEvents as BillingMeterEvents } from './resources/Billing/MeterEvents.js';\nimport { MeterEvents as V2BillingMeterEvents } from './resources/V2/Billing/MeterEvents.js';\nimport { Meters as BillingMeters } from './resources/Billing/Meters.js';\nimport { Orders as ClimateOrders } from './resources/Climate/Orders.js';\nimport { OutboundPayments as TestHelpersTreasuryOutboundPayments } from './resources/TestHelpers/Treasury/OutboundPayments.js';\nimport { OutboundPayments as TreasuryOutboundPayments } from './resources/Treasury/OutboundPayments.js';\nimport { OutboundTransfers as TestHelpersTreasuryOutboundTransfers } from './resources/TestHelpers/Treasury/OutboundTransfers.js';\nimport { OutboundTransfers as TreasuryOutboundTransfers } from './resources/Treasury/OutboundTransfers.js';\nimport { PersonalizationDesigns as TestHelpersIssuingPersonalizationDesigns } from './resources/TestHelpers/Issuing/PersonalizationDesigns.js';\nimport { PersonalizationDesigns as IssuingPersonalizationDesigns } from './resources/Issuing/PersonalizationDesigns.js';\nimport { PhysicalBundles as IssuingPhysicalBundles } from './resources/Issuing/PhysicalBundles.js';\nimport { Products as ClimateProducts } from './resources/Climate/Products.js';\nimport { Readers as TestHelpersTerminalReaders } from './resources/TestHelpers/Terminal/Readers.js';\nimport { Readers as TerminalReaders } from './resources/Terminal/Readers.js';\nimport { ReceivedCredits as TestHelpersTreasuryReceivedCredits } from './resources/TestHelpers/Treasury/ReceivedCredits.js';\nimport { ReceivedCredits as TreasuryReceivedCredits } from './resources/Treasury/ReceivedCredits.js';\nimport { ReceivedDebits as TestHelpersTreasuryReceivedDebits } from './resources/TestHelpers/Treasury/ReceivedDebits.js';\nimport { ReceivedDebits as TreasuryReceivedDebits } from './resources/Treasury/ReceivedDebits.js';\nimport { Refunds as TestHelpersRefunds } from './resources/TestHelpers/Refunds.js';\nimport { Registrations as TaxRegistrations } from './resources/Tax/Registrations.js';\nimport { ReportRuns as ReportingReportRuns } from './resources/Reporting/ReportRuns.js';\nimport { ReportTypes as ReportingReportTypes } from './resources/Reporting/ReportTypes.js';\nimport { Requests as ForwardingRequests } from './resources/Forwarding/Requests.js';\nimport { ScheduledQueryRuns as SigmaScheduledQueryRuns } from './resources/Sigma/ScheduledQueryRuns.js';\nimport { Secrets as AppsSecrets } from './resources/Apps/Secrets.js';\nimport { Sessions as BillingPortalSessions } from './resources/BillingPortal/Sessions.js';\nimport { Sessions as CheckoutSessions } from './resources/Checkout/Sessions.js';\nimport { Sessions as FinancialConnectionsSessions } from './resources/FinancialConnections/Sessions.js';\nimport { Settings as TaxSettings } from './resources/Tax/Settings.js';\nimport { Suppliers as ClimateSuppliers } from './resources/Climate/Suppliers.js';\nimport { TestClocks as TestHelpersTestClocks } from './resources/TestHelpers/TestClocks.js';\nimport { Tokens as IssuingTokens } from './resources/Issuing/Tokens.js';\nimport { TransactionEntries as TreasuryTransactionEntries } from './resources/Treasury/TransactionEntries.js';\nimport { Transactions as TestHelpersIssuingTransactions } from './resources/TestHelpers/Issuing/Transactions.js';\nimport { Transactions as FinancialConnectionsTransactions } from './resources/FinancialConnections/Transactions.js';\nimport { Transactions as IssuingTransactions } from './resources/Issuing/Transactions.js';\nimport { Transactions as TaxTransactions } from './resources/Tax/Transactions.js';\nimport { Transactions as TreasuryTransactions } from './resources/Treasury/Transactions.js';\nimport { ValueListItems as RadarValueListItems } from './resources/Radar/ValueListItems.js';\nimport { ValueLists as RadarValueLists } from './resources/Radar/ValueLists.js';\nimport { VerificationReports as IdentityVerificationReports } from './resources/Identity/VerificationReports.js';\nimport { VerificationSessions as IdentityVerificationSessions } from './resources/Identity/VerificationSessions.js';\nexport { Accounts as Account } from './resources/Accounts.js';\nexport { AccountLinks } from './resources/AccountLinks.js';\nexport { AccountSessions } from './resources/AccountSessions.js';\nexport { Accounts } from './resources/Accounts.js';\nexport { ApplePayDomains } from './resources/ApplePayDomains.js';\nexport { ApplicationFees } from './resources/ApplicationFees.js';\nexport { Balance } from './resources/Balance.js';\nexport { BalanceTransactions } from './resources/BalanceTransactions.js';\nexport { Charges } from './resources/Charges.js';\nexport { ConfirmationTokens } from './resources/ConfirmationTokens.js';\nexport { CountrySpecs } from './resources/CountrySpecs.js';\nexport { Coupons } from './resources/Coupons.js';\nexport { CreditNotes } from './resources/CreditNotes.js';\nexport { CustomerSessions } from './resources/CustomerSessions.js';\nexport { Customers } from './resources/Customers.js';\nexport { Disputes } from './resources/Disputes.js';\nexport { EphemeralKeys } from './resources/EphemeralKeys.js';\nexport { Events } from './resources/Events.js';\nexport { ExchangeRates } from './resources/ExchangeRates.js';\nexport { FileLinks } from './resources/FileLinks.js';\nexport { Files } from './resources/Files.js';\nexport { InvoiceItems } from './resources/InvoiceItems.js';\nexport { InvoicePayments } from './resources/InvoicePayments.js';\nexport { InvoiceRenderingTemplates } from './resources/InvoiceRenderingTemplates.js';\nexport { Invoices } from './resources/Invoices.js';\nexport { Mandates } from './resources/Mandates.js';\nexport { OAuth } from './resources/OAuth.js';\nexport { PaymentIntents } from './resources/PaymentIntents.js';\nexport { PaymentLinks } from './resources/PaymentLinks.js';\nexport { PaymentMethodConfigurations } from './resources/PaymentMethodConfigurations.js';\nexport { PaymentMethodDomains } from './resources/PaymentMethodDomains.js';\nexport { PaymentMethods } from './resources/PaymentMethods.js';\nexport { Payouts } from './resources/Payouts.js';\nexport { Plans } from './resources/Plans.js';\nexport { Prices } from './resources/Prices.js';\nexport { Products } from './resources/Products.js';\nexport { PromotionCodes } from './resources/PromotionCodes.js';\nexport { Quotes } from './resources/Quotes.js';\nexport { Refunds } from './resources/Refunds.js';\nexport { Reviews } from './resources/Reviews.js';\nexport { SetupAttempts } from './resources/SetupAttempts.js';\nexport { SetupIntents } from './resources/SetupIntents.js';\nexport { ShippingRates } from './resources/ShippingRates.js';\nexport { Sources } from './resources/Sources.js';\nexport { SubscriptionItems } from './resources/SubscriptionItems.js';\nexport { SubscriptionSchedules } from './resources/SubscriptionSchedules.js';\nexport { Subscriptions } from './resources/Subscriptions.js';\nexport { TaxCodes } from './resources/TaxCodes.js';\nexport { TaxIds } from './resources/TaxIds.js';\nexport { TaxRates } from './resources/TaxRates.js';\nexport { Tokens } from './resources/Tokens.js';\nexport { Topups } from './resources/Topups.js';\nexport { Transfers } from './resources/Transfers.js';\nexport { WebhookEndpoints } from './resources/WebhookEndpoints.js';\nexport const Apps = resourceNamespace('apps', { Secrets: AppsSecrets });\nexport const Billing = resourceNamespace('billing', {\n    Alerts: BillingAlerts,\n    CreditBalanceSummary: BillingCreditBalanceSummary,\n    CreditBalanceTransactions: BillingCreditBalanceTransactions,\n    CreditGrants: BillingCreditGrants,\n    MeterEventAdjustments: BillingMeterEventAdjustments,\n    MeterEvents: BillingMeterEvents,\n    Meters: BillingMeters,\n});\nexport const BillingPortal = resourceNamespace('billingPortal', {\n    Configurations: BillingPortalConfigurations,\n    Sessions: BillingPortalSessions,\n});\nexport const Checkout = resourceNamespace('checkout', {\n    Sessions: CheckoutSessions,\n});\nexport const Climate = resourceNamespace('climate', {\n    Orders: ClimateOrders,\n    Products: ClimateProducts,\n    Suppliers: ClimateSuppliers,\n});\nexport const Entitlements = resourceNamespace('entitlements', {\n    ActiveEntitlements: EntitlementsActiveEntitlements,\n    Features: EntitlementsFeatures,\n});\nexport const FinancialConnections = resourceNamespace('financialConnections', {\n    Accounts: FinancialConnectionsAccounts,\n    Sessions: FinancialConnectionsSessions,\n    Transactions: FinancialConnectionsTransactions,\n});\nexport const Forwarding = resourceNamespace('forwarding', {\n    Requests: ForwardingRequests,\n});\nexport const Identity = resourceNamespace('identity', {\n    VerificationReports: IdentityVerificationReports,\n    VerificationSessions: IdentityVerificationSessions,\n});\nexport const Issuing = resourceNamespace('issuing', {\n    Authorizations: IssuingAuthorizations,\n    Cardholders: IssuingCardholders,\n    Cards: IssuingCards,\n    Disputes: IssuingDisputes,\n    PersonalizationDesigns: IssuingPersonalizationDesigns,\n    PhysicalBundles: IssuingPhysicalBundles,\n    Tokens: IssuingTokens,\n    Transactions: IssuingTransactions,\n});\nexport const Radar = resourceNamespace('radar', {\n    EarlyFraudWarnings: RadarEarlyFraudWarnings,\n    ValueListItems: RadarValueListItems,\n    ValueLists: RadarValueLists,\n});\nexport const Reporting = resourceNamespace('reporting', {\n    ReportRuns: ReportingReportRuns,\n    ReportTypes: ReportingReportTypes,\n});\nexport const Sigma = resourceNamespace('sigma', {\n    ScheduledQueryRuns: SigmaScheduledQueryRuns,\n});\nexport const Tax = resourceNamespace('tax', {\n    Calculations: TaxCalculations,\n    Registrations: TaxRegistrations,\n    Settings: TaxSettings,\n    Transactions: TaxTransactions,\n});\nexport const Terminal = resourceNamespace('terminal', {\n    Configurations: TerminalConfigurations,\n    ConnectionTokens: TerminalConnectionTokens,\n    Locations: TerminalLocations,\n    Readers: TerminalReaders,\n});\nexport const TestHelpers = resourceNamespace('testHelpers', {\n    ConfirmationTokens: TestHelpersConfirmationTokens,\n    Customers: TestHelpersCustomers,\n    Refunds: TestHelpersRefunds,\n    TestClocks: TestHelpersTestClocks,\n    Issuing: resourceNamespace('issuing', {\n        Authorizations: TestHelpersIssuingAuthorizations,\n        Cards: TestHelpersIssuingCards,\n        PersonalizationDesigns: TestHelpersIssuingPersonalizationDesigns,\n        Transactions: TestHelpersIssuingTransactions,\n    }),\n    Terminal: resourceNamespace('terminal', {\n        Readers: TestHelpersTerminalReaders,\n    }),\n    Treasury: resourceNamespace('treasury', {\n        InboundTransfers: TestHelpersTreasuryInboundTransfers,\n        OutboundPayments: TestHelpersTreasuryOutboundPayments,\n        OutboundTransfers: TestHelpersTreasuryOutboundTransfers,\n        ReceivedCredits: TestHelpersTreasuryReceivedCredits,\n        ReceivedDebits: TestHelpersTreasuryReceivedDebits,\n    }),\n});\nexport const Treasury = resourceNamespace('treasury', {\n    CreditReversals: TreasuryCreditReversals,\n    DebitReversals: TreasuryDebitReversals,\n    FinancialAccounts: TreasuryFinancialAccounts,\n    InboundTransfers: TreasuryInboundTransfers,\n    OutboundPayments: TreasuryOutboundPayments,\n    OutboundTransfers: TreasuryOutboundTransfers,\n    ReceivedCredits: TreasuryReceivedCredits,\n    ReceivedDebits: TreasuryReceivedDebits,\n    TransactionEntries: TreasuryTransactionEntries,\n    Transactions: TreasuryTransactions,\n});\nexport const V2 = resourceNamespace('v2', {\n    Billing: resourceNamespace('billing', {\n        MeterEventAdjustments: V2BillingMeterEventAdjustments,\n        MeterEventSession: V2BillingMeterEventSession,\n        MeterEventStream: V2BillingMeterEventStream,\n        MeterEvents: V2BillingMeterEvents,\n    }),\n    Core: resourceNamespace('core', {\n        EventDestinations: V2CoreEventDestinations,\n        Events: V2CoreEvents,\n    }),\n});\n"], "names": [], "mappings": "AAAA,uCAAuC;;;;;;;;;;;;;;;;;;;;;AACvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACO,MAAM,OAAO,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;IAAE,SAAS,sPAAA,CAAA,UAAW;AAAC;AAC9D,MAAM,UAAU,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;IAChD,QAAQ,wPAAA,CAAA,SAAa;IACrB,sBAAsB,sQAAA,CAAA,uBAA2B;IACjD,2BAA2B,2QAAA,CAAA,4BAAgC;IAC3D,cAAc,8PAAA,CAAA,eAAmB;IACjC,uBAAuB,uQAAA,CAAA,wBAA4B;IACnD,aAAa,6PAAA,CAAA,cAAkB;IAC/B,QAAQ,wPAAA,CAAA,SAAa;AACzB;AACO,MAAM,gBAAgB,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,iBAAiB;IAC5D,gBAAgB,sQAAA,CAAA,iBAA2B;IAC3C,UAAU,gQAAA,CAAA,WAAqB;AACnC;AACO,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;IAClD,UAAU,2PAAA,CAAA,WAAgB;AAC9B;AACO,MAAM,UAAU,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;IAChD,QAAQ,wPAAA,CAAA,SAAa;IACrB,UAAU,0PAAA,CAAA,WAAe;IACzB,WAAW,2PAAA,CAAA,YAAgB;AAC/B;AACO,MAAM,eAAe,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,gBAAgB;IAC1D,oBAAoB,yQAAA,CAAA,qBAA8B;IAClD,UAAU,+PAAA,CAAA,WAAoB;AAClC;AACO,MAAM,uBAAuB,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,wBAAwB;IAC1E,UAAU,uQAAA,CAAA,WAA4B;IACtC,UAAU,uQAAA,CAAA,WAA4B;IACtC,cAAc,2QAAA,CAAA,eAAgC;AAClD;AACO,MAAM,aAAa,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,cAAc;IACtD,UAAU,6PAAA,CAAA,WAAkB;AAChC;AACO,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;IAClD,qBAAqB,sQAAA,CAAA,sBAA2B;IAChD,sBAAsB,uQAAA,CAAA,uBAA4B;AACtD;AACO,MAAM,UAAU,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;IAChD,gBAAgB,gQAAA,CAAA,iBAAqB;IACrC,aAAa,6PAAA,CAAA,cAAkB;IAC/B,OAAO,uPAAA,CAAA,QAAY;IACnB,UAAU,0PAAA,CAAA,WAAe;IACzB,wBAAwB,wQAAA,CAAA,yBAA6B;IACrD,iBAAiB,iQAAA,CAAA,kBAAsB;IACvC,QAAQ,wPAAA,CAAA,SAAa;IACrB,cAAc,8PAAA,CAAA,eAAmB;AACrC;AACO,MAAM,QAAQ,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;IAC5C,oBAAoB,kQAAA,CAAA,qBAAuB;IAC3C,gBAAgB,8PAAA,CAAA,iBAAmB;IACnC,YAAY,0PAAA,CAAA,aAAe;AAC/B;AACO,MAAM,YAAY,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,aAAa;IACpD,YAAY,8PAAA,CAAA,aAAmB;IAC/B,aAAa,+PAAA,CAAA,cAAoB;AACrC;AACO,MAAM,QAAQ,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,SAAS;IAC5C,oBAAoB,kQAAA,CAAA,qBAAuB;AAC/C;AACO,MAAM,MAAM,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,OAAO;IACxC,cAAc,0PAAA,CAAA,eAAe;IAC7B,eAAe,2PAAA,CAAA,gBAAgB;IAC/B,UAAU,sPAAA,CAAA,WAAW;IACrB,cAAc,0PAAA,CAAA,eAAe;AACjC;AACO,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;IAClD,gBAAgB,iQAAA,CAAA,iBAAsB;IACtC,kBAAkB,mQAAA,CAAA,mBAAwB;IAC1C,WAAW,4PAAA,CAAA,YAAiB;IAC5B,SAAS,0PAAA,CAAA,UAAe;AAC5B;AACO,MAAM,cAAc,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,eAAe;IACxD,oBAAoB,wQAAA,CAAA,qBAA6B;IACjD,WAAW,+PAAA,CAAA,YAAoB;IAC/B,SAAS,6PAAA,CAAA,UAAkB;IAC3B,YAAY,gQAAA,CAAA,aAAqB;IACjC,SAAS,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAClC,gBAAgB,+QAAA,CAAA,iBAAgC;QAChD,OAAO,sQAAA,CAAA,QAAuB;QAC9B,wBAAwB,uRAAA,CAAA,yBAAwC;QAChE,cAAc,6QAAA,CAAA,eAA8B;IAChD;IACA,UAAU,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;QACpC,SAAS,yQAAA,CAAA,UAA0B;IACvC;IACA,UAAU,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;QACpC,kBAAkB,kRAAA,CAAA,mBAAmC;QACrD,kBAAkB,kRAAA,CAAA,mBAAmC;QACrD,mBAAmB,mRAAA,CAAA,oBAAoC;QACvD,iBAAiB,iRAAA,CAAA,kBAAkC;QACnD,gBAAgB,gRAAA,CAAA,iBAAiC;IACrD;AACJ;AACO,MAAM,WAAW,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,YAAY;IAClD,iBAAiB,kQAAA,CAAA,kBAAuB;IACxC,gBAAgB,iQAAA,CAAA,iBAAsB;IACtC,mBAAmB,oQAAA,CAAA,oBAAyB;IAC5C,kBAAkB,mQAAA,CAAA,mBAAwB;IAC1C,kBAAkB,mQAAA,CAAA,mBAAwB;IAC1C,mBAAmB,oQAAA,CAAA,oBAAyB;IAC5C,iBAAiB,kQAAA,CAAA,kBAAuB;IACxC,gBAAgB,iQAAA,CAAA,iBAAsB;IACtC,oBAAoB,qQAAA,CAAA,qBAA0B;IAC9C,cAAc,+PAAA,CAAA,eAAoB;AACtC;AACO,MAAM,KAAK,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM;IACtC,SAAS,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,WAAW;QAClC,uBAAuB,6QAAA,CAAA,wBAA8B;QACrD,mBAAmB,yQAAA,CAAA,oBAA0B;QAC7C,kBAAkB,wQAAA,CAAA,mBAAyB;QAC3C,aAAa,mQAAA,CAAA,cAAoB;IACrC;IACA,MAAM,CAAA,GAAA,2OAAA,CAAA,oBAAiB,AAAD,EAAE,QAAQ;QAC5B,mBAAmB,sQAAA,CAAA,oBAAuB;QAC1C,QAAQ,2PAAA,CAAA,SAAY;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7393, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/stripe.core.js"], "sourcesContent": ["import * as _Error from './Error.js';\nimport { RequestSender } from './RequestSender.js';\nimport { StripeResource } from './StripeResource.js';\nimport { createWebhooks } from './Webhooks.js';\nimport { ApiVersion } from './apiVersion.js';\nimport { CryptoProvider } from './crypto/CryptoProvider.js';\nimport { HttpClient, HttpClientResponse } from './net/HttpClient.js';\nimport * as resources from './resources.js';\nimport { createApiKeyAuthenticator, determineProcessUserAgentProperties, pascalToCamelCase, validateInteger, } from './utils.js';\nconst DEFAULT_HOST = 'api.stripe.com';\nconst DEFAULT_PORT = '443';\nconst DEFAULT_BASE_PATH = '/v1/';\nconst DEFAULT_API_VERSION = ApiVersion;\nconst DEFAULT_TIMEOUT = 80000;\nconst MAX_NETWORK_RETRY_DELAY_SEC = 5;\nconst INITIAL_NETWORK_RETRY_DELAY_SEC = 0.5;\nconst APP_INFO_PROPERTIES = ['name', 'version', 'url', 'partner_id'];\nconst ALLOWED_CONFIG_PROPERTIES = [\n    'authenticator',\n    'apiVersion',\n    'typescript',\n    'maxNetworkRetries',\n    'httpAgent',\n    'httpClient',\n    'timeout',\n    'host',\n    'port',\n    'protocol',\n    'telemetry',\n    'appInfo',\n    'stripeAccount',\n    'stripeContext',\n];\nconst defaultRequestSenderFactory = (stripe) => new RequestSender(stripe, StripeResource.MAX_BUFFERED_REQUEST_METRICS);\nexport function createStripe(platformFunctions, requestSender = defaultRequestSenderFactory) {\n    Stripe.PACKAGE_VERSION = '18.3.0';\n    Stripe.USER_AGENT = Object.assign({ bindings_version: Stripe.PACKAGE_VERSION, lang: 'node', publisher: 'stripe', uname: null, typescript: false }, determineProcessUserAgentProperties());\n    Stripe.StripeResource = StripeResource;\n    Stripe.resources = resources;\n    Stripe.HttpClient = HttpClient;\n    Stripe.HttpClientResponse = HttpClientResponse;\n    Stripe.CryptoProvider = CryptoProvider;\n    Stripe.webhooks = createWebhooks(platformFunctions);\n    function Stripe(key, config = {}) {\n        if (!(this instanceof Stripe)) {\n            return new Stripe(key, config);\n        }\n        const props = this._getPropsFromConfig(config);\n        this._platformFunctions = platformFunctions;\n        Object.defineProperty(this, '_emitter', {\n            value: this._platformFunctions.createEmitter(),\n            enumerable: false,\n            configurable: false,\n            writable: false,\n        });\n        this.VERSION = Stripe.PACKAGE_VERSION;\n        this.on = this._emitter.on.bind(this._emitter);\n        this.once = this._emitter.once.bind(this._emitter);\n        this.off = this._emitter.removeListener.bind(this._emitter);\n        const agent = props.httpAgent || null;\n        this._api = {\n            host: props.host || DEFAULT_HOST,\n            port: props.port || DEFAULT_PORT,\n            protocol: props.protocol || 'https',\n            basePath: DEFAULT_BASE_PATH,\n            version: props.apiVersion || DEFAULT_API_VERSION,\n            timeout: validateInteger('timeout', props.timeout, DEFAULT_TIMEOUT),\n            maxNetworkRetries: validateInteger('maxNetworkRetries', props.maxNetworkRetries, 2),\n            agent: agent,\n            httpClient: props.httpClient ||\n                (agent\n                    ? this._platformFunctions.createNodeHttpClient(agent)\n                    : this._platformFunctions.createDefaultHttpClient()),\n            dev: false,\n            stripeAccount: props.stripeAccount || null,\n            stripeContext: props.stripeContext || null,\n        };\n        const typescript = props.typescript || false;\n        if (typescript !== Stripe.USER_AGENT.typescript) {\n            // The mutation here is uncomfortable, but likely fastest;\n            // serializing the user agent involves shelling out to the system,\n            // and given some users may instantiate the library many times without switching between TS and non-TS,\n            // we only want to incur the performance hit when that actually happens.\n            Stripe.USER_AGENT.typescript = typescript;\n        }\n        if (props.appInfo) {\n            this._setAppInfo(props.appInfo);\n        }\n        this._prepResources();\n        this._setAuthenticator(key, props.authenticator);\n        this.errors = _Error;\n        this.webhooks = Stripe.webhooks;\n        this._prevRequestMetrics = [];\n        this._enableTelemetry = props.telemetry !== false;\n        this._requestSender = requestSender(this);\n        // Expose StripeResource on the instance too\n        // @ts-ignore\n        this.StripeResource = Stripe.StripeResource;\n    }\n    Stripe.errors = _Error;\n    Stripe.createNodeHttpClient = platformFunctions.createNodeHttpClient;\n    /**\n     * Creates an HTTP client for issuing Stripe API requests which uses the Web\n     * Fetch API.\n     *\n     * A fetch function can optionally be passed in as a parameter. If none is\n     * passed, will default to the default `fetch` function in the global scope.\n     */\n    Stripe.createFetchHttpClient = platformFunctions.createFetchHttpClient;\n    /**\n     * Create a CryptoProvider which uses the built-in Node crypto libraries for\n     * its crypto operations.\n     */\n    Stripe.createNodeCryptoProvider = platformFunctions.createNodeCryptoProvider;\n    /**\n     * Creates a CryptoProvider which uses the Subtle Crypto API from the Web\n     * Crypto API spec for its crypto operations.\n     *\n     * A SubtleCrypto interface can optionally be passed in as a parameter. If none\n     * is passed, will default to the default `crypto.subtle` object in the global\n     * scope.\n     */\n    Stripe.createSubtleCryptoProvider =\n        platformFunctions.createSubtleCryptoProvider;\n    Stripe.prototype = {\n        // Properties are set in the constructor above\n        _appInfo: undefined,\n        on: null,\n        off: null,\n        once: null,\n        VERSION: null,\n        StripeResource: null,\n        webhooks: null,\n        errors: null,\n        _api: null,\n        _prevRequestMetrics: null,\n        _emitter: null,\n        _enableTelemetry: null,\n        _requestSender: null,\n        _platformFunctions: null,\n        rawRequest(method, path, params, options) {\n            return this._requestSender._rawRequest(method, path, params, options);\n        },\n        /**\n         * @private\n         */\n        _setAuthenticator(key, authenticator) {\n            if (key && authenticator) {\n                throw new Error(\"Can't specify both apiKey and authenticator\");\n            }\n            if (!key && !authenticator) {\n                throw new Error('Neither apiKey nor config.authenticator provided');\n            }\n            this._authenticator = key\n                ? createApiKeyAuthenticator(key)\n                : authenticator;\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setAppInfo(info) {\n            if (info && typeof info !== 'object') {\n                throw new Error('AppInfo must be an object.');\n            }\n            if (info && !info.name) {\n                throw new Error('AppInfo.name is required');\n            }\n            info = info || {};\n            this._appInfo = APP_INFO_PROPERTIES.reduce((accum, prop) => {\n                if (typeof info[prop] == 'string') {\n                    accum = accum || {};\n                    accum[prop] = info[prop];\n                }\n                return accum;\n            }, {});\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setApiField(key, value) {\n            this._api[key] = value;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getApiField(key) {\n            return this._api[key];\n        },\n        setClientId(clientId) {\n            this._clientId = clientId;\n        },\n        getClientId() {\n            return this._clientId;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getConstant: (c) => {\n            switch (c) {\n                case 'DEFAULT_HOST':\n                    return DEFAULT_HOST;\n                case 'DEFAULT_PORT':\n                    return DEFAULT_PORT;\n                case 'DEFAULT_BASE_PATH':\n                    return DEFAULT_BASE_PATH;\n                case 'DEFAULT_API_VERSION':\n                    return DEFAULT_API_VERSION;\n                case 'DEFAULT_TIMEOUT':\n                    return DEFAULT_TIMEOUT;\n                case 'MAX_NETWORK_RETRY_DELAY_SEC':\n                    return MAX_NETWORK_RETRY_DELAY_SEC;\n                case 'INITIAL_NETWORK_RETRY_DELAY_SEC':\n                    return INITIAL_NETWORK_RETRY_DELAY_SEC;\n            }\n            return Stripe[c];\n        },\n        getMaxNetworkRetries() {\n            return this.getApiField('maxNetworkRetries');\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _setApiNumberField(prop, n, defaultVal) {\n            const val = validateInteger(prop, n, defaultVal);\n            this._setApiField(prop, val);\n        },\n        getMaxNetworkRetryDelay() {\n            return MAX_NETWORK_RETRY_DELAY_SEC;\n        },\n        getInitialNetworkRetryDelay() {\n            return INITIAL_NETWORK_RETRY_DELAY_SEC;\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         *\n         * Gets a JSON version of a User-Agent and uses a cached version for a slight\n         * speed advantage.\n         */\n        getClientUserAgent(cb) {\n            return this.getClientUserAgentSeeded(Stripe.USER_AGENT, cb);\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         *\n         * Gets a JSON version of a User-Agent by encoding a seeded object and\n         * fetching a uname from the system.\n         */\n        getClientUserAgentSeeded(seed, cb) {\n            this._platformFunctions.getUname().then((uname) => {\n                var _a;\n                const userAgent = {};\n                for (const field in seed) {\n                    if (!Object.prototype.hasOwnProperty.call(seed, field)) {\n                        continue;\n                    }\n                    userAgent[field] = encodeURIComponent((_a = seed[field]) !== null && _a !== void 0 ? _a : 'null');\n                }\n                // URI-encode in case there are unusual characters in the system's uname.\n                userAgent.uname = encodeURIComponent(uname || 'UNKNOWN');\n                const client = this.getApiField('httpClient');\n                if (client) {\n                    userAgent.httplib = encodeURIComponent(client.getClientName());\n                }\n                if (this._appInfo) {\n                    userAgent.application = this._appInfo;\n                }\n                cb(JSON.stringify(userAgent));\n            });\n        },\n        /**\n         * @private\n         * Please open or upvote an issue at github.com/stripe/stripe-node\n         * if you use this, detailing your use-case.\n         *\n         * It may be deprecated and removed in the future.\n         */\n        getAppInfoAsString() {\n            if (!this._appInfo) {\n                return '';\n            }\n            let formatted = this._appInfo.name;\n            if (this._appInfo.version) {\n                formatted += `/${this._appInfo.version}`;\n            }\n            if (this._appInfo.url) {\n                formatted += ` (${this._appInfo.url})`;\n            }\n            return formatted;\n        },\n        getTelemetryEnabled() {\n            return this._enableTelemetry;\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _prepResources() {\n            for (const name in resources) {\n                if (!Object.prototype.hasOwnProperty.call(resources, name)) {\n                    continue;\n                }\n                // @ts-ignore\n                this[pascalToCamelCase(name)] = new resources[name](this);\n            }\n        },\n        /**\n         * @private\n         * This may be removed in the future.\n         */\n        _getPropsFromConfig(config) {\n            // If config is null or undefined, just bail early with no props\n            if (!config) {\n                return {};\n            }\n            // config can be an object or a string\n            const isString = typeof config === 'string';\n            const isObject = config === Object(config) && !Array.isArray(config);\n            if (!isObject && !isString) {\n                throw new Error('Config must either be an object or a string');\n            }\n            // If config is a string, we assume the old behavior of passing in a string representation of the api version\n            if (isString) {\n                return {\n                    apiVersion: config,\n                };\n            }\n            // If config is an object, we assume the new behavior and make sure it doesn't contain any unexpected values\n            const values = Object.keys(config).filter((value) => !ALLOWED_CONFIG_PROPERTIES.includes(value));\n            if (values.length > 0) {\n                throw new Error(`Config object may only contain the following: ${ALLOWED_CONFIG_PROPERTIES.join(', ')}`);\n            }\n            return config;\n        },\n        parseThinEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt) {\n            // parses and validates the event payload all in one go\n            return this.webhooks.constructEvent(payload, header, secret, tolerance, cryptoProvider, receivedAt);\n        },\n    };\n    return Stripe;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;AACA;;;;;;;;;;AACA,MAAM,eAAe;AACrB,MAAM,eAAe;AACrB,MAAM,oBAAoB;AAC1B,MAAM,sBAAsB,oOAAA,CAAA,aAAU;AACtC,MAAM,kBAAkB;AACxB,MAAM,8BAA8B;AACpC,MAAM,kCAAkC;AACxC,MAAM,sBAAsB;IAAC;IAAQ;IAAW;IAAO;CAAa;AACpE,MAAM,4BAA4B;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,MAAM,8BAA8B,CAAC,SAAW,IAAI,uOAAA,CAAA,gBAAa,CAAC,QAAQ,wOAAA,CAAA,iBAAc,CAAC,4BAA4B;AAC9G,SAAS,aAAa,iBAAiB,EAAE,gBAAgB,2BAA2B;IACvF,OAAO,eAAe,GAAG;IACzB,OAAO,UAAU,GAAG,OAAO,MAAM,CAAC;QAAE,kBAAkB,OAAO,eAAe;QAAE,MAAM;QAAQ,WAAW;QAAU,OAAO;QAAM,YAAY;IAAM,GAAG,CAAA,GAAA,+NAAA,CAAA,sCAAmC,AAAD;IACrL,OAAO,cAAc,GAAG,wOAAA,CAAA,iBAAc;IACtC,OAAO,SAAS,GAAG;IACnB,OAAO,UAAU,GAAG,2OAAA,CAAA,aAAU;IAC9B,OAAO,kBAAkB,GAAG,2OAAA,CAAA,qBAAkB;IAC9C,OAAO,cAAc,GAAG,kPAAA,CAAA,iBAAc;IACtC,OAAO,QAAQ,GAAG,CAAA,GAAA,kOAAA,CAAA,iBAAc,AAAD,EAAE;IACjC,SAAS,OAAO,GAAG,EAAE,SAAS,CAAC,CAAC;QAC5B,IAAI,CAAC,CAAC,IAAI,YAAY,MAAM,GAAG;YAC3B,OAAO,IAAI,OAAO,KAAK;QAC3B;QACA,MAAM,QAAQ,IAAI,CAAC,mBAAmB,CAAC;QACvC,IAAI,CAAC,kBAAkB,GAAG;QAC1B,OAAO,cAAc,CAAC,IAAI,EAAE,YAAY;YACpC,OAAO,IAAI,CAAC,kBAAkB,CAAC,aAAa;YAC5C,YAAY;YACZ,cAAc;YACd,UAAU;QACd;QACA,IAAI,CAAC,OAAO,GAAG,OAAO,eAAe;QACrC,IAAI,CAAC,EAAE,GAAG,IAAI,CAAC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC7C,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;QACjD,IAAI,CAAC,GAAG,GAAG,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ;QAC1D,MAAM,QAAQ,MAAM,SAAS,IAAI;QACjC,IAAI,CAAC,IAAI,GAAG;YACR,MAAM,MAAM,IAAI,IAAI;YACpB,MAAM,MAAM,IAAI,IAAI;YACpB,UAAU,MAAM,QAAQ,IAAI;YAC5B,UAAU;YACV,SAAS,MAAM,UAAU,IAAI;YAC7B,SAAS,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,WAAW,MAAM,OAAO,EAAE;YACnD,mBAAmB,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,qBAAqB,MAAM,iBAAiB,EAAE;YACjF,OAAO;YACP,YAAY,MAAM,UAAU,IACxB,CAAC,QACK,IAAI,CAAC,kBAAkB,CAAC,oBAAoB,CAAC,SAC7C,IAAI,CAAC,kBAAkB,CAAC,uBAAuB,EAAE;YAC3D,KAAK;YACL,eAAe,MAAM,aAAa,IAAI;YACtC,eAAe,MAAM,aAAa,IAAI;QAC1C;QACA,MAAM,aAAa,MAAM,UAAU,IAAI;QACvC,IAAI,eAAe,OAAO,UAAU,CAAC,UAAU,EAAE;YAC7C,0DAA0D;YAC1D,kEAAkE;YAClE,uGAAuG;YACvG,wEAAwE;YACxE,OAAO,UAAU,CAAC,UAAU,GAAG;QACnC;QACA,IAAI,MAAM,OAAO,EAAE;YACf,IAAI,CAAC,WAAW,CAAC,MAAM,OAAO;QAClC;QACA,IAAI,CAAC,cAAc;QACnB,IAAI,CAAC,iBAAiB,CAAC,KAAK,MAAM,aAAa;QAC/C,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,QAAQ,GAAG,OAAO,QAAQ;QAC/B,IAAI,CAAC,mBAAmB,GAAG,EAAE;QAC7B,IAAI,CAAC,gBAAgB,GAAG,MAAM,SAAS,KAAK;QAC5C,IAAI,CAAC,cAAc,GAAG,cAAc,IAAI;QACxC,4CAA4C;QAC5C,aAAa;QACb,IAAI,CAAC,cAAc,GAAG,OAAO,cAAc;IAC/C;IACA,OAAO,MAAM,GAAG;IAChB,OAAO,oBAAoB,GAAG,kBAAkB,oBAAoB;IACpE;;;;;;KAMC,GACD,OAAO,qBAAqB,GAAG,kBAAkB,qBAAqB;IACtE;;;KAGC,GACD,OAAO,wBAAwB,GAAG,kBAAkB,wBAAwB;IAC5E;;;;;;;KAOC,GACD,OAAO,0BAA0B,GAC7B,kBAAkB,0BAA0B;IAChD,OAAO,SAAS,GAAG;QACf,8CAA8C;QAC9C,UAAU;QACV,IAAI;QACJ,KAAK;QACL,MAAM;QACN,SAAS;QACT,gBAAgB;QAChB,UAAU;QACV,QAAQ;QACR,MAAM;QACN,qBAAqB;QACrB,UAAU;QACV,kBAAkB;QAClB,gBAAgB;QAChB,oBAAoB;QACpB,YAAW,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO;YACpC,OAAO,IAAI,CAAC,cAAc,CAAC,WAAW,CAAC,QAAQ,MAAM,QAAQ;QACjE;QACA;;SAEC,GACD,mBAAkB,GAAG,EAAE,aAAa;YAChC,IAAI,OAAO,eAAe;gBACtB,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,CAAC,OAAO,CAAC,eAAe;gBACxB,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,CAAC,cAAc,GAAG,MAChB,CAAA,GAAA,+NAAA,CAAA,4BAAyB,AAAD,EAAE,OAC1B;QACV;QACA;;;SAGC,GACD,aAAY,IAAI;YACZ,IAAI,QAAQ,OAAO,SAAS,UAAU;gBAClC,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,QAAQ,CAAC,KAAK,IAAI,EAAE;gBACpB,MAAM,IAAI,MAAM;YACpB;YACA,OAAO,QAAQ,CAAC;YAChB,IAAI,CAAC,QAAQ,GAAG,oBAAoB,MAAM,CAAC,CAAC,OAAO;gBAC/C,IAAI,OAAO,IAAI,CAAC,KAAK,IAAI,UAAU;oBAC/B,QAAQ,SAAS,CAAC;oBAClB,KAAK,CAAC,KAAK,GAAG,IAAI,CAAC,KAAK;gBAC5B;gBACA,OAAO;YACX,GAAG,CAAC;QACR;QACA;;;SAGC,GACD,cAAa,GAAG,EAAE,KAAK;YACnB,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG;QACrB;QACA;;;;;;SAMC,GACD,aAAY,GAAG;YACX,OAAO,IAAI,CAAC,IAAI,CAAC,IAAI;QACzB;QACA,aAAY,QAAQ;YAChB,IAAI,CAAC,SAAS,GAAG;QACrB;QACA;YACI,OAAO,IAAI,CAAC,SAAS;QACzB;QACA;;;;;;SAMC,GACD,aAAa,CAAC;YACV,OAAQ;gBACJ,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;gBACX,KAAK;oBACD,OAAO;YACf;YACA,OAAO,MAAM,CAAC,EAAE;QACpB;QACA;YACI,OAAO,IAAI,CAAC,WAAW,CAAC;QAC5B;QACA;;;SAGC,GACD,oBAAmB,IAAI,EAAE,CAAC,EAAE,UAAU;YAClC,MAAM,MAAM,CAAA,GAAA,+NAAA,CAAA,kBAAe,AAAD,EAAE,MAAM,GAAG;YACrC,IAAI,CAAC,YAAY,CAAC,MAAM;QAC5B;QACA;YACI,OAAO;QACX;QACA;YACI,OAAO;QACX;QACA;;;;;;;;;SASC,GACD,oBAAmB,EAAE;YACjB,OAAO,IAAI,CAAC,wBAAwB,CAAC,OAAO,UAAU,EAAE;QAC5D;QACA;;;;;;;;;SASC,GACD,0BAAyB,IAAI,EAAE,EAAE;YAC7B,IAAI,CAAC,kBAAkB,CAAC,QAAQ,GAAG,IAAI,CAAC,CAAC;gBACrC,IAAI;gBACJ,MAAM,YAAY,CAAC;gBACnB,IAAK,MAAM,SAAS,KAAM;oBACtB,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,QAAQ;wBACpD;oBACJ;oBACA,SAAS,CAAC,MAAM,GAAG,mBAAmB,CAAC,KAAK,IAAI,CAAC,MAAM,MAAM,QAAQ,OAAO,KAAK,IAAI,KAAK;gBAC9F;gBACA,yEAAyE;gBACzE,UAAU,KAAK,GAAG,mBAAmB,SAAS;gBAC9C,MAAM,SAAS,IAAI,CAAC,WAAW,CAAC;gBAChC,IAAI,QAAQ;oBACR,UAAU,OAAO,GAAG,mBAAmB,OAAO,aAAa;gBAC/D;gBACA,IAAI,IAAI,CAAC,QAAQ,EAAE;oBACf,UAAU,WAAW,GAAG,IAAI,CAAC,QAAQ;gBACzC;gBACA,GAAG,KAAK,SAAS,CAAC;YACtB;QACJ;QACA;;;;;;SAMC,GACD;YACI,IAAI,CAAC,IAAI,CAAC,QAAQ,EAAE;gBAChB,OAAO;YACX;YACA,IAAI,YAAY,IAAI,CAAC,QAAQ,CAAC,IAAI;YAClC,IAAI,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;gBACvB,aAAa,CAAC,CAAC,EAAE,IAAI,CAAC,QAAQ,CAAC,OAAO,EAAE;YAC5C;YACA,IAAI,IAAI,CAAC,QAAQ,CAAC,GAAG,EAAE;gBACnB,aAAa,CAAC,EAAE,EAAE,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC;YAC1C;YACA,OAAO;QACX;QACA;YACI,OAAO,IAAI,CAAC,gBAAgB;QAChC;QACA;;;SAGC,GACD;YACI,IAAK,MAAM,QAAQ,oOAAW;gBAC1B,IAAI,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,qOAAW,OAAO;oBACxD;gBACJ;gBACA,aAAa;gBACb,IAAI,CAAC,CAAA,GAAA,+NAAA,CAAA,oBAAiB,AAAD,EAAE,MAAM,GAAG,IAAI,mOAAS,CAAC,KAAK,CAAC,IAAI;YAC5D;QACJ;QACA;;;SAGC,GACD,qBAAoB,MAAM;YACtB,gEAAgE;YAChE,IAAI,CAAC,QAAQ;gBACT,OAAO,CAAC;YACZ;YACA,sCAAsC;YACtC,MAAM,WAAW,OAAO,WAAW;YACnC,MAAM,WAAW,WAAW,OAAO,WAAW,CAAC,MAAM,OAAO,CAAC;YAC7D,IAAI,CAAC,YAAY,CAAC,UAAU;gBACxB,MAAM,IAAI,MAAM;YACpB;YACA,6GAA6G;YAC7G,IAAI,UAAU;gBACV,OAAO;oBACH,YAAY;gBAChB;YACJ;YACA,4GAA4G;YAC5G,MAAM,SAAS,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,CAAC,QAAU,CAAC,0BAA0B,QAAQ,CAAC;YACzF,IAAI,OAAO,MAAM,GAAG,GAAG;gBACnB,MAAM,IAAI,MAAM,CAAC,8CAA8C,EAAE,0BAA0B,IAAI,CAAC,OAAO;YAC3G;YACA,OAAO;QACX;QACA,gBAAe,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,cAAc,EAAE,UAAU;YACzE,uDAAuD;YACvD,OAAO,IAAI,CAAC,QAAQ,CAAC,cAAc,CAAC,SAAS,QAAQ,QAAQ,WAAW,gBAAgB;QAC5F;IACJ;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7762, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/stripe%4018.3.0_%40types%2Bnode%4024.0.13/node_modules/stripe/esm/stripe.esm.node.js"], "sourcesContent": ["import { NodePlatformFunctions } from './platform/NodePlatformFunctions.js';\nimport { createStripe } from './stripe.core.js';\nexport const Stripe = createStripe(new NodePlatformFunctions());\nexport default Stripe;\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AACO,MAAM,SAAS,CAAA,GAAA,wOAAA,CAAA,eAAY,AAAD,EAAE,IAAI,2PAAA,CAAA,wBAAqB;uCAC7C", "ignoreList": [0], "debugId": null}}]}