module.exports = {

"[project]/src/lib/email.ts [app-route] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/0d880_@react-email_render_dist_node_index_mjs_e1784e6d._.js",
  "server/chunks/_21b36791._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/lib/email.ts [app-route] (ecmascript)");
    });
});
}}),

};