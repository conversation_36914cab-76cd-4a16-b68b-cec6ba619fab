{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/admin/orders/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui\";\nimport { motion } from \"framer-motion\";\nimport {\n  ChevronLeft,\n  ChevronRight,\n  Eye,\n  Filter,\n  Package,\n  Search,\n} from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\n\ninterface Order {\n  id: string;\n  customerId: string;\n  customer: {\n    firstName: string;\n    lastName: string;\n    email: string;\n  };\n  totalAmount: number;\n  status: string;\n  createdAt: string;\n  items: Array<{\n    id: string;\n    quantity: number;\n    price: number;\n    product: {\n      name: string;\n    };\n    variant?: {\n      flavor: string;\n    };\n  }>;\n  shippingFirstName: string;\n  shippingLastName: string;\n  shippingCity: string;\n}\n\nconst fadeIn = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },\n};\n\nexport default function AdminOrdersPage() {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [currentPage, setCurrentPage] = useState(1);\n  const [totalPages, setTotalPages] = useState(1);\n  const [totalOrders, setTotalOrders] = useState(0);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [statusFilter, setStatusFilter] = useState(\"all\");\n  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);\n  const [isModalOpen, setIsModalOpen] = useState(false);\n\n  const ordersPerPage = 10;\n\n  const loadOrders = async (page = 1, search = \"\", status = \"all\") => {\n    try {\n      setLoading(true);\n      const params = new URLSearchParams({\n        page: page.toString(),\n        limit: ordersPerPage.toString(),\n        ...(search && { search }),\n        ...(status !== \"all\" && { status }),\n      });\n\n      const response = await fetch(`/api/admin/orders?${params}`);\n      const data = await response.json();\n\n      if (data.success) {\n        setOrders(data.data.orders);\n        setTotalPages(data.data.totalPages);\n        setTotalOrders(data.data.totalOrders);\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement des commandes:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadOrders(currentPage, searchTerm, statusFilter);\n  }, [currentPage, searchTerm, statusFilter]);\n\n  const handleSearch = (e: React.FormEvent) => {\n    e.preventDefault();\n    setCurrentPage(1);\n    loadOrders(1, searchTerm, statusFilter);\n  };\n\n  const handlePageChange = (page: number) => {\n    setCurrentPage(page);\n    window.scrollTo({ top: 0, behavior: \"smooth\" });\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status.toLowerCase()) {\n      case \"pending\":\n        return \"bg-yellow-100 text-yellow-800\";\n      case \"confirmed\":\n        return \"bg-blue-100 text-blue-800\";\n      case \"shipped\":\n        return \"bg-purple-100 text-purple-800\";\n      case \"delivered\":\n        return \"bg-green-100 text-green-800\";\n      case \"cancelled\":\n        return \"bg-red-100 text-red-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  };\n\n  const getStatusLabel = (status: string) => {\n    switch (status.toLowerCase()) {\n      case \"pending\":\n        return \"En attente\";\n      case \"confirmed\":\n        return \"Confirmée\";\n      case \"shipped\":\n        return \"Expédiée\";\n      case \"delivered\":\n        return \"Livrée\";\n      case \"cancelled\":\n        return \"Annulée\";\n      default:\n        return status;\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\"\n      >\n        <div>\n          <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900\">\n            Gestion des commandes\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {totalOrders} commande{totalOrders > 1 ? \"s\" : \"\"} au total\n          </p>\n        </div>\n\n        <Button\n          onClick={() => loadOrders(currentPage, searchTerm, statusFilter)}\n          variant=\"outline\"\n          className=\"flex items-center space-x-2\"\n        >\n          <Package className=\"w-4 h-4\" />\n          <span>Actualiser</span>\n        </Button>\n      </motion.div>\n\n      {/* Filtres et recherche */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\"\n      >\n        <form\n          onSubmit={handleSearch}\n          className=\"flex flex-col sm:flex-row gap-4\"\n        >\n          <div className=\"flex-1\">\n            <div className=\"relative\">\n              <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n              <input\n                type=\"text\"\n                placeholder=\"Rechercher par nom, email, ID commande...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n              />\n            </div>\n          </div>\n\n          <div className=\"flex gap-2\">\n            <select\n              value={statusFilter}\n              onChange={(e) => setStatusFilter(e.target.value)}\n              className=\"px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n            >\n              <option value=\"all\">Tous les statuts</option>\n              <option value=\"pending\">En attente</option>\n              <option value=\"confirmed\">Confirmées</option>\n              <option value=\"shipped\">Expédiées</option>\n              <option value=\"delivered\">Livrées</option>\n              <option value=\"cancelled\">Annulées</option>\n            </select>\n\n            <Button type=\"submit\" variant=\"primary\">\n              <Filter className=\"w-4 h-4\" />\n            </Button>\n          </div>\n        </form>\n      </motion.div>\n\n      {/* Liste des commandes */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"bg-white rounded-xl shadow-sm border border-gray-200\"\n      >\n        {loading ? (\n          <div className=\"p-8 text-center\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">Chargement des commandes...</p>\n          </div>\n        ) : orders.length === 0 ? (\n          <div className=\"p-8 text-center\">\n            <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600\">Aucune commande trouvée</p>\n          </div>\n        ) : (\n          <>\n            {/* Table header */}\n            <div className=\"px-6 py-4 border-b border-gray-200 bg-gray-50 rounded-t-xl\">\n              <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 text-sm font-medium text-gray-700\">\n                <div>Commande</div>\n                <div>Client</div>\n                <div>Montant</div>\n                <div>Statut</div>\n                <div>Date</div>\n                <div>Actions</div>\n              </div>\n            </div>\n\n            {/* Table body */}\n            <div className=\"divide-y divide-gray-200\">\n              {orders.map((order) => (\n                <div\n                  key={order.id}\n                  className=\"px-6 py-4 hover:bg-gray-50 transition-colors\"\n                >\n                  <div className=\"grid grid-cols-1 md:grid-cols-6 gap-4 items-center\">\n                    <div>\n                      <p className=\"font-medium text-gray-900\">\n                        #{order.id.slice(-8)}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {order.items.length} article\n                        {order.items.length > 1 ? \"s\" : \"\"}\n                      </p>\n                    </div>\n\n                    <div>\n                      <p className=\"font-medium text-gray-900\">\n                        {order.customer.firstName} {order.customer.lastName}\n                      </p>\n                      <p className=\"text-sm text-gray-500\">\n                        {order.customer.email}\n                      </p>\n                    </div>\n\n                    <div>\n                      <p className=\"font-medium text-gray-900\">\n                        {Number(order.totalAmount).toFixed(2)}€\n                      </p>\n                    </div>\n\n                    <div>\n                      <span\n                        className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(\n                          order.status\n                        )}`}\n                      >\n                        {getStatusLabel(order.status)}\n                      </span>\n                    </div>\n\n                    <div>\n                      <p className=\"text-sm text-gray-900\">\n                        {new Date(order.createdAt).toLocaleDateString(\"fr-FR\")}\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {new Date(order.createdAt).toLocaleTimeString(\"fr-FR\", {\n                          hour: \"2-digit\",\n                          minute: \"2-digit\",\n                        })}\n                      </p>\n                    </div>\n\n                    <div>\n                      <Button\n                        onClick={() => {\n                          setSelectedOrder(order);\n                          setIsModalOpen(true);\n                        }}\n                        variant=\"outline\"\n                        size=\"sm\"\n                        className=\"flex items-center space-x-1\"\n                      >\n                        <Eye className=\"w-4 h-4\" />\n                        <span>Voir</span>\n                      </Button>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </>\n        )}\n      </motion.div>\n\n      {/* Pagination */}\n      {totalPages > 1 && (\n        <motion.div\n          initial={fadeIn.initial}\n          animate={fadeIn.animate}\n          className=\"flex justify-center items-center space-x-2\"\n        >\n          <Button\n            onClick={() => handlePageChange(currentPage - 1)}\n            disabled={currentPage === 1}\n            variant=\"outline\"\n            size=\"sm\"\n          >\n            <ChevronLeft className=\"w-4 h-4\" />\n          </Button>\n\n          {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => (\n            <Button\n              key={page}\n              onClick={() => handlePageChange(page)}\n              variant={currentPage === page ? \"primary\" : \"outline\"}\n              size=\"sm\"\n              className=\"min-w-[40px]\"\n            >\n              {page}\n            </Button>\n          ))}\n\n          <Button\n            onClick={() => handlePageChange(currentPage + 1)}\n            disabled={currentPage === totalPages}\n            variant=\"outline\"\n            size=\"sm\"\n          >\n            <ChevronRight className=\"w-4 h-4\" />\n          </Button>\n        </motion.div>\n      )}\n\n      {/* Modal de détails de commande */}\n      <OrderDetailsModal\n        order={selectedOrder}\n        isOpen={isModalOpen}\n        onClose={() => {\n          setIsModalOpen(false);\n          setSelectedOrder(null);\n        }}\n      />\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AAZA;;;;;;AAyCA,MAAM,SAAS;IACb,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC7D;AAEe,SAAS;IACtB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAgB;IACjE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,gBAAgB;IAEtB,MAAM,aAAa,OAAO,OAAO,CAAC,EAAE,SAAS,EAAE,EAAE,SAAS,KAAK;QAC7D,IAAI;YACF,WAAW;YACX,MAAM,SAAS,IAAI,gBAAgB;gBACjC,MAAM,KAAK,QAAQ;gBACnB,OAAO,cAAc,QAAQ;gBAC7B,GAAI,UAAU;oBAAE;gBAAO,CAAC;gBACxB,GAAI,WAAW,SAAS;oBAAE;gBAAO,CAAC;YACpC;YAEA,MAAM,WAAW,MAAM,MAAM,CAAC,kBAAkB,EAAE,QAAQ;YAC1D,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,UAAU,KAAK,IAAI,CAAC,MAAM;gBAC1B,cAAc,KAAK,IAAI,CAAC,UAAU;gBAClC,eAAe,KAAK,IAAI,CAAC,WAAW;YACtC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4CAA4C;QAC5D,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW,aAAa,YAAY;IACtC,GAAG;QAAC;QAAa;QAAY;KAAa;IAE1C,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,eAAe;QACf,WAAW,GAAG,YAAY;IAC5B;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;QACf,OAAO,QAAQ,CAAC;YAAE,KAAK;YAAG,UAAU;QAAS;IAC/C;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ,OAAO,WAAW;YACxB,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAEV,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,6VAAC;gCAAE,WAAU;;oCACV;oCAAY;oCAAU,cAAc,IAAI,MAAM;oCAAG;;;;;;;;;;;;;kCAItD,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,WAAW,aAAa,YAAY;wBACnD,SAAQ;wBACR,WAAU;;0CAEV,6VAAC,4RAAA,CAAA,UAAO;gCAAC,WAAU;;;;;;0CACnB,6VAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;0BAEV,cAAA,6VAAC;oBACC,UAAU;oBACV,WAAU;;sCAEV,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;kDAClB,6VAAC;wCACC,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wCAC7C,WAAU;;;;;;;;;;;;;;;;;sCAKhB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;oCAC/C,WAAU;;sDAEV,6VAAC;4CAAO,OAAM;sDAAM;;;;;;sDACpB,6VAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6VAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6VAAC;4CAAO,OAAM;sDAAU;;;;;;sDACxB,6VAAC;4CAAO,OAAM;sDAAY;;;;;;sDAC1B,6VAAC;4CAAO,OAAM;sDAAY;;;;;;;;;;;;8CAG5B,6VAAC,kIAAA,CAAA,SAAM;oCAAC,MAAK;oCAAS,SAAQ;8CAC5B,cAAA,6VAAC,0RAAA,CAAA,SAAM;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO1B,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;0BAET,wBACC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;;;;;sCACf,6VAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;2BAElC,OAAO,MAAM,KAAK,kBACpB,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6VAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;yCAG/B;;sCAEE,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;kDAAI;;;;;;kDACL,6VAAC;kDAAI;;;;;;kDACL,6VAAC;kDAAI;;;;;;kDACL,6VAAC;kDAAI;;;;;;kDACL,6VAAC;kDAAI;;;;;;kDACL,6VAAC;kDAAI;;;;;;;;;;;;;;;;;sCAKT,6VAAC;4BAAI,WAAU;sCACZ,OAAO,GAAG,CAAC,CAAC,sBACX,6VAAC;oCAEC,WAAU;8CAEV,cAAA,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;;kEACC,6VAAC;wDAAE,WAAU;;4DAA4B;4DACrC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;kEAEpB,6VAAC;wDAAE,WAAU;;4DACV,MAAM,KAAK,CAAC,MAAM;4DAAC;4DACnB,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI,MAAM;;;;;;;;;;;;;0DAIpC,6VAAC;;kEACC,6VAAC;wDAAE,WAAU;;4DACV,MAAM,QAAQ,CAAC,SAAS;4DAAC;4DAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;kEAErD,6VAAC;wDAAE,WAAU;kEACV,MAAM,QAAQ,CAAC,KAAK;;;;;;;;;;;;0DAIzB,6VAAC;0DACC,cAAA,6VAAC;oDAAE,WAAU;;wDACV,OAAO,MAAM,WAAW,EAAE,OAAO,CAAC;wDAAG;;;;;;;;;;;;0DAI1C,6VAAC;0DACC,cAAA,6VAAC;oDACC,WAAW,CAAC,uDAAuD,EAAE,eACnE,MAAM,MAAM,GACX;8DAEF,eAAe,MAAM,MAAM;;;;;;;;;;;0DAIhC,6VAAC;;kEACC,6VAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;kEAEhD,6VAAC;wDAAE,WAAU;kEACV,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC,SAAS;4DACrD,MAAM;4DACN,QAAQ;wDACV;;;;;;;;;;;;0DAIJ,6VAAC;0DACC,cAAA,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAS;wDACP,iBAAiB;wDACjB,eAAe;oDACjB;oDACA,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,6VAAC,oRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;sEACf,6VAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;mCA9DP,MAAM,EAAE;;;;;;;;;;;;;;;;;YA0ExB,aAAa,mBACZ,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAEV,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,SAAQ;wBACR,MAAK;kCAEL,cAAA,6VAAC,wSAAA,CAAA,cAAW;4BAAC,WAAU;;;;;;;;;;;oBAGxB,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAW,GAAG,CAAC,GAAG,IAAM,IAAI,GAAG,GAAG,CAAC,CAAC,qBACxD,6VAAC,kIAAA,CAAA,SAAM;4BAEL,SAAS,IAAM,iBAAiB;4BAChC,SAAS,gBAAgB,OAAO,YAAY;4BAC5C,MAAK;4BACL,WAAU;sCAET;2BANI;;;;;kCAUT,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS,IAAM,iBAAiB,cAAc;wBAC9C,UAAU,gBAAgB;wBAC1B,SAAQ;wBACR,MAAK;kCAEL,cAAA,6VAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAM9B,6VAAC;gBACC,OAAO;gBACP,QAAQ;gBACR,SAAS;oBACP,eAAe;oBACf,iBAAiB;gBACnB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 707, "column": 0}, "map": {"version": 3, "file": "chevron-left.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/chevron-left.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm15 18-6-6 6-6', key: '1wnfg3' }]];\n\n/**\n * @component @name ChevronLeft\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMTUgMTgtNi02IDYtNiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/chevron-left\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronLeft = createLucideIcon('chevron-left', __iconNode);\n\nexport default ChevronLeft;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,gBAAkB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa/E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,WAAA,CAAc,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAgB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 746, "column": 0}, "map": {"version": 3, "file": "chevron-right.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/chevron-right.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'm9 18 6-6-6-6', key: 'mthhwq' }]];\n\n/**\n * @component @name ChevronRight\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtOSAxOCA2LTYtNi02IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/chevron-right\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst ChevronRight = createLucideIcon('chevron-right', __iconNode);\n\nexport default ChevronRight;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAuB;IAAC;QAAC,MAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,eAAiB,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAS;QAAA,CAAC;KAAC;CAAA;AAa9E,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,YAAA,CAAe,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAA,AAAjB,CAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAiB,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 785, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 833, "column": 0}, "map": {"version": 3, "file": "funnel.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/funnel.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z',\n      key: 'sc7q7i',\n    },\n  ],\n];\n\n/**\n * @component @name Funnel\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTAgMjBhMSAxIDAgMCAwIC41NTMuODk1bDIgMUExIDEgMCAwIDAgMTQgMjF2LTdhMiAyIDAgMCAxIC41MTctMS4zNDFMMjEuNzQgNC42N0ExIDEgMCAwIDAgMjEgM0gzYTEgMSAwIDAgMC0uNzQyIDEuNjdsNy4yMjUgNy45ODlBMiAyIDAgMCAxIDEwIDE0eiIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/funnel\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Funnel = createLucideIcon('funnel', __iconNode);\n\nexport default Funnel;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 872, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}