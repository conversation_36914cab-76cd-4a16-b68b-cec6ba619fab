{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>ontent,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CardTitle,\n} from \"@/components/ui\";\nimport { motion } from \"framer-motion\";\nimport {\n  DollarSign,\n  Eye,\n  Package,\n  ShoppingCart,\n  TrendingUp,\n  Users,\n} from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect, useState } from \"react\";\n\nconst fadeIn = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },\n};\n\nexport default function AdminDashboard() {\n  const [statsData, setStatsData] = useState({\n    products: 0,\n    orders: 0,\n    customers: 0,\n    revenue: 0,\n  });\n  const [recentOrders, setRecentOrders] = useState([]);\n  const [loading, setLoading] = useState(true);\n\n  const loadStats = async () => {\n    try {\n      setLoading(true);\n\n      const [productsRes, ordersRes] = await Promise.all([\n        fetch(\"/api/products\"),\n        fetch(\"/api/admin/orders?limit=5\"),\n      ]);\n\n      const [productsData, ordersData] = await Promise.all([\n        productsRes.json(),\n        ordersRes.json(),\n      ]);\n\n      const products = productsData.success ? productsData.data.length : 0;\n      const orders = ordersData.success ? ordersData.data.totalOrders : 0;\n      const revenue = ordersData.success\n        ? ordersData.data.orders.reduce(\n            (sum: number, order: any) => sum + Number(order.totalAmount),\n            0\n          )\n        : 0;\n\n      setStatsData({\n        products,\n        orders,\n        customers: 0,\n        revenue,\n      });\n\n      if (ordersData.success) {\n        setRecentOrders(ordersData.data.orders.slice(0, 5));\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement des statistiques:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadStats();\n  }, []);\n\n  return (\n    <div className=\"space-y-8\">\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\"\n      >\n        <div>\n          <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900\">\n            Vue d'ensemble\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            Tableau de bord administrateur Deltagum\n          </p>\n        </div>\n        <Button\n          onClick={loadStats}\n          variant=\"outline\"\n          className=\"flex items-center space-x-2\"\n        >\n          <TrendingUp className=\"w-4 h-4\" />\n          <span>Actualiser</span>\n        </Button>\n      </motion.div>\n\n      {/* Statistiques principales */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\"\n      >\n        <Link href=\"/admin/products\">\n          <Card className=\"hover:shadow-lg transition-all duration-200 cursor-pointer group\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 mb-1\">\n                    Produits\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {loading ? \"...\" : statsData.products}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-blue-500 group-hover:scale-110 transition-transform\">\n                  <Package className=\"w-6 h-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Link>\n\n        <Link href=\"/admin/orders\">\n          <Card className=\"hover:shadow-lg transition-all duration-200 cursor-pointer group\">\n            <CardContent className=\"p-6\">\n              <div className=\"flex items-center justify-between\">\n                <div>\n                  <p className=\"text-sm font-medium text-gray-600 mb-1\">\n                    Commandes\n                  </p>\n                  <p className=\"text-2xl font-bold text-gray-900\">\n                    {loading ? \"...\" : statsData.orders}\n                  </p>\n                </div>\n                <div className=\"p-3 rounded-full bg-green-500 group-hover:scale-110 transition-transform\">\n                  <ShoppingCart className=\"w-6 h-6 text-white\" />\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n        </Link>\n\n        <Card className=\"opacity-75\">\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600 mb-1\">\n                  Clients\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {loading ? \"...\" : statsData.customers}\n                </p>\n                <p className=\"text-xs text-gray-500\">Bientôt disponible</p>\n              </div>\n              <div className=\"p-3 rounded-full bg-purple-500\">\n                <Users className=\"w-6 h-6 text-white\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n\n        <Card>\n          <CardContent className=\"p-6\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-600 mb-1\">\n                  Chiffre d'affaires\n                </p>\n                <p className=\"text-2xl font-bold text-gray-900\">\n                  {loading ? \"...\" : `${statsData.revenue.toFixed(2)}€`}\n                </p>\n              </div>\n              <div className=\"p-3 rounded-full bg-orange-500\">\n                <DollarSign className=\"w-6 h-6 text-white\" />\n              </div>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n\n      {/* Commandes récentes et actions rapides */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\"\n      >\n        {/* Commandes récentes */}\n        <Card>\n          <CardHeader className=\"flex flex-row items-center justify-between\">\n            <CardTitle className=\"text-lg font-semibold\">\n              Commandes récentes\n            </CardTitle>\n            <Link href=\"/admin/orders\">\n              <Button\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"flex items-center space-x-1\"\n              >\n                <Eye className=\"w-4 h-4\" />\n                <span>Voir tout</span>\n              </Button>\n            </Link>\n          </CardHeader>\n          <CardContent>\n            {loading ? (\n              <div className=\"text-center py-4\">\n                <div className=\"animate-spin rounded-full h-6 w-6 border-b-2 border-pink-500 mx-auto\"></div>\n              </div>\n            ) : recentOrders.length === 0 ? (\n              <p className=\"text-gray-500 text-center py-4\">\n                Aucune commande récente\n              </p>\n            ) : (\n              <div className=\"space-y-3\">\n                {recentOrders.map((order: any) => (\n                  <div\n                    key={order.id}\n                    className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n                  >\n                    <div>\n                      <p className=\"font-medium text-gray-900\">\n                        #{order.id.slice(-8)}\n                      </p>\n                      <p className=\"text-sm text-gray-600\">\n                        {order.customer.firstName} {order.customer.lastName}\n                      </p>\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"font-medium text-gray-900\">\n                        {Number(order.totalAmount).toFixed(2)}€\n                      </p>\n                      <p className=\"text-xs text-gray-500\">\n                        {new Date(order.createdAt).toLocaleDateString(\"fr-FR\")}\n                      </p>\n                    </div>\n                  </div>\n                ))}\n              </div>\n            )}\n          </CardContent>\n        </Card>\n\n        {/* Actions rapides */}\n        <Card>\n          <CardHeader>\n            <CardTitle className=\"text-lg font-semibold\">\n              Actions rapides\n            </CardTitle>\n          </CardHeader>\n          <CardContent>\n            <div className=\"space-y-3\">\n              <Link href=\"/admin/products\" className=\"mt-2\">\n                <Button variant=\"outline\" className=\"w-full justify-start\">\n                  <Package className=\"w-4 h-4 mr-2\" />\n                  Gérer les produits\n                </Button>\n              </Link>\n              <Link href=\"/admin/orders\" className=\"mt-2\">\n                <Button variant=\"outline\" className=\"w-full justify-start\">\n                  <ShoppingCart className=\"w-4 h-4 mr-2\" />\n                  Voir les commandes\n                </Button>\n              </Link>\n              <Link href=\"/\" className=\"mt-2\">\n                <Button variant=\"outline\" className=\"w-full justify-start\">\n                  <Eye className=\"w-4 h-4 mr-2\" />\n                  Voir le site\n                </Button>\n              </Link>\n            </div>\n          </CardContent>\n        </Card>\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAOA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAnBA;;;;;;;AAqBA,MAAM,SAAS;IACb,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC7D;AAEe,SAAS;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;QACzC,UAAU;QACV,QAAQ;QACR,WAAW;QACX,SAAS;IACX;IACA,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE,EAAE;IACnD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YAEX,MAAM,CAAC,aAAa,UAAU,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACjD,MAAM;gBACN,MAAM;aACP;YAED,MAAM,CAAC,cAAc,WAAW,GAAG,MAAM,QAAQ,GAAG,CAAC;gBACnD,YAAY,IAAI;gBAChB,UAAU,IAAI;aACf;YAED,MAAM,WAAW,aAAa,OAAO,GAAG,aAAa,IAAI,CAAC,MAAM,GAAG;YACnE,MAAM,SAAS,WAAW,OAAO,GAAG,WAAW,IAAI,CAAC,WAAW,GAAG;YAClE,MAAM,UAAU,WAAW,OAAO,GAC9B,WAAW,IAAI,CAAC,MAAM,CAAC,MAAM,CAC3B,CAAC,KAAa,QAAe,MAAM,OAAO,MAAM,WAAW,GAC3D,KAEF;YAEJ,aAAa;gBACX;gBACA;gBACA,WAAW;gBACX;YACF;YAEA,IAAI,WAAW,OAAO,EAAE;gBACtB,gBAAgB,WAAW,IAAI,CAAC,MAAM,CAAC,KAAK,CAAC,GAAG;YAClD;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,+CAA+C;QAC/D,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAEV,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,6VAAC;gCAAE,WAAU;0CAAqB;;;;;;;;;;;;kCAIpC,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,SAAQ;wBACR,WAAU;;0CAEV,6VAAC,sSAAA,CAAA,aAAU;gCAAC,WAAU;;;;;;0CACtB,6VAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAKV,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAEV,6VAAC,2QAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;;8DACC,6VAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6VAAC;oDAAE,WAAU;8DACV,UAAU,QAAQ,UAAU,QAAQ;;;;;;;;;;;;sDAGzC,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC,4RAAA,CAAA,UAAO;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAO7B,6VAAC,2QAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;sCACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;;8DACC,6VAAC;oDAAE,WAAU;8DAAyC;;;;;;8DAGtD,6VAAC;oDAAE,WAAU;8DACV,UAAU,QAAQ,UAAU,MAAM;;;;;;;;;;;;sDAGvC,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC,0SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOlC,6VAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;kCACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;;0DACC,6VAAC;gDAAE,WAAU;0DAAyC;;;;;;0DAGtD,6VAAC;gDAAE,WAAU;0DACV,UAAU,QAAQ,UAAU,SAAS;;;;;;0DAExC,6VAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,wRAAA,CAAA,QAAK;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAMzB,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACrB,cAAA,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;;0DACC,6VAAC;gDAAE,WAAU;0DAAyC;;;;;;0DAGtD,6VAAC;gDAAE,WAAU;0DACV,UAAU,QAAQ,GAAG,UAAU,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC;;;;;;;;;;;;kDAGzD,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC,sSAAA,CAAA,aAAU;4CAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQhC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAGV,6VAAC,gIAAA,CAAA,OAAI;;0CACH,6VAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,6VAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAAwB;;;;;;kDAG7C,6VAAC,2QAAA,CAAA,UAAI;wCAAC,MAAK;kDACT,cAAA,6VAAC,kIAAA,CAAA,SAAM;4CACL,SAAQ;4CACR,MAAK;4CACL,WAAU;;8DAEV,6VAAC,oRAAA,CAAA,MAAG;oDAAC,WAAU;;;;;;8DACf,6VAAC;8DAAK;;;;;;;;;;;;;;;;;;;;;;;0CAIZ,6VAAC,gIAAA,CAAA,cAAW;0CACT,wBACC,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;;;;;;;;;;2CAEf,aAAa,MAAM,KAAK,kBAC1B,6VAAC;oCAAE,WAAU;8CAAiC;;;;;yDAI9C,6VAAC;oCAAI,WAAU;8CACZ,aAAa,GAAG,CAAC,CAAC,sBACjB,6VAAC;4CAEC,WAAU;;8DAEV,6VAAC;;sEACC,6VAAC;4DAAE,WAAU;;gEAA4B;gEACrC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;sEAEpB,6VAAC;4DAAE,WAAU;;gEACV,MAAM,QAAQ,CAAC,SAAS;gEAAC;gEAAE,MAAM,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;8DAGvD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAE,WAAU;;gEACV,OAAO,MAAM,WAAW,EAAE,OAAO,CAAC;gEAAG;;;;;;;sEAExC,6VAAC;4DAAE,WAAU;sEACV,IAAI,KAAK,MAAM,SAAS,EAAE,kBAAkB,CAAC;;;;;;;;;;;;;2CAhB7C,MAAM,EAAE;;;;;;;;;;;;;;;;;;;;;kCA2BzB,6VAAC,gIAAA,CAAA,OAAI;;0CACH,6VAAC,gIAAA,CAAA,aAAU;0CACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;oCAAC,WAAU;8CAAwB;;;;;;;;;;;0CAI/C,6VAAC,gIAAA,CAAA,cAAW;0CACV,cAAA,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAK;4CAAkB,WAAU;sDACrC,cAAA,6VAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,6VAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAIxC,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAK;4CAAgB,WAAU;sDACnC,cAAA,6VAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,6VAAC,0SAAA,CAAA,eAAY;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;sDAI7C,6VAAC,2QAAA,CAAA,UAAI;4CAAC,MAAK;4CAAI,WAAU;sDACvB,cAAA,6VAAC,kIAAA,CAAA,SAAM;gDAAC,SAAQ;gDAAU,WAAU;;kEAClC,6VAAC,oRAAA,CAAA,MAAG;wDAAC,WAAU;;;;;;oDAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUlD", "debugId": null}}, {"offset": {"line": 720, "column": 0}, "map": {"version": 3, "file": "eye.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/eye.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  [\n    'path',\n    {\n      d: 'M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0',\n      key: '1nclc0',\n    },\n  ],\n  ['circle', { cx: '12', cy: '12', r: '3', key: '1v7zrd' }],\n];\n\n/**\n * @component @name Eye\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMi4wNjIgMTIuMzQ4YTEgMSAwIDAgMSAwLS42OTYgMTAuNzUgMTAuNzUgMCAwIDEgMTkuODc2IDAgMSAxIDAgMCAxIDAgLjY5NiAxMC43NSAxMC43NSAwIDAgMS0xOS44NzYgMCIgLz4KICA8Y2lyY2xlIGN4PSIxMiIgY3k9IjEyIiByPSIzIiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/eye\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Eye = createLucideIcon('eye', __iconNode);\n\nexport default Eye;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KAET;IACA;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,CAAM,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,EAAO,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 768, "column": 0}, "map": {"version": 3, "file": "trending-up.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/trending-up.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M16 7h6v6', key: 'box55l' }],\n  ['path', { d: 'm22 7-8.5 8.5-5-5L2 17', key: '1t1m79' }],\n];\n\n/**\n * @component @name TrendingUp\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTYgN2g2djYiIC8+CiAgPHBhdGggZD0ibTIyIDctOC41IDguNS01LTVMMiAxNyIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/trending-up\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst TrendingUp = createLucideIcon('trending-up', __iconNode);\n\nexport default TrendingUp;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAa,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC1C;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAA0B,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CACzD;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,UAAA,CAAa,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAe,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}