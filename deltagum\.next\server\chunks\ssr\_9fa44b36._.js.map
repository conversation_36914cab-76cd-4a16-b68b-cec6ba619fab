{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/utils/format.ts"], "sourcesContent": ["/**\n * Utilitaires de formatage pour l'application Deltagum\n */\n\n/**\n * Formate un prix en string avec 2 décimales\n * @param price - Le prix à formater (peut être string, number, ou undefined)\n * @returns Le prix formaté avec 2 décimales\n */\nexport const formatPrice = (price: any): string => {\n  const numPrice = Number(price || 0);\n  return isNaN(numPrice) ? \"0.00\" : numPrice.toFixed(2);\n};\n\n/**\n * Formate un prix avec le symbole euro\n * @param price - Le prix à formater\n * @returns Le prix formaté avec le symbole €\n */\nexport const formatPriceWithCurrency = (price: any): string => {\n  return `${formatPrice(price)}€`;\n};\n\n/**\n * Formate une date en français\n * @param date - La date à formater\n * @returns La date formatée en français\n */\nexport const formatDate = (date: string | Date): string => {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric'\n  });\n};\n\n/**\n * Formate une date avec l'heure en français\n * @param date - La date à formater\n * @returns La date et l'heure formatées en français\n */\nexport const formatDateTime = (date: string | Date): string => {\n  const dateObj = typeof date === 'string' ? new Date(date) : date;\n  return dateObj.toLocaleDateString('fr-FR', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit'\n  });\n};\n\n/**\n * Sécurise l'accès à un tableau qui peut être undefined\n * @param array - Le tableau à sécuriser\n * @returns Un tableau vide si undefined, sinon le tableau original\n */\nexport const safeArray = <T>(array: T[] | undefined | null): T[] => {\n  return array || [];\n};\n\n/**\n * Sécurise l'accès à une propriété qui peut être undefined\n * @param value - La valeur à sécuriser\n * @param defaultValue - La valeur par défaut\n * @returns La valeur ou la valeur par défaut\n */\nexport const safeValue = <T>(value: T | undefined | null, defaultValue: T): T => {\n  return value ?? defaultValue;\n};\n"], "names": [], "mappings": "AAAA;;CAEC,GAED;;;;CAIC;;;;;;;;AACM,MAAM,cAAc,CAAC;IAC1B,MAAM,WAAW,OAAO,SAAS;IACjC,OAAO,MAAM,YAAY,SAAS,SAAS,OAAO,CAAC;AACrD;AAOO,MAAM,0BAA0B,CAAC;IACtC,OAAO,GAAG,YAAY,OAAO,CAAC,CAAC;AACjC;AAOO,MAAM,aAAa,CAAC;IACzB,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAOO,MAAM,iBAAiB,CAAC;IAC7B,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,OAAO,QAAQ,kBAAkB,CAAC,SAAS;QACzC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV;AACF;AAOO,MAAM,YAAY,CAAI;IAC3B,OAAO,SAAS,EAAE;AACpB;AAQO,MAAM,YAAY,CAAI,OAA6B;IACxD,OAAO,SAAS;AAClB", "debugId": null}}, {"offset": {"line": 58, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/admin/products/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui\";\nimport { formatPriceWithCurrency, safeArray } from \"@/lib/utils/format\";\nimport { motion } from \"framer-motion\";\nimport { Edit, Package, Plus, Search, Trash2 } from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\n\ninterface Product {\n  id: string;\n  name: string;\n  description: string;\n  basePrice: number;\n  isActive: boolean;\n  createdAt: string;\n  variants: Array<{\n    id: string;\n    flavor: string;\n    price: number;\n    stock: number;\n  }>;\n  images: string[];\n}\n\nconst fadeIn = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },\n};\n\nexport default function AdminProductsPage() {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [searchTerm, setSearchTerm] = useState(\"\");\n\n  const loadProducts = async () => {\n    try {\n      setLoading(true);\n\n      // Créer un AbortController pour gérer les annulations\n      const controller = new AbortController();\n\n      const response = await fetch(\"/api/products\", {\n        signal: controller.signal,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const data = await response.json();\n\n      if (data.success) {\n        // Gérer la structure de l'API: { products: [...], total: number }\n        let productsArray = [];\n        if (Array.isArray(data.data)) {\n          productsArray = data.data;\n        } else if (data.data && Array.isArray(data.data.products)) {\n          productsArray = data.data.products;\n        }\n        setProducts(productsArray);\n      } else {\n        console.warn(\"API returned success: false\", data);\n        setProducts([]);\n      }\n    } catch (error) {\n      // Ignorer les erreurs d'annulation\n      if (error instanceof Error && error.name === \"AbortError\") {\n        console.log(\"Requête annulée\");\n        return;\n      }\n\n      console.error(\"Erreur lors du chargement des produits:\", error);\n      setProducts([]); // Fallback vers un tableau vide\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    loadProducts();\n  }, []);\n\n  const filteredProducts = safeArray(products).filter(\n    (product) =>\n      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      product.description.toLowerCase().includes(searchTerm.toLowerCase())\n  );\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\"\n      >\n        <div>\n          <h1 className=\"text-2xl sm:text-3xl font-bold text-gray-900\">\n            Gestion des produits\n          </h1>\n          <p className=\"text-gray-600 mt-1\">\n            {safeArray(products).length} produit\n            {safeArray(products).length > 1 ? \"s\" : \"\"} au total\n          </p>\n        </div>\n\n        <div className=\"flex gap-2\">\n          <Button\n            onClick={loadProducts}\n            variant=\"outline\"\n            className=\"flex items-center space-x-2\"\n          >\n            <Package className=\"w-4 h-4\" />\n            <span>Actualiser</span>\n          </Button>\n\n          <Button variant=\"primary\" className=\"flex items-center space-x-2\">\n            <Plus className=\"w-4 h-4\" />\n            <span>Nouveau produit</span>\n          </Button>\n        </div>\n      </motion.div>\n\n      {/* Recherche */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"bg-white rounded-xl shadow-sm border border-gray-200 p-6\"\n      >\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4\" />\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher un produit...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n          />\n        </div>\n      </motion.div>\n\n      {/* Liste des produits */}\n      <motion.div\n        initial={fadeIn.initial}\n        animate={fadeIn.animate}\n        className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\"\n      >\n        {loading ? (\n          <div className=\"col-span-full text-center py-8\">\n            <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500 mx-auto\"></div>\n            <p className=\"mt-2 text-gray-600\">Chargement des produits...</p>\n          </div>\n        ) : filteredProducts.length === 0 ? (\n          <div className=\"col-span-full text-center py-8\">\n            <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-4\" />\n            <p className=\"text-gray-600\">Aucun produit trouvé</p>\n          </div>\n        ) : (\n          filteredProducts.map((product) => {\n            if (!product || !product.id || !product.name) return null;\n\n            return (\n              <div\n                key={product.id}\n                className=\"bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow\"\n              >\n                {/* Image du produit */}\n                <div className=\"h-48 bg-gradient-to-br from-pink-100 to-orange-100 flex items-center justify-center\">\n                  {safeArray(product.images).length > 0 ? (\n                    <img\n                      src={safeArray(product.images)[0]}\n                      alt={product.name}\n                      className=\"w-full h-full object-cover\"\n                    />\n                  ) : (\n                    <Package className=\"w-16 h-16 text-gray-400\" />\n                  )}\n                </div>\n\n                {/* Contenu */}\n                <div className=\"p-6\">\n                  <div className=\"flex justify-between items-start mb-2\">\n                    <h3 className=\"text-lg font-semibold text-gray-900 truncate\">\n                      {product.name}\n                    </h3>\n                    <span\n                      className={`px-2 py-1 text-xs font-medium rounded-full ${\n                        product.isActive\n                          ? \"bg-green-100 text-green-800\"\n                          : \"bg-red-100 text-red-800\"\n                      }`}\n                    >\n                      {product.isActive ? \"Actif\" : \"Inactif\"}\n                    </span>\n                  </div>\n\n                  <p className=\"text-gray-600 text-sm mb-4 line-clamp-2\">\n                    {product.description}\n                  </p>\n\n                  <div className=\"space-y-2 mb-4\">\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-600\">Prix de base:</span>\n                      <span className=\"font-medium\">\n                        {formatPriceWithCurrency(product.basePrice)}\n                      </span>\n                    </div>\n                    <div className=\"flex justify-between text-sm\">\n                      <span className=\"text-gray-600\">Variantes:</span>\n                      <span className=\"font-medium\">\n                        {safeArray(product.variants).length}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Variantes */}\n                  {safeArray(product.variants).length > 0 && (\n                    <div className=\"mb-4\">\n                      <p className=\"text-xs text-gray-500 mb-2\">\n                        Saveurs disponibles:\n                      </p>\n                      <div className=\"flex flex-wrap gap-1\">\n                        {safeArray(product.variants)\n                          .slice(0, 3)\n                          .map((variant) => (\n                            <span\n                              key={variant.id}\n                              className=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full\"\n                            >\n                              {variant.flavor}\n                            </span>\n                          ))}\n                        {safeArray(product.variants).length > 3 && (\n                          <span className=\"px-2 py-1 bg-gray-100 text-gray-700 text-xs rounded-full\">\n                            +{safeArray(product.variants).length - 3}\n                          </span>\n                        )}\n                      </div>\n                    </div>\n                  )}\n\n                  {/* Actions */}\n                  <div className=\"flex gap-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"flex-1 flex items-center justify-center space-x-1\"\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                      <span>Modifier</span>\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      className=\"text-red-600 hover:text-red-700 hover:bg-red-50\"\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </div>\n              </div>\n            );\n          })\n        )}\n      </motion.div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;;AAwBA,MAAM,SAAS;IACb,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;QAAG,YAAY;YAAE,UAAU;QAAI;IAAE;AAC7D;AAEe,SAAS;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,eAAe;QACnB,IAAI;YACF,WAAW;YAEX,sDAAsD;YACtD,MAAM,aAAa,IAAI;YAEvB,MAAM,WAAW,MAAM,MAAM,iBAAiB;gBAC5C,QAAQ,WAAW,MAAM;YAC3B;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;YAC1D;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,kEAAkE;gBAClE,IAAI,gBAAgB,EAAE;gBACtB,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,GAAG;oBAC5B,gBAAgB,KAAK,IAAI;gBAC3B,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,GAAG;oBACzD,gBAAgB,KAAK,IAAI,CAAC,QAAQ;gBACpC;gBACA,YAAY;YACd,OAAO;gBACL,QAAQ,IAAI,CAAC,+BAA+B;gBAC5C,YAAY,EAAE;YAChB;QACF,EAAE,OAAO,OAAO;YACd,mCAAmC;YACnC,IAAI,iBAAiB,SAAS,MAAM,IAAI,KAAK,cAAc;gBACzD,QAAQ,GAAG,CAAC;gBACZ;YACF;YAEA,QAAQ,KAAK,CAAC,2CAA2C;YACzD,YAAY,EAAE,GAAG,gCAAgC;QACnD,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,mBAAmB,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,UAAU,MAAM,CACjD,CAAC,UACC,QAAQ,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OAC1D,QAAQ,WAAW,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;IAGrE,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;;kCAEV,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAA+C;;;;;;0CAG7D,6VAAC;gCAAE,WAAU;;oCACV,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,UAAU,MAAM;oCAAC;oCAC3B,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,UAAU,MAAM,GAAG,IAAI,MAAM;oCAAG;;;;;;;;;;;;;kCAI/C,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,SAAQ;gCACR,WAAU;;kDAEV,6VAAC,4RAAA,CAAA,UAAO;wCAAC,WAAU;;;;;;kDACnB,6VAAC;kDAAK;;;;;;;;;;;;0CAGR,6VAAC,kIAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAU,WAAU;;kDAClC,6VAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;kDAChB,6VAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;0BAEV,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,0RAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,6VAAC;4BACC,MAAK;4BACL,aAAY;4BACZ,OAAO;4BACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;4BAC7C,WAAU;;;;;;;;;;;;;;;;;0BAMhB,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS,OAAO,OAAO;gBACvB,SAAS,OAAO,OAAO;gBACvB,WAAU;0BAET,wBACC,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAI,WAAU;;;;;;sCACf,6VAAC;4BAAE,WAAU;sCAAqB;;;;;;;;;;;2BAElC,iBAAiB,MAAM,KAAK,kBAC9B,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6VAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;2BAG/B,iBAAiB,GAAG,CAAC,CAAC;oBACpB,IAAI,CAAC,WAAW,CAAC,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,EAAE,OAAO;oBAErD,qBACE,6VAAC;wBAEC,WAAU;;0CAGV,6VAAC;gCAAI,WAAU;0CACZ,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,EAAE,MAAM,GAAG,kBAClC,6VAAC;oCACC,KAAK,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,MAAM,CAAC,CAAC,EAAE;oCACjC,KAAK,QAAQ,IAAI;oCACjB,WAAU;;;;;yDAGZ,6VAAC,4RAAA,CAAA,UAAO;oCAAC,WAAU;;;;;;;;;;;0CAKvB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAG,WAAU;0DACX,QAAQ,IAAI;;;;;;0DAEf,6VAAC;gDACC,WAAW,CAAC,2CAA2C,EACrD,QAAQ,QAAQ,GACZ,gCACA,2BACJ;0DAED,QAAQ,QAAQ,GAAG,UAAU;;;;;;;;;;;;kDAIlC,6VAAC;wCAAE,WAAU;kDACV,QAAQ,WAAW;;;;;;kDAGtB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6VAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,6HAAA,CAAA,0BAAuB,AAAD,EAAE,QAAQ,SAAS;;;;;;;;;;;;0DAG9C,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6VAAC;wDAAK,WAAU;kEACb,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,EAAE,MAAM;;;;;;;;;;;;;;;;;;oCAMxC,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,EAAE,MAAM,GAAG,mBACpC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAE,WAAU;0DAA6B;;;;;;0DAG1C,6VAAC;gDAAI,WAAU;;oDACZ,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,EACxB,KAAK,CAAC,GAAG,GACT,GAAG,CAAC,CAAC,wBACJ,6VAAC;4DAEC,WAAU;sEAET,QAAQ,MAAM;2DAHV,QAAQ,EAAE;;;;;oDAMpB,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,EAAE,MAAM,GAAG,mBACpC,6VAAC;wDAAK,WAAU;;4DAA2D;4DACvE,CAAA,GAAA,6HAAA,CAAA,YAAS,AAAD,EAAE,QAAQ,QAAQ,EAAE,MAAM,GAAG;;;;;;;;;;;;;;;;;;;kDAQjD,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;;kEAEV,6VAAC,+RAAA,CAAA,OAAI;wDAAC,WAAU;;;;;;kEAChB,6VAAC;kEAAK;;;;;;;;;;;;0DAER,6VAAC,kIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,WAAU;0DAEV,cAAA,6VAAC,8RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;uBA7FnB,QAAQ,EAAE;;;;;gBAmGrB;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "file": "square-pen.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/square-pen.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7', key: '1m0v6g' }],\n  [\n    'path',\n    {\n      d: 'M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z',\n      key: 'ohrbg2',\n    },\n  ],\n];\n\n/**\n * @component @name SquarePen\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMTIgM0g1YTIgMiAwIDAgMC0yIDJ2MTRhMiAyIDAgMCAwIDIgMmgxNGEyIDIgMCAwIDAgMi0ydi03IiAvPgogIDxwYXRoIGQ9Ik0xOC4zNzUgMi42MjVhMSAxIDAgMCAxIDMgM2wtOS4wMTMgOS4wMTRhMiAyIDAgMCAxLS44NTMuNTA1bC0yLjg3My44NGEuNS41IDAgMCAxLS42Mi0uNjJsLjg0LTIuODczYTIgMiAwIDAgMSAuNTA2LS44NTJ6IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/square-pen\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst SquarePen = createLucideIcon('square-pen', __iconNode);\n\nexport default SquarePen;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAA8D,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAC3F;QACE,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QACA;YACE,CAAG,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YACH,GAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;KACP;CAEJ;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,SAAA,CAAY,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAc,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 618, "column": 0}, "map": {"version": 3, "file": "plus.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/plus.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M5 12h14', key: '1ays0h' }],\n  ['path', { d: 'M12 5v14', key: 's699le' }],\n];\n\n/**\n * @component @name Plus\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNNSAxMmgxNCIgLz4KICA8cGF0aCBkPSJNMTIgNXYxNCIgLz4KPC9zdmc+Cg==) - https://lucide.dev/icons/plus\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Plus = createLucideIcon('plus', __iconNode);\n\nexport default Plus;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAY,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACzC;QAAC,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA,CAAE;YAAA,EAAG,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAY,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAU,CAAA;KAAA;CAC3C;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,IAAA,CAAO,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,EAAQ,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 664, "column": 0}, "map": {"version": 3, "file": "search.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/search.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'm21 21-4.34-4.34', key: '14j7rj' }],\n  ['circle', { cx: '11', cy: '11', r: '8', key: '4ej97u' }],\n];\n\n/**\n * @component @name Search\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJtMjEgMjEtNC4zNC00LjM0IiAvPgogIDxjaXJjbGUgY3g9IjExIiBjeT0iMTEiIHI9IjgiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/search\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Search = createLucideIcon('search', __iconNode);\n\nexport default Search;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAoB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACjD;QAAC,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAA;YAAE,CAAI,CAAA,CAAA,CAAA,IAAA,CAAM;YAAA,CAAA,CAAI,EAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAM,CAAG,CAAA,CAAA,CAAA,CAAA,CAAA;YAAK,CAAK,CAAA,CAAA,CAAA,CAAA,QAAA;QAAU,CAAA;KAAA;CAC1D;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAU,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 712, "column": 0}, "map": {"version": 3, "file": "trash-2.js", "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/lucide-react%400.525.0_react%4019.1.0/node_modules/lucide-react/src/icons/trash-2.ts"], "sourcesContent": ["import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [\n  ['path', { d: 'M3 6h18', key: 'd0wm0j' }],\n  ['path', { d: 'M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6', key: '4alrt4' }],\n  ['path', { d: 'M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2', key: 'v07s0e' }],\n  ['line', { x1: '10', x2: '10', y1: '11', y2: '17', key: '1uufr5' }],\n  ['line', { x1: '14', x2: '14', y1: '11', y2: '17', key: 'xtxkd' }],\n];\n\n/**\n * @component @name Trash2\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMyA2aDE4IiAvPgogIDxwYXRoIGQ9Ik0xOSA2djE0YzAgMS0xIDItMiAySDdjLTEgMC0yLTEtMi0yVjYiIC8+CiAgPHBhdGggZD0iTTggNlY0YzAtMSAxLTIgMi0yaDRjMSAwIDIgMSAyIDJ2MiIgLz4KICA8bGluZSB4MT0iMTAiIHgyPSIxMCIgeTE9IjExIiB5Mj0iMTciIC8+CiAgPGxpbmUgeDE9IjE0IiB4Mj0iMTQiIHkxPSIxMSIgeTI9IjE3IiAvPgo8L3N2Zz4K) - https://lucide.dev/icons/trash-2\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Trash2 = createLucideIcon('trash-2', __iconNode);\n\nexport default Trash2;\n"], "names": [], "mappings": ";;;;;;;;;;;AAGO,CAAA,CAAA,CAAA,CAAA,CAAA,CAAM,UAAuB,CAAA,CAAA,CAAA;IAClC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAW,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACxC;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAyC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACtE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAQ,CAAA;QAAA,CAAA;YAAE,GAAG,CAAsC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IACnE;QAAC,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAQ,CAAE;YAAA,CAAA,CAAA,EAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAA,CAAK;QAAA,CAAU;KAAA;IAClE;QAAC,MAAA,CAAQ;QAAA,CAAA;YAAE,EAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM,CAAA;YAAA,CAAA,CAAA,CAAI,CAAA,CAAA,CAAA,CAAA,CAAM;YAAA,CAAA,CAAA,CAAA,CAAI,CAAM,CAAA,CAAA,CAAA,CAAA;YAAA,CAAA,CAAA,CAAA,CAAI,IAAM,CAAA;YAAA,CAAA,CAAA,CAAA,CAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA;QAAS,CAAA;KAAA;CACnE;AAaM,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,MAAA,CAAS,CAAA,4PAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAiB,CAAjB,CAAA,AAAiB,CAAjB,AAAiB,CAAjB,AAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,EAAW,CAAU,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA", "ignoreList": [0], "debugId": null}}]}