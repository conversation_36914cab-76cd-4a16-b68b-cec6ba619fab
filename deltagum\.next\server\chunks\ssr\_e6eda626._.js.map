{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/success/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui\";\nimport { useCart } from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport Link from \"next/link\";\nimport { useRouter, useSearchParams } from \"next/navigation\";\nimport { useEffect, useState } from \"react\";\n\ninterface OrderDetails {\n  id: string;\n  totalAmount: number;\n  status: string;\n  items: Array<{\n    productName: string;\n    variantFlavor: string;\n    quantity: number;\n    price: number;\n  }>;\n  customer: {\n    firstName: string;\n    lastName: string;\n    email: string;\n  };\n}\n\nexport default function SuccessPage() {\n  const router = useRouter();\n  const searchParams = useSearchParams();\n  const sessionId = searchParams.get(\"session_id\");\n  const { clearCart } = useCart();\n\n  const [orderDetails, setOrderDetails] = useState<OrderDetails | null>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    if (!sessionId) {\n      setError(\"Session de paiement non trouvée\");\n      setLoading(false);\n      return;\n    }\n\n    // Récupérer les détails de la commande\n    const fetchOrderDetails = async () => {\n      try {\n        // D'abord, vérifier et confirmer le paiement si order_id est présent\n        const orderId = searchParams.get(\"order_id\");\n        if (orderId) {\n          const verifyResponse = await fetch(\"/api/checkout/verify-payment\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({\n              sessionId,\n              orderId,\n            }),\n          });\n\n          const verifyData = await verifyResponse.json();\n          if (verifyData.success) {\n            setOrderDetails(verifyData.data.order);\n            clearCart();\n            localStorage.removeItem(\"deltagum_pending_order\");\n            setLoading(false);\n            return;\n          }\n        }\n\n        // Fallback : utiliser l'ancienne méthode\n        const response = await fetch(`/api/checkout/session/${sessionId}`);\n        const data = await response.json();\n\n        if (data.success) {\n          setOrderDetails(data.data.order);\n          clearCart();\n          localStorage.removeItem(\"deltagum_pending_order\");\n        } else {\n          setError(\n            data.error || \"Erreur lors de la récupération de la commande\"\n          );\n        }\n      } catch (err) {\n        setError(\"Erreur de connexion\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchOrderDetails();\n  }, [sessionId]);\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center\">\n        <div className=\"text-center\">\n          <motion.div\n            className=\"text-6xl mb-4\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 2, repeat: Infinity, ease: \"linear\" }}\n          >\n            🍭\n          </motion.div>\n          <p className=\"text-lg text-gray-600\">\n            Vérification de votre commande...\n          </p>\n        </div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center\">\n        <div className=\"text-center max-w-md mx-auto p-8\">\n          <div className=\"text-6xl mb-4\">❌</div>\n          <h1 className=\"text-2xl font-bold text-gray-800 mb-4\">Erreur</h1>\n          <p className=\"text-gray-600 mb-6\">{error}</p>\n          <Link href=\"/\">\n            <Button variant=\"primary\">Retour à l'accueil</Button>\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-green-50 to-blue-50\">\n      <div className=\"container mx-auto px-4 py-16\">\n        <motion.div\n          className=\"max-w-2xl mx-auto text-center\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.6 }}\n        >\n          {/* Animation de succès */}\n          <motion.div\n            className=\"text-8xl mb-6\"\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{\n              type: \"spring\",\n              stiffness: 260,\n              damping: 20,\n              delay: 0.2,\n            }}\n          >\n            ✅\n          </motion.div>\n\n          <motion.h1\n            className=\"text-4xl font-bold text-gray-800 mb-4\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.4 }}\n          >\n            Paiement réussi !\n          </motion.h1>\n\n          <motion.p\n            className=\"text-xl text-gray-600 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.6 }}\n          >\n            Merci pour votre commande ! Vos délicieux bonbons Deltagum seront\n            bientôt en route.\n          </motion.p>\n\n          {/* Détails de la commande */}\n          {orderDetails && (\n            <motion.div\n              className=\"bg-white rounded-lg shadow-lg p-6 mb-8 text-left\"\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: 0.8 }}\n            >\n              <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">\n                Commande #{orderDetails.id.slice(-8)}\n              </h2>\n\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <h3 className=\"font-semibold text-gray-700 mb-2\">Client</h3>\n                  <p className=\"text-gray-600\">\n                    {orderDetails.customer.firstName}{\" \"}\n                    {orderDetails.customer.lastName}\n                  </p>\n                  <p className=\"text-gray-600\">{orderDetails.customer.email}</p>\n                </div>\n\n                <div>\n                  <h3 className=\"font-semibold text-gray-700 mb-2\">Statut</h3>\n                  <span className=\"inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium\">\n                    {orderDetails.status === \"PAID\"\n                      ? \"Payé\"\n                      : orderDetails.status}\n                  </span>\n                </div>\n              </div>\n\n              <div className=\"mt-6\">\n                <h3 className=\"font-semibold text-gray-700 mb-3\">\n                  Articles commandés\n                </h3>\n                <div className=\"space-y-2\">\n                  {orderDetails.items.map((item, index) => (\n                    <div\n                      key={index}\n                      className=\"flex text-black justify-between items-center py-2 border-b border-gray-100\"\n                    >\n                      <span className=\"text-gray-600\">\n                        {item.productName} - {item.variantFlavor} x{\" \"}\n                        {item.quantity}\n                      </span>\n                      <span className=\"font-medium\">\n                        {(item.price * item.quantity).toFixed(2)}€\n                      </span>\n                    </div>\n                  ))}\n                  <div className=\"flex justify-between items-center pt-3 font-bold text-black text-lg\">\n                    <span>Total</span>\n                    <span>{Number(orderDetails.totalAmount).toFixed(2)}€</span>\n                  </div>\n                </div>\n              </div>\n            </motion.div>\n          )}\n\n          {/* Prochaines étapes */}\n          <motion.div\n            className=\"bg-blue-50 rounded-lg p-6 mb-8\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.0 }}\n          >\n            <h3 className=\"text-xl font-bold text-blue-800 mb-4\">\n              Que se passe-t-il maintenant ?\n            </h3>\n            <div className=\"space-y-3 text-left\">\n              <div className=\"flex items-center\">\n                <span className=\"text-2xl mr-3\">📧</span>\n                <span className=\"text-blue-700\">\n                  Vous recevrez un email de confirmation sous peu\n                </span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-2xl mr-3\">📦</span>\n                <span className=\"text-blue-700\">\n                  Votre commande sera préparée dans les 24h\n                </span>\n              </div>\n              <div className=\"flex items-center\">\n                <span className=\"text-2xl mr-3\">🚚</span>\n                <span className=\"text-blue-700\">\n                  Livraison sous 3-5 jours ouvrés\n                </span>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Actions */}\n          <motion.div\n            className=\"flex flex-col sm:flex-row gap-4 justify-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.2 }}\n          >\n            <Link href=\"/\">\n              <Button variant=\"primary\" size=\"lg\">\n                Continuer mes achats\n              </Button>\n            </Link>\n            <Link href=\"/profile\">\n              <Button variant=\"outline\" size=\"lg\">\n                Voir mes commandes\n              </Button>\n            </Link>\n          </motion.div>\n        </motion.div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AA0Be,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,eAAe,CAAA,GAAA,iPAAA,CAAA,kBAAe,AAAD;IACnC,MAAM,YAAY,aAAa,GAAG,CAAC;IACnC,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD;IAE5B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAuB;IACtE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW;YACd,SAAS;YACT,WAAW;YACX;QACF;QAEA,uCAAuC;QACvC,MAAM,oBAAoB;YACxB,IAAI;gBACF,qEAAqE;gBACrE,MAAM,UAAU,aAAa,GAAG,CAAC;gBACjC,IAAI,SAAS;oBACX,MAAM,iBAAiB,MAAM,MAAM,gCAAgC;wBACjE,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;4BACnB;4BACA;wBACF;oBACF;oBAEA,MAAM,aAAa,MAAM,eAAe,IAAI;oBAC5C,IAAI,WAAW,OAAO,EAAE;wBACtB,gBAAgB,WAAW,IAAI,CAAC,KAAK;wBACrC;wBACA,aAAa,UAAU,CAAC;wBACxB,WAAW;wBACX;oBACF;gBACF;gBAEA,yCAAyC;gBACzC,MAAM,WAAW,MAAM,MAAM,CAAC,sBAAsB,EAAE,WAAW;gBACjE,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,EAAE;oBAChB,gBAAgB,KAAK,IAAI,CAAC,KAAK;oBAC/B;oBACA,aAAa,UAAU,CAAC;gBAC1B,OAAO;oBACL,SACE,KAAK,KAAK,IAAI;gBAElB;YACF,EAAE,OAAO,KAAK;gBACZ,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA;IACF,GAAG;QAAC;KAAU;IAEd,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,QAAQ;wBAAI;wBACvB,YAAY;4BAAE,UAAU;4BAAG,QAAQ;4BAAU,MAAM;wBAAS;kCAC7D;;;;;;kCAGD,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAM7C;IAEA,IAAI,OAAO;QACT,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6VAAC;wBAAG,WAAU;kCAAwC;;;;;;kCACtD,6VAAC;wBAAE,WAAU;kCAAsB;;;;;;kCACnC,6VAAC,2QAAA,CAAA,UAAI;wBAAC,MAAK;kCACT,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;sCAAU;;;;;;;;;;;;;;;;;;;;;;IAKpC;IAEA,qBACE,6VAAC;QAAI,WAAU;kBACb,cAAA,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;;kCAG5B,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BACV,MAAM;4BACN,WAAW;4BACX,SAAS;4BACT,OAAO;wBACT;kCACD;;;;;;kCAID,6VAAC,gSAAA,CAAA,SAAM,CAAC,EAAE;wBACR,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAC1B;;;;;;kCAID,6VAAC,gSAAA,CAAA,SAAM,CAAC,CAAC;wBACP,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAC1B;;;;;;oBAMA,8BACC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6VAAC;gCAAG,WAAU;;oCAAwC;oCACzC,aAAa,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;0CAGpC,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;;0DACC,6VAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6VAAC;gDAAE,WAAU;;oDACV,aAAa,QAAQ,CAAC,SAAS;oDAAE;oDACjC,aAAa,QAAQ,CAAC,QAAQ;;;;;;;0DAEjC,6VAAC;gDAAE,WAAU;0DAAiB,aAAa,QAAQ,CAAC,KAAK;;;;;;;;;;;;kDAG3D,6VAAC;;0DACC,6VAAC;gDAAG,WAAU;0DAAmC;;;;;;0DACjD,6VAAC;gDAAK,WAAU;0DACb,aAAa,MAAM,KAAK,SACrB,SACA,aAAa,MAAM;;;;;;;;;;;;;;;;;;0CAK7B,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAG,WAAU;kDAAmC;;;;;;kDAGjD,6VAAC;wCAAI,WAAU;;4CACZ,aAAa,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6VAAC;oDAEC,WAAU;;sEAEV,6VAAC;4DAAK,WAAU;;gEACb,KAAK,WAAW;gEAAC;gEAAI,KAAK,aAAa;gEAAC;gEAAG;gEAC3C,KAAK,QAAQ;;;;;;;sEAEhB,6VAAC;4DAAK,WAAU;;gEACb,CAAC,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE,OAAO,CAAC;gEAAG;;;;;;;;mDARtC;;;;;0DAYT,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;kEAAK;;;;;;kEACN,6VAAC;;4DAAM,OAAO,aAAa,WAAW,EAAE,OAAO,CAAC;4DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAQ7D,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6VAAC;gCAAG,WAAU;0CAAuC;;;;;;0CAGrD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAIlC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;kDAIlC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,6VAAC;gDAAK,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;;;;;;;kCAQtC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,6VAAC,2QAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;0CAItC,6VAAC,2QAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,6VAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;8CAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlD", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next%4015.3.5_%40babel%2Bcore%407.2_185ca0f072c7c00081c01751178945af/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}