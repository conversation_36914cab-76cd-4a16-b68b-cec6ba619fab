{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/flavors.ts"], "sourcesContent": ["// Traduction des saveurs\nexport const flavorTranslations = {\n  STRAWBERRY: \"Fraise\",\n  BLUEBERRY: \"Myrt<PERSON>\",\n  APPLE: \"Pomme\",\n} as const;\n\nexport type FlavorType = keyof typeof flavorTranslations;\n\nexport function translateFlavor(flavor: string): string {\n  return flavorTranslations[flavor as FlavorType] || flavor;\n}\n\n// Descriptions des saveurs\nexport const flavorDescriptions = {\n  STRAWBERRY:\n    \"Saveur fraise naturelle, douce et fruitée pour une expérience gustative délicieuse.\",\n  BLUEBERRY:\n    \"Saveur myrtille authentique, légèrement acidulée avec des notes sucrées.\",\n  APPLE:\n    \"Saveur pomme fraîche et croquante, parfaitement équilibrée entre douceur et fraîcheur.\",\n} as const;\n\nexport function getFlavorDescription(flavor: string): string {\n  return (\n    flavorDescriptions[flavor as FlavorType] ||\n    \"Délicieux délice Deltagum aux saveurs naturelles.\"\n  );\n}\n"], "names": [], "mappings": "AAAA,yBAAyB;;;;;;;AAClB,MAAM,qBAAqB;IAChC,YAAY;IACZ,WAAW;IACX,OAAO;AACT;AAIO,SAAS,gBAAgB,MAAc;IAC5C,OAAO,kBAAkB,CAAC,OAAqB,IAAI;AACrD;AAGO,MAAM,qBAAqB;IAChC,YACE;IACF,WACE;IACF,OACE;AACJ;AAEO,SAAS,qBAAqB,MAAc;IACjD,OACE,kBAAkB,CAAC,OAAqB,IACxC;AAEJ", "debugId": null}}, {"offset": {"line": 36, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/product/FlavorSlider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Badge } from \"@/components/ui\";\nimport { translateFlavor } from \"@/lib/flavors\";\nimport { formatPrice } from \"@/lib/utils\";\nimport type { ProductVariant } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport { ChevronLeft, ChevronRight } from \"lucide-react\";\nimport React, { useRef } from \"react\";\nimport { Navigation, Pagination } from \"swiper/modules\";\nimport { Swiper, SwiperSlide } from \"swiper/react\";\n\n// Import Swiper styles\nimport \"@/styles/swiper-custom.css\";\nimport \"swiper/css\";\nimport \"swiper/css/navigation\";\nimport \"swiper/css/pagination\";\n\ninterface FlavorSliderProps {\n  variants: ProductVariant[];\n  selectedVariant: ProductVariant | null;\n  onVariantSelect: (variant: ProductVariant) => void;\n}\n\nconst flavorConfig = {\n  STRAWBERRY: {\n    name: \"Fraise\",\n    emoji: \"🍓\",\n    color: \"from-pink-400 to-red-500\",\n    bgColor: \"bg-pink-50\",\n    borderColor: \"border-pink-300\",\n    selectedBorder: \"border-pink-500\",\n    textColor: \"text-pink-700\",\n  },\n  BLUEBERRY: {\n    name: \"Myrtille\",\n    emoji: \"🫐\",\n    color: \"from-blue-400 to-purple-500\",\n    bgColor: \"bg-blue-50\",\n    borderColor: \"border-blue-300\",\n    selectedBorder: \"border-blue-500\",\n    textColor: \"text-blue-700\",\n  },\n  APPLE: {\n    name: \"Pomme\",\n    emoji: \"🍏\",\n    color: \"from-green-400 to-emerald-500\",\n    bgColor: \"bg-green-50\",\n    borderColor: \"border-green-300\",\n    selectedBorder: \"border-green-500\",\n    textColor: \"text-green-700\",\n  },\n};\n\nconst FlavorSlider: React.FC<FlavorSliderProps> = ({\n  variants,\n  selectedVariant,\n  onVariantSelect,\n}) => {\n  const swiperRef = useRef<any>(null);\n\n  const getFlavorConfig = (flavor: string) => {\n    return (\n      flavorConfig[flavor as keyof typeof flavorConfig] || {\n        name: translateFlavor(flavor),\n        emoji: \"🍭\",\n        color: \"from-gray-400 to-gray-500\",\n        bgColor: \"bg-gray-50\",\n        borderColor: \"border-gray-300\",\n        selectedBorder: \"border-gray-500\",\n        textColor: \"text-gray-700\",\n      }\n    );\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Image de la saveur sélectionnée */}\n      {selectedVariant && (\n        <motion.div\n          key={selectedVariant.id}\n          className=\"text-center\"\n          initial={{ opacity: 0, y: -20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ duration: 0.5 }}\n        >\n          <div className=\"relative w-48 h-48 mx-auto rounded-2xl overflow-hidden shadow-xl\">\n            <img\n              src={\n                (selectedVariant as any).images?.[0] || \"/img/placeholder.svg\"\n              }\n              alt={`Délices Deltagum saveur ${\n                getFlavorConfig(selectedVariant.flavor).name\n              }`}\n              className=\"w-full h-full object-cover\"\n              onError={(e) => {\n                e.currentTarget.src = \"/img/placeholder.svg\";\n              }}\n            />\n            <div\n              className={`absolute inset-0 bg-gradient-to-br ${\n                getFlavorConfig(selectedVariant.flavor).color\n              } opacity-20`}\n            />\n            <div className=\"absolute bottom-3 left-3 right-3\">\n              <div className=\"bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg\">\n                <p className=\"text-base font-bold text-gray-800 text-center\">\n                  {getFlavorConfig(selectedVariant.flavor).emoji}{\" \"}\n                  {getFlavorConfig(selectedVariant.flavor).name}\n                </p>\n              </div>\n            </div>\n          </div>\n        </motion.div>\n      )}\n\n      {/* Slider des saveurs */}\n      <div className=\"relative\">\n        {/* Boutons de navigation personnalisés */}\n        <button\n          onClick={() => swiperRef.current?.slidePrev()}\n          className=\"flavor-nav-button prev\"\n          aria-label=\"Saveur précédente\"\n        >\n          <ChevronLeft size={20} />\n        </button>\n\n        <button\n          onClick={() => swiperRef.current?.slideNext()}\n          className=\"flavor-nav-button next\"\n          aria-label=\"Saveur suivante\"\n        >\n          <ChevronRight size={20} />\n        </button>\n\n        {/* Swiper Slider */}\n        <Swiper\n          ref={swiperRef}\n          modules={[Navigation, Pagination]}\n          spaceBetween={16}\n          slidesPerView={1}\n          centeredSlides={true}\n          pagination={{\n            clickable: true,\n            bulletClass: \"swiper-pagination-bullet !bg-pink-300\",\n            bulletActiveClass: \"swiper-pagination-bullet-active !bg-pink-500\",\n          }}\n          breakpoints={{\n            640: {\n              slidesPerView: 2,\n              spaceBetween: 20,\n            },\n            768: {\n              slidesPerView: 3,\n              spaceBetween: 24,\n            },\n          }}\n          className=\"!pb-12\"\n        >\n          {variants.map((variant) => {\n            const config = getFlavorConfig(variant.flavor);\n            const isSelected = selectedVariant?.id === variant.id;\n            const isOutOfStock = variant.stock === 0;\n\n            return (\n              <SwiperSlide key={variant.id}>\n                <motion.div\n                  whileHover={{ scale: isOutOfStock ? 1 : 1.02 }}\n                  whileTap={{ scale: isOutOfStock ? 1 : 0.98 }}\n                  className=\"h-full\"\n                >\n                  <div\n                    className={`\n                      flavor-card relative p-6 rounded-2xl border-2 cursor-pointer text-center h-full\n                      ${\n                        isSelected\n                          ? `${config.selectedBorder} ${config.bgColor} shadow-lg selected`\n                          : `${config.borderColor} bg-white hover:${config.bgColor} shadow-md`\n                      }\n                      ${isOutOfStock ? \"opacity-50 cursor-not-allowed\" : \"\"}\n                    `}\n                    onClick={() => !isOutOfStock && onVariantSelect(variant)}\n                  >\n                    {/* Selection Indicator */}\n                    {isSelected && (\n                      <motion.div\n                        className=\"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center\"\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{\n                          type: \"spring\",\n                          stiffness: 500,\n                          damping: 30,\n                        }}\n                      >\n                        <span className=\"text-white text-xs\">✓</span>\n                      </motion.div>\n                    )}\n\n                    {/* Out of Stock Badge */}\n                    {isOutOfStock && (\n                      <div className=\"absolute -top-2 -left-2\">\n                        <Badge variant=\"danger\" size=\"sm\">\n                          Épuisé\n                        </Badge>\n                      </div>\n                    )}\n\n                    {/* Contenu de la carte */}\n                    <div className=\"space-y-4\">\n                      {/* Emoji grande taille */}\n                      <div className=\"text-5xl\">{config.emoji}</div>\n\n                      {/* Nom de la saveur */}\n                      <h5 className={`font-bold text-xl ${config.textColor}`}>\n                        {config.name}\n                      </h5>\n\n                      {/* Prix */}\n                      <div className=\"text-xl font-semibold text-gray-800\">\n                        {formatPrice(Number(variant.product?.price || 0))}\n                      </div>\n\n                      {/* Indicateur de stock */}\n                      <div className=\"flex items-center justify-center space-x-1\">\n                        <div\n                          className={`w-2 h-2 rounded-full ${\n                            variant.stock > 10\n                              ? \"bg-green-500\"\n                              : variant.stock > 0\n                              ? \"bg-yellow-500\"\n                              : \"bg-red-500\"\n                          }`}\n                        />\n                        <span className=\"text-sm text-gray-500\">\n                          {variant.stock > 10\n                            ? \"En stock\"\n                            : variant.stock > 0\n                            ? `${variant.stock} restant${\n                                variant.stock > 1 ? \"s\" : \"\"\n                              }`\n                            : \"Épuisé\"}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Hover Effect Overlay */}\n                    {!isOutOfStock && (\n                      <motion.div\n                        className={`absolute inset-0 rounded-2xl bg-gradient-to-r ${config.color} opacity-0 pointer-events-none`}\n                        whileHover={{ opacity: 0.1 }}\n                        transition={{ duration: 0.2 }}\n                      />\n                    )}\n                  </div>\n                </motion.div>\n              </SwiperSlide>\n            );\n          })}\n        </Swiper>\n      </div>\n\n      {/* Message si aucune variante */}\n      {variants.length === 0 && (\n        <motion.div\n          className=\"text-center py-8\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n        >\n          <div className=\"text-4xl mb-2\">🍭</div>\n          <p className=\"text-gray-500\">\n            Aucune saveur disponible pour ce produit\n          </p>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport { FlavorSlider };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAEA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAVA;;;;;;;;;;;;;;AAwBA,MAAM,eAAe;IACnB,YAAY;QACV,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;IACA,OAAO;QACL,MAAM;QACN,OAAO;QACP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;AACF;AAEA,MAAM,eAA4C,CAAC,EACjD,QAAQ,EACR,eAAe,EACf,eAAe,EAChB;IACC,MAAM,YAAY,CAAA,GAAA,oTAAA,CAAA,SAAM,AAAD,EAAO;IAE9B,MAAM,kBAAkB,CAAC;QACvB,OACE,YAAY,CAAC,OAAoC,IAAI;YACnD,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;YACtB,OAAO;YACP,OAAO;YACP,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,WAAW;QACb;IAEJ;IAEA,qBACE,6VAAC;QAAI,WAAU;;YAEZ,iCACC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,UAAU;gBAAI;0BAE5B,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BACC,KACE,AAAC,gBAAwB,MAAM,EAAE,CAAC,EAAE,IAAI;4BAE1C,KAAK,CAAC,wBAAwB,EAC5B,gBAAgB,gBAAgB,MAAM,EAAE,IAAI,EAC5C;4BACF,WAAU;4BACV,SAAS,CAAC;gCACR,EAAE,aAAa,CAAC,GAAG,GAAG;4BACxB;;;;;;sCAEF,6VAAC;4BACC,WAAW,CAAC,mCAAmC,EAC7C,gBAAgB,gBAAgB,MAAM,EAAE,KAAK,CAC9C,WAAW,CAAC;;;;;;sCAEf,6VAAC;4BAAI,WAAU;sCACb,cAAA,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC;oCAAE,WAAU;;wCACV,gBAAgB,gBAAgB,MAAM,EAAE,KAAK;wCAAE;wCAC/C,gBAAgB,gBAAgB,MAAM,EAAE,IAAI;;;;;;;;;;;;;;;;;;;;;;;eA5BhD,gBAAgB,EAAE;;;;;0BAqC3B,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBACC,SAAS,IAAM,UAAU,OAAO,EAAE;wBAClC,WAAU;wBACV,cAAW;kCAEX,cAAA,6VAAC,wSAAA,CAAA,cAAW;4BAAC,MAAM;;;;;;;;;;;kCAGrB,6VAAC;wBACC,SAAS,IAAM,UAAU,OAAO,EAAE;wBAClC,WAAU;wBACV,cAAW;kCAEX,cAAA,6VAAC,0SAAA,CAAA,eAAY;4BAAC,MAAM;;;;;;;;;;;kCAItB,6VAAC,+LAAA,CAAA,SAAM;wBACL,KAAK;wBACL,SAAS;4BAAC,8OAAA,CAAA,aAAU;4BAAE,8OAAA,CAAA,aAAU;yBAAC;wBACjC,cAAc;wBACd,eAAe;wBACf,gBAAgB;wBAChB,YAAY;4BACV,WAAW;4BACX,aAAa;4BACb,mBAAmB;wBACrB;wBACA,aAAa;4BACX,KAAK;gCACH,eAAe;gCACf,cAAc;4BAChB;4BACA,KAAK;gCACH,eAAe;gCACf,cAAc;4BAChB;wBACF;wBACA,WAAU;kCAET,SAAS,GAAG,CAAC,CAAC;4BACb,MAAM,SAAS,gBAAgB,QAAQ,MAAM;4BAC7C,MAAM,aAAa,iBAAiB,OAAO,QAAQ,EAAE;4BACrD,MAAM,eAAe,QAAQ,KAAK,KAAK;4BAEvC,qBACE,6VAAC,+LAAA,CAAA,cAAW;0CACV,cAAA,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,YAAY;wCAAE,OAAO,eAAe,IAAI;oCAAK;oCAC7C,UAAU;wCAAE,OAAO,eAAe,IAAI;oCAAK;oCAC3C,WAAU;8CAEV,cAAA,6VAAC;wCACC,WAAW,CAAC;;sBAEV,EACE,aACI,GAAG,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,mBAAmB,CAAC,GAC/D,GAAG,OAAO,WAAW,CAAC,gBAAgB,EAAE,OAAO,OAAO,CAAC,UAAU,CAAC,CACvE;sBACD,EAAE,eAAe,kCAAkC,GAAG;oBACxD,CAAC;wCACD,SAAS,IAAM,CAAC,gBAAgB,gBAAgB;;4CAG/C,4BACC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDACV,MAAM;oDACN,WAAW;oDACX,SAAS;gDACX;0DAEA,cAAA,6VAAC;oDAAK,WAAU;8DAAqB;;;;;;;;;;;4CAKxC,8BACC,6VAAC;gDAAI,WAAU;0DACb,cAAA,6VAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAS,MAAK;8DAAK;;;;;;;;;;;0DAOtC,6VAAC;gDAAI,WAAU;;kEAEb,6VAAC;wDAAI,WAAU;kEAAY,OAAO,KAAK;;;;;;kEAGvC,6VAAC;wDAAG,WAAW,CAAC,kBAAkB,EAAE,OAAO,SAAS,EAAE;kEACnD,OAAO,IAAI;;;;;;kEAId,6VAAC;wDAAI,WAAU;kEACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,OAAO,QAAQ,OAAO,EAAE,SAAS;;;;;;kEAIhD,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,KAAK,GAAG,KACZ,iBACA,QAAQ,KAAK,GAAG,IAChB,kBACA,cACJ;;;;;;0EAEJ,6VAAC;gEAAK,WAAU;0EACb,QAAQ,KAAK,GAAG,KACb,aACA,QAAQ,KAAK,GAAG,IAChB,GAAG,QAAQ,KAAK,CAAC,QAAQ,EACvB,QAAQ,KAAK,GAAG,IAAI,MAAM,IAC1B,GACF;;;;;;;;;;;;;;;;;;4CAMT,CAAC,8BACA,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAW,CAAC,8CAA8C,EAAE,OAAO,KAAK,CAAC,8BAA8B,CAAC;gDACxG,YAAY;oDAAE,SAAS;gDAAI;gDAC3B,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;;;;;;+BAtFpB,QAAQ,EAAE;;;;;wBA6FhC;;;;;;;;;;;;YAKH,SAAS,MAAM,KAAK,mBACnB,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;;kCAEtB,6VAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6VAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 449, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/product/FlavorSelector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Badge } from \"@/components/ui\";\nimport { staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { translateFlavor } from \"@/lib/flavors\";\nimport type { ProductVariant } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport React, { useEffect, useState } from \"react\";\nimport { FlavorSlider } from \"./FlavorSlider\";\n\ninterface FlavorSelectorProps {\n  variants: ProductVariant[];\n  selectedVariant: ProductVariant | null;\n  onVariantSelect: (variant: ProductVariant) => void;\n}\n\nconst flavorConfig = {\n  STRAWBERRY: {\n    name: \"Fraise\",\n    emoji: \"🍓\",\n\n    color: \"from-pink-400 to-red-500\",\n    bgColor: \"bg-pink-50\",\n    borderColor: \"border-pink-300\",\n    selectedBorder: \"border-pink-500\",\n    textColor: \"text-pink-700\",\n  },\n  BLUEBERRY: {\n    name: \"<PERSON><PERSON><PERSON>\",\n    emoji: \"🫐\",\n\n    color: \"from-blue-400 to-purple-500\",\n    bgColor: \"bg-blue-50\",\n    borderColor: \"border-blue-300\",\n    selectedBorder: \"border-blue-500\",\n    textColor: \"text-blue-700\",\n  },\n  APPLE: {\n    name: \"Pomme\",\n    emoji: \"🍏\",\n\n    color: \"from-green-400 to-emerald-500\",\n    bgColor: \"bg-green-50\",\n    borderColor: \"border-green-300\",\n    selectedBorder: \"border-green-500\",\n    textColor: \"text-green-700\",\n  },\n};\n\nconst FlavorSelector: React.FC<FlavorSelectorProps> = ({\n  variants,\n  selectedVariant,\n  onVariantSelect,\n}) => {\n  const [screenSize, setScreenSize] = useState<\"mobile\" | \"tablet\" | \"desktop\">(\n    \"desktop\"\n  );\n\n  // Détecter la taille d'écran\n  useEffect(() => {\n    const checkScreenSize = () => {\n      const width = window.innerWidth;\n      if (width < 640) {\n        setScreenSize(\"mobile\");\n      } else if (width < 1024) {\n        setScreenSize(\"tablet\");\n      } else {\n        setScreenSize(\"desktop\");\n      }\n    };\n\n    checkScreenSize();\n    window.addEventListener(\"resize\", checkScreenSize);\n\n    return () => window.removeEventListener(\"resize\", checkScreenSize);\n  }, []);\n\n  const getFlavorConfig = (flavor: string) => {\n    return (\n      flavorConfig[flavor as keyof typeof flavorConfig] || {\n        name: translateFlavor(flavor),\n        emoji: \"🍭\",\n        color: \"from-gray-400 to-gray-500\",\n        bgColor: \"bg-gray-50\",\n        borderColor: \"border-gray-300\",\n        selectedBorder: \"border-gray-500\",\n        textColor: \"text-gray-700\",\n      }\n    );\n  };\n\n  // Si on est sur mobile ou tablette, utiliser le slider\n  if (screenSize === \"mobile\" || screenSize === \"tablet\") {\n    return (\n      <FlavorSlider\n        variants={variants}\n        selectedVariant={selectedVariant}\n        onVariantSelect={onVariantSelect}\n      />\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Sélecteur horizontal des saveurs */}\n      <motion.div\n        className=\"grid grid-cols-3 gap-4\"\n        variants={staggerContainer}\n        initial=\"initial\"\n        animate=\"animate\"\n      >\n        {variants.map((variant) => {\n          const config = getFlavorConfig(variant.flavor);\n          const isSelected = selectedVariant?.id === variant.id;\n          const isOutOfStock = variant.stock === 0;\n\n          return (\n            <motion.div\n              key={variant.id}\n              variants={staggerItem}\n              whileHover={{ scale: isOutOfStock ? 1 : 1.05 }}\n              whileTap={{ scale: isOutOfStock ? 1 : 0.95 }}\n            >\n              <div\n                className={`\n                  relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 text-center\n                  ${\n                    isSelected\n                      ? `${config.selectedBorder} ${config.bgColor} shadow-lg`\n                      : `${config.borderColor} bg-white hover:${config.bgColor}`\n                  }\n                  ${isOutOfStock ? \"opacity-50 cursor-not-allowed\" : \"\"}\n                `}\n                onClick={() => !isOutOfStock && onVariantSelect(variant)}\n              >\n                {/* Selection Indicator */}\n                {isSelected && (\n                  <motion.div\n                    className=\"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center\"\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 500, damping: 30 }}\n                  >\n                    <span className=\"text-white text-xs\">✓</span>\n                  </motion.div>\n                )}\n\n                {/* Out of Stock Badge */}\n                {isOutOfStock && (\n                  <div className=\"absolute -top-2 -left-2\">\n                    <Badge variant=\"danger\" size=\"sm\">\n                      Épuisé\n                    </Badge>\n                  </div>\n                )}\n\n                {/* Contenu simplifié */}\n                <div className=\"space-y-3\">\n                  {/* Nom de la saveur */}\n                  <h5 className={`font-bold text-lg ${config.textColor}`}>\n                    {config.name}\n                  </h5>\n\n                  {/* Indicateur de stock */}\n                  <div className=\"flex items-center justify-center space-x-1\">\n                    <div\n                      className={`w-2 h-2 rounded-full ${\n                        variant.stock > 10\n                          ? \"bg-green-500\"\n                          : variant.stock > 0\n                          ? \"bg-yellow-500\"\n                          : \"bg-red-500\"\n                      }`}\n                    />\n                    <span className=\"text-xs text-gray-500\">\n                      {variant.stock > 10\n                        ? \"En stock\"\n                        : variant.stock > 0\n                        ? `${variant.stock} restant${\n                            variant.stock > 1 ? \"s\" : \"\"\n                          }`\n                        : \"Épuisé\"}\n                    </span>\n                  </div>\n                </div>\n\n                {/* Hover Effect Overlay */}\n                {!isOutOfStock && (\n                  <motion.div\n                    className={`absolute inset-0 rounded-xl bg-gradient-to-r ${config.color} opacity-0 pointer-events-none`}\n                    whileHover={{ opacity: 0.1 }}\n                    transition={{ duration: 0.2 }}\n                  />\n                )}\n              </div>\n            </motion.div>\n          );\n        })}\n      </motion.div>\n\n      {/* No Variants Message */}\n      {variants.length === 0 && (\n        <motion.div\n          className=\"text-center py-8\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n        >\n          <div className=\"text-4xl mb-2\">🍭</div>\n          <p className=\"text-gray-500\">\n            Aucune saveur disponible pour ce produit\n          </p>\n        </motion.div>\n      )}\n    </div>\n  );\n};\n\nexport { FlavorSelector };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAEA;AACA;AACA;AARA;;;;;;;;AAgBA,MAAM,eAAe;IACnB,YAAY;QACV,MAAM;QACN,OAAO;QAEP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;IACA,WAAW;QACT,MAAM;QACN,OAAO;QAEP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;IACA,OAAO;QACL,MAAM;QACN,OAAO;QAEP,OAAO;QACP,SAAS;QACT,aAAa;QACb,gBAAgB;QAChB,WAAW;IACb;AACF;AAEA,MAAM,iBAAgD,CAAC,EACrD,QAAQ,EACR,eAAe,EACf,eAAe,EAChB;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EACzC;IAGF,6BAA6B;IAC7B,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,kBAAkB;YACtB,MAAM,QAAQ,OAAO,UAAU;YAC/B,IAAI,QAAQ,KAAK;gBACf,cAAc;YAChB,OAAO,IAAI,QAAQ,MAAM;gBACvB,cAAc;YAChB,OAAO;gBACL,cAAc;YAChB;QACF;QAEA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAElC,OAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IAEL,MAAM,kBAAkB,CAAC;QACvB,OACE,YAAY,CAAC,OAAoC,IAAI;YACnD,MAAM,CAAA,GAAA,qHAAA,CAAA,kBAAe,AAAD,EAAE;YACtB,OAAO;YACP,OAAO;YACP,SAAS;YACT,aAAa;YACb,gBAAgB;YAChB,WAAW;QACb;IAEJ;IAEA,uDAAuD;IACvD,IAAI,eAAe,YAAY,eAAe,UAAU;QACtD,qBACE,6VAAC,6IAAA,CAAA,eAAY;YACX,UAAU;YACV,iBAAiB;YACjB,iBAAiB;;;;;;IAGvB;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,UAAU,wHAAA,CAAA,mBAAgB;gBAC1B,SAAQ;gBACR,SAAQ;0BAEP,SAAS,GAAG,CAAC,CAAC;oBACb,MAAM,SAAS,gBAAgB,QAAQ,MAAM;oBAC7C,MAAM,aAAa,iBAAiB,OAAO,QAAQ,EAAE;oBACrD,MAAM,eAAe,QAAQ,KAAK,KAAK;oBAEvC,qBACE,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,UAAU,wHAAA,CAAA,cAAW;wBACrB,YAAY;4BAAE,OAAO,eAAe,IAAI;wBAAK;wBAC7C,UAAU;4BAAE,OAAO,eAAe,IAAI;wBAAK;kCAE3C,cAAA,6VAAC;4BACC,WAAW,CAAC;;kBAEV,EACE,aACI,GAAG,OAAO,cAAc,CAAC,CAAC,EAAE,OAAO,OAAO,CAAC,UAAU,CAAC,GACtD,GAAG,OAAO,WAAW,CAAC,gBAAgB,EAAE,OAAO,OAAO,EAAE,CAC7D;kBACD,EAAE,eAAe,kCAAkC,GAAG;gBACxD,CAAC;4BACD,SAAS,IAAM,CAAC,gBAAgB,gBAAgB;;gCAG/C,4BACC,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAE;oCACpB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,MAAM;wCAAU,WAAW;wCAAK,SAAS;oCAAG;8CAE1D,cAAA,6VAAC;wCAAK,WAAU;kDAAqB;;;;;;;;;;;gCAKxC,8BACC,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAS,MAAK;kDAAK;;;;;;;;;;;8CAOtC,6VAAC;oCAAI,WAAU;;sDAEb,6VAAC;4CAAG,WAAW,CAAC,kBAAkB,EAAE,OAAO,SAAS,EAAE;sDACnD,OAAO,IAAI;;;;;;sDAId,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDACC,WAAW,CAAC,qBAAqB,EAC/B,QAAQ,KAAK,GAAG,KACZ,iBACA,QAAQ,KAAK,GAAG,IAChB,kBACA,cACJ;;;;;;8DAEJ,6VAAC;oDAAK,WAAU;8DACb,QAAQ,KAAK,GAAG,KACb,aACA,QAAQ,KAAK,GAAG,IAChB,GAAG,QAAQ,KAAK,CAAC,QAAQ,EACvB,QAAQ,KAAK,GAAG,IAAI,MAAM,IAC1B,GACF;;;;;;;;;;;;;;;;;;gCAMT,CAAC,8BACA,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAW,CAAC,6CAA6C,EAAE,OAAO,KAAK,CAAC,8BAA8B,CAAC;oCACvG,YAAY;wCAAE,SAAS;oCAAI;oCAC3B,YAAY;wCAAE,UAAU;oCAAI;;;;;;;;;;;;uBAzE7B,QAAQ,EAAE;;;;;gBA+ErB;;;;;;YAID,SAAS,MAAM,KAAK,mBACnB,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;;kCAEtB,6VAAC;wBAAI,WAAU;kCAAgB;;;;;;kCAC/B,6VAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;;AAOvC", "debugId": null}}, {"offset": {"line": 724, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/product/QuantitySelector.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"@/components/ui\";\nimport { formatPrice } from \"@/lib/utils\";\nimport type { PriceTier } from \"@/types\";\nimport { motion } from \"framer-motion\";\n\ninterface QuantitySelectorProps {\n  priceTiers: PriceTier[];\n  selectedQuantity: number;\n  onQuantityChange: (quantity: number) => void;\n  className?: string;\n}\n\nexport function QuantitySelector({\n  priceTiers,\n  selectedQuantity,\n  onQuantityChange,\n  className = \"\",\n}: QuantitySelectorProps) {\n  // Trier les prix par quantité\n  const sortedTiers = [...priceTiers].sort((a, b) => a.quantity - b.quantity);\n\n  // Calculer les économies par rapport au prix unitaire\n  const basePrice = sortedTiers[0]?.price || 0;\n\n  const calculateSavings = (tier: PriceTier) => {\n    const unitPrice = basePrice;\n    const totalAtUnitPrice = unitPrice * tier.quantity;\n    const savings = totalAtUnitPrice - tier.price;\n    const savingsPercent = Math.round((savings / totalAtUnitPrice) * 100);\n    return { savings, savingsPercent };\n  };\n\n  return (\n    <div className={`space-y-4 ${className}`}>\n      <div className=\"text-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900 mb-2\">\n          Choisissez votre quantité\n        </h3>\n        <p className=\"text-sm text-gray-600\">\n          Plus vous achetez, plus vous économisez !\n        </p>\n      </div>\n\n      <div className=\"flex justify-center\">\n        <div className=\"grid grid-cols-2 lg:grid-cols-3 gap-4 max-w-md\">\n          {sortedTiers.map((tier) => {\n            const { savings, savingsPercent } = calculateSavings(tier);\n            const isSelected = selectedQuantity === tier.quantity;\n            const isPopular = tier.quantity === 6 || tier.quantity === 5; // 6 pour bonbons, 5 pour cookies\n\n            return (\n              <motion.div\n                key={tier.id}\n                className=\"relative\"\n                whileHover={{ scale: 1.02 }}\n                whileTap={{ scale: 0.98 }}\n              >\n                {isPopular && (\n                  <Badge\n                    variant=\"primary\"\n                    className=\"absolute -top-2 left-1/2 transform -translate-x-1/2 z-10 text-xs\"\n                  >\n                    Populaire\n                  </Badge>\n                )}\n\n                <Button\n                  variant={isSelected ? \"primary\" : \"outline\"}\n                  size=\"lg\"\n                  onClick={() => onQuantityChange(tier.quantity)}\n                  className={`w-full h-32 p-4 flex flex-col items-center justify-center space-y-2 ${\n                    isSelected\n                      ? \"ring-2 ring-pink-500 ring-offset-2\"\n                      : \"hover:border-pink-300\"\n                  }`}\n                >\n                  <div className=\"text-2xl font-bold\">{tier.quantity}</div>\n\n                  <div className=\"text-center\">\n                    <div\n                      className={`text-lg font-semibold ${\n                        isSelected ? \"text-white\" : \"text-gray-900\"\n                      }`}\n                    >\n                      {formatPrice(tier.price)}\n                    </div>\n\n                    {tier.quantity > 1 && (\n                      <div\n                        className={`text-xs ${\n                          isSelected ? \"text-pink-100\" : \"text-gray-500\"\n                        }`}\n                      >\n                        {formatPrice(tier.price / tier.quantity)} / unité\n                      </div>\n                    )}\n                  </div>\n\n                  {savings > 0 && (\n                    <div\n                      className={`text-xs font-medium ${\n                        isSelected ? \"text-pink-100\" : \"text-green-600\"\n                      }`}\n                    >\n                      Économisez {savingsPercent}%\n                    </div>\n                  )}\n                </Button>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Affichage du prix sélectionné */}\n      <div className=\"text-center p-4 bg-gray-50 rounded-lg\">\n        <div className=\"text-sm text-gray-600 mb-1\">Prix total</div>\n        <div className=\"text-2xl font-bold text-gray-900\">\n          {formatPrice(\n            sortedTiers.find((t) => t.quantity === selectedQuantity)?.price || 0\n          )}\n        </div>\n        {selectedQuantity > 1 && (\n          <div className=\"text-sm text-gray-500\">\n            Soit{\" \"}\n            {formatPrice(\n              (sortedTiers.find((t) => t.quantity === selectedQuantity)\n                ?.price || 0) / selectedQuantity\n            )}{\" \"}\n            par unité\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAEA;AALA;;;;;AAcO,SAAS,iBAAiB,EAC/B,UAAU,EACV,gBAAgB,EAChB,gBAAgB,EAChB,YAAY,EAAE,EACQ;IACtB,8BAA8B;IAC9B,MAAM,cAAc;WAAI;KAAW,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;IAE1E,sDAAsD;IACtD,MAAM,YAAY,WAAW,CAAC,EAAE,EAAE,SAAS;IAE3C,MAAM,mBAAmB,CAAC;QACxB,MAAM,YAAY;QAClB,MAAM,mBAAmB,YAAY,KAAK,QAAQ;QAClD,MAAM,UAAU,mBAAmB,KAAK,KAAK;QAC7C,MAAM,iBAAiB,KAAK,KAAK,CAAC,AAAC,UAAU,mBAAoB;QACjE,OAAO;YAAE;YAAS;QAAe;IACnC;IAEA,qBACE,6VAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAG,WAAU;kCAA2C;;;;;;kCAGzD,6VAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;0BAKvC,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;8BACZ,YAAY,GAAG,CAAC,CAAC;wBAChB,MAAM,EAAE,OAAO,EAAE,cAAc,EAAE,GAAG,iBAAiB;wBACrD,MAAM,aAAa,qBAAqB,KAAK,QAAQ;wBACrD,MAAM,YAAY,KAAK,QAAQ,KAAK,KAAK,KAAK,QAAQ,KAAK,GAAG,iCAAiC;wBAE/F,qBACE,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;gCAEvB,2BACC,6VAAC,iIAAA,CAAA,QAAK;oCACJ,SAAQ;oCACR,WAAU;8CACX;;;;;;8CAKH,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAS,aAAa,YAAY;oCAClC,MAAK;oCACL,SAAS,IAAM,iBAAiB,KAAK,QAAQ;oCAC7C,WAAW,CAAC,oEAAoE,EAC9E,aACI,uCACA,yBACJ;;sDAEF,6VAAC;4CAAI,WAAU;sDAAsB,KAAK,QAAQ;;;;;;sDAElD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDACC,WAAW,CAAC,sBAAsB,EAChC,aAAa,eAAe,iBAC5B;8DAED,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK;;;;;;gDAGxB,KAAK,QAAQ,GAAG,mBACf,6VAAC;oDACC,WAAW,CAAC,QAAQ,EAClB,aAAa,kBAAkB,iBAC/B;;wDAED,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK,GAAG,KAAK,QAAQ;wDAAE;;;;;;;;;;;;;wCAK9C,UAAU,mBACT,6VAAC;4CACC,WAAW,CAAC,oBAAoB,EAC9B,aAAa,kBAAkB,kBAC/B;;gDACH;gDACa;gDAAe;;;;;;;;;;;;;;2BApD5B,KAAK,EAAE;;;;;oBA0DlB;;;;;;;;;;;0BAKJ,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCAA6B;;;;;;kCAC5C,6VAAC;wBAAI,WAAU;kCACZ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EACT,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,mBAAmB,SAAS;;;;;;oBAGtE,mBAAmB,mBAClB,6VAAC;wBAAI,WAAU;;4BAAwB;4BAChC;4BACJ,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EACT,CAAC,YAAY,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,mBACpC,SAAS,CAAC,IAAI;4BACjB;4BAAI;;;;;;;;;;;;;;;;;;;AAOnB", "debugId": null}}, {"offset": {"line": 938, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/products/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { FlavorSelector } from \"@/components/product/FlavorSelector\";\nimport { QuantitySelector } from \"@/components/product/QuantitySelector\";\nimport { Button } from \"@/components/ui\";\nimport { fadeIn } from \"@/lib/animations\";\nimport { formatPrice } from \"@/lib/utils\";\nimport { useCart, useNotifications, useProduct } from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport { ArrowLeft, ShoppingCart } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useParams } from \"next/navigation\";\nimport { useEffect, useState } from \"react\";\n\nexport default function ProductPage() {\n  const params = useParams();\n  const productId = params.id;\n\n  const { products, fetchProducts } = useProduct();\n  const { addItem } = useCart();\n  const { addNotification } = useNotifications();\n\n  const [selectedProduct, setSelectedProduct] = useState<any>(null);\n  const [selectedVariant, setSelectedVariant] = useState<any>(null);\n  const [selectedQuantity, setSelectedQuantity] = useState(1);\n  const [selectedImageIndex, setSelectedImageIndex] = useState(0);\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  useEffect(() => {\n    if (products.length > 0 && productId) {\n      const product = products.find((p) => p.id === productId);\n      if (product) {\n        setSelectedProduct(product);\n        if (product.variants && product.variants.length > 0) {\n          setSelectedVariant(product.variants[0]);\n        }\n        if (product.priceTiers && product.priceTiers.length > 0) {\n          const sortedTiers = [...product.priceTiers].sort(\n            (a, b) => a.quantity - b.quantity\n          );\n          setSelectedQuantity(sortedTiers[0].quantity);\n        }\n      }\n    }\n  }, [products, productId]);\n\n  // Réinitialiser l'index d'image quand on change de variante\n  useEffect(() => {\n    setSelectedImageIndex(0);\n  }, [selectedVariant]);\n\n  const getPriceForQuantity = (product: any, quantity: number) => {\n    if (!product.priceTiers || product.priceTiers.length === 0) {\n      return Number(product.basePrice) * quantity;\n    }\n\n    const priceTier = product.priceTiers.find(\n      (tier) => tier.quantity === quantity\n    );\n    return priceTier\n      ? Number(priceTier.price)\n      : Number(product.basePrice) * quantity;\n  };\n\n  const handleQuantityChange = (newQuantity: number) => {\n    setSelectedQuantity(newQuantity);\n  };\n\n  const handleAddToCart = () => {\n    if (!selectedProduct || !selectedVariant) return;\n\n    addItem({\n      productId: selectedProduct.id,\n      variantId: selectedVariant.id,\n      quantity: selectedQuantity,\n      name: selectedProduct.name,\n      price: getPriceForQuantity(selectedProduct, selectedQuantity),\n      image: selectedProduct.image,\n      flavor: selectedVariant.flavor,\n      color: selectedVariant.color,\n    });\n\n    addNotification({\n      type: \"success\",\n      title: \"Produit ajouté !\",\n      message: `${selectedQuantity} ${selectedProduct.name} ajouté au panier !`,\n    });\n\n    if (selectedProduct.priceTiers && selectedProduct.priceTiers.length > 0) {\n      const sortedTiers = [...selectedProduct.priceTiers].sort(\n        (a, b) => a.quantity - b.quantity\n      );\n      setSelectedQuantity(sortedTiers[0].quantity);\n    }\n  };\n\n  if (!selectedProduct) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"text-center\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4\"></div>\n          <p className=\"text-gray-600\">Chargement du produit...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <main className=\"min-h-screen bg-gradient-to-br from-pink-50 via-white to-orange-50 pt-20\">\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <motion.div\n          className=\"mb-8\"\n          initial={fadeIn.initial}\n          animate={fadeIn.animate}\n        >\n          <Link\n            href=\"/\"\n            className=\"inline-flex items-center text-gray-600 hover:text-pink-500 transition-colors\"\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Retour à l'accueil\n          </Link>\n        </motion.div>\n\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n          <motion.div\n            className=\"space-y-6\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n          >\n            {/* Carrousel d'images de la variante sélectionnée */}\n            {selectedVariant &&\n            selectedVariant.images &&\n            selectedVariant.images.length > 0 ? (\n              <div className=\"space-y-4\">\n                {/* Image principale */}\n                <div className=\"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl\">\n                  <img\n                    src={selectedVariant.images[selectedImageIndex]}\n                    alt={`${selectedVariant.name} - Image ${\n                      selectedImageIndex + 1\n                    }`}\n                    className=\"w-full h-full object-cover\"\n                    onError={(e) => {\n                      e.currentTarget.src = \"/img/placeholder.svg\";\n                    }}\n                  />\n                  <div className=\"absolute top-4 right-4 space-y-2\">\n                    <div className=\"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                      🌿 THC\n                    </div>\n                    <div className=\"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                      18+\n                    </div>\n                  </div>\n                </div>\n\n                {/* Miniatures */}\n                {selectedVariant.images.length > 1 && (\n                  <div className=\"flex justify-center space-x-3\">\n                    {selectedVariant.images.map(\n                      (image: string, index: number) => (\n                        <button\n                          key={index}\n                          onClick={() => setSelectedImageIndex(index)}\n                          className={`relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ${\n                            selectedImageIndex === index\n                              ? \"border-pink-500 ring-2 ring-pink-200\"\n                              : \"border-gray-200 hover:border-gray-300\"\n                          }`}\n                        >\n                          <img\n                            src={image}\n                            alt={`${selectedVariant.name} - Miniature ${\n                              index + 1\n                            }`}\n                            className=\"w-full h-full object-cover\"\n                            onError={(e) => {\n                              e.currentTarget.src = \"/img/placeholder.svg\";\n                            }}\n                          />\n                        </button>\n                      )\n                    )}\n                  </div>\n                )}\n\n                {/* Informations produit pour combler l'espace */}\n                <div className=\"space-y-4 mt-6\">\n                  {/* Caractéristiques principales */}\n                  <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4\">\n                    <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n                      Caractéristiques\n                    </h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">THC</span>\n                        <span className=\"font-medium text-green-600\">\n                          &lt; 0,3%\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Delta-9 THC</span>\n                        <span className=\"font-medium text-blue-600\">\n                          &lt; 0,3%\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Origine</span>\n                        <span className=\"font-medium\">UE</span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Certification</span>\n                        <span className=\"font-medium text-green-600\">Bio</span>\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Avantages */}\n                  <div className=\"bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-4\">\n                    <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n                      <span className=\"w-2 h-2 bg-pink-500 rounded-full mr-2\"></span>\n                      Avantages Deltagum\n                    </h4>\n                    <div className=\"space-y-2 text-sm text-gray-600\">\n                      <div className=\"flex items-center\">\n                        <span className=\"text-green-500 mr-2\">✓</span>\n                        Goût naturel et authentique\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className=\"text-green-500 mr-2\">✓</span>\n                        Dosage précis et constant\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className=\"text-green-500 mr-2\">✓</span>\n                        Texture fondante unique\n                      </div>\n                      <div className=\"flex items-center\">\n                        <span className=\"text-green-500 mr-2\">✓</span>\n                        Emballage discret et pratique\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              // Fallback si pas d'images de variante\n              <div className=\"space-y-6\">\n                <div className=\"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl\">\n                  <img\n                    src={selectedProduct.image || \"/img/placeholder.svg\"}\n                    alt={selectedProduct.name}\n                    className=\"w-full h-full object-cover\"\n                    onError={(e) => {\n                      e.currentTarget.src = \"/img/placeholder.svg\";\n                    }}\n                  />\n                  <div className=\"absolute top-4 right-4 space-y-2\">\n                    <div className=\"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                      🌿 CBD\n                    </div>\n                    <div className=\"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full\">\n                      18+\n                    </div>\n                  </div>\n                </div>\n\n                {/* Informations produit pour le fallback aussi */}\n                <div className=\"space-y-4\">\n                  <div className=\"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4\">\n                    <h4 className=\"font-semibold text-gray-800 mb-3 flex items-center\">\n                      <span className=\"w-2 h-2 bg-green-500 rounded-full mr-2\"></span>\n                      Caractéristiques\n                    </h4>\n                    <div className=\"space-y-2 text-sm\">\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">THC</span>\n                        <span className=\"font-medium text-green-600\">\n                          &lt; 0,3%\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">CBD</span>\n                        <span className=\"font-medium text-blue-600\">\n                          Premium\n                        </span>\n                      </div>\n                      <div className=\"flex justify-between\">\n                        <span className=\"text-gray-600\">Origine</span>\n                        <span className=\"font-medium\">UE</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </motion.div>\n\n          <motion.div\n            className=\"space-y-8\"\n            initial={{ opacity: 0, x: 50 }}\n            animate={{ opacity: 1, x: 0 }}\n            transition={{ delay: 0.2 }}\n          >\n            <div>\n              <h1 className=\"text-4xl font-bold text-gray-900 mb-4\">\n                {selectedProduct.name}\n              </h1>\n              <p className=\"text-lg text-gray-600 leading-relaxed\">\n                {selectedProduct.description}\n              </p>\n              {selectedProduct.dosage && (\n                <div className=\"mt-4 inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium\">\n                  💊 {selectedProduct.dosage} par unité\n                </div>\n              )}\n            </div>\n\n            <div className=\"text-center lg:text-left\">\n              <div className=\"flex items-center justify-center lg:justify-start space-x-3\">\n                <span className=\"text-4xl font-bold text-pink-600\">\n                  {formatPrice(\n                    getPriceForQuantity(selectedProduct, selectedQuantity)\n                  )}\n                </span>\n              </div>\n            </div>\n\n            {selectedProduct.variants &&\n              selectedProduct.variants.length > 0 && (\n                <FlavorSelector\n                  variants={selectedProduct.variants.map((variant: any) => ({\n                    ...variant,\n                    product: selectedProduct,\n                  }))}\n                  selectedVariant={selectedVariant}\n                  onVariantSelect={setSelectedVariant}\n                />\n              )}\n\n            {selectedProduct.priceTiers &&\n              selectedProduct.priceTiers.length > 0 && (\n                <QuantitySelector\n                  priceTiers={selectedProduct.priceTiers}\n                  selectedQuantity={selectedQuantity}\n                  onQuantityChange={handleQuantityChange}\n                  className=\"mb-6\"\n                />\n              )}\n\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"w-3 h-3 rounded-full bg-green-500\" />\n              <span className=\"text-sm text-gray-600\">En stock</span>\n            </div>\n\n            <Button\n              onClick={handleAddToCart}\n              disabled={!selectedVariant}\n              size=\"lg\"\n              className=\"w-full text-lg py-4\"\n            >\n              <ShoppingCart className=\"w-5 h-5 mr-2\" />\n              Ajouter au panier -{\" \"}\n              {formatPrice(\n                getPriceForQuantity(selectedProduct, selectedQuantity)\n              )}\n            </Button>\n\n            <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4\">\n              <h4 className=\"font-semibold text-yellow-800 mb-2\">\n                ⚠️ Informations importantes\n              </h4>\n              <ul className=\"text-sm text-yellow-700 space-y-1\">\n                <li>• Réservé aux adultes (18+)</li>\n                <li>• Ne pas conduire après consommation</li>\n                <li>• Interdit sous traitement médical</li>\n                <li>• THC &lt; 0,3% (conforme UE)</li>\n              </ul>\n            </div>\n          </motion.div>\n        </div>\n      </div>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAAA;AACA;AACA;AACA;AAZA;;;;;;;;;;;;;AAce,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,YAAY,OAAO,EAAE;IAE3B,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,kLAAA,CAAA,aAAU,AAAD;IAC7C,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,yKAAA,CAAA,UAAO,AAAD;IAC1B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,sIAAA,CAAA,mBAAgB,AAAD;IAE3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAc;IAElB,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS,MAAM,GAAG,KAAK,WAAW;YACpC,MAAM,UAAU,SAAS,IAAI,CAAC,CAAC,IAAM,EAAE,EAAE,KAAK;YAC9C,IAAI,SAAS;gBACX,mBAAmB;gBACnB,IAAI,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,GAAG,GAAG;oBACnD,mBAAmB,QAAQ,QAAQ,CAAC,EAAE;gBACxC;gBACA,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;oBACvD,MAAM,cAAc;2BAAI,QAAQ,UAAU;qBAAC,CAAC,IAAI,CAC9C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;oBAEnC,oBAAoB,WAAW,CAAC,EAAE,CAAC,QAAQ;gBAC7C;YACF;QACF;IACF,GAAG;QAAC;QAAU;KAAU;IAExB,4DAA4D;IAC5D,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,sBAAsB;IACxB,GAAG;QAAC;KAAgB;IAEpB,MAAM,sBAAsB,CAAC,SAAc;QACzC,IAAI,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,GAAG;YAC1D,OAAO,OAAO,QAAQ,SAAS,IAAI;QACrC;QAEA,MAAM,YAAY,QAAQ,UAAU,CAAC,IAAI,CACvC,CAAC,OAAS,KAAK,QAAQ,KAAK;QAE9B,OAAO,YACH,OAAO,UAAU,KAAK,IACtB,OAAO,QAAQ,SAAS,IAAI;IAClC;IAEA,MAAM,uBAAuB,CAAC;QAC5B,oBAAoB;IACtB;IAEA,MAAM,kBAAkB;QACtB,IAAI,CAAC,mBAAmB,CAAC,iBAAiB;QAE1C,QAAQ;YACN,WAAW,gBAAgB,EAAE;YAC7B,WAAW,gBAAgB,EAAE;YAC7B,UAAU;YACV,MAAM,gBAAgB,IAAI;YAC1B,OAAO,oBAAoB,iBAAiB;YAC5C,OAAO,gBAAgB,KAAK;YAC5B,QAAQ,gBAAgB,MAAM;YAC9B,OAAO,gBAAgB,KAAK;QAC9B;QAEA,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS,GAAG,iBAAiB,CAAC,EAAE,gBAAgB,IAAI,CAAC,mBAAmB,CAAC;QAC3E;QAEA,IAAI,gBAAgB,UAAU,IAAI,gBAAgB,UAAU,CAAC,MAAM,GAAG,GAAG;YACvE,MAAM,cAAc;mBAAI,gBAAgB,UAAU;aAAC,CAAC,IAAI,CACtD,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAEnC,oBAAoB,WAAW,CAAC,EAAE,CAAC,QAAQ;QAC7C;IACF;IAEA,IAAI,CAAC,iBAAiB;QACpB,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;;;;;kCACf,6VAAC;wBAAE,WAAU;kCAAgB;;;;;;;;;;;;;;;;;IAIrC;IAEA,qBACE,6VAAC;QAAK,WAAU;kBACd,cAAA,6VAAC;YAAI,WAAU;;8BACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,wHAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,SAAS,wHAAA,CAAA,SAAM,CAAC,OAAO;8BAEvB,cAAA,6VAAC,2QAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;;0CAEV,6VAAC,oSAAA,CAAA,YAAS;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;8BAK1C,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;sCAG3B,mBACD,gBAAgB,MAAM,IACtB,gBAAgB,MAAM,CAAC,MAAM,GAAG,kBAC9B,6VAAC;gCAAI,WAAU;;kDAEb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDACC,KAAK,gBAAgB,MAAM,CAAC,mBAAmB;gDAC/C,KAAK,GAAG,gBAAgB,IAAI,CAAC,SAAS,EACpC,qBAAqB,GACrB;gDACF,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,GAAG,GAAG;gDACxB;;;;;;0DAEF,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;kEAAmE;;;;;;kEAGlF,6VAAC;wDAAI,WAAU;kEAAiE;;;;;;;;;;;;;;;;;;oCAOnF,gBAAgB,MAAM,CAAC,MAAM,GAAG,mBAC/B,6VAAC;wCAAI,WAAU;kDACZ,gBAAgB,MAAM,CAAC,GAAG,CACzB,CAAC,OAAe,sBACd,6VAAC;gDAEC,SAAS,IAAM,sBAAsB;gDACrC,WAAW,CAAC,sEAAsE,EAChF,uBAAuB,QACnB,yCACA,yCACJ;0DAEF,cAAA,6VAAC;oDACC,KAAK;oDACL,KAAK,GAAG,gBAAgB,IAAI,CAAC,aAAa,EACxC,QAAQ,GACR;oDACF,WAAU;oDACV,SAAS,CAAC;wDACR,EAAE,aAAa,CAAC,GAAG,GAAG;oDACxB;;;;;;+CAhBG;;;;;;;;;;kDAyBf,6VAAC;wCAAI,WAAU;;0DAEb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAG,WAAU;;0EACZ,6VAAC;gEAAK,WAAU;;;;;;4DAAgD;;;;;;;kEAGlE,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6VAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;0EAI/C,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6VAAC;wEAAK,WAAU;kFAA4B;;;;;;;;;;;;0EAI9C,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6VAAC;wEAAK,WAAU;kFAAc;;;;;;;;;;;;0EAEhC,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAgB;;;;;;kFAChC,6VAAC;wEAAK,WAAU;kFAA6B;;;;;;;;;;;;;;;;;;;;;;;;0DAMnD,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAG,WAAU;;0EACZ,6VAAC;gEAAK,WAAU;;;;;;4DAA+C;;;;;;;kEAGjE,6VAAC;wDAAI,WAAU;;0EACb,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAsB;;;;;;oEAAQ;;;;;;;0EAGhD,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAsB;;;;;;oEAAQ;;;;;;;0EAGhD,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAsB;;;;;;oEAAQ;;;;;;;0EAGhD,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAK,WAAU;kFAAsB;;;;;;oEAAQ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uCAQxD,uCAAuC;0CACvC,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDACC,KAAK,gBAAgB,KAAK,IAAI;gDAC9B,KAAK,gBAAgB,IAAI;gDACzB,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,GAAG,GAAG;gDACxB;;;;;;0DAEF,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;kEAAmE;;;;;;kEAGlF,6VAAC;wDAAI,WAAU;kEAAiE;;;;;;;;;;;;;;;;;;kDAOpF,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAG,WAAU;;sEACZ,6VAAC;4DAAK,WAAU;;;;;;wDAAgD;;;;;;;8DAGlE,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6VAAC;oEAAK,WAAU;8EAA6B;;;;;;;;;;;;sEAI/C,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6VAAC;oEAAK,WAAU;8EAA4B;;;;;;;;;;;;sEAI9C,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEAAK,WAAU;8EAAgB;;;;;;8EAChC,6VAAC;oEAAK,WAAU;8EAAc;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAS5C,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,6VAAC;;sDACC,6VAAC;4CAAG,WAAU;sDACX,gBAAgB,IAAI;;;;;;sDAEvB,6VAAC;4CAAE,WAAU;sDACV,gBAAgB,WAAW;;;;;;wCAE7B,gBAAgB,MAAM,kBACrB,6VAAC;4CAAI,WAAU;;gDAAqG;gDAC9G,gBAAgB,MAAM;gDAAC;;;;;;;;;;;;;8CAKjC,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CAAK,WAAU;sDACb,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EACT,oBAAoB,iBAAiB;;;;;;;;;;;;;;;;gCAM5C,gBAAgB,QAAQ,IACvB,gBAAgB,QAAQ,CAAC,MAAM,GAAG,mBAChC,6VAAC,+IAAA,CAAA,iBAAc;oCACb,UAAU,gBAAgB,QAAQ,CAAC,GAAG,CAAC,CAAC,UAAiB,CAAC;4CACxD,GAAG,OAAO;4CACV,SAAS;wCACX,CAAC;oCACD,iBAAiB;oCACjB,iBAAiB;;;;;;gCAItB,gBAAgB,UAAU,IACzB,gBAAgB,UAAU,CAAC,MAAM,GAAG,mBAClC,6VAAC,iJAAA,CAAA,mBAAgB;oCACf,YAAY,gBAAgB,UAAU;oCACtC,kBAAkB;oCAClB,kBAAkB;oCAClB,WAAU;;;;;;8CAIhB,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAI,WAAU;;;;;;sDACf,6VAAC;4CAAK,WAAU;sDAAwB;;;;;;;;;;;;8CAG1C,6VAAC,kIAAA,CAAA,SAAM;oCACL,SAAS;oCACT,UAAU,CAAC;oCACX,MAAK;oCACL,WAAU;;sDAEV,6VAAC,0SAAA,CAAA,eAAY;4CAAC,WAAU;;;;;;wCAAiB;wCACrB;wCACnB,CAAA,GAAA,mHAAA,CAAA,cAAW,AAAD,EACT,oBAAoB,iBAAiB;;;;;;;8CAIzC,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;4CAAG,WAAU;sDAAqC;;;;;;sDAGnD,6VAAC;4CAAG,WAAU;;8DACZ,6VAAC;8DAAG;;;;;;8DACJ,6VAAC;8DAAG;;;;;;8DACJ,6VAAC;8DAAG;;;;;;8DACJ,6VAAC;8DAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpB", "debugId": null}}]}