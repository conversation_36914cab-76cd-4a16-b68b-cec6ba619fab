{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/OrderList.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, CardContent } from \"@/components/ui\";\nimport { motion } from \"framer-motion\";\nimport {\n  Calendar,\n  DollarSign,\n  Download,\n  Eye,\n  Package,\n  Search,\n  ShoppingCart,\n  User,\n} from \"lucide-react\";\nimport { useEffect, useState } from \"react\";\n\ninterface OrderItem {\n  id: string;\n  quantity: number;\n  price: number;\n  product: {\n    id: string;\n    name: string;\n    image: string;\n  };\n  variant: {\n    id: string;\n    flavor: string;\n  };\n}\n\ninterface Order {\n  id: string;\n  customerId: string;\n  status: string;\n  totalAmount: number;\n  createdAt: string;\n  updatedAt: string;\n  customer: {\n    id: string;\n    email: string;\n    firstName: string;\n    lastName: string;\n  };\n  items: OrderItem[];\n}\n\ninterface OrderListProps {\n  onViewOrder?: (order: Order) => void;\n}\n\nexport default function OrderList({ onViewOrder }: OrderListProps) {\n  const [orders, setOrders] = useState<Order[]>([]);\n  const [loading, setLoading] = useState(false);\n  const [statusFilter, setStatusFilter] = useState<string>(\"ALL\");\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [dateFilter, setDateFilter] = useState<string>(\"ALL\");\n\n  useEffect(() => {\n    fetchOrders();\n  }, []);\n\n  const fetchOrders = async () => {\n    try {\n      setLoading(true);\n      console.log(\"🔍 [ADMIN] Récupération de toutes les commandes...\");\n\n      const response = await fetch(\"/api/orders\");\n      const data = await response.json();\n\n      console.log(\"📥 [ADMIN] Réponse API orders:\", data);\n      console.log(\"📊 [ADMIN] Statut de la réponse:\", response.status);\n\n      if (data.success) {\n        const allOrders = data.data.orders || [];\n        console.log(\"✅ [ADMIN] Commandes récupérées:\", allOrders.length);\n        console.log(\"📋 [ADMIN] Détails des commandes:\", allOrders);\n        setOrders(allOrders);\n      } else {\n        console.error(\n          \"❌ [ADMIN] Erreur lors du chargement des commandes:\",\n          data.error\n        );\n      }\n    } catch (err) {\n      console.error(\"❌ [ADMIN] Erreur de connexion:\", err);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const formatDate = (dateString: string) => {\n    return new Date(dateString).toLocaleDateString(\"fr-FR\", {\n      day: \"numeric\",\n      month: \"short\",\n      year: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat(\"fr-FR\", {\n      style: \"currency\",\n      currency: \"EUR\",\n    }).format(amount);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case \"PENDING\":\n        return \"bg-yellow-100 text-yellow-800\";\n      case \"PAID\":\n        return \"bg-green-100 text-green-800\";\n      case \"SHIPPED\":\n        return \"bg-blue-100 text-blue-800\";\n      case \"DELIVERED\":\n        return \"bg-purple-100 text-purple-800\";\n      case \"CANCELLED\":\n        return \"bg-red-100 text-red-800\";\n      default:\n        return \"bg-gray-100 text-gray-800\";\n    }\n  };\n\n  const getStatusLabel = (status: string) => {\n    switch (status) {\n      case \"PENDING\":\n        return \"En attente\";\n      case \"PAID\":\n        return \"Payée\";\n      case \"SHIPPED\":\n        return \"Expédiée\";\n      case \"DELIVERED\":\n        return \"Livrée\";\n      case \"CANCELLED\":\n        return \"Annulée\";\n      default:\n        return status;\n    }\n  };\n\n  const filteredOrders = orders.filter((order) => {\n    const matchesSearch =\n      order.id.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      order.customer.firstName\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase()) ||\n      order.customer.lastName\n        .toLowerCase()\n        .includes(searchTerm.toLowerCase()) ||\n      order.customer.email.toLowerCase().includes(searchTerm.toLowerCase());\n\n    const matchesStatus =\n      statusFilter === \"ALL\" || order.status === statusFilter;\n\n    let matchesDate = true;\n    if (dateFilter !== \"ALL\") {\n      const orderDate = new Date(order.createdAt);\n      const now = new Date();\n\n      switch (dateFilter) {\n        case \"TODAY\":\n          matchesDate = orderDate.toDateString() === now.toDateString();\n          break;\n        case \"WEEK\":\n          const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n          matchesDate = orderDate >= weekAgo;\n          break;\n        case \"MONTH\":\n          const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n          matchesDate = orderDate >= monthAgo;\n          break;\n      }\n    }\n\n    return matchesSearch && matchesStatus && matchesDate;\n  });\n\n  if (loading) {\n    return (\n      <div className=\"space-y-6\">\n        <div className=\"flex justify-between items-center\">\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">\n              Gestion des Commandes\n            </h2>\n            <div className=\"h-4 bg-gray-200 rounded w-32 mt-2 animate-pulse\"></div>\n          </div>\n        </div>\n\n        <div className=\"space-y-4\">\n          {[...Array(5)].map((_, i) => (\n            <Card key={i} className=\"animate-pulse\">\n              <CardContent className=\"p-6\">\n                <div className=\"h-20 bg-gray-200 rounded\"></div>\n              </CardContent>\n            </Card>\n          ))}\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">\n            Gestion des Commandes\n          </h2>\n          <p className=\"text-black\">\n            {filteredOrders.length} commande(s) au total\n          </p>\n        </div>\n        <Button variant=\"outline\" size=\"sm\">\n          <Download className=\"w-4 h-4 mr-2\" />\n          Exporter\n        </Button>\n      </div>\n\n      {/* Filtres et Recherche */}\n      <div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5\" />\n          <input\n            type=\"text\"\n            placeholder=\"Rechercher une commande...\"\n            value={searchTerm}\n            onChange={(e) => setSearchTerm(e.target.value)}\n            className=\"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white\"\n          />\n        </div>\n\n        <select\n          value={statusFilter}\n          onChange={(e) => setStatusFilter(e.target.value)}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white\"\n        >\n          <option value=\"ALL\">Tous les statuts ({orders.length})</option>\n          <option value=\"PENDING\">\n            En attente ({orders.filter((o) => o.status === \"PENDING\").length})\n          </option>\n          <option value=\"PAID\">\n            Payées ({orders.filter((o) => o.status === \"PAID\").length})\n          </option>\n          <option value=\"SHIPPED\">\n            Expédiées ({orders.filter((o) => o.status === \"SHIPPED\").length})\n          </option>\n          <option value=\"DELIVERED\">\n            Livrées ({orders.filter((o) => o.status === \"DELIVERED\").length})\n          </option>\n          <option value=\"CANCELLED\">\n            Annulées ({orders.filter((o) => o.status === \"CANCELLED\").length})\n          </option>\n        </select>\n\n        <select\n          value={dateFilter}\n          onChange={(e) => setDateFilter(e.target.value)}\n          className=\"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white\"\n        >\n          <option value=\"ALL\">Toutes les dates</option>\n          <option value=\"TODAY\">Aujourd'hui</option>\n          <option value=\"WEEK\">Cette semaine</option>\n          <option value=\"MONTH\">Ce mois</option>\n        </select>\n\n        <Button onClick={fetchOrders} variant=\"outline\" size=\"sm\">\n          Actualiser\n        </Button>\n      </div>\n\n      {/* Liste des Commandes */}\n      {filteredOrders.length === 0 ? (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <ShoppingCart className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              {searchTerm || statusFilter !== \"ALL\" || dateFilter !== \"ALL\"\n                ? \"Aucune commande trouvée\"\n                : \"Aucune commande enregistrée\"}\n            </h3>\n            <p className=\"text-black\">\n              {searchTerm || statusFilter !== \"ALL\" || dateFilter !== \"ALL\"\n                ? \"Essayez de modifier vos filtres de recherche\"\n                : \"Les commandes apparaîtront ici après les premiers achats\"}\n            </p>\n          </CardContent>\n        </Card>\n      ) : (\n        <div className=\"space-y-4\">\n          {filteredOrders.map((order, index) => (\n            <motion.div\n              key={order.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.05 }}\n            >\n              <Card\n                className=\"hover:shadow-lg transition-shadow cursor-pointer\"\n                onClick={() => onViewOrder?.(order)}\n              >\n                <CardContent className=\"p-6\">\n                  <div className=\"flex items-center justify-between\">\n                    <div className=\"flex items-center space-x-4\">\n                      <div className=\"w-12 h-12 bg-gradient-to-r from-pink-500 to-orange-500 rounded-lg flex items-center justify-center text-white font-semibold\">\n                        <ShoppingCart className=\"w-6 h-6\" />\n                      </div>\n\n                      <div>\n                        <div className=\"flex items-center space-x-3\">\n                          <h3 className=\"text-lg font-semibold text-gray-900\">\n                            Commande #{order.id.slice(-8)}\n                          </h3>\n                          <span\n                            className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(\n                              order.status\n                            )}`}\n                          >\n                            {getStatusLabel(order.status)}\n                          </span>\n                        </div>\n\n                        <div className=\"flex items-center space-x-4 mt-1 text-sm text-gray-600\">\n                          <div className=\"flex items-center\">\n                            <User className=\"w-4 h-4 mr-1\" />\n                            <span>\n                              {order.customer.firstName}{\" \"}\n                              {order.customer.lastName}\n                            </span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Calendar className=\"w-4 h-4 mr-1\" />\n                            <span>{formatDate(order.createdAt)}</span>\n                          </div>\n                          <div className=\"flex items-center\">\n                            <Package className=\"w-4 h-4 mr-1\" />\n                            <span>{order.items.length} article(s)</span>\n                          </div>\n                        </div>\n                      </div>\n                    </div>\n\n                    <div className=\"text-right\">\n                      <div className=\"flex items-center text-2xl font-bold text-gray-900\">\n                        <DollarSign className=\"w-6 h-6 mr-1\" />\n                        {Number(order.totalAmount).toFixed(2)}€\n                      </div>\n                      <Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                        onClick={(e) => {\n                          e.stopPropagation();\n                          onViewOrder?.(order);\n                        }}\n                        className=\"mt-2\"\n                      >\n                        <Eye className=\"w-4 h-4 mr-2\" />\n                        Voir détails\n                      </Button>\n                    </div>\n                  </div>\n\n                  {/* Aperçu des articles */}\n                  <div className=\"mt-4 pt-4 border-t border-gray-100\">\n                    <div className=\"flex items-center space-x-2\">\n                      {order.items.slice(0, 3).map((item, itemIndex) => (\n                        <div\n                          key={itemIndex}\n                          className=\"flex items-center space-x-2 bg-gray-50 rounded-lg px-3 py-1\"\n                        >\n                          <span className=\"text-sm font-medium\">\n                            {item.quantity}x\n                          </span>\n                          <span className=\"text-sm text-gray-700\">\n                            {item.product.name}\n                          </span>\n                          {item.variant && (\n                            <span className=\"text-xs text-gray-500\">\n                              ({item.variant.flavor})\n                            </span>\n                          )}\n                          <span className=\"text-xs text-gray-400\">\n                            {formatCurrency(item.price)}\n                          </span>\n                        </div>\n                      ))}\n                      {order.items.length > 3 && (\n                        <span className=\"text-sm text-gray-500\">\n                          +{order.items.length - 3} autre(s)\n                        </span>\n                      )}\n                    </div>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAUA;AAdA;;;;;;AAmDe,SAAS,UAAU,EAAE,WAAW,EAAkB;IAC/D,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,QAAQ,GAAG,CAAC;YAEZ,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,QAAQ,GAAG,CAAC,kCAAkC;YAC9C,QAAQ,GAAG,CAAC,oCAAoC,SAAS,MAAM;YAE/D,IAAI,KAAK,OAAO,EAAE;gBAChB,MAAM,YAAY,KAAK,IAAI,CAAC,MAAM,IAAI,EAAE;gBACxC,QAAQ,GAAG,CAAC,mCAAmC,UAAU,MAAM;gBAC/D,QAAQ,GAAG,CAAC,qCAAqC;gBACjD,UAAU;YACZ,OAAO;gBACL,QAAQ,KAAK,CACX,sDACA,KAAK,KAAK;YAEd;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,kCAAkC;QAClD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,YAAY,kBAAkB,CAAC,SAAS;YACtD,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,iBAAiB,OAAO,MAAM,CAAC,CAAC;QACpC,MAAM,gBACJ,MAAM,EAAE,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,MAAM,QAAQ,CAAC,SAAS,CACrB,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW,OAClC,MAAM,QAAQ,CAAC,QAAQ,CACpB,WAAW,GACX,QAAQ,CAAC,WAAW,WAAW,OAClC,MAAM,QAAQ,CAAC,KAAK,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW;QAEpE,MAAM,gBACJ,iBAAiB,SAAS,MAAM,MAAM,KAAK;QAE7C,IAAI,cAAc;QAClB,IAAI,eAAe,OAAO;YACxB,MAAM,YAAY,IAAI,KAAK,MAAM,SAAS;YAC1C,MAAM,MAAM,IAAI;YAEhB,OAAQ;gBACN,KAAK;oBACH,cAAc,UAAU,YAAY,OAAO,IAAI,YAAY;oBAC3D;gBACF,KAAK;oBACH,MAAM,UAAU,IAAI,KAAK,IAAI,OAAO,KAAK,IAAI,KAAK,KAAK,KAAK;oBAC5D,cAAc,aAAa;oBAC3B;gBACF,KAAK;oBACH,MAAM,WAAW,IAAI,KAAK,IAAI,OAAO,KAAK,KAAK,KAAK,KAAK,KAAK;oBAC9D,cAAc,aAAa;oBAC3B;YACJ;QACF;QAEA,OAAO,iBAAiB,iBAAiB;IAC3C;IAEA,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;;8BACb,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6VAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;8BAInB,6VAAC;oBAAI,WAAU;8BACZ;2BAAI,MAAM;qBAAG,CAAC,GAAG,CAAC,CAAC,GAAG,kBACrB,6VAAC,gIAAA,CAAA,OAAI;4BAAS,WAAU;sCACtB,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;0CACrB,cAAA,6VAAC;oCAAI,WAAU;;;;;;;;;;;2BAFR;;;;;;;;;;;;;;;;IASrB;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAAmC;;;;;;0CAGjD,6VAAC;gCAAE,WAAU;;oCACV,eAAe,MAAM;oCAAC;;;;;;;;;;;;;kCAG3B,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,MAAK;;0CAC7B,6VAAC,8RAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAMzC,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,0RAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,6VAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,OAAO;gCACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;gCAC7C,WAAU;;;;;;;;;;;;kCAId,6VAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,gBAAgB,EAAE,MAAM,CAAC,KAAK;wBAC/C,WAAU;;0CAEV,6VAAC;gCAAO,OAAM;;oCAAM;oCAAmB,OAAO,MAAM;oCAAC;;;;;;;0CACrD,6VAAC;gCAAO,OAAM;;oCAAU;oCACT,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM;oCAAC;;;;;;;0CAEnE,6VAAC;gCAAO,OAAM;;oCAAO;oCACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,QAAQ,MAAM;oCAAC;;;;;;;0CAE5D,6VAAC;gCAAO,OAAM;;oCAAU;oCACV,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,WAAW,MAAM;oCAAC;;;;;;;0CAElE,6VAAC;gCAAO,OAAM;;oCAAY;oCACd,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM;oCAAC;;;;;;;0CAElE,6VAAC;gCAAO,OAAM;;oCAAY;oCACb,OAAO,MAAM,CAAC,CAAC,IAAM,EAAE,MAAM,KAAK,aAAa,MAAM;oCAAC;;;;;;;;;;;;;kCAIrE,6VAAC;wBACC,OAAO;wBACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wBAC7C,WAAU;;0CAEV,6VAAC;gCAAO,OAAM;0CAAM;;;;;;0CACpB,6VAAC;gCAAO,OAAM;0CAAQ;;;;;;0CACtB,6VAAC;gCAAO,OAAM;0CAAO;;;;;;0CACrB,6VAAC;gCAAO,OAAM;0CAAQ;;;;;;;;;;;;kCAGxB,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAa,SAAQ;wBAAU,MAAK;kCAAK;;;;;;;;;;;;YAM3D,eAAe,MAAM,KAAK,kBACzB,6VAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,0SAAA,CAAA,eAAY;4BAAC,WAAU;;;;;;sCACxB,6VAAC;4BAAG,WAAU;sCACX,cAAc,iBAAiB,SAAS,eAAe,QACpD,4BACA;;;;;;sCAEN,6VAAC;4BAAE,WAAU;sCACV,cAAc,iBAAiB,SAAS,eAAe,QACpD,iDACA;;;;;;;;;;;;;;;;qCAKV,6VAAC;gBAAI,WAAU;0BACZ,eAAe,GAAG,CAAC,CAAC,OAAO,sBAC1B,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAK;kCAElC,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BACH,WAAU;4BACV,SAAS,IAAM,cAAc;sCAE7B,cAAA,6VAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC,0SAAA,CAAA,eAAY;4DAAC,WAAU;;;;;;;;;;;kEAG1B,6VAAC;;0EACC,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAG,WAAU;;4EAAsC;4EACvC,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC;;;;;;;kFAE7B,6VAAC;wEACC,WAAW,CAAC,uDAAuD,EAAE,eACnE,MAAM,MAAM,GACX;kFAEF,eAAe,MAAM,MAAM;;;;;;;;;;;;0EAIhC,6VAAC;gEAAI,WAAU;;kFACb,6VAAC;wEAAI,WAAU;;0FACb,6VAAC,sRAAA,CAAA,OAAI;gFAAC,WAAU;;;;;;0FAChB,6VAAC;;oFACE,MAAM,QAAQ,CAAC,SAAS;oFAAE;oFAC1B,MAAM,QAAQ,CAAC,QAAQ;;;;;;;;;;;;;kFAG5B,6VAAC;wEAAI,WAAU;;0FACb,6VAAC,8RAAA,CAAA,WAAQ;gFAAC,WAAU;;;;;;0FACpB,6VAAC;0FAAM,WAAW,MAAM,SAAS;;;;;;;;;;;;kFAEnC,6VAAC;wEAAI,WAAU;;0FACb,6VAAC,4RAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;0FACnB,6VAAC;;oFAAM,MAAM,KAAK,CAAC,MAAM;oFAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAMlC,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,sSAAA,CAAA,aAAU;gEAAC,WAAU;;;;;;4DACrB,OAAO,MAAM,WAAW,EAAE,OAAO,CAAC;4DAAG;;;;;;;kEAExC,6VAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,CAAC;4DACR,EAAE,eAAe;4DACjB,cAAc;wDAChB;wDACA,WAAU;;0EAEV,6VAAC,oRAAA,CAAA,MAAG;gEAAC,WAAU;;;;;;4DAAiB;;;;;;;;;;;;;;;;;;;kDAOtC,6VAAC;wCAAI,WAAU;kDACb,cAAA,6VAAC;4CAAI,WAAU;;gDACZ,MAAM,KAAK,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,MAAM,0BAClC,6VAAC;wDAEC,WAAU;;0EAEV,6VAAC;gEAAK,WAAU;;oEACb,KAAK,QAAQ;oEAAC;;;;;;;0EAEjB,6VAAC;gEAAK,WAAU;0EACb,KAAK,OAAO,CAAC,IAAI;;;;;;4DAEnB,KAAK,OAAO,kBACX,6VAAC;gEAAK,WAAU;;oEAAwB;oEACpC,KAAK,OAAO,CAAC,MAAM;oEAAC;;;;;;;0EAG1B,6VAAC;gEAAK,WAAU;0EACb,eAAe,KAAK,KAAK;;;;;;;uDAfvB;;;;;gDAmBR,MAAM,KAAK,CAAC,MAAM,GAAG,mBACpB,6VAAC;oDAAK,WAAU;;wDAAwB;wDACpC,MAAM,KAAK,CAAC,MAAM,GAAG;wDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAhGhC,MAAM,EAAE;;;;;;;;;;;;;;;;AA6G3B", "debugId": null}}, {"offset": {"line": 801, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/ProductDetails.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui\";\nimport { Product, ProductVariant } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport { ArrowLeft, Edit, Package, Plus } from \"lucide-react\";\nimport Image from \"next/image\";\n\ninterface ProductDetailsProps {\n  product: Product;\n  onEdit: () => void;\n  onBack: () => void;\n  onAddVariant: () => void;\n}\n\nexport default function ProductDetails({ product, onEdit, onBack, onAddVariant }: ProductDetailsProps) {\n  return (\n    <motion.div\n      initial={{ opacity: 0, x: 20 }}\n      animate={{ opacity: 1, x: 0 }}\n      className=\"space-y-6\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={onBack}\n          >\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Retour\n          </Button>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">{product.name}</h2>\n            <p className=\"text-gray-600\">Détails du produit</p>\n          </div>\n        </div>\n        <Button\n          onClick={onEdit}\n          className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n        >\n          <Edit className=\"w-4 h-4 mr-2\" />\n          Modifier\n        </Button>\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-6\">\n        {/* Product Info */}\n        <div className=\"lg:col-span-2 space-y-6\">\n          {/* Basic Information */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Informations générales</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-4\">\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Nom</label>\n                  <p className=\"text-gray-900 font-medium\">{product.name}</p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Prix de base</label>\n                  <p className=\"text-gray-900 font-medium\">\n                    {product.price ? `${product.price}€` : 'Variable'}\n                  </p>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Statut</label>\n                  <span className={`inline-flex px-2 py-1 rounded-full text-xs font-medium ${\n                    product.active \n                      ? 'bg-green-100 text-green-800' \n                      : 'bg-red-100 text-red-800'\n                  }`}>\n                    {product.active ? 'Actif' : 'Inactif'}\n                  </span>\n                </div>\n                <div>\n                  <label className=\"text-sm font-medium text-gray-500\">Dosage</label>\n                  <p className=\"text-gray-900 font-medium\">\n                    {(product as any).dosage || 'Non spécifié'}\n                  </p>\n                </div>\n              </div>\n              <div>\n                <label className=\"text-sm font-medium text-gray-500\">Description</label>\n                <p className=\"text-gray-900 mt-1\">{product.description}</p>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Variants */}\n          <Card>\n            <CardHeader>\n              <div className=\"flex items-center justify-between\">\n                <CardTitle>Variantes ({product.variants?.length || 0})</CardTitle>\n                <Button\n                  size=\"sm\"\n                  onClick={onAddVariant}\n                  className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n                >\n                  <Plus className=\"w-4 h-4 mr-2\" />\n                  Ajouter une variante\n                </Button>\n              </div>\n            </CardHeader>\n            <CardContent>\n              {!product.variants || product.variants.length === 0 ? (\n                <div className=\"text-center py-8\">\n                  <Package className=\"w-12 h-12 text-gray-400 mx-auto mb-3\" />\n                  <p className=\"text-gray-600 mb-4\">Aucune variante créée</p>\n                  <Button\n                    size=\"sm\"\n                    onClick={onAddVariant}\n                    variant=\"outline\"\n                  >\n                    <Plus className=\"w-4 h-4 mr-2\" />\n                    Créer la première variante\n                  </Button>\n                </div>\n              ) : (\n                <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n                  {product.variants.map((variant) => (\n                    <div\n                      key={variant.id}\n                      className=\"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow\"\n                    >\n                      <div className=\"flex items-center space-x-3\">\n                        <div\n                          className=\"w-4 h-4 rounded-full border-2 border-gray-300\"\n                          style={{ backgroundColor: variant.color }}\n                        />\n                        <div className=\"flex-1\">\n                          <p className=\"font-medium text-gray-900 capitalize\">\n                            {variant.flavor}\n                          </p>\n                          <p className=\"text-sm text-gray-500\">\n                            Stock: {variant.stock} • SKU: {variant.sku}\n                          </p>\n                        </div>\n                      </div>\n                      {variant.images && variant.images.length > 0 && (\n                        <div className=\"mt-3 flex space-x-2\">\n                          {variant.images.slice(0, 3).map((image, index) => (\n                            <div key={index} className=\"relative w-12 h-12 rounded-md overflow-hidden\">\n                              <Image\n                                src={image}\n                                alt={`${variant.flavor} ${index + 1}`}\n                                fill\n                                className=\"object-cover\"\n                                onError={(e) => {\n                                  e.currentTarget.src = '/img/placeholder.svg';\n                                }}\n                              />\n                            </div>\n                          ))}\n                          {variant.images.length > 3 && (\n                            <div className=\"w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center\">\n                              <span className=\"text-xs text-gray-500\">\n                                +{variant.images.length - 3}\n                              </span>\n                            </div>\n                          )}\n                        </div>\n                      )}\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n\n          {/* Price Tiers */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Paliers de prix</CardTitle>\n            </CardHeader>\n            <CardContent>\n              {!product.priceTiers || product.priceTiers.length === 0 ? (\n                <div className=\"text-center py-6\">\n                  <p className=\"text-gray-600\">Aucun palier de prix configuré</p>\n                  <p className=\"text-sm text-gray-500 mt-1\">\n                    Les paliers permettent d'offrir des réductions pour les achats en quantité\n                  </p>\n                </div>\n              ) : (\n                <div className=\"space-y-3\">\n                  {product.priceTiers.map((tier, index) => (\n                    <div\n                      key={index}\n                      className=\"flex items-center justify-between p-3 bg-gray-50 rounded-lg\"\n                    >\n                      <span className=\"font-medium\">\n                        {tier.quantity} {tier.quantity === 1 ? 'unité' : 'unités'}\n                      </span>\n                      <span className=\"text-lg font-bold text-pink-600\">\n                        {tier.price}€\n                      </span>\n                    </div>\n                  ))}\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        </div>\n\n        {/* Product Image */}\n        <div className=\"space-y-6\">\n          <Card>\n            <CardHeader>\n              <CardTitle>Image principale</CardTitle>\n            </CardHeader>\n            <CardContent>\n              <div className=\"relative aspect-square rounded-lg overflow-hidden bg-gray-100\">\n                <Image\n                  src={product.image || '/img/placeholder.svg'}\n                  alt={product.name}\n                  fill\n                  className=\"object-cover\"\n                  onError={(e) => {\n                    e.currentTarget.src = '/img/placeholder.svg';\n                  }}\n                />\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* Quick Stats */}\n          <Card>\n            <CardHeader>\n              <CardTitle>Statistiques</CardTitle>\n            </CardHeader>\n            <CardContent className=\"space-y-3\">\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Variantes</span>\n                <span className=\"font-medium\">{product.variants?.length || 0}</span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Stock total</span>\n                <span className=\"font-medium\">\n                  {product.variants?.reduce((total, variant) => total + variant.stock, 0) || 0}\n                </span>\n              </div>\n              <div className=\"flex justify-between\">\n                <span className=\"text-gray-600\">Paliers de prix</span>\n                <span className=\"font-medium\">{product.priceTiers?.length || 0}</span>\n              </div>\n            </CardContent>\n          </Card>\n        </div>\n      </div>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AACA;AANA;;;;;;AAee,SAAS,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,MAAM,EAAE,YAAY,EAAuB;IACnG,qBACE,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAGV,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;;kDAET,6VAAC,oSAAA,CAAA,YAAS;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGxC,6VAAC;;kDACC,6VAAC;wCAAG,WAAU;kDAAoC,QAAQ,IAAI;;;;;;kDAC9D,6VAAC;wCAAE,WAAU;kDAAgB;;;;;;;;;;;;;;;;;;kCAGjC,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,6VAAC,+RAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;0BAKrC,6VAAC;gBAAI,WAAU;;kCAEb,6VAAC;wBAAI,WAAU;;0CAEb,6VAAC,gIAAA,CAAA,OAAI;;kDACH,6VAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6VAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;;0EACC,6VAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6VAAC;gEAAE,WAAU;0EAA6B,QAAQ,IAAI;;;;;;;;;;;;kEAExD,6VAAC;;0EACC,6VAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6VAAC;gEAAE,WAAU;0EACV,QAAQ,KAAK,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;kEAG3C,6VAAC;;0EACC,6VAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6VAAC;gEAAK,WAAW,CAAC,uDAAuD,EACvE,QAAQ,MAAM,GACV,gCACA,2BACJ;0EACC,QAAQ,MAAM,GAAG,UAAU;;;;;;;;;;;;kEAGhC,6VAAC;;0EACC,6VAAC;gEAAM,WAAU;0EAAoC;;;;;;0EACrD,6VAAC;gEAAE,WAAU;0EACV,AAAC,QAAgB,MAAM,IAAI;;;;;;;;;;;;;;;;;;0DAIlC,6VAAC;;kEACC,6VAAC;wDAAM,WAAU;kEAAoC;;;;;;kEACrD,6VAAC;wDAAE,WAAU;kEAAsB,QAAQ,WAAW;;;;;;;;;;;;;;;;;;;;;;;;0CAM5D,6VAAC,gIAAA,CAAA,OAAI;;kDACH,6VAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,gIAAA,CAAA,YAAS;;wDAAC;wDAAY,QAAQ,QAAQ,EAAE,UAAU;wDAAE;;;;;;;8DACrD,6VAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,6VAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;;;;;;;kDAKvC,6VAAC,gIAAA,CAAA,cAAW;kDACT,CAAC,QAAQ,QAAQ,IAAI,QAAQ,QAAQ,CAAC,MAAM,KAAK,kBAChD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,4RAAA,CAAA,UAAO;oDAAC,WAAU;;;;;;8DACnB,6VAAC;oDAAE,WAAU;8DAAqB;;;;;;8DAClC,6VAAC,kIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAS;oDACT,SAAQ;;sEAER,6VAAC,sRAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;;;;;;iEAKrC,6VAAC;4CAAI,WAAU;sDACZ,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,wBACrB,6VAAC;oDAEC,WAAU;;sEAEV,6VAAC;4DAAI,WAAU;;8EACb,6VAAC;oEACC,WAAU;oEACV,OAAO;wEAAE,iBAAiB,QAAQ,KAAK;oEAAC;;;;;;8EAE1C,6VAAC;oEAAI,WAAU;;sFACb,6VAAC;4EAAE,WAAU;sFACV,QAAQ,MAAM;;;;;;sFAEjB,6VAAC;4EAAE,WAAU;;gFAAwB;gFAC3B,QAAQ,KAAK;gFAAC;gFAAS,QAAQ,GAAG;;;;;;;;;;;;;;;;;;;wDAI/C,QAAQ,MAAM,IAAI,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACzC,6VAAC;4DAAI,WAAU;;gEACZ,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,sBACtC,6VAAC;wEAAgB,WAAU;kFACzB,cAAA,6VAAC,4OAAA,CAAA,UAAK;4EACJ,KAAK;4EACL,KAAK,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,QAAQ,GAAG;4EACrC,IAAI;4EACJ,WAAU;4EACV,SAAS,CAAC;gFACR,EAAE,aAAa,CAAC,GAAG,GAAG;4EACxB;;;;;;uEARM;;;;;gEAYX,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6VAAC;oEAAI,WAAU;8EACb,cAAA,6VAAC;wEAAK,WAAU;;4EAAwB;4EACpC,QAAQ,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;mDAnC/B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;0CAiD3B,6VAAC,gIAAA,CAAA,OAAI;;kDACH,6VAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6VAAC,gIAAA,CAAA,cAAW;kDACT,CAAC,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,KAAK,kBACpD,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAE,WAAU;8DAAgB;;;;;;8DAC7B,6VAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;iEAK5C,6VAAC;4CAAI,WAAU;sDACZ,QAAQ,UAAU,CAAC,GAAG,CAAC,CAAC,MAAM,sBAC7B,6VAAC;oDAEC,WAAU;;sEAEV,6VAAC;4DAAK,WAAU;;gEACb,KAAK,QAAQ;gEAAC;gEAAE,KAAK,QAAQ,KAAK,IAAI,UAAU;;;;;;;sEAEnD,6VAAC;4DAAK,WAAU;;gEACb,KAAK,KAAK;gEAAC;;;;;;;;mDAPT;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAkBnB,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,gIAAA,CAAA,OAAI;;kDACH,6VAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6VAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC,4OAAA,CAAA,UAAK;gDACJ,KAAK,QAAQ,KAAK,IAAI;gDACtB,KAAK,QAAQ,IAAI;gDACjB,IAAI;gDACJ,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,GAAG,GAAG;gDACxB;;;;;;;;;;;;;;;;;;;;;;0CAOR,6VAAC,gIAAA,CAAA,OAAI;;kDACH,6VAAC,gIAAA,CAAA,aAAU;kDACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sDAAC;;;;;;;;;;;kDAEb,6VAAC,gIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6VAAC;wDAAK,WAAU;kEAAe,QAAQ,QAAQ,EAAE,UAAU;;;;;;;;;;;;0DAE7D,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6VAAC;wDAAK,WAAU;kEACb,QAAQ,QAAQ,EAAE,OAAO,CAAC,OAAO,UAAY,QAAQ,QAAQ,KAAK,EAAE,MAAM;;;;;;;;;;;;0DAG/E,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;kEAChC,6VAAC;wDAAK,WAAU;kEAAe,QAAQ,UAAU,EAAE,UAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ7E", "debugId": null}}, {"offset": {"line": 1565, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/PricingTiers.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle, Input } from \"@/components/ui\";\nimport { motion } from \"framer-motion\";\nimport { \n  Plus, \n  Trash2, \n  Edit, \n  Save, \n  X,\n  DollarSign,\n  Package\n} from \"lucide-react\";\nimport { useState } from \"react\";\n\ninterface PricingTier {\n  id?: string;\n  quantity: number;\n  price: number;\n  discount?: number;\n}\n\ninterface PricingTiersProps {\n  tiers: PricingTier[];\n  onTiersChange: (tiers: PricingTier[]) => void;\n  currency?: string;\n}\n\nexport default function PricingTiers({ tiers, onTiersChange, currency = \"EUR\" }: PricingTiersProps) {\n  const [editingIndex, setEditingIndex] = useState<number | null>(null);\n  const [newTier, setNewTier] = useState<PricingTier>({\n    quantity: 1,\n    price: 0,\n    discount: 0\n  });\n\n  const handleAddTier = () => {\n    if (newTier.quantity > 0 && newTier.price > 0) {\n      const sortedTiers = [...tiers, { ...newTier, id: Date.now().toString() }]\n        .sort((a, b) => a.quantity - b.quantity);\n      onTiersChange(sortedTiers);\n      setNewTier({\n        quantity: 1,\n        price: 0,\n        discount: 0\n      });\n    }\n  };\n\n  const handleEditTier = (index: number, updatedTier: PricingTier) => {\n    const updatedTiers = tiers.map((tier, i) => \n      i === index ? updatedTier : tier\n    ).sort((a, b) => a.quantity - b.quantity);\n    onTiersChange(updatedTiers);\n    setEditingIndex(null);\n  };\n\n  const handleDeleteTier = (index: number) => {\n    const updatedTiers = tiers.filter((_, i) => i !== index);\n    onTiersChange(updatedTiers);\n  };\n\n  const calculateDiscount = (basePrice: number, tierPrice: number) => {\n    if (basePrice <= 0) return 0;\n    return Math.round(((basePrice - tierPrice) / basePrice) * 100);\n  };\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  };\n\n  const basePrice = tiers.find(t => t.quantity === 1)?.price || 0;\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h3 className=\"text-lg font-semibold text-gray-900\">Prix par palier</h3>\n          <p className=\"text-sm text-gray-600\">Définissez des prix dégressifs selon la quantité</p>\n        </div>\n        <span className=\"text-sm text-gray-600\">{tiers.length} palier(s)</span>\n      </div>\n\n      {/* Aperçu des économies */}\n      {tiers.length > 1 && basePrice > 0 && (\n        <Card className=\"bg-gradient-to-r from-green-50 to-blue-50 border-green-200\">\n          <CardContent className=\"p-4\">\n            <div className=\"flex items-center space-x-2 mb-3\">\n              <DollarSign className=\"w-5 h-5 text-green-600\" />\n              <h4 className=\"font-medium text-green-800\">Économies maximales</h4>\n            </div>\n            <div className=\"grid grid-cols-2 md:grid-cols-4 gap-4\">\n              {tiers.slice(1).map((tier, index) => {\n                const discount = calculateDiscount(basePrice, tier.price);\n                const savings = (basePrice - tier.price) * tier.quantity;\n                return (\n                  <div key={tier.id || index} className=\"text-center\">\n                    <p className=\"text-sm text-gray-600\">{tier.quantity} unités</p>\n                    <p className=\"font-semibold text-green-700\">-{discount}%</p>\n                    <p className=\"text-xs text-gray-500\">\n                      Économie: {formatCurrency(savings)}\n                    </p>\n                  </div>\n                );\n              })}\n            </div>\n          </CardContent>\n        </Card>\n      )}\n\n      {/* Liste des paliers existants */}\n      <div className=\"space-y-3\">\n        {tiers.map((tier, index) => (\n          <Card key={tier.id || index} className={tier.quantity === 1 ? \"border-blue-200 bg-blue-50\" : \"\"}>\n            <CardContent className=\"p-4\">\n              {editingIndex === index ? (\n                <EditTierForm\n                  tier={tier}\n                  onSave={(updatedTier) => handleEditTier(index, updatedTier)}\n                  onCancel={() => setEditingIndex(null)}\n                  currency={currency}\n                />\n              ) : (\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    <div className=\"flex items-center space-x-2\">\n                      <Package className=\"w-5 h-5 text-gray-400\" />\n                      <span className=\"font-medium text-gray-900\">\n                        {tier.quantity} {tier.quantity === 1 ? 'unité' : 'unités'}\n                      </span>\n                      {tier.quantity === 1 && (\n                        <span className=\"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full\">\n                          Prix de base\n                        </span>\n                      )}\n                    </div>\n                    <div className=\"text-right\">\n                      <p className=\"text-lg font-semibold text-gray-900\">\n                        {formatCurrency(tier.price)}\n                      </p>\n                      <p className=\"text-sm text-gray-600\">\n                        {formatCurrency(tier.price / tier.quantity)} / unité\n                      </p>\n                      {tier.quantity > 1 && basePrice > 0 && (\n                        <p className=\"text-sm text-green-600\">\n                          -{calculateDiscount(basePrice, tier.price / tier.quantity)}% par unité\n                        </p>\n                      )}\n                    </div>\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => setEditingIndex(index)}\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </Button>\n                    {tier.quantity !== 1 && (\n                      <Button\n                        size=\"sm\"\n                        variant=\"danger\"\n                        onClick={() => handleDeleteTier(index)}\n                      >\n                        <Trash2 className=\"w-4 h-4\" />\n                      </Button>\n                    )}\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Formulaire d'ajout de nouveau palier */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Ajouter un palier de prix</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Quantité\n              </label>\n              <Input\n                type=\"number\"\n                min=\"1\"\n                value={newTier.quantity}\n                onChange={(e) => setNewTier(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}\n                placeholder=\"Ex: 3\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Prix total ({currency})\n              </label>\n              <Input\n                type=\"number\"\n                min=\"0\"\n                step=\"0.01\"\n                value={newTier.price}\n                onChange={(e) => setNewTier(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}\n                placeholder=\"Ex: 15.00\"\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Prix unitaire\n              </label>\n              <div className=\"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700\">\n                {newTier.quantity > 0 && newTier.price > 0 \n                  ? formatCurrency(newTier.price / newTier.quantity)\n                  : formatCurrency(0)\n                }\n              </div>\n            </div>\n          </div>\n\n          {/* Aperçu de l'économie */}\n          {newTier.quantity > 1 && newTier.price > 0 && basePrice > 0 && (\n            <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"text-sm text-green-700\">\n                  Économie par rapport au prix unitaire de base:\n                </span>\n                <span className=\"font-semibold text-green-800\">\n                  -{calculateDiscount(basePrice, newTier.price / newTier.quantity)}%\n                </span>\n              </div>\n              <p className=\"text-xs text-green-600 mt-1\">\n                Le client économise {formatCurrency((basePrice * newTier.quantity) - newTier.price)} \n                en achetant {newTier.quantity} unités\n              </p>\n            </div>\n          )}\n\n          <Button\n            onClick={handleAddTier}\n            disabled={newTier.quantity <= 0 || newTier.price <= 0 || tiers.some(t => t.quantity === newTier.quantity)}\n            className=\"w-full\"\n          >\n            <Plus className=\"w-4 h-4 mr-2\" />\n            Ajouter le palier\n          </Button>\n\n          {tiers.some(t => t.quantity === newTier.quantity) && (\n            <p className=\"text-sm text-red-600 text-center\">\n              Un palier existe déjà pour cette quantité\n            </p>\n          )}\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\n// Composant pour éditer un palier existant\nfunction EditTierForm({ \n  tier, \n  onSave, \n  onCancel, \n  currency \n}: {\n  tier: PricingTier;\n  onSave: (tier: PricingTier) => void;\n  onCancel: () => void;\n  currency: string;\n}) {\n  const [editedTier, setEditedTier] = useState(tier);\n\n  const formatCurrency = (amount: number) => {\n    return new Intl.NumberFormat('fr-FR', {\n      style: 'currency',\n      currency: currency\n    }).format(amount);\n  };\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Quantité\n          </label>\n          <Input\n            type=\"number\"\n            min=\"1\"\n            value={editedTier.quantity}\n            onChange={(e) => setEditedTier(prev => ({ ...prev, quantity: parseInt(e.target.value) || 1 }))}\n            disabled={tier.quantity === 1} // Ne pas permettre de modifier le palier de base\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-1\">\n            Prix total\n          </label>\n          <Input\n            type=\"number\"\n            min=\"0\"\n            step=\"0.01\"\n            value={editedTier.price}\n            onChange={(e) => setEditedTier(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}\n          />\n        </div>\n      </div>\n\n      <div className=\"text-sm text-gray-600\">\n        Prix unitaire: {editedTier.quantity > 0 ? formatCurrency(editedTier.price / editedTier.quantity) : formatCurrency(0)}\n      </div>\n\n      <div className=\"flex space-x-2\">\n        <Button size=\"sm\" onClick={() => onSave(editedTier)}>\n          <Save className=\"w-4 h-4 mr-2\" />\n          Sauvegarder\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onCancel}>\n          <X className=\"w-4 h-4 mr-2\" />\n          Annuler\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAEA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AAbA;;;;;AA4Be,SAAS,aAAa,EAAE,KAAK,EAAE,aAAa,EAAE,WAAW,KAAK,EAAqB;IAChG,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAe;QAClD,UAAU;QACV,OAAO;QACP,UAAU;IACZ;IAEA,MAAM,gBAAgB;QACpB,IAAI,QAAQ,QAAQ,GAAG,KAAK,QAAQ,KAAK,GAAG,GAAG;YAC7C,MAAM,cAAc;mBAAI;gBAAO;oBAAE,GAAG,OAAO;oBAAE,IAAI,KAAK,GAAG,GAAG,QAAQ;gBAAG;aAAE,CACtE,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YACzC,cAAc;YACd,WAAW;gBACT,UAAU;gBACV,OAAO;gBACP,UAAU;YACZ;QACF;IACF;IAEA,MAAM,iBAAiB,CAAC,OAAe;QACrC,MAAM,eAAe,MAAM,GAAG,CAAC,CAAC,MAAM,IACpC,MAAM,QAAQ,cAAc,MAC5B,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;QACxC,cAAc;QACd,gBAAgB;IAClB;IAEA,MAAM,mBAAmB,CAAC;QACxB,MAAM,eAAe,MAAM,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QAClD,cAAc;IAChB;IAEA,MAAM,oBAAoB,CAAC,WAAmB;QAC5C,IAAI,aAAa,GAAG,OAAO;QAC3B,OAAO,KAAK,KAAK,CAAC,AAAC,CAAC,YAAY,SAAS,IAAI,YAAa;IAC5D;IAEA,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,MAAM,YAAY,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,IAAI,SAAS;IAE9D,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAAsC;;;;;;0CACpD,6VAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,6VAAC;wBAAK,WAAU;;4BAAyB,MAAM,MAAM;4BAAC;;;;;;;;;;;;;YAIvD,MAAM,MAAM,GAAG,KAAK,YAAY,mBAC/B,6VAAC,gIAAA,CAAA,OAAI;gBAAC,WAAU;0BACd,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC;4BAAI,WAAU;;8CACb,6VAAC,sSAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;8CACtB,6VAAC;oCAAG,WAAU;8CAA6B;;;;;;;;;;;;sCAE7C,6VAAC;4BAAI,WAAU;sCACZ,MAAM,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM;gCACzB,MAAM,WAAW,kBAAkB,WAAW,KAAK,KAAK;gCACxD,MAAM,UAAU,CAAC,YAAY,KAAK,KAAK,IAAI,KAAK,QAAQ;gCACxD,qBACE,6VAAC;oCAA2B,WAAU;;sDACpC,6VAAC;4CAAE,WAAU;;gDAAyB,KAAK,QAAQ;gDAAC;;;;;;;sDACpD,6VAAC;4CAAE,WAAU;;gDAA+B;gDAAE;gDAAS;;;;;;;sDACvD,6VAAC;4CAAE,WAAU;;gDAAwB;gDACxB,eAAe;;;;;;;;mCAJpB,KAAK,EAAE,IAAI;;;;;4BAQzB;;;;;;;;;;;;;;;;;0BAOR,6VAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,MAAM,sBAChB,6VAAC,gIAAA,CAAA,OAAI;wBAAwB,WAAW,KAAK,QAAQ,KAAK,IAAI,+BAA+B;kCAC3F,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,iBAAiB,sBAChB,6VAAC;gCACC,MAAM;gCACN,QAAQ,CAAC,cAAgB,eAAe,OAAO;gCAC/C,UAAU,IAAM,gBAAgB;gCAChC,UAAU;;;;;qDAGZ,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAI,WAAU;;kEACb,6VAAC,4RAAA,CAAA,UAAO;wDAAC,WAAU;;;;;;kEACnB,6VAAC;wDAAK,WAAU;;4DACb,KAAK,QAAQ;4DAAC;4DAAE,KAAK,QAAQ,KAAK,IAAI,UAAU;;;;;;;oDAElD,KAAK,QAAQ,KAAK,mBACjB,6VAAC;wDAAK,WAAU;kEAA2D;;;;;;;;;;;;0DAK/E,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDAAE,WAAU;kEACV,eAAe,KAAK,KAAK;;;;;;kEAE5B,6VAAC;wDAAE,WAAU;;4DACV,eAAe,KAAK,KAAK,GAAG,KAAK,QAAQ;4DAAE;;;;;;;oDAE7C,KAAK,QAAQ,GAAG,KAAK,YAAY,mBAChC,6VAAC;wDAAE,WAAU;;4DAAyB;4DAClC,kBAAkB,WAAW,KAAK,KAAK,GAAG,KAAK,QAAQ;4DAAE;;;;;;;;;;;;;;;;;;;kDAKnE,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB;0DAE/B,cAAA,6VAAC,+RAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;4CAEjB,KAAK,QAAQ,KAAK,mBACjB,6VAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,iBAAiB;0DAEhC,cAAA,6VAAC,8RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAnDrB,KAAK,EAAE,IAAI;;;;;;;;;;0BA+D1B,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6VAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6VAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,KAAI;gDACJ,OAAO,QAAQ,QAAQ;gDACvB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAE,CAAC;gDACzF,aAAY;;;;;;;;;;;;kDAIhB,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;;oDAA+C;oDACjD;oDAAS;;;;;;;0DAExB,6VAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,KAAI;gDACJ,MAAK;gDACL,OAAO,QAAQ,KAAK;gDACpB,UAAU,CAAC,IAAM,WAAW,CAAA,OAAQ,CAAC;4DAAE,GAAG,IAAI;4DAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAAE,CAAC;gDACxF,aAAY;;;;;;;;;;;;kDAIhB,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6VAAC;gDAAI,WAAU;0DACZ,QAAQ,QAAQ,GAAG,KAAK,QAAQ,KAAK,GAAG,IACrC,eAAe,QAAQ,KAAK,GAAG,QAAQ,QAAQ,IAC/C,eAAe;;;;;;;;;;;;;;;;;;4BAOxB,QAAQ,QAAQ,GAAG,KAAK,QAAQ,KAAK,GAAG,KAAK,YAAY,mBACxD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDAAK,WAAU;0DAAyB;;;;;;0DAGzC,6VAAC;gDAAK,WAAU;;oDAA+B;oDAC3C,kBAAkB,WAAW,QAAQ,KAAK,GAAG,QAAQ,QAAQ;oDAAE;;;;;;;;;;;;;kDAGrE,6VAAC;wCAAE,WAAU;;4CAA8B;4CACpB,eAAe,AAAC,YAAY,QAAQ,QAAQ,GAAI,QAAQ,KAAK;4CAAE;4CACvE,QAAQ,QAAQ;4CAAC;;;;;;;;;;;;;0CAKpC,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,QAAQ,QAAQ,IAAI,KAAK,QAAQ,KAAK,IAAI,KAAK,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ;gCACxG,WAAU;;kDAEV,6VAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;4BAIlC,MAAM,IAAI,CAAC,CAAA,IAAK,EAAE,QAAQ,KAAK,QAAQ,QAAQ,mBAC9C,6VAAC;gCAAE,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;AAQ5D;AAEA,2CAA2C;AAC3C,SAAS,aAAa,EACpB,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,QAAQ,EAMT;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,iBAAiB,CAAC;QACtB,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;YACpC,OAAO;YACP,UAAU;QACZ,GAAG,MAAM,CAAC;IACZ;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6VAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,KAAI;gCACJ,OAAO,WAAW,QAAQ;gCAC1B,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,UAAU,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wCAAE,CAAC;gCAC5F,UAAU,KAAK,QAAQ,KAAK;;;;;;;;;;;;kCAIhC,6VAAC;;0CACC,6VAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,6VAAC,iIAAA,CAAA,QAAK;gCACJ,MAAK;gCACL,KAAI;gCACJ,MAAK;gCACL,OAAO,WAAW,KAAK;gCACvB,UAAU,CAAC,IAAM,cAAc,CAAA,OAAQ,CAAC;4CAAE,GAAG,IAAI;4CAAE,OAAO,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wCAAE,CAAC;;;;;;;;;;;;;;;;;;0BAKjG,6VAAC;gBAAI,WAAU;;oBAAwB;oBACrB,WAAW,QAAQ,GAAG,IAAI,eAAe,WAAW,KAAK,GAAG,WAAW,QAAQ,IAAI,eAAe;;;;;;;0BAGpH,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS,IAAM,OAAO;;0CACtC,6VAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGnC,6VAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;;0CAC3C,6VAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 2301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/VariantManager.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  Card<PERSON>ontent,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CardTitle,\n  Input,\n} from \"@/components/ui\";\nimport { Download, Edit, Plus, Save, Trash2, Upload, X } from \"lucide-react\";\nimport Image from \"next/image\";\nimport { useState } from \"react\";\n\ninterface ProductVariant {\n  id?: string;\n  flavor: string;\n  color: string;\n  stock: number;\n  sku: string;\n  images: string[];\n}\n\ninterface VariantManagerProps {\n  variants: ProductVariant[];\n  onVariantsChange: (variants: ProductVariant[]) => void;\n  productId?: string;\n}\n\nexport default function VariantManager({\n  variants,\n  onVariantsChange,\n  productId,\n}: VariantManagerProps) {\n  const [editingIndex, setEditingIndex] = useState<number | null>(null);\n  const [selectedVariants, setSelectedVariants] = useState<string[]>([]);\n  const [newVariant, setNewVariant] = useState<ProductVariant>({\n    flavor: \"\",\n    color: \"#ff6b9d\",\n    stock: 0,\n    sku: \"\", // Sera généré automatiquement\n    images: [],\n  });\n\n  const flavors = [\n    { name: \"Fraise\", color: \"#ff6b9d\" },\n    { name: \"Myrt<PERSON>\", color: \"#4dabf7\" },\n    { name: \"Pomme\", color: \"#51cf66\" },\n    { name: \"Orange\", color: \"#ff922b\" },\n    { name: \"Citron\", color: \"#ffd43b\" },\n    { name: \"Raisin\", color: \"#9775fa\" },\n  ];\n\n  // Fonction pour générer un SKU automatiquement\n  const generateSKU = (flavor: string) => {\n    if (!flavor) return \"\";\n\n    // Convertir en majuscules et remplacer les espaces par des tirets\n    const flavorCode = flavor.toUpperCase().replace(/\\s+/g, \"-\");\n\n    // Ajouter un identifiant unique basé sur la date\n    const uniqueId = Math.floor(Math.random() * 1000)\n      .toString()\n      .padStart(3, \"0\");\n\n    return `DELTA-${flavorCode}-${uniqueId}`;\n  };\n\n  const handleAddVariant = async () => {\n    if (newVariant.flavor) {\n      const generatedSKU = generateSKU(newVariant.flavor);\n      const variantToAdd = {\n        ...newVariant,\n        sku: generatedSKU,\n      };\n\n      if (productId) {\n        // Créer via API si on a un productId\n        try {\n          const response = await fetch(`/api/products/${productId}/variants`, {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(variantToAdd),\n          });\n\n          if (!response.ok) {\n            const data = await response.json();\n            alert(data.error || \"Erreur lors de la création\");\n            return;\n          }\n\n          const data = await response.json();\n          const updatedVariants = [...variants, data.data];\n          onVariantsChange(updatedVariants);\n        } catch (error) {\n          alert(\"Erreur de connexion\");\n          return;\n        }\n      } else {\n        // Ajouter localement pour les nouveaux produits\n        const updatedVariants = [\n          ...variants,\n          {\n            ...variantToAdd,\n            id: Date.now().toString(),\n          },\n        ];\n        onVariantsChange(updatedVariants);\n      }\n\n      setNewVariant({\n        flavor: \"\",\n        color: \"#ff6b9d\",\n        stock: 0,\n        sku: \"\",\n        images: [],\n      });\n    }\n  };\n\n  const handleEditVariant = async (\n    index: number,\n    updatedVariant: ProductVariant\n  ) => {\n    if (updatedVariant.id && productId) {\n      // Mettre à jour via API si la variante existe en base\n      try {\n        const response = await fetch(\n          `/api/products/${productId}/variants/${updatedVariant.id}`,\n          {\n            method: \"PUT\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(updatedVariant),\n          }\n        );\n\n        if (!response.ok) {\n          const data = await response.json();\n          alert(data.error || \"Erreur lors de la mise à jour\");\n          return;\n        }\n\n        const data = await response.json();\n        const updatedVariants = variants.map((variant, i) =>\n          i === index ? data.data : variant\n        );\n        onVariantsChange(updatedVariants);\n      } catch (error) {\n        alert(\"Erreur de connexion\");\n        return;\n      }\n    } else {\n      // Mettre à jour localement\n      const updatedVariants = variants.map((variant, i) =>\n        i === index ? updatedVariant : variant\n      );\n      onVariantsChange(updatedVariants);\n    }\n\n    setEditingIndex(null);\n  };\n\n  const handleDeleteVariant = async (index: number) => {\n    const variant = variants[index];\n\n    if (variant.id && productId) {\n      // Supprimer via API si la variante existe en base\n      try {\n        const response = await fetch(\n          `/api/products/${productId}/variants/${variant.id}`,\n          {\n            method: \"DELETE\",\n          }\n        );\n\n        if (!response.ok) {\n          const data = await response.json();\n          alert(data.error || \"Erreur lors de la suppression\");\n          return;\n        }\n      } catch (error) {\n        alert(\"Erreur de connexion\");\n        return;\n      }\n    }\n\n    // Supprimer localement\n    const updatedVariants = variants.filter((_, i) => i !== index);\n    onVariantsChange(updatedVariants);\n  };\n\n  const handleBulkDelete = async () => {\n    if (selectedVariants.length === 0) return;\n\n    if (\n      !confirm(\n        `Êtes-vous sûr de vouloir supprimer ${selectedVariants.length} variante(s) ?`\n      )\n    ) {\n      return;\n    }\n\n    if (productId) {\n      try {\n        const response = await fetch(\n          `/api/products/${productId}/variants/bulk`,\n          {\n            method: \"DELETE\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify({ variantIds: selectedVariants }),\n          }\n        );\n\n        if (!response.ok) {\n          const data = await response.json();\n          alert(data.error || \"Erreur lors de la suppression\");\n          return;\n        }\n      } catch (error) {\n        alert(\"Erreur de connexion\");\n        return;\n      }\n    }\n\n    // Supprimer localement\n    const updatedVariants = variants.filter(\n      (v) => !selectedVariants.includes(v.id || \"\")\n    );\n    onVariantsChange(updatedVariants);\n    setSelectedVariants([]);\n  };\n\n  const handleSelectVariant = (variantId: string) => {\n    setSelectedVariants((prev) =>\n      prev.includes(variantId)\n        ? prev.filter((id) => id !== variantId)\n        : [...prev, variantId]\n    );\n  };\n\n  const handleSelectAll = () => {\n    const allIds = variants.map((v) => v.id).filter(Boolean) as string[];\n    setSelectedVariants(\n      selectedVariants.length === allIds.length ? [] : allIds\n    );\n  };\n\n  const handleExportVariants = () => {\n    const dataStr = JSON.stringify(variants, null, 2);\n    const dataUri = `data:application/json;charset=utf-8,${encodeURIComponent(\n      dataStr\n    )}`;\n\n    const exportFileDefaultName = `variants-${new Date()\n      .toISOString()\n      .slice(0, 10)}.json`;\n\n    const linkElement = document.createElement(\"a\");\n    linkElement.setAttribute(\"href\", dataUri);\n    linkElement.setAttribute(\"download\", exportFileDefaultName);\n    linkElement.click();\n  };\n\n  const handleImageUpload = async (file: File, variantIndex?: number) => {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    try {\n      const response = await fetch(\"/api/upload\", {\n        method: \"POST\",\n        body: formData,\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        if (variantIndex !== undefined) {\n          // Ajouter l'image à un variant existant\n          const updatedVariants = variants.map((variant, i) =>\n            i === variantIndex\n              ? { ...variant, images: [...variant.images, data.url] }\n              : variant\n          );\n          onVariantsChange(updatedVariants);\n        } else {\n          // Ajouter l'image au nouveau variant\n          setNewVariant((prev) => ({\n            ...prev,\n            images: [...prev.images, data.url],\n          }));\n        }\n      }\n    } catch (error) {\n      alert(\"Erreur lors de l'upload de l'image\");\n    }\n  };\n\n  const removeImage = (variantIndex: number | null, imageIndex: number) => {\n    if (variantIndex === null) {\n      // Supprimer de newVariant\n      setNewVariant((prev) => ({\n        ...prev,\n        images: prev.images.filter((_, i) => i !== imageIndex),\n      }));\n    } else {\n      // Supprimer d'un variant existant\n      const updatedVariants = variants.map((variant, i) =>\n        i === variantIndex\n          ? {\n              ...variant,\n              images: variant.images.filter((_, imgI) => imgI !== imageIndex),\n            }\n          : variant\n      );\n      onVariantsChange(updatedVariants);\n    }\n  };\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"flex justify-between items-center\">\n        <h3 className=\"text-lg font-semibold text-gray-900\">\n          Variants du produit\n        </h3>\n        <div className=\"flex items-center space-x-4\">\n          {selectedVariants.length > 0 && (\n            <div className=\"flex items-center space-x-2\">\n              <span className=\"text-sm text-gray-600\">\n                {selectedVariants.length} sélectionné(s)\n              </span>\n              <Button\n                onClick={handleBulkDelete}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"text-red-600 hover:text-red-700\"\n              >\n                <Trash2 className=\"w-4 h-4 mr-1\" />\n                Supprimer\n              </Button>\n            </div>\n          )}\n          <span className=\"text-sm text-gray-600\">\n            {variants.length} variant(s)\n          </span>\n        </div>\n      </div>\n\n      {/* Actions en lot */}\n      {variants.length > 0 && (\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-2 text-sm\">\n            <Button onClick={handleSelectAll} variant=\"outline\" size=\"sm\">\n              {selectedVariants.length === variants.filter((v) => v.id).length\n                ? \"Désélectionner tout\"\n                : \"Sélectionner tout\"}\n            </Button>\n          </div>\n          <div className=\"flex items-center space-x-2\">\n            <Button onClick={handleExportVariants} variant=\"outline\" size=\"sm\">\n              <Download className=\"w-4 h-4 mr-1\" />\n              Exporter\n            </Button>\n          </div>\n        </div>\n      )}\n\n      {/* Liste des variants existants */}\n      <div className=\"space-y-4\">\n        {variants.map((variant, index) => (\n          <Card key={variant.id || index}>\n            <CardContent className=\"p-4\">\n              {editingIndex === index ? (\n                <EditVariantForm\n                  variant={variant}\n                  onSave={(updatedVariant) =>\n                    handleEditVariant(index, updatedVariant)\n                  }\n                  onCancel={() => setEditingIndex(null)}\n                  onImageUpload={(file) => handleImageUpload(file, index)}\n                  onRemoveImage={(imageIndex) => removeImage(index, imageIndex)}\n                />\n              ) : (\n                <div className=\"flex items-center justify-between\">\n                  <div className=\"flex items-center space-x-4\">\n                    {variant.id && (\n                      <input\n                        type=\"checkbox\"\n                        checked={selectedVariants.includes(variant.id)}\n                        onChange={() => handleSelectVariant(variant.id!)}\n                        className=\"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded\"\n                      />\n                    )}\n                    <div\n                      className=\"w-6 h-6 rounded-full border-2 border-gray-300\"\n                      style={{ backgroundColor: variant.color }}\n                    />\n                    <div>\n                      <h4 className=\"font-medium text-gray-900 capitalize\">\n                        {variant.flavor}\n                      </h4>\n                      <p className=\"text-sm text-gray-600\">\n                        Stock: {variant.stock} • SKU: {variant.sku}\n                      </p>\n                    </div>\n                    {variant.images.length > 0 && (\n                      <div className=\"flex space-x-1\">\n                        {variant.images.slice(0, 3).map((image, imgIndex) => (\n                          <div\n                            key={imgIndex}\n                            className=\"relative w-8 h-8 rounded overflow-hidden\"\n                          >\n                            <Image\n                              src={image}\n                              alt={`${variant.flavor} ${imgIndex + 1}`}\n                              fill\n                              className=\"object-cover\"\n                              onError={(e) => {\n                                e.currentTarget.src = \"/img/placeholder.svg\";\n                              }}\n                            />\n                          </div>\n                        ))}\n                        {variant.images.length > 3 && (\n                          <div className=\"w-8 h-8 rounded bg-gray-100 flex items-center justify-center\">\n                            <span className=\"text-xs text-gray-500\">\n                              +{variant.images.length - 3}\n                            </span>\n                          </div>\n                        )}\n                      </div>\n                    )}\n                  </div>\n                  <div className=\"flex items-center space-x-2\">\n                    <Button\n                      size=\"sm\"\n                      variant=\"outline\"\n                      onClick={() => setEditingIndex(index)}\n                    >\n                      <Edit className=\"w-4 h-4\" />\n                    </Button>\n                    <Button\n                      size=\"sm\"\n                      variant=\"danger\"\n                      onClick={() => handleDeleteVariant(index)}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </div>\n              )}\n            </CardContent>\n          </Card>\n        ))}\n      </div>\n\n      {/* Formulaire d'ajout de nouveau variant */}\n      <Card>\n        <CardHeader>\n          <CardTitle>Ajouter un variant</CardTitle>\n        </CardHeader>\n        <CardContent className=\"space-y-4\">\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Saveur\n              </label>\n              <select\n                value={newVariant.flavor}\n                onChange={(e) => {\n                  const selectedFlavor = flavors.find(\n                    (f) => f.name === e.target.value\n                  );\n                  setNewVariant((prev) => ({\n                    ...prev,\n                    flavor: e.target.value,\n                    color: selectedFlavor?.color || prev.color,\n                  }));\n                }}\n                className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-gray-900\"\n              >\n                <option value=\"\" className=\"text-gray-700\">\n                  Sélectionner une saveur\n                </option>\n                {flavors.map((flavor) => (\n                  <option key={flavor.name} value={flavor.name}>\n                    {flavor.name}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Stock\n              </label>\n              <Input\n                type=\"number\"\n                min=\"0\"\n                value={newVariant.stock}\n                onChange={(e) =>\n                  setNewVariant((prev) => ({\n                    ...prev,\n                    stock: parseInt(e.target.value) || 0,\n                  }))\n                }\n              />\n            </div>\n\n            <div>\n              <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                Couleur\n              </label>\n              <div className=\"flex items-center space-x-2\">\n                <input\n                  type=\"color\"\n                  value={newVariant.color}\n                  onChange={(e) =>\n                    setNewVariant((prev) => ({\n                      ...prev,\n                      color: e.target.value,\n                    }))\n                  }\n                  className=\"w-12 h-10 border border-gray-300 rounded cursor-pointer\"\n                />\n                <Input\n                  value={newVariant.color}\n                  onChange={(e) =>\n                    setNewVariant((prev) => ({\n                      ...prev,\n                      color: e.target.value,\n                    }))\n                  }\n                  placeholder=\"#ff6b9d\"\n                />\n              </div>\n            </div>\n          </div>\n\n          {/* Images du variant */}\n          <div>\n            <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n              Images du variant\n            </label>\n            <div className=\"flex items-center space-x-4\">\n              <input\n                type=\"file\"\n                accept=\"image/*\"\n                onChange={(e) => {\n                  const file = e.target.files?.[0];\n                  if (file) handleImageUpload(file);\n                }}\n                className=\"hidden\"\n                id=\"variant-image-upload\"\n              />\n              <label\n                htmlFor=\"variant-image-upload\"\n                className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\"\n              >\n                <Upload className=\"w-4 h-4 text-gray-700\" />\n                <span className=\"text-gray-900\">Ajouter une image</span>\n              </label>\n            </div>\n\n            {newVariant.images.length > 0 && (\n              <div className=\"flex space-x-2 mt-3\">\n                {newVariant.images.map((image, index) => (\n                  <div\n                    key={index}\n                    className=\"relative w-16 h-16 rounded-lg overflow-hidden\"\n                  >\n                    <Image\n                      src={image}\n                      alt={`Variant ${index + 1}`}\n                      fill\n                      className=\"object-cover\"\n                    />\n                    <button\n                      onClick={() => removeImage(null, index)}\n                      className=\"absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs\"\n                    >\n                      ×\n                    </button>\n                  </div>\n                ))}\n              </div>\n            )}\n          </div>\n\n          <Button\n            onClick={handleAddVariant}\n            disabled={!newVariant.flavor}\n            className=\"w-full\"\n          >\n            <Plus className=\"w-4 h-4 mr-2\" />\n            Ajouter le variant\n          </Button>\n        </CardContent>\n      </Card>\n    </div>\n  );\n}\n\n// Composant pour éditer un variant existant\nfunction EditVariantForm({\n  variant,\n  onSave,\n  onCancel,\n  onImageUpload,\n  onRemoveImage,\n}: {\n  variant: ProductVariant;\n  onSave: (variant: ProductVariant) => void;\n  onCancel: () => void;\n  onImageUpload: (file: File) => void;\n  onRemoveImage: (imageIndex: number) => void;\n}) {\n  const [editedVariant, setEditedVariant] = useState(variant);\n\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n        <Input\n          value={editedVariant.flavor}\n          onChange={(e) =>\n            setEditedVariant((prev) => ({ ...prev, flavor: e.target.value }))\n          }\n          placeholder=\"Saveur\"\n        />\n        <Input\n          value={editedVariant.sku}\n          onChange={(e) =>\n            setEditedVariant((prev) => ({ ...prev, sku: e.target.value }))\n          }\n          placeholder=\"SKU\"\n        />\n        <Input\n          type=\"number\"\n          value={editedVariant.stock}\n          onChange={(e) =>\n            setEditedVariant((prev) => ({\n              ...prev,\n              stock: parseInt(e.target.value) || 0,\n            }))\n          }\n          placeholder=\"Stock\"\n        />\n      </div>\n\n      {/* Images */}\n      {editedVariant.images.length > 0 && (\n        <div className=\"flex space-x-2\">\n          {editedVariant.images.map((image, index) => (\n            <div\n              key={index}\n              className=\"relative w-12 h-12 rounded overflow-hidden\"\n            >\n              <Image\n                src={image}\n                alt={`${editedVariant.flavor} ${index + 1}`}\n                fill\n                className=\"object-cover\"\n              />\n              <button\n                onClick={() => onRemoveImage(index)}\n                className=\"absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs\"\n              >\n                ×\n              </button>\n            </div>\n          ))}\n          <input\n            type=\"file\"\n            accept=\"image/*\"\n            onChange={(e) => {\n              const file = e.target.files?.[0];\n              if (file) onImageUpload(file);\n            }}\n            className=\"hidden\"\n            id={`edit-variant-image-${variant.id}`}\n          />\n          <label\n            htmlFor={`edit-variant-image-${variant.id}`}\n            className=\"w-12 h-12 border-2 border-dashed border-gray-300 rounded flex items-center justify-center cursor-pointer hover:border-gray-400\"\n          >\n            <Plus className=\"w-4 h-4 text-gray-400\" />\n          </label>\n        </div>\n      )}\n\n      <div className=\"flex space-x-2\">\n        <Button size=\"sm\" onClick={() => onSave(editedVariant)}>\n          <Save className=\"w-4 h-4 mr-2\" />\n          Sauvegarder\n        </Button>\n        <Button size=\"sm\" variant=\"outline\" onClick={onCancel}>\n          <X className=\"w-4 h-4 mr-2\" />\n          Annuler\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAQA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAZA;;;;;;AA6Be,SAAS,eAAe,EACrC,QAAQ,EACR,gBAAgB,EAChB,SAAS,EACW;IACpB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAChE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAkB;QAC3D,QAAQ;QACR,OAAO;QACP,OAAO;QACP,KAAK;QACL,QAAQ,EAAE;IACZ;IAEA,MAAM,UAAU;QACd;YAAE,MAAM;YAAU,OAAO;QAAU;QACnC;YAAE,MAAM;YAAY,OAAO;QAAU;QACrC;YAAE,MAAM;YAAS,OAAO;QAAU;QAClC;YAAE,MAAM;YAAU,OAAO;QAAU;QACnC;YAAE,MAAM;YAAU,OAAO;QAAU;QACnC;YAAE,MAAM;YAAU,OAAO;QAAU;KACpC;IAED,+CAA+C;IAC/C,MAAM,cAAc,CAAC;QACnB,IAAI,CAAC,QAAQ,OAAO;QAEpB,kEAAkE;QAClE,MAAM,aAAa,OAAO,WAAW,GAAG,OAAO,CAAC,QAAQ;QAExD,iDAAiD;QACjD,MAAM,WAAW,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MACzC,QAAQ,GACR,QAAQ,CAAC,GAAG;QAEf,OAAO,CAAC,MAAM,EAAE,WAAW,CAAC,EAAE,UAAU;IAC1C;IAEA,MAAM,mBAAmB;QACvB,IAAI,WAAW,MAAM,EAAE;YACrB,MAAM,eAAe,YAAY,WAAW,MAAM;YAClD,MAAM,eAAe;gBACnB,GAAG,UAAU;gBACb,KAAK;YACP;YAEA,IAAI,WAAW;gBACb,qCAAqC;gBACrC,IAAI;oBACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,UAAU,SAAS,CAAC,EAAE;wBAClE,QAAQ;wBACR,SAAS;4BACP,gBAAgB;wBAClB;wBACA,MAAM,KAAK,SAAS,CAAC;oBACvB;oBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;wBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;wBAChC,MAAM,KAAK,KAAK,IAAI;wBACpB;oBACF;oBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,kBAAkB;2BAAI;wBAAU,KAAK,IAAI;qBAAC;oBAChD,iBAAiB;gBACnB,EAAE,OAAO,OAAO;oBACd,MAAM;oBACN;gBACF;YACF,OAAO;gBACL,gDAAgD;gBAChD,MAAM,kBAAkB;uBACnB;oBACH;wBACE,GAAG,YAAY;wBACf,IAAI,KAAK,GAAG,GAAG,QAAQ;oBACzB;iBACD;gBACD,iBAAiB;YACnB;YAEA,cAAc;gBACZ,QAAQ;gBACR,OAAO;gBACP,OAAO;gBACP,KAAK;gBACL,QAAQ,EAAE;YACZ;QACF;IACF;IAEA,MAAM,oBAAoB,OACxB,OACA;QAEA,IAAI,eAAe,EAAE,IAAI,WAAW;YAClC,sDAAsD;YACtD,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,eAAe,EAAE,EAAE,EAC1D;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,KAAK,KAAK,IAAI;oBACpB;gBACF;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAChC,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAC,SAAS,IAC7C,MAAM,QAAQ,KAAK,IAAI,GAAG;gBAE5B,iBAAiB;YACnB,EAAE,OAAO,OAAO;gBACd,MAAM;gBACN;YACF;QACF,OAAO;YACL,2BAA2B;YAC3B,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAC,SAAS,IAC7C,MAAM,QAAQ,iBAAiB;YAEjC,iBAAiB;QACnB;QAEA,gBAAgB;IAClB;IAEA,MAAM,sBAAsB,OAAO;QACjC,MAAM,UAAU,QAAQ,CAAC,MAAM;QAE/B,IAAI,QAAQ,EAAE,IAAI,WAAW;YAC3B,kDAAkD;YAClD,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,cAAc,EAAE,UAAU,UAAU,EAAE,QAAQ,EAAE,EAAE,EACnD;oBACE,QAAQ;gBACV;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,KAAK,KAAK,IAAI;oBACpB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;gBACN;YACF;QACF;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,SAAS,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;QACxD,iBAAiB;IACnB;IAEA,MAAM,mBAAmB;QACvB,IAAI,iBAAiB,MAAM,KAAK,GAAG;QAEnC,IACE,CAAC,QACC,CAAC,mCAAmC,EAAE,iBAAiB,MAAM,CAAC,cAAc,CAAC,GAE/E;YACA;QACF;QAEA,IAAI,WAAW;YACb,IAAI;gBACF,MAAM,WAAW,MAAM,MACrB,CAAC,cAAc,EAAE,UAAU,cAAc,CAAC,EAC1C;oBACE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBAAE,YAAY;oBAAiB;gBACtD;gBAGF,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,MAAM,KAAK,KAAK,IAAI;oBACpB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,MAAM;gBACN;YACF;QACF;QAEA,uBAAuB;QACvB,MAAM,kBAAkB,SAAS,MAAM,CACrC,CAAC,IAAM,CAAC,iBAAiB,QAAQ,CAAC,EAAE,EAAE,IAAI;QAE5C,iBAAiB;QACjB,oBAAoB,EAAE;IACxB;IAEA,MAAM,sBAAsB,CAAC;QAC3B,oBAAoB,CAAC,OACnB,KAAK,QAAQ,CAAC,aACV,KAAK,MAAM,CAAC,CAAC,KAAO,OAAO,aAC3B;mBAAI;gBAAM;aAAU;IAE5B;IAEA,MAAM,kBAAkB;QACtB,MAAM,SAAS,SAAS,GAAG,CAAC,CAAC,IAAM,EAAE,EAAE,EAAE,MAAM,CAAC;QAChD,oBACE,iBAAiB,MAAM,KAAK,OAAO,MAAM,GAAG,EAAE,GAAG;IAErD;IAEA,MAAM,uBAAuB;QAC3B,MAAM,UAAU,KAAK,SAAS,CAAC,UAAU,MAAM;QAC/C,MAAM,UAAU,CAAC,oCAAoC,EAAE,mBACrD,UACC;QAEH,MAAM,wBAAwB,CAAC,SAAS,EAAE,IAAI,OAC3C,WAAW,GACX,KAAK,CAAC,GAAG,IAAI,KAAK,CAAC;QAEtB,MAAM,cAAc,SAAS,aAAa,CAAC;QAC3C,YAAY,YAAY,CAAC,QAAQ;QACjC,YAAY,YAAY,CAAC,YAAY;QACrC,YAAY,KAAK;IACnB;IAEA,MAAM,oBAAoB,OAAO,MAAY;QAC3C,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,IAAI,iBAAiB,WAAW;oBAC9B,wCAAwC;oBACxC,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAC,SAAS,IAC7C,MAAM,eACF;4BAAE,GAAG,OAAO;4BAAE,QAAQ;mCAAI,QAAQ,MAAM;gCAAE,KAAK,GAAG;6BAAC;wBAAC,IACpD;oBAEN,iBAAiB;gBACnB,OAAO;oBACL,qCAAqC;oBACrC,cAAc,CAAC,OAAS,CAAC;4BACvB,GAAG,IAAI;4BACP,QAAQ;mCAAI,KAAK,MAAM;gCAAE,KAAK,GAAG;6BAAC;wBACpC,CAAC;gBACH;YACF;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,cAAc,CAAC,cAA6B;QAChD,IAAI,iBAAiB,MAAM;YACzB,0BAA0B;YAC1B,cAAc,CAAC,OAAS,CAAC;oBACvB,GAAG,IAAI;oBACP,QAAQ,KAAK,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,IAAM,MAAM;gBAC7C,CAAC;QACH,OAAO;YACL,kCAAkC;YAClC,MAAM,kBAAkB,SAAS,GAAG,CAAC,CAAC,SAAS,IAC7C,MAAM,eACF;oBACE,GAAG,OAAO;oBACV,QAAQ,QAAQ,MAAM,CAAC,MAAM,CAAC,CAAC,GAAG,OAAS,SAAS;gBACtD,IACA;YAEN,iBAAiB;QACnB;IACF;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,6VAAC;wBAAI,WAAU;;4BACZ,iBAAiB,MAAM,GAAG,mBACzB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAK,WAAU;;4CACb,iBAAiB,MAAM;4CAAC;;;;;;;kDAE3B,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6VAAC,8RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;;;;;;;0CAKzC,6VAAC;gCAAK,WAAU;;oCACb,SAAS,MAAM;oCAAC;;;;;;;;;;;;;;;;;;;YAMtB,SAAS,MAAM,GAAG,mBACjB,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAiB,SAAQ;4BAAU,MAAK;sCACtD,iBAAiB,MAAM,KAAK,SAAS,MAAM,CAAC,CAAC,IAAM,EAAE,EAAE,EAAE,MAAM,GAC5D,wBACA;;;;;;;;;;;kCAGR,6VAAC;wBAAI,WAAU;kCACb,cAAA,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAS;4BAAsB,SAAQ;4BAAU,MAAK;;8CAC5D,6VAAC,8RAAA,CAAA,WAAQ;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;;0BAQ7C,6VAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;sCACpB,iBAAiB,sBAChB,6VAAC;gCACC,SAAS;gCACT,QAAQ,CAAC,iBACP,kBAAkB,OAAO;gCAE3B,UAAU,IAAM,gBAAgB;gCAChC,eAAe,CAAC,OAAS,kBAAkB,MAAM;gCACjD,eAAe,CAAC,aAAe,YAAY,OAAO;;;;;qDAGpD,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;wCAAI,WAAU;;4CACZ,QAAQ,EAAE,kBACT,6VAAC;gDACC,MAAK;gDACL,SAAS,iBAAiB,QAAQ,CAAC,QAAQ,EAAE;gDAC7C,UAAU,IAAM,oBAAoB,QAAQ,EAAE;gDAC9C,WAAU;;;;;;0DAGd,6VAAC;gDACC,WAAU;gDACV,OAAO;oDAAE,iBAAiB,QAAQ,KAAK;gDAAC;;;;;;0DAE1C,6VAAC;;kEACC,6VAAC;wDAAG,WAAU;kEACX,QAAQ,MAAM;;;;;;kEAEjB,6VAAC;wDAAE,WAAU;;4DAAwB;4DAC3B,QAAQ,KAAK;4DAAC;4DAAS,QAAQ,GAAG;;;;;;;;;;;;;4CAG7C,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6VAAC;gDAAI,WAAU;;oDACZ,QAAQ,MAAM,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,OAAO,yBACtC,6VAAC;4DAEC,WAAU;sEAEV,cAAA,6VAAC,4OAAA,CAAA,UAAK;gEACJ,KAAK;gEACL,KAAK,GAAG,QAAQ,MAAM,CAAC,CAAC,EAAE,WAAW,GAAG;gEACxC,IAAI;gEACJ,WAAU;gEACV,SAAS,CAAC;oEACR,EAAE,aAAa,CAAC,GAAG,GAAG;gEACxB;;;;;;2DAVG;;;;;oDAcR,QAAQ,MAAM,CAAC,MAAM,GAAG,mBACvB,6VAAC;wDAAI,WAAU;kEACb,cAAA,6VAAC;4DAAK,WAAU;;gEAAwB;gEACpC,QAAQ,MAAM,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;kDAOtC,6VAAC;wCAAI,WAAU;;0DACb,6VAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,gBAAgB;0DAE/B,cAAA,6VAAC,+RAAA,CAAA,OAAI;oDAAC,WAAU;;;;;;;;;;;0DAElB,6VAAC,kIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,SAAS,IAAM,oBAAoB;0DAEnC,cAAA,6VAAC,8RAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA5EnB,QAAQ,EAAE,IAAI;;;;;;;;;;0BAuF7B,6VAAC,gIAAA,CAAA,OAAI;;kCACH,6VAAC,gIAAA,CAAA,aAAU;kCACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;sCAAC;;;;;;;;;;;kCAEb,6VAAC,gIAAA,CAAA,cAAW;wBAAC,WAAU;;0CACrB,6VAAC;gCAAI,WAAU;;kDACb,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6VAAC;gDACC,OAAO,WAAW,MAAM;gDACxB,UAAU,CAAC;oDACT,MAAM,iBAAiB,QAAQ,IAAI,CACjC,CAAC,IAAM,EAAE,IAAI,KAAK,EAAE,MAAM,CAAC,KAAK;oDAElC,cAAc,CAAC,OAAS,CAAC;4DACvB,GAAG,IAAI;4DACP,QAAQ,EAAE,MAAM,CAAC,KAAK;4DACtB,OAAO,gBAAgB,SAAS,KAAK,KAAK;wDAC5C,CAAC;gDACH;gDACA,WAAU;;kEAEV,6VAAC;wDAAO,OAAM;wDAAG,WAAU;kEAAgB;;;;;;oDAG1C,QAAQ,GAAG,CAAC,CAAC,uBACZ,6VAAC;4DAAyB,OAAO,OAAO,IAAI;sEACzC,OAAO,IAAI;2DADD,OAAO,IAAI;;;;;;;;;;;;;;;;;kDAO9B,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6VAAC,iIAAA,CAAA,QAAK;gDACJ,MAAK;gDACL,KAAI;gDACJ,OAAO,WAAW,KAAK;gDACvB,UAAU,CAAC,IACT,cAAc,CAAC,OAAS,CAAC;4DACvB,GAAG,IAAI;4DACP,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDACrC,CAAC;;;;;;;;;;;;kDAKP,6VAAC;;0DACC,6VAAC;gDAAM,WAAU;0DAA+C;;;;;;0DAGhE,6VAAC;gDAAI,WAAU;;kEACb,6VAAC;wDACC,MAAK;wDACL,OAAO,WAAW,KAAK;wDACvB,UAAU,CAAC,IACT,cAAc,CAAC,OAAS,CAAC;oEACvB,GAAG,IAAI;oEACP,OAAO,EAAE,MAAM,CAAC,KAAK;gEACvB,CAAC;wDAEH,WAAU;;;;;;kEAEZ,6VAAC,iIAAA,CAAA,QAAK;wDACJ,OAAO,WAAW,KAAK;wDACvB,UAAU,CAAC,IACT,cAAc,CAAC,OAAS,CAAC;oEACvB,GAAG,IAAI;oEACP,OAAO,EAAE,MAAM,CAAC,KAAK;gEACvB,CAAC;wDAEH,aAAY;;;;;;;;;;;;;;;;;;;;;;;;0CAOpB,6VAAC;;kDACC,6VAAC;wCAAM,WAAU;kDAA+C;;;;;;kDAGhE,6VAAC;wCAAI,WAAU;;0DACb,6VAAC;gDACC,MAAK;gDACL,QAAO;gDACP,UAAU,CAAC;oDACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;oDAChC,IAAI,MAAM,kBAAkB;gDAC9B;gDACA,WAAU;gDACV,IAAG;;;;;;0DAEL,6VAAC;gDACC,SAAQ;gDACR,WAAU;;kEAEV,6VAAC,0RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,6VAAC;wDAAK,WAAU;kEAAgB;;;;;;;;;;;;;;;;;;oCAInC,WAAW,MAAM,CAAC,MAAM,GAAG,mBAC1B,6VAAC;wCAAI,WAAU;kDACZ,WAAW,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC7B,6VAAC;gDAEC,WAAU;;kEAEV,6VAAC,4OAAA,CAAA,UAAK;wDACJ,KAAK;wDACL,KAAK,CAAC,QAAQ,EAAE,QAAQ,GAAG;wDAC3B,IAAI;wDACJ,WAAU;;;;;;kEAEZ,6VAAC;wDACC,SAAS,IAAM,YAAY,MAAM;wDACjC,WAAU;kEACX;;;;;;;+CAZI;;;;;;;;;;;;;;;;0CAqBf,6VAAC,kIAAA,CAAA,SAAM;gCACL,SAAS;gCACT,UAAU,CAAC,WAAW,MAAM;gCAC5B,WAAU;;kDAEV,6VAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;;;;;;;;;;;;;;;;;;;AAO7C;AAEA,4CAA4C;AAC5C,SAAS,gBAAgB,EACvB,OAAO,EACP,MAAM,EACN,QAAQ,EACR,aAAa,EACb,aAAa,EAOd;IACC,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,qBACE,6VAAC;QAAI,WAAU;;0BACb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,iIAAA,CAAA,QAAK;wBACJ,OAAO,cAAc,MAAM;wBAC3B,UAAU,CAAC,IACT,iBAAiB,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,QAAQ,EAAE,MAAM,CAAC,KAAK;gCAAC,CAAC;wBAEjE,aAAY;;;;;;kCAEd,6VAAC,iIAAA,CAAA,QAAK;wBACJ,OAAO,cAAc,GAAG;wBACxB,UAAU,CAAC,IACT,iBAAiB,CAAC,OAAS,CAAC;oCAAE,GAAG,IAAI;oCAAE,KAAK,EAAE,MAAM,CAAC,KAAK;gCAAC,CAAC;wBAE9D,aAAY;;;;;;kCAEd,6VAAC,iIAAA,CAAA,QAAK;wBACJ,MAAK;wBACL,OAAO,cAAc,KAAK;wBAC1B,UAAU,CAAC,IACT,iBAAiB,CAAC,OAAS,CAAC;oCAC1B,GAAG,IAAI;oCACP,OAAO,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;gCACrC,CAAC;wBAEH,aAAY;;;;;;;;;;;;YAKf,cAAc,MAAM,CAAC,MAAM,GAAG,mBAC7B,6VAAC;gBAAI,WAAU;;oBACZ,cAAc,MAAM,CAAC,GAAG,CAAC,CAAC,OAAO,sBAChC,6VAAC;4BAEC,WAAU;;8CAEV,6VAAC,4OAAA,CAAA,UAAK;oCACJ,KAAK;oCACL,KAAK,GAAG,cAAc,MAAM,CAAC,CAAC,EAAE,QAAQ,GAAG;oCAC3C,IAAI;oCACJ,WAAU;;;;;;8CAEZ,6VAAC;oCACC,SAAS,IAAM,cAAc;oCAC7B,WAAU;8CACX;;;;;;;2BAZI;;;;;kCAiBT,6VAAC;wBACC,MAAK;wBACL,QAAO;wBACP,UAAU,CAAC;4BACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;4BAChC,IAAI,MAAM,cAAc;wBAC1B;wBACA,WAAU;wBACV,IAAI,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE;;;;;;kCAExC,6VAAC;wBACC,SAAS,CAAC,mBAAmB,EAAE,QAAQ,EAAE,EAAE;wBAC3C,WAAU;kCAEV,cAAA,6VAAC,sRAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKtB,6VAAC;gBAAI,WAAU;;kCACb,6VAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAS,IAAM,OAAO;;0CACtC,6VAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;kCAGnC,6VAAC,kIAAA,CAAA,SAAM;wBAAC,MAAK;wBAAK,SAAQ;wBAAU,SAAS;;0CAC3C,6VAAC,gRAAA,CAAA,IAAC;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 3362, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/ProductFormAdvanced.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, CardContent, Input } from \"@/components/ui\";\nimport { Product } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport {\n  ArrowLeft,\n  Image as ImageIcon,\n  Package,\n  Save,\n  Upload,\n  X,\n} from \"lucide-react\";\nimport Image from \"next/image\";\nimport { useEffect, useState } from \"react\";\nimport PricingTiers from \"./PricingTiers\";\nimport VariantManager from \"./VariantManager\";\n\ninterface ProductVariant {\n  id?: string;\n  flavor: string;\n  color: string;\n  stock: number;\n  sku: string;\n  images: string[];\n}\n\ninterface PricingTier {\n  id?: string;\n  quantity: number;\n  price: number;\n  discount?: number;\n}\n\ninterface ProductFormAdvancedProps {\n  product?: Product | null;\n  onSave: (product: any) => void;\n  onCancel: () => void;\n}\n\nexport default function ProductFormAdvanced({\n  product,\n  onSave,\n  onCancel,\n}: ProductFormAdvancedProps) {\n  const [formData, setFormData] = useState({\n    name: \"\",\n    description: \"\",\n    dosage: \"\",\n    image: \"\",\n    active: true,\n  });\n\n  const [variants, setVariants] = useState<ProductVariant[]>([]);\n  const [pricingTiers, setPricingTiers] = useState<PricingTier[]>([\n    { quantity: 1, price: 8.0 }, // Prix de base par défaut\n  ]);\n  const [loading, setLoading] = useState(false);\n  const [activeTab, setActiveTab] = useState(\"general\");\n\n  useEffect(() => {\n    if (product) {\n      setFormData({\n        name: product.name || \"\",\n        description: product.description || \"\",\n        dosage: (product as any).dosage || \"\",\n        image: product.image || \"\",\n        active: product.active ?? true,\n      });\n\n      // Charger les variants existants\n      if (product.variants) {\n        setVariants(\n          product.variants.map((v) => ({\n            id: v.id,\n            flavor: v.flavor || \"\",\n            color: v.color || \"#ff6b9d\",\n            stock: v.stock || 0,\n            sku: v.sku || \"\",\n            images: v.images || [],\n          }))\n        );\n      }\n\n      // Charger les prix par palier existants\n      if (product.priceTiers) {\n        setPricingTiers(product.priceTiers);\n      }\n    }\n  }, [product]);\n\n  const handleImageUpload = async (file: File) => {\n    const formData = new FormData();\n    formData.append(\"file\", file);\n\n    try {\n      const response = await fetch(\"/api/upload\", {\n        method: \"POST\",\n        body: formData,\n      });\n\n      const data = await response.json();\n      if (data.success) {\n        setFormData((prev) => ({ ...prev, image: data.url }));\n      } else {\n        alert(\"Erreur lors de l'upload\");\n      }\n    } catch (error) {\n      alert(\"Erreur lors de l'upload\");\n    }\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n    setLoading(true);\n\n    try {\n      const productData = {\n        ...formData,\n        variants,\n        pricingTiers,\n        basePrice: pricingTiers.find((t) => t.quantity === 1)?.price || 0,\n        // Utiliser une image par défaut si aucune image n'est fournie\n        image:\n          formData.image ||\n          (typeof window !== \"undefined\"\n            ? `${window.location.origin}/img/placeholder.svg`\n            : \"/img/placeholder.svg\"),\n      };\n\n      await onSave(productData);\n    } catch (error) {\n      alert(\"Erreur lors de la sauvegarde\");\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const tabs = [\n    { id: \"general\", label: \"Informations générales\", icon: Package },\n    { id: \"variants\", label: \"Variants\", icon: Package },\n    { id: \"pricing\", label: \"Prix par palier\", icon: Package },\n  ];\n\n  return (\n    <motion.div\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      className=\"space-y-6\"\n    >\n      {/* Header */}\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center space-x-4\">\n          <Button variant=\"outline\" size=\"sm\" onClick={onCancel}>\n            <ArrowLeft className=\"w-4 h-4 mr-2\" />\n            Retour\n          </Button>\n          <div>\n            <h2 className=\"text-2xl font-bold text-gray-900\">\n              {product ? \"Modifier le produit\" : \"Nouveau produit\"}\n            </h2>\n            <p className=\"text-gray-600\">\n              {product\n                ? \"Modifiez les informations du produit\"\n                : \"Créez un nouveau produit avec variants et prix par palier\"}\n            </p>\n          </div>\n        </div>\n      </div>\n\n      <form onSubmit={handleSubmit} className=\"space-y-6\">\n        {/* Navigation par onglets */}\n        <Card>\n          <CardContent className=\"p-0\">\n            <div className=\"border-b border-gray-200\">\n              <nav className=\"flex space-x-8 px-6\">\n                {tabs.map((tab) => (\n                  <button\n                    key={tab.id}\n                    type=\"button\"\n                    onClick={() => setActiveTab(tab.id)}\n                    className={`py-4 px-1 border-b-2 font-medium text-sm ${\n                      activeTab === tab.id\n                        ? \"border-pink-500 text-pink-600\"\n                        : \"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300\"\n                    }`}\n                  >\n                    <tab.icon className=\"w-4 h-4 inline mr-2\" />\n                    {tab.label}\n                  </button>\n                ))}\n              </nav>\n            </div>\n\n            <div className=\"p-6\">\n              {/* Onglet Informations générales */}\n              {activeTab === \"general\" && (\n                <div className=\"space-y-6\">\n                  <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Nom du produit *\n                      </label>\n                      <Input\n                        value={formData.name}\n                        onChange={(e) =>\n                          setFormData((prev) => ({\n                            ...prev,\n                            name: e.target.value,\n                          }))\n                        }\n                        placeholder=\"Ex: Cookie Delta-9\"\n                        required\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Dosage\n                      </label>\n                      <Input\n                        value={formData.dosage}\n                        onChange={(e) =>\n                          setFormData((prev) => ({\n                            ...prev,\n                            dosage: e.target.value,\n                          }))\n                        }\n                        placeholder=\"Ex: 5mg THC\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Description\n                    </label>\n                    <textarea\n                      value={formData.description}\n                      onChange={(e) =>\n                        setFormData((prev) => ({\n                          ...prev,\n                          description: e.target.value,\n                        }))\n                      }\n                      rows={4}\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none text-gray-900 placeholder:text-gray-700\"\n                      placeholder=\"Description détaillée du produit...\"\n                      minLength={10}\n                    />\n                  </div>\n\n                  {/* Image principale */}\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Image principale\n                    </label>\n                    <div className=\"flex items-center space-x-4\">\n                      {formData.image ? (\n                        <div className=\"relative w-24 h-24 rounded-lg overflow-hidden\">\n                          <Image\n                            src={formData.image}\n                            alt=\"Produit\"\n                            fill\n                            className=\"object-cover\"\n                            onError={(e) => {\n                              e.currentTarget.src = \"/img/placeholder.svg\";\n                            }}\n                          />\n                          <button\n                            type=\"button\"\n                            onClick={() => {\n                              // Si c'est une image uploadée, supprimer le fichier\n                              if (\n                                formData.image &&\n                                formData.image.includes(\"/uploads/\")\n                              ) {\n                                const filename = formData.image\n                                  .split(\"/\")\n                                  .pop();\n                                if (filename) {\n                                  fetch(`/api/upload/${filename}`, {\n                                    method: \"DELETE\",\n                                  }).catch((err) =>\n                                    console.error(\n                                      \"Erreur lors de la suppression:\",\n                                      err\n                                    )\n                                  );\n                                }\n                              }\n                              setFormData((prev) => ({ ...prev, image: \"\" }));\n                            }}\n                            className=\"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm\"\n                          >\n                            ×\n                          </button>\n                        </div>\n                      ) : (\n                        <div className=\"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center\">\n                          <ImageIcon className=\"w-8 h-8 text-gray-400\" />\n                        </div>\n                      )}\n\n                      <div>\n                        <input\n                          type=\"file\"\n                          accept=\"image/*\"\n                          onChange={(e) => {\n                            const file = e.target.files?.[0];\n                            if (file) handleImageUpload(file);\n                          }}\n                          className=\"hidden\"\n                          id=\"main-image-upload\"\n                        />\n                        <label\n                          htmlFor=\"main-image-upload\"\n                          className=\"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50\"\n                        >\n                          <Upload className=\"w-4 h-4 text-gray-700\" />\n                          <span className=\"text-gray-900\">\n                            Choisir une image\n                          </span>\n                        </label>\n                        <p className=\"text-xs text-gray-700 mt-1\">\n                          JPG, PNG jusqu'à 5MB\n                        </p>\n                        <p className=\"text-xs text-green-600 mt-1\">\n                          Les images sont stockées dans /public/uploads\n                        </p>\n                      </div>\n                    </div>\n                  </div>\n\n                  <div className=\"flex items-center\">\n                    <input\n                      type=\"checkbox\"\n                      id=\"active\"\n                      checked={formData.active}\n                      onChange={(e) =>\n                        setFormData((prev) => ({\n                          ...prev,\n                          active: e.target.checked,\n                        }))\n                      }\n                      className=\"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded\"\n                    />\n                    <label\n                      htmlFor=\"active\"\n                      className=\"ml-2 block text-sm text-gray-900\"\n                    >\n                      Produit actif (visible sur le site)\n                    </label>\n                  </div>\n                </div>\n              )}\n\n              {/* Onglet Variants */}\n              {activeTab === \"variants\" && (\n                <VariantManager\n                  variants={variants}\n                  onVariantsChange={setVariants}\n                  productId={product?.id}\n                />\n              )}\n\n              {/* Onglet Prix par palier */}\n              {activeTab === \"pricing\" && (\n                <PricingTiers\n                  tiers={pricingTiers}\n                  onTiersChange={setPricingTiers}\n                  currency=\"EUR\"\n                />\n              )}\n            </div>\n          </CardContent>\n        </Card>\n\n        {/* Actions */}\n        <div className=\"flex justify-end space-x-4\">\n          <Button type=\"button\" variant=\"outline\" onClick={onCancel}>\n            <X className=\"w-4 h-4 mr-2\" />\n            Annuler\n          </Button>\n          <Button\n            type=\"submit\"\n            disabled={loading || !formData.name}\n            className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n          >\n            <Save className=\"w-4 h-4 mr-2\" />\n            {loading ? \"Sauvegarde...\" : \"Sauvegarder\"}\n          </Button>\n        </div>\n      </form>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AACA;AACA;AAhBA;;;;;;;;;AAwCe,SAAS,oBAAoB,EAC1C,OAAO,EACP,MAAM,EACN,QAAQ,EACiB;IACzB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,QAAQ;QACR,OAAO;QACP,QAAQ;IACV;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAoB,EAAE;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;QAC9D;YAAE,UAAU;YAAG,OAAO;QAAI;KAC3B;IACD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,SAAS;YACX,YAAY;gBACV,MAAM,QAAQ,IAAI,IAAI;gBACtB,aAAa,QAAQ,WAAW,IAAI;gBACpC,QAAQ,AAAC,QAAgB,MAAM,IAAI;gBACnC,OAAO,QAAQ,KAAK,IAAI;gBACxB,QAAQ,QAAQ,MAAM,IAAI;YAC5B;YAEA,iCAAiC;YACjC,IAAI,QAAQ,QAAQ,EAAE;gBACpB,YACE,QAAQ,QAAQ,CAAC,GAAG,CAAC,CAAC,IAAM,CAAC;wBAC3B,IAAI,EAAE,EAAE;wBACR,QAAQ,EAAE,MAAM,IAAI;wBACpB,OAAO,EAAE,KAAK,IAAI;wBAClB,OAAO,EAAE,KAAK,IAAI;wBAClB,KAAK,EAAE,GAAG,IAAI;wBACd,QAAQ,EAAE,MAAM,IAAI,EAAE;oBACxB,CAAC;YAEL;YAEA,wCAAwC;YACxC,IAAI,QAAQ,UAAU,EAAE;gBACtB,gBAAgB,QAAQ,UAAU;YACpC;QACF;IACF,GAAG;QAAC;KAAQ;IAEZ,MAAM,oBAAoB,OAAO;QAC/B,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,QAAQ;QAExB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,eAAe;gBAC1C,QAAQ;gBACR,MAAM;YACR;YAEA,MAAM,OAAO,MAAM,SAAS,IAAI;YAChC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,CAAC,OAAS,CAAC;wBAAE,GAAG,IAAI;wBAAE,OAAO,KAAK,GAAG;oBAAC,CAAC;YACrD,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAChB,WAAW;QAEX,IAAI;YACF,MAAM,cAAc;gBAClB,GAAG,QAAQ;gBACX;gBACA;gBACA,WAAW,aAAa,IAAI,CAAC,CAAC,IAAM,EAAE,QAAQ,KAAK,IAAI,SAAS;gBAChE,8DAA8D;gBAC9D,OACE,SAAS,KAAK,IACd,CAAC,6EAEG,sBAAsB;YAC9B;YAEA,MAAM,OAAO;QACf,EAAE,OAAO,OAAO;YACd,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,OAAO;QACX;YAAE,IAAI;YAAW,OAAO;YAA0B,MAAM,4RAAA,CAAA,UAAO;QAAC;QAChE;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,4RAAA,CAAA,UAAO;QAAC;QACnD;YAAE,IAAI;YAAW,OAAO;YAAmB,MAAM,4RAAA,CAAA,UAAO;QAAC;KAC1D;IAED,qBACE,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;QACT,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,WAAU;;0BAGV,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC,kIAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAU,MAAK;4BAAK,SAAS;;8CAC3C,6VAAC,oSAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;sCAGxC,6VAAC;;8CACC,6VAAC;oCAAG,WAAU;8CACX,UAAU,wBAAwB;;;;;;8CAErC,6VAAC;oCAAE,WAAU;8CACV,UACG,yCACA;;;;;;;;;;;;;;;;;;;;;;;0BAMZ,6VAAC;gBAAK,UAAU;gBAAc,WAAU;;kCAEtC,6VAAC,gIAAA,CAAA,OAAI;kCACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4BAAC,WAAU;;8CACrB,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCAAI,WAAU;kDACZ,KAAK,GAAG,CAAC,CAAC,oBACT,6VAAC;gDAEC,MAAK;gDACL,SAAS,IAAM,aAAa,IAAI,EAAE;gDAClC,WAAW,CAAC,yCAAyC,EACnD,cAAc,IAAI,EAAE,GAChB,kCACA,8EACJ;;kEAEF,6VAAC,IAAI,IAAI;wDAAC,WAAU;;;;;;oDACnB,IAAI,KAAK;;+CAVL,IAAI,EAAE;;;;;;;;;;;;;;;8CAgBnB,6VAAC;oCAAI,WAAU;;wCAEZ,cAAc,2BACb,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;;8EACC,6VAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6VAAC,iIAAA,CAAA,QAAK;oEACJ,OAAO,SAAS,IAAI;oEACpB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gFACrB,GAAG,IAAI;gFACP,MAAM,EAAE,MAAM,CAAC,KAAK;4EACtB,CAAC;oEAEH,aAAY;oEACZ,QAAQ;;;;;;;;;;;;sEAIZ,6VAAC;;8EACC,6VAAC;oEAAM,WAAU;8EAA+C;;;;;;8EAGhE,6VAAC,iIAAA,CAAA,QAAK;oEACJ,OAAO,SAAS,MAAM;oEACtB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;gFACrB,GAAG,IAAI;gFACP,QAAQ,EAAE,MAAM,CAAC,KAAK;4EACxB,CAAC;oEAEH,aAAY;;;;;;;;;;;;;;;;;;8DAKlB,6VAAC;;sEACC,6VAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6VAAC;4DACC,OAAO,SAAS,WAAW;4DAC3B,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;wEACrB,GAAG,IAAI;wEACP,aAAa,EAAE,MAAM,CAAC,KAAK;oEAC7B,CAAC;4DAEH,MAAM;4DACN,WAAU;4DACV,aAAY;4DACZ,WAAW;;;;;;;;;;;;8DAKf,6VAAC;;sEACC,6VAAC;4DAAM,WAAU;sEAA+C;;;;;;sEAGhE,6VAAC;4DAAI,WAAU;;gEACZ,SAAS,KAAK,iBACb,6VAAC;oEAAI,WAAU;;sFACb,6VAAC,4OAAA,CAAA,UAAK;4EACJ,KAAK,SAAS,KAAK;4EACnB,KAAI;4EACJ,IAAI;4EACJ,WAAU;4EACV,SAAS,CAAC;gFACR,EAAE,aAAa,CAAC,GAAG,GAAG;4EACxB;;;;;;sFAEF,6VAAC;4EACC,MAAK;4EACL,SAAS;gFACP,oDAAoD;gFACpD,IACE,SAAS,KAAK,IACd,SAAS,KAAK,CAAC,QAAQ,CAAC,cACxB;oFACA,MAAM,WAAW,SAAS,KAAK,CAC5B,KAAK,CAAC,KACN,GAAG;oFACN,IAAI,UAAU;wFACZ,MAAM,CAAC,YAAY,EAAE,UAAU,EAAE;4FAC/B,QAAQ;wFACV,GAAG,KAAK,CAAC,CAAC,MACR,QAAQ,KAAK,CACX,kCACA;oFAGN;gFACF;gFACA,YAAY,CAAC,OAAS,CAAC;wFAAE,GAAG,IAAI;wFAAE,OAAO;oFAAG,CAAC;4EAC/C;4EACA,WAAU;sFACX;;;;;;;;;;;yFAKH,6VAAC;oEAAI,WAAU;8EACb,cAAA,6VAAC,wRAAA,CAAA,QAAS;wEAAC,WAAU;;;;;;;;;;;8EAIzB,6VAAC;;sFACC,6VAAC;4EACC,MAAK;4EACL,QAAO;4EACP,UAAU,CAAC;gFACT,MAAM,OAAO,EAAE,MAAM,CAAC,KAAK,EAAE,CAAC,EAAE;gFAChC,IAAI,MAAM,kBAAkB;4EAC9B;4EACA,WAAU;4EACV,IAAG;;;;;;sFAEL,6VAAC;4EACC,SAAQ;4EACR,WAAU;;8FAEV,6VAAC,0RAAA,CAAA,SAAM;oFAAC,WAAU;;;;;;8FAClB,6VAAC;oFAAK,WAAU;8FAAgB;;;;;;;;;;;;sFAIlC,6VAAC;4EAAE,WAAU;sFAA6B;;;;;;sFAG1C,6VAAC;4EAAE,WAAU;sFAA8B;;;;;;;;;;;;;;;;;;;;;;;;8DAOjD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DACC,MAAK;4DACL,IAAG;4DACH,SAAS,SAAS,MAAM;4DACxB,UAAU,CAAC,IACT,YAAY,CAAC,OAAS,CAAC;wEACrB,GAAG,IAAI;wEACP,QAAQ,EAAE,MAAM,CAAC,OAAO;oEAC1B,CAAC;4DAEH,WAAU;;;;;;sEAEZ,6VAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;;;;;;;;;;;;;wCAQN,cAAc,4BACb,6VAAC,6IAAA,CAAA,UAAc;4CACb,UAAU;4CACV,kBAAkB;4CAClB,WAAW,SAAS;;;;;;wCAKvB,cAAc,2BACb,6VAAC,2IAAA,CAAA,UAAY;4CACX,OAAO;4CACP,eAAe;4CACf,UAAS;;;;;;;;;;;;;;;;;;;;;;;kCAQnB,6VAAC;wBAAI,WAAU;;0CACb,6VAAC,kIAAA,CAAA,SAAM;gCAAC,MAAK;gCAAS,SAAQ;gCAAU,SAAS;;kDAC/C,6VAAC,gRAAA,CAAA,IAAC;wCAAC,WAAU;;;;;;oCAAiB;;;;;;;0CAGhC,6VAAC,kIAAA,CAAA,SAAM;gCACL,MAAK;gCACL,UAAU,WAAW,CAAC,SAAS,IAAI;gCACnC,WAAU;;kDAEV,6VAAC,sRAAA,CAAA,OAAI;wCAAC,WAAU;;;;;;oCACf,UAAU,kBAAkB;;;;;;;;;;;;;;;;;;;;;;;;;AAMzC", "debugId": null}}, {"offset": {"line": 3992, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/admin/ProductList.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from \"@/components/ui\";\nimport { Product, ProductVariant } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport { Edit, Eye, Package, Plus, Trash2 } from \"lucide-react\";\nimport Image from \"next/image\";\nimport { useEffect, useState } from \"react\";\n\ninterface ProductListProps {\n  onAddProduct: () => void;\n  onEditProduct: (product: Product) => void;\n  onViewProduct: (product: Product) => void;\n}\n\nexport default function ProductList({ onAddProduct, onEditProduct, onViewProduct }: ProductListProps) {\n  const [products, setProducts] = useState<Product[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  useEffect(() => {\n    fetchProducts();\n  }, []);\n\n  const fetchProducts = async () => {\n    try {\n      setLoading(true);\n      const response = await fetch('/api/products');\n      const data = await response.json();\n      \n      if (data.success) {\n        setProducts(data.data.products || []);\n      } else {\n        setError('Erreur lors du chargement des produits');\n      }\n    } catch (err) {\n      setError('Erreur de connexion');\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleDeleteProduct = async (productId: string) => {\n    if (!confirm('Êtes-vous sûr de vouloir supprimer ce produit ?')) {\n      return;\n    }\n\n    try {\n      const response = await fetch(`/api/products/${productId}`, {\n        method: 'DELETE',\n      });\n\n      if (response.ok) {\n        setProducts(products.filter(p => p.id !== productId));\n      } else {\n        alert('Erreur lors de la suppression');\n      }\n    } catch (err) {\n      alert('Erreur de connexion');\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex items-center justify-center py-12\">\n        <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <Card>\n        <CardContent className=\"p-6 text-center\">\n          <p className=\"text-red-600\">{error}</p>\n          <Button onClick={fetchProducts} className=\"mt-4\">\n            Réessayer\n          </Button>\n        </CardContent>\n      </Card>\n    );\n  }\n\n  return (\n    <div className=\"space-y-6\">\n      {/* Header */}\n      <div className=\"flex justify-between items-center\">\n        <div>\n          <h2 className=\"text-2xl font-bold text-gray-900\">Gestion des produits</h2>\n          <p className=\"text-gray-600\">{products.length} produit(s) au total</p>\n        </div>\n        <Button \n          onClick={onAddProduct}\n          className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n        >\n          <Plus className=\"w-4 h-4 mr-2\" />\n          Nouveau produit\n        </Button>\n      </div>\n\n      {/* Products Grid */}\n      {products.length === 0 ? (\n        <Card>\n          <CardContent className=\"p-12 text-center\">\n            <Package className=\"w-16 h-16 text-gray-400 mx-auto mb-4\" />\n            <h3 className=\"text-lg font-medium text-gray-900 mb-2\">\n              Aucun produit\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Commencez par créer votre premier produit Deltagum.\n            </p>\n            <Button \n              onClick={onAddProduct}\n              className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n            >\n              <Plus className=\"w-4 h-4 mr-2\" />\n              Créer un produit\n            </Button>\n          </CardContent>\n        </Card>\n      ) : (\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6\">\n          {products.map((product, index) => (\n            <motion.div\n              key={product.id}\n              initial={{ opacity: 0, y: 20 }}\n              animate={{ opacity: 1, y: 0 }}\n              transition={{ delay: index * 0.1 }}\n            >\n              <Card className=\"overflow-hidden hover:shadow-lg transition-shadow\">\n                <div className=\"relative h-48 bg-gray-100\">\n                  <Image\n                    src={product.image || '/img/placeholder.svg'}\n                    alt={product.name}\n                    fill\n                    className=\"object-cover\"\n                    onError={(e) => {\n                      e.currentTarget.src = '/img/placeholder.svg';\n                    }}\n                  />\n                  <div className=\"absolute top-2 right-2\">\n                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${\n                      product.active \n                        ? 'bg-green-100 text-green-800' \n                        : 'bg-red-100 text-red-800'\n                    }`}>\n                      {product.active ? 'Actif' : 'Inactif'}\n                    </span>\n                  </div>\n                </div>\n                \n                <CardHeader className=\"pb-2\">\n                  <CardTitle className=\"text-lg\">{product.name}</CardTitle>\n                  <p className=\"text-sm text-gray-600 line-clamp-2\">\n                    {product.description}\n                  </p>\n                </CardHeader>\n                \n                <CardContent className=\"pt-0\">\n                  <div className=\"flex items-center justify-between mb-4\">\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Prix de base</p>\n                      <p className=\"font-semibold\">\n                        {product.price ? `${product.price}€` : 'Variable'}\n                      </p>\n                    </div>\n                    <div>\n                      <p className=\"text-sm text-gray-500\">Variants</p>\n                      <p className=\"font-semibold\">\n                        {product.variants?.length || 0}\n                      </p>\n                    </div>\n                  </div>\n                  \n                  <div className=\"flex space-x-2\">\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => onViewProduct(product)}\n                      className=\"flex-1\"\n                    >\n                      <Eye className=\"w-4 h-4 mr-1\" />\n                      Voir\n                    </Button>\n                    <Button\n                      variant=\"outline\"\n                      size=\"sm\"\n                      onClick={() => onEditProduct(product)}\n                      className=\"flex-1\"\n                    >\n                      <Edit className=\"w-4 h-4 mr-1\" />\n                      Modifier\n                    </Button>\n                    <Button\n                      variant=\"danger\"\n                      size=\"sm\"\n                      onClick={() => handleDeleteProduct(product.id)}\n                    >\n                      <Trash2 className=\"w-4 h-4\" />\n                    </Button>\n                  </div>\n                </CardContent>\n              </Card>\n            </motion.div>\n          ))}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AAPA;;;;;;;AAee,SAAS,YAAY,EAAE,YAAY,EAAE,aAAa,EAAE,aAAa,EAAoB;IAClG,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAa,EAAE;IACtD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,gBAAgB;QACpB,IAAI;YACF,WAAW;YACX,MAAM,WAAW,MAAM,MAAM;YAC7B,MAAM,OAAO,MAAM,SAAS,IAAI;YAEhC,IAAI,KAAK,OAAO,EAAE;gBAChB,YAAY,KAAK,IAAI,CAAC,QAAQ,IAAI,EAAE;YACtC,OAAO;gBACL,SAAS;YACX;QACF,EAAE,OAAO,KAAK;YACZ,SAAS;QACX,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,sBAAsB,OAAO;QACjC,IAAI,CAAC,QAAQ,oDAAoD;YAC/D;QACF;QAEA,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,cAAc,EAAE,WAAW,EAAE;gBACzD,QAAQ;YACV;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,YAAY,SAAS,MAAM,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAC5C,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,KAAK;YACZ,MAAM;QACR;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,6VAAC,gIAAA,CAAA,OAAI;sBACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;gBAAC,WAAU;;kCACrB,6VAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,6VAAC,kIAAA,CAAA,SAAM;wBAAC,SAAS;wBAAe,WAAU;kCAAO;;;;;;;;;;;;;;;;;IAMzD;IAEA,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC;gBAAI,WAAU;;kCACb,6VAAC;;0CACC,6VAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,6VAAC;gCAAE,WAAU;;oCAAiB,SAAS,MAAM;oCAAC;;;;;;;;;;;;;kCAEhD,6VAAC,kIAAA,CAAA,SAAM;wBACL,SAAS;wBACT,WAAU;;0CAEV,6VAAC,sRAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;4BAAiB;;;;;;;;;;;;;YAMpC,SAAS,MAAM,KAAK,kBACnB,6VAAC,gIAAA,CAAA,OAAI;0BACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;oBAAC,WAAU;;sCACrB,6VAAC,4RAAA,CAAA,UAAO;4BAAC,WAAU;;;;;;sCACnB,6VAAC;4BAAG,WAAU;sCAAyC;;;;;;sCAGvD,6VAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,6VAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,WAAU;;8CAEV,6VAAC,sRAAA,CAAA,OAAI;oCAAC,WAAU;;;;;;gCAAiB;;;;;;;;;;;;;;;;;qCAMvC,6VAAC;gBAAI,WAAU;0BACZ,SAAS,GAAG,CAAC,CAAC,SAAS,sBACtB,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO,QAAQ;wBAAI;kCAEjC,cAAA,6VAAC,gIAAA,CAAA,OAAI;4BAAC,WAAU;;8CACd,6VAAC;oCAAI,WAAU;;sDACb,6VAAC,4OAAA,CAAA,UAAK;4CACJ,KAAK,QAAQ,KAAK,IAAI;4CACtB,KAAK,QAAQ,IAAI;4CACjB,IAAI;4CACJ,WAAU;4CACV,SAAS,CAAC;gDACR,EAAE,aAAa,CAAC,GAAG,GAAG;4CACxB;;;;;;sDAEF,6VAAC;4CAAI,WAAU;sDACb,cAAA,6VAAC;gDAAK,WAAW,CAAC,2CAA2C,EAC3D,QAAQ,MAAM,GACV,gCACA,2BACJ;0DACC,QAAQ,MAAM,GAAG,UAAU;;;;;;;;;;;;;;;;;8CAKlC,6VAAC,gIAAA,CAAA,aAAU;oCAAC,WAAU;;sDACpB,6VAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAW,QAAQ,IAAI;;;;;;sDAC5C,6VAAC;4CAAE,WAAU;sDACV,QAAQ,WAAW;;;;;;;;;;;;8CAIxB,6VAAC,gIAAA,CAAA,cAAW;oCAAC,WAAU;;sDACrB,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;;sEACC,6VAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6VAAC;4DAAE,WAAU;sEACV,QAAQ,KAAK,GAAG,GAAG,QAAQ,KAAK,CAAC,CAAC,CAAC,GAAG;;;;;;;;;;;;8DAG3C,6VAAC;;sEACC,6VAAC;4DAAE,WAAU;sEAAwB;;;;;;sEACrC,6VAAC;4DAAE,WAAU;sEACV,QAAQ,QAAQ,EAAE,UAAU;;;;;;;;;;;;;;;;;;sDAKnC,6VAAC;4CAAI,WAAU;;8DACb,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,cAAc;oDAC7B,WAAU;;sEAEV,6VAAC,oRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGlC,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,cAAc;oDAC7B,WAAU;;sEAEV,6VAAC,+RAAA,CAAA,OAAI;4DAAC,WAAU;;;;;;wDAAiB;;;;;;;8DAGnC,6VAAC,kIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS,IAAM,oBAAoB,QAAQ,EAAE;8DAE7C,cAAA,6VAAC,8RAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBA1ErB,QAAQ,EAAE;;;;;;;;;;;;;;;;AAqF7B", "debugId": null}}, {"offset": {"line": 4455, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/admin/dashboard/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON>,\n  CardContent,\n  <PERSON><PERSON><PERSON><PERSON>,\n  CardTitle,\n} from \"@/components/ui\";\nimport { useAuth } from \"@/stores/auth-store\";\nimport { motion } from \"framer-motion\";\nimport {\n  DollarSign,\n  LogOut,\n  Package,\n  Plus,\n  ShoppingCart,\n  Upload,\n  Users,\n} from \"lucide-react\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect, useState } from \"react\";\n\n// Admin components\nimport OrderList from \"@/components/admin/OrderList\";\nimport ProductDetails from \"@/components/admin/ProductDetails\";\nimport ProductFormAdvanced from \"@/components/admin/ProductFormAdvanced\";\nimport ProductList from \"@/components/admin/ProductList\";\n\nexport default function AdminDashboard() {\n  const { user, isAuthenticated, logout, isAdmin, checkAuth } = useAuth();\n  const router = useRouter();\n  const [activeTab, setActiveTab] = useState(\"overview\");\n\n  // Product management states\n  const [productView, setProductView] = useState<\"list\" | \"form\" | \"details\">(\n    \"list\"\n  );\n  const [selectedProduct, setSelectedProduct] = useState<any>(null);\n\n  // Customer management states\n  const [customerView, setCustomerView] = useState<\"list\" | \"details\">(\"list\");\n  const [selectedCustomer, setSelectedCustomer] = useState<any>(null);\n\n  // Order management states\n  const [orderView, setOrderView] = useState<\"list\" | \"details\">(\"list\");\n  const [selectedOrder, setSelectedOrder] = useState<any>(null);\n\n  // Vérification simple des permissions\n  useEffect(() => {\n    checkAuth();\n  }, []);\n\n  // Pas de redirection automatique, on laisse l'utilisateur voir le debug\n\n  const handleLogout = async () => {\n    await logout();\n    router.push(\"/\");\n  };\n\n  // Product management functions\n  const handleAddProduct = () => {\n    setSelectedProduct(null);\n    setProductView(\"form\");\n  };\n\n  const handleEditProduct = (product: any) => {\n    setSelectedProduct(product);\n    setProductView(\"form\");\n  };\n\n  const handleViewProduct = (product: any) => {\n    setSelectedProduct(product);\n    setProductView(\"details\");\n  };\n\n  const handleSaveProduct = async (productData: any) => {\n    try {\n      const url = selectedProduct\n        ? `/api/products/${selectedProduct.id}`\n        : \"/api/products\";\n\n      const method = selectedProduct ? \"PUT\" : \"POST\";\n\n      const response = await fetch(url, {\n        method,\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(productData),\n      });\n\n      if (response.ok) {\n        setProductView(\"list\");\n        setSelectedProduct(null);\n        // Refresh the product list\n      } else {\n        alert(\"Erreur lors de la sauvegarde\");\n      }\n    } catch (error) {\n      alert(\"Erreur de connexion\");\n    }\n  };\n\n  const handleBackToProducts = () => {\n    setProductView(\"list\");\n    setSelectedProduct(null);\n  };\n\n  const handleQuickAction = (action: string) => {\n    switch (action) {\n      case \"products\":\n        setActiveTab(\"products\");\n        break;\n      case \"orders\":\n        setActiveTab(\"orders\");\n        break;\n      case \"customers\":\n        setActiveTab(\"customers\");\n        break;\n      default:\n        break;\n    }\n  };\n\n  // Customer management functions\n  const handleViewCustomer = (customer: any) => {\n    setSelectedCustomer(customer);\n    setCustomerView(\"details\");\n  };\n\n  const handleBackToCustomers = () => {\n    setCustomerView(\"list\");\n    setSelectedCustomer(null);\n  };\n\n  // Order management functions\n  const handleViewOrder = (order: any) => {\n    setSelectedOrder(order);\n    setOrderView(\"details\");\n  };\n\n  const handleBackToOrders = () => {\n    setOrderView(\"list\");\n    setSelectedOrder(null);\n  };\n\n  const handleUpdateOrderStatus = async (orderId: string, status: string) => {\n    try {\n      const response = await fetch(`/api/orders/${orderId}`, {\n        method: \"PATCH\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({ status }),\n      });\n\n      if (response.ok) {\n        // Mettre à jour l'ordre local\n        setSelectedOrder((prev: any) => (prev ? { ...prev, status } : null));\n        alert(\"Statut de la commande mis à jour avec succès !\");\n      } else {\n        alert(\"Erreur lors de la mise à jour du statut\");\n      }\n    } catch (error) {\n      alert(\"Erreur de connexion\");\n    }\n  };\n\n  // Debug info\n  console.log(\"Dashboard - isAuthenticated:\", isAuthenticated);\n  console.log(\"Dashboard - user:\", user);\n  console.log(\"Dashboard - isAdmin():\", isAdmin());\n\n  // Affichage conditionnel : si pas admin, on montre le debug\n  if (!isAuthenticated || !isAdmin()) {\n    return (\n      <div className=\"min-h-screen bg-gray-50 flex items-center justify-center\">\n        <div className=\"bg-white p-8 rounded-lg shadow-lg max-w-lg w-full\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold text-gray-900 mb-6\">\n              🔐 Accès Dashboard Admin\n            </h2>\n\n            <div className=\"text-left bg-gray-50 p-4 rounded-lg mb-6\">\n              <h3 className=\"font-semibold mb-3\">État actuel :</h3>\n              <div className=\"space-y-2 text-sm\">\n                <p>✅ Authentifié: {isAuthenticated ? \"Oui\" : \"Non\"}</p>\n                <p>\n                  👤 Utilisateur:{\" \"}\n                  {user ? `${user.firstName} ${user.lastName}` : \"Aucun\"}\n                </p>\n                <p>🎭 Rôle: {user?.role || \"Aucun\"}</p>\n                <p>🔑 Admin: {isAdmin() ? \"Oui\" : \"Non\"}</p>\n              </div>\n            </div>\n\n            <div className=\"space-y-3\">\n              {!isAuthenticated ? (\n                <div>\n                  <p className=\"text-gray-600 mb-4\">\n                    Vous devez vous connecter en tant qu'admin\n                  </p>\n                  <a\n                    href=\"/auth\"\n                    className=\"inline-block bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700\"\n                  >\n                    Se connecter\n                  </a>\n                </div>\n              ) : !isAdmin() ? (\n                <div>\n                  <p className=\"text-gray-600 mb-4\">\n                    Votre compte n'a pas les permissions admin\n                  </p>\n                  <a\n                    href=\"/\"\n                    className=\"inline-block bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700\"\n                  >\n                    Retour à l'accueil\n                  </a>\n                </div>\n              ) : null}\n\n              <div className=\"pt-4 border-t\">\n                <a\n                  href=\"/test-auth\"\n                  className=\"text-sm text-blue-600 hover:underline\"\n                >\n                  🧪 Page de test d'authentification\n                </a>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    );\n  }\n\n  const fadeIn = {\n    initial: { opacity: 0, y: 20 },\n    animate: { opacity: 1, y: 0 },\n    transition: { duration: 0.3, ease: \"easeOut\" },\n  };\n\n  const slideIn = {\n    initial: { opacity: 0, x: -20 },\n    animate: { opacity: 1, x: 0 },\n    transition: { duration: 0.4, ease: \"easeOut\" },\n  };\n\n  const staggerContainer = {\n    initial: {},\n    animate: {\n      transition: {\n        staggerChildren: 0.1,\n      },\n    },\n  };\n\n  const stats = [\n    { title: \"Produits\", value: \"3\", icon: Package, color: \"bg-blue-500\" },\n    {\n      title: \"Commandes\",\n      value: \"12\",\n      icon: ShoppingCart,\n      color: \"bg-green-500\",\n    },\n    { title: \"Clients\", value: \"45\", icon: Users, color: \"bg-purple-500\" },\n    {\n      title: \"Revenus\",\n      value: \"€1,234\",\n      icon: DollarSign,\n      color: \"bg-orange-500\",\n    },\n  ];\n\n  const menuItems = [\n    { id: \"overview\", label: \"Vue d'ensemble\", icon: Package },\n    { id: \"products\", label: \"Produits\", icon: Package },\n    { id: \"orders\", label: \"Commandes\", icon: ShoppingCart },\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100\">\n      {/* Header */}\n      <motion.header\n        initial={{ y: -20, opacity: 0 }}\n        animate={{ y: 0, opacity: 1 }}\n        className=\"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-[99]\"\n      >\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center h-16\">\n            <div className=\"flex items-center space-x-4\">\n              <motion.h1\n                initial={{ scale: 0.9 }}\n                animate={{ scale: 1 }}\n                className=\"text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\"\n              >\n                Deltagum Admin\n              </motion.h1>\n            </div>\n            <div className=\"flex items-center space-x-4\">\n              <motion.span\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                transition={{ delay: 0.2 }}\n                className=\"text-gray-700 hidden sm:block\"\n              >\n                Bonjour, {user?.firstName} {user?.lastName}\n              </motion.span>\n              <Button\n                onClick={handleLogout}\n                variant=\"outline\"\n                size=\"sm\"\n                className=\"flex items-center space-x-2 hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors\"\n              >\n                <LogOut className=\"w-4 h-4\" />\n                <span className=\"hidden sm:block\">Déconnexion</span>\n              </Button>\n            </div>\n          </div>\n        </div>\n      </motion.header>\n\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        <div className=\"flex flex-col lg:flex-row gap-8\">\n          {/* Sidebar */}\n          <motion.div\n            initial={slideIn.initial}\n            animate={slideIn.animate}\n            className=\"w-full lg:w-64 bg-white rounded-xl shadow-lg p-6 border border-gray-200\"\n          >\n            <nav className=\"space-y-2\">\n              {menuItems.map((item) => (\n                <button\n                  key={item.id}\n                  onClick={() => setActiveTab(item.id)}\n                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                    activeTab === item.id\n                      ? \"bg-pink-50 text-pink-600 border-l-4 border-pink-500\"\n                      : \"text-gray-900 hover:bg-gray-50 hover:text-black\"\n                  }`}\n                >\n                  <item.icon className=\"w-5 h-5\" />\n                  <span className=\"font-medium\">{item.label}</span>\n                </button>\n              ))}\n            </nav>\n          </motion.div>\n\n          {/* Main Content */}\n          <div className=\"flex-1\">\n            {activeTab === \"overview\" && (\n              <motion.div\n                initial={fadeIn.initial}\n                animate={fadeIn.animate}\n                className=\"space-y-8\"\n              >\n                {/* Statistiques du dashboard */}\n                <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6\">\n                  <div className=\"bg-white p-6 rounded-lg shadow border border-gray-100\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                      Produits\n                    </h3>\n                    <p className=\"text-3xl font-bold text-pink-600\">2</p>\n                  </div>\n                  <div className=\"bg-white p-6 rounded-lg shadow border border-gray-100\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                      Commandes\n                    </h3>\n                    <p className=\"text-3xl font-bold text-green-600\">0</p>\n                  </div>\n                  <div className=\"bg-white p-6 rounded-lg shadow border border-gray-100\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                      Clients\n                    </h3>\n                    <p className=\"text-3xl font-bold text-blue-600\">1</p>\n                  </div>\n                  <div className=\"bg-white p-6 rounded-lg shadow border border-gray-100\">\n                    <h3 className=\"text-lg font-semibold text-gray-800 mb-2\">\n                      Revenus\n                    </h3>\n                    <p className=\"text-3xl font-bold text-orange-600\">0€</p>\n                  </div>\n                </div>\n\n                {/* Quick Actions */}\n                <Card>\n                  <CardHeader>\n                    <CardTitle>Actions rapides</CardTitle>\n                  </CardHeader>\n                  <CardContent>\n                    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n                      <Button\n                        onClick={handleAddProduct}\n                        className=\"h-20 flex flex-col items-center justify-center space-y-2 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600\"\n                      >\n                        <Plus className=\"w-6 h-6\" />\n                        <span>Ajouter un produit</span>\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        className=\"h-20 flex flex-col items-center justify-center space-y-2\"\n                        onClick={() => setActiveTab(\"images\")}\n                      >\n                        <Upload className=\"w-6 h-6\" />\n                        <span>Gérer les images</span>\n                      </Button>\n                      <Button\n                        variant=\"outline\"\n                        onClick={() => setActiveTab(\"orders\")}\n                        className=\"h-20 flex flex-col items-center justify-center space-y-2\"\n                      >\n                        <ShoppingCart className=\"w-6 h-6\" />\n                        <span>Voir les commandes</span>\n                      </Button>\n                    </div>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            )}\n\n            {activeTab === \"products\" && (\n              <motion.div\n                initial={fadeIn.initial}\n                animate={fadeIn.animate}\n                className=\"space-y-6\"\n              >\n                {productView === \"list\" && (\n                  <ProductList\n                    onAddProduct={handleAddProduct}\n                    onEditProduct={handleEditProduct}\n                    onViewProduct={handleViewProduct}\n                  />\n                )}\n\n                {productView === \"form\" && (\n                  <ProductFormAdvanced\n                    product={selectedProduct}\n                    onSave={handleSaveProduct}\n                    onCancel={handleBackToProducts}\n                  />\n                )}\n\n                {productView === \"details\" && selectedProduct && (\n                  <ProductDetails\n                    product={selectedProduct}\n                    onEdit={() => handleEditProduct(selectedProduct)}\n                    onBack={handleBackToProducts}\n                    onAddVariant={() => {\n                      // TODO: Implement variant management\n                      alert(\"Gestion des variantes à implémenter\");\n                    }}\n                  />\n                )}\n              </motion.div>\n            )}\n\n            {activeTab === \"orders\" && (\n              <motion.div\n                initial={fadeIn.initial}\n                animate={fadeIn.animate}\n                className=\"space-y-6\"\n              >\n                {orderView === \"list\" && (\n                  <OrderList\n                    onViewOrder={(order) => {\n                      setSelectedOrder(order);\n                      setOrderView(\"details\");\n                    }}\n                  />\n                )}\n\n                {orderView === \"details\" && selectedOrder && (\n                  <OrderDetails\n                    order={selectedOrder}\n                    onBack={handleBackToOrders}\n                    onUpdateStatus={handleUpdateOrderStatus}\n                  />\n                )}\n              </motion.div>\n            )}\n\n            {/* Autres onglets à implémenter */}\n            {activeTab !== \"overview\" &&\n              activeTab !== \"products\" &&\n              activeTab !== \"orders\" && (\n                <motion.div\n                  initial={fadeIn.initial}\n                  animate={fadeIn.animate}\n                  className=\"space-y-6\"\n                >\n                  <Card>\n                    <CardContent className=\"p-12 text-center\">\n                      <h3 className=\"text-xl font-medium text-gray-900 mb-2\">\n                        Section en développement\n                      </h3>\n                      <p className=\"text-gray-600\">\n                        Cette section sera bientôt disponible.\n                      </p>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAOA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AASA;AACA;AAEA,mBAAmB;AACnB;AACA;AACA;AACA;AA3BA;;;;;;;;;;;;AA6Be,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,eAAe,EAAE,MAAM,EAAE,OAAO,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8HAAA,CAAA,UAAO,AAAD;IACpE,MAAM,SAAS,CAAA,GAAA,iPAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,4BAA4B;IAC5B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAC3C;IAEF,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAO;IAE5D,6BAA6B;IAC7B,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAsB;IACrE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAO;IAE9D,0BAA0B;IAC1B,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAsB;IAC/D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,oTAAA,CAAA,WAAQ,AAAD,EAAO;IAExD,sCAAsC;IACtC,CAAA,GAAA,oTAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,wEAAwE;IAExE,MAAM,eAAe;QACnB,MAAM;QACN,OAAO,IAAI,CAAC;IACd;IAEA,+BAA+B;IAC/B,MAAM,mBAAmB;QACvB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB,CAAC;QACzB,mBAAmB;QACnB,eAAe;IACjB;IAEA,MAAM,oBAAoB,OAAO;QAC/B,IAAI;YACF,MAAM,MAAM,kBACR,CAAC,cAAc,EAAE,gBAAgB,EAAE,EAAE,GACrC;YAEJ,MAAM,SAAS,kBAAkB,QAAQ;YAEzC,MAAM,WAAW,MAAM,MAAM,KAAK;gBAChC;gBACA,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,eAAe;gBACf,mBAAmB;YACnB,2BAA2B;YAC7B,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,uBAAuB;QAC3B,eAAe;QACf,mBAAmB;IACrB;IAEA,MAAM,oBAAoB,CAAC;QACzB,OAAQ;YACN,KAAK;gBACH,aAAa;gBACb;YACF,KAAK;gBACH,aAAa;gBACb;YACF,KAAK;gBACH,aAAa;gBACb;YACF;gBACE;QACJ;IACF;IAEA,gCAAgC;IAChC,MAAM,qBAAqB,CAAC;QAC1B,oBAAoB;QACpB,gBAAgB;IAClB;IAEA,MAAM,wBAAwB;QAC5B,gBAAgB;QAChB,oBAAoB;IACtB;IAEA,6BAA6B;IAC7B,MAAM,kBAAkB,CAAC;QACvB,iBAAiB;QACjB,aAAa;IACf;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,iBAAiB;IACnB;IAEA,MAAM,0BAA0B,OAAO,SAAiB;QACtD,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,CAAC,YAAY,EAAE,SAAS,EAAE;gBACrD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBAAE;gBAAO;YAChC;YAEA,IAAI,SAAS,EAAE,EAAE;gBACf,8BAA8B;gBAC9B,iBAAiB,CAAC,OAAe,OAAO;wBAAE,GAAG,IAAI;wBAAE;oBAAO,IAAI;gBAC9D,MAAM;YACR,OAAO;gBACL,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,aAAa;IACb,QAAQ,GAAG,CAAC,gCAAgC;IAC5C,QAAQ,GAAG,CAAC,qBAAqB;IACjC,QAAQ,GAAG,CAAC,0BAA0B;IAEtC,4DAA4D;IAC5D,IAAI,CAAC,mBAAmB,CAAC,WAAW;QAClC,qBACE,6VAAC;YAAI,WAAU;sBACb,cAAA,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCACb,6VAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAItD,6VAAC;4BAAI,WAAU;;8CACb,6VAAC;oCAAG,WAAU;8CAAqB;;;;;;8CACnC,6VAAC;oCAAI,WAAU;;sDACb,6VAAC;;gDAAE;gDAAgB,kBAAkB,QAAQ;;;;;;;sDAC7C,6VAAC;;gDAAE;gDACe;gDACf,OAAO,GAAG,KAAK,SAAS,CAAC,CAAC,EAAE,KAAK,QAAQ,EAAE,GAAG;;;;;;;sDAEjD,6VAAC;;gDAAE;gDAAU,MAAM,QAAQ;;;;;;;sDAC3B,6VAAC;;gDAAE;gDAAW,YAAY,QAAQ;;;;;;;;;;;;;;;;;;;sCAItC,6VAAC;4BAAI,WAAU;;gCACZ,CAAC,gCACA,6VAAC;;sDACC,6VAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6VAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;2CAID,CAAC,0BACH,6VAAC;;sDACC,6VAAC;4CAAE,WAAU;sDAAqB;;;;;;sDAGlC,6VAAC;4CACC,MAAK;4CACL,WAAU;sDACX;;;;;;;;;;;2CAID;8CAEJ,6VAAC;oCAAI,WAAU;8CACb,cAAA,6VAAC;wCACC,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IASf;IAEA,MAAM,SAAS;QACb,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;IAEA,MAAM,UAAU;QACd,SAAS;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;QAC9B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;IAEA,MAAM,mBAAmB;QACvB,SAAS,CAAC;QACV,SAAS;YACP,YAAY;gBACV,iBAAiB;YACnB;QACF;IACF;IAEA,MAAM,QAAQ;QACZ;YAAE,OAAO;YAAY,OAAO;YAAK,MAAM,4RAAA,CAAA,UAAO;YAAE,OAAO;QAAc;QACrE;YACE,OAAO;YACP,OAAO;YACP,MAAM,0SAAA,CAAA,eAAY;YAClB,OAAO;QACT;QACA;YAAE,OAAO;YAAW,OAAO;YAAM,MAAM,wRAAA,CAAA,QAAK;YAAE,OAAO;QAAgB;QACrE;YACE,OAAO;YACP,OAAO;YACP,MAAM,sSAAA,CAAA,aAAU;YAChB,OAAO;QACT;KACD;IAED,MAAM,YAAY;QAChB;YAAE,IAAI;YAAY,OAAO;YAAkB,MAAM,4RAAA,CAAA,UAAO;QAAC;QACzD;YAAE,IAAI;YAAY,OAAO;YAAY,MAAM,4RAAA,CAAA,UAAO;QAAC;QACnD;YAAE,IAAI;YAAU,OAAO;YAAa,MAAM,0SAAA,CAAA,eAAY;QAAC;KACxD;IAED,qBACE,6VAAC;QAAI,WAAU;;0BAEb,6VAAC,gSAAA,CAAA,SAAM,CAAC,MAAM;gBACZ,SAAS;oBAAE,GAAG,CAAC;oBAAI,SAAS;gBAAE;gBAC9B,SAAS;oBAAE,GAAG;oBAAG,SAAS;gBAAE;gBAC5B,WAAU;0BAEV,cAAA,6VAAC;oBAAI,WAAU;8BACb,cAAA,6VAAC;wBAAI,WAAU;;0CACb,6VAAC;gCAAI,WAAU;0CACb,cAAA,6VAAC,gSAAA,CAAA,SAAM,CAAC,EAAE;oCACR,SAAS;wCAAE,OAAO;oCAAI;oCACtB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,WAAU;8CACX;;;;;;;;;;;0CAIH,6VAAC;gCAAI,WAAU;;kDACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,IAAI;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;;4CACX;4CACW,MAAM;4CAAU;4CAAE,MAAM;;;;;;;kDAEpC,6VAAC,kIAAA,CAAA,SAAM;wCACL,SAAS;wCACT,SAAQ;wCACR,MAAK;wCACL,WAAU;;0DAEV,6VAAC,8RAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,6VAAC;gDAAK,WAAU;0DAAkB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO5C,6VAAC;gBAAI,WAAU;0BACb,cAAA,6VAAC;oBAAI,WAAU;;sCAEb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,SAAS,QAAQ,OAAO;4BACxB,SAAS,QAAQ,OAAO;4BACxB,WAAU;sCAEV,cAAA,6VAAC;gCAAI,WAAU;0CACZ,UAAU,GAAG,CAAC,CAAC,qBACd,6VAAC;wCAEC,SAAS,IAAM,aAAa,KAAK,EAAE;wCACnC,WAAW,CAAC,oFAAoF,EAC9F,cAAc,KAAK,EAAE,GACjB,wDACA,mDACJ;;0DAEF,6VAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,6VAAC;gDAAK,WAAU;0DAAe,KAAK,KAAK;;;;;;;uCATpC,KAAK,EAAE;;;;;;;;;;;;;;;sCAgBpB,6VAAC;4BAAI,WAAU;;gCACZ,cAAc,4BACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS,OAAO,OAAO;oCACvB,SAAS,OAAO,OAAO;oCACvB,WAAU;;sDAGV,6VAAC;4CAAI,WAAU;;8DACb,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,6VAAC;4DAAE,WAAU;sEAAmC;;;;;;;;;;;;8DAElD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,6VAAC;4DAAE,WAAU;sEAAoC;;;;;;;;;;;;8DAEnD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,6VAAC;4DAAE,WAAU;sEAAmC;;;;;;;;;;;;8DAElD,6VAAC;oDAAI,WAAU;;sEACb,6VAAC;4DAAG,WAAU;sEAA2C;;;;;;sEAGzD,6VAAC;4DAAE,WAAU;sEAAqC;;;;;;;;;;;;;;;;;;sDAKtD,6VAAC,gIAAA,CAAA,OAAI;;8DACH,6VAAC,gIAAA,CAAA,aAAU;8DACT,cAAA,6VAAC,gIAAA,CAAA,YAAS;kEAAC;;;;;;;;;;;8DAEb,6VAAC,gIAAA,CAAA,cAAW;8DACV,cAAA,6VAAC;wDAAI,WAAU;;0EACb,6VAAC,kIAAA,CAAA,SAAM;gEACL,SAAS;gEACT,WAAU;;kFAEV,6VAAC,sRAAA,CAAA,OAAI;wEAAC,WAAU;;;;;;kFAChB,6VAAC;kFAAK;;;;;;;;;;;;0EAER,6VAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,WAAU;gEACV,SAAS,IAAM,aAAa;;kFAE5B,6VAAC,0RAAA,CAAA,SAAM;wEAAC,WAAU;;;;;;kFAClB,6VAAC;kFAAK;;;;;;;;;;;;0EAER,6VAAC,kIAAA,CAAA,SAAM;gEACL,SAAQ;gEACR,SAAS,IAAM,aAAa;gEAC5B,WAAU;;kFAEV,6VAAC,0SAAA,CAAA,eAAY;wEAAC,WAAU;;;;;;kFACxB,6VAAC;kFAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;gCAQjB,cAAc,4BACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS,OAAO,OAAO;oCACvB,SAAS,OAAO,OAAO;oCACvB,WAAU;;wCAET,gBAAgB,wBACf,6VAAC,0IAAA,CAAA,UAAW;4CACV,cAAc;4CACd,eAAe;4CACf,eAAe;;;;;;wCAIlB,gBAAgB,wBACf,6VAAC,kJAAA,CAAA,UAAmB;4CAClB,SAAS;4CACT,QAAQ;4CACR,UAAU;;;;;;wCAIb,gBAAgB,aAAa,iCAC5B,6VAAC,6IAAA,CAAA,UAAc;4CACb,SAAS;4CACT,QAAQ,IAAM,kBAAkB;4CAChC,QAAQ;4CACR,cAAc;gDACZ,qCAAqC;gDACrC,MAAM;4CACR;;;;;;;;;;;;gCAMP,cAAc,0BACb,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS,OAAO,OAAO;oCACvB,SAAS,OAAO,OAAO;oCACvB,WAAU;;wCAET,cAAc,wBACb,6VAAC,wIAAA,CAAA,UAAS;4CACR,aAAa,CAAC;gDACZ,iBAAiB;gDACjB,aAAa;4CACf;;;;;;wCAIH,cAAc,aAAa,+BAC1B,6VAAC;4CACC,OAAO;4CACP,QAAQ;4CACR,gBAAgB;;;;;;;;;;;;gCAOvB,cAAc,cACb,cAAc,cACd,cAAc,0BACZ,6VAAC,gSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,SAAS,OAAO,OAAO;oCACvB,SAAS,OAAO,OAAO;oCACvB,WAAU;8CAEV,cAAA,6VAAC,gIAAA,CAAA,OAAI;kDACH,cAAA,6VAAC,gIAAA,CAAA,cAAW;4CAAC,WAAU;;8DACrB,6VAAC;oDAAG,WAAU;8DAAyC;;;;;;8DAGvD,6VAAC;oDAAE,WAAU;8DAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD", "debugId": null}}]}