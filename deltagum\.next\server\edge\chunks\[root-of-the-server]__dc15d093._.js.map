{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/middleware.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from \"next/server\";\n\ninterface AuthUser {\n  userId: string;\n  email: string;\n  role: string;\n  firstName: string;\n  lastName: string;\n}\n\nexport function middleware(request: NextRequest) {\n  // DÉSACTIVÉ TEMPORAIREMENT - Aucune protection pour faciliter l'accès\n\n  // Vous pouvez réactiver cette protection plus tard si nécessaire\n  // const pathname = request.nextUrl.pathname;\n  // if (pathname.startsWith(\"/admin\")) {\n  //   const token = request.cookies.get(\"auth-token\")?.value;\n  //   if (!token) {\n  //     return NextResponse.redirect(new URL(\"/auth\", request.url));\n  //   }\n  // }\n\n  // Laisser passer toutes les requêtes\n  return NextResponse.next();\n}\n\nexport const config = {\n  // Désactivé temporairement - aucune route n'est interceptée\n  matcher: [],\n};\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;AAUO,SAAS,WAAW,OAAoB;IAC7C,sEAAsE;IAEtE,iEAAiE;IACjE,6CAA6C;IAC7C,uCAAuC;IACvC,4DAA4D;IAC5D,kBAAkB;IAClB,mEAAmE;IACnE,MAAM;IACN,IAAI;IAEJ,qCAAqC;IACrC,OAAO,4SAAA,CAAA,eAAY,CAAC,IAAI;AAC1B;AAEO,MAAM,SAAS;IACpB,4DAA4D;IAC5D,SAAS,EAAE;AACb"}}]}