{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "b953cfa4117dc6b817224abc4c8512a8", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "c4501fad06ac883a46d41b4cf9ccc2a8989858141e177d88d38e8c228db7c66c", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1eebbae6e34f324e3513e0024b83c228ed29465c9670c3838cd2b7b95667cc45"}}}, "sortedMiddleware": ["/"], "functions": {}}