{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "8d2bebd187b265708aeb9a1cd5cfe9b0", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "49284c010079118ba2a01fea61e30ced13a391d2d73e85ef95e6bc0e89dfec11", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "9377dcc0efc0f97c42d7a5a427f5eceb136929e6d2d7ca8154a8cd15c4c1aee5"}}}, "sortedMiddleware": ["/"], "functions": {}}