{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "1cb07c7aa3d7e946b31cf6ac8c44de7c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "6551822b47428bee178e185ef31c257d2e282818efdd53881b031c304ca890f8", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "8428347073e91c3a65e85d320bd5052722ff460200e9b62dad0079368d74972a"}}}, "sortedMiddleware": ["/"], "functions": {}}