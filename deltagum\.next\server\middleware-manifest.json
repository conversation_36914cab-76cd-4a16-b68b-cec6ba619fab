{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "ba313a2c0fa4b66ebddf1df5fed526b3", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e8ce749d08f2949e11f3fbafd368c3d5f13fd976278106f0b0168a5f77176f24", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4e4ee42d2a5dfa872cd0b302018da326b24dcd3d3852e66384728e52b7447a05"}}}, "sortedMiddleware": ["/"], "functions": {}}