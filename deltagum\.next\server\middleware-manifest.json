{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "KfFh49RZAlCJT4gpcePdJ", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "263f04b52b3651868cb71dd0733998e7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "e1da3ac1df5b298ac6ca251c47fd3013dc7668072e7fafa6246afdcb476bbc52", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "cf2e594f2b855adbedebd090f84892b0240c7dd420ef677a33614903a4bd2ea5"}}}, "functions": {}, "sortedMiddleware": ["/"]}