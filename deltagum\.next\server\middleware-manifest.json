{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "74aa56fe5496a5deda0e0031372050c1", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "04f69a4e5a118eb886908ccb8c87cdfd6707d51bbf0c9fcd5b6a5b1f7bca9345", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "ffee3003717323b202fc09d8f9c4622fb172563b458bf3271436dcbd455ce9f8"}}}, "sortedMiddleware": ["/"], "functions": {}}