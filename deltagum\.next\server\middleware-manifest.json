{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "51417605c549684c5369770177d4444d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "14ee54543d3b2560ea454046cef25a83ce95539340e493d3d790ed11470fe8e5", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "56c22a3ead5afa61972b86ac82f33084c3bb0a9c1444a0c57f86ba7bc7a6859a"}}}, "sortedMiddleware": ["/"], "functions": {}}