{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "L_iZ5wo9yiEFMbYYri1sl", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "030a937b4f9d728e3b3dd4db20d5344c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9a7705103408354cc8f672cb58c3a3110597ff3d61bdab41454b67142e89e407", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4aa7aa86d89c7839f65464297002efb37ddc757247d4111585c90127764ec041"}}}, "functions": {}, "sortedMiddleware": ["/"]}