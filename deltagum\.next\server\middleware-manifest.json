{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "550c89977405cfdc25ac6a7f7c07a844", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ac98aa6827b92a6c45c0cc1d3aa7d6b8b408b050cce67c1ce3f39fc283e3ce0d", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "3086739d488a876fd5fbe3ff538999007ff2a554058bdeb2382c23be5d5a9e66"}}}, "sortedMiddleware": ["/"], "functions": {}}