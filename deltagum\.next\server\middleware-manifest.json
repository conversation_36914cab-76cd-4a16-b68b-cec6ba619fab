{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "74f694c1850f9f9417957719b4563847", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "7d84f2b4ba20ee1023dfff5ce5db58c81b244682febd8f5602e98155ad007db9", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "454c38718fc24fe6e4e2d1c75583fda0be8ac4f0c33128b081bdf0cca5eb2680"}}}, "sortedMiddleware": ["/"], "functions": {}}