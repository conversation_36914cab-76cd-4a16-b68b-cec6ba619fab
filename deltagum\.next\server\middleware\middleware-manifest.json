{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "df84887193537e0c81be8fbf2fa1f638", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "f7cf7ec77066d352291a62e582c2d50eb8a0a242864262c85e3bf21e417c4792", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a6b40ac67866c7ea621716bc0f2c326df3d090978bd5ce5f15f115279d132975"}}}, "instrumentation": null, "functions": {}}