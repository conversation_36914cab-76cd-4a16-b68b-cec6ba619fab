{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "biaJBmSyqOSjoz0Qk4MwM6o05y1+kuAluT8MXWu/JxU=", "__NEXT_PREVIEW_MODE_ID": "c4f0c8b387e0fcc511339bcd40254287", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "8fec229a4721108b462d13eafae5a193e1c49d07c2a964d406cfc3061925705f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "0c191fbdfafa6c93e48866996ca4bb00fe7e6a893b4735a4c0fdb046d65a6034"}}}, "instrumentation": null, "functions": {}}