{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_1365e518._.js", "server/edge/chunks/[root-of-the-server]__dc15d093._.js", "server/edge/chunks/edge-wrapper_8e93a69c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "c8U6EGds0OL+r2bsCLcw7A3uNPFOGlGFf1IX0mZbzP4=", "__NEXT_PREVIEW_MODE_ID": "c9ae35083914e9c56dd01009b5e0b941", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "206420839439c4fde620ddacac9a00d40b2bf277705ac81bb186b0b9147c9aff", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "a0e9980f8df7c5d1927e3c46fa36d0186ac33ad237e2668e6f947989f25ecc5b"}}}, "instrumentation": null, "functions": {}}