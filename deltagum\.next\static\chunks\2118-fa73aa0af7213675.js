"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2118],{339:(e,s,a)=>{a.d(s,{N:()=>m});var t=a(5936),i=a(9084),r=a(5156),n=a(3537),l=a(6953);a(5180);var d=a(4481),c=a(3515);let o=c.Ik({firstName:c.Yj().min(2,"Le pr\xe9nom doit contenir au moins 2 caract\xe8res"),lastName:c.Yj().min(2,"Le nom doit contenir au moins 2 caract\xe8res"),email:c.Yj().email("Email invalide"),phone:c.Yj().min(10,"Num\xe9ro de t\xe9l\xe9phone invalide"),address:c.Yj().min(5,"Adresse trop courte"),city:c.Yj().min(2,"Ville requise"),postalCode:c.Yj().regex(/^\d{5}$/,"Code postal invalide (5 chiffres)"),country:c.Yj().min(2,"Pays requis"),deliveryInstructions:c.Yj().optional()}),m=e=>{var s,a,c,m,x,u,p,h;let{onNext:g,isProcessing:j}=e,{customer:y,updateCustomer:v}=(0,r.useCustomer)(),{addNotification:b}=(0,r.useNotifications)(),{closeModal:N}=(0,r.useCheckoutModal)(),{register:f,handleSubmit:w,formState:{errors:k,isValid:P},setValue:C,watch:D}=(0,d.mN)({resolver:(0,n.u)(o),defaultValues:{firstName:(null==y?void 0:y.firstName)||"",lastName:(null==y?void 0:y.lastName)||"",email:(null==y?void 0:y.email)||"",phone:(null==y?void 0:y.phone)||"",address:"",city:"",postalCode:"",country:"France",deliveryInstructions:""}}),I=async e=>{try{v({firstName:e.firstName,lastName:e.lastName,email:e.email,phone:e.phone}),b({type:"success",title:"Livraison",message:"Informations de livraison sauvegard\xe9es"}),g()}catch(e){b({type:"error",title:"Erreur",message:"Erreur lors de la sauvegarde"})}};return(0,t.jsxs)(l.P.form,{onSubmit:w(I),className:"space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-800",children:"Mode d\xe9monstration"}),(0,t.jsx)("p",{className:"text-sm text-blue-600",children:"Remplir avec des donn\xe9es de test"})]}),(0,t.jsx)(i.$n,{type:"button",variant:"outline",size:"sm",onClick:()=>{C("firstName","Marie"),C("lastName","Dupont"),C("email","<EMAIL>"),C("phone","0123456789"),C("address","123 Rue de la Paix"),C("city","Paris"),C("postalCode","75001"),C("country","France"),C("deliveryInstructions","Laisser devant la porte si absent")},className:"border-blue-300 text-blue-600 hover:bg-blue-100",children:"Remplir automatiquement"})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pr\xe9nom *"}),(0,t.jsx)(i.pd,{...f("firstName"),placeholder:"Votre pr\xe9nom",error:null==(s=k.firstName)?void 0:s.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom *"}),(0,t.jsx)(i.pd,{...f("lastName"),placeholder:"Votre nom",error:null==(a=k.lastName)?void 0:a.message})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Email *"}),(0,t.jsx)(i.pd,{...f("email"),type:"email",placeholder:"<EMAIL>",error:null==(c=k.email)?void 0:c.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"T\xe9l\xe9phone *"}),(0,t.jsx)(i.pd,{...f("phone"),type:"tel",placeholder:"01 23 45 67 89",error:null==(m=k.phone)?void 0:m.message})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Adresse *"}),(0,t.jsx)(i.pd,{...f("address"),placeholder:"123 Rue de la Paix",error:null==(x=k.address)?void 0:x.message})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Ville *"}),(0,t.jsx)(i.pd,{...f("city"),placeholder:"Paris",error:null==(u=k.city)?void 0:u.message})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Code postal *"}),(0,t.jsx)(i.pd,{...f("postalCode"),placeholder:"75001",error:null==(p=k.postalCode)?void 0:p.message})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Pays *"}),(0,t.jsxs)(i.l6,{...f("country"),error:null==(h=k.country)?void 0:h.message,children:[(0,t.jsx)("option",{value:"France",children:"France"}),(0,t.jsx)("option",{value:"Belgique",children:"Belgique"}),(0,t.jsx)("option",{value:"Suisse",children:"Suisse"}),(0,t.jsx)("option",{value:"Luxembourg",children:"Luxembourg"}),(0,t.jsx)("option",{value:"Monaco",children:"Monaco"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Instructions de livraison (optionnel)"}),(0,t.jsx)(i.TM,{...f("deliveryInstructions"),placeholder:"Laisser devant la porte, sonner chez le voisin, etc.",rows:3})]}),(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800 mb-3",children:"Options de livraison"}),(0,t.jsxs)("div",{className:"space-y-3",children:[(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",name:"deliveryOption",value:"standard",defaultChecked:!0,className:"text-pink-500 focus:ring-pink-500"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-medium text-gray-800",children:"Livraison standard"}),(0,t.jsx)("span",{className:"text-green-600 font-medium",children:"Gratuite"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"3-5 jours ouvr\xe9s"})]})]}),(0,t.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,t.jsx)("input",{type:"radio",name:"deliveryOption",value:"express",className:"text-pink-500 focus:ring-pink-500"}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"font-medium text-gray-800",children:"Livraison express"}),(0,t.jsx)("span",{className:"text-gray-800 font-medium",children:"4,99 €"})]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"24-48h"})]})]})]})]}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,t.jsx)("button",{type:"button",onClick:N,className:"text-gray-600 hover:text-gray-800 transition-colors",children:"← Retour au panier"}),(0,t.jsx)(i.$n,{type:"submit",variant:"primary",size:"lg",disabled:!P||j,className:"min-w-[200px]",children:j?(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(l.P.span,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:"\uD83C\uDF6D"}),"Traitement..."]}):(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83D\uDCB3"}),"Continuer vers le paiement"]})})]})]})}},4100:(e,s,a)=>{a.d(s,{s:()=>r});var t=a(5936),i=a(6953);a(5180);let r=e=>{let{currentStep:s}=e,a=[{id:"shipping",name:"Livraison",icon:"\uD83D\uDCE6",description:"Adresse de livraison"},{id:"payment",name:"Paiement",icon:"\uD83D\uDCB3",description:"M\xe9thode de paiement"},{id:"confirmation",name:"Confirmation",icon:"✅",description:"Commande confirm\xe9e"}],r=a.findIndex(e=>e.id===s);return(0,t.jsxs)("div",{className:"w-full",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute top-1/2 left-0 right-0 h-1 bg-gray-200 rounded-full transform -translate-y-1/2"}),(0,t.jsx)(i.P.div,{className:"absolute top-1/2 left-0 h-1 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full transform -translate-y-1/2",initial:{width:"0%"},animate:{width:0===r?"0%":1===r?"50%":"100%"},transition:{duration:.5,ease:"easeInOut"}}),(0,t.jsx)("div",{className:"relative flex justify-between",children:a.map((e,s)=>{let a=s<r,n=s===r;return(0,t.jsxs)(i.P.div,{className:"flex flex-col items-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:[(0,t.jsxs)(i.P.div,{className:"\n                    relative w-12 h-12 rounded-full flex items-center justify-center text-lg font-semibold\n                    ".concat(a?"bg-gradient-to-r from-pink-500 to-orange-500 text-white":n?"bg-white border-2 border-pink-500 text-pink-500 shadow-lg":"bg-gray-200 text-gray-400","\n                  "),whileHover:{scale:1.05},animate:n?{boxShadow:["0 0 0 0 rgba(236, 72, 153, 0.4)","0 0 0 10px rgba(236, 72, 153, 0)","0 0 0 0 rgba(236, 72, 153, 0)"]}:{},transition:{boxShadow:{duration:2,repeat:1/0},scale:{duration:.2}},children:[a?(0,t.jsx)(i.P.span,{initial:{scale:0},animate:{scale:1},transition:{delay:.2},children:"✓"}):(0,t.jsx)(i.P.span,{animate:n?{scale:[1,1.2,1],rotate:[0,5,-5,0]}:{},transition:{duration:2,repeat:1/0},children:e.icon}),n&&(0,t.jsx)(i.P.div,{className:"absolute -inset-1 rounded-full border-2 border-pink-300",animate:{scale:[1,1.2,1],opacity:[.5,1,.5]},transition:{duration:2,repeat:1/0}})]}),(0,t.jsxs)(i.P.div,{className:"mt-3 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.1*s+.2},children:[(0,t.jsx)("h4",{className:"\n                    text-sm font-semibold\n                    ".concat(a||n?"text-gray-800":"text-gray-400","\n                  "),children:e.name}),(0,t.jsx)("p",{className:"\n                    text-xs mt-1\n                    ".concat(a||n?"text-gray-600":"text-gray-400","\n                  "),children:e.description})]}),(0,t.jsx)("div",{className:"md:hidden mt-1",children:(0,t.jsxs)("span",{className:"\n                    text-xs px-2 py-1 rounded-full\n                    ".concat(a?"bg-green-100 text-green-600":n?"bg-pink-100 text-pink-600":"bg-gray-100 text-gray-400","\n                  "),children:[s+1,"/3"]})})]},e.id)})})]}),(0,t.jsx)(i.P.div,{className:"md:hidden mt-6 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},children:(0,t.jsxs)("div",{className:"bg-gray-50 rounded-lg p-4",children:[(0,t.jsxs)("h3",{className:"font-semibold text-gray-800 mb-1",children:["\xc9tape ",r+1," sur ",a.length]}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:a[r].description}),(0,t.jsxs)("div",{className:"mt-3",children:[(0,t.jsxs)("div",{className:"flex justify-between text-xs text-gray-500 mb-1",children:[(0,t.jsx)("span",{children:"Progression"}),(0,t.jsxs)("span",{children:[Math.round((r+1)/a.length*100),"%"]})]}),(0,t.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2",children:(0,t.jsx)(i.P.div,{className:"bg-gradient-to-r from-pink-500 to-orange-500 h-2 rounded-full",initial:{width:"0%"},animate:{width:"".concat((r+1)/a.length*100,"%")},transition:{duration:.5,ease:"easeInOut"}})})]})]})}),(0,t.jsx)(i.P.div,{className:"hidden md:block mt-6 text-center",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{delay:.6},children:(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["⏱️ Temps estim\xe9 restant :"," ","shipping"===s?"2-3 minutes":"payment"===s?"1-2 minutes":"Termin\xe9 !"]})})]})}},6489:(e,s,a)=>{a.d(s,{b:()=>o});var t=a(5936),i=a(9084),r=a(1774),n=a(5156),l=a(6953),d=a(2096),c=a(5180);let o=e=>{let{onBack:s,onSuccess:a,isProcessing:o,setIsProcessing:m}=e,{cart:x,clearCart:u}=(0,n.useCart)(),{customer:p}=(0,n.useCustomer)(),{addNotification:h}=(0,n.useNotifications)(),[g,j]=(0,c.useState)("card"),[y,v]=(0,c.useState)({number:"",expiry:"",cvc:"",name:""}),b=(e,s)=>{let a=s;"number"===e?(a=s.replace(/\s/g,"").replace(/(.{4})/g,"$1 ").trim()).length>19&&(a=a.slice(0,19)):"expiry"===e?(a=s.replace(/\D/g,"").replace(/(\d{2})(\d)/,"$1/$2")).length>5&&(a=a.slice(0,5)):"cvc"===e&&(a=s.replace(/\D/g,"").slice(0,4)),v(s=>({...s,[e]:a}))},N=()=>{let{number:e,expiry:s,cvc:a,name:t}=y;return!e||e.replace(/\s/g,"").length<16?(h({type:"error",title:"Paiement",message:"Num\xe9ro de carte invalide"}),!1):!s||s.length<5?(h({type:"error",title:"Paiement",message:"Date d'expiration invalide"}),!1):!a||a.length<3?(h({type:"error",title:"Paiement",message:"Code CVC invalide"}),!1):!!t.trim()||(h({type:"error",title:"Paiement",message:"Nom du titulaire requis"}),!1)},f=async()=>{if("card"!==g||N()){m(!0);try{if(0===x.items.length)return void h({type:"error",title:"Panier vide",message:"Votre panier est vide. Ajoutez des produits avant de proc\xe9der au paiement."});let e=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({...(null==p?void 0:p.id)&&{customerId:p.id},items:x.items.map(e=>({productId:e.productId,variantId:e.variantId,quantity:e.quantity})),shippingAddress:{firstName:(null==p?void 0:p.firstName)||"Client",lastName:(null==p?void 0:p.lastName)||"Deltagum",email:(null==p?void 0:p.email)||"<EMAIL>",phone:(null==p?void 0:p.phone)||"0123456789",street:(null==p?void 0:p.address)||"123 Rue de la Livraison",city:(null==p?void 0:p.city)||"Paris",postalCode:(null==p?void 0:p.postalCode)||"75001",country:"France"},totalAmount:x.totalAmount})});if(!e.ok)throw Error("Erreur lors de la cr\xe9ation de la commande");let{order:s}=await e.json(),a=await fetch("/api/checkout/session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:s.id})});if(!a.ok)throw Error("Erreur lors de la cr\xe9ation de la session de paiement");let{data:t}=await a.json();if(t.url)localStorage.setItem("deltagum_pending_order",JSON.stringify({orderId:s.id,cartItems:x.items,timestamp:Date.now()})),window.location.href=t.url;else throw Error("URL de paiement non re\xe7ue")}catch(e){console.error("Erreur de paiement:",e),h({type:"error",title:"Erreur de paiement",message:e instanceof Error?e.message:"Une erreur est survenue lors du paiement"}),m(!1)}}};return(0,t.jsxs)(l.P.div,{className:"space-y-6",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800 mb-4",children:"Choisissez votre m\xe9thode de paiement"}),(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-3",children:[{id:"card",name:"Carte bancaire",icon:"\uD83D\uDCB3",description:"Visa, Mastercard, American Express",available:!0},{id:"paypal",name:"PayPal",icon:"\uD83C\uDD7F️",description:"Paiement s\xe9curis\xe9 avec PayPal",available:!0},{id:"apple_pay",name:"Apple Pay",icon:"\uD83C\uDF4E",description:"Paiement rapide avec Touch ID",available:!1},{id:"google_pay",name:"Google Pay",icon:"\uD83D\uDD35",description:"Paiement rapide avec Google",available:!1}].map(e=>(0,t.jsx)(l.P.button,{onClick:()=>e.available&&j(e.id),disabled:!e.available,className:"\n                p-4 rounded-lg border-2 text-left transition-all duration-200\n                ".concat(g===e.id?"border-pink-500 bg-pink-50":e.available?"border-gray-200 hover:border-gray-300 bg-white":"border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed","\n              "),whileHover:e.available?{scale:1.02}:{},whileTap:e.available?{scale:.98}:{},children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-2xl",children:e.icon}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h5",{className:"font-medium text-gray-800",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:e.description}),!e.available&&(0,t.jsx)("p",{className:"text-xs text-gray-400 mt-1",children:"Bient\xf4t disponible"})]}),g===e.id&&(0,t.jsx)(l.P.div,{className:"w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:300},children:(0,t.jsx)("span",{className:"text-white text-xs",children:"✓"})})]})},e.id))})]}),(0,t.jsxs)(d.N,{mode:"wait",children:["card"===g&&(0,t.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(i.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-800",children:"Informations de carte"}),(0,t.jsx)(i.$n,{type:"button",variant:"outline",size:"sm",onClick:()=>{v({number:"4242 4242 4242 4242",expiry:"12/25",cvc:"123",name:(null==p?void 0:p.firstName)&&(null==p?void 0:p.lastName)?"".concat(p.firstName," ").concat(p.lastName):"Marie Dupont"})},className:"text-xs",children:"Donn\xe9es de test"})]}),(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Num\xe9ro de carte"}),(0,t.jsx)("input",{type:"text",value:y.number,onChange:e=>b("number",e.target.value),placeholder:"1234 5678 9012 3456",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Date d'expiration"}),(0,t.jsx)("input",{type:"text",value:y.expiry,onChange:e=>b("expiry",e.target.value),placeholder:"MM/YY",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"CVC"}),(0,t.jsx)("input",{type:"text",value:y.cvc,onChange:e=>b("cvc",e.target.value),placeholder:"123",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom du titulaire"}),(0,t.jsx)("input",{type:"text",value:y.name,onChange:e=>b("name",e.target.value),placeholder:"Marie Dupont",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent"})]})]})]})})},"card-form"),"paypal"===g&&(0,t.jsx)(l.P.div,{initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.3},children:(0,t.jsx)(i.Zp,{children:(0,t.jsxs)(i.Wu,{className:"p-6 text-center",children:[(0,t.jsx)("div",{className:"text-4xl mb-4",children:"\uD83C\uDD7F️"}),(0,t.jsx)("h4",{className:"font-medium text-gray-800 mb-2",children:"Paiement PayPal"}),(0,t.jsx)("p",{className:"text-gray-600 text-sm",children:"Vous serez redirig\xe9 vers PayPal pour finaliser votre paiement de mani\xe8re s\xe9curis\xe9e."})]})})},"paypal-info")]}),(0,t.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"text-green-600 text-xl",children:"\uD83D\uDD12"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-green-800",children:"Paiement 100% s\xe9curis\xe9"}),(0,t.jsx)("p",{className:"text-sm text-green-600",children:"Vos donn\xe9es sont prot\xe9g\xe9es par un cryptage SSL 256-bit"})]})]})}),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,t.jsx)(i.$n,{variant:"outline",onClick:s,disabled:o,children:"← Retour aux informations"}),(0,t.jsx)(i.$n,{variant:"primary",size:"lg",onClick:f,disabled:o,className:"min-w-[200px]",children:o?(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)(l.P.span,{className:"mr-2",animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"},children:"\uD83D\uDCB3"}),"Traitement en cours..."]}):(0,t.jsxs)("span",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"mr-2",children:"\uD83D\uDCB0"}),"Payer"," ",(0,r.$g)(x.totalAmount+.2*x.totalAmount+(x.totalAmount>=50?0:5.99))]})})]})]})}},8413:(e,s,a)=>{a.d(s,{D:()=>d});var t=a(5936),i=a(9084),r=a(1774),n=a(5156),l=a(6953);a(5180);let d=()=>{let{cart:e}=(0,n.useCart)(),s={strawberry:{emoji:"\uD83C\uDF53",color:"text-pink-600",bg:"bg-pink-50"},blueberry:{emoji:"\uD83E\uDED0",color:"text-blue-600",bg:"bg-blue-50"},apple:{emoji:"\uD83C\uDF4F",color:"text-green-600",bg:"bg-green-50"}},a=e.totalAmount,d=.2*a,c=a>=50?0:5.99,o=a+d+c;return(0,t.jsxs)(i.Zp,{children:[(0,t.jsx)(i.aR,{children:(0,t.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"R\xe9capitulatif de commande"})}),(0,t.jsxs)(i.Wu,{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("h4",{className:"font-medium text-gray-800 border-b border-gray-200 pb-2",children:["Articles command\xe9s (",e.totalItems,")"]}),(0,t.jsx)("div",{className:"space-y-3 max-h-60 overflow-y-auto",children:e.items.map(e=>{let a=s[e.flavor.toLowerCase()]||{emoji:"\uD83C\uDF6D",color:"text-gray-600",bg:"bg-gray-50"};return(0,t.jsxs)(l.P.div,{className:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg",initial:{opacity:0,y:10},animate:{opacity:1,y:0},transition:{duration:.2},children:[(0,t.jsx)("div",{className:"w-10 h-10 rounded-lg ".concat(a.bg," flex items-center justify-center text-lg"),children:a.emoji}),(0,t.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,t.jsx)("h5",{className:"font-medium text-gray-800 text-sm truncate",children:e.name}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(i.Ex,{variant:"secondary",size:"sm",className:"".concat(a.color," border-current text-xs"),children:e.flavor}),(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["Qt\xe9: ",e.quantity]})]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsx)("div",{className:"font-medium text-gray-800 text-sm",children:(0,r.$g)(e.price*e.quantity)})})]},e.id)})})]}),(0,t.jsxs)("div",{className:"space-y-3 border-t border-gray-200 pt-4",children:[(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Sous-total"}),(0,t.jsx)("span",{className:"font-medium",children:(0,r.$g)(a)})]}),!1,(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Livraison"}),(0,t.jsx)("span",{className:"font-medium",children:0===c?(0,t.jsx)("span",{className:"text-green-600",children:"Gratuite"}):(0,r.$g)(c)})]}),d>0&&(0,t.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"TVA (20%)"}),(0,t.jsx)("span",{className:"font-medium",children:(0,r.$g)(d)})]})]}),(0,t.jsx)("div",{className:"border-t border-gray-200 pt-4",children:(0,t.jsxs)("div",{className:"flex justify-between text-lg font-bold",children:[(0,t.jsx)("span",{children:"Total \xe0 payer"}),(0,t.jsx)(l.P.span,{className:"text-pink-600",initial:{scale:1.1},animate:{scale:1},transition:{duration:.2},children:(0,r.$g)(o)},o)]})}),(0,t.jsx)("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:(0,t.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,t.jsx)("span",{className:"text-blue-600 text-lg",children:"\uD83D\uDE9A"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-blue-800 text-sm",children:"Livraison estim\xe9e"}),(0,t.jsx)("p",{className:"text-blue-600 text-sm",children:0===c?"3-5 jours ouvr\xe9s":"24-48h"}),(0,t.jsx)("p",{className:"text-blue-600 text-xs mt-1",children:"Suivi de commande par email"})]})]})}),(0,t.jsxs)("div",{className:"grid grid-cols-2 gap-3 text-center text-xs text-gray-500",children:[(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("span",{className:"text-lg mb-1",children:"\uD83D\uDD12"}),(0,t.jsx)("span",{children:"Paiement s\xe9curis\xe9"})]}),(0,t.jsxs)("div",{className:"flex flex-col items-center",children:[(0,t.jsx)("span",{className:"text-lg mb-1",children:"↩️"}),(0,t.jsx)("span",{children:"Retour gratuit"})]})]}),(0,t.jsx)("div",{className:"bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg p-3",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm font-medium text-gray-800",children:"Points de fid\xe9lit\xe9"}),(0,t.jsxs)("p",{className:"text-xs text-gray-600",children:["Vous gagnerez ",Math.floor(o/100)," points"]})]}),(0,t.jsxs)(i.Ex,{variant:"info",size:"sm",children:["+",Math.floor(o/100)," \uD83C\uDF81"]})]})}),(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("p",{className:"text-xs text-gray-500 mb-2",children:"Une question sur votre commande ?"}),(0,t.jsx)("button",{className:"text-xs text-pink-600 hover:text-pink-700 font-medium",children:"Contacter le support \uD83D\uDCAC"})]})]})]})}}}]);