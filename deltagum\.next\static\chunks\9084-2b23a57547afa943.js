"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9084],{1362:(e,r,t)=>{t.d(r,{N:()=>n,c:()=>l});var o=t(2446),a=t(5978);let n=(0,o.v)()((0,a.Zr)((e,r)=>({customer:null,loading:!1,error:null,setCustomer:r=>{e({customer:r,error:null})},updateCustomer:async t=>{let o=r().customer;if(!o)return void e({error:"Aucun client connect\xe9"});e({loading:!0,error:null});try{let r=await fetch("/api/customers/".concat(o.id),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok)throw Error("HTTP error! status: ".concat(r.status));let a=await r.json();if(a.success)e({customer:a.data,loading:!1});else throw Error(a.error||"Erreur lors de la mise \xe0 jour")}catch(r){e({error:r instanceof Error?r.message:"Erreur inconnue",loading:!1})}},clearCustomer:()=>{e({customer:null,error:null})}}),{name:"deltagum-customer",storage:(0,a.KU)(()=>localStorage),partialize:e=>({customer:e.customer})})),l=()=>{let{customer:e,loading:r,error:t,setCustomer:o,updateCustomer:a,clearCustomer:l}=n();return{customer:e,loading:r,error:t,setCustomer:o,updateCustomer:a,clearCustomer:l}}},1774:(e,r,t)=>{t.d(r,{wy:()=>s,te:()=>i,cn:()=>n,$g:()=>l});var o=t(5580),a=t(7612);function n(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return(0,a.QP)((0,o.$)(r))}function l(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"EUR";return new Intl.NumberFormat("fr-FR",{style:"currency",currency:r}).format(e)}t(3466).env.NEXTAUTH_URL,t(3466);function i(e){return e.reduce((e,r)=>e+r.price*r.quantity,0)}function s(e){return e.reduce((e,r)=>e+r.quantity,0)}},4657:(e,r,t)=>{t.d(r,{K:()=>o,Q:()=>a});let o=(0,t(2446).v)((e,r)=>({products:[],selectedProduct:null,selectedVariant:null,loading:!0,error:null,fetchProducts:async function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;e({loading:!0,error:null});try{let r=new AbortController,t=setTimeout(()=>r.abort(),1e4),o=await fetch("/api/products",{cache:"no-store",headers:{"Content-Type":"application/json"},signal:r.signal});if(clearTimeout(t),!o.ok)throw Error("Erreur HTTP: ".concat(o.status," - ").concat(o.statusText));let a=await o.json();if(a.success&&a.data&&Array.isArray(a.data.products))console.log("Produits charg\xe9s avec succ\xe8s:",a.data.products.length),e({products:a.data.products,loading:!1,error:null});else throw Error(a.error||"Format de r\xe9ponse invalide")}catch(o){if(console.error("Erreur lors du chargement des produits:",o),t<2){console.log("Tentative de rechargement ".concat(t+1,"/2...")),setTimeout(()=>{let e=r();e.fetchProducts&&e.fetchProducts(t+1)},1e3*(t+1));return}e({error:o instanceof Error?o.message:"Erreur de connexion",loading:!1})}},selectProduct:r=>{var t;let o=null==(t=r.variants)?void 0:t[0],a=o?{...o,product:r}:null;e({selectedProduct:r,selectedVariant:a})},selectVariant:r=>{e({selectedVariant:r})}}));setTimeout(()=>{o.getState().fetchProducts()},100);let a=()=>{let{products:e,selectedProduct:r,selectedVariant:t,loading:a,error:n,fetchProducts:l,selectProduct:i,selectVariant:s}=o();return{products:e,selectedProduct:r,selectedVariant:t,loading:a,error:n,fetchProducts:l,selectProduct:i,selectVariant:s}}},5156:(e,r,t)=>{t.r(r),t.d(r,{useAuth:()=>o.A,useCart:()=>a.x,useCartStore:()=>a.x,useCheckoutModal:()=>l,useCustomer:()=>i.c,useCustomerStore:()=>i.N,useNotificationStore:()=>s,useNotifications:()=>d,useProduct:()=>c.K,useProductStore:()=>c.K,useProducts:()=>c.Q,useUI:()=>m,useUIStore:()=>u});var o=t(7244),a=t(5884),n=t(2446);let l=(0,n.v)(e=>({isOpen:!1,openModal:()=>e({isOpen:!0}),closeModal:()=>e({isOpen:!1})}));var i=t(1362);let s=(0,n.v)((e,r)=>({notifications:[],addNotification:t=>{let o="notification-".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),a={...t,id:o,duration:t.duration||5e3};e(e=>({notifications:[...e.notifications,a]})),a.duration&&a.duration>0&&setTimeout(()=>{r().removeNotification(o)},a.duration)},removeNotification:r=>{e(e=>({notifications:e.notifications.filter(e=>e.id!==r)}))},clearNotifications:()=>{e({notifications:[]})}})),d=()=>{let{notifications:e,addNotification:r,removeNotification:t,clearNotifications:o}=s();return{notifications:e,addNotification:r,removeNotification:t,clearNotifications:o,showSuccess:(e,t,o)=>{r({type:"success",title:e,message:t,...o})},showError:(e,t,o)=>{r({type:"error",title:e,message:t,...o})},showWarning:(e,t,o)=>{r({type:"warning",title:e,message:t,...o})},showInfo:(e,t,o)=>{r({type:"info",title:e,message:t,...o})}}};var c=t(4657);let u=(0,n.v)(e=>({isCartOpen:!1,isMenuOpen:!1,isCheckoutOpen:!1,isAuthModalOpen:!1,isLoading:!1,loadingMessage:"",isMobile:!1,scrollY:0,openCart:()=>e({isCartOpen:!0}),closeCart:()=>e({isCartOpen:!1}),toggleCart:()=>e(e=>({isCartOpen:!e.isCartOpen})),openMenu:()=>e({isMenuOpen:!0}),closeMenu:()=>e({isMenuOpen:!1}),toggleMenu:()=>e(e=>({isMenuOpen:!e.isMenuOpen})),openCheckout:()=>e({isCheckoutOpen:!0,isCartOpen:!1}),closeCheckout:()=>e({isCheckoutOpen:!1}),openAuthModal:()=>e({isAuthModalOpen:!0}),closeAuthModal:()=>e({isAuthModalOpen:!1}),setLoading:function(r){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";return e({isLoading:r,loadingMessage:t})},setIsMobile:r=>e({isMobile:r}),setScrollY:r=>e({scrollY:r}),closeAllModals:()=>e({isCartOpen:!1,isMenuOpen:!1,isCheckoutOpen:!1,isAuthModalOpen:!1})})),m=()=>{let{isCartOpen:e,isMenuOpen:r,isCheckoutOpen:t,isAuthModalOpen:o,isLoading:a,loadingMessage:n,isMobile:l,scrollY:i,openCart:s,closeCart:d,toggleCart:c,openMenu:m,closeMenu:g,toggleMenu:p,openCheckout:h,closeCheckout:x,openAuthModal:f,closeAuthModal:b,setLoading:y,setIsMobile:w,setScrollY:v,closeAllModals:N}=u();return{isCartOpen:e,isMenuOpen:r,isCheckoutOpen:t,isAuthModalOpen:o,isLoading:a,loadingMessage:n,isMobile:l,scrollY:i,openCart:s,closeCart:d,toggleCart:c,openMenu:m,closeMenu:g,toggleMenu:p,openCheckout:h,closeCheckout:x,openAuthModal:f,closeAuthModal:b,setLoading:y,setIsMobile:w,setScrollY:v,closeAllModals:N}}},5884:(e,r,t)=>{t.d(r,{x:()=>i});var o=t(1774),a=t(2446),n=t(5978);let l={items:[],totalItems:0,totalAmount:0},i=(0,a.v)()((0,n.Zr)((e,r)=>({cart:l,addItem:r=>{e(e=>{let t,a=e.cart.items.findIndex(e=>e.productId===r.productId&&e.variantId===r.variantId);if(a>=0)t=e.cart.items.map((e,t)=>t===a?{...e,quantity:e.quantity+r.quantity}:e);else{let o={...r,id:"".concat(r.productId,"-").concat(r.variantId,"-").concat(Date.now())};t=[...e.cart.items,o]}let n=(0,o.wy)(t),l=(0,o.te)(t);return{cart:{items:t,totalItems:n,totalAmount:l}}})},removeItem:r=>{e(e=>{let t=e.cart.items.filter(e=>e.id!==r),a=(0,o.wy)(t),n=(0,o.te)(t);return{cart:{items:t,totalItems:a,totalAmount:n}}})},updateQuantity:(t,a)=>{if(a<=0)return void r().removeItem(t);e(e=>{let r=e.cart.items.map(e=>e.id===t?{...e,quantity:a}:e),n=(0,o.wy)(r),l=(0,o.te)(r);return{cart:{items:r,totalItems:n,totalAmount:l}}})},clearCart:()=>{e({cart:l})},getTotalItems:()=>r().cart.totalItems,getTotalAmount:()=>r().cart.totalAmount}),{name:"deltagum-cart",storage:(0,n.KU)(()=>localStorage),partialize:e=>({cart:e.cart})}))},6652:(e,r,t)=>{t.d(r,{C9:()=>n,Rf:()=>s,Vd:()=>a,Yo:()=>l,bK:()=>i,nM:()=>d,qG:()=>o});let o={initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.3}},a={initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.4}},n={initial:{opacity:0,y:-20},animate:{opacity:1,y:0},exit:{opacity:0,y:20},transition:{duration:.4}},l={initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},exit:{opacity:0,scale:.8},transition:{duration:.3}},i={animate:{transition:{staggerChildren:.1}}},s={initial:{opacity:0,y:20},animate:{opacity:1,y:0,transition:{duration:.4}}},d={initial:{opacity:0,x:300},animate:{opacity:1,x:0,transition:{duration:.3}},exit:{opacity:0,x:300,transition:{duration:.2}}}},7244:(e,r,t)=>{t.d(r,{A:()=>n});var o=t(2446),a=t(5978);let n=(0,o.v)()((0,a.Zr)((e,r)=>({user:null,isAuthenticated:!1,isLoading:!1,login:async(r,t)=>{e({isLoading:!0});try{let o=await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},credentials:"include",body:JSON.stringify({email:r,password:t})}),a=await o.json();if(!o.ok)throw Error(a.error||"Erreur de connexion");e({user:a.user,isAuthenticated:!0,isLoading:!1})}catch(r){throw e({isLoading:!1}),r}},register:async r=>{e({isLoading:!0});try{let t=await fetch("/api/auth/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(r)}),o=await t.json();if(!t.ok)throw Error(o.error||"Erreur d'inscription");e({user:o.user,isAuthenticated:!0,isLoading:!1})}catch(r){throw e({isLoading:!1}),r}},logout:async()=>{try{await fetch("/api/auth/logout",{method:"POST"}),e({user:null,isAuthenticated:!1})}catch(r){console.error("Erreur lors de la d\xe9connexion:",r),e({user:null,isAuthenticated:!1})}},checkAuth:async()=>{try{let r=await fetch("/api/auth/me",{credentials:"include"});if(r.ok){let t=await r.json();e({user:t.user,isAuthenticated:!0})}else e({user:null,isAuthenticated:!1})}catch(r){console.error("Erreur lors de la v\xe9rification de l'authentification:",r),e({user:null,isAuthenticated:!1})}},setUser:r=>{e({user:r,isAuthenticated:!!r})},setLoading:r=>{e({isLoading:r})},isAdmin:()=>{let{user:e}=r();return(null==e?void 0:e.role)==="ADMIN"}}),{name:"auth-storage",partialize:e=>({user:e.user,isAuthenticated:e.isAuthenticated})}))},9084:(e,r,t)=>{t.d(r,{Ex:()=>v,$n:()=>i,Zp:()=>d,Wu:()=>m,aR:()=>c,ZB:()=>u,pd:()=>b,aF:()=>h,cw:()=>x,jl:()=>f,l6:()=>w,TM:()=>y,N9:()=>k});var o=t(5936),a=t(1774),n=t(6953),l=t(5180);let i=l.forwardRef((e,r)=>{let{className:t,variant:l="primary",size:i="md",loading:s=!1,icon:d,iconPosition:c="left",fullWidth:u=!1,rounded:m=!1,children:g,disabled:p,...h}=e,x=(0,a.cn)(["inline-flex items-center justify-center font-medium transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-2","disabled:opacity-50 disabled:cursor-not-allowed","relative overflow-hidden"],{primary:["bg-gradient-to-r from-pink-500 to-orange-400","hover:from-pink-600 hover:to-orange-500","text-white shadow-lg hover:shadow-xl","focus:ring-pink-500"],secondary:["bg-gradient-to-r from-purple-500 to-blue-500","hover:from-purple-600 hover:to-blue-600","text-white shadow-lg hover:shadow-xl","focus:ring-purple-500"],outline:["border-2 border-pink-500 text-pink-500","hover:bg-pink-500 hover:text-white","focus:ring-pink-500"],ghost:["text-gray-700 hover:text-pink-500","hover:bg-pink-50","focus:ring-pink-500"],danger:["bg-red-500 hover:bg-red-600","text-white shadow-lg hover:shadow-xl","focus:ring-red-500"]}[l],{sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg",xl:"px-8 py-4 text-xl"}[i],{sm:m?"rounded-full":"rounded-md",md:m?"rounded-full":"rounded-lg",lg:m?"rounded-full":"rounded-xl",xl:m?"rounded-full":"rounded-2xl"}[i],u&&"w-full",t),f={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6",xl:"w-7 h-7"},{onDrag:b,onDragStart:y,onDragEnd:w,onAnimationStart:v,onAnimationEnd:N,onAnimationIteration:j,...k}=h;return(0,o.jsx)(n.P.button,{ref:r,className:x,disabled:p||s,whileHover:p||s?void 0:{scale:1.02},whileTap:p||s?void 0:{scale:.98},...k,children:s?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)(()=>(0,o.jsx)(n.P.div,{className:(0,a.cn)("border-2 border-current border-t-transparent rounded-full",f[i]),animate:{rotate:360},transition:{duration:1,repeat:1/0,ease:"linear"}}),{}),g&&(0,o.jsx)("span",{className:"ml-2",children:"Chargement..."})]}):d&&"left"===c?(0,o.jsxs)(o.Fragment,{children:[(0,o.jsx)("span",{className:(0,a.cn)(f[i],g&&"mr-2"),children:d}),g]}):d&&"right"===c?(0,o.jsxs)(o.Fragment,{children:[g,(0,o.jsx)("span",{className:(0,a.cn)(f[i],g&&"ml-2"),children:d})]}):g})});i.displayName="Button";var s=t(6652);let d=l.forwardRef((e,r)=>{let{className:t,variant:l="default",padding:i="md",rounded:d="lg",hover:c=!1,clickable:u=!1,gradient:m=!1,children:g,...p}=e,h=(0,a.cn)(["relative overflow-hidden transition-all duration-300"],{default:["bg-white border border-gray-200","shadow-sm"],elevated:["bg-white","shadow-lg hover:shadow-xl"],outlined:["bg-white border-2 border-gray-300","hover:border-pink-300"],glass:["bg-white/80 backdrop-blur-sm","border border-white/20","shadow-lg"]}[l],{none:"",sm:"p-3",md:"p-4",lg:"p-6",xl:"p-8"}[i],{none:"",sm:"rounded-sm",md:"rounded-md",lg:"rounded-lg",xl:"rounded-xl",full:"rounded-full"}[d],c?["hover:shadow-lg hover:-translate-y-1","hover:scale-105"]:[],u?["cursor-pointer","hover:shadow-lg hover:-translate-y-1","active:scale-95"]:[],m?["bg-gradient-to-br from-pink-50 to-orange-50","border-gradient-to-r from-pink-200 to-orange-200"]:[],t),x=n.P.div;return(0,o.jsx)(x,{ref:r,className:h,initial:!1,animate:s.Yo.animate,whileHover:c||u?{y:-4,scale:1.02}:void 0,whileTap:u?{scale:.98}:void 0,...(e=>{let{onAnimationStart:r,onAnimationEnd:t,onAnimationIteration:o,onDrag:a,onDragStart:n,onDragEnd:l,...i}=e;return i})(p),children:g})});d.displayName="Card";let c=l.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6 pb-0",t),...n})});c.displayName="CardHeader";let u=l.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("h3",{ref:r,className:(0,a.cn)("text-lg font-semibold leading-none tracking-tight",t),...n})});u.displayName="CardTitle",l.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("p",{ref:r,className:(0,a.cn)("text-sm text-gray-600",t),...n})}).displayName="CardDescription";let m=l.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",t),...n})});m.displayName="CardContent",l.forwardRef((e,r)=>{let{className:t,...n}=e;return(0,o.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",t),...n})}).displayName="CardFooter",t(5884),t(1362),t(4657);var g=t(2096),p=t(2957);let h=e=>{let{isOpen:r,onClose:t,title:i,description:s,size:d="md",closeOnOverlayClick:c=!0,closeOnEscape:u=!0,showCloseButton:m=!0,children:h,className:x,overlayClassName:f}=e;(0,l.useEffect)(()=>{if(!u)return;let e=e=>{"Escape"===e.key&&r&&t()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[r,t,u]);let b=function(e){let r=(0,l.useRef)(null);return(0,l.useEffect)(()=>{let t=t=>{r.current&&!r.current.contains(t.target)&&e()};return document.addEventListener("mousedown",t),()=>{document.removeEventListener("mousedown",t)}},[e]),r}(()=>{c&&r&&t()});(0,l.useEffect)(()=>(r?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[r]);let y=(0,a.cn)("fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4","bg-black/50 backdrop-blur-sm",f),w=(0,a.cn)("relative w-full bg-white rounded-lg sm:rounded-xl shadow-2xl","max-h-[95vh] sm:max-h-[90vh] overflow-y-auto",{sm:"max-w-sm sm:max-w-md",md:"max-w-md sm:max-w-lg",lg:"max-w-lg sm:max-w-xl lg:max-w-2xl",xl:"max-w-xl sm:max-w-2xl lg:max-w-4xl",full:"max-w-full mx-2 sm:mx-4"}[d],x);if(!r)return null;let v=(0,o.jsx)(g.N,{children:r&&(0,o.jsx)(n.P.div,{className:y,initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},transition:{duration:.2},children:(0,o.jsxs)(n.P.div,{ref:b,className:w,initial:{opacity:0,scale:.95,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.95,y:20},transition:{duration:.2},children:[(i||m)&&(0,o.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,o.jsxs)("div",{children:[i&&(0,o.jsx)("h2",{className:"text-xl font-semibold text-gray-900",children:i}),s&&(0,o.jsx)("p",{className:"mt-1 text-sm text-gray-600",children:s})]}),m&&(0,o.jsx)("button",{onClick:t,className:"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors","aria-label":"Fermer",children:(0,o.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,o.jsx)("div",{className:"p-6",children:h})]})})});return(0,p.createPortal)(v,document.body)},x=e=>{let{children:r,className:t}=e;return(0,o.jsx)("div",{className:(0,a.cn)("p-6",t),children:r})},f=e=>{let{children:r,className:t}=e;return(0,o.jsx)("div",{className:(0,a.cn)("flex items-center justify-end gap-3 p-6 border-t border-gray-200",t),children:r})},b=l.forwardRef((e,r)=>{let{className:t,type:i="text",label:s,error:d,helperText:c,leftIcon:u,rightIcon:m,variant:g="default",inputSize:p="md",fullWidth:h=!1,disabled:x,...f}=e,[b,y]=l.useState(!1),w=(0,a.cn)(["transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-1","disabled:opacity-50 disabled:cursor-not-allowed"],{default:["border border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",d?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""],filled:["border-0 bg-gray-50 text-gray-900","placeholder:text-gray-700","hover:bg-gray-100","focus:bg-white focus:ring-pink-500/20 focus:shadow-md","shadow-sm",d?"bg-red-50 focus:ring-red-500/20":""],outlined:["border-2 border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",d?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""]}[g],{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-base",lg:"px-5 py-3 text-lg"}[p],{sm:"rounded-md",md:"rounded-lg",lg:"rounded-xl"}[p],u&&"pl-10",m&&"pr-10",h&&"w-full",t),v={sm:"w-4 h-4",md:"w-5 h-5",lg:"w-6 h-6"};return(0,o.jsxs)("div",{className:(0,a.cn)("relative",h&&"w-full"),children:[s&&(0,o.jsx)(n.P.label,{className:(0,a.cn)("block text-sm font-medium mb-2 transition-colors",d?"text-red-700":"text-gray-700",x&&"text-gray-400"),animate:{color:b?d?"#dc2626":"#ec4899":d?"#dc2626":"#374151"},children:s}),(0,o.jsxs)("div",{className:"relative",children:[u&&(0,o.jsx)("div",{className:(0,a.cn)("absolute top-1/2 transform -translate-y-1/2 text-gray-400",{sm:"left-3",md:"left-3",lg:"left-4"}[p]),children:(0,o.jsx)("span",{className:v[p],children:u})}),(0,o.jsx)(n.P.input,{ref:r,type:i,className:w,disabled:x,onFocus:e=>{var r;y(!0),null==(r=f.onFocus)||r.call(f,e)},onBlur:e=>{var r;y(!1),null==(r=f.onBlur)||r.call(f,e)},whileFocus:{scale:1.01},...(e=>{let{onAnimationStart:r,onAnimationEnd:t,onAnimationIteration:o,onDrag:a,onDragStart:n,onDragEnd:l,...i}=e;return i})(f)}),m&&(0,o.jsx)("div",{className:(0,a.cn)("absolute top-1/2 transform -translate-y-1/2 text-gray-400",{sm:"right-3",md:"right-3",lg:"right-4"}[p]),children:(0,o.jsx)("span",{className:v[p],children:m})})]}),d&&(0,o.jsx)(n.P.p,{className:"mt-1 text-sm text-red-600",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:d}),c&&!d&&(0,o.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:c})]})});b.displayName="Input";let y=l.forwardRef((e,r)=>{let{className:t,label:i,error:s,helperText:d,variant:c="default",inputSize:u="md",fullWidth:m=!1,resize:g="vertical",disabled:p,...h}=e,[x,f]=l.useState(!1),b=(0,a.cn)(["transition-all duration-200","focus:outline-none focus:ring-2 focus:ring-offset-1","disabled:opacity-50 disabled:cursor-not-allowed","min-h-[80px]"],{default:["border border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",s?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""],filled:["border-0 bg-gray-50 text-gray-900","placeholder:text-gray-700","hover:bg-gray-100","focus:bg-white focus:ring-pink-500/20 focus:shadow-md","shadow-sm",s?"bg-red-50 focus:ring-red-500/20":""],outlined:["border-2 border-gray-300 bg-white text-gray-900","placeholder:text-gray-700","hover:border-gray-400","focus:border-pink-500 focus:ring-pink-500/20","shadow-sm",s?"border-red-500 focus:border-red-500 focus:ring-red-500/20":""]}[c],{sm:"px-3 py-2 text-sm",md:"px-4 py-2.5 text-base",lg:"px-5 py-3 text-lg"}[u],{sm:"rounded-md",md:"rounded-lg",lg:"rounded-xl"}[u],{none:"resize-none",vertical:"resize-y",horizontal:"resize-x",both:"resize"}[g],m&&"w-full",t);return(0,o.jsxs)("div",{className:(0,a.cn)("relative",m&&"w-full"),children:[i&&(0,o.jsx)(n.P.label,{className:(0,a.cn)("block text-sm font-medium mb-2 transition-colors",s?"text-red-700":"text-gray-700",p&&"text-gray-400"),animate:{color:x?s?"#dc2626":"#ec4899":s?"#dc2626":"#374151"},children:i}),(0,o.jsx)(n.P.textarea,{ref:r,className:b,disabled:p,onFocus:e=>{var r;f(!0),null==(r=h.onFocus)||r.call(h,e)},onBlur:e=>{var r;f(!1),null==(r=h.onBlur)||r.call(h,e)},whileFocus:{scale:1.01},...(e=>{let{onAnimationStart:r,onAnimationEnd:t,onAnimationIteration:o,onDrag:a,onDragStart:n,onDragEnd:l,...i}=e;return i})(h)}),s&&(0,o.jsx)(n.P.p,{className:"mt-1 text-sm text-red-600",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},exit:{opacity:0,y:-10},children:s}),d&&!s&&(0,o.jsx)("p",{className:"mt-1 text-sm text-gray-500",children:d})]})});y.displayName="Textarea";let w=(0,l.forwardRef)((e,r)=>{let{className:t,label:n,error:l,helperText:i,variant:s="default",size:d="md",children:c,...u}=e,m=l?"border-red-500 focus:border-red-500 focus:ring-red-500":"";return(0,o.jsxs)("div",{className:"w-full",children:[n&&(0,o.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:n}),(0,o.jsx)("select",{ref:r,className:(0,a.cn)("w-full rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",{default:"border-gray-300 bg-white focus:border-pink-500 focus:ring-pink-500",outline:"border-gray-300 bg-transparent focus:border-pink-500 focus:ring-pink-500",filled:"border-gray-200 bg-gray-50 focus:border-pink-500 focus:ring-pink-500"}[s],{sm:"px-3 py-2 text-sm",md:"px-4 py-3 text-base",lg:"px-5 py-4 text-lg"}[d],m,t),...u,children:c}),l&&(0,o.jsx)("p",{className:"mt-2 text-sm text-red-600",children:l}),i&&!l&&(0,o.jsx)("p",{className:"mt-2 text-sm text-gray-500",children:i})]})});w.displayName="Select";let v=l.forwardRef((e,r)=>{let{className:t,variant:l="default",size:i="md",rounded:s=!1,outline:d=!1,icon:c,iconPosition:u="left",removable:m=!1,onRemove:g,children:p,...h}=e,x={sm:"w-3 h-3",md:"w-4 h-4",lg:"w-5 h-5"},f=(0,a.cn)(["inline-flex items-center font-medium transition-all duration-200","whitespace-nowrap"],{default:d?"border border-gray-300 text-gray-700 bg-transparent hover:bg-gray-50":"bg-gray-100 text-gray-800 hover:bg-gray-200",primary:d?"border border-pink-300 text-pink-700 bg-transparent hover:bg-pink-50":"bg-gradient-to-r from-pink-500 to-orange-400 text-white hover:from-pink-600 hover:to-orange-500",secondary:d?"border border-purple-300 text-purple-700 bg-transparent hover:bg-purple-50":"bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600",success:d?"border border-green-300 text-green-700 bg-transparent hover:bg-green-50":"bg-green-100 text-green-800 hover:bg-green-200",warning:d?"border border-yellow-300 text-yellow-700 bg-transparent hover:bg-yellow-50":"bg-yellow-100 text-yellow-800 hover:bg-yellow-200",danger:d?"border border-red-300 text-red-700 bg-transparent hover:bg-red-50":"bg-red-100 text-red-800 hover:bg-red-200",info:d?"border border-blue-300 text-blue-700 bg-transparent hover:bg-blue-50":"bg-blue-100 text-blue-800 hover:bg-blue-200"}[l],{sm:"px-2 py-0.5 text-xs",md:"px-2.5 py-1 text-sm",lg:"px-3 py-1.5 text-base"}[i],{sm:s?"rounded-full":"rounded",md:s?"rounded-full":"rounded-md",lg:s?"rounded-full":"rounded-lg"}[i],t),b=e=>c&&u===e?(0,o.jsx)("span",{className:(0,a.cn)(x[i],"left"===e&&p&&"mr-1","right"===e&&p&&"ml-1"),children:c}):null,{onAnimationStart:y,onAnimationEnd:w,onAnimationIteration:v,onDrag:N,onDragStart:j,onDragEnd:k,...C}=h;return(0,o.jsxs)(n.P.div,{ref:r,className:f,initial:{opacity:0,scale:.8},animate:{opacity:1,scale:1},whileHover:{scale:1.05},whileTap:{scale:.95},...C,children:[b("left"),p,b("right"),m?(0,o.jsx)("button",{onClick:g,className:(0,a.cn)("ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors",x[i]),"aria-label":"Supprimer",children:(0,o.jsx)("svg",{className:"w-full h-full",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})}):null]})});v.displayName="Badge";var N=t(5156);let j=e=>{let{notification:r,onRemove:t}=e,{id:l,type:i,title:d,message:c,action:u}=r,m={success:{icon:"✅",bgColor:"bg-green-50",borderColor:"border-green-200",textColor:"text-green-800",iconColor:"text-green-500"},error:{icon:"❌",bgColor:"bg-red-50",borderColor:"border-red-200",textColor:"text-red-800",iconColor:"text-red-500"},warning:{icon:"⚠️",bgColor:"bg-yellow-50",borderColor:"border-yellow-200",textColor:"text-yellow-800",iconColor:"text-yellow-500"},info:{icon:"ℹ️",bgColor:"bg-blue-50",borderColor:"border-blue-200",textColor:"text-blue-800",iconColor:"text-blue-500"}}[i];return(0,o.jsxs)(n.P.div,{className:(0,a.cn)("relative flex items-start p-4 rounded-lg border shadow-lg max-w-sm w-full",m.bgColor,m.borderColor),initial:s.nM.initial,animate:s.nM.animate,exit:s.nM.exit,layout:!0,children:[(0,o.jsx)("div",{className:(0,a.cn)("flex-shrink-0 mr-3",m.iconColor),children:(0,o.jsx)("span",{className:"text-xl",children:m.icon})}),(0,o.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,o.jsx)("h4",{className:(0,a.cn)("text-sm font-medium",m.textColor),children:d}),c&&(0,o.jsx)("p",{className:(0,a.cn)("mt-1 text-sm",m.textColor,"opacity-90"),children:c}),u&&(0,o.jsx)("div",{className:"mt-2",children:(0,o.jsx)("button",{onClick:u.onClick,className:(0,a.cn)("text-sm font-medium underline hover:no-underline",m.textColor),children:u.label})})]}),(0,o.jsx)("button",{onClick:()=>t(l),className:(0,a.cn)("flex-shrink-0 ml-3 p-1 rounded-md hover:bg-black/10 transition-colors",m.textColor),"aria-label":"Fermer",children:(0,o.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,o.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})},k=()=>{let{notifications:e,removeNotification:r}=(0,N.useNotifications)();return(0,p.createPortal)((0,o.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:(0,o.jsx)(g.N,{mode:"popLayout",children:e.map(e=>(0,o.jsx)(j,{notification:e,onRemove:r},e.id))})}),document.body)};t(2085)}}]);