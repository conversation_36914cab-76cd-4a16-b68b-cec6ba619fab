(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5957],{5445:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>B});var t=a(5936),r=a(9084),l=a(7244),i=a(6953),n=a(4487),c=a(642),d=a(5087),o=a(4287),m=a(9100),x=a(4729),h=a(2212),u=a(4982),p=a(5180),g=a(9196),j=a(6411),N=a(9058),v=a(9270),f=a(7231),y=a(767),b=a(2095),w=a(1204),A=a(1535),k=a(2372),C=a(2812),E=a(2085);function D(e){let{order:s,onBack:a,onUpdateStatus:l}=e;return(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:a,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-900",children:["Commande #",s.id.slice(-8)]}),(0,t.jsx)("p",{className:"text-gray-600",children:"D\xe9tails de la commande"})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("span",{className:"inline-flex px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PAID":return"bg-green-100 text-green-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"DELIVERED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}})(s.status)),children:(e=>{switch(e){case"PENDING":return"En attente";case"PAID":return"Pay\xe9e";case"SHIPPED":return"Exp\xe9di\xe9e";case"DELIVERED":return"Livr\xe9e";case"CANCELLED":return"Annul\xe9e";default:return e}})(s.status)}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",children:[(0,t.jsx)(j.A,{className:"w-4 h-4 mr-2"}),"Imprimer"]})]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)(r.ZB,{children:["Articles command\xe9s (",s.items.length,")"]})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"space-y-4",children:s.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-4 p-4 bg-gray-50 rounded-lg",children:[(0,t.jsx)("div",{className:"relative w-16 h-16 rounded-lg overflow-hidden bg-white",children:(0,t.jsx)(E.default,{src:e.product.image||"/img/placeholder.svg",alt:e.product.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900",children:e.product.name}),e.variant&&(0,t.jsxs)("p",{className:"text-sm text-gray-600 capitalize",children:["Saveur: ",e.variant.flavor]}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:[Number(e.price).toFixed(2),"€ \xd7 ",e.quantity]})]}),(0,t.jsx)("div",{className:"text-right",children:(0,t.jsxs)("p",{className:"font-semibold text-gray-900",children:[(Number(e.price)*e.quantity).toFixed(2),"€"]})})]},s))})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations client"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Nom complet"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsxs)("p",{className:"text-gray-900",children:[s.customer.firstName," ",s.customer.lastName]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Email"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:s.customer.email})]})]}),s.shippingAddress.phone&&(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"T\xe9l\xe9phone"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(f.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:s.shippingAddress.phone})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Adresse de livraison"}),(0,t.jsxs)("div",{className:"flex items-start space-x-2 mt-1",children:[(0,t.jsx)(y.A,{className:"w-4 h-4 text-gray-400 mt-0.5"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-900",children:s.shippingAddress.street}),(0,t.jsxs)("p",{className:"text-gray-600",children:[s.shippingAddress.postalCode," ",s.shippingAddress.city]}),(0,t.jsx)("p",{className:"text-gray-600",children:s.shippingAddress.country})]})]})]})]})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"R\xe9sum\xe9"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Sous-total"}),(0,t.jsxs)("span",{className:"font-medium",children:[Number(s.totalAmount).toFixed(2),"€"]})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Livraison"}),(0,t.jsx)("span",{className:"font-medium",children:"Gratuite"})]}),(0,t.jsx)("div",{className:"border-t pt-3",children:(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-lg font-semibold",children:"Total"}),(0,t.jsxs)("span",{className:"text-lg font-bold text-pink-600",children:[Number(s.totalAmount).toFixed(2),"€"]})]})})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Date de commande"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsx)("p",{className:"text-gray-900",children:new Date(s.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"})})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Num\xe9ro de commande"}),(0,t.jsxs)("p",{className:"text-gray-900 font-mono",children:["#",s.id]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Articles"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2 mt-1",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 text-gray-400"}),(0,t.jsxs)("p",{className:"text-gray-900",children:[s.items.length," article(s)"]})]})]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Actions"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:["PENDING"===s.status&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(r.$n,{className:"w-full bg-blue-600 hover:bg-blue-700",onClick:()=>null==l?void 0:l(s.id,"PROCESSING"),children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Marquer en cours"]}),(0,t.jsxs)(r.$n,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>null==l?void 0:l(s.id,"COMPLETED"),children:[(0,t.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"Marquer termin\xe9e"]})]}),"PROCESSING"===s.status&&(0,t.jsxs)(r.$n,{className:"w-full bg-green-600 hover:bg-green-700",onClick:()=>null==l?void 0:l(s.id,"COMPLETED"),children:[(0,t.jsx)(A.A,{className:"w-4 h-4 mr-2"}),"Marquer termin\xe9e"]}),"CANCELLED"!==s.status&&"COMPLETED"!==s.status&&(0,t.jsxs)(r.$n,{variant:"danger",className:"w-full",onClick:()=>null==l?void 0:l(s.id,"CANCELLED"),children:[(0,t.jsx)(k.A,{className:"w-4 h-4 mr-2"}),"Annuler la commande"]}),(0,t.jsxs)(r.$n,{variant:"outline",className:"w-full",children:[(0,t.jsx)(v.A,{className:"w-4 h-4 mr-2"}),"Contacter le client"]}),(0,t.jsxs)(r.$n,{variant:"outline",className:"w-full",children:[(0,t.jsx)(C.A,{className:"w-4 h-4 mr-2"}),"Suivi de livraison"]})]})]})]})]})]})}var S=a(2209),P=a(4061),L=a(2494);function I(e){let{onViewOrder:s}=e,[a,l]=(0,p.useState)([]),[d,m]=(0,p.useState)(!1),[x,h]=(0,p.useState)("ALL"),[u,g]=(0,p.useState)(""),[j,v]=(0,p.useState)("ALL");(0,p.useEffect)(()=>{f()},[]);let f=async()=>{try{m(!0),console.log("\uD83D\uDD0D [ADMIN] R\xe9cup\xe9ration de toutes les commandes...");let e=await fetch("/api/orders"),s=await e.json();if(console.log("\uD83D\uDCE5 [ADMIN] R\xe9ponse API orders:",s),console.log("\uD83D\uDCCA [ADMIN] Statut de la r\xe9ponse:",e.status),s.success){let e=s.data.orders||[];console.log("✅ [ADMIN] Commandes r\xe9cup\xe9r\xe9es:",e.length),console.log("\uD83D\uDCCB [ADMIN] D\xe9tails des commandes:",e),l(e)}else console.error("❌ [ADMIN] Erreur lors du chargement des commandes:",s.error)}catch(e){console.error("❌ [ADMIN] Erreur de connexion:",e)}finally{m(!1)}},y=e=>new Date(e).toLocaleDateString("fr-FR",{day:"numeric",month:"short",year:"numeric",hour:"2-digit",minute:"2-digit"}),w=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e),A=e=>{switch(e){case"PENDING":return"bg-yellow-100 text-yellow-800";case"PAID":return"bg-green-100 text-green-800";case"SHIPPED":return"bg-blue-100 text-blue-800";case"DELIVERED":return"bg-purple-100 text-purple-800";case"CANCELLED":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},k=e=>{switch(e){case"PENDING":return"En attente";case"PAID":return"Pay\xe9e";case"SHIPPED":return"Exp\xe9di\xe9e";case"DELIVERED":return"Livr\xe9e";case"CANCELLED":return"Annul\xe9e";default:return e}},C=a.filter(e=>{let s=e.id.toLowerCase().includes(u.toLowerCase())||e.customer.firstName.toLowerCase().includes(u.toLowerCase())||e.customer.lastName.toLowerCase().includes(u.toLowerCase())||e.customer.email.toLowerCase().includes(u.toLowerCase()),a="ALL"===x||e.status===x,t=!0;if("ALL"!==j){let s=new Date(e.createdAt),a=new Date;switch(j){case"TODAY":t=s.toDateString()===a.toDateString();break;case"WEEK":t=s>=new Date(a.getTime()-6048e5);break;case"MONTH":t=s>=new Date(a.getTime()-2592e6)}}return s&&a&&t});return d?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex justify-between items-center",children:(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Commandes"}),(0,t.jsx)("div",{className:"h-4 bg-gray-200 rounded w-32 mt-2 animate-pulse"})]})}),(0,t.jsx)("div",{className:"space-y-4",children:[void 0,void 0,void 0,void 0,void 0].map((e,s)=>(0,t.jsx)(r.Zp,{className:"animate-pulse",children:(0,t.jsx)(r.Wu,{className:"p-6",children:(0,t.jsx)("div",{className:"h-20 bg-gray-200 rounded"})})},s))})]}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des Commandes"}),(0,t.jsxs)("p",{className:"text-black",children:[C.length," commande(s) au total"]})]}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",children:[(0,t.jsx)(S.A,{className:"w-4 h-4 mr-2"}),"Exporter"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)(P.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5"}),(0,t.jsx)("input",{type:"text",placeholder:"Rechercher une commande...",value:u,onChange:e=>g(e.target.value),className:"w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white"})]}),(0,t.jsxs)("select",{value:x,onChange:e=>h(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white",children:[(0,t.jsxs)("option",{value:"ALL",children:["Tous les statuts (",a.length,")"]}),(0,t.jsxs)("option",{value:"PENDING",children:["En attente (",a.filter(e=>"PENDING"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"PAID",children:["Pay\xe9es (",a.filter(e=>"PAID"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"SHIPPED",children:["Exp\xe9di\xe9es (",a.filter(e=>"SHIPPED"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"DELIVERED",children:["Livr\xe9es (",a.filter(e=>"DELIVERED"===e.status).length,")"]}),(0,t.jsxs)("option",{value:"CANCELLED",children:["Annul\xe9es (",a.filter(e=>"CANCELLED"===e.status).length,")"]})]}),(0,t.jsxs)("select",{value:j,onChange:e=>v(e.target.value),className:"px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-black bg-white",children:[(0,t.jsx)("option",{value:"ALL",children:"Toutes les dates"}),(0,t.jsx)("option",{value:"TODAY",children:"Aujourd'hui"}),(0,t.jsx)("option",{value:"WEEK",children:"Cette semaine"}),(0,t.jsx)("option",{value:"MONTH",children:"Ce mois"})]}),(0,t.jsx)(r.$n,{onClick:f,variant:"outline",size:"sm",children:"Actualiser"})]}),0===C.length?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(c.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:u||"ALL"!==x||"ALL"!==j?"Aucune commande trouv\xe9e":"Aucune commande enregistr\xe9e"}),(0,t.jsx)("p",{className:"text-black",children:u||"ALL"!==x||"ALL"!==j?"Essayez de modifier vos filtres de recherche":"Les commandes appara\xeetront ici apr\xe8s les premiers achats"})]})}):(0,t.jsx)("div",{className:"space-y-4",children:C.map((e,a)=>(0,t.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.05*a},children:(0,t.jsx)(r.Zp,{className:"hover:shadow-lg transition-shadow cursor-pointer",onClick:()=>null==s?void 0:s(e),children:(0,t.jsxs)(r.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("div",{className:"w-12 h-12 bg-gradient-to-r from-pink-500 to-orange-500 rounded-lg flex items-center justify-center text-white font-semibold",children:(0,t.jsx)(c.A,{className:"w-6 h-6"})}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsxs)("h3",{className:"text-lg font-semibold text-gray-900",children:["Commande #",e.id.slice(-8)]}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 rounded-full text-xs font-medium ".concat(A(e.status)),children:k(e.status)})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-4 mt-1 text-sm text-gray-600",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(N.A,{className:"w-4 h-4 mr-1"}),(0,t.jsxs)("span",{children:[e.customer.firstName," ",e.customer.lastName]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(b.A,{className:"w-4 h-4 mr-1"}),(0,t.jsx)("span",{children:y(e.createdAt)})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)(n.A,{className:"w-4 h-4 mr-1"}),(0,t.jsxs)("span",{children:[e.items.length," article(s)"]})]})]})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"flex items-center text-2xl font-bold text-gray-900",children:[(0,t.jsx)(o.A,{className:"w-6 h-6 mr-1"}),Number(e.totalAmount).toFixed(2),"€"]}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:a=>{a.stopPropagation(),null==s||s(e)},className:"mt-2",children:[(0,t.jsx)(L.A,{className:"w-4 h-4 mr-2"}),"Voir d\xe9tails"]})]})]}),(0,t.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-100",children:(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[e.items.slice(0,3).map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center space-x-2 bg-gray-50 rounded-lg px-3 py-1",children:[(0,t.jsxs)("span",{className:"text-sm font-medium",children:[e.quantity,"x"]}),(0,t.jsx)("span",{className:"text-sm text-gray-700",children:e.product.name}),e.variant&&(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["(",e.variant.flavor,")"]}),(0,t.jsx)("span",{className:"text-xs text-gray-400",children:w(e.price)})]},s)),e.items.length>3&&(0,t.jsxs)("span",{className:"text-sm text-gray-500",children:["+",e.items.length-3," autre(s)"]})]})})]})})},e.id))})]})}function q(e){var s,a,l,c;let{product:d,onEdit:o,onBack:m,onAddVariant:h}=e;return(0,t.jsxs)(i.P.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:m,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:d.name}),(0,t.jsx)("p",{className:"text-gray-600",children:"D\xe9tails du produit"})]})]}),(0,t.jsxs)(r.$n,{onClick:o,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-2"}),"Modifier"]})]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,t.jsxs)("div",{className:"lg:col-span-2 space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Informations g\xe9n\xe9rales"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Nom"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:d.name})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Prix de base"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:d.price?"".concat(d.price,"€"):"Variable"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Statut"}),(0,t.jsx)("span",{className:"inline-flex px-2 py-1 rounded-full text-xs font-medium ".concat(d.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:d.active?"Actif":"Inactif"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Dosage"}),(0,t.jsx)("p",{className:"text-gray-900 font-medium",children:d.dosage||"Non sp\xe9cifi\xe9"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"text-sm font-medium text-gray-500",children:"Description"}),(0,t.jsx)("p",{className:"text-gray-900 mt-1",children:d.description})]})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)(r.ZB,{children:["Variantes (",(null==(s=d.variants)?void 0:s.length)||0,")"]}),(0,t.jsxs)(r.$n,{size:"sm",onClick:h,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Ajouter une variante"]})]})}),(0,t.jsx)(r.Wu,{children:d.variants&&0!==d.variants.length?(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:d.variants.map(e=>(0,t.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("div",{className:"w-4 h-4 rounded-full border-2 border-gray-300",style:{backgroundColor:e.color}}),(0,t.jsxs)("div",{className:"flex-1",children:[(0,t.jsx)("p",{className:"font-medium text-gray-900 capitalize",children:e.flavor}),(0,t.jsxs)("p",{className:"text-sm text-gray-500",children:["Stock: ",e.stock," • SKU: ",e.sku]})]})]}),e.images&&e.images.length>0&&(0,t.jsxs)("div",{className:"mt-3 flex space-x-2",children:[e.images.slice(0,3).map((s,a)=>(0,t.jsx)("div",{className:"relative w-12 h-12 rounded-md overflow-hidden",children:(0,t.jsx)(E.default,{src:s,alt:"".concat(e.flavor," ").concat(a+1),fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},a)),e.images.length>3&&(0,t.jsx)("div",{className:"w-12 h-12 rounded-md bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.images.length-3]})})]})]},e.id))}):(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)(n.A,{className:"w-12 h-12 text-gray-400 mx-auto mb-3"}),(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Aucune variante cr\xe9\xe9e"}),(0,t.jsxs)(r.$n,{size:"sm",onClick:h,variant:"outline",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Cr\xe9er la premi\xe8re variante"]})]})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Paliers de prix"})}),(0,t.jsx)(r.Wu,{children:d.priceTiers&&0!==d.priceTiers.length?(0,t.jsx)("div",{className:"space-y-3",children:d.priceTiers.map((e,s)=>(0,t.jsxs)("div",{className:"flex items-center justify-between p-3 bg-gray-50 rounded-lg",children:[(0,t.jsxs)("span",{className:"font-medium",children:[e.quantity," ",1===e.quantity?"unit\xe9":"unit\xe9s"]}),(0,t.jsxs)("span",{className:"text-lg font-bold text-pink-600",children:[e.price,"€"]})]},s))}):(0,t.jsxs)("div",{className:"text-center py-6",children:[(0,t.jsx)("p",{className:"text-gray-600",children:"Aucun palier de prix configur\xe9"}),(0,t.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Les paliers permettent d'offrir des r\xe9ductions pour les achats en quantit\xe9"})]})})]})]}),(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Image principale"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsx)("div",{className:"relative aspect-square rounded-lg overflow-hidden bg-gray-100",children:(0,t.jsx)(E.default,{src:d.image||"/img/placeholder.svg",alt:d.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})})})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Statistiques"})}),(0,t.jsxs)(r.Wu,{className:"space-y-3",children:[(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Variantes"}),(0,t.jsx)("span",{className:"font-medium",children:(null==(a=d.variants)?void 0:a.length)||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Stock total"}),(0,t.jsx)("span",{className:"font-medium",children:(null==(l=d.variants)?void 0:l.reduce((e,s)=>e+s.stock,0))||0})]}),(0,t.jsxs)("div",{className:"flex justify-between",children:[(0,t.jsx)("span",{className:"text-gray-600",children:"Paliers de prix"}),(0,t.jsx)("span",{className:"font-medium",children:(null==(c=d.priceTiers)?void 0:c.length)||0})]})]})]})]})]})]})}var R=a(1994),T=a(3527),$=a(4604),Z=a(9790);function z(e){var s;let{tiers:a,onTiersChange:l,currency:i="EUR"}=e,[c,d]=(0,p.useState)(null),[m,h]=(0,p.useState)({quantity:1,price:0,discount:0}),u=(e,s)=>{l(a.map((a,t)=>t===e?s:a).sort((e,s)=>e.quantity-s.quantity)),d(null)},g=e=>{l(a.filter((s,a)=>a!==e))},j=(e,s)=>e<=0?0:Math.round((e-s)/e*100),N=e=>new Intl.NumberFormat("fr-FR",{style:"currency",currency:i}).format(e),v=(null==(s=a.find(e=>1===e.quantity))?void 0:s.price)||0;return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Prix par palier"}),(0,t.jsx)("p",{className:"text-sm text-gray-600",children:"D\xe9finissez des prix d\xe9gressifs selon la quantit\xe9"})]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[a.length," palier(s)"]})]}),a.length>1&&v>0&&(0,t.jsx)(r.Zp,{className:"bg-gradient-to-r from-green-50 to-blue-50 border-green-200",children:(0,t.jsxs)(r.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2 mb-3",children:[(0,t.jsx)(o.A,{className:"w-5 h-5 text-green-600"}),(0,t.jsx)("h4",{className:"font-medium text-green-800",children:"\xc9conomies maximales"})]}),(0,t.jsx)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:a.slice(1).map((e,s)=>{let a=j(v,e.price),r=(v-e.price)*e.quantity;return(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[e.quantity," unit\xe9s"]}),(0,t.jsxs)("p",{className:"font-semibold text-green-700",children:["-",a,"%"]}),(0,t.jsxs)("p",{className:"text-xs text-gray-500",children:["\xc9conomie: ",N(r)]})]},e.id||s)})})]})}),(0,t.jsx)("div",{className:"space-y-3",children:a.map((e,s)=>(0,t.jsx)(r.Zp,{className:1===e.quantity?"border-blue-200 bg-blue-50":"",children:(0,t.jsx)(r.Wu,{className:"p-4",children:c===s?(0,t.jsx)(O,{tier:e,onSave:e=>u(s,e),onCancel:()=>d(null),currency:i}):(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(n.A,{className:"w-5 h-5 text-gray-400"}),(0,t.jsxs)("span",{className:"font-medium text-gray-900",children:[e.quantity," ",1===e.quantity?"unit\xe9":"unit\xe9s"]}),1===e.quantity&&(0,t.jsx)("span",{className:"px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full",children:"Prix de base"})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:N(e.price)}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:[N(e.price/e.quantity)," / unit\xe9"]}),e.quantity>1&&v>0&&(0,t.jsxs)("p",{className:"text-sm text-green-600",children:["-",j(v,e.price/e.quantity),"% par unit\xe9"]})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.$n,{size:"sm",variant:"outline",onClick:()=>d(s),children:(0,t.jsx)(w.A,{className:"w-4 h-4"})}),1!==e.quantity&&(0,t.jsx)(r.$n,{size:"sm",variant:"danger",onClick:()=>g(s),children:(0,t.jsx)(Z.A,{className:"w-4 h-4"})})]})]})})},e.id||s))}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Ajouter un palier de prix"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Quantit\xe9"}),(0,t.jsx)(r.pd,{type:"number",min:"1",value:m.quantity,onChange:e=>h(s=>({...s,quantity:parseInt(e.target.value)||1})),placeholder:"Ex: 3"})]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:["Prix total (",i,")"]}),(0,t.jsx)(r.pd,{type:"number",min:"0",step:"0.01",value:m.price,onChange:e=>h(s=>({...s,price:parseFloat(e.target.value)||0})),placeholder:"Ex: 15.00"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Prix unitaire"}),(0,t.jsx)("div",{className:"px-3 py-2 bg-gray-50 border border-gray-300 rounded-lg text-gray-700",children:m.quantity>0&&m.price>0?N(m.price/m.quantity):N(0)})]})]}),m.quantity>1&&m.price>0&&v>0&&(0,t.jsxs)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-lg",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("span",{className:"text-sm text-green-700",children:"\xc9conomie par rapport au prix unitaire de base:"}),(0,t.jsxs)("span",{className:"font-semibold text-green-800",children:["-",j(v,m.price/m.quantity),"%"]})]}),(0,t.jsxs)("p",{className:"text-xs text-green-600 mt-1",children:["Le client \xe9conomise ",N(v*m.quantity-m.price),"en achetant ",m.quantity," unit\xe9s"]})]}),(0,t.jsxs)(r.$n,{onClick:()=>{m.quantity>0&&m.price>0&&(l([...a,{...m,id:Date.now().toString()}].sort((e,s)=>e.quantity-s.quantity)),h({quantity:1,price:0,discount:0}))},disabled:m.quantity<=0||m.price<=0||a.some(e=>e.quantity===m.quantity),className:"w-full",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Ajouter le palier"]}),a.some(e=>e.quantity===m.quantity)&&(0,t.jsx)("p",{className:"text-sm text-red-600 text-center",children:"Un palier existe d\xe9j\xe0 pour cette quantit\xe9"})]})]})]})}function O(e){let s,{tier:a,onSave:l,onCancel:i,currency:n}=e,[c,d]=(0,p.useState)(a);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Quantit\xe9"}),(0,t.jsx)(r.pd,{type:"number",min:"1",value:c.quantity,onChange:e=>d(s=>({...s,quantity:parseInt(e.target.value)||1})),disabled:1===a.quantity})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Prix total"}),(0,t.jsx)(r.pd,{type:"number",min:"0",step:"0.01",value:c.price,onChange:e=>d(s=>({...s,price:parseFloat(e.target.value)||0}))})]})]}),(0,t.jsxs)("div",{className:"text-sm text-gray-600",children:["Prix unitaire: ",(s=c.quantity>0?c.price/c.quantity:0,new Intl.NumberFormat("fr-FR",{style:"currency",currency:n}).format(s))]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{size:"sm",onClick:()=>l(c),children:[(0,t.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Sauvegarder"]}),(0,t.jsxs)(r.$n,{size:"sm",variant:"outline",onClick:i,children:[(0,t.jsx)(T.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]})}function W(e){let{variants:s,onVariantsChange:a,productId:l}=e,[i,n]=(0,p.useState)(null),[c,d]=(0,p.useState)([]),[o,m]=(0,p.useState)({flavor:"",color:"#ff6b9d",stock:0,sku:"",images:[]}),u=[{name:"Fraise",color:"#ff6b9d"},{name:"Myrtille",color:"#4dabf7"},{name:"Pomme",color:"#51cf66"},{name:"Orange",color:"#ff922b"},{name:"Citron",color:"#ffd43b"},{name:"Raisin",color:"#9775fa"}],g=e=>{if(!e)return"";let s=e.toUpperCase().replace(/\s+/g,"-"),a=Math.floor(1e3*Math.random()).toString().padStart(3,"0");return"DELTA-".concat(s,"-").concat(a)},j=async()=>{if(o.flavor){let e=g(o.flavor),t={...o,sku:e};if(l)try{let e=await fetch("/api/products/".concat(l,"/variants"),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!e.ok){let s=await e.json();alert(s.error||"Erreur lors de la cr\xe9ation");return}let r=await e.json(),i=[...s,r.data];a(i)}catch(e){alert("Erreur de connexion");return}else a([...s,{...t,id:Date.now().toString()}]);m({flavor:"",color:"#ff6b9d",stock:0,sku:"",images:[]})}},N=async(e,t)=>{if(t.id&&l)try{let r=await fetch("/api/products/".concat(l,"/variants/").concat(t.id),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(t)});if(!r.ok){let e=await r.json();alert(e.error||"Erreur lors de la mise \xe0 jour");return}let i=await r.json(),n=s.map((s,a)=>a===e?i.data:s);a(n)}catch(e){alert("Erreur de connexion");return}else a(s.map((s,a)=>a===e?t:s));n(null)},v=async e=>{let t=s[e];if(t.id&&l)try{let e=await fetch("/api/products/".concat(l,"/variants/").concat(t.id),{method:"DELETE"});if(!e.ok){let s=await e.json();alert(s.error||"Erreur lors de la suppression");return}}catch(e){alert("Erreur de connexion");return}a(s.filter((s,a)=>a!==e))},f=async()=>{if(0!==c.length&&confirm("\xcates-vous s\xfbr de vouloir supprimer ".concat(c.length," variante(s) ?"))){if(l)try{let e=await fetch("/api/products/".concat(l,"/variants/bulk"),{method:"DELETE",headers:{"Content-Type":"application/json"},body:JSON.stringify({variantIds:c})});if(!e.ok){let s=await e.json();alert(s.error||"Erreur lors de la suppression");return}}catch(e){alert("Erreur de connexion");return}a(s.filter(e=>!c.includes(e.id||""))),d([])}},y=e=>{d(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])},b=async(e,t)=>{let r=new FormData;r.append("file",e);try{let e=await fetch("/api/upload",{method:"POST",body:r}),l=await e.json();if(l.success)if(void 0!==t){let e=s.map((e,s)=>s===t?{...e,images:[...e.images,l.url]}:e);a(e)}else m(e=>({...e,images:[...e.images,l.url]}))}catch(e){alert("Erreur lors de l'upload de l'image")}},A=(e,t)=>{null===e?m(e=>({...e,images:e.images.filter((e,s)=>s!==t)})):a(s.map((s,a)=>a===e?{...s,images:s.images.filter((e,s)=>s!==t)}:s))};return(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Variants du produit"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[c.length>0&&(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[c.length," s\xe9lectionn\xe9(s)"]}),(0,t.jsxs)(r.$n,{onClick:f,variant:"outline",size:"sm",className:"text-red-600 hover:text-red-700",children:[(0,t.jsx)(Z.A,{className:"w-4 h-4 mr-1"}),"Supprimer"]})]}),(0,t.jsxs)("span",{className:"text-sm text-gray-600",children:[s.length," variant(s)"]})]})]}),s.length>0&&(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsx)("div",{className:"flex items-center space-x-2 text-sm",children:(0,t.jsx)(r.$n,{onClick:()=>{let e=s.map(e=>e.id).filter(Boolean);d(c.length===e.length?[]:e)},variant:"outline",size:"sm",children:c.length===s.filter(e=>e.id).length?"D\xe9s\xe9lectionner tout":"S\xe9lectionner tout"})}),(0,t.jsx)("div",{className:"flex items-center space-x-2",children:(0,t.jsxs)(r.$n,{onClick:()=>{let e=JSON.stringify(s,null,2),a="data:application/json;charset=utf-8,".concat(encodeURIComponent(e)),t="variants-".concat(new Date().toISOString().slice(0,10),".json"),r=document.createElement("a");r.setAttribute("href",a),r.setAttribute("download",t),r.click()},variant:"outline",size:"sm",children:[(0,t.jsx)(S.A,{className:"w-4 h-4 mr-1"}),"Exporter"]})})]}),(0,t.jsx)("div",{className:"space-y-4",children:s.map((e,s)=>(0,t.jsx)(r.Zp,{children:(0,t.jsx)(r.Wu,{className:"p-4",children:i===s?(0,t.jsx)(F,{variant:e,onSave:e=>N(s,e),onCancel:()=>n(null),onImageUpload:e=>b(e,s),onRemoveImage:e=>A(s,e)}):(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[e.id&&(0,t.jsx)("input",{type:"checkbox",checked:c.includes(e.id),onChange:()=>y(e.id),className:"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"}),(0,t.jsx)("div",{className:"w-6 h-6 rounded-full border-2 border-gray-300",style:{backgroundColor:e.color}}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h4",{className:"font-medium text-gray-900 capitalize",children:e.flavor}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Stock: ",e.stock," • SKU: ",e.sku]})]}),e.images.length>0&&(0,t.jsxs)("div",{className:"flex space-x-1",children:[e.images.slice(0,3).map((s,a)=>(0,t.jsx)("div",{className:"relative w-8 h-8 rounded overflow-hidden",children:(0,t.jsx)(E.default,{src:s,alt:"".concat(e.flavor," ").concat(a+1),fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},a)),e.images.length>3&&(0,t.jsx)("div",{className:"w-8 h-8 rounded bg-gray-100 flex items-center justify-center",children:(0,t.jsxs)("span",{className:"text-xs text-gray-500",children:["+",e.images.length-3]})})]})]}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)(r.$n,{size:"sm",variant:"outline",onClick:()=>n(s),children:(0,t.jsx)(w.A,{className:"w-4 h-4"})}),(0,t.jsx)(r.$n,{size:"sm",variant:"danger",onClick:()=>v(s),children:(0,t.jsx)(Z.A,{className:"w-4 h-4"})})]})]})})},e.id||s))}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Ajouter un variant"})}),(0,t.jsxs)(r.Wu,{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Saveur"}),(0,t.jsxs)("select",{value:o.flavor,onChange:e=>{let s=u.find(s=>s.name===e.target.value);m(a=>({...a,flavor:e.target.value,color:(null==s?void 0:s.color)||a.color}))},className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 text-gray-900",children:[(0,t.jsx)("option",{value:"",className:"text-gray-700",children:"S\xe9lectionner une saveur"}),u.map(e=>(0,t.jsx)("option",{value:e.name,children:e.name},e.name))]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Stock"}),(0,t.jsx)(r.pd,{type:"number",min:"0",value:o.stock,onChange:e=>m(s=>({...s,stock:parseInt(e.target.value)||0}))})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Couleur"}),(0,t.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,t.jsx)("input",{type:"color",value:o.color,onChange:e=>m(s=>({...s,color:e.target.value})),className:"w-12 h-10 border border-gray-300 rounded cursor-pointer"}),(0,t.jsx)(r.pd,{value:o.color,onChange:e=>m(s=>({...s,color:e.target.value})),placeholder:"#ff6b9d"})]})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Images du variant"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&b(a)},className:"hidden",id:"variant-image-upload"}),(0,t.jsxs)("label",{htmlFor:"variant-image-upload",className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 text-gray-700"}),(0,t.jsx)("span",{className:"text-gray-900",children:"Ajouter une image"})]})]}),o.images.length>0&&(0,t.jsx)("div",{className:"flex space-x-2 mt-3",children:o.images.map((e,s)=>(0,t.jsxs)("div",{className:"relative w-16 h-16 rounded-lg overflow-hidden",children:[(0,t.jsx)(E.default,{src:e,alt:"Variant ".concat(s+1),fill:!0,className:"object-cover"}),(0,t.jsx)("button",{onClick:()=>A(null,s),className:"absolute top-1 right-1 w-5 h-5 bg-red-500 text-white rounded-full flex items-center justify-center text-xs",children:"\xd7"})]},s))})]}),(0,t.jsxs)(r.$n,{onClick:j,disabled:!o.flavor,className:"w-full",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Ajouter le variant"]})]})]})]})}function F(e){let{variant:s,onSave:a,onCancel:l,onImageUpload:i,onRemoveImage:n}=e,[c,d]=(0,p.useState)(s);return(0,t.jsxs)("div",{className:"space-y-4",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsx)(r.pd,{value:c.flavor,onChange:e=>d(s=>({...s,flavor:e.target.value})),placeholder:"Saveur"}),(0,t.jsx)(r.pd,{value:c.sku,onChange:e=>d(s=>({...s,sku:e.target.value})),placeholder:"SKU"}),(0,t.jsx)(r.pd,{type:"number",value:c.stock,onChange:e=>d(s=>({...s,stock:parseInt(e.target.value)||0})),placeholder:"Stock"})]}),c.images.length>0&&(0,t.jsxs)("div",{className:"flex space-x-2",children:[c.images.map((e,s)=>(0,t.jsxs)("div",{className:"relative w-12 h-12 rounded overflow-hidden",children:[(0,t.jsx)(E.default,{src:e,alt:"".concat(c.flavor," ").concat(s+1),fill:!0,className:"object-cover"}),(0,t.jsx)("button",{onClick:()=>n(s),className:"absolute top-0 right-0 w-4 h-4 bg-red-500 text-white rounded-full flex items-center justify-center text-xs",children:"\xd7"})]},s)),(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&i(a)},className:"hidden",id:"edit-variant-image-".concat(s.id)}),(0,t.jsx)("label",{htmlFor:"edit-variant-image-".concat(s.id),className:"w-12 h-12 border-2 border-dashed border-gray-300 rounded flex items-center justify-center cursor-pointer hover:border-gray-400",children:(0,t.jsx)(x.A,{className:"w-4 h-4 text-gray-400"})})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{size:"sm",onClick:()=>a(c),children:[(0,t.jsx)($.A,{className:"w-4 h-4 mr-2"}),"Sauvegarder"]}),(0,t.jsxs)(r.$n,{size:"sm",variant:"outline",onClick:l,children:[(0,t.jsx)(T.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]})}function M(e){let{product:s,onSave:a,onCancel:l}=e,[c,d]=(0,p.useState)({name:"",description:"",dosage:"",image:"",active:!0}),[o,m]=(0,p.useState)([]),[x,u]=(0,p.useState)([{quantity:1,price:8}]),[j,N]=(0,p.useState)(!1),[v,f]=(0,p.useState)("general");(0,p.useEffect)(()=>{if(s){var e;d({name:s.name||"",description:s.description||"",dosage:s.dosage||"",image:s.image||"",active:null==(e=s.active)||e}),s.variants&&m(s.variants.map(e=>({id:e.id,flavor:e.flavor||"",color:e.color||"#ff6b9d",stock:e.stock||0,sku:e.sku||"",images:e.images||[]}))),s.priceTiers&&u(s.priceTiers)}},[s]);let y=async e=>{let s=new FormData;s.append("file",e);try{let e=await fetch("/api/upload",{method:"POST",body:s}),a=await e.json();a.success?d(e=>({...e,image:a.url})):alert("Erreur lors de l'upload")}catch(e){alert("Erreur lors de l'upload")}},b=async e=>{e.preventDefault(),N(!0);try{var s;let e={...c,variants:o,pricingTiers:x,basePrice:(null==(s=x.find(e=>1===e.quantity))?void 0:s.price)||0,image:c.image||"".concat(window.location.origin,"/img/placeholder.svg")};await a(e)}catch(e){alert("Erreur lors de la sauvegarde")}finally{N(!1)}},w=[{id:"general",label:"Informations g\xe9n\xe9rales",icon:n.A},{id:"variants",label:"Variants",icon:n.A},{id:"pricing",label:"Prix par palier",icon:n.A}];return(0,t.jsxs)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},className:"space-y-6",children:[(0,t.jsx)("div",{className:"flex items-center justify-between",children:(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:l,children:[(0,t.jsx)(g.A,{className:"w-4 h-4 mr-2"}),"Retour"]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:s?"Modifier le produit":"Nouveau produit"}),(0,t.jsx)("p",{className:"text-gray-600",children:s?"Modifiez les informations du produit":"Cr\xe9ez un nouveau produit avec variants et prix par palier"})]})]})}),(0,t.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-0",children:[(0,t.jsx)("div",{className:"border-b border-gray-200",children:(0,t.jsx)("nav",{className:"flex space-x-8 px-6",children:w.map(e=>(0,t.jsxs)("button",{type:"button",onClick:()=>f(e.id),className:"py-4 px-1 border-b-2 font-medium text-sm ".concat(v===e.id?"border-pink-500 text-pink-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"),children:[(0,t.jsx)(e.icon,{className:"w-4 h-4 inline mr-2"}),e.label]},e.id))})}),(0,t.jsxs)("div",{className:"p-6",children:["general"===v&&(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Nom du produit *"}),(0,t.jsx)(r.pd,{value:c.name,onChange:e=>d(s=>({...s,name:e.target.value})),placeholder:"Ex: Cookie Delta-9",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Dosage"}),(0,t.jsx)(r.pd,{value:c.dosage,onChange:e=>d(s=>({...s,dosage:e.target.value})),placeholder:"Ex: 5mg THC"})]})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Description"}),(0,t.jsx)("textarea",{value:c.description,onChange:e=>d(s=>({...s,description:e.target.value})),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none text-gray-900 placeholder:text-gray-700",placeholder:"Description d\xe9taill\xe9e du produit...",minLength:10})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Image principale"}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[c.image?(0,t.jsxs)("div",{className:"relative w-24 h-24 rounded-lg overflow-hidden",children:[(0,t.jsx)(E.default,{src:c.image,alt:"Produit",fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,t.jsx)("button",{type:"button",onClick:()=>{if(c.image&&c.image.includes("/uploads/")){let e=c.image.split("/").pop();e&&fetch("/api/upload/".concat(e),{method:"DELETE"}).catch(e=>console.error("Erreur lors de la suppression:",e))}d(e=>({...e,image:""}))},className:"absolute top-1 right-1 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-sm",children:"\xd7"})]}):(0,t.jsx)("div",{className:"w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center",children:(0,t.jsx)(R.A,{className:"w-8 h-8 text-gray-400"})}),(0,t.jsxs)("div",{children:[(0,t.jsx)("input",{type:"file",accept:"image/*",onChange:e=>{var s;let a=null==(s=e.target.files)?void 0:s[0];a&&y(a)},className:"hidden",id:"main-image-upload"}),(0,t.jsxs)("label",{htmlFor:"main-image-upload",className:"flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg cursor-pointer hover:bg-gray-50",children:[(0,t.jsx)(h.A,{className:"w-4 h-4 text-gray-700"}),(0,t.jsx)("span",{className:"text-gray-900",children:"Choisir une image"})]}),(0,t.jsx)("p",{className:"text-xs text-gray-700 mt-1",children:"JPG, PNG jusqu'\xe0 5MB"}),(0,t.jsx)("p",{className:"text-xs text-green-600 mt-1",children:"Les images sont stock\xe9es dans /public/uploads"})]})]})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("input",{type:"checkbox",id:"active",checked:c.active,onChange:e=>d(s=>({...s,active:e.target.checked})),className:"h-4 w-4 text-pink-600 focus:ring-pink-500 border-gray-300 rounded"}),(0,t.jsx)("label",{htmlFor:"active",className:"ml-2 block text-sm text-gray-900",children:"Produit actif (visible sur le site)"})]})]}),"variants"===v&&(0,t.jsx)(W,{variants:o,onVariantsChange:m,productId:null==s?void 0:s.id}),"pricing"===v&&(0,t.jsx)(z,{tiers:x,onTiersChange:u,currency:"EUR"})]})]})}),(0,t.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,t.jsxs)(r.$n,{type:"button",variant:"outline",onClick:l,children:[(0,t.jsx)(T.A,{className:"w-4 h-4 mr-2"}),"Annuler"]}),(0,t.jsxs)(r.$n,{type:"submit",disabled:j||!c.name,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)($.A,{className:"w-4 h-4 mr-2"}),j?"Sauvegarde...":"Sauvegarder"]})]})]})]})}function V(e){let{onAddProduct:s,onEditProduct:a,onViewProduct:l}=e,[c,d]=(0,p.useState)([]),[o,m]=(0,p.useState)(!0),[h,u]=(0,p.useState)(null);(0,p.useEffect)(()=>{g()},[]);let g=async()=>{try{m(!0);let e=await fetch("/api/products"),s=await e.json();s.success?d(s.data.products||[]):u("Erreur lors du chargement des produits")}catch(e){u("Erreur de connexion")}finally{m(!1)}},j=async e=>{if(confirm("\xcates-vous s\xfbr de vouloir supprimer ce produit ?"))try{(await fetch("/api/products/".concat(e),{method:"DELETE"})).ok?d(c.filter(s=>s.id!==e)):alert("Erreur lors de la suppression")}catch(e){alert("Erreur de connexion")}};return o?(0,t.jsx)("div",{className:"flex items-center justify-center py-12",children:(0,t.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-500"})}):h?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-6 text-center",children:[(0,t.jsx)("p",{className:"text-red-600",children:h}),(0,t.jsx)(r.$n,{onClick:g,className:"mt-4",children:"R\xe9essayer"})]})}):(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Gestion des produits"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[c.length," produit(s) au total"]})]}),(0,t.jsxs)(r.$n,{onClick:s,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Nouveau produit"]})]}),0===c.length?(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)(n.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Aucun produit"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:"Commencez par cr\xe9er votre premier produit Deltagum."}),(0,t.jsxs)(r.$n,{onClick:s,className:"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(x.A,{className:"w-4 h-4 mr-2"}),"Cr\xe9er un produit"]})]})}):(0,t.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:c.map((e,s)=>{var n;return(0,t.jsx)(i.P.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1*s},children:(0,t.jsxs)(r.Zp,{className:"overflow-hidden hover:shadow-lg transition-shadow",children:[(0,t.jsxs)("div",{className:"relative h-48 bg-gray-100",children:[(0,t.jsx)(E.default,{src:e.image||"/img/placeholder.svg",alt:e.name,fill:!0,className:"object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,t.jsx)("div",{className:"absolute top-2 right-2",children:(0,t.jsx)("span",{className:"px-2 py-1 rounded-full text-xs font-medium ".concat(e.active?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.active?"Actif":"Inactif"})})]}),(0,t.jsxs)(r.aR,{className:"pb-2",children:[(0,t.jsx)(r.ZB,{className:"text-lg",children:e.name}),(0,t.jsx)("p",{className:"text-sm text-gray-600 line-clamp-2",children:e.description})]}),(0,t.jsxs)(r.Wu,{className:"pt-0",children:[(0,t.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Prix de base"}),(0,t.jsx)("p",{className:"font-semibold",children:e.price?"".concat(e.price,"€"):"Variable"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-sm text-gray-500",children:"Variants"}),(0,t.jsx)("p",{className:"font-semibold",children:(null==(n=e.variants)?void 0:n.length)||0})]})]}),(0,t.jsxs)("div",{className:"flex space-x-2",children:[(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:()=>l(e),className:"flex-1",children:[(0,t.jsx)(L.A,{className:"w-4 h-4 mr-1"}),"Voir"]}),(0,t.jsxs)(r.$n,{variant:"outline",size:"sm",onClick:()=>a(e),className:"flex-1",children:[(0,t.jsx)(w.A,{className:"w-4 h-4 mr-1"}),"Modifier"]}),(0,t.jsx)(r.$n,{variant:"danger",size:"sm",onClick:()=>j(e.id),children:(0,t.jsx)(Z.A,{className:"w-4 h-4"})})]})]})]})},e.id)})})]})}function B(){let{user:e,isAuthenticated:s,logout:a,isAdmin:g,checkAuth:j}=(0,l.A)(),N=(0,u.useRouter)(),[v,f]=(0,p.useState)("overview"),[y,b]=(0,p.useState)("list"),[w,A]=(0,p.useState)(null),[k,C]=(0,p.useState)("list"),[E,S]=(0,p.useState)(null),[P,L]=(0,p.useState)("list"),[R,T]=(0,p.useState)(null);(0,p.useEffect)(()=>{j()},[]);let $=async()=>{await a(),N.push("/")},Z=()=>{A(null),b("form")},z=e=>{A(e),b("form")},O=async e=>{try{let s=w?"/api/products/".concat(w.id):"/api/products",a=w?"PUT":"POST";(await fetch(s,{method:a,headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).ok?(b("list"),A(null)):alert("Erreur lors de la sauvegarde")}catch(e){alert("Erreur de connexion")}},W=()=>{b("list"),A(null)},F=async(e,s)=>{try{(await fetch("/api/orders/".concat(e),{method:"PATCH",headers:{"Content-Type":"application/json"},body:JSON.stringify({status:s})})).ok?(T(e=>e?{...e,status:s}:null),alert("Statut de la commande mis \xe0 jour avec succ\xe8s !")):alert("Erreur lors de la mise \xe0 jour du statut")}catch(e){alert("Erreur de connexion")}};if(console.log("Dashboard - isAuthenticated:",s),console.log("Dashboard - user:",e),console.log("Dashboard - isAdmin():",g()),!s||!g())return(0,t.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,t.jsx)("div",{className:"bg-white p-8 rounded-lg shadow-lg max-w-lg w-full",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"\uD83D\uDD10 Acc\xe8s Dashboard Admin"}),(0,t.jsxs)("div",{className:"text-left bg-gray-50 p-4 rounded-lg mb-6",children:[(0,t.jsx)("h3",{className:"font-semibold mb-3",children:"\xc9tat actuel :"}),(0,t.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,t.jsxs)("p",{children:["✅ Authentifi\xe9: ",s?"Oui":"Non"]}),(0,t.jsxs)("p",{children:["\uD83D\uDC64 Utilisateur:"," ",e?"".concat(e.firstName," ").concat(e.lastName):"Aucun"]}),(0,t.jsxs)("p",{children:["\uD83C\uDFAD R\xf4le: ",(null==e?void 0:e.role)||"Aucun"]}),(0,t.jsxs)("p",{children:["\uD83D\uDD11 Admin: ",g()?"Oui":"Non"]})]})]}),(0,t.jsxs)("div",{className:"space-y-3",children:[s?g()?null:(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Votre compte n'a pas les permissions admin"}),(0,t.jsx)("a",{href:"/",className:"inline-block bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700",children:"Retour \xe0 l'accueil"})]}):(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"text-gray-600 mb-4",children:"Vous devez vous connecter en tant qu'admin"}),(0,t.jsx)("a",{href:"/auth",className:"inline-block bg-pink-600 text-white px-6 py-2 rounded-lg hover:bg-pink-700",children:"Se connecter"})]}),(0,t.jsx)("div",{className:"pt-4 border-t",children:(0,t.jsx)("a",{href:"/test-auth",className:"text-sm text-blue-600 hover:underline",children:"\uD83E\uDDEA Page de test d'authentification"})})]})]})})});let B={initial:{opacity:0,y:20},animate:{opacity:1,y:0}},G={initial:{opacity:0,x:-20},animate:{opacity:1,x:0}};n.A,c.A,d.A,o.A;let U=[{id:"overview",label:"Vue d'ensemble",icon:n.A},{id:"products",label:"Produits",icon:n.A},{id:"orders",label:"Commandes",icon:c.A}];return(0,t.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-gray-50 to-gray-100",children:[(0,t.jsx)(i.P.header,{initial:{y:-20,opacity:0},animate:{y:0,opacity:1},className:"bg-white shadow-lg border-b border-gray-200 sticky top-0 z-[99]",children:(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,t.jsxs)("div",{className:"flex justify-between items-center h-16",children:[(0,t.jsx)("div",{className:"flex items-center space-x-4",children:(0,t.jsx)(i.P.h1,{initial:{scale:.9},animate:{scale:1},className:"text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent",children:"Deltagum Admin"})}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsxs)(i.P.span,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.2},className:"text-gray-700 hidden sm:block",children:["Bonjour, ",null==e?void 0:e.firstName," ",null==e?void 0:e.lastName]}),(0,t.jsxs)(r.$n,{onClick:$,variant:"outline",size:"sm",className:"flex items-center space-x-2 hover:bg-red-50 hover:border-red-200 hover:text-red-600 transition-colors",children:[(0,t.jsx)(m.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{className:"hidden sm:block",children:"D\xe9connexion"})]})]})]})})}),(0,t.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,t.jsxs)("div",{className:"flex flex-col lg:flex-row gap-8",children:[(0,t.jsx)(i.P.div,{initial:G.initial,animate:G.animate,className:"w-full lg:w-64 bg-white rounded-xl shadow-lg p-6 border border-gray-200",children:(0,t.jsx)("nav",{className:"space-y-2",children:U.map(e=>(0,t.jsxs)("button",{onClick:()=>f(e.id),className:"w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ".concat(v===e.id?"bg-pink-50 text-pink-600 border-l-4 border-pink-500":"text-gray-900 hover:bg-gray-50 hover:text-black"),children:[(0,t.jsx)(e.icon,{className:"w-5 h-5"}),(0,t.jsx)("span",{className:"font-medium",children:e.label})]},e.id))})}),(0,t.jsxs)("div",{className:"flex-1",children:["overview"===v&&(0,t.jsxs)(i.P.div,{initial:B.initial,animate:B.animate,className:"space-y-8",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Produits"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-pink-600",children:"2"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Commandes"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-green-600",children:"0"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Clients"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-blue-600",children:"1"})]}),(0,t.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow border border-gray-100",children:[(0,t.jsx)("h3",{className:"text-lg font-semibold text-gray-800 mb-2",children:"Revenus"}),(0,t.jsx)("p",{className:"text-3xl font-bold text-orange-600",children:"0€"})]})]}),(0,t.jsxs)(r.Zp,{children:[(0,t.jsx)(r.aR,{children:(0,t.jsx)(r.ZB,{children:"Actions rapides"})}),(0,t.jsx)(r.Wu,{children:(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,t.jsxs)(r.$n,{onClick:Z,className:"h-20 flex flex-col items-center justify-center space-y-2 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(x.A,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"Ajouter un produit"})]}),(0,t.jsxs)(r.$n,{variant:"outline",className:"h-20 flex flex-col items-center justify-center space-y-2",onClick:()=>f("images"),children:[(0,t.jsx)(h.A,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"G\xe9rer les images"})]}),(0,t.jsxs)(r.$n,{variant:"outline",onClick:()=>f("orders"),className:"h-20 flex flex-col items-center justify-center space-y-2",children:[(0,t.jsx)(c.A,{className:"w-6 h-6"}),(0,t.jsx)("span",{children:"Voir les commandes"})]})]})})]})]}),"products"===v&&(0,t.jsxs)(i.P.div,{initial:B.initial,animate:B.animate,className:"space-y-6",children:["list"===y&&(0,t.jsx)(V,{onAddProduct:Z,onEditProduct:z,onViewProduct:e=>{A(e),b("details")}}),"form"===y&&(0,t.jsx)(M,{product:w,onSave:O,onCancel:W}),"details"===y&&w&&(0,t.jsx)(q,{product:w,onEdit:()=>z(w),onBack:W,onAddVariant:()=>{alert("Gestion des variantes \xe0 impl\xe9menter")}})]}),"orders"===v&&(0,t.jsxs)(i.P.div,{initial:B.initial,animate:B.animate,className:"space-y-6",children:["list"===P&&(0,t.jsx)(I,{onViewOrder:e=>{T(e),L("details")}}),"details"===P&&R&&(0,t.jsx)(D,{order:R,onBack:()=>{L("list"),T(null)},onUpdateStatus:F})]}),"overview"!==v&&"products"!==v&&"orders"!==v&&(0,t.jsx)(i.P.div,{initial:B.initial,animate:B.animate,className:"space-y-6",children:(0,t.jsx)(r.Zp,{children:(0,t.jsxs)(r.Wu,{className:"p-12 text-center",children:[(0,t.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"Section en d\xe9veloppement"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Cette section sera bient\xf4t disponible."})]})})})]})]})})]})}},9110:(e,s,a)=>{Promise.resolve().then(a.bind(a,5445))}},e=>{var s=s=>e(e.s=s);e.O(0,[6953,4026,2065,9084,8656,75,7358],()=>s(9110)),_N_E=e.O()}]);