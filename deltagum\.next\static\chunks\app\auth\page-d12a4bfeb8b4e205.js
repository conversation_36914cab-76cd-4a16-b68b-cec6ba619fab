(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8365],{1671:(e,r,t)=>{Promise.resolve().then(t.bind(t,1997))},1997:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>f});var a=t(5936),s=t(9084),l=t(5156),n=t(6953),o=t(9270),i=t(9058),d=t(3411);let c=(0,d.A)("lock",[["rect",{width:"18",height:"11",x:"3",y:"11",rx:"2",ry:"2",key:"1w4ew1"}],["path",{d:"M7 11V7a5 5 0 0 1 10 0v4",key:"fwvmzm"}]]),m=(0,d.A)("eye-off",[["path",{d:"M10.733 5.076a10.744 10.744 0 0 1 11.205 6.575 1 1 0 0 1 0 .696 10.747 10.747 0 0 1-1.444 2.49",key:"ct8e1f"}],["path",{d:"M14.084 14.158a3 3 0 0 1-4.242-4.242",key:"151rxh"}],["path",{d:"M17.479 17.499a10.75 10.75 0 0 1-15.417-5.151 1 1 0 0 1 0-.696 10.75 10.75 0 0 1 4.446-5.143",key:"13bj9a"}],["path",{d:"m2 2 20 20",key:"1ooewy"}]]);var u=t(2494),x=t(8581),h=t.n(x),p=t(4982),b=t(5180);let f=()=>{let[e,r]=(0,b.useState)(!0),[t,d]=(0,b.useState)(!1),[x,f]=(0,b.useState)({email:"",password:"",firstName:"",lastName:"",phone:"",address:"",postalCode:"",city:""}),[g,y]=(0,b.useState)({}),{login:k,register:N,isAuthenticated:j,isLoading:v}=(0,l.useAuth)(),w=(0,p.useRouter)();(0,b.useEffect)(()=>{j&&w.push("/")},[j,w]);let C=async r=>{r.preventDefault(),y({});try{e?await k(x.email,x.password):await N(x),w.push("/")}catch(e){y({general:e.message||"Une erreur est survenue"})}},A=e=>{f({...x,[e.target.name]:e.target.value})};return(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center py-8 px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)(n.P.div,{className:"max-w-2xl w-full space-y-8 bg-white p-10 rounded-3xl shadow-2xl border border-gray-200",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},children:[(0,a.jsxs)("div",{children:[(0,a.jsx)(h(),{href:"/",className:"flex justify-center",children:(0,a.jsx)("img",{className:"h-12 w-auto",src:"/img/logo.png",alt:"Deltagum",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})}),(0,a.jsx)("h2",{className:"mt-6 text-center text-3xl font-extrabold text-black",children:e?"Connexion":"Cr\xe9er un compte"}),(0,a.jsx)("p",{className:"mt-2 text-center text-sm text-black",children:e?(0,a.jsxs)(a.Fragment,{children:["Pas encore de compte ?"," ",(0,a.jsx)("button",{onClick:()=>r(!1),className:"font-medium text-pink-600 hover:text-pink-500",children:"S'inscrire"})]}):(0,a.jsxs)(a.Fragment,{children:["D\xe9j\xe0 un compte ?"," ",(0,a.jsx)("button",{onClick:()=>r(!0),className:"font-medium text-pink-600 hover:text-pink-500",children:"Se connecter"})]})})]}),(0,a.jsxs)("form",{className:"mt-10 space-y-8",onSubmit:C,children:[(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-black mb-3",children:"Adresse email"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(o.A,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5 z-10"}),(0,a.jsx)(s.pd,{id:"email",name:"email",type:"email",required:!0,className:"w-full pl-12 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"<EMAIL>",value:x.email,onChange:A})]})]}),!e&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"firstName",className:"block text-sm font-medium text-black mb-2",children:"Pr\xe9nom"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(i.A,{className:"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5"}),(0,a.jsx)(s.pd,{id:"firstName",name:"firstName",type:"text",required:!0,className:"w-full pl-12 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre pr\xe9nom",value:x.firstName,onChange:A})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"lastName",className:"block text-sm font-medium text-black mb-2",children:"Nom"}),(0,a.jsx)(s.pd,{id:"lastName",name:"lastName",type:"text",required:!0,className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre nom",value:x.lastName,onChange:A})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{htmlFor:"phone",className:"block text-sm font-medium text-black mb-3",children:"T\xe9l\xe9phone (optionnel)"}),(0,a.jsx)(s.pd,{id:"phone",name:"phone",type:"tel",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"06 12 34 56 78",value:x.phone,onChange:A})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{htmlFor:"address",className:"block text-sm font-medium text-black mb-3",children:"Adresse (optionnel)"}),(0,a.jsx)(s.pd,{id:"address",name:"address",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"123 rue de la Paix",value:x.address,onChange:A})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"postalCode",className:"block text-sm font-medium text-black mb-2",children:"Code postal"}),(0,a.jsx)(s.pd,{id:"postalCode",name:"postalCode",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"75001",value:x.postalCode,onChange:A})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"city",className:"block text-sm font-medium text-black mb-2",children:"Ville"}),(0,a.jsx)(s.pd,{id:"city",name:"city",type:"text",className:"w-full h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Paris",value:x.city,onChange:A})]})]})]}),(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-black mb-3",children:"Mot de passe"}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)(c,{className:"absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-700 w-5 h-5 z-10"}),(0,a.jsx)(s.pd,{id:"password",name:"password",type:t?"text":"password",required:!0,className:"w-full pl-12 pr-14 h-14 text-base border-2 border-gray-200 rounded-xl focus:border-pink-500 focus:ring-pink-500 focus:ring-2 transition-all duration-200",placeholder:"Votre mot de passe",value:x.password,onChange:A}),(0,a.jsx)("button",{type:"button",className:"absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-700 hover:text-black transition-colors z-10 p-1",onClick:()=>d(!t),children:t?(0,a.jsx)(m,{className:"w-5 h-5"}):(0,a.jsx)(u.A,{className:"w-5 h-5"})})]}),!e&&(0,a.jsx)("p",{className:"mt-1 text-xs text-black",children:"Minimum 6 caract\xe8res"})]})]}),g.general&&(0,a.jsx)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-3 text-red-700 text-sm text-center",children:g.general}),(0,a.jsx)("div",{className:"pt-4",children:(0,a.jsx)(s.$n,{type:"submit",variant:"primary",size:"lg",className:"w-full h-16 text-lg font-semibold bg-pink-600 hover:bg-pink-700 focus:ring-pink-500 focus:ring-4 rounded-xl transition-all duration-200 shadow-lg hover:shadow-xl",disabled:v,children:v?"Chargement...":e?"Se connecter":"Cr\xe9er mon compte"})}),(0,a.jsx)("div",{className:"text-center pt-4 border-t border-gray-100",children:(0,a.jsxs)("div",{className:"bg-amber-50 border border-amber-200 rounded-lg p-3 text-amber-800",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:"\uD83D\uDD1E R\xe9serv\xe9 aux personnes majeures (18+)"}),(0,a.jsx)("p",{className:"text-xs mt-1",children:"En vous inscrivant, vous confirmez avoir 18 ans ou plus"})]})})]})]})})}},2494:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(3411).A)("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},3411:(e,r,t)=>{"use strict";t.d(r,{A:()=>m});var a=t(5180);let s=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),l=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,r,t)=>t?t.toUpperCase():r.toLowerCase()),n=e=>{let r=l(e);return r.charAt(0).toUpperCase()+r.slice(1)},o=function(){for(var e=arguments.length,r=Array(e),t=0;t<e;t++)r[t]=arguments[t];return r.filter((e,r,t)=>!!e&&""!==e.trim()&&t.indexOf(e)===r).join(" ").trim()},i=e=>{for(let r in e)if(r.startsWith("aria-")||"role"===r||"title"===r)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let c=(0,a.forwardRef)((e,r)=>{let{color:t="currentColor",size:s=24,strokeWidth:l=2,absoluteStrokeWidth:n,className:c="",children:m,iconNode:u,...x}=e;return(0,a.createElement)("svg",{ref:r,...d,width:s,height:s,stroke:t,strokeWidth:n?24*Number(l)/Number(s):l,className:o("lucide",c),...!m&&!i(x)&&{"aria-hidden":"true"},...x},[...u.map(e=>{let[r,t]=e;return(0,a.createElement)(r,t)}),...Array.isArray(m)?m:[m]])}),m=(e,r)=>{let t=(0,a.forwardRef)((t,l)=>{let{className:i,...d}=t;return(0,a.createElement)(c,{ref:l,iconNode:r,className:o("lucide-".concat(s(n(e))),"lucide-".concat(e),i),...d})});return t.displayName=n(e),t}},4982:(e,r,t)=>{"use strict";var a=t(1802);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},9058:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(3411).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9270:(e,r,t)=>{"use strict";t.d(r,{A:()=>a});let a=(0,t(3411).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])}},e=>{var r=r=>e(e.s=r);e.O(0,[6953,4026,8581,9084,8656,75,7358],()=>r(1671)),_N_E=e.O()}]);