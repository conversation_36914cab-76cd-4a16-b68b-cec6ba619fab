(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[9577],{1320:(e,a,t)=>{Promise.resolve().then(t.bind(t,6917))},6917:(e,a,t)=>{"use strict";t.r(a),t.d(a,{default:()=>d});var s=t(5936),i=t(9084),r=t(5156),n=t(6953),l=t(8581),c=t.n(l),o=t(5180);function d(){let{addItem:e}=(0,r.useCart)();return(0,o.useEffect)(()=>{let a=localStorage.getItem("deltagum_pending_order");if(a)try{let{cartItems:t}=JSON.parse(a);t&&Array.isArray(t)&&t.forEach(a=>{e({productId:a.productId,variantId:a.variantId,quantity:a.quantity,name:a.name,price:a.price,image:a.image,flavor:a.flavor,color:a.color})}),localStorage.removeItem("deltagum_pending_order")}catch(e){console.error("Erreur lors de la restauration du panier:",e)}},[e]),(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-orange-50 to-red-50",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,s.jsxs)(n.P.div,{className:"max-w-2xl mx-auto text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,s.jsx)(n.P.div,{className:"text-8xl mb-6",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:260,damping:20,delay:.2},children:"❌"}),(0,s.jsx)(n.P.h1,{className:"text-4xl font-bold text-gray-800 mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:"Paiement annul\xe9"}),(0,s.jsx)(n.P.p,{className:"text-xl text-gray-600 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:"Votre paiement a \xe9t\xe9 annul\xe9. Aucun montant n'a \xe9t\xe9 d\xe9bit\xe9 de votre compte."}),(0,s.jsxs)(n.P.div,{className:"bg-orange-50 border border-orange-200 rounded-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:[(0,s.jsx)("div",{className:"flex items-center justify-center mb-4",children:(0,s.jsx)("span",{className:"text-4xl",children:"\uD83D\uDED2"})}),(0,s.jsx)("h3",{className:"text-lg font-semibold text-orange-800 mb-2",children:"Vos articles sont toujours dans votre panier"}),(0,s.jsx)("p",{className:"text-orange-700",children:"Vous pouvez reprendre votre commande \xe0 tout moment. Vos d\xe9licieux bonbons Deltagum vous attendent !"})]}),(0,s.jsxs)(n.P.div,{className:"bg-white rounded-lg shadow-lg p-6 mb-8 text-left",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},children:[(0,s.jsx)("h3",{className:"text-xl font-bold text-gray-800 mb-4 text-center",children:"Pourquoi reprendre votre commande ?"}),(0,s.jsxs)("div",{className:"space-y-3",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83C\uDF53"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Saveurs naturelles de fraise, myrtille et pomme"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDE9A"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Livraison gratuite d\xe8s 25€ d'achat"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDD12"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Paiement 100% s\xe9curis\xe9 par Stripe"})]}),(0,s.jsxs)("div",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"text-2xl mr-3",children:"⭐"}),(0,s.jsx)("span",{className:"text-gray-700",children:"Satisfaction garantie ou rembours\xe9"})]})]})]}),(0,s.jsxs)(n.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2},children:[(0,s.jsx)(c(),{href:"/cart",children:(0,s.jsx)(i.$n,{variant:"primary",size:"lg",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDED2"}),"Reprendre ma commande"]})})}),(0,s.jsx)(c(),{href:"/",children:(0,s.jsx)(i.$n,{variant:"outline",size:"lg",children:"Continuer mes achats"})})]}),(0,s.jsxs)(n.P.div,{className:"mt-12 text-center",initial:{opacity:0},animate:{opacity:1},transition:{delay:1.4},children:[(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Besoin d'aide ? Notre \xe9quipe est l\xe0 pour vous accompagner."}),(0,s.jsx)(c(),{href:"/#contact",children:(0,s.jsx)(i.$n,{variant:"ghost",size:"sm",children:(0,s.jsxs)("span",{className:"flex items-center",children:[(0,s.jsx)("span",{className:"mr-2",children:"\uD83D\uDCAC"}),"Nous contacter"]})})})]})]})})})}}},e=>{var a=a=>e(e.s=a);e.O(0,[6953,4026,8581,9084,8656,75,7358],()=>a(1320)),_N_E=e.O()}]);