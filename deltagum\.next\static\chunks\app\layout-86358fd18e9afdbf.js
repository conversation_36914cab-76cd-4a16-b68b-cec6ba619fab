(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7177],{766:(e,s,t)=>{"use strict";t.d(s,{AdminLayout:()=>z,AuthLayout:()=>E,ErrorLayout:()=>D,Layout:()=>P});var a=t(5936),i=t(9084),r=t(5180);let l="deltagum_age_verified",n=()=>{let[e,s]=(0,r.useState)(null),[t,a]=(0,r.useState)(!1);return(0,r.useEffect)(()=>{(()=>{try{let e=localStorage.getItem(l);if(e){let{timestamp:t,verified:i}=JSON.parse(e),r=Date.now();if(i&&r-t<864e5){s(!0),a(!1);return}localStorage.removeItem(l)}s(!1),a(!0)}catch(e){console.error("Erreur lors de la v\xe9rification d'\xe2ge:",e),s(!1),a(!0)}})()},[]),{isVerified:e,showModal:t,confirmAge:()=>{try{let e={verified:!0,timestamp:Date.now()};localStorage.setItem(l,JSON.stringify(e)),s(!0),a(!1)}catch(e){console.error("Erreur lors de la sauvegarde de la v\xe9rification:",e)}},denyAge:()=>{s(!1),a(!1),window.location.href="https://www.service-public.fr/particuliers/vosdroits/F32094"},resetVerification:()=>{localStorage.removeItem(l),s(!1),a(!0)}}};var o=t(5156),c=t(1774),d=t(2096),m=t(6953),x=t(6674),h=t(8392);let p=e=>{let{isOpen:s,onConfirm:t,onDeny:l}=e,[n,o]=(0,r.useState)(!1);return s?(0,a.jsx)(d.N,{children:(0,a.jsxs)("div",{className:"fixed inset-0 z-50 flex items-center justify-center",children:[(0,a.jsx)(m.P.div,{className:"absolute inset-0 bg-black/80 backdrop-blur-sm",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0}}),(0,a.jsx)(m.P.div,{className:"relative z-10 w-full max-w-md mx-4",initial:{opacity:0,scale:.9,y:20},animate:{opacity:1,scale:1,y:0},exit:{opacity:0,scale:.9,y:20},transition:{duration:.3},children:(0,a.jsxs)("div",{className:"bg-white rounded-2xl shadow-2xl overflow-hidden",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-pink-500 to-orange-500 p-6 text-center",children:[(0,a.jsx)(m.P.div,{className:"text-6xl mb-4",animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0},children:"\uD83D\uDD1E"}),(0,a.jsx)("h2",{className:"text-2xl font-bold text-white font-candy",children:"V\xe9rification d'\xe2ge"})]}),(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800 mb-3",children:"Acc\xe8s r\xe9serv\xe9 aux adultes"}),(0,a.jsxs)("p",{className:"text-gray-600 leading-relaxed",children:["Ce site propose des produits \xe0 base de"," ",(0,a.jsx)("strong",{children:"Delta-9 THC"}),".",(0,a.jsx)("br",{}),"L'acc\xe8s est strictement r\xe9serv\xe9 aux personnes majeures."]})]}),(0,a.jsx)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6",children:(0,a.jsxs)("p",{className:"text-sm text-yellow-800",children:["⚠️ ",(0,a.jsx)("strong",{children:"Avertissement :"})," Ces produits contiennent du Delta-9 THC et ne sont pas destin\xe9s aux mineurs, aux femmes enceintes ou allaitantes."]})}),(0,a.jsx)("div",{className:"text-lg font-medium text-gray-800 mb-6",children:"Avez-vous 18 ans ou plus ?"}),n?(0,a.jsxs)(m.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"text-center",children:[(0,a.jsx)("div",{className:"text-red-600 font-semibold mb-4",children:"❌ Acc\xe8s refus\xe9"}),(0,a.jsxs)("p",{className:"text-gray-600",children:["Vous devez \xeatre majeur pour acc\xe9der \xe0 ce site.",(0,a.jsx)("br",{}),"Redirection en cours..."]})]}):(0,a.jsxs)("div",{className:"flex gap-4",children:[(0,a.jsx)(i.$n,{onClick:t,className:"flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3",children:"✓ Oui, j'ai 18 ans ou plus"}),(0,a.jsx)(i.$n,{onClick:()=>{o(!0),setTimeout(()=>{l()},2e3)},variant:"outline",className:"flex-1 border-red-300 text-red-600 hover:bg-red-50 font-semibold py-3",children:"✗ Non, j'ai moins de 18 ans"})]})]}),(0,a.jsx)("div",{className:"bg-gray-50 px-6 py-4 text-center",children:(0,a.jsx)("p",{className:"text-xs text-gray-500",children:"En continuant, vous confirmez \xeatre majeur et acceptez nos conditions d'utilisation."})})]})})]})}):null};var u=t(2085);let g=e=>{let{isOpen:s,onClose:t}=e,{cart:l,removeItem:n,clearCart:x}=(0,o._$)(),{openModal:h}=(0,o.Je)(),{isAuthenticated:p,user:g}=(0,o.As)(),{addNotification:f}=(0,o.E$)(),[j,v]=r.useState(!1),y=l.items,b=l.totalAmount,N=l.totalItems,w=b>=50?0:5.99,k=b+w,C=async()=>{if(!j){if(!p){f({type:"warning",title:"\uD83D\uDD12 Connexion requise",message:"Vous devez vous connecter pour passer commande. Redirection en cours..."}),t(),setTimeout(()=>{window.location.href="/auth"},1500);return}try{if(0===y.length)return void f({type:"error",title:"Panier vide",message:"Ajoutez des produits \xe0 votre panier avant de commander"});v(!0),console.log("\uD83D\uDED2 D\xe9but du processus de commande..."),console.log("\uD83D\uDCE6 Articles du panier:",y),console.log("\uD83D\uDCB0 Total:",k);let e={items:y.map(e=>({productId:e.productId,variantId:e.variantId,quantity:e.quantity})),shippingAddress:{firstName:(null==g?void 0:g.firstName)||"Client",lastName:(null==g?void 0:g.lastName)||"Deltagum",email:(null==g?void 0:g.email)||"client-".concat(Date.now(),"@deltagum.com"),phone:(null==g?void 0:g.phone)||"0123456789",street:(null==g?void 0:g.address)||"123 Rue de la Livraison",city:(null==g?void 0:g.city)||"Paris",postalCode:(null==g?void 0:g.postalCode)||"75001",country:"France"},...(null==g?void 0:g.id)&&{customerId:g.id},totalAmount:k};console.log("\uD83D\uDCE4 Donn\xe9es de commande:",e);let s=await fetch("/api/orders",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)});if(!s.ok){let e=await s.json();console.error("❌ Erreur commande:",e),f({type:"error",title:"Erreur de commande",message:"Impossible de cr\xe9er la commande. Veuillez r\xe9essayer."}),v(!1);return}let{data:a}=await s.json();console.log("✅ Commande cr\xe9\xe9e:",a.id);let i=await fetch("/api/checkout/session",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({orderId:a.id})});if(!i.ok){let e=await i.json();console.error("❌ Erreur session:",e),f({type:"error",title:"Erreur de paiement",message:"Impossible de cr\xe9er la session de paiement. Veuillez r\xe9essayer."}),v(!1);return}let r=await i.json();r.success&&r.data.url?(console.log("✅ Session Stripe cr\xe9\xe9e, redirection..."),localStorage.setItem("deltagum_pending_order",JSON.stringify({orderId:a.id,cartItems:y,timestamp:Date.now()})),t(),window.location.href=r.data.url):(console.error("❌ Erreur session:",r),f({type:"error",title:"Erreur de session",message:"Probl\xe8me lors de la cr\xe9ation de la session de paiement"}),v(!1))}catch(e){console.error("❌ Erreur:",e),f({type:"error",title:"Erreur inattendue",message:"Une erreur s'est produite. Veuillez r\xe9essayer."}),v(!1)}}};return(0,a.jsxs)(i.aF,{isOpen:s,onClose:t,title:"Mon Panier",size:"lg",className:"max-h-[95vh] sm:max-h-[90vh]",children:[(0,a.jsx)(i.cw,{className:"p-0",children:0===y.length?(0,a.jsx)(()=>(0,a.jsxs)("div",{className:"text-center py-8 sm:py-12 px-4",children:[(0,a.jsx)(m.P.div,{className:"text-4xl sm:text-5xl lg:text-6xl mb-3 sm:mb-4",animate:{rotate:[0,10,-10,0]},transition:{duration:2,repeat:1/0},children:"\uD83D\uDED2"}),(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold text-gray-900 mb-2",children:"Votre panier est vide"}),(0,a.jsx)("p",{className:"text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base",children:"D\xe9couvrez nos d\xe9licieux bonbons et ajoutez-les \xe0 votre panier !"}),(0,a.jsx)(i.$n,{variant:"primary",onClick:t,className:"w-full sm:w-auto",children:"Continuer mes achats"})]}),{}):(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6",children:[(0,a.jsx)("div",{className:"max-h-80 sm:max-h-96 overflow-y-auto px-3 sm:px-6",children:(0,a.jsx)(d.N,{mode:"popLayout",children:y.map(e=>(0,a.jsxs)(m.P.div,{className:"flex items-center space-x-2 sm:space-x-4 py-3 sm:py-4 border-b border-gray-200 last:border-b-0",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},layout:!0,children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-lg overflow-hidden",children:e.image?(0,a.jsx)(u.default,{src:e.image,alt:e.name,width:64,height:64,className:"w-full h-full object-cover"}):(0,a.jsx)("div",{className:"w-full h-full flex items-center justify-center text-lg sm:text-2xl",children:"\uD83C\uDF6D"})}),(0,a.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 truncate text-sm sm:text-base",children:e.name}),(0,a.jsxs)("p",{className:"text-xs sm:text-sm text-gray-600",children:["Saveur: ",e.flavor]}),(0,a.jsx)("div",{className:"flex items-center space-x-2 mt-1",children:(0,a.jsx)("span",{className:"font-semibold text-pink-600 text-sm sm:text-base",children:(0,c.$g)(e.price)})})]}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)("span",{className:"text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full",children:["Qt\xe9: ",e.quantity]})}),(0,a.jsx)("div",{className:"text-right",children:(0,a.jsx)("p",{className:"font-semibold text-gray-900",children:(0,c.$g)(e.price*e.quantity)})}),(0,a.jsx)(i.$n,{variant:"ghost",size:"sm",onClick:()=>n(e.id),className:"text-red-500 hover:text-red-700 p-1",children:(0,a.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]},e.id))})}),(0,a.jsx)("div",{className:"px-6 py-4 bg-gray-50 border-t",children:(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsxs)("span",{className:"text-gray-700",children:["Sous-total (",N," article",N>1?"s":"",")"]}),(0,a.jsx)("span",{className:"text-gray-900 font-medium",children:(0,c.$g)(b)})]}),(0,a.jsxs)("div",{className:"flex justify-between text-sm",children:[(0,a.jsx)("span",{className:"text-gray-700",children:"Livraison"}),(0,a.jsx)("span",{className:(0,c.cn)("text-gray-900 font-medium",0===w&&"text-green-600 font-semibold"),children:0===w?"Gratuite":(0,c.$g)(w)})]}),(0,a.jsxs)("div",{className:"border-t pt-2 flex justify-between font-semibold text-lg",children:[(0,a.jsx)("span",{className:"text-gray-900",children:"Total"}),(0,a.jsx)("span",{className:"text-pink-600 font-bold",children:(0,c.$g)(k)})]})]})})]})}),y.length>0&&(0,a.jsxs)(i.jl,{className:"flex-col space-y-3",children:[!1,(0,a.jsxs)("div",{className:"flex space-x-3 w-full",children:[(0,a.jsx)(i.$n,{variant:"outline",onClick:()=>x(),className:"flex-1",children:"Vider le panier"}),(0,a.jsx)(i.$n,{variant:"primary",onClick:C,disabled:j,className:"flex-1",children:j?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"}),"Traitement en cours..."]}):"Commander (".concat((0,c.$g)(k),")")})]}),(0,a.jsx)("p",{className:"text-xs text-gray-500 text-center",children:"Paiement s\xe9curis\xe9 avec Stripe"})]})]})};var f=t(339),j=t(4100),v=t(8413),y=t(6489);let b=e=>{var s;let{isOpen:t,onClose:l}=e,[n,c]=(0,r.useState)("shipping"),[x,h]=(0,r.useState)(!1),p=(0,o._$)();if((0,o.cN)(),r.useEffect(()=>{t&&(c("shipping"),h(!1))},[t]),!(null==p||null==(s=p.cart)?void 0:s.items)||0===p.cart.items.length)return null;let u=e=>{c(e)},g=()=>{c("shipping"),h(!1),l()};return(0,a.jsx)(i.aF,{isOpen:t,onClose:g,title:"Finaliser votre commande",size:"full",className:"max-w-7xl",children:(0,a.jsxs)("div",{className:"min-h-[600px]",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)(j.s,{currentStep:n})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8",children:[(0,a.jsx)("div",{className:"lg:col-span-2",children:(0,a.jsxs)(d.N,{mode:"wait",children:["shipping"===n&&(0,a.jsx)(m.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"Informations de livraison"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(f.N,{onNext:()=>u("payment"),isProcessing:x})})]})},"shipping"),"payment"===n&&(0,a.jsx)(m.P.div,{initial:{opacity:0,x:-20},animate:{opacity:1,x:0},exit:{opacity:0,x:20},transition:{duration:.3},children:(0,a.jsxs)(i.Zp,{children:[(0,a.jsx)(i.aR,{children:(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-800",children:"M\xe9thode de paiement"})}),(0,a.jsx)(i.Wu,{children:(0,a.jsx)(y.b,{onBack:()=>u("shipping"),onSuccess:()=>{c("confirmation")},isProcessing:x,setIsProcessing:h})})]})},"payment"),"confirmation"===n&&(0,a.jsx)(m.P.div,{initial:{opacity:0,scale:.95},animate:{opacity:1,scale:1},transition:{duration:.5},children:(0,a.jsx)(i.Zp,{children:(0,a.jsxs)(i.Wu,{className:"text-center py-12",children:[(0,a.jsx)(m.P.div,{initial:{scale:0},animate:{scale:1},transition:{delay:.2,type:"spring"},className:"text-6xl mb-6",children:"\uD83C\uDF89"}),(0,a.jsx)("h3",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Commande confirm\xe9e !"}),(0,a.jsx)("p",{className:"text-gray-600 mb-8",children:"Merci pour votre commande ! Vous recevrez un email de confirmation sous peu."}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsx)("button",{onClick:g,className:"bg-pink-500 text-white px-8 py-3 rounded-lg hover:bg-pink-600 transition-colors mr-4",children:"Fermer"})})]})})},"confirmation")]})}),(0,a.jsx)("div",{className:"lg:col-span-1",children:(0,a.jsx)("div",{className:"sticky top-4",children:(0,a.jsx)(v.D,{})})})]})]})})},N=e=>{let{isOpen:s,onClose:t}=e,{selectedProduct:r}=(0,o.Bj)();return r?(0,a.jsxs)(i.aF,{isOpen:s,onClose:t,title:r.name,size:"lg",children:[(0,a.jsx)(i.cw,{children:(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-6xl mb-4",children:"\uD83C\uDF6D"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Modal de d\xe9tail du produit - \xc0 d\xe9velopper dans les prochaines t\xe2ches"})]})}),(0,a.jsx)(i.jl,{children:(0,a.jsx)(i.$n,{variant:"outline",onClick:t,children:"Fermer"})})]}):null};var w=t(6723);let k=(0,t(794).c)("pk_test_51RhRu9GP36x2BKi0Zeh7E8nYyXBaJdtwnnTM7hDuFB2ZPsd3lVEUl7A3Op83HkdNQAqdTfg2619sik6NJmYPYUUl00ZWiBSORN");function C(e){let{children:s}=e;return(0,a.jsx)(w.Elements,{stripe:k,options:{appearance:{theme:"stripe",variables:{colorPrimary:"#ff6b9d",colorBackground:"#ffffff",colorText:"#30313d",colorDanger:"#df1b41",fontFamily:"Inter, system-ui, sans-serif",spacingUnit:"4px",borderRadius:"8px"}},locale:"fr"},children:s})}let P=e=>{let{children:s,className:t,showHeader:l=!0,showFooter:u=!0}=e,{isCartOpen:f,isAuthModalOpen:j,setIsMobile:v,isLoading:y,closeAllModals:w}=(0,o.mL)(),{isOpen:k,closeModal:P}=(0,o.Je)(),{isVerified:D,showModal:E,confirmAge:z,denyAge:S}=n();!function(){let{checkAuth:e}=(0,o.As)();(0,r.useEffect)(()=>{e()},[e])}();let[A,T]=r.useState(!1);return((0,r.useEffect)(()=>{T(!0)},[]),(0,r.useEffect)(()=>{let e=()=>{v(window.innerWidth<768)};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[v]),(0,r.useEffect)(()=>(f||k||j?document.body.style.overflow="hidden":document.body.style.overflow="unset",()=>{document.body.style.overflow="unset"}),[f,k,j]),null===D)?(0,a.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-pink-400 via-purple-500 to-orange-400 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-white text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"}),(0,a.jsx)("p",{children:"Chargement..."})]})}):(0,a.jsxs)("div",{className:"min-h-screen flex flex-col bg-white",children:[(0,a.jsx)(p,{isOpen:E,onConfirm:z,onDeny:S}),D&&(0,a.jsxs)(C,{children:[l&&(0,a.jsx)(h.Header,{}),(0,a.jsx)("main",{className:(0,c.cn)("flex-1",l&&"pt-16 lg:pt-20",t),children:s}),u&&(0,a.jsx)(x.Footer,{}),A&&(0,a.jsxs)(d.N,{mode:"wait",children:[(0,a.jsx)(g,{isOpen:f,onClose:w},"cart-modal"),(0,a.jsx)(N,{isOpen:!1,onClose:w},"product-modal"),(0,a.jsx)(b,{isOpen:k,onClose:P},"checkout-modal")]})]}),(0,a.jsx)(i.N9,{}),(0,a.jsx)(d.N,{children:y&&(0,a.jsx)(m.P.div,{className:"fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center",initial:{opacity:0},animate:{opacity:1},exit:{opacity:0},children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(m.P.div,{className:"text-6xl mb-4",animate:{rotate:[0,360],scale:[1,1.2,1]},transition:{duration:2,repeat:1/0,ease:"easeInOut"},children:"\uD83C\uDF6D"}),(0,a.jsx)(m.P.p,{className:"text-lg font-medium text-gray-600",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.2},children:"Chargement en cours..."})]})})})]})},D=e=>{let{children:s}=e;return(0,a.jsx)(P,{showHeader:!1,showFooter:!1,className:"flex items-center justify-center",children:s})},E=e=>{let{children:s}=e;return(0,a.jsx)(P,{showHeader:!1,showFooter:!1,className:"flex items-center justify-center min-h-screen bg-gradient-to-br from-pink-50 to-orange-50",children:s})},z=e=>{let{children:s}=e;return(0,a.jsx)(P,{className:"bg-gray-50",children:(0,a.jsx)("div",{className:"container mx-auto px-4 py-8",children:s})})}},1016:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,7928,23)),Promise.resolve().then(t.t.bind(t,4915,23)),Promise.resolve().then(t.bind(t,6674)),Promise.resolve().then(t.bind(t,8392)),Promise.resolve().then(t.bind(t,766))},4915:()=>{},6674:(e,s,t)=>{"use strict";t.d(s,{Footer:()=>l});var a=t(5936),i=t(8581),r=t.n(i);t(5180);let l=()=>{let e={navigation:[{label:"Accueil",href:"/"},{label:"\xc0 propos & L\xe9gal",href:"/about"},{label:"Professionnels & Revendeurs",href:"/professionals"}],legal:[{label:"Mentions l\xe9gales",href:"/legal"},{label:"Politique de confidentialit\xe9",href:"/privacy"},{label:"Conditions g\xe9n\xe9rales",href:"/terms"},{label:"V\xe9rification d'\xe2ge",href:"/age-verification"},{label:"Cookies",href:"/cookies"}],social:[{label:"Facebook",href:"https://facebook.com/deltagum",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"})})},{label:"Instagram",href:"https://instagram.com/deltagum",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.876-.875-1.366-2.026-1.366-3.323s.49-2.448 1.366-3.323c.875-.876 2.026-1.366 3.323-1.366s2.448.49 3.323 1.366c.876.875 1.366 2.026 1.366 3.323s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z"})})},{label:"Twitter",href:"https://twitter.com/deltagum",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"})})},{label:"TikTok",href:"https://tiktok.com/@deltagum",icon:(0,a.jsx)("svg",{className:"w-5 h-5",fill:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{d:"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"})})}]};return(0,a.jsxs)("footer",{className:"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white",children:[(0,a.jsx)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8 py-8 sm:py-12 lg:py-16",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12",children:[(0,a.jsxs)("div",{className:"lg:col-span-1 text-center sm:text-left",children:[(0,a.jsxs)("div",{className:"flex items-center justify-center sm:justify-start space-x-2 sm:space-x-3 mb-4 sm:mb-6",children:[(0,a.jsx)("div",{className:"w-8 h-8 sm:w-10 sm:h-10",children:(0,a.jsx)("img",{src:"/img/logo.jpg",alt:"Deltagum Logo",className:"w-full h-full object-contain rounded-lg"})}),(0,a.jsx)("span",{className:"text-xl sm:text-2xl font-bold bg-gradient-to-r from-pink-400 to-orange-300 bg-clip-text text-transparent",children:"Deltagum"})]}),(0,a.jsx)("p",{className:"text-gray-300 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base",children:"Des bonbons artisanaux aux saveurs uniques qui \xe9veillent vos sens. D\xe9couvrez nos cr\xe9ations gourmandes aux parfums de fraise, myrtille et pomme."})]}),(0,a.jsxs)("div",{className:"text-center sm:text-left",children:[(0,a.jsx)("h3",{className:"font-bold text-white sm:text-lg mb-3 sm:mb-4",style:{color:"#ffffff"},children:"Informations"}),(0,a.jsx)("ul",{className:"space-y-1 sm:space-y-2",children:e.legal.map(e=>(0,a.jsx)("li",{children:(0,a.jsx)(r(),{href:e.href,className:"text-gray-300 hover:text-pink-300 transition-colors duration-200 text-sm sm:text-base",children:e.label})},e.href))})]}),(0,a.jsxs)("div",{className:"text-center sm:text-left",children:[(0,a.jsx)("h3",{className:"font-bold text-white sm:text-lg mb-3 sm:mb-4",style:{color:"#ffffff"},children:"Contact"}),(0,a.jsxs)("div",{className:"space-y-2 sm:space-y-3 mb-4 sm:mb-6",children:[(0,a.jsxs)("p",{className:"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base",children:[(0,a.jsx)("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),"<EMAIL>"]}),(0,a.jsxs)("p",{className:"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base",children:[(0,a.jsx)("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),"+33 1 23 45 67 89"]})]}),(0,a.jsx)("h4",{className:"font-semibold mb-2 sm:mb-3 text-pink-300 text-sm sm:text-base",children:"Suivez-nous"}),(0,a.jsx)("div",{className:"flex justify-center sm:justify-start space-x-2 sm:space-x-3",children:e.social.map(e=>(0,a.jsx)("a",{href:e.href,target:"_blank",rel:"noopener noreferrer",className:"p-1.5 sm:p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-pink-300 hover:bg-gray-700 transition-colors duration-200","aria-label":e.label,children:e.icon},e.label))})]})]})}),(0,a.jsx)("div",{className:"border-t border-gray-700 py-4 sm:py-6",children:(0,a.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8",children:[(0,a.jsx)("div",{className:"bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-yellow-300 text-xs sm:text-sm font-semibold mb-1 sm:mb-2",children:"\uD83C\uDF3F AVERTISSEMENT IMPORTANT - PRODUITS DELTA-9 THC"}),(0,a.jsxs)("p",{className:"text-yellow-200 text-xs leading-relaxed",children:["Nos produits sont strictement r\xe9serv\xe9s aux personnes majeures (18 ans et plus).",(0,a.jsx)("br",{}),"Ne pas conduire ou utiliser de machines apr\xe8s consommation. D\xe9conseill\xe9 aux femmes enceintes ou allaitantes.",(0,a.jsx)("br",{}),"Tenir hors de port\xe9e des enfants. Consommer avec mod\xe9ration."]})]})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0",children:[(0,a.jsx)("p",{className:"text-gray-400 text-xs sm:text-sm text-center sm:text-left",children:"\xa9 2024 Deltagum . Tous droits r\xe9serv\xe9s. Fait avec ❤️ en France."}),(0,a.jsx)("div",{className:"flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-400",children:(0,a.jsx)("span",{children:"\uD83C\uDF3F D\xe9tente naturelle avec style ! ✨"})})]})]})})]})}},8392:(e,s,t)=>{"use strict";t.d(s,{Header:()=>j});var a=t(5936),i=t(9084),r=t(6652),l=t(1774),n=t(5156),o=t(6953),c=t(2096),d=t(4255),m=t(9058),x=t(642),h=t(3527),p=t(3950),u=t(8581),g=t.n(u),f=t(5180);let j=()=>{let[e,s]=(0,f.useState)(!1),{getTotalItems:t}=(0,n._$)(),{openCart:u}=(0,n.mL)(),[j,v]=(0,f.useState)(!1),[y,b]=(0,f.useState)(!1),{products:N,fetchProducts:w}=(0,n.Bj)(),{isAuthenticated:k,user:C,logout:P}=(0,n.As)();(0,f.useEffect)(()=>{let e=()=>{s(window.scrollY>50)};return window.addEventListener("scroll",e),()=>window.removeEventListener("scroll",e)},[]),(0,f.useEffect)(()=>{w()},[w]);let D=t(),E=[{label:"Accueil",href:"/",type:"link"},{label:"\xc0 propos & L\xe9gal",href:"/about",type:"link"},{label:"Professionnels & Revendeurs",href:"/professionals",type:"link"}],z=e=>{if("scroll"===e.type){let s=document.querySelector(e.href);s&&s.scrollIntoView({behavior:"smooth"})}else window.location.href=e.href;v(!1)};return(0,a.jsx)(o.P.header,{className:(0,l.cn)("fixed top-0 left-0 right-0 z-40 transition-all duration-300",e?"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200":"bg-transparent"),initial:r.C9.initial,animate:r.C9.animate,transition:r.C9.transition,children:(0,a.jsxs)("div",{className:"container mx-auto px-3 sm:px-4 lg:px-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between h-14 sm:h-16 lg:h-20",children:[(0,a.jsx)(o.P.div,{className:"flex items-center space-x-1 sm:space-x-2",whileHover:{scale:1.05},whileTap:{scale:.95},children:(0,a.jsxs)(g(),{href:"/",className:"flex items-center space-x-1 sm:space-x-2",children:[(0,a.jsx)(o.P.div,{className:"relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12",animate:{rotate:[0,5,-5,0]},transition:{duration:2,repeat:1/0,repeatDelay:3},children:(0,a.jsx)("img",{src:"/img/logo.jpg",alt:"Deltagum Logo",className:"w-full h-full object-contain rounded-lg"})}),(0,a.jsx)("span",{className:(0,l.cn)("text-lg sm:text-xl lg:text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-400 bg-clip-text text-transparent","hidden xs:block"),children:"Deltagum"})]})}),(0,a.jsxs)("nav",{className:"hidden lg:flex items-center space-x-8",children:[(0,a.jsx)(g(),{href:E[0].href,children:(0,a.jsxs)(o.P.button,{className:(0,l.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:0},children:[E[0].label,(0,a.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]})}),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsxs)(o.P.button,{onClick:()=>b(!y),className:(0,l.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2 flex items-center space-x-1"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.1},children:[(0,a.jsx)("span",{children:"Produits"}),(0,a.jsx)(d.A,{className:(0,l.cn)("w-4 h-4 transition-transform",y&&"rotate-180")}),(0,a.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]}),(0,a.jsx)(c.N,{children:y&&(0,a.jsx)(o.P.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},exit:{opacity:0,y:10},className:"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50",children:N.map(e=>(0,a.jsx)(g(),{href:"/products/".concat(e.id),className:"block px-4 py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors",onClick:()=>b(!1),children:(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("img",{src:e.image,alt:e.name,className:"w-8 h-8 rounded object-cover"}),(0,a.jsx)("div",{children:(0,a.jsx)("div",{className:"font-medium",children:e.name})})]})},e.id))})})]}),E.slice(1).map((e,s)=>(0,a.jsx)(g(),{href:e.href,children:(0,a.jsxs)(o.P.button,{className:(0,l.cn)("text-black hover:text-pink-500 font-medium transition-colors","relative py-2"),whileHover:{y:-2},initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:(s+2)*.1},children:[e.label,(0,a.jsx)(o.P.div,{className:"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400",initial:{scaleX:0},whileHover:{scaleX:1},transition:{duration:.2}})]})},s))]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2 sm:space-x-4",children:[k?(0,a.jsxs)("div",{className:"relative group",children:[(0,a.jsx)(o.P.button,{className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(m.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-green-600"})}),(0,a.jsxs)("div",{className:"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-9999",children:[(0,a.jsxs)("div",{className:"p-3 border-b",children:[(0,a.jsxs)("p",{className:"text-sm font-medium text-gray-900",children:[null==C?void 0:C.firstName," ",null==C?void 0:C.lastName]}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:null==C?void 0:C.email})]}),(0,a.jsxs)("div",{className:"p-2",children:[(0,a.jsx)(g(),{href:"/profile",children:(0,a.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1",children:"Mon profil"})}),(null==C?void 0:C.role)==="ADMIN"&&(0,a.jsx)(g(),{href:"/admin/dashboard",children:(0,a.jsx)("button",{className:"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1",children:"Dashboard Admin"})}),(0,a.jsx)("button",{onClick:P,className:"w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors",children:"Se d\xe9connecter"})]})]})]}):(0,a.jsx)(g(),{href:"/auth",children:(0,a.jsx)(o.P.button,{className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:(0,a.jsx)(m.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-black"})})}),(0,a.jsx)(o.P.div,{className:"relative",children:(0,a.jsxs)(o.P.button,{onClick:()=>{u()},className:"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors",whileHover:{scale:1.1},whileTap:{scale:.9},children:[(0,a.jsx)(x.A,{className:"w-5 h-5 sm:w-6 sm:h-6 text-black"}),(0,a.jsx)(c.N,{children:D>0&&(0,a.jsx)(o.P.div,{className:"absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1",initial:{scale:0},animate:{scale:1},exit:{scale:0},children:(0,a.jsx)(i.Ex,{variant:"primary",size:"sm",rounded:!0,className:"min-w-[18px] h-4 sm:min-w-[20px] sm:h-5 text-xs",children:D})})})]})}),(0,a.jsx)("div",{className:"lg:hidden",children:(0,a.jsx)(i.$n,{variant:"ghost",size:"md",onClick:()=>v(!j),className:"p-1.5 sm:p-2",children:(0,a.jsx)(o.P.div,{animate:j?{rotate:180}:{rotate:0},transition:{duration:.2},children:j?(0,a.jsx)(h.A,{className:"w-5 h-5 sm:w-6 sm:h-6"}):(0,a.jsx)(p.A,{className:"w-5 h-5 sm:w-6 sm:h-6"})})})})]})]}),(0,a.jsx)(c.N,{children:j&&(0,a.jsx)(o.P.div,{className:"lg:hidden border-t border-gray-200 bg-white/95 backdrop-blur-md",initial:{opacity:0,height:0},animate:{opacity:1,height:"auto"},exit:{opacity:0,height:0},transition:{duration:.2},children:(0,a.jsx)("nav",{className:"py-3 sm:py-4 space-y-1 sm:space-y-2",children:E.map((e,s)=>(0,a.jsx)(o.P.button,{onClick:()=>z(e),className:"block w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors text-sm sm:text-base font-medium",initial:{opacity:0,x:-20},animate:{opacity:1,x:0},transition:{delay:.05*s},children:e.label},e.href))})})})]})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[3627,6953,4026,8581,7614,4952,9084,2118,8656,75,7358],()=>s(1016)),_N_E=e.O()}]);