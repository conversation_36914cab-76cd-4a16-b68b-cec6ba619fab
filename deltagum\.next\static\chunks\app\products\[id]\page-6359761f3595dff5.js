(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3403],{4094:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>k});var a=t(5936),r=t(9084),l=t(6652);let i={STRAWBERRY:"Fraise",BLUEBERRY:"Myrtille",APPLE:"Pomme"};var n=t(6953),c=t(5180),o=t(1774),d=t(3894),x=t(6719),m=t(2674),u=t(8787);t(9378),t(7889),t(9561),t(1855);let g={STRAWBERRY:{name:"Fraise",emoji:"\uD83C\uDF53",color:"from-pink-400 to-red-500",bgColor:"bg-pink-50",borderColor:"border-pink-300",selectedBorder:"border-pink-500",textColor:"text-pink-700"},BLUEBERRY:{name:"My<PERSON><PERSON>",emoji:"\uD83E\uDED0",color:"from-blue-400 to-purple-500",bgColor:"bg-blue-50",borderColor:"border-blue-300",selectedBorder:"border-blue-500",textColor:"text-blue-700"},APPLE:{name:"Pomme",emoji:"\uD83C\uDF4F",color:"from-green-400 to-emerald-500",bgColor:"bg-green-50",borderColor:"border-green-300",selectedBorder:"border-green-500",textColor:"text-green-700"}},h=e=>{var s;let{variants:t,selectedVariant:l,onVariantSelect:h}=e,p=(0,c.useRef)(null),b=e=>g[e]||{name:function(e){return i[e]||e}(e),emoji:"\uD83C\uDF6D",color:"from-gray-400 to-gray-500",bgColor:"bg-gray-50",borderColor:"border-gray-300",selectedBorder:"border-gray-500",textColor:"text-gray-700"};return(0,a.jsxs)("div",{className:"space-y-6",children:[l&&(0,a.jsx)(n.P.div,{className:"text-center",initial:{opacity:0,y:-20},animate:{opacity:1,y:0},transition:{duration:.5},children:(0,a.jsxs)("div",{className:"relative w-48 h-48 mx-auto rounded-2xl overflow-hidden shadow-xl",children:[(0,a.jsx)("img",{src:(null==(s=l.images)?void 0:s[0])||"/img/placeholder.svg",alt:"D\xe9lices Deltagum saveur ".concat(b(l.flavor).name),className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,a.jsx)("div",{className:"absolute inset-0 bg-gradient-to-br ".concat(b(l.flavor).color," opacity-20")}),(0,a.jsx)("div",{className:"absolute bottom-3 left-3 right-3",children:(0,a.jsx)("div",{className:"bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg",children:(0,a.jsxs)("p",{className:"text-base font-bold text-gray-800 text-center",children:[b(l.flavor).emoji," ",b(l.flavor).name]})})})]})},l.id),(0,a.jsxs)("div",{className:"relative",children:[(0,a.jsx)("button",{onClick:()=>{var e;return null==(e=p.current)?void 0:e.slidePrev()},className:"flavor-nav-button prev","aria-label":"Saveur pr\xe9c\xe9dente",children:(0,a.jsx)(d.A,{size:20})}),(0,a.jsx)("button",{onClick:()=>{var e;return null==(e=p.current)?void 0:e.slideNext()},className:"flavor-nav-button next","aria-label":"Saveur suivante",children:(0,a.jsx)(x.A,{size:20})}),(0,a.jsx)(u.RC,{ref:p,modules:[m.Vx,m.dK],spaceBetween:16,slidesPerView:1,centeredSlides:!0,pagination:{clickable:!0,bulletClass:"swiper-pagination-bullet !bg-pink-300",bulletActiveClass:"swiper-pagination-bullet-active !bg-pink-500"},breakpoints:{640:{slidesPerView:2,spaceBetween:20},768:{slidesPerView:3,spaceBetween:24}},className:"!pb-12",children:t.map(e=>{var s;let t=b(e.flavor),i=(null==l?void 0:l.id)===e.id,c=0===e.stock;return(0,a.jsx)(u.qr,{children:(0,a.jsx)(n.P.div,{whileHover:{scale:c?1:1.02},whileTap:{scale:c?1:.98},className:"h-full",children:(0,a.jsxs)("div",{className:"\n                      flavor-card relative p-6 rounded-2xl border-2 cursor-pointer text-center h-full\n                      ".concat(i?"".concat(t.selectedBorder," ").concat(t.bgColor," shadow-lg selected"):"".concat(t.borderColor," bg-white hover:").concat(t.bgColor," shadow-md"),"\n                      ").concat(c?"opacity-50 cursor-not-allowed":"","\n                    "),onClick:()=>!c&&h(e),children:[i&&(0,a.jsx)(n.P.div,{className:"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:30},children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),c&&(0,a.jsx)("div",{className:"absolute -top-2 -left-2",children:(0,a.jsx)(r.Ex,{variant:"danger",size:"sm",children:"\xc9puis\xe9"})}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("div",{className:"text-5xl",children:t.emoji}),(0,a.jsx)("h5",{className:"font-bold text-xl ".concat(t.textColor),children:t.name}),(0,a.jsx)("div",{className:"text-xl font-semibold text-gray-800",children:(0,o.$g)(Number((null==(s=e.product)?void 0:s.price)||0))}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e.stock>10?"bg-green-500":e.stock>0?"bg-yellow-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:e.stock>10?"En stock":e.stock>0?"".concat(e.stock," restant").concat(e.stock>1?"s":""):"\xc9puis\xe9"})]})]}),!c&&(0,a.jsx)(n.P.div,{className:"absolute inset-0 rounded-2xl bg-gradient-to-r ".concat(t.color," opacity-0 pointer-events-none"),whileHover:{opacity:.1},transition:{duration:.2}})]})})},e.id)})})]}),0===t.length&&(0,a.jsxs)(n.P.div,{className:"text-center py-8",initial:{opacity:0},animate:{opacity:1},children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF6D"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Aucune saveur disponible pour ce produit"})]})]})},p={STRAWBERRY:{name:"Fraise",emoji:"\uD83C\uDF53",color:"from-pink-400 to-red-500",bgColor:"bg-pink-50",borderColor:"border-pink-300",selectedBorder:"border-pink-500",textColor:"text-pink-700"},BLUEBERRY:{name:"Myrtille",emoji:"\uD83E\uDED0",color:"from-blue-400 to-purple-500",bgColor:"bg-blue-50",borderColor:"border-blue-300",selectedBorder:"border-blue-500",textColor:"text-blue-700"},APPLE:{name:"Pomme",emoji:"\uD83C\uDF4F",color:"from-green-400 to-emerald-500",bgColor:"bg-green-50",borderColor:"border-green-300",selectedBorder:"border-green-500",textColor:"text-green-700"}},b=e=>{let{variants:s,selectedVariant:t,onVariantSelect:o}=e,[d,x]=(0,c.useState)("desktop");(0,c.useEffect)(()=>{let e=()=>{let e=window.innerWidth;e<640?x("mobile"):e<1024?x("tablet"):x("desktop")};return e(),window.addEventListener("resize",e),()=>window.removeEventListener("resize",e)},[]);let m=e=>p[e]||{name:function(e){return i[e]||e}(e),emoji:"\uD83C\uDF6D",color:"from-gray-400 to-gray-500",bgColor:"bg-gray-50",borderColor:"border-gray-300",selectedBorder:"border-gray-500",textColor:"text-gray-700"};return"mobile"===d||"tablet"===d?(0,a.jsx)(h,{variants:s,selectedVariant:t,onVariantSelect:o}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)(n.P.div,{className:"grid grid-cols-3 gap-4",variants:l.bK,initial:"initial",animate:"animate",children:s.map(e=>{let s=m(e.flavor),i=(null==t?void 0:t.id)===e.id,c=0===e.stock;return(0,a.jsx)(n.P.div,{variants:l.Rf,whileHover:{scale:c?1:1.05},whileTap:{scale:c?1:.95},children:(0,a.jsxs)("div",{className:"\n                  relative p-4 rounded-xl border-2 cursor-pointer transition-all duration-300 text-center\n                  ".concat(i?"".concat(s.selectedBorder," ").concat(s.bgColor," shadow-lg"):"".concat(s.borderColor," bg-white hover:").concat(s.bgColor),"\n                  ").concat(c?"opacity-50 cursor-not-allowed":"","\n                "),onClick:()=>!c&&o(e),children:[i&&(0,a.jsx)(n.P.div,{className:"absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:500,damping:30},children:(0,a.jsx)("span",{className:"text-white text-xs",children:"✓"})}),c&&(0,a.jsx)("div",{className:"absolute -top-2 -left-2",children:(0,a.jsx)(r.Ex,{variant:"danger",size:"sm",children:"\xc9puis\xe9"})}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsx)("h5",{className:"font-bold text-lg ".concat(s.textColor),children:s.name}),(0,a.jsxs)("div",{className:"flex items-center justify-center space-x-1",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(e.stock>10?"bg-green-500":e.stock>0?"bg-yellow-500":"bg-red-500")}),(0,a.jsx)("span",{className:"text-xs text-gray-500",children:e.stock>10?"En stock":e.stock>0?"".concat(e.stock," restant").concat(e.stock>1?"s":""):"\xc9puis\xe9"})]})]}),!c&&(0,a.jsx)(n.P.div,{className:"absolute inset-0 rounded-xl bg-gradient-to-r ".concat(s.color," opacity-0 pointer-events-none"),whileHover:{opacity:.1},transition:{duration:.2}})]})},e.id)})}),0===s.length&&(0,a.jsxs)(n.P.div,{className:"text-center py-8",initial:{opacity:0},animate:{opacity:1},children:[(0,a.jsx)("div",{className:"text-4xl mb-2",children:"\uD83C\uDF6D"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Aucune saveur disponible pour ce produit"})]})]})};function j(e){var s,t,l;let{priceTiers:i,selectedQuantity:c,onQuantityChange:d,className:x=""}=e,m=[...i].sort((e,s)=>e.quantity-s.quantity),u=(null==(s=m[0])?void 0:s.price)||0,g=e=>{let s=u*e.quantity,t=s-e.price,a=Math.round(t/s*100);return{savings:t,savingsPercent:a}};return(0,a.jsxs)("div",{className:"space-y-4 ".concat(x),children:[(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900 mb-2",children:"Choisissez votre quantit\xe9"}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Plus vous achetez, plus vous \xe9conomisez !"})]}),(0,a.jsx)("div",{className:"flex justify-center",children:(0,a.jsx)("div",{className:"grid grid-cols-2 lg:grid-cols-3 gap-4 max-w-md",children:m.map(e=>{let{savings:s,savingsPercent:t}=g(e),l=c===e.quantity,i=6===e.quantity||5===e.quantity;return(0,a.jsxs)(n.P.div,{className:"relative",whileHover:{scale:1.02},whileTap:{scale:.98},children:[i&&(0,a.jsx)(r.Ex,{variant:"primary",className:"absolute -top-2 left-1/2 transform -translate-x-1/2 z-10 text-xs",children:"Populaire"}),(0,a.jsxs)(r.$n,{variant:l?"primary":"outline",size:"lg",onClick:()=>d(e.quantity),className:"w-full h-32 p-4 flex flex-col items-center justify-center space-y-2 ".concat(l?"ring-2 ring-pink-500 ring-offset-2":"hover:border-pink-300"),children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.quantity}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-lg font-semibold ".concat(l?"text-white":"text-gray-900"),children:(0,o.$g)(e.price)}),e.quantity>1&&(0,a.jsxs)("div",{className:"text-xs ".concat(l?"text-pink-100":"text-gray-500"),children:[(0,o.$g)(e.price/e.quantity)," / unit\xe9"]})]}),s>0&&(0,a.jsxs)("div",{className:"text-xs font-medium ".concat(l?"text-pink-100":"text-green-600"),children:["\xc9conomisez ",t,"%"]})]})]},e.id)})})}),(0,a.jsxs)("div",{className:"text-center p-4 bg-gray-50 rounded-lg",children:[(0,a.jsx)("div",{className:"text-sm text-gray-600 mb-1",children:"Prix total"}),(0,a.jsx)("div",{className:"text-2xl font-bold text-gray-900",children:(0,o.$g)((null==(t=m.find(e=>e.quantity===c))?void 0:t.price)||0)}),c>1&&(0,a.jsxs)("div",{className:"text-sm text-gray-500",children:["Soit"," ",(0,o.$g)(((null==(l=m.find(e=>e.quantity===c))?void 0:l.price)||0)/c)," ","par unit\xe9"]})]})]})}var v=t(5156),f=t(9196),N=t(642),y=t(8581),w=t.n(y),C=t(4982);function k(){let e=(0,C.useParams)().id,{products:s,fetchProducts:t}=(0,v.Bj)(),{addItem:i}=(0,v._$)(),{addNotification:d}=(0,v.E$)(),[x,m]=(0,c.useState)(null),[u,g]=(0,c.useState)(null),[h,p]=(0,c.useState)(1),[y,k]=(0,c.useState)(0);(0,c.useEffect)(()=>{t()},[t]),(0,c.useEffect)(()=>{if(s.length>0&&e){let t=s.find(s=>s.id===e);t&&(m(t),t.variants&&t.variants.length>0&&g(t.variants[0]),t.priceTiers&&t.priceTiers.length>0&&p([...t.priceTiers].sort((e,s)=>e.quantity-s.quantity)[0].quantity))}},[s,e]),(0,c.useEffect)(()=>{k(0)},[u]);let E=(e,s)=>{if(!e.priceTiers||0===e.priceTiers.length)return Number(e.basePrice)*s;let t=e.priceTiers.find(e=>e.quantity===s);return t?Number(t.price):Number(e.basePrice)*s};return x?(0,a.jsx)("main",{className:"min-h-screen bg-gradient-to-br from-pink-50 via-white to-orange-50 pt-20",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,a.jsx)(n.P.div,{className:"mb-8",initial:l.qG.initial,animate:l.qG.animate,children:(0,a.jsxs)(w(),{href:"/",className:"inline-flex items-center text-gray-600 hover:text-pink-500 transition-colors",children:[(0,a.jsx)(f.A,{className:"w-4 h-4 mr-2"}),"Retour \xe0 l'accueil"]})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12",children:[(0,a.jsx)(n.P.div,{className:"space-y-6",initial:{opacity:0,y:50},animate:{opacity:1,y:0},children:u&&u.images&&u.images.length>0?(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl",children:[(0,a.jsx)("img",{src:u.images[y],alt:"".concat(u.name," - Image ").concat(y+1),className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 space-y-2",children:[(0,a.jsx)("div",{className:"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"\uD83C\uDF3F THC"}),(0,a.jsx)("div",{className:"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"18+"})]})]}),u.images.length>1&&(0,a.jsx)("div",{className:"flex justify-center space-x-3",children:u.images.map((e,s)=>(0,a.jsx)("button",{onClick:()=>k(s),className:"relative w-20 h-20 rounded-lg overflow-hidden border-2 transition-all ".concat(y===s?"border-pink-500 ring-2 ring-pink-200":"border-gray-200 hover:border-gray-300"),children:(0,a.jsx)("img",{src:e,alt:"".concat(u.name," - Miniature ").concat(s+1),className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}})},s))}),(0,a.jsxs)("div",{className:"space-y-4 mt-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Caract\xe9ristiques"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"THC"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"< 0,3%"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Delta-9 THC"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:"< 0,3%"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Origine"}),(0,a.jsx)("span",{className:"font-medium",children:"UE"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Certification"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"Bio"})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-pink-50 to-purple-50 rounded-xl p-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-pink-500 rounded-full mr-2"}),"Avantages Deltagum"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm text-gray-600",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Go\xfbt naturel et authentique"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Dosage pr\xe9cis et constant"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Texture fondante unique"]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("span",{className:"text-green-500 mr-2",children:"✓"}),"Emballage discret et pratique"]})]})]})]})]}):(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"relative aspect-square rounded-2xl overflow-hidden bg-white shadow-xl",children:[(0,a.jsx)("img",{src:x.image||"/img/placeholder.svg",alt:x.name,className:"w-full h-full object-cover",onError:e=>{e.currentTarget.src="/img/placeholder.svg"}}),(0,a.jsxs)("div",{className:"absolute top-4 right-4 space-y-2",children:[(0,a.jsx)("div",{className:"bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"\uD83C\uDF3F CBD"}),(0,a.jsx)("div",{className:"bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full",children:"18+"})]})]}),(0,a.jsx)("div",{className:"space-y-4",children:(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-50 to-blue-50 rounded-xl p-4",children:[(0,a.jsxs)("h4",{className:"font-semibold text-gray-800 mb-3 flex items-center",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-500 rounded-full mr-2"}),"Caract\xe9ristiques"]}),(0,a.jsxs)("div",{className:"space-y-2 text-sm",children:[(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"THC"}),(0,a.jsx)("span",{className:"font-medium text-green-600",children:"< 0,3%"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"CBD"}),(0,a.jsx)("span",{className:"font-medium text-blue-600",children:"Premium"})]}),(0,a.jsxs)("div",{className:"flex justify-between",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Origine"}),(0,a.jsx)("span",{className:"font-medium",children:"UE"})]})]})]})})]})}),(0,a.jsxs)(n.P.div,{className:"space-y-8",initial:{opacity:0,x:50},animate:{opacity:1,x:0},transition:{delay:.2},children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:x.name}),(0,a.jsx)("p",{className:"text-lg text-gray-600 leading-relaxed",children:x.description}),x.dosage&&(0,a.jsxs)("div",{className:"mt-4 inline-flex items-center bg-blue-100 text-blue-800 px-3 py-1 rounded-full text-sm font-medium",children:["\uD83D\uDC8A ",x.dosage," par unit\xe9"]})]}),(0,a.jsx)("div",{className:"text-center lg:text-left",children:(0,a.jsx)("div",{className:"flex items-center justify-center lg:justify-start space-x-3",children:(0,a.jsx)("span",{className:"text-4xl font-bold text-pink-600",children:(0,o.$g)(E(x,h))})})}),x.variants&&x.variants.length>0&&(0,a.jsx)(b,{variants:x.variants.map(e=>({...e,product:x})),selectedVariant:u,onVariantSelect:g}),x.priceTiers&&x.priceTiers.length>0&&(0,a.jsx)(j,{priceTiers:x.priceTiers,selectedQuantity:h,onQuantityChange:e=>{p(e)},className:"mb-6"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"w-3 h-3 rounded-full bg-green-500"}),(0,a.jsx)("span",{className:"text-sm text-gray-600",children:"En stock"})]}),(0,a.jsxs)(r.$n,{onClick:()=>{x&&u&&(i({productId:x.id,variantId:u.id,quantity:h,name:x.name,price:E(x,h),image:x.image,flavor:u.flavor,color:u.color}),d({type:"success",title:"Produit ajout\xe9 !",message:"".concat(h," ").concat(x.name," ajout\xe9 au panier !")}),x.priceTiers&&x.priceTiers.length>0&&p([...x.priceTiers].sort((e,s)=>e.quantity-s.quantity)[0].quantity))},disabled:!u,size:"lg",className:"w-full text-lg py-4",children:[(0,a.jsx)(N.A,{className:"w-5 h-5 mr-2"}),"Ajouter au panier -"," ",(0,o.$g)(E(x,h))]}),(0,a.jsxs)("div",{className:"bg-yellow-50 border border-yellow-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-semibold text-yellow-800 mb-2",children:"⚠️ Informations importantes"}),(0,a.jsxs)("ul",{className:"text-sm text-yellow-700 space-y-1",children:[(0,a.jsx)("li",{children:"• R\xe9serv\xe9 aux adultes (18+)"}),(0,a.jsx)("li",{children:"• Ne pas conduire apr\xe8s consommation"}),(0,a.jsx)("li",{children:"• Interdit sous traitement m\xe9dical"}),(0,a.jsx)("li",{children:"• THC < 0,3% (conforme UE)"})]})]})]})]})]})}):(0,a.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"Chargement du produit..."})]})})}},5054:(e,s,t)=>{Promise.resolve().then(t.bind(t,4094))},9378:()=>{}},e=>{var s=s=>e(e.s=s);e.O(0,[8868,6953,4026,8581,4511,9084,8656,75,7358],()=>s(5054)),_N_E=e.O()}]);