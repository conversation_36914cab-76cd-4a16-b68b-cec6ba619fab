(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6636],{767:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("map-pin",[["path",{d:"M20 10c0 4.993-5.539 10.193-7.399 11.799a1 1 0 0 1-1.202 0C9.539 20.193 4 14.993 4 10a8 8 0 0 1 16 0",key:"1r0f0z"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},1204:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]])},2095:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("calendar",[["path",{d:"M8 2v4",key:"1cmpym"}],["path",{d:"M16 2v4",key:"4m81vk"}],["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",key:"1hopcy"}],["path",{d:"M3 10h18",key:"8toen8"}]])},2205:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>b});var t=a(5936),l=a(9084),r=a(5156),c=a(9270),i=a(9058),n=a(4487),d=a(1204),o=a(4604),m=a(3527),x=a(7231),h=a(767),u=a(2095);let p=(0,a(3411).A)("credit-card",[["rect",{width:"20",height:"14",x:"2",y:"5",rx:"2",key:"ynyp8z"}],["line",{x1:"2",x2:"22",y1:"10",y2:"10",key:"1b3vmo"}]]);var j=a(4982),N=a(5180);function b(){var e,s;let a=(0,j.useRouter)(),{user:b,isAuthenticated:f,logout:g,isLoading:v,checkAuth:y}=(0,r.As)(),[k,A]=(0,N.useState)(!1),[w,C]=(0,N.useState)([]),[P,M]=(0,N.useState)(!1),[E,S]=(0,N.useState)("profile"),[R,q]=(0,N.useState)({firstName:"",lastName:"",email:"",phone:"",address:"",postalCode:"",city:""});(0,N.useEffect)(()=>{y()},[y]),(0,N.useEffect)(()=>{v||f||a.push("/auth")},[f,v,a]),(0,N.useEffect)(()=>{b&&q({firstName:b.firstName||"",lastName:b.lastName||"",email:b.email||"",phone:b.phone||"",address:b.address||"",postalCode:b.postalCode||"",city:b.city||""})},[b]);let D=async()=>{if(!(null==b?void 0:b.id))return void console.log("❌ Pas d'ID utilisateur pour r\xe9cup\xe9rer les commandes");console.log("\uD83D\uDD0D R\xe9cup\xe9ration des commandes pour l'utilisateur:",b.id),M(!0);try{let e="/api/orders?customerId=".concat(b.id);console.log("\uD83D\uDCE1 URL de la requ\xeate:",e);let s=await fetch(e),a=await s.json();if(console.log("\uD83D\uDCE5 R\xe9ponse API orders:",a),console.log("\uD83D\uDCCA Statut de la r\xe9ponse:",s.status),a.success){let e=a.data.orders||[];console.log("✅ Commandes r\xe9cup\xe9r\xe9es:",e.length),console.log("\uD83D\uDCCB D\xe9tails des commandes:",e),C(e)}else console.error("❌ Erreur dans la r\xe9ponse API:",a.error)}catch(e){console.error("❌ Erreur lors de la r\xe9cup\xe9ration des commandes:",e)}finally{M(!1)}};(0,N.useEffect)(()=>{(null==b?void 0:b.id)&&"orders"===E&&D()},[null==b?void 0:b.id,E]);let W=async()=>{try{let e=await fetch("/api/user/profile",{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(R)});if(e.ok)alert("Profil mis \xe0 jour avec succ\xe8s !"),A(!1),await y();else{let s=await e.json();alert(s.error||"Erreur lors de la mise \xe0 jour")}}catch(e){alert("Erreur de connexion")}};return v||!b?(0,t.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gray-50",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"w-16 h-16 border-4 border-pink-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{className:"text-lg font-medium text-gray-900",children:"Chargement..."})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl p-8 mb-8",children:(0,t.jsxs)("div",{className:"flex items-center space-x-6",children:[(0,t.jsxs)("div",{className:"w-20 h-20 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:[null==(e=b.firstName)?void 0:e.charAt(0),null==(s=b.lastName)?void 0:s.charAt(0)]}),(0,t.jsxs)("div",{children:[(0,t.jsxs)("h1",{className:"text-3xl font-bold text-black",children:[b.firstName," ",b.lastName]}),(0,t.jsxs)("p",{className:"text-black flex items-center mt-2",children:[(0,t.jsx)(c.A,{className:"w-4 h-4 mr-2"}),b.email]}),"ADMIN"===b.role&&(0,t.jsx)("div",{className:"mt-2 inline-block bg-gradient-to-r from-pink-500 to-orange-500 text-white text-xs px-2 py-1 rounded-full",children:"Administrateur"})]})]})}),(0,t.jsx)("div",{className:"bg-white rounded-2xl shadow-xl mb-8",children:(0,t.jsxs)("div",{className:"flex border-b",children:[(0,t.jsxs)("button",{onClick:()=>S("profile"),className:"flex-1 py-4 px-6 text-center font-medium transition-colors ".concat("profile"===E?"text-pink-600 border-b-2 border-pink-600 bg-pink-50":"text-gray-600 hover:text-pink-600"),children:[(0,t.jsx)(i.A,{className:"w-5 h-5 inline-block mr-2"}),"Mon Profil"]}),(0,t.jsxs)("button",{onClick:()=>S("orders"),className:"flex-1 py-4 px-6 text-center font-medium transition-colors ".concat("orders"===E?"text-pink-600 border-b-2 border-pink-600 bg-pink-50":"text-gray-600 hover:text-pink-600"),children:[(0,t.jsx)(n.A,{className:"w-5 h-5 inline-block mr-2"}),"Mes Commandes"]})]})}),"profile"===E&&(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-black",children:"Mes informations"}),!k&&(0,t.jsxs)(l.$n,{onClick:()=>A(!0),variant:"outline",className:"flex items-center space-x-2",children:[(0,t.jsx)(d.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Modifier"})]})]}),k?(0,t.jsxs)("div",{className:"space-y-6",children:[(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Pr\xe9nom *"}),(0,t.jsx)(l.pd,{value:R.firstName,onChange:e=>q(s=>({...s,firstName:e.target.value})),placeholder:"Votre pr\xe9nom",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Nom *"}),(0,t.jsx)(l.pd,{value:R.lastName,onChange:e=>q(s=>({...s,lastName:e.target.value})),placeholder:"Votre nom",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Email *"}),(0,t.jsx)(l.pd,{type:"email",value:R.email,onChange:e=>q(s=>({...s,email:e.target.value})),placeholder:"<EMAIL>",required:!0})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"T\xe9l\xe9phone"}),(0,t.jsx)(l.pd,{value:R.phone,onChange:e=>q(s=>({...s,phone:e.target.value})),placeholder:"06 12 34 56 78"})]}),(0,t.jsxs)("div",{className:"md:col-span-2",children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Adresse"}),(0,t.jsx)(l.pd,{value:R.address,onChange:e=>q(s=>({...s,address:e.target.value})),placeholder:"123 Rue de la Paix"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Code postal"}),(0,t.jsx)(l.pd,{value:R.postalCode,onChange:e=>q(s=>({...s,postalCode:e.target.value})),placeholder:"75000"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{className:"block text-sm font-medium text-black mb-2",children:"Ville"}),(0,t.jsx)(l.pd,{value:R.city,onChange:e=>q(s=>({...s,city:e.target.value})),placeholder:"Paris"})]})]}),(0,t.jsxs)("div",{className:"flex space-x-4",children:[(0,t.jsxs)(l.$n,{onClick:W,className:"flex items-center space-x-2 bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600",children:[(0,t.jsx)(o.A,{className:"w-4 h-4"}),(0,t.jsx)("span",{children:"Sauvegarder"})]}),(0,t.jsxs)(l.$n,{onClick:()=>A(!1),variant:"outline",children:[(0,t.jsx)(m.A,{className:"w-4 h-4 mr-2"}),"Annuler"]})]})]}):(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(i.A,{className:"w-5 h-5 text-gray-700"}),(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"Pr\xe9nom"})]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:b.firstName||"Non renseign\xe9"})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(i.A,{className:"w-5 h-5 text-gray-700"}),(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"Nom"})]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:b.lastName||"Non renseign\xe9"})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(c.A,{className:"w-5 h-5 text-gray-700"}),(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"Email"})]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:b.email})]})}),(0,t.jsx)(l.Zp,{children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(x.A,{className:"w-5 h-5 text-gray-700"}),(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"T\xe9l\xe9phone"})]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:b.phone||"Non renseign\xe9"})]})}),(0,t.jsx)(l.Zp,{className:"md:col-span-2",children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3 mb-2",children:[(0,t.jsx)(h.A,{className:"w-5 h-5 text-gray-700"}),(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"Adresse"})]}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:b.address?(0,t.jsxs)(t.Fragment,{children:[b.address,", ",b.postalCode," ",b.city]}):"Non renseign\xe9e"})]})}),b.createdAt&&(0,t.jsx)(l.Zp,{className:"md:col-span-2",children:(0,t.jsxs)(l.Wu,{className:"p-4",children:[(0,t.jsx)("div",{className:"flex items-center space-x-3 mb-2",children:(0,t.jsx)("span",{className:"text-sm font-medium text-black",children:"Membre depuis"})}),(0,t.jsx)("p",{className:"text-lg font-semibold text-black",children:new Date(b.createdAt).toLocaleDateString("fr-FR",{day:"numeric",month:"long",year:"numeric"})})]})})]})]}),"orders"===E&&(0,t.jsxs)("div",{className:"bg-white rounded-2xl shadow-xl p-8",children:[(0,t.jsx)("h2",{className:"text-2xl font-bold text-black mb-6",children:"Mes Commandes"}),P?(0,t.jsxs)("div",{className:"text-center py-8",children:[(0,t.jsx)("div",{className:"w-8 h-8 border-4 border-pink-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"}),(0,t.jsx)("p",{children:"Chargement des commandes..."})]}):0===w.length?(0,t.jsxs)("div",{className:"text-center py-12",children:[(0,t.jsx)(n.A,{className:"w-16 h-16 text-gray-400 mx-auto mb-4"}),(0,t.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Aucune commande"}),(0,t.jsx)("p",{className:"text-gray-600",children:"Vous n'avez pas encore pass\xe9 de commande."})]}):(0,t.jsx)("div",{className:"space-y-6",children:w.map(e=>(0,t.jsx)(l.Zp,{className:"border border-gray-200",children:(0,t.jsxs)(l.Wu,{className:"p-6",children:[(0,t.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,t.jsxs)("div",{children:[(0,t.jsxs)("h3",{className:"font-semibold text-lg text-black",children:["Commande #",e.id.slice(-8)]}),(0,t.jsxs)("div",{className:"flex items-center text-gray-600 mt-1",children:[(0,t.jsx)(u.A,{className:"w-4 h-4 mr-2"}),new Date(e.createdAt).toLocaleDateString("fr-FR",{year:"numeric",month:"long",day:"numeric"})]})]}),(0,t.jsxs)("div",{className:"text-right",children:[(0,t.jsxs)("div",{className:"flex items-center text-gray-600 mb-2",children:[(0,t.jsx)(p,{className:"w-4 h-4 mr-2"}),(Number(e.totalAmount)||0).toFixed(2),"€"]}),(0,t.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("PAID"===e.status?"bg-green-100 text-green-800":"PENDING"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"),children:"PAID"===e.status?"Pay\xe9e":"PENDING"===e.status?"En attente":e.status})]})]}),(0,t.jsxs)("div",{className:"border-t pt-4",children:[(0,t.jsx)("h4",{className:"font-medium text-black mb-3",children:"Articles command\xe9s :"}),(0,t.jsx)("div",{className:"space-y-2",children:e.items.map(e=>(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,t.jsx)("img",{src:e.product.image,alt:e.product.name,className:"w-12 h-12 rounded-lg object-cover"}),(0,t.jsxs)("div",{children:[(0,t.jsx)("p",{className:"font-medium text-black",children:e.product.name}),(0,t.jsxs)("p",{className:"text-sm text-gray-600",children:["Saveur: ",e.variant.flavor," • Quantit\xe9:"," ",e.quantity]})]})]}),(0,t.jsxs)("p",{className:"font-medium text-black",children:[(Number(e.price)*e.quantity).toFixed(2),"€"]})]},e.id))})]})]})},e.id))})]})]})})})}},3411:(e,s,a)=>{"use strict";a.d(s,{A:()=>m});var t=a(5180);let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),r=e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,s,a)=>a?a.toUpperCase():s.toLowerCase()),c=e=>{let s=r(e);return s.charAt(0).toUpperCase()+s.slice(1)},i=function(){for(var e=arguments.length,s=Array(e),a=0;a<e;a++)s[a]=arguments[a];return s.filter((e,s,a)=>!!e&&""!==e.trim()&&a.indexOf(e)===s).join(" ").trim()},n=e=>{for(let s in e)if(s.startsWith("aria-")||"role"===s||"title"===s)return!0};var d={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let o=(0,t.forwardRef)((e,s)=>{let{color:a="currentColor",size:l=24,strokeWidth:r=2,absoluteStrokeWidth:c,className:o="",children:m,iconNode:x,...h}=e;return(0,t.createElement)("svg",{ref:s,...d,width:l,height:l,stroke:a,strokeWidth:c?24*Number(r)/Number(l):r,className:i("lucide",o),...!m&&!n(h)&&{"aria-hidden":"true"},...h},[...x.map(e=>{let[s,a]=e;return(0,t.createElement)(s,a)}),...Array.isArray(m)?m:[m]])}),m=(e,s)=>{let a=(0,t.forwardRef)((a,r)=>{let{className:n,...d}=a;return(0,t.createElement)(o,{ref:r,iconNode:s,className:i("lucide-".concat(l(c(e))),"lucide-".concat(e),n),...d})});return a.displayName=c(e),a}},3527:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4487:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("package",[["path",{d:"M11 21.73a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16V8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73z",key:"1a0edw"}],["path",{d:"M12 22V12",key:"d0xqtd"}],["polyline",{points:"3.29 7 12 12 20.71 7",key:"ousv84"}],["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}]])},4604:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]])},4982:(e,s,a)=>{"use strict";var t=a(1802);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},7231:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("phone",[["path",{d:"M13.832 16.568a1 1 0 0 0 1.213-.303l.355-.465A2 2 0 0 1 17 15h3a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2A18 18 0 0 1 2 4a2 2 0 0 1 2-2h3a2 2 0 0 1 2 2v3a2 2 0 0 1-.8 1.6l-.468.351a1 1 0 0 0-.292 1.233 14 14 0 0 0 6.392 6.384",key:"9njp5v"}]])},9058:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},9270:(e,s,a)=>{"use strict";a.d(s,{A:()=>t});let t=(0,a(3411).A)("mail",[["path",{d:"m22 7-8.991 5.727a2 2 0 0 1-2.009 0L2 7",key:"132q7q"}],["rect",{x:"2",y:"4",width:"20",height:"16",rx:"2",key:"izxlao"}]])},9792:(e,s,a)=>{Promise.resolve().then(a.bind(a,2205))}},e=>{var s=s=>e(e.s=s);e.O(0,[6953,4026,9084,8656,75,7358],()=>s(9792)),_N_E=e.O()}]);