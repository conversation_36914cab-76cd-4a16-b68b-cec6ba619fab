(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5582],{4982:(e,s,a)=>{"use strict";var t=a(1802);a.o(t,"useParams")&&a.d(s,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(s,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(s,{useSearchParams:function(){return t.useSearchParams}})},6766:(e,s,a)=>{Promise.resolve().then(a.bind(a,9674))},9674:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>o});var t=a(5936),i=a(9084),r=a(5156),n=a(6953),l=a(8581),c=a.n(l),d=a(4982),m=a(5180);function o(){(0,d.useRouter)();let e=(0,d.useSearchParams)().get("session_id"),{clearCart:s}=(0,r.useCart)(),[a,l]=(0,m.useState)(null),[o,x]=(0,m.useState)(!0),[u,h]=(0,m.useState)(null);return((0,m.useEffect)(()=>{if(!e){h("Session de paiement non trouv\xe9e"),x(!1);return}(async()=>{try{let a=await fetch("/api/checkout/session/".concat(e)),t=await a.json();t.success?(l(t.data.order),s(),localStorage.removeItem("deltagum_pending_order")):h(t.error||"Erreur lors de la r\xe9cup\xe9ration de la commande")}catch(e){h("Erreur de connexion")}finally{x(!1)}})()},[e]),o)?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)(n.P.div,{className:"text-6xl mb-4",animate:{rotate:360},transition:{duration:2,repeat:1/0,ease:"linear"},children:"\uD83C\uDF6D"}),(0,t.jsx)("p",{className:"text-lg text-gray-600",children:"V\xe9rification de votre commande..."})]})}):u?(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-red-50 to-pink-50 flex items-center justify-center",children:(0,t.jsxs)("div",{className:"text-center max-w-md mx-auto p-8",children:[(0,t.jsx)("div",{className:"text-6xl mb-4",children:"❌"}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Erreur"}),(0,t.jsx)("p",{className:"text-gray-600 mb-6",children:u}),(0,t.jsx)(c(),{href:"/",children:(0,t.jsx)(i.$n,{variant:"primary",children:"Retour \xe0 l'accueil"})})]})}):(0,t.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-green-50 to-blue-50",children:(0,t.jsx)("div",{className:"container mx-auto px-4 py-16",children:(0,t.jsxs)(n.P.div,{className:"max-w-2xl mx-auto text-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.6},children:[(0,t.jsx)(n.P.div,{className:"text-8xl mb-6",initial:{scale:0},animate:{scale:1},transition:{type:"spring",stiffness:260,damping:20,delay:.2},children:"✅"}),(0,t.jsx)(n.P.h1,{className:"text-4xl font-bold text-gray-800 mb-4",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.4},children:"Paiement r\xe9ussi !"}),(0,t.jsx)(n.P.p,{className:"text-xl text-gray-600 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.6},children:"Merci pour votre commande ! Vos d\xe9licieux bonbons Deltagum seront bient\xf4t en route."}),a&&(0,t.jsxs)(n.P.div,{className:"bg-white rounded-lg shadow-lg p-6 mb-8 text-left",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:.8},children:[(0,t.jsxs)("h2",{className:"text-2xl font-bold text-gray-800 mb-4",children:["Commande #",a.id.slice(-8)]}),(0,t.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Client"}),(0,t.jsxs)("p",{className:"text-gray-600",children:[a.customer.firstName," ",a.customer.lastName]}),(0,t.jsx)("p",{className:"text-gray-600",children:a.customer.email})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-700 mb-2",children:"Statut"}),(0,t.jsx)("span",{className:"inline-block bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium",children:"PAID"===a.status?"Pay\xe9":a.status})]})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsx)("h3",{className:"font-semibold text-gray-700 mb-3",children:"Articles command\xe9s"}),(0,t.jsxs)("div",{className:"space-y-2",children:[a.items.map((e,s)=>(0,t.jsxs)("div",{className:"flex justify-between items-center py-2 border-b border-gray-100",children:[(0,t.jsxs)("span",{className:"text-gray-600",children:[e.productName," - ",e.variantFlavor," x",e.quantity]}),(0,t.jsxs)("span",{className:"font-medium",children:[(e.price*e.quantity).toFixed(2),"€"]})]},s)),(0,t.jsxs)("div",{className:"flex justify-between items-center pt-3 font-bold text-lg",children:[(0,t.jsx)("span",{children:"Total"}),(0,t.jsxs)("span",{children:[Number(a.totalAmount).toFixed(2),"€"]})]})]})]})]}),(0,t.jsxs)(n.P.div,{className:"bg-blue-50 rounded-lg p-6 mb-8",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1},children:[(0,t.jsx)("h3",{className:"text-xl font-bold text-blue-800 mb-4",children:"Que se passe-t-il maintenant ?"}),(0,t.jsxs)("div",{className:"space-y-3 text-left",children:[(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCE7"}),(0,t.jsx)("span",{className:"text-blue-700",children:"Vous recevrez un email de confirmation sous peu"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDCE6"}),(0,t.jsx)("span",{className:"text-blue-700",children:"Votre commande sera pr\xe9par\xe9e dans les 24h"})]}),(0,t.jsxs)("div",{className:"flex items-center",children:[(0,t.jsx)("span",{className:"text-2xl mr-3",children:"\uD83D\uDE9A"}),(0,t.jsx)("span",{className:"text-blue-700",children:"Livraison sous 3-5 jours ouvr\xe9s"})]})]})]}),(0,t.jsxs)(n.P.div,{className:"flex flex-col sm:flex-row gap-4 justify-center",initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{delay:1.2},children:[(0,t.jsx)(c(),{href:"/",children:(0,t.jsx)(i.$n,{variant:"primary",size:"lg",children:"Continuer mes achats"})}),(0,t.jsx)(c(),{href:"/profile",children:(0,t.jsx)(i.$n,{variant:"outline",size:"lg",children:"Voir mes commandes"})})]})]})})})}}},e=>{var s=s=>e(e.s=s);e.O(0,[6953,4026,8581,9084,8656,75,7358],()=>s(6766)),_N_E=e.O()}]);