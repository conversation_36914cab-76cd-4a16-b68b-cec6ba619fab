{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/config.ts"], "sourcesContent": ["export const siteConfig = {\n  name: \"Deltagum\",\n  description: \"Chewing-gum premium aux saveurs naturelles de fruits\",\n  url: process.env.NEXTAUTH_URL || \"http://localhost:3000\",\n  ogImage: \"/images/og-image.jpg\",\n  links: {\n    twitter: \"https://twitter.com/deltagum\",\n    instagram: \"https://instagram.com/deltagum\",\n    facebook: \"https://facebook.com/deltagum\",\n  },\n};\n\n// Alias pour compatibilité\nexport const SITE_CONFIG = siteConfig;\n\nexport const productConfig = {\n  flavors: {\n    STRAWBERRY: {\n      name: \"Fraise\",\n      color: \"#FF6B9D\",\n      emoji: \"🍓\",\n      description: \"Délice Deltagum saveur fraise naturelle et relaxante\",\n    },\n    BLUEBERRY: {\n      name: \"<PERSON><PERSON><PERSON>\",\n      color: \"#4A90E2\",\n      emoji: \"🫐\",\n      description: \"Délice Deltagum saveur myrtille fraîche et apaisante\",\n    },\n    APPLE: {\n      name: \"<PERSON><PERSON>\",\n      color: \"#7ED321\",\n      emoji: \"🍏\",\n      description: \"Délice Deltagum saveur pomme verte rafraîchissante\",\n    },\n  },\n  defaultPrice: 12.99,\n  currency: \"EUR\",\n  currencySymbol: \"€\",\n  thcInfo: {\n    concentration: \"Delta-9 THC < 0,3%\",\n    warning: \"Produit contenant du Delta-9 THC - Réservé aux adultes\",\n    legalNotice: \"Ne pas conduire après consommation\",\n  },\n};\n\nexport const loyaltyConfig = {\n  levels: {\n    BRONZE: {\n      name: \"Bronze\",\n      color: \"#CD7F32\",\n      minPoints: 0,\n      benefits: [\"Livraison gratuite dès 25€\"],\n    },\n    SILVER: {\n      name: \"Argent\",\n      color: \"#C0C0C0\",\n      minPoints: 100,\n      benefits: [\"Livraison gratuite dès 20€\", \"5% de réduction\"],\n    },\n    GOLD: {\n      name: \"Or\",\n      color: \"#FFD700\",\n      minPoints: 500,\n      benefits: [\n        \"Livraison gratuite\",\n        \"10% de réduction\",\n        \"Accès prioritaire aux nouveautés\",\n      ],\n    },\n    PLATINUM: {\n      name: \"Platine\",\n      color: \"#E5E4E2\",\n      minPoints: 1000,\n      benefits: [\n        \"Livraison gratuite\",\n        \"15% de réduction\",\n        \"Accès prioritaire\",\n        \"Cadeaux exclusifs\",\n      ],\n    },\n  },\n  pointsPerEuro: 10, // 10 points par euro dépensé\n};\n\nexport const shippingConfig = {\n  freeShippingThreshold: 25, // Livraison gratuite dès 25€\n  standardShipping: 4.9,\n  expressShipping: 9.9,\n  estimatedDelivery: {\n    standard: \"3-5 jours ouvrés\",\n    express: \"24-48h\",\n  },\n};\n\nexport const orderConfig = {\n  statuses: {\n    PENDING: {\n      name: \"En attente\",\n      color: \"#FFA500\",\n      description: \"Commande en attente de paiement\",\n    },\n    PAID: {\n      name: \"Payée\",\n      color: \"#32CD32\",\n      description: \"Paiement confirmé, préparation en cours\",\n    },\n    SHIPPED: {\n      name: \"Expédiée\",\n      color: \"#4169E1\",\n      description: \"Commande expédiée\",\n    },\n    DELIVERED: {\n      name: \"Livrée\",\n      color: \"#228B22\",\n      description: \"Commande livrée\",\n    },\n    CANCELLED: {\n      name: \"Annulée\",\n      color: \"#DC143C\",\n      description: \"Commande annulée\",\n    },\n  },\n};\n"], "names": [], "mappings": ";;;;;;;;AAGO;AAHA,MAAM,aAAa;IACxB,MAAM;IACN,aAAa;IACb,KAAK,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,YAAY,IAAI;IACjC,SAAS;IACT,OAAO;QACL,SAAS;QACT,WAAW;QACX,UAAU;IACZ;AACF;AAGO,MAAM,cAAc;AAEpB,MAAM,gBAAgB;IAC3B,SAAS;QACP,YAAY;YACV,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA,OAAO;YACL,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;IACF;IACA,cAAc;IACd,UAAU;IACV,gBAAgB;IAChB,SAAS;QACP,eAAe;QACf,SAAS;QACT,aAAa;IACf;AACF;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;QACN,QAAQ;YACN,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;gBAAC;aAA6B;QAC1C;QACA,QAAQ;YACN,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;gBAAC;gBAA8B;aAAkB;QAC7D;QACA,MAAM;YACJ,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;gBACR;gBACA;gBACA;aACD;QACH;QACA,UAAU;YACR,MAAM;YACN,OAAO;YACP,WAAW;YACX,UAAU;gBACR;gBACA;gBACA;gBACA;aACD;QACH;IACF;IACA,eAAe;AACjB;AAEO,MAAM,iBAAiB;IAC5B,uBAAuB;IACvB,kBAAkB;IAClB,iBAAiB;IACjB,mBAAmB;QACjB,UAAU;QACV,SAAS;IACX;AACF;AAEO,MAAM,cAAc;IACzB,UAAU;QACR,SAAS;YACP,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA,MAAM;YACJ,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA,SAAS;YACP,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,aAAa;QACf;QACA,WAAW;YACT,MAAM;YACN,OAAO;YACP,aAAa;QACf;IACF;AACF", "debugId": null}}, {"offset": {"line": 148, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/utils.ts"], "sourcesContent": ["import { FlavorType, LoyaltyLevel, OrderStatus } from \"@/types\";\nimport { type ClassValue, clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\nimport { loyaltyConfig, orderConfig, productConfig } from \"./config\";\n\n// Utilitaire pour combiner les classes CSS\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n\n// Formatage des prix\nexport function formatPrice(price: number, currency: string = \"EUR\"): string {\n  return new Intl.NumberFormat(\"fr-FR\", {\n    style: \"currency\",\n    currency,\n  }).format(price);\n}\n\n// Formatage des dates\nexport function formatDate(\n  date: Date | string,\n  options?: Intl.DateTimeFormatOptions\n): string {\n  const dateObj = typeof date === \"string\" ? new Date(date) : date;\n\n  const defaultOptions: Intl.DateTimeFormatOptions = {\n    year: \"numeric\",\n    month: \"long\",\n    day: \"numeric\",\n  };\n\n  return new Intl.DateTimeFormat(\"fr-FR\", {\n    ...defaultOptions,\n    ...options,\n  }).format(dateObj);\n}\n\n// Formatage des dates relatives\nexport function formatRelativeDate(date: Date | string): string {\n  const dateObj = typeof date === \"string\" ? new Date(date) : date;\n  const now = new Date();\n  const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n\n  if (diffInSeconds < 60) return \"À l'instant\";\n  if (diffInSeconds < 3600)\n    return `Il y a ${Math.floor(diffInSeconds / 60)} min`;\n  if (diffInSeconds < 86400)\n    return `Il y a ${Math.floor(diffInSeconds / 3600)} h`;\n  if (diffInSeconds < 2592000)\n    return `Il y a ${Math.floor(diffInSeconds / 86400)} j`;\n\n  return formatDate(dateObj);\n}\n\n// Utilitaires pour les saveurs\nexport function getFlavorInfo(flavor: FlavorType) {\n  return productConfig.flavors[flavor];\n}\n\nexport function getFlavorName(flavor: FlavorType): string {\n  return getFlavorInfo(flavor).name;\n}\n\nexport function getFlavorColor(flavor: FlavorType): string {\n  return getFlavorInfo(flavor).color;\n}\n\nexport function getFlavorEmoji(flavor: FlavorType): string {\n  return getFlavorInfo(flavor).emoji;\n}\n\n// Utilitaires pour le programme de fidélité\nexport function getLoyaltyInfo(level: LoyaltyLevel) {\n  return loyaltyConfig.levels[level];\n}\n\nexport function calculateLoyaltyLevel(points: number): LoyaltyLevel {\n  if (points >= loyaltyConfig.levels.PLATINUM.minPoints) return \"PLATINUM\";\n  if (points >= loyaltyConfig.levels.GOLD.minPoints) return \"GOLD\";\n  if (points >= loyaltyConfig.levels.SILVER.minPoints) return \"SILVER\";\n  return \"BRONZE\";\n}\n\nexport function calculatePointsFromAmount(amount: number): number {\n  return Math.floor(amount * loyaltyConfig.pointsPerEuro);\n}\n\nexport function getNextLoyaltyLevel(\n  currentLevel: LoyaltyLevel\n): LoyaltyLevel | null {\n  const levels: LoyaltyLevel[] = [\"BRONZE\", \"SILVER\", \"GOLD\", \"PLATINUM\"];\n  const currentIndex = levels.indexOf(currentLevel);\n  return currentIndex < levels.length - 1 ? levels[currentIndex + 1] : null;\n}\n\nexport function getPointsToNextLevel(\n  currentPoints: number,\n  currentLevel: LoyaltyLevel\n): number {\n  const nextLevel = getNextLoyaltyLevel(currentLevel);\n  if (!nextLevel) return 0;\n  return loyaltyConfig.levels[nextLevel].minPoints - currentPoints;\n}\n\n// Utilitaires pour les commandes\nexport function getOrderStatusInfo(status: OrderStatus) {\n  return orderConfig.statuses[status];\n}\n\nexport function getOrderStatusColor(status: OrderStatus): string {\n  return getOrderStatusInfo(status).color;\n}\n\nexport function getOrderStatusName(status: OrderStatus): string {\n  return getOrderStatusInfo(status).name;\n}\n\n// Utilitaires pour les calculs de panier\nexport function calculateCartTotal(\n  items: Array<{ price: number; quantity: number }>\n): number {\n  return items.reduce((total, item) => total + item.price * item.quantity, 0);\n}\n\nexport function calculateCartItemsCount(\n  items: Array<{ quantity: number }>\n): number {\n  return items.reduce((total, item) => total + item.quantity, 0);\n}\n\n// Utilitaires pour les URLs et slugs\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .normalize(\"NFD\")\n    .replace(/[\\u0300-\\u036f]/g, \"\") // Supprimer les accents\n    .replace(/[^a-z0-9 -]/g, \"\") // Supprimer les caractères spéciaux\n    .replace(/\\s+/g, \"-\") // Remplacer les espaces par des tirets\n    .replace(/-+/g, \"-\") // Supprimer les tirets multiples\n    .trim();\n}\n\nexport function generateSKU(productName: string, flavor: FlavorType): string {\n  const productSlug = slugify(productName).toUpperCase().substring(0, 3);\n  const flavorCode = flavor.substring(0, 3);\n  const timestamp = Date.now().toString().slice(-3);\n  return `${productSlug}-${flavorCode}-${timestamp}`;\n}\n\n// Utilitaires pour la validation\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^(?:\\+33|0)[1-9](?:[0-9]{8})$/;\n  return phoneRegex.test(phone.replace(/\\s/g, \"\"));\n}\n\nexport function isValidPostalCode(postalCode: string): boolean {\n  const postalCodeRegex = /^\\d{5}$/;\n  return postalCodeRegex.test(postalCode);\n}\n\n// Utilitaires pour les images\nexport function getImageUrl(path: string, size?: \"sm\" | \"md\" | \"lg\"): string {\n  if (path.startsWith(\"http\")) return path;\n\n  const baseUrl = process.env.NEXT_PUBLIC_IMAGES_URL || \"/images\";\n  const sizePrefix = size ? `${size}/` : \"\";\n\n  return `${baseUrl}/${sizePrefix}${path}`;\n}\n\nexport function getPlaceholderImage(\n  width: number = 400,\n  height: number = 400\n): string {\n  return `https://via.placeholder.com/${width}x${height}/FF6B9D/FFFFFF?text=Deltagum`;\n}\n\n// Utilitaires pour le localStorage\nexport function getFromLocalStorage<T>(key: string, defaultValue: T): T {\n  if (typeof window === \"undefined\") return defaultValue;\n\n  try {\n    const item = window.localStorage.getItem(key);\n    return item ? JSON.parse(item) : defaultValue;\n  } catch (error) {\n    console.error(`Error reading localStorage key \"${key}\":`, error);\n    return defaultValue;\n  }\n}\n\nexport function setToLocalStorage<T>(key: string, value: T): void {\n  if (typeof window === \"undefined\") return;\n\n  try {\n    window.localStorage.setItem(key, JSON.stringify(value));\n  } catch (error) {\n    console.error(`Error setting localStorage key \"${key}\":`, error);\n  }\n}\n\nexport function removeFromLocalStorage(key: string): void {\n  if (typeof window === \"undefined\") return;\n\n  try {\n    window.localStorage.removeItem(key);\n  } catch (error) {\n    console.error(`Error removing localStorage key \"${key}\":`, error);\n  }\n}\n\n// Utilitaires pour les animations\nexport function getRandomDelay(min: number = 0, max: number = 1): number {\n  return Math.random() * (max - min) + min;\n}\n\nexport function easeInOutCubic(t: number): number {\n  return t < 0.5 ? 4 * t * t * t : (t - 1) * (2 * t - 2) * (2 * t - 2) + 1;\n}\n\n// Utilitaires pour les erreurs\nexport function getErrorMessage(error: unknown): string {\n  if (error instanceof Error) return error.message;\n  if (typeof error === \"string\") return error;\n  return \"Une erreur inattendue s'est produite\";\n}\n\n// Utilitaires pour les API\nexport function createApiUrl(\n  endpoint: string,\n  params?: Record<string, string | number>\n): string {\n  const baseUrl = process.env.NEXT_PUBLIC_API_URL || \"/api\";\n  const url = new URL(`${baseUrl}${endpoint}`, window.location.origin);\n\n  if (params) {\n    Object.entries(params).forEach(([key, value]) => {\n      url.searchParams.append(key, value.toString());\n    });\n  }\n\n  return url.toString();\n}\n\n// Utilitaire pour débouncer les fonctions\nexport function debounce<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout | null = null;\n\n  return (...args: Parameters<T>) => {\n    if (timeout) clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// Utilitaire pour throttler les fonctions\nexport function throttle<T extends (...args: unknown[]) => unknown>(\n  func: T,\n  limit: number\n): (...args: Parameters<T>) => void {\n  let inThrottle: boolean = false;\n\n  return (...args: Parameters<T>) => {\n    if (!inThrottle) {\n      func(...args);\n      inThrottle = true;\n      setTimeout(() => (inThrottle = false), limit);\n    }\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyKkB;AAxKlB;AACA;AACA;;;;AAGO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,4NAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,yLAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAGO,SAAS,YAAY,KAAa,EAAE,WAAmB,KAAK;IACjE,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP;IACF,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,WACd,IAAmB,EACnB,OAAoC;IAEpC,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAE5D,MAAM,iBAA6C;QACjD,MAAM;QACN,OAAO;QACP,KAAK;IACP;IAEA,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,GAAG,cAAc;QACjB,GAAG,OAAO;IACZ,GAAG,MAAM,CAAC;AACZ;AAGO,SAAS,mBAAmB,IAAmB;IACpD,MAAM,UAAU,OAAO,SAAS,WAAW,IAAI,KAAK,QAAQ;IAC5D,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,IAAI,OAAO,KAAK,QAAQ,OAAO,EAAE,IAAI;IAEvE,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAClB,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,gBAAgB,IAAI,IAAI,CAAC;IACvD,IAAI,gBAAgB,OAClB,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,gBAAgB,MAAM,EAAE,CAAC;IACvD,IAAI,gBAAgB,SAClB,OAAO,CAAC,OAAO,EAAE,KAAK,KAAK,CAAC,gBAAgB,OAAO,EAAE,CAAC;IAExD,OAAO,WAAW;AACpB;AAGO,SAAS,cAAc,MAAkB;IAC9C,OAAO,uHAAA,CAAA,gBAAa,CAAC,OAAO,CAAC,OAAO;AACtC;AAEO,SAAS,cAAc,MAAkB;IAC9C,OAAO,cAAc,QAAQ,IAAI;AACnC;AAEO,SAAS,eAAe,MAAkB;IAC/C,OAAO,cAAc,QAAQ,KAAK;AACpC;AAEO,SAAS,eAAe,MAAkB;IAC/C,OAAO,cAAc,QAAQ,KAAK;AACpC;AAGO,SAAS,eAAe,KAAmB;IAChD,OAAO,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,MAAM;AACpC;AAEO,SAAS,sBAAsB,MAAc;IAClD,IAAI,UAAU,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,QAAQ,CAAC,SAAS,EAAE,OAAO;IAC9D,IAAI,UAAU,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,IAAI,CAAC,SAAS,EAAE,OAAO;IAC1D,IAAI,UAAU,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,MAAM,CAAC,SAAS,EAAE,OAAO;IAC5D,OAAO;AACT;AAEO,SAAS,0BAA0B,MAAc;IACtD,OAAO,KAAK,KAAK,CAAC,SAAS,uHAAA,CAAA,gBAAa,CAAC,aAAa;AACxD;AAEO,SAAS,oBACd,YAA0B;IAE1B,MAAM,SAAyB;QAAC;QAAU;QAAU;QAAQ;KAAW;IACvE,MAAM,eAAe,OAAO,OAAO,CAAC;IACpC,OAAO,eAAe,OAAO,MAAM,GAAG,IAAI,MAAM,CAAC,eAAe,EAAE,GAAG;AACvE;AAEO,SAAS,qBACd,aAAqB,EACrB,YAA0B;IAE1B,MAAM,YAAY,oBAAoB;IACtC,IAAI,CAAC,WAAW,OAAO;IACvB,OAAO,uHAAA,CAAA,gBAAa,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS,GAAG;AACrD;AAGO,SAAS,mBAAmB,MAAmB;IACpD,OAAO,uHAAA,CAAA,cAAW,CAAC,QAAQ,CAAC,OAAO;AACrC;AAEO,SAAS,oBAAoB,MAAmB;IACrD,OAAO,mBAAmB,QAAQ,KAAK;AACzC;AAEO,SAAS,mBAAmB,MAAmB;IACpD,OAAO,mBAAmB,QAAQ,IAAI;AACxC;AAGO,SAAS,mBACd,KAAiD;IAEjD,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,KAAK,GAAG,KAAK,QAAQ,EAAE;AAC3E;AAEO,SAAS,wBACd,KAAkC;IAElC,OAAO,MAAM,MAAM,CAAC,CAAC,OAAO,OAAS,QAAQ,KAAK,QAAQ,EAAE;AAC9D;AAGO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,SAAS,CAAC,OACV,OAAO,CAAC,oBAAoB,IAAI,wBAAwB;KACxD,OAAO,CAAC,gBAAgB,IAAI,oCAAoC;KAChE,OAAO,CAAC,QAAQ,KAAK,uCAAuC;KAC5D,OAAO,CAAC,OAAO,KAAK,iCAAiC;KACrD,IAAI;AACT;AAEO,SAAS,YAAY,WAAmB,EAAE,MAAkB;IACjE,MAAM,cAAc,QAAQ,aAAa,WAAW,GAAG,SAAS,CAAC,GAAG;IACpE,MAAM,aAAa,OAAO,SAAS,CAAC,GAAG;IACvC,MAAM,YAAY,KAAK,GAAG,GAAG,QAAQ,GAAG,KAAK,CAAC,CAAC;IAC/C,OAAO,GAAG,YAAY,CAAC,EAAE,WAAW,CAAC,EAAE,WAAW;AACpD;AAGO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C;AAEO,SAAS,kBAAkB,UAAkB;IAClD,MAAM,kBAAkB;IACxB,OAAO,gBAAgB,IAAI,CAAC;AAC9B;AAGO,SAAS,YAAY,IAAY,EAAE,IAAyB;IACjE,IAAI,KAAK,UAAU,CAAC,SAAS,OAAO;IAEpC,MAAM,UAAU,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,sBAAsB,IAAI;IACtD,MAAM,aAAa,OAAO,GAAG,KAAK,CAAC,CAAC,GAAG;IAEvC,OAAO,GAAG,QAAQ,CAAC,EAAE,aAAa,MAAM;AAC1C;AAEO,SAAS,oBACd,QAAgB,GAAG,EACnB,SAAiB,GAAG;IAEpB,OAAO,CAAC,4BAA4B,EAAE,MAAM,CAAC,EAAE,OAAO,4BAA4B,CAAC;AACrF;AAGO,SAAS,oBAAuB,GAAW,EAAE,YAAe;IACjE,uCAAmC;;IAAmB;IAEtD,IAAI;QACF,MAAM,OAAO,OAAO,YAAY,CAAC,OAAO,CAAC;QACzC,OAAO,OAAO,KAAK,KAAK,CAAC,QAAQ;IACnC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC1D,OAAO;IACT;AACF;AAEO,SAAS,kBAAqB,GAAW,EAAE,KAAQ;IACxD,uCAAmC;;IAAM;IAEzC,IAAI;QACF,OAAO,YAAY,CAAC,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;IAClD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;IAC5D;AACF;AAEO,SAAS,uBAAuB,GAAW;IAChD,uCAAmC;;IAAM;IAEzC,IAAI;QACF,OAAO,YAAY,CAAC,UAAU,CAAC;IACjC,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;IAC7D;AACF;AAGO,SAAS,eAAe,MAAc,CAAC,EAAE,MAAc,CAAC;IAC7D,OAAO,KAAK,MAAM,KAAK,CAAC,MAAM,GAAG,IAAI;AACvC;AAEO,SAAS,eAAe,CAAS;IACtC,OAAO,IAAI,MAAM,IAAI,IAAI,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,IAAI,CAAC,IAAI;AACzE;AAGO,SAAS,gBAAgB,KAAc;IAC5C,IAAI,iBAAiB,OAAO,OAAO,MAAM,OAAO;IAChD,IAAI,OAAO,UAAU,UAAU,OAAO;IACtC,OAAO;AACT;AAGO,SAAS,aACd,QAAgB,EAChB,MAAwC;IAExC,MAAM,UAAU,+QAAA,CAAA,UAAO,CAAC,GAAG,CAAC,mBAAmB,IAAI;IACnD,MAAM,MAAM,IAAI,IAAI,GAAG,UAAU,UAAU,EAAE,OAAO,QAAQ,CAAC,MAAM;IAEnE,IAAI,QAAQ;QACV,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;YAC1C,IAAI,YAAY,CAAC,MAAM,CAAC,KAAK,MAAM,QAAQ;QAC7C;IACF;IAEA,OAAO,IAAI,QAAQ;AACrB;AAGO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI,UAAiC;IAErC,OAAO,CAAC,GAAG;QACT,IAAI,SAAS,aAAa;QAC1B,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAGO,SAAS,SACd,IAAO,EACP,KAAa;IAEb,IAAI,aAAsB;IAE1B,OAAO,CAAC,GAAG;QACT,IAAI,CAAC,YAAY;YACf,QAAQ;YACR,aAAa;YACb,WAAW,IAAO,aAAa,OAAQ;QACzC;IACF;AACF", "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Button.tsx"], "sourcesContent": ["\"use client\";\n\n// Animations removed - using inline animations\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement> {\n  variant?: \"primary\" | \"secondary\" | \"outline\" | \"ghost\" | \"danger\";\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  loading?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: \"left\" | \"right\";\n  fullWidth?: boolean;\n  rounded?: boolean;\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  (\n    {\n      className,\n      variant = \"primary\",\n      size = \"md\",\n      loading = false,\n      icon,\n      iconPosition = \"left\",\n      fullWidth = false,\n      rounded = false,\n      children,\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = [\n      \"inline-flex items-center justify-center font-medium transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-offset-2\",\n      \"disabled:opacity-50 disabled:cursor-not-allowed\",\n      \"relative overflow-hidden\",\n    ];\n\n    const variantClasses = {\n      primary: [\n        \"bg-gradient-to-r from-pink-500 to-orange-400\",\n        \"hover:from-pink-600 hover:to-orange-500\",\n        \"text-white shadow-lg hover:shadow-xl\",\n        \"focus:ring-pink-500\",\n      ],\n      secondary: [\n        \"bg-gradient-to-r from-purple-500 to-blue-500\",\n        \"hover:from-purple-600 hover:to-blue-600\",\n        \"text-white shadow-lg hover:shadow-xl\",\n        \"focus:ring-purple-500\",\n      ],\n      outline: [\n        \"border-2 border-pink-500 text-pink-500\",\n        \"hover:bg-pink-500 hover:text-white\",\n        \"focus:ring-pink-500\",\n      ],\n      ghost: [\n        \"text-gray-700 hover:text-pink-500\",\n        \"hover:bg-pink-50\",\n        \"focus:ring-pink-500\",\n      ],\n      danger: [\n        \"bg-red-500 hover:bg-red-600\",\n        \"text-white shadow-lg hover:shadow-xl\",\n        \"focus:ring-red-500\",\n      ],\n    };\n\n    const sizeClasses = {\n      sm: \"px-3 py-1.5 text-sm\",\n      md: \"px-4 py-2 text-base\",\n      lg: \"px-6 py-3 text-lg\",\n      xl: \"px-8 py-4 text-xl\",\n    };\n\n    const roundedClasses = {\n      sm: rounded ? \"rounded-full\" : \"rounded-md\",\n      md: rounded ? \"rounded-full\" : \"rounded-lg\",\n      lg: rounded ? \"rounded-full\" : \"rounded-xl\",\n      xl: rounded ? \"rounded-full\" : \"rounded-2xl\",\n    };\n\n    const classes = cn(\n      baseClasses,\n      variantClasses[variant],\n      sizeClasses[size],\n      roundedClasses[size],\n      fullWidth && \"w-full\",\n      className\n    );\n\n    const iconSize = {\n      sm: \"w-4 h-4\",\n      md: \"w-5 h-5\",\n      lg: \"w-6 h-6\",\n      xl: \"w-7 h-7\",\n    };\n\n    const LoadingSpinner = () => (\n      <motion.div\n        className={cn(\n          \"border-2 border-current border-t-transparent rounded-full\",\n          iconSize[size]\n        )}\n        animate={{ rotate: 360 }}\n        transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n      />\n    );\n\n    const renderContent = () => {\n      if (loading) {\n        return (\n          <>\n            <LoadingSpinner />\n            {children && <span className=\"ml-2\">Chargement...</span>}\n          </>\n        );\n      }\n\n      if (icon && iconPosition === \"left\") {\n        return (\n          <>\n            <span className={cn(iconSize[size], children && \"mr-2\")}>\n              {icon}\n            </span>\n            {children}\n          </>\n        );\n      }\n\n      if (icon && iconPosition === \"right\") {\n        return (\n          <>\n            {children}\n            <span className={cn(iconSize[size], children && \"ml-2\")}>\n              {icon}\n            </span>\n          </>\n        );\n      }\n\n      return children;\n    };\n\n    const {\n      onDrag,\n      onDragStart,\n      onDragEnd,\n      onAnimationStart,\n      onAnimationEnd,\n      onAnimationIteration,\n      ...buttonProps\n    } = props;\n\n    return (\n      <motion.button\n        ref={ref}\n        className={classes}\n        disabled={disabled || loading}\n        whileHover={!disabled && !loading ? { scale: 1.02 } : undefined}\n        whileTap={!disabled && !loading ? { scale: 0.98 } : undefined}\n        {...buttonProps}\n      >\n        {renderContent()}\n      </motion.button>\n    );\n  }\n);\n\nButton.displayName = \"Button\";\n\nexport { Button };\n"], "names": [], "mappings": ";;;;AAEA,+CAA+C;AAC/C;AACA;AACA;AALA;;;;;AAkBA,MAAM,uBAAS,4QAAA,CAAA,UAAK,CAAC,UAAU,MAC7B,CACE,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,YAAY,KAAK,EACjB,UAAU,KAAK,EACf,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;QAClB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YACP;YACA;YACA;YACA;SACD;QACD,WAAW;YACT;YACA;YACA;YACA;SACD;QACD,SAAS;YACP;YACA;YACA;SACD;QACD,OAAO;YACL;YACA;YACA;SACD;QACD,QAAQ;YACN;YACA;YACA;SACD;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,iBAAiB;QAC/B,IAAI,UAAU,iBAAiB;QAC/B,IAAI,UAAU,iBAAiB;QAC/B,IAAI,UAAU,iBAAiB;IACjC;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,cAAc,CAAC,KAAK,EACpB,aAAa,UACb;IAGF,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB,kBACrB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,QAAQ,CAAC,KAAK;YAEhB,SAAS;gBAAE,QAAQ;YAAI;YACvB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAS;;;;;;IAIhE,MAAM,gBAAgB;QACpB,IAAI,SAAS;YACX,qBACE;;kCACE,4SAAC;;;;;oBACA,0BAAY,4SAAC;wBAAK,WAAU;kCAAO;;;;;;;;QAG1C;QAEA,IAAI,QAAQ,iBAAiB,QAAQ;YACnC,qBACE;;kCACE,4SAAC;wBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY;kCAC7C;;;;;;oBAEF;;;QAGP;QAEA,IAAI,QAAQ,iBAAiB,SAAS;YACpC,qBACE;;oBACG;kCACD,4SAAC;wBAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ,CAAC,KAAK,EAAE,YAAY;kCAC7C;;;;;;;;QAIT;QAEA,OAAO;IACT;IAEA,MAAM,EACJ,MAAM,EACN,WAAW,EACX,SAAS,EACT,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,GAAG,aACJ,GAAG;IAEJ,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;QACZ,KAAK;QACL,WAAW;QACX,UAAU,YAAY;QACtB,YAAY,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAK,IAAI;QACtD,UAAU,CAAC,YAAY,CAAC,UAAU;YAAE,OAAO;QAAK,IAAI;QACnD,GAAG,WAAW;kBAEd;;;;;;AAGP;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 558, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/lib/animations.ts"], "sourcesContent": ["// Configurations d'animations pour Framer Motion\n\nexport const fadeIn = {\n  initial: { opacity: 0 },\n  animate: { opacity: 1 },\n  exit: { opacity: 0 },\n  transition: { duration: 0.3 },\n};\n\nexport const slideUp = {\n  initial: { opacity: 0, y: 20 },\n  animate: { opacity: 1, y: 0 },\n  exit: { opacity: 0, y: -20 },\n  transition: { duration: 0.4 },\n};\n\nexport const slideDown = {\n  initial: { opacity: 0, y: -20 },\n  animate: { opacity: 1, y: 0 },\n  exit: { opacity: 0, y: 20 },\n  transition: { duration: 0.4 },\n};\n\nexport const slideLeft = {\n  initial: { opacity: 0, x: 20 },\n  animate: { opacity: 1, x: 0 },\n  exit: { opacity: 0, x: -20 },\n  transition: { duration: 0.4 },\n};\n\nexport const slideRight = {\n  initial: { opacity: 0, x: -20 },\n  animate: { opacity: 1, x: 0 },\n  exit: { opacity: 0, x: 20 },\n  transition: { duration: 0.4 },\n};\n\nexport const scaleIn = {\n  initial: { opacity: 0, scale: 0.8 },\n  animate: { opacity: 1, scale: 1 },\n  exit: { opacity: 0, scale: 0.8 },\n  transition: { duration: 0.3 },\n};\n\nexport const scaleOut = {\n  initial: { opacity: 1, scale: 1 },\n  animate: { opacity: 0, scale: 0.8 },\n  transition: { duration: 0.3 },\n};\n\nexport const bounce = {\n  initial: { opacity: 0, scale: 0.3 },\n  animate: {\n    opacity: 1,\n    scale: 1,\n    transition: {\n      type: \"spring\",\n      stiffness: 260,\n      damping: 20,\n    },\n  },\n  exit: { opacity: 0, scale: 0.3 },\n};\n\nexport const staggerContainer = {\n  animate: {\n    transition: {\n      staggerChildren: 0.1,\n    },\n  },\n};\n\nexport const staggerItem = {\n  initial: { opacity: 0, y: 20 },\n  animate: {\n    opacity: 1,\n    y: 0,\n    transition: { duration: 0.4 },\n  },\n};\n\n// Animations spécifiques pour les saveurs\nexport const flavorHover = {\n  scale: 1.05,\n  transition: { duration: 0.2 },\n};\n\nexport const flavorTap = {\n  scale: 0.95,\n  transition: { duration: 0.1 },\n};\n\nexport const flavorSelect = {\n  scale: [1, 1.1, 1],\n  transition: { duration: 0.3 },\n};\n\n// Animations pour le panier\nexport const cartItemAdd = {\n  initial: { opacity: 0, x: -20, scale: 0.8 },\n  animate: {\n    opacity: 1,\n    x: 0,\n    scale: 1,\n    transition: { duration: 0.3 },\n  },\n  exit: {\n    opacity: 0,\n    x: 20,\n    scale: 0.8,\n    transition: { duration: 0.2 },\n  },\n};\n\nexport const cartBadgePulse = {\n  scale: [1, 1.2, 1],\n  transition: { duration: 0.3 },\n};\n\n// Animations pour les modales\nexport const modalOverlay = {\n  initial: { opacity: 0 },\n  animate: { opacity: 1 },\n  exit: { opacity: 0 },\n  transition: { duration: 0.2 },\n};\n\nexport const modalContent = {\n  initial: { opacity: 0, scale: 0.9, y: 20 },\n  animate: {\n    opacity: 1,\n    scale: 1,\n    y: 0,\n    transition: { duration: 0.3 },\n  },\n  exit: {\n    opacity: 0,\n    scale: 0.9,\n    y: 20,\n    transition: { duration: 0.2 },\n  },\n};\n\n// Animations pour les notifications\nexport const notificationSlide = {\n  initial: { opacity: 0, x: 300 },\n  animate: {\n    opacity: 1,\n    x: 0,\n    transition: { duration: 0.3 },\n  },\n  exit: {\n    opacity: 0,\n    x: 300,\n    transition: { duration: 0.2 },\n  },\n};\n\n// Animations pour les boutons\nexport const buttonHover = {\n  scale: 1.02,\n  transition: { duration: 0.2 },\n};\n\nexport const buttonTap = {\n  scale: 0.98,\n  transition: { duration: 0.1 },\n};\n\nexport const buttonLoading = {\n  rotate: 360,\n  transition: {\n    duration: 1,\n    repeat: Infinity,\n    ease: \"linear\",\n  },\n};\n\n// Animations pour les éléments flottants\nexport const floatingCandy = {\n  initial: { y: 0, x: 0, rotate: 0, scale: 0.8, opacity: 0.7 },\n  animate: {\n    y: [-20, 20, -20],\n    x: [-10, 10, -10],\n    rotate: [-5, 5, -5],\n    scale: [0.8, 1.1, 0.9, 1],\n    opacity: [0.7, 1, 0.8, 0.9],\n  },\n  transition: {\n    duration: 4,\n    repeat: Infinity,\n    ease: \"easeInOut\",\n  },\n};\n\nexport const parallaxSlow = {\n  y: [0, -50],\n  transition: {\n    duration: 2,\n    repeat: Infinity,\n    repeatType: \"reverse\" as const,\n    ease: \"easeInOut\",\n  },\n};\n\nexport const parallaxFast = {\n  y: [0, -100],\n  transition: {\n    duration: 1.5,\n    repeat: Infinity,\n    repeatType: \"reverse\" as const,\n    ease: \"easeInOut\",\n  },\n};\n\n// Animations pour les confettis\nexport const confettiPiece = {\n  y: [0, 300],\n  x: [-50, 50],\n  rotate: [0, 360],\n  transition: {\n    duration: 2,\n    ease: \"easeOut\",\n  },\n};\n\n// Animations spécifiques au Hero\nexport const heroTitle = {\n  initial: { opacity: 0, y: 50, scale: 0.9 },\n  animate: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: {\n      duration: 0.8,\n      ease: [0.25, 0.46, 0.45, 0.94],\n    },\n  },\n};\n\nexport const heroSubtitle = {\n  initial: { opacity: 0, y: 30 },\n  animate: {\n    opacity: 1,\n    y: 0,\n    transition: {\n      duration: 0.6,\n      delay: 0.3,\n      ease: \"easeOut\",\n    },\n  },\n};\n\nexport const heroButton = {\n  initial: { opacity: 0, y: 20, scale: 0.9 },\n  animate: {\n    opacity: 1,\n    y: 0,\n    scale: 1,\n    transition: {\n      duration: 0.5,\n      delay: 0.6,\n      ease: \"easeOut\",\n    },\n  },\n};\n\nexport const slideInLeft = {\n  initial: { opacity: 0, x: -50 },\n  animate: {\n    opacity: 1,\n    x: 0,\n    transition: { duration: 0.6, ease: \"easeOut\" },\n  },\n};\n\nexport const slideInRight = {\n  initial: { opacity: 0, x: 50 },\n  animate: {\n    opacity: 1,\n    x: 0,\n    transition: { duration: 0.6, ease: \"easeOut\" },\n  },\n};\n\n// Easings personnalisés\nexport const easings = {\n  easeOutCubic: [0.33, 1, 0.68, 1],\n  easeInOutCubic: [0.65, 0, 0.35, 1],\n  easeOutBack: [0.34, 1.56, 0.64, 1],\n  easeInOutBack: [0.68, -0.6, 0.32, 1.6],\n};\n"], "names": [], "mappings": "AAAA,iDAAiD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE1C,MAAM,SAAS;IACpB,SAAS;QAAE,SAAS;IAAE;IACtB,SAAS;QAAE,SAAS;IAAE;IACtB,MAAM;QAAE,SAAS;IAAE;IACnB,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC3B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,YAAY;IACvB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAG;IAC1B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,YAAY;IACvB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC3B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAE;IAC5B,MAAM;QAAE,SAAS;QAAG,GAAG;IAAG;IAC1B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,UAAU;IACrB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,MAAM;QAAE,SAAS;QAAG,OAAO;IAAI;IAC/B,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,WAAW;IACtB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAE;IAChC,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,SAAS;IACpB,SAAS;QAAE,SAAS;QAAG,OAAO;IAAI;IAClC,SAAS;QACP,SAAS;QACT,OAAO;QACP,YAAY;YACV,MAAM;YACN,WAAW;YACX,SAAS;QACX;IACF;IACA,MAAM;QAAE,SAAS;QAAG,OAAO;IAAI;AACjC;AAEO,MAAM,mBAAmB;IAC9B,SAAS;QACP,YAAY;YACV,iBAAiB;QACnB;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAGO,MAAM,cAAc;IACzB,OAAO;IACP,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,YAAY;IACvB,OAAO;IACP,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,eAAe;IAC1B,OAAO;QAAC;QAAG;QAAK;KAAE;IAClB,YAAY;QAAE,UAAU;IAAI;AAC9B;AAGO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;QAAI,OAAO;IAAI;IAC1C,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,MAAM;QACJ,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAEO,MAAM,iBAAiB;IAC5B,OAAO;QAAC;QAAG;QAAK;KAAE;IAClB,YAAY;QAAE,UAAU;IAAI;AAC9B;AAGO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;IAAE;IACtB,SAAS;QAAE,SAAS;IAAE;IACtB,MAAM;QAAE,SAAS;IAAE;IACnB,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,OAAO;QAAK,GAAG;IAAG;IACzC,SAAS;QACP,SAAS;QACT,OAAO;QACP,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,MAAM;QACJ,SAAS;QACT,OAAO;QACP,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAGO,MAAM,oBAAoB;IAC/B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAI;IAC9B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;IACA,MAAM;QACJ,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;QAAI;IAC9B;AACF;AAGO,MAAM,cAAc;IACzB,OAAO;IACP,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,YAAY;IACvB,OAAO;IACP,YAAY;QAAE,UAAU;IAAI;AAC9B;AAEO,MAAM,gBAAgB;IAC3B,QAAQ;IACR,YAAY;QACV,UAAU;QACV,QAAQ;QACR,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,SAAS;QAAE,GAAG;QAAG,GAAG;QAAG,QAAQ;QAAG,OAAO;QAAK,SAAS;IAAI;IAC3D,SAAS;QACP,GAAG;YAAC,CAAC;YAAI;YAAI,CAAC;SAAG;QACjB,GAAG;YAAC,CAAC;YAAI;YAAI,CAAC;SAAG;QACjB,QAAQ;YAAC,CAAC;YAAG;YAAG,CAAC;SAAE;QACnB,OAAO;YAAC;YAAK;YAAK;YAAK;SAAE;QACzB,SAAS;YAAC;YAAK;YAAG;YAAK;SAAI;IAC7B;IACA,YAAY;QACV,UAAU;QACV,QAAQ;QACR,MAAM;IACR;AACF;AAEO,MAAM,eAAe;IAC1B,GAAG;QAAC;QAAG,CAAC;KAAG;IACX,YAAY;QACV,UAAU;QACV,QAAQ;QACR,YAAY;QACZ,MAAM;IACR;AACF;AAEO,MAAM,eAAe;IAC1B,GAAG;QAAC;QAAG,CAAC;KAAI;IACZ,YAAY;QACV,UAAU;QACV,QAAQ;QACR,YAAY;QACZ,MAAM;IACR;AACF;AAGO,MAAM,gBAAgB;IAC3B,GAAG;QAAC;QAAG;KAAI;IACX,GAAG;QAAC,CAAC;QAAI;KAAG;IACZ,QAAQ;QAAC;QAAG;KAAI;IAChB,YAAY;QACV,UAAU;QACV,MAAM;IACR;AACF;AAGO,MAAM,YAAY;IACvB,SAAS;QAAE,SAAS;QAAG,GAAG;QAAI,OAAO;IAAI;IACzC,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,UAAU;YACV,MAAM;gBAAC;gBAAM;gBAAM;gBAAM;aAAK;QAChC;IACF;AACF;AAEO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;IACF;AACF;AAEO,MAAM,aAAa;IACxB,SAAS;QAAE,SAAS;QAAG,GAAG;QAAI,OAAO;IAAI;IACzC,SAAS;QACP,SAAS;QACT,GAAG;QACH,OAAO;QACP,YAAY;YACV,UAAU;YACV,OAAO;YACP,MAAM;QACR;IACF;AACF;AAEO,MAAM,cAAc;IACzB,SAAS;QAAE,SAAS;QAAG,GAAG,CAAC;IAAG;IAC9B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;AACF;AAEO,MAAM,eAAe;IAC1B,SAAS;QAAE,SAAS;QAAG,GAAG;IAAG;IAC7B,SAAS;QACP,SAAS;QACT,GAAG;QACH,YAAY;YAAE,UAAU;YAAK,MAAM;QAAU;IAC/C;AACF;AAGO,MAAM,UAAU;IACrB,cAAc;QAAC;QAAM;QAAG;QAAM;KAAE;IAChC,gBAAgB;QAAC;QAAM;QAAG;QAAM;KAAE;IAClC,aAAa;QAAC;QAAM;QAAM;QAAM;KAAE;IAClC,eAAe;QAAC;QAAM,CAAC;QAAK;QAAM;KAAI;AACxC", "debugId": null}}, {"offset": {"line": 1076, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Card.tsx"], "sourcesContent": ["\"use client\";\n\nimport { scaleIn } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nexport interface CardProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?: \"default\" | \"elevated\" | \"outlined\" | \"glass\";\n  padding?: \"none\" | \"sm\" | \"md\" | \"lg\" | \"xl\";\n  rounded?: \"none\" | \"sm\" | \"md\" | \"lg\" | \"xl\" | \"full\";\n  hover?: boolean;\n  clickable?: boolean;\n  gradient?: boolean;\n}\n\nconst Card = React.forwardRef<HTMLDivElement, CardProps>(\n  (\n    {\n      className,\n      variant = \"default\",\n      padding = \"md\",\n      rounded = \"lg\",\n      hover = false,\n      clickable = false,\n      gradient = false,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = [\n      \"relative overflow-hidden transition-all duration-300\",\n    ];\n\n    const variantClasses = {\n      default: [\"bg-white border border-gray-200\", \"shadow-sm\"],\n      elevated: [\"bg-white\", \"shadow-lg hover:shadow-xl\"],\n      outlined: [\"bg-white border-2 border-gray-300\", \"hover:border-pink-300\"],\n      glass: [\n        \"bg-white/80 backdrop-blur-sm\",\n        \"border border-white/20\",\n        \"shadow-lg\",\n      ],\n    };\n\n    const paddingClasses = {\n      none: \"\",\n      sm: \"p-3\",\n      md: \"p-4\",\n      lg: \"p-6\",\n      xl: \"p-8\",\n    };\n\n    const roundedClasses = {\n      none: \"\",\n      sm: \"rounded-sm\",\n      md: \"rounded-md\",\n      lg: \"rounded-lg\",\n      xl: \"rounded-xl\",\n      full: \"rounded-full\",\n    };\n\n    const hoverClasses = hover\n      ? [\"hover:shadow-lg hover:-translate-y-1\", \"hover:scale-105\"]\n      : [];\n\n    const clickableClasses = clickable\n      ? [\n          \"cursor-pointer\",\n          \"hover:shadow-lg hover:-translate-y-1\",\n          \"active:scale-95\",\n        ]\n      : [];\n\n    const gradientClasses = gradient\n      ? [\n          \"bg-gradient-to-br from-pink-50 to-orange-50\",\n          \"border-gradient-to-r from-pink-200 to-orange-200\",\n        ]\n      : [];\n\n    const classes = cn(\n      baseClasses,\n      variantClasses[variant],\n      paddingClasses[padding],\n      roundedClasses[rounded],\n      hoverClasses,\n      clickableClasses,\n      gradientClasses,\n      className\n    );\n\n    const MotionCard = motion.div;\n\n    return (\n      <MotionCard\n        ref={ref}\n        className={classes}\n        initial={false}\n        animate={scaleIn.animate}\n        whileHover={hover || clickable ? { y: -4, scale: 1.02 } : undefined}\n        whileTap={clickable ? { scale: 0.98 } : undefined}\n        {...(({\n          onAnimationStart,\n          onAnimationEnd,\n          onAnimationIteration,\n          onDrag,\n          onDragStart,\n          onDragEnd,\n          ...rest\n        }) => rest)(props)}\n      >\n        {children}\n      </MotionCard>\n    );\n  }\n);\n\nCard.displayName = \"Card\";\n\n// Composants auxiliaires pour la structure de la carte\nconst CardHeader = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex flex-col space-y-1.5 p-6 pb-0\", className)}\n    {...props}\n  />\n));\nCardHeader.displayName = \"CardHeader\";\n\nconst CardTitle = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLHeadingElement>\n>(({ className, ...props }, ref) => (\n  <h3\n    ref={ref}\n    className={cn(\n      \"text-lg font-semibold leading-none tracking-tight\",\n      className\n    )}\n    {...props}\n  />\n));\nCardTitle.displayName = \"CardTitle\";\n\nconst CardDescription = React.forwardRef<\n  HTMLParagraphElement,\n  React.HTMLAttributes<HTMLParagraphElement>\n>(({ className, ...props }, ref) => (\n  <p ref={ref} className={cn(\"text-sm text-gray-600\", className)} {...props} />\n));\nCardDescription.displayName = \"CardDescription\";\n\nconst CardContent = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\n));\nCardContent.displayName = \"CardContent\";\n\nconst CardFooter = React.forwardRef<\n  HTMLDivElement,\n  React.HTMLAttributes<HTMLDivElement>\n>(({ className, ...props }, ref) => (\n  <div\n    ref={ref}\n    className={cn(\"flex items-center p-6 pt-0\", className)}\n    {...props}\n  />\n));\nCardFooter.displayName = \"CardFooter\";\n\nexport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n};\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAgBA,MAAM,qBAAO,4QAAA,CAAA,UAAK,CAAC,UAAU,MAC3B,CACE,EACE,SAAS,EACT,UAAU,SAAS,EACnB,UAAU,IAAI,EACd,UAAU,IAAI,EACd,QAAQ,KAAK,EACb,YAAY,KAAK,EACjB,WAAW,KAAK,EAChB,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;QAClB;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YAAC;YAAmC;SAAY;QACzD,UAAU;YAAC;YAAY;SAA4B;QACnD,UAAU;YAAC;YAAqC;SAAwB;QACxE,OAAO;YACL;YACA;YACA;SACD;IACH;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,MAAM;QACN,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,eAAe,QACjB;QAAC;QAAwC;KAAkB,GAC3D,EAAE;IAEN,MAAM,mBAAmB,YACrB;QACE;QACA;QACA;KACD,GACD,EAAE;IAEN,MAAM,kBAAkB,WACpB;QACE;QACA;KACD,GACD,EAAE;IAEN,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,QAAQ,EACvB,cAAc,CAAC,QAAQ,EACvB,cACA,kBACA,iBACA;IAGF,MAAM,aAAa,mSAAA,CAAA,SAAM,CAAC,GAAG;IAE7B,qBACE,4SAAC;QACC,KAAK;QACL,WAAW;QACX,SAAS;QACT,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;QACxB,YAAY,SAAS,YAAY;YAAE,GAAG,CAAC;YAAG,OAAO;QAAK,IAAI;QAC1D,UAAU,YAAY;YAAE,OAAO;QAAK,IAAI;QACvC,GAAG,CAAC,CAAC,EACJ,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,MAAM,EACN,WAAW,EACX,SAAS,EACT,GAAG,MACJ,GAAK,IAAI,EAAE,MAAM;kBAEjB;;;;;;AAGP;;AAGF,KAAK,WAAW,GAAG;AAEnB,uDAAuD;AACvD,MAAM,2BAAa,4QAAA,CAAA,UAAK,CAAC,UAAU,OAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,4QAAA,CAAA,UAAK,CAAC,UAAU,OAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,4QAAA,CAAA,UAAK,CAAC,UAAU,OAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC;QAAE,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,yBAAyB;QAAa,GAAG,KAAK;;;;;;;AAE3E,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,4QAAA,CAAA,UAAK,CAAC,UAAU,OAGlC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,4QAAA,CAAA,UAAK,CAAC,UAAU,QAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,4SAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 1249, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-api.ts"], "sourcesContent": ["import { UseApiReturn } from \"@/types\";\nimport { useCallback, useEffect, useState } from \"react\";\n\nexport function useApi<T>(\n  url: string,\n  options?: RequestInit,\n  dependencies: unknown[] = []\n): UseApiReturn<T> {\n  const [data, setData] = useState<T | null>(null);\n  const [loading, setLoading] = useState<boolean>(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchData = useCallback(async () => {\n    try {\n      setLoading(true);\n      setError(null);\n\n      const response = await fetch(url, {\n        headers: {\n          \"Content-Type\": \"application/json\",\n          ...options?.headers,\n        },\n        ...options,\n      });\n\n      if (!response.ok) {\n        throw new Error(`HTTP error! status: ${response.status}`);\n      }\n\n      const result = await response.json();\n      setData(result);\n    } catch (err) {\n      setError(err instanceof Error ? err.message : \"Une erreur est survenue\");\n    } finally {\n      setLoading(false);\n    }\n  }, [url, options]);\n\n  useEffect(() => {\n    fetchData();\n  }, [fetchData, ...dependencies]);\n\n  const refetch = useCallback(() => {\n    return fetchData();\n  }, [fetchData]);\n\n  return { data, loading, error, refetch };\n}\n"], "names": [], "mappings": ";;;AACA;;;AAEO,SAAS,OACd,GAAW,EACX,OAAqB,EACrB,eAA0B,EAAE;;IAE5B,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAY;IAC3C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAW;IAChD,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yCAAE;YAC5B,IAAI;gBACF,WAAW;gBACX,SAAS;gBAET,MAAM,WAAW,MAAM,MAAM,KAAK;oBAChC,SAAS;wBACP,gBAAgB;wBAChB,GAAG,SAAS,OAAO;oBACrB;oBACA,GAAG,OAAO;gBACZ;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAClC,QAAQ;YACV,EAAE,OAAO,KAAK;gBACZ,SAAS,eAAe,QAAQ,IAAI,OAAO,GAAG;YAChD,SAAU;gBACR,WAAW;YACb;QACF;wCAAG;QAAC;QAAK;KAAQ;IAEjB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;WAAc;KAAa;IAE/B,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;uCAAE;YAC1B,OAAO;QACT;sCAAG;QAAC;KAAU;IAEd,OAAO;QAAE;QAAM;QAAS;QAAO;IAAQ;AACzC;GA5CgB", "debugId": null}}, {"offset": {"line": 1319, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/cart-store.ts"], "sourcesContent": ["import { calculateCartItemsCount, calculateCartTotal } from \"@/lib/utils\";\nimport { Cart, CartItem, CartStore } from \"@/types\";\nimport { create } from \"zustand\";\nimport { createJSONStorage, persist } from \"zustand/middleware\";\n\nconst initialCart: Cart = {\n  items: [],\n  totalItems: 0,\n  totalAmount: 0,\n};\n\nexport const useCartStore = create<CartStore>()(\n  persist(\n    (set, get) => ({\n      cart: initialCart,\n\n      addItem: (newItem: Omit<CartItem, \"id\">) => {\n        set((state) => {\n          const existingItemIndex = state.cart.items.findIndex(\n            (item) =>\n              item.productId === newItem.productId &&\n              item.variantId === newItem.variantId\n          );\n\n          let updatedItems: CartItem[];\n\n          if (existingItemIndex >= 0) {\n            // L'article existe déjà, augmenter la quantité\n            updatedItems = state.cart.items.map((item, index) =>\n              index === existingItemIndex\n                ? { ...item, quantity: item.quantity + newItem.quantity }\n                : item\n            );\n          } else {\n            // Nouvel article, l'ajouter avec un ID unique\n            const newCartItem: CartItem = {\n              ...newItem,\n              id: `${newItem.productId}-${newItem.variantId}-${Date.now()}`,\n            };\n            updatedItems = [...state.cart.items, newCartItem];\n          }\n\n          const totalItems = calculateCartItemsCount(updatedItems);\n          const totalAmount = calculateCartTotal(updatedItems);\n\n          return {\n            cart: {\n              items: updatedItems,\n              totalItems,\n              totalAmount,\n            },\n          };\n        });\n      },\n\n      removeItem: (itemId: string) => {\n        set((state) => {\n          const updatedItems = state.cart.items.filter(\n            (item) => item.id !== itemId\n          );\n          const totalItems = calculateCartItemsCount(updatedItems);\n          const totalAmount = calculateCartTotal(updatedItems);\n\n          return {\n            cart: {\n              items: updatedItems,\n              totalItems,\n              totalAmount,\n            },\n          };\n        });\n      },\n\n      updateQuantity: (itemId: string, quantity: number) => {\n        if (quantity <= 0) {\n          get().removeItem(itemId);\n          return;\n        }\n\n        set((state) => {\n          const updatedItems = state.cart.items.map((item: any) =>\n            item.id === itemId ? { ...item, quantity } : item\n          );\n          const totalItems = calculateCartItemsCount(updatedItems);\n          const totalAmount = calculateCartTotal(updatedItems);\n\n          return {\n            cart: {\n              items: updatedItems,\n              totalItems,\n              totalAmount,\n            },\n          };\n        });\n      },\n\n      clearCart: () => {\n        set({ cart: initialCart });\n      },\n\n      getTotalItems: () => {\n        return get().cart.totalItems;\n      },\n\n      getTotalAmount: () => {\n        return get().cart.totalAmount;\n      },\n    }),\n    {\n      name: \"deltagum-cart\",\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({ cart: state.cart }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AAEA;AACA;;;;AAEA,MAAM,cAAoB;IACxB,OAAO,EAAE;IACT,YAAY;IACZ,aAAa;AACf;AAEO,MAAM,eAAe,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IAC/B,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QAEN,SAAS,CAAC;YACR,IAAI,CAAC;gBACH,MAAM,oBAAoB,MAAM,IAAI,CAAC,KAAK,CAAC,SAAS,CAClD,CAAC,OACC,KAAK,SAAS,KAAK,QAAQ,SAAS,IACpC,KAAK,SAAS,KAAK,QAAQ,SAAS;gBAGxC,IAAI;gBAEJ,IAAI,qBAAqB,GAAG;oBAC1B,+CAA+C;oBAC/C,eAAe,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,MAAM,QACzC,UAAU,oBACN;4BAAE,GAAG,IAAI;4BAAE,UAAU,KAAK,QAAQ,GAAG,QAAQ,QAAQ;wBAAC,IACtD;gBAER,OAAO;oBACL,8CAA8C;oBAC9C,MAAM,cAAwB;wBAC5B,GAAG,OAAO;wBACV,IAAI,GAAG,QAAQ,SAAS,CAAC,CAAC,EAAE,QAAQ,SAAS,CAAC,CAAC,EAAE,KAAK,GAAG,IAAI;oBAC/D;oBACA,eAAe;2BAAI,MAAM,IAAI,CAAC,KAAK;wBAAE;qBAAY;gBACnD;gBAEA,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAC3C,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;gBAEvC,OAAO;oBACL,MAAM;wBACJ,OAAO;wBACP;wBACA;oBACF;gBACF;YACF;QACF;QAEA,YAAY,CAAC;YACX,IAAI,CAAC;gBACH,MAAM,eAAe,MAAM,IAAI,CAAC,KAAK,CAAC,MAAM,CAC1C,CAAC,OAAS,KAAK,EAAE,KAAK;gBAExB,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAC3C,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;gBAEvC,OAAO;oBACL,MAAM;wBACJ,OAAO;wBACP;wBACA;oBACF;gBACF;YACF;QACF;QAEA,gBAAgB,CAAC,QAAgB;YAC/B,IAAI,YAAY,GAAG;gBACjB,MAAM,UAAU,CAAC;gBACjB;YACF;YAEA,IAAI,CAAC;gBACH,MAAM,eAAe,MAAM,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,OACzC,KAAK,EAAE,KAAK,SAAS;wBAAE,GAAG,IAAI;wBAAE;oBAAS,IAAI;gBAE/C,MAAM,aAAa,CAAA,GAAA,sHAAA,CAAA,0BAAuB,AAAD,EAAE;gBAC3C,MAAM,cAAc,CAAA,GAAA,sHAAA,CAAA,qBAAkB,AAAD,EAAE;gBAEvC,OAAO;oBACL,MAAM;wBACJ,OAAO;wBACP;wBACA;oBACF;gBACF;YACF;QACF;QAEA,WAAW;YACT,IAAI;gBAAE,MAAM;YAAY;QAC1B;QAEA,eAAe;YACb,OAAO,MAAM,IAAI,CAAC,UAAU;QAC9B;QAEA,gBAAgB;YACd,OAAO,MAAM,IAAI,CAAC,WAAW;QAC/B;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,4PAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YAAE,MAAM,MAAM,IAAI;QAAC,CAAC;AAC9C", "debugId": null}}, {"offset": {"line": 1429, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-cart.ts"], "sourcesContent": ["import { useCartStore } from \"@/stores/cart-store\";\n\nexport function useCart() {\n  const {\n    cart,\n    addItem,\n    removeItem,\n    updateQuantity,\n    clearCart,\n    getTotalItems,\n    getTotalAmount,\n  } = useCartStore();\n\n  return {\n    cart,\n    addItem,\n    removeItem,\n    updateQuantity,\n    clearCart,\n    getTotalItems,\n    getTotalAmount,\n  };\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS;;IACd,MAAM,EACJ,IAAI,EACJ,OAAO,EACP,UAAU,EACV,cAAc,EACd,SAAS,EACT,aAAa,EACb,cAAc,EACf,GAAG,CAAA,GAAA,iIAAA,CAAA,eAAY,AAAD;IAEf,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GApBgB;;QASV,iIAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-click-outside.ts"], "sourcesContent": ["import { useEffect, useRef } from 'react'\n\nexport function useClickOutside<T extends HTMLElement = HTMLElement>(\n  callback: () => void\n) {\n  const ref = useRef<T>(null)\n\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (ref.current && !ref.current.contains(event.target as Node)) {\n        callback()\n      }\n    }\n\n    document.addEventListener('mousedown', handleClickOutside)\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside)\n    }\n  }, [callback])\n\n  return ref\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS,gBACd,QAAoB;;IAEpB,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAK;IAEtB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;gEAAqB,CAAC;oBAC1B,IAAI,IAAI,OAAO,IAAI,CAAC,IAAI,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAW;wBAC9D;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,aAAa;YACvC;6CAAO;oBACL,SAAS,mBAAmB,CAAC,aAAa;gBAC5C;;QACF;oCAAG;QAAC;KAAS;IAEb,OAAO;AACT;GAnBgB", "debugId": null}}, {"offset": {"line": 1502, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/customer-store.ts"], "sourcesContent": ["import { create } from 'zustand'\nimport { persist, createJSONStorage } from 'zustand/middleware'\nimport { Customer, CustomerStore } from '@/types'\n\nexport const useCustomerStore = create<CustomerStore>()(\n  persist(\n    (set, get) => ({\n      customer: null,\n      loading: false,\n      error: null,\n\n      setCustomer: (customer: Customer) => {\n        set({ customer, error: null })\n      },\n\n      updateCustomer: async (data: Partial<Customer>) => {\n        const currentCustomer = get().customer\n        if (!currentCustomer) {\n          set({ error: 'Aucun client connecté' })\n          return\n        }\n\n        set({ loading: true, error: null })\n\n        try {\n          const response = await fetch(`/api/customers/${currentCustomer.id}`, {\n            method: 'PATCH',\n            headers: {\n              'Content-Type': 'application/json',\n            },\n            body: JSON.stringify(data),\n          })\n\n          if (!response.ok) {\n            throw new Error(`HTTP error! status: ${response.status}`)\n          }\n\n          const result = await response.json()\n\n          if (result.success) {\n            set({ \n              customer: result.data,\n              loading: false \n            })\n          } else {\n            throw new Error(result.error || 'Erreur lors de la mise à jour')\n          }\n        } catch (error) {\n          set({ \n            error: error instanceof Error ? error.message : 'Erreur inconnue',\n            loading: false \n          })\n        }\n      },\n\n      clearCustomer: () => {\n        set({ customer: null, error: null })\n      },\n    }),\n    {\n      name: 'deltagum-customer',\n      storage: createJSONStorage(() => localStorage),\n      partialize: (state) => ({ customer: state.customer }),\n    }\n  )\n)\n\n// Hook personnalisé pour utiliser le store des clients\nexport const useCustomer = () => {\n  const {\n    customer,\n    loading,\n    error,\n    setCustomer,\n    updateCustomer,\n    clearCustomer,\n  } = useCustomerStore()\n\n  return {\n    customer,\n    loading,\n    error,\n    setCustomer,\n    updateCustomer,\n    clearCustomer,\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAGO,MAAM,mBAAmB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IACnC,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,UAAU;QACV,SAAS;QACT,OAAO;QAEP,aAAa,CAAC;YACZ,IAAI;gBAAE;gBAAU,OAAO;YAAK;QAC9B;QAEA,gBAAgB,OAAO;YACrB,MAAM,kBAAkB,MAAM,QAAQ;YACtC,IAAI,CAAC,iBAAiB;gBACpB,IAAI;oBAAE,OAAO;gBAAwB;gBACrC;YACF;YAEA,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YAEjC,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,CAAC,eAAe,EAAE,gBAAgB,EAAE,EAAE,EAAE;oBACnE,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,CAAC,oBAAoB,EAAE,SAAS,MAAM,EAAE;gBAC1D;gBAEA,MAAM,SAAS,MAAM,SAAS,IAAI;gBAElC,IAAI,OAAO,OAAO,EAAE;oBAClB,IAAI;wBACF,UAAU,OAAO,IAAI;wBACrB,SAAS;oBACX;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;gBAClC;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,SAAS;gBACX;YACF;QACF;QAEA,eAAe;YACb,IAAI;gBAAE,UAAU;gBAAM,OAAO;YAAK;QACpC;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,4PAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YAAE,UAAU,MAAM,QAAQ;QAAC,CAAC;AACtD;AAKG,MAAM,cAAc;;IACzB,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,KAAK,EACL,WAAW,EACX,cAAc,EACd,aAAa,EACd,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlBa;;QAQP", "debugId": null}}, {"offset": {"line": 1599, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-customer.ts"], "sourcesContent": ["// Re-export du hook depuis le store\nexport { useCustomer } from \"@/stores/customer-store\";\n"], "names": [], "mappings": "AAAA,oCAAoC;;AACpC", "debugId": null}}, {"offset": {"line": 1621, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-debounce.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\n\nexport function useDebounce<T>(value: T, delay: number): T {\n  const [debouncedValue, setDebouncedValue] = useState<T>(value)\n\n  useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value)\n    }, delay)\n\n    return () => {\n      clearTimeout(handler)\n    }\n  }, [value, delay])\n\n  return debouncedValue\n}\n"], "names": [], "mappings": ";;;AAAA;;;AAEO,SAAS,YAAe,KAAQ,EAAE,KAAa;;IACpD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAK;IAExD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,UAAU;iDAAW;oBACzB,kBAAkB;gBACpB;gDAAG;YAEH;yCAAO;oBACL,aAAa;gBACf;;QACF;gCAAG;QAAC;QAAO;KAAM;IAEjB,OAAO;AACT;GAdgB", "debugId": null}}, {"offset": {"line": 1659, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-intersection-observer.ts"], "sourcesContent": ["import { useEffect, useRef, useState } from \"react\";\n\ninterface UseIntersectionObserverOptions {\n  threshold?: number | number[];\n  root?: Element | null;\n  rootMargin?: string;\n  freezeOnceVisible?: boolean;\n}\n\nexport function useIntersectionObserver(\n  options: UseIntersectionObserverOptions = {}\n) {\n  const {\n    threshold = 0,\n    root = null,\n    rootMargin = \"0%\",\n    freezeOnceVisible = false,\n  } = options;\n\n  const [entry, setEntry] = useState<IntersectionObserverEntry | undefined>();\n  const [isVisible, setIsVisible] = useState(false);\n  const elementRef = useRef<Element | null>(null);\n\n  const frozen = entry?.isIntersecting && freezeOnceVisible;\n\n  const updateEntry = ([entry]: IntersectionObserverEntry[]): void => {\n    setEntry(entry);\n    setIsVisible(entry.isIntersecting);\n  };\n\n  useEffect(() => {\n    const node = elementRef?.current;\n    const hasIOSupport = !!window.IntersectionObserver;\n\n    if (!hasIOSupport || frozen || !node) return;\n\n    const observerParams = { threshold, root, rootMargin };\n    const observer = new IntersectionObserver(updateEntry, observerParams);\n\n    observer.observe(node);\n\n    return () => observer.disconnect();\n  }, [elementRef, threshold, root, rootMargin, frozen]);\n\n  return { ref: elementRef, entry, isVisible };\n}\n"], "names": [], "mappings": ";;;AAAA;;;AASO,SAAS,wBACd,UAA0C,CAAC,CAAC;;IAE5C,MAAM,EACJ,YAAY,CAAC,EACb,OAAO,IAAI,EACX,aAAa,IAAI,EACjB,oBAAoB,KAAK,EAC1B,GAAG;IAEJ,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;IACjC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IAE1C,MAAM,SAAS,OAAO,kBAAkB;IAExC,MAAM,cAAc,CAAC,CAAC,MAAmC;QACvD,SAAS;QACT,aAAa,MAAM,cAAc;IACnC;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;6CAAE;YACR,MAAM,OAAO,YAAY;YACzB,MAAM,eAAe,CAAC,CAAC,OAAO,oBAAoB;YAElD,IAAI,CAAC,gBAAgB,UAAU,CAAC,MAAM;YAEtC,MAAM,iBAAiB;gBAAE;gBAAW;gBAAM;YAAW;YACrD,MAAM,WAAW,IAAI,qBAAqB,aAAa;YAEvD,SAAS,OAAO,CAAC;YAEjB;qDAAO,IAAM,SAAS,UAAU;;QAClC;4CAAG;QAAC;QAAY;QAAW;QAAM;QAAY;KAAO;IAEpD,OAAO;QAAE,KAAK;QAAY;QAAO;IAAU;AAC7C;GApCgB", "debugId": null}}, {"offset": {"line": 1715, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-local-storage.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\nimport { UseLocalStorageReturn } from '@/types'\nimport { getFromLocalStorage, setToLocalStorage, removeFromLocalStorage } from '@/lib/utils'\n\nexport function useLocalStorage<T>(key: string, initialValue: T): UseLocalStorageReturn<T> {\n  // État pour stocker la valeur\n  const [storedValue, setStoredValue] = useState<T>(() => {\n    return getFromLocalStorage(key, initialValue)\n  })\n\n  // Fonction pour mettre à jour la valeur\n  const setValue = (value: T | ((val: T) => T)) => {\n    try {\n      // Permettre à la valeur d'être une fonction pour avoir la même API que useState\n      const valueToStore = value instanceof Function ? value(storedValue) : value\n      \n      // Sauvegarder l'état\n      setStoredValue(valueToStore)\n      \n      // Sauvegarder dans localStorage\n      setToLocalStorage(key, valueToStore)\n    } catch (error) {\n      console.error(`Error setting localStorage key \"${key}\":`, error)\n    }\n  }\n\n  // Fonction pour supprimer la valeur\n  const removeValue = () => {\n    try {\n      setStoredValue(initialValue)\n      removeFromLocalStorage(key)\n    } catch (error) {\n      console.error(`Error removing localStorage key \"${key}\":`, error)\n    }\n  }\n\n  // Écouter les changements dans localStorage (pour la synchronisation entre onglets)\n  useEffect(() => {\n    const handleStorageChange = (e: StorageEvent) => {\n      if (e.key === key && e.newValue !== null) {\n        try {\n          setStoredValue(JSON.parse(e.newValue))\n        } catch (error) {\n          console.error(`Error parsing localStorage value for key \"${key}\":`, error)\n        }\n      }\n    }\n\n    window.addEventListener('storage', handleStorageChange)\n    return () => window.removeEventListener('storage', handleStorageChange)\n  }, [key])\n\n  return { value: storedValue, setValue, removeValue }\n}\n"], "names": [], "mappings": ";;;AAAA;AAEA;;;;AAEO,SAAS,gBAAmB,GAAW,EAAE,YAAe;;IAC7D,8BAA8B;IAC9B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD;oCAAK;YAChD,OAAO,CAAA,GAAA,sHAAA,CAAA,sBAAmB,AAAD,EAAE,KAAK;QAClC;;IAEA,wCAAwC;IACxC,MAAM,WAAW,CAAC;QAChB,IAAI;YACF,gFAAgF;YAChF,MAAM,eAAe,iBAAiB,WAAW,MAAM,eAAe;YAEtE,qBAAqB;YACrB,eAAe;YAEf,gCAAgC;YAChC,CAAA,GAAA,sHAAA,CAAA,oBAAiB,AAAD,EAAE,KAAK;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,gCAAgC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC5D;IACF;IAEA,oCAAoC;IACpC,MAAM,cAAc;QAClB,IAAI;YACF,eAAe;YACf,CAAA,GAAA,sHAAA,CAAA,yBAAsB,AAAD,EAAE;QACzB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,CAAC,iCAAiC,EAAE,IAAI,EAAE,CAAC,EAAE;QAC7D;IACF;IAEA,oFAAoF;IACpF,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,MAAM;iEAAsB,CAAC;oBAC3B,IAAI,EAAE,GAAG,KAAK,OAAO,EAAE,QAAQ,KAAK,MAAM;wBACxC,IAAI;4BACF,eAAe,KAAK,KAAK,CAAC,EAAE,QAAQ;wBACtC,EAAE,OAAO,OAAO;4BACd,QAAQ,KAAK,CAAC,CAAC,0CAA0C,EAAE,IAAI,EAAE,CAAC,EAAE;wBACtE;oBACF;gBACF;;YAEA,OAAO,gBAAgB,CAAC,WAAW;YACnC;6CAAO,IAAM,OAAO,mBAAmB,CAAC,WAAW;;QACrD;oCAAG;QAAC;KAAI;IAER,OAAO;QAAE,OAAO;QAAa;QAAU;IAAY;AACrD;GAjDgB", "debugId": null}}, {"offset": {"line": 1791, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/product-store.ts"], "sourcesContent": ["import { Product, ProductStore, ProductVariant } from \"@/types\";\nimport { create } from \"zustand\";\n\nexport const useProductStore = create<ProductStore>((set, get) => ({\n  products: [],\n  selectedProduct: null,\n  selectedVariant: null,\n  loading: true, // Commencer en état de chargement\n  error: null,\n\n  fetchProducts: async (retryCount = 0) => {\n    set({ loading: true, error: null });\n\n    try {\n      // Timeout de 10 secondes\n      const controller = new AbortController();\n      const timeoutId = setTimeout(() => controller.abort(), 10000);\n\n      const response = await fetch(\"/api/products\", {\n        cache: \"no-store\", // Éviter les problèmes de cache\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        signal: controller.signal,\n      });\n\n      clearTimeout(timeoutId);\n\n      if (!response.ok) {\n        throw new Error(\n          `Erreur HTTP: ${response.status} - ${response.statusText}`\n        );\n      }\n\n      const data = await response.json();\n\n      if (data.success && data.data && Array.isArray(data.data.products)) {\n        console.log(\"Produits chargés avec succès:\", data.data.products.length);\n        set({\n          products: data.data.products,\n          loading: false,\n          error: null,\n        });\n      } else {\n        throw new Error(data.error || \"Format de réponse invalide\");\n      }\n    } catch (error) {\n      console.error(\"Erreur lors du chargement des produits:\", error);\n\n      // Retry automatique (max 2 tentatives)\n      if (retryCount < 2) {\n        console.log(`Tentative de rechargement ${retryCount + 1}/2...`);\n        setTimeout(() => {\n          const store = get();\n          if (store.fetchProducts) {\n            (store.fetchProducts as any)(retryCount + 1);\n          }\n        }, 1000 * (retryCount + 1)); // Délai progressif\n        return;\n      }\n\n      set({\n        error: error instanceof Error ? error.message : \"Erreur de connexion\",\n        loading: false,\n      });\n    }\n  },\n\n  selectProduct: (product: Product) => {\n    const firstVariant = product.variants?.[0];\n    const variantWithProduct = firstVariant\n      ? {\n          ...firstVariant,\n          product: product,\n        }\n      : null;\n\n    set({\n      selectedProduct: product,\n      selectedVariant: variantWithProduct,\n    });\n  },\n\n  selectVariant: (variant: ProductVariant) => {\n    set({ selectedVariant: variant });\n  },\n}));\n\n// Préchargement automatique des produits\nif (typeof window !== \"undefined\") {\n  // Précharger les produits dès que le store est initialisé côté client\n  setTimeout(() => {\n    useProductStore.getState().fetchProducts();\n  }, 100);\n}\n\n// Hook personnalisé pour utiliser le store des produits\nexport const useProducts = () => {\n  const {\n    products,\n    selectedProduct,\n    selectedVariant,\n    loading,\n    error,\n    fetchProducts,\n    selectProduct,\n    selectVariant,\n  } = useProductStore();\n\n  return {\n    products,\n    selectedProduct,\n    selectedVariant,\n    loading,\n    error,\n    fetchProducts,\n    selectProduct,\n    selectVariant,\n  };\n};\n"], "names": [], "mappings": ";;;;AACA;;;AAEO,MAAM,kBAAkB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EAAgB,CAAC,KAAK,MAAQ,CAAC;QACjE,UAAU,EAAE;QACZ,iBAAiB;QACjB,iBAAiB;QACjB,SAAS;QACT,OAAO;QAEP,eAAe,OAAO,aAAa,CAAC;YAClC,IAAI;gBAAE,SAAS;gBAAM,OAAO;YAAK;YAEjC,IAAI;gBACF,yBAAyB;gBACzB,MAAM,aAAa,IAAI;gBACvB,MAAM,YAAY,WAAW,IAAM,WAAW,KAAK,IAAI;gBAEvD,MAAM,WAAW,MAAM,MAAM,iBAAiB;oBAC5C,OAAO;oBACP,SAAS;wBACP,gBAAgB;oBAClB;oBACA,QAAQ,WAAW,MAAM;gBAC3B;gBAEA,aAAa;gBAEb,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MACR,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,GAAG,EAAE,SAAS,UAAU,EAAE;gBAE9D;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,KAAK,OAAO,IAAI,KAAK,IAAI,IAAI,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,QAAQ,GAAG;oBAClE,QAAQ,GAAG,CAAC,iCAAiC,KAAK,IAAI,CAAC,QAAQ,CAAC,MAAM;oBACtE,IAAI;wBACF,UAAU,KAAK,IAAI,CAAC,QAAQ;wBAC5B,SAAS;wBACT,OAAO;oBACT;gBACF,OAAO;oBACL,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,2CAA2C;gBAEzD,uCAAuC;gBACvC,IAAI,aAAa,GAAG;oBAClB,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,aAAa,EAAE,KAAK,CAAC;oBAC9D,WAAW;wBACT,MAAM,QAAQ;wBACd,IAAI,MAAM,aAAa,EAAE;4BACtB,MAAM,aAAa,CAAS,aAAa;wBAC5C;oBACF,GAAG,OAAO,CAAC,aAAa,CAAC,IAAI,mBAAmB;oBAChD;gBACF;gBAEA,IAAI;oBACF,OAAO,iBAAiB,QAAQ,MAAM,OAAO,GAAG;oBAChD,SAAS;gBACX;YACF;QACF;QAEA,eAAe,CAAC;YACd,MAAM,eAAe,QAAQ,QAAQ,EAAE,CAAC,EAAE;YAC1C,MAAM,qBAAqB,eACvB;gBACE,GAAG,YAAY;gBACf,SAAS;YACX,IACA;YAEJ,IAAI;gBACF,iBAAiB;gBACjB,iBAAiB;YACnB;QACF;QAEA,eAAe,CAAC;YACd,IAAI;gBAAE,iBAAiB;YAAQ;QACjC;IACF,CAAC;AAED,yCAAyC;AACzC,wCAAmC;IACjC,sEAAsE;IACtE,WAAW;QACT,gBAAgB,QAAQ,GAAG,aAAa;IAC1C,GAAG;AACL;AAGO,MAAM,cAAc;;IACzB,MAAM,EACJ,QAAQ,EACR,eAAe,EACf,eAAe,EACf,OAAO,EACP,KAAK,EACL,aAAa,EACb,aAAa,EACb,aAAa,EACd,GAAG;IAEJ,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAtBa;;QAUP", "debugId": null}}, {"offset": {"line": 1906, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-products.ts"], "sourcesContent": ["// Re-export du hook depuis le store\nexport { useProducts } from \"@/stores/product-store\";\n"], "names": [], "mappings": "AAAA,oCAAoC;;AACpC", "debugId": null}}, {"offset": {"line": 1928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-scroll-position.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\nimport { throttle } from '@/lib/utils'\n\ninterface ScrollPosition {\n  x: number\n  y: number\n}\n\nexport function useScrollPosition(): ScrollPosition {\n  const [scrollPosition, setScrollPosition] = useState<ScrollPosition>({\n    x: 0,\n    y: 0,\n  })\n\n  useEffect(() => {\n    const updatePosition = throttle(() => {\n      setScrollPosition({\n        x: window.pageXOffset,\n        y: window.pageYOffset,\n      })\n    }, 100)\n\n    window.addEventListener('scroll', updatePosition)\n    updatePosition() // Initialiser la position\n\n    return () => window.removeEventListener('scroll', updatePosition)\n  }, [])\n\n  return scrollPosition\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAOO,SAAS;;IACd,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAkB;QACnE,GAAG;QACH,GAAG;IACL;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;8DAAE;oBAC9B,kBAAkB;wBAChB,GAAG,OAAO,WAAW;wBACrB,GAAG,OAAO,WAAW;oBACvB;gBACF;6DAAG;YAEH,OAAO,gBAAgB,CAAC,UAAU;YAClC,iBAAiB,0BAA0B;;YAE3C;+CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;sCAAG,EAAE;IAEL,OAAO;AACT;GArBgB", "debugId": null}}, {"offset": {"line": 1972, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/use-window-size.ts"], "sourcesContent": ["import { useState, useEffect } from 'react'\nimport { throttle } from '@/lib/utils'\n\ninterface WindowSize {\n  width: number\n  height: number\n}\n\nexport function useWindowSize(): WindowSize {\n  const [windowSize, setWindowSize] = useState<WindowSize>({\n    width: 0,\n    height: 0,\n  })\n\n  useEffect(() => {\n    const handleResize = throttle(() => {\n      setWindowSize({\n        width: window.innerWidth,\n        height: window.innerHeight,\n      })\n    }, 100)\n\n    // Initialiser la taille\n    handleResize()\n\n    window.addEventListener('resize', handleResize)\n    return () => window.removeEventListener('resize', handleResize)\n  }, [])\n\n  return windowSize\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;;AAOO,SAAS;;IACd,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAc;QACvD,OAAO;QACP,QAAQ;IACV;IAEA,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;mCAAE;YACR,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,WAAQ,AAAD;wDAAE;oBAC5B,cAAc;wBACZ,OAAO,OAAO,UAAU;wBACxB,QAAQ,OAAO,WAAW;oBAC5B;gBACF;uDAAG;YAEH,wBAAwB;YACxB;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;2CAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;kCAAG,EAAE;IAEL,OAAO;AACT;GAtBgB", "debugId": null}}, {"offset": {"line": 2016, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/index.ts"], "sourcesContent": ["export { useApi } from \"./use-api\";\nexport { useCart } from \"./use-cart\";\nexport { useClickOutside } from \"./use-click-outside\";\nexport { useCustomer } from \"./use-customer\";\nexport { useDebounce } from \"./use-debounce\";\nexport { useIntersectionObserver } from \"./use-intersection-observer\";\nexport { useLocalStorage } from \"./use-local-storage\";\nexport { useProducts } from \"./use-products\";\nexport { useScrollPosition } from \"./use-scroll-position\";\nexport { useWindowSize } from \"./use-window-size\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 2064, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Modal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useClickOutside } from \"@/hooks\";\n// Animations removed - using inline animations\nimport { cn } from \"@/lib/utils\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useEffect } from \"react\";\nimport { createPortal } from \"react-dom\";\n\nexport interface ModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n  title?: string;\n  description?: string;\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"full\";\n  closeOnOverlayClick?: boolean;\n  closeOnEscape?: boolean;\n  showCloseButton?: boolean;\n  children: React.ReactNode;\n  className?: string;\n  overlayClassName?: string;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  title,\n  description,\n  size = \"md\",\n  closeOnOverlayClick = true,\n  closeOnEscape = true,\n  showCloseButton = true,\n  children,\n  className,\n  overlayClassName,\n}) => {\n  // Fermer avec Escape\n  useEffect(() => {\n    if (!closeOnEscape) return;\n\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\" && isOpen) {\n        onClose();\n      }\n    };\n\n    document.addEventListener(\"keydown\", handleEscape);\n    return () => document.removeEventListener(\"keydown\", handleEscape);\n  }, [isOpen, onClose, closeOnEscape]);\n\n  // Fermer en cliquant à l'extérieur\n  const modalRef = useClickOutside<HTMLDivElement>(() => {\n    if (closeOnOverlayClick && isOpen) {\n      onClose();\n    }\n  });\n\n  // Bloquer le scroll du body quand la modal est ouverte\n  useEffect(() => {\n    if (isOpen) {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"unset\";\n    }\n\n    return () => {\n      document.body.style.overflow = \"unset\";\n    };\n  }, [isOpen]);\n\n  const sizeClasses = {\n    sm: \"max-w-sm sm:max-w-md\",\n    md: \"max-w-md sm:max-w-lg\",\n    lg: \"max-w-lg sm:max-w-xl lg:max-w-2xl\",\n    xl: \"max-w-xl sm:max-w-2xl lg:max-w-4xl\",\n    full: \"max-w-full mx-2 sm:mx-4\",\n  };\n\n  const overlayClasses = cn(\n    \"fixed inset-0 z-50 flex items-center justify-center p-2 sm:p-4\",\n    \"bg-black/50 backdrop-blur-sm\",\n    overlayClassName\n  );\n\n  const contentClasses = cn(\n    \"relative w-full bg-white rounded-lg sm:rounded-xl shadow-2xl\",\n    \"max-h-[95vh] sm:max-h-[90vh] overflow-y-auto\",\n    sizeClasses[size],\n    className\n  );\n\n  if (!isOpen) return null;\n\n  const modalContent = (\n    <AnimatePresence>\n      {isOpen && (\n        <motion.div\n          className={overlayClasses}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.2 }}\n        >\n          <motion.div\n            ref={modalRef}\n            className={contentClasses}\n            initial={{ opacity: 0, scale: 0.95, y: 20 }}\n            animate={{ opacity: 1, scale: 1, y: 0 }}\n            exit={{ opacity: 0, scale: 0.95, y: 20 }}\n            transition={{ duration: 0.2 }}\n          >\n            {/* Header */}\n            {(title || showCloseButton) && (\n              <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\n                <div>\n                  {title && (\n                    <h2 className=\"text-xl font-semibold text-gray-900\">\n                      {title}\n                    </h2>\n                  )}\n                  {description && (\n                    <p className=\"mt-1 text-sm text-gray-600\">{description}</p>\n                  )}\n                </div>\n                {showCloseButton && (\n                  <button\n                    onClick={onClose}\n                    className=\"p-2 text-gray-400 hover:text-gray-600 hover:bg-gray-100 rounded-lg transition-colors\"\n                    aria-label=\"Fermer\"\n                  >\n                    <svg\n                      className=\"w-5 h-5\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                    >\n                      <path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={2}\n                        d=\"M6 18L18 6M6 6l12 12\"\n                      />\n                    </svg>\n                  </button>\n                )}\n              </div>\n            )}\n\n            {/* Content */}\n            <div className=\"p-6\">{children}</div>\n          </motion.div>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  );\n\n  // Utiliser un portal pour rendre la modal au niveau du body\n  return typeof window !== \"undefined\"\n    ? createPortal(modalContent, document.body)\n    : null;\n};\n\n// Composants auxiliaires pour la structure de la modal\nconst ModalHeader: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => (\n  <div className={cn(\"p-6 border-b border-gray-200\", className)}>\n    {children}\n  </div>\n);\n\nconst ModalBody: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => (\n  <div className={cn(\"p-6\", className)}>{children}</div>\n);\n\nconst ModalFooter: React.FC<{\n  children: React.ReactNode;\n  className?: string;\n}> = ({ children, className }) => (\n  <div\n    className={cn(\n      \"flex items-center justify-end gap-3 p-6 border-t border-gray-200\",\n      className\n    )}\n  >\n    {children}\n  </div>\n);\n\nexport { Modal, ModalBody, ModalFooter, ModalHeader };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AACA,+CAA+C;AAC/C;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;AAuBA,MAAM,QAA8B,CAAC,EACnC,MAAM,EACN,OAAO,EACP,KAAK,EACL,WAAW,EACX,OAAO,IAAI,EACX,sBAAsB,IAAI,EAC1B,gBAAgB,IAAI,EACpB,kBAAkB,IAAI,EACtB,QAAQ,EACR,SAAS,EACT,gBAAgB,EACjB;;IACC,qBAAqB;IACrB,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,CAAC,eAAe;YAEpB,MAAM;gDAAe,CAAC;oBACpB,IAAI,EAAE,GAAG,KAAK,YAAY,QAAQ;wBAChC;oBACF;gBACF;;YAEA,SAAS,gBAAgB,CAAC,WAAW;YACrC;mCAAO,IAAM,SAAS,mBAAmB,CAAC,WAAW;;QACvD;0BAAG;QAAC;QAAQ;QAAS;KAAc;IAEnC,mCAAmC;IACnC,MAAM,WAAW,CAAA,GAAA,0IAAA,CAAA,kBAAe,AAAD;2CAAkB;YAC/C,IAAI,uBAAuB,QAAQ;gBACjC;YACF;QACF;;IAEA,uDAAuD;IACvD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2BAAE;YACR,IAAI,QAAQ;gBACV,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;mCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;0BAAG;QAAC;KAAO;IAEX,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,MAAM;IACR;IAEA,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACtB,kEACA,gCACA;IAGF,MAAM,iBAAiB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACtB,gEACA,gDACA,WAAW,CAAC,KAAK,EACjB;IAGF,IAAI,CAAC,QAAQ,OAAO;IAEpB,MAAM,6BACJ,4SAAC,kSAAA,CAAA,kBAAe;kBACb,wBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW;YACX,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;sBAE5B,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,KAAK;gBACL,WAAW;gBACX,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAM,GAAG;gBAAG;gBAC1C,SAAS;oBAAE,SAAS;oBAAG,OAAO;oBAAG,GAAG;gBAAE;gBACtC,MAAM;oBAAE,SAAS;oBAAG,OAAO;oBAAM,GAAG;gBAAG;gBACvC,YAAY;oBAAE,UAAU;gBAAI;;oBAG3B,CAAC,SAAS,eAAe,mBACxB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;;oCACE,uBACC,4SAAC;wCAAG,WAAU;kDACX;;;;;;oCAGJ,6BACC,4SAAC;wCAAE,WAAU;kDAA8B;;;;;;;;;;;;4BAG9C,iCACC,4SAAC;gCACC,SAAS;gCACT,WAAU;gCACV,cAAW;0CAEX,cAAA,4SAAC;oCACC,WAAU;oCACV,MAAK;oCACL,QAAO;oCACP,SAAQ;8CAER,cAAA,4SAAC;wCACC,eAAc;wCACd,gBAAe;wCACf,aAAa;wCACb,GAAE;;;;;;;;;;;;;;;;;;;;;;kCASd,4SAAC;wBAAI,WAAU;kCAAO;;;;;;;;;;;;;;;;;;;;;;IAOhC,4DAA4D;IAC5D,OAAO,qDACH,CAAA,GAAA,mRAAA,CAAA,eAAY,AAAD,EAAE,cAAc,SAAS,IAAI;AAE9C;GAzIM;;QA4Ba,0IAAA,CAAA,kBAAe;;;KA5B5B;AA2IN,uDAAuD;AACvD,MAAM,cAGD,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAC3B,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gCAAgC;kBAChD;;;;;;MALC;AASN,MAAM,YAGD,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAC3B,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,OAAO;kBAAa;;;;;;MAJnC;AAON,MAAM,cAGD,CAAC,EAAE,QAAQ,EAAE,SAAS,EAAE,iBAC3B,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oEACA;kBAGD;;;;;;MAVC", "debugId": null}}, {"offset": {"line": 2320, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Input.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nexport interface InputProps\n  extends React.InputHTMLAttributes<HTMLInputElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  leftIcon?: React.ReactNode;\n  rightIcon?: React.ReactNode;\n  variant?: \"default\" | \"filled\" | \"outlined\";\n  inputSize?: \"sm\" | \"md\" | \"lg\";\n  fullWidth?: boolean;\n}\n\nconst Input = React.forwardRef<HTMLInputElement, InputProps>(\n  (\n    {\n      className,\n      type = \"text\",\n      label,\n      error,\n      helperText,\n      leftIcon,\n      rightIcon,\n      variant = \"default\",\n      inputSize = \"md\",\n      fullWidth = false,\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    const baseClasses = [\n      \"transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-offset-1\",\n      \"disabled:opacity-50 disabled:cursor-not-allowed\",\n    ];\n\n    const variantClasses = {\n      default: [\n        \"border border-gray-300 bg-white text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:border-gray-400\",\n        \"focus:border-pink-500 focus:ring-pink-500/20\",\n        \"shadow-sm\",\n        error\n          ? \"border-red-500 focus:border-red-500 focus:ring-red-500/20\"\n          : \"\",\n      ],\n      filled: [\n        \"border-0 bg-gray-50 text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:bg-gray-100\",\n        \"focus:bg-white focus:ring-pink-500/20 focus:shadow-md\",\n        \"shadow-sm\",\n        error ? \"bg-red-50 focus:ring-red-500/20\" : \"\",\n      ],\n      outlined: [\n        \"border-2 border-gray-300 bg-white text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:border-gray-400\",\n        \"focus:border-pink-500 focus:ring-pink-500/20\",\n        \"shadow-sm\",\n        error\n          ? \"border-red-500 focus:border-red-500 focus:ring-red-500/20\"\n          : \"\",\n      ],\n    };\n\n    const sizeClasses = {\n      sm: \"px-3 py-2 text-sm\",\n      md: \"px-4 py-2.5 text-base\",\n      lg: \"px-5 py-3 text-lg\",\n    };\n\n    const roundedClasses = {\n      sm: \"rounded-md\",\n      md: \"rounded-lg\",\n      lg: \"rounded-xl\",\n    };\n\n    const inputClasses = cn(\n      baseClasses,\n      variantClasses[variant],\n      sizeClasses[inputSize],\n      roundedClasses[inputSize],\n      leftIcon && \"pl-10\",\n      rightIcon && \"pr-10\",\n      fullWidth && \"w-full\",\n      className\n    );\n\n    const iconSize = {\n      sm: \"w-4 h-4\",\n      md: \"w-5 h-5\",\n      lg: \"w-6 h-6\",\n    };\n\n    const iconPosition = {\n      sm: \"left-3\",\n      md: \"left-3\",\n      lg: \"left-4\",\n    };\n\n    const rightIconPosition = {\n      sm: \"right-3\",\n      md: \"right-3\",\n      lg: \"right-4\",\n    };\n\n    return (\n      <div className={cn(\"relative\", fullWidth && \"w-full\")}>\n        {/* Label */}\n        {label && (\n          <motion.label\n            className={cn(\n              \"block text-sm font-medium mb-2 transition-colors\",\n              error ? \"text-red-700\" : \"text-gray-700\",\n              disabled && \"text-gray-400\"\n            )}\n            animate={{\n              color: isFocused\n                ? error\n                  ? \"#dc2626\"\n                  : \"#ec4899\"\n                : error\n                ? \"#dc2626\"\n                : \"#374151\",\n            }}\n          >\n            {label}\n          </motion.label>\n        )}\n\n        {/* Input Container */}\n        <div className=\"relative\">\n          {/* Left Icon */}\n          {leftIcon && (\n            <div\n              className={cn(\n                \"absolute top-1/2 transform -translate-y-1/2 text-gray-400\",\n                iconPosition[inputSize]\n              )}\n            >\n              <span className={iconSize[inputSize]}>{leftIcon}</span>\n            </div>\n          )}\n\n          {/* Input */}\n          <motion.input\n            ref={ref}\n            type={type}\n            className={inputClasses}\n            disabled={disabled}\n            onFocus={(e) => {\n              setIsFocused(true);\n              props.onFocus?.(e);\n            }}\n            onBlur={(e) => {\n              setIsFocused(false);\n              props.onBlur?.(e);\n            }}\n            whileFocus={{ scale: 1.01 }}\n            {...(({\n              onAnimationStart,\n              onAnimationEnd,\n              onAnimationIteration,\n              onDrag,\n              onDragStart,\n              onDragEnd,\n              ...rest\n            }) => rest)(props)}\n          />\n\n          {/* Right Icon */}\n          {rightIcon && (\n            <div\n              className={cn(\n                \"absolute top-1/2 transform -translate-y-1/2 text-gray-400\",\n                rightIconPosition[inputSize]\n              )}\n            >\n              <span className={iconSize[inputSize]}>{rightIcon}</span>\n            </div>\n          )}\n        </div>\n\n        {/* Error Message */}\n        {error && (\n          <motion.p\n            className=\"mt-1 text-sm text-red-600\"\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n          >\n            {error}\n          </motion.p>\n        )}\n\n        {/* Helper Text */}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nInput.displayName = \"Input\";\n\n// Composant Textarea\nexport interface TextareaProps\n  extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  variant?: \"default\" | \"filled\" | \"outlined\";\n  inputSize?: \"sm\" | \"md\" | \"lg\";\n  fullWidth?: boolean;\n  resize?: \"none\" | \"vertical\" | \"horizontal\" | \"both\";\n}\n\nconst Textarea = React.forwardRef<HTMLTextAreaElement, TextareaProps>(\n  (\n    {\n      className,\n      label,\n      error,\n      helperText,\n      variant = \"default\",\n      inputSize = \"md\",\n      fullWidth = false,\n      resize = \"vertical\",\n      disabled,\n      ...props\n    },\n    ref\n  ) => {\n    const [isFocused, setIsFocused] = React.useState(false);\n\n    const baseClasses = [\n      \"transition-all duration-200\",\n      \"focus:outline-none focus:ring-2 focus:ring-offset-1\",\n      \"disabled:opacity-50 disabled:cursor-not-allowed\",\n      \"min-h-[80px]\",\n    ];\n\n    const variantClasses = {\n      default: [\n        \"border border-gray-300 bg-white text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:border-gray-400\",\n        \"focus:border-pink-500 focus:ring-pink-500/20\",\n        \"shadow-sm\",\n        error\n          ? \"border-red-500 focus:border-red-500 focus:ring-red-500/20\"\n          : \"\",\n      ],\n      filled: [\n        \"border-0 bg-gray-50 text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:bg-gray-100\",\n        \"focus:bg-white focus:ring-pink-500/20 focus:shadow-md\",\n        \"shadow-sm\",\n        error ? \"bg-red-50 focus:ring-red-500/20\" : \"\",\n      ],\n      outlined: [\n        \"border-2 border-gray-300 bg-white text-gray-900\",\n        \"placeholder:text-gray-700\", // Texte du placeholder plus foncé\n        \"hover:border-gray-400\",\n        \"focus:border-pink-500 focus:ring-pink-500/20\",\n        \"shadow-sm\",\n        error\n          ? \"border-red-500 focus:border-red-500 focus:ring-red-500/20\"\n          : \"\",\n      ],\n    };\n\n    const sizeClasses = {\n      sm: \"px-3 py-2 text-sm\",\n      md: \"px-4 py-2.5 text-base\",\n      lg: \"px-5 py-3 text-lg\",\n    };\n\n    const roundedClasses = {\n      sm: \"rounded-md\",\n      md: \"rounded-lg\",\n      lg: \"rounded-xl\",\n    };\n\n    const resizeClasses = {\n      none: \"resize-none\",\n      vertical: \"resize-y\",\n      horizontal: \"resize-x\",\n      both: \"resize\",\n    };\n\n    const textareaClasses = cn(\n      baseClasses,\n      variantClasses[variant],\n      sizeClasses[inputSize],\n      roundedClasses[inputSize],\n      resizeClasses[resize],\n      fullWidth && \"w-full\",\n      className\n    );\n\n    return (\n      <div className={cn(\"relative\", fullWidth && \"w-full\")}>\n        {/* Label */}\n        {label && (\n          <motion.label\n            className={cn(\n              \"block text-sm font-medium mb-2 transition-colors\",\n              error ? \"text-red-700\" : \"text-gray-700\",\n              disabled && \"text-gray-400\"\n            )}\n            animate={{\n              color: isFocused\n                ? error\n                  ? \"#dc2626\"\n                  : \"#ec4899\"\n                : error\n                ? \"#dc2626\"\n                : \"#374151\",\n            }}\n          >\n            {label}\n          </motion.label>\n        )}\n\n        {/* Textarea */}\n        <motion.textarea\n          ref={ref}\n          className={textareaClasses}\n          disabled={disabled}\n          onFocus={(e) => {\n            setIsFocused(true);\n            props.onFocus?.(e);\n          }}\n          onBlur={(e) => {\n            setIsFocused(false);\n            props.onBlur?.(e);\n          }}\n          whileFocus={{ scale: 1.01 }}\n          {...(({\n            onAnimationStart,\n            onAnimationEnd,\n            onAnimationIteration,\n            onDrag,\n            onDragStart,\n            onDragEnd,\n            ...rest\n          }) => rest)(props)}\n        />\n\n        {/* Error Message */}\n        {error && (\n          <motion.p\n            className=\"mt-1 text-sm text-red-600\"\n            initial={{ opacity: 0, y: -10 }}\n            animate={{ opacity: 1, y: 0 }}\n            exit={{ opacity: 0, y: -10 }}\n          >\n            {error}\n          </motion.p>\n        )}\n\n        {/* Helper Text */}\n        {helperText && !error && (\n          <p className=\"mt-1 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nTextarea.displayName = \"Textarea\";\n\nexport { Input, Textarea };\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;;AAJA;;;;AAkBA,MAAM,sBAAQ,GAAA,4QAAA,CAAA,UAAK,CAAC,UAAU,SAC5B,CACE,EACE,SAAS,EACT,OAAO,MAAM,EACb,KAAK,EACL,KAAK,EACL,UAAU,EACV,QAAQ,EACR,SAAS,EACT,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,KAAK,EACjB,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,4QAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjD,MAAM,cAAc;QAClB;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA,QACI,8DACA;SACL;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA,QAAQ,oCAAoC;SAC7C;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA,QACI,8DACA;SACL;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACpB,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,UAAU,EACtB,cAAc,CAAC,UAAU,EACzB,YAAY,SACZ,aAAa,SACb,aAAa,UACb;IAGF,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,oBAAoB;QACxB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,aAAa;;YAEzC,uBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,KAAK;gBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,QAAQ,iBAAiB,iBACzB,YAAY;gBAEd,SAAS;oBACP,OAAO,YACH,QACE,YACA,YACF,QACA,YACA;gBACN;0BAEC;;;;;;0BAKL,4SAAC;gBAAI,WAAU;;oBAEZ,0BACC,4SAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,YAAY,CAAC,UAAU;kCAGzB,cAAA,4SAAC;4BAAK,WAAW,QAAQ,CAAC,UAAU;sCAAG;;;;;;;;;;;kCAK3C,4SAAC,mSAAA,CAAA,SAAM,CAAC,KAAK;wBACX,KAAK;wBACL,MAAM;wBACN,WAAW;wBACX,UAAU;wBACV,SAAS,CAAC;4BACR,aAAa;4BACb,MAAM,OAAO,GAAG;wBAClB;wBACA,QAAQ,CAAC;4BACP,aAAa;4BACb,MAAM,MAAM,GAAG;wBACjB;wBACA,YAAY;4BAAE,OAAO;wBAAK;wBACzB,GAAG,CAAC,CAAC,EACJ,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,MAAM,EACN,WAAW,EACX,SAAS,EACT,GAAG,MACJ,GAAK,IAAI,EAAE,MAAM;;;;;;oBAInB,2BACC,4SAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,iBAAiB,CAAC,UAAU;kCAG9B,cAAA,4SAAC;4BAAK,WAAW,QAAQ,CAAC,UAAU;sCAAG;;;;;;;;;;;;;;;;;YAM5C,uBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;0BAE1B;;;;;;YAKJ,cAAc,CAAC,uBACd,4SAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,MAAM,WAAW,GAAG;AAcpB,MAAM,yBAAW,IAAA,4QAAA,CAAA,UAAK,CAAC,UAAU,WAC/B,CACE,EACE,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,YAAY,IAAI,EAChB,YAAY,KAAK,EACjB,SAAS,UAAU,EACnB,QAAQ,EACR,GAAG,OACJ,EACD;;IAEA,MAAM,CAAC,WAAW,aAAa,GAAG,4QAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjD,MAAM,cAAc;QAClB;QACA;QACA;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS;YACP;YACA;YACA;YACA;YACA;YACA,QACI,8DACA;SACL;QACD,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA,QAAQ,oCAAoC;SAC7C;QACD,UAAU;YACR;YACA;YACA;YACA;YACA;YACA,QACI,8DACA;SACL;IACH;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,gBAAgB;QACpB,MAAM;QACN,UAAU;QACV,YAAY;QACZ,MAAM;IACR;IAEA,MAAM,kBAAkB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACvB,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,UAAU,EACtB,cAAc,CAAC,UAAU,EACzB,aAAa,CAAC,OAAO,EACrB,aAAa,UACb;IAGF,qBACE,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,YAAY,aAAa;;YAEzC,uBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,KAAK;gBACX,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,QAAQ,iBAAiB,iBACzB,YAAY;gBAEd,SAAS;oBACP,OAAO,YACH,QACE,YACA,YACF,QACA,YACA;gBACN;0BAEC;;;;;;0BAKL,4SAAC,mSAAA,CAAA,SAAM,CAAC,QAAQ;gBACd,KAAK;gBACL,WAAW;gBACX,UAAU;gBACV,SAAS,CAAC;oBACR,aAAa;oBACb,MAAM,OAAO,GAAG;gBAClB;gBACA,QAAQ,CAAC;oBACP,aAAa;oBACb,MAAM,MAAM,GAAG;gBACjB;gBACA,YAAY;oBAAE,OAAO;gBAAK;gBACzB,GAAG,CAAC,CAAC,EACJ,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,MAAM,EACN,WAAW,EACX,SAAS,EACT,GAAG,MACJ,GAAK,IAAI,EAAE,MAAM;;;;;;YAInB,uBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;gBAC9B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,MAAM;oBAAE,SAAS;oBAAG,GAAG,CAAC;gBAAG;0BAE1B;;;;;;YAKJ,cAAc,CAAC,uBACd,4SAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,SAAS,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2645, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Select.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React, { forwardRef } from \"react\";\n\nexport interface SelectProps\n  extends Omit<React.SelectHTMLAttributes<HTMLSelectElement>, \"size\"> {\n  label?: string;\n  error?: string;\n  helperText?: string;\n  variant?: \"default\" | \"outline\" | \"filled\";\n  size?: \"sm\" | \"md\" | \"lg\";\n}\n\nconst Select = forwardRef<HTMLSelectElement, SelectProps>(\n  (\n    {\n      className,\n      label,\n      error,\n      helperText,\n      variant = \"default\",\n      size = \"md\",\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses =\n      \"w-full rounded-lg border transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2\";\n\n    const variantClasses = {\n      default:\n        \"border-gray-300 bg-white focus:border-pink-500 focus:ring-pink-500\",\n      outline:\n        \"border-gray-300 bg-transparent focus:border-pink-500 focus:ring-pink-500\",\n      filled:\n        \"border-gray-200 bg-gray-50 focus:border-pink-500 focus:ring-pink-500\",\n    };\n\n    const sizeClasses = {\n      sm: \"px-3 py-2 text-sm\",\n      md: \"px-4 py-3 text-base\",\n      lg: \"px-5 py-4 text-lg\",\n    };\n\n    const errorClasses = error\n      ? \"border-red-500 focus:border-red-500 focus:ring-red-500\"\n      : \"\";\n\n    return (\n      <div className=\"w-full\">\n        {label && (\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            {label}\n          </label>\n        )}\n\n        <select\n          ref={ref}\n          className={cn(\n            baseClasses,\n            variantClasses[variant],\n            sizeClasses[size],\n            errorClasses,\n            className\n          )}\n          {...props}\n        >\n          {children}\n        </select>\n\n        {error && <p className=\"mt-2 text-sm text-red-600\">{error}</p>}\n\n        {helperText && !error && (\n          <p className=\"mt-2 text-sm text-gray-500\">{helperText}</p>\n        )}\n      </div>\n    );\n  }\n);\n\nSelect.displayName = \"Select\";\n\nexport { Select };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAcA,MAAM,uBAAS,CAAA,GAAA,4QAAA,CAAA,aAAU,AAAD,OACtB,CACE,EACE,SAAS,EACT,KAAK,EACL,KAAK,EACL,UAAU,EACV,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cACJ;IAEF,MAAM,iBAAiB;QACrB,SACE;QACF,SACE;QACF,QACE;IACJ;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe,QACjB,2DACA;IAEJ,qBACE,4SAAC;QAAI,WAAU;;YACZ,uBACC,4SAAC;gBAAM,WAAU;0BACd;;;;;;0BAIL,4SAAC;gBACC,KAAK;gBACL,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,cACA;gBAED,GAAG,KAAK;0BAER;;;;;;YAGF,uBAAS,4SAAC;gBAAE,WAAU;0BAA6B;;;;;;YAEnD,cAAc,CAAC,uBACd,4SAAC;gBAAE,WAAU;0BAA8B;;;;;;;;;;;;AAInD;;AAGF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 2727, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Badge.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n// Animations removed - using inline animations\n\nexport interface BadgeProps extends React.HTMLAttributes<HTMLDivElement> {\n  variant?:\n    | \"default\"\n    | \"primary\"\n    | \"secondary\"\n    | \"success\"\n    | \"warning\"\n    | \"danger\"\n    | \"info\";\n  size?: \"sm\" | \"md\" | \"lg\";\n  rounded?: boolean;\n  outline?: boolean;\n  icon?: React.ReactNode;\n  iconPosition?: \"left\" | \"right\";\n  removable?: boolean;\n  onRemove?: () => void;\n}\n\nconst Badge = React.forwardRef<HTMLDivElement, BadgeProps>(\n  (\n    {\n      className,\n      variant = \"default\",\n      size = \"md\",\n      rounded = false,\n      outline = false,\n      icon,\n      iconPosition = \"left\",\n      removable = false,\n      onRemove,\n      children,\n      ...props\n    },\n    ref\n  ) => {\n    const baseClasses = [\n      \"inline-flex items-center font-medium transition-all duration-200\",\n      \"whitespace-nowrap\",\n    ];\n\n    const variantClasses = {\n      default: outline\n        ? \"border border-gray-300 text-gray-700 bg-transparent hover:bg-gray-50\"\n        : \"bg-gray-100 text-gray-800 hover:bg-gray-200\",\n      primary: outline\n        ? \"border border-pink-300 text-pink-700 bg-transparent hover:bg-pink-50\"\n        : \"bg-gradient-to-r from-pink-500 to-orange-400 text-white hover:from-pink-600 hover:to-orange-500\",\n      secondary: outline\n        ? \"border border-purple-300 text-purple-700 bg-transparent hover:bg-purple-50\"\n        : \"bg-gradient-to-r from-purple-500 to-blue-500 text-white hover:from-purple-600 hover:to-blue-600\",\n      success: outline\n        ? \"border border-green-300 text-green-700 bg-transparent hover:bg-green-50\"\n        : \"bg-green-100 text-green-800 hover:bg-green-200\",\n      warning: outline\n        ? \"border border-yellow-300 text-yellow-700 bg-transparent hover:bg-yellow-50\"\n        : \"bg-yellow-100 text-yellow-800 hover:bg-yellow-200\",\n      danger: outline\n        ? \"border border-red-300 text-red-700 bg-transparent hover:bg-red-50\"\n        : \"bg-red-100 text-red-800 hover:bg-red-200\",\n      info: outline\n        ? \"border border-blue-300 text-blue-700 bg-transparent hover:bg-blue-50\"\n        : \"bg-blue-100 text-blue-800 hover:bg-blue-200\",\n    };\n\n    const sizeClasses = {\n      sm: \"px-2 py-0.5 text-xs\",\n      md: \"px-2.5 py-1 text-sm\",\n      lg: \"px-3 py-1.5 text-base\",\n    };\n\n    const roundedClasses = {\n      sm: rounded ? \"rounded-full\" : \"rounded\",\n      md: rounded ? \"rounded-full\" : \"rounded-md\",\n      lg: rounded ? \"rounded-full\" : \"rounded-lg\",\n    };\n\n    const iconSize = {\n      sm: \"w-3 h-3\",\n      md: \"w-4 h-4\",\n      lg: \"w-5 h-5\",\n    };\n\n    const classes = cn(\n      baseClasses,\n      variantClasses[variant],\n      sizeClasses[size],\n      roundedClasses[size],\n      className\n    );\n\n    const renderIcon = (position: \"left\" | \"right\") => {\n      if (!icon || iconPosition !== position) return null;\n\n      return (\n        <span\n          className={cn(\n            iconSize[size],\n            position === \"left\" && children && \"mr-1\",\n            position === \"right\" && children && \"ml-1\"\n          )}\n        >\n          {icon}\n        </span>\n      );\n    };\n\n    const renderRemoveButton = () => {\n      if (!removable) return null;\n\n      return (\n        <button\n          onClick={onRemove}\n          className={cn(\n            \"ml-1 hover:bg-black/10 rounded-full p-0.5 transition-colors\",\n            iconSize[size]\n          )}\n          aria-label=\"Supprimer\"\n        >\n          <svg\n            className=\"w-full h-full\"\n            fill=\"none\"\n            stroke=\"currentColor\"\n            viewBox=\"0 0 24 24\"\n          >\n            <path\n              strokeLinecap=\"round\"\n              strokeLinejoin=\"round\"\n              strokeWidth={2}\n              d=\"M6 18L18 6M6 6l12 12\"\n            />\n          </svg>\n        </button>\n      );\n    };\n\n    const {\n      onAnimationStart,\n      onAnimationEnd,\n      onAnimationIteration,\n      onDrag,\n      onDragStart,\n      onDragEnd,\n      ...divProps\n    } = props;\n\n    return (\n      <motion.div\n        ref={ref}\n        className={classes}\n        initial={{ opacity: 0, scale: 0.8 }}\n        animate={{ opacity: 1, scale: 1 }}\n        whileHover={{ scale: 1.05 }}\n        whileTap={{ scale: 0.95 }}\n        {...divProps}\n      >\n        {renderIcon(\"left\")}\n        {children}\n        {renderIcon(\"right\")}\n        {renderRemoveButton()}\n      </motion.div>\n    );\n  }\n);\n\nBadge.displayName = \"Badge\";\n\n// Badge spécialisé pour les statuts de commande\nexport interface OrderStatusBadgeProps {\n  status:\n    | \"PENDING\"\n    | \"PROCESSING\"\n    | \"PAID\"\n    | \"SHIPPED\"\n    | \"DELIVERED\"\n    | \"CANCELLED\"\n    | \"FAILED\";\n  size?: \"sm\" | \"md\" | \"lg\";\n}\n\nconst OrderStatusBadge: React.FC<OrderStatusBadgeProps> = ({\n  status,\n  size = \"md\",\n}) => {\n  const statusConfig = {\n    PENDING: {\n      variant: \"warning\" as const,\n      label: \"En attente\",\n      icon: \"⏳\",\n    },\n    PROCESSING: {\n      variant: \"info\" as const,\n      label: \"En cours\",\n      icon: \"⚙️\",\n    },\n    PAID: {\n      variant: \"success\" as const,\n      label: \"Payé\",\n      icon: \"✅\",\n    },\n    SHIPPED: {\n      variant: \"primary\" as const,\n      label: \"Expédié\",\n      icon: \"📦\",\n    },\n    DELIVERED: {\n      variant: \"success\" as const,\n      label: \"Livré\",\n      icon: \"🎉\",\n    },\n    CANCELLED: {\n      variant: \"danger\" as const,\n      label: \"Annulé\",\n      icon: \"❌\",\n    },\n    FAILED: {\n      variant: \"danger\" as const,\n      label: \"Échoué\",\n      icon: \"⚠️\",\n    },\n  };\n\n  const config = statusConfig[status];\n\n  return (\n    <Badge\n      variant={config.variant}\n      size={size}\n      icon={<span>{config.icon}</span>}\n      rounded\n    >\n      {config.label}\n    </Badge>\n  );\n};\n\n// Badge spécialisé pour les niveaux de fidélité\nexport interface LoyaltyBadgeProps {\n  level: \"BRONZE\" | \"SILVER\" | \"GOLD\" | \"PLATINUM\";\n  size?: \"sm\" | \"md\" | \"lg\";\n}\n\nconst LoyaltyBadge: React.FC<LoyaltyBadgeProps> = ({ level, size = \"md\" }) => {\n  const levelConfig = {\n    BRONZE: {\n      variant: \"warning\" as const,\n      label: \"Bronze\",\n      icon: \"🥉\",\n    },\n    SILVER: {\n      variant: \"default\" as const,\n      label: \"Argent\",\n      icon: \"🥈\",\n    },\n    GOLD: {\n      variant: \"warning\" as const,\n      label: \"Or\",\n      icon: \"🥇\",\n    },\n    PLATINUM: {\n      variant: \"primary\" as const,\n      label: \"Platine\",\n      icon: \"💎\",\n    },\n  };\n\n  const config = levelConfig[level];\n\n  return (\n    <Badge\n      variant={config.variant}\n      size={size}\n      icon={<span>{config.icon}</span>}\n      rounded\n    >\n      {config.label}\n    </Badge>\n  );\n};\n\nexport { Badge, LoyaltyBadge, OrderStatusBadge };\n"], "names": [], "mappings": ";;;;;;AAEA;AACA;AACA;AAJA;;;;;AAyBA,MAAM,sBAAQ,4QAAA,CAAA,UAAK,CAAC,UAAU,CAC5B,CACE,EACE,SAAS,EACT,UAAU,SAAS,EACnB,OAAO,IAAI,EACX,UAAU,KAAK,EACf,UAAU,KAAK,EACf,IAAI,EACJ,eAAe,MAAM,EACrB,YAAY,KAAK,EACjB,QAAQ,EACR,QAAQ,EACR,GAAG,OACJ,EACD;IAEA,MAAM,cAAc;QAClB;QACA;KACD;IAED,MAAM,iBAAiB;QACrB,SAAS,UACL,yEACA;QACJ,SAAS,UACL,yEACA;QACJ,WAAW,UACP,+EACA;QACJ,SAAS,UACL,4EACA;QACJ,SAAS,UACL,+EACA;QACJ,QAAQ,UACJ,sEACA;QACJ,MAAM,UACF,yEACA;IACN;IAEA,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,iBAAiB;QACrB,IAAI,UAAU,iBAAiB;QAC/B,IAAI,UAAU,iBAAiB;QAC/B,IAAI,UAAU,iBAAiB;IACjC;IAEA,MAAM,WAAW;QACf,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,UAAU,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACf,aACA,cAAc,CAAC,QAAQ,EACvB,WAAW,CAAC,KAAK,EACjB,cAAc,CAAC,KAAK,EACpB;IAGF,MAAM,aAAa,CAAC;QAClB,IAAI,CAAC,QAAQ,iBAAiB,UAAU,OAAO;QAE/C,qBACE,4SAAC;YACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,QAAQ,CAAC,KAAK,EACd,aAAa,UAAU,YAAY,QACnC,aAAa,WAAW,YAAY;sBAGrC;;;;;;IAGP;IAEA,MAAM,qBAAqB;QACzB,IAAI,CAAC,WAAW,OAAO;QAEvB,qBACE,4SAAC;YACC,SAAS;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,QAAQ,CAAC,KAAK;YAEhB,cAAW;sBAEX,cAAA,4SAAC;gBACC,WAAU;gBACV,MAAK;gBACL,QAAO;gBACP,SAAQ;0BAER,cAAA,4SAAC;oBACC,eAAc;oBACd,gBAAe;oBACf,aAAa;oBACb,GAAE;;;;;;;;;;;;;;;;IAKZ;IAEA,MAAM,EACJ,gBAAgB,EAChB,cAAc,EACd,oBAAoB,EACpB,MAAM,EACN,WAAW,EACX,SAAS,EACT,GAAG,UACJ,GAAG;IAEJ,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,KAAK;QACL,WAAW;QACX,SAAS;YAAE,SAAS;YAAG,OAAO;QAAI;QAClC,SAAS;YAAE,SAAS;YAAG,OAAO;QAAE;QAChC,YAAY;YAAE,OAAO;QAAK;QAC1B,UAAU;YAAE,OAAO;QAAK;QACvB,GAAG,QAAQ;;YAEX,WAAW;YACX;YACA,WAAW;YACX;;;;;;;AAGP;KA/II;AAkJN,MAAM,WAAW,GAAG;AAepB,MAAM,mBAAoD,CAAC,EACzD,MAAM,EACN,OAAO,IAAI,EACZ;IACC,MAAM,eAAe;QACnB,SAAS;YACP,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,YAAY;YACV,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,SAAS;YACP,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,WAAW;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,WAAW;YACT,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,QAAQ;YACN,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,SAAS,YAAY,CAAC,OAAO;IAEnC,qBACE,4SAAC;QACC,SAAS,OAAO,OAAO;QACvB,MAAM;QACN,oBAAM,4SAAC;sBAAM,OAAO,IAAI;;;;;;QACxB,OAAO;kBAEN,OAAO,KAAK;;;;;;AAGnB;MAtDM;AA8DN,MAAM,eAA4C,CAAC,EAAE,KAAK,EAAE,OAAO,IAAI,EAAE;IACvE,MAAM,cAAc;QAClB,QAAQ;YACN,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,QAAQ;YACN,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,OAAO;YACP,MAAM;QACR;QACA,UAAU;YACR,SAAS;YACT,OAAO;YACP,MAAM;QACR;IACF;IAEA,MAAM,SAAS,WAAW,CAAC,MAAM;IAEjC,qBACE,4SAAC;QACC,SAAS,OAAO,OAAO;QACvB,MAAM;QACN,oBAAM,4SAAC;sBAAM,OAAO,IAAI;;;;;;QACxB,OAAO;kBAEN,OAAO,KAAK;;;;;;AAGnB;MApCM", "debugId": null}}, {"offset": {"line": 2962, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Loading.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { cn } from '@/lib/utils'\n\nexport interface LoadingProps {\n  size?: 'sm' | 'md' | 'lg' | 'xl'\n  variant?: 'spinner' | 'dots' | 'pulse' | 'candy'\n  color?: 'primary' | 'secondary' | 'white' | 'gray'\n  text?: string\n  fullScreen?: boolean\n  className?: string\n}\n\nconst Loading: React.FC<LoadingProps> = ({\n  size = 'md',\n  variant = 'spinner',\n  color = 'primary',\n  text,\n  fullScreen = false,\n  className\n}) => {\n  const sizeClasses = {\n    sm: 'w-4 h-4',\n    md: 'w-8 h-8',\n    lg: 'w-12 h-12',\n    xl: 'w-16 h-16'\n  }\n\n  const colorClasses = {\n    primary: 'text-pink-500',\n    secondary: 'text-purple-500',\n    white: 'text-white',\n    gray: 'text-gray-500'\n  }\n\n  const textSizeClasses = {\n    sm: 'text-sm',\n    md: 'text-base',\n    lg: 'text-lg',\n    xl: 'text-xl'\n  }\n\n  const containerClasses = cn(\n    'flex flex-col items-center justify-center',\n    fullScreen && 'fixed inset-0 bg-white/80 backdrop-blur-sm z-50',\n    className\n  )\n\n  const renderSpinner = () => (\n    <motion.div\n      className={cn(\n        'border-2 border-current border-t-transparent rounded-full',\n        sizeClasses[size],\n        colorClasses[color]\n      )}\n      animate={{ rotate: 360 }}\n      transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}\n    />\n  )\n\n  const renderDots = () => (\n    <div className=\"flex space-x-1\">\n      {[0, 1, 2].map((i) => (\n        <motion.div\n          key={i}\n          className={cn(\n            'rounded-full',\n            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : size === 'lg' ? 'w-4 h-4' : 'w-5 h-5',\n            colorClasses[color] === 'text-white' ? 'bg-white' : \n            colorClasses[color] === 'text-gray-500' ? 'bg-gray-500' :\n            colorClasses[color] === 'text-purple-500' ? 'bg-purple-500' : 'bg-pink-500'\n          )}\n          animate={{\n            scale: [1, 1.2, 1],\n            opacity: [0.7, 1, 0.7]\n          }}\n          transition={{\n            duration: 0.8,\n            repeat: Infinity,\n            delay: i * 0.2\n          }}\n        />\n      ))}\n    </div>\n  )\n\n  const renderPulse = () => (\n    <motion.div\n      className={cn(\n        'rounded-full',\n        sizeClasses[size],\n        colorClasses[color] === 'text-white' ? 'bg-white' : \n        colorClasses[color] === 'text-gray-500' ? 'bg-gray-500' :\n        colorClasses[color] === 'text-purple-500' ? 'bg-purple-500' : 'bg-pink-500'\n      )}\n      animate={{\n        scale: [1, 1.2, 1],\n        opacity: [0.7, 1, 0.7]\n      }}\n      transition={{\n        duration: 1.5,\n        repeat: Infinity\n      }}\n    />\n  )\n\n  const renderCandy = () => (\n    <motion.div\n      className={cn(\n        'text-4xl',\n        size === 'sm' && 'text-2xl',\n        size === 'md' && 'text-4xl',\n        size === 'lg' && 'text-6xl',\n        size === 'xl' && 'text-8xl'\n      )}\n      animate={{\n        rotate: [0, 360],\n        scale: [1, 1.1, 1]\n      }}\n      transition={{\n        duration: 2,\n        repeat: Infinity,\n        ease: 'easeInOut'\n      }}\n    >\n      🍭\n    </motion.div>\n  )\n\n  const renderLoader = () => {\n    switch (variant) {\n      case 'dots':\n        return renderDots()\n      case 'pulse':\n        return renderPulse()\n      case 'candy':\n        return renderCandy()\n      default:\n        return renderSpinner()\n    }\n  }\n\n  return (\n    <div className={containerClasses}>\n      {renderLoader()}\n      {text && (\n        <motion.p\n          className={cn(\n            'mt-3 font-medium',\n            textSizeClasses[size],\n            colorClasses[color]\n          )}\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          transition={{ delay: 0.2 }}\n        >\n          {text}\n        </motion.p>\n      )}\n    </div>\n  )\n}\n\n// Composant de loading pour les pages entières\nexport const PageLoading: React.FC<{ text?: string }> = ({ text = 'Chargement...' }) => (\n  <Loading\n    variant=\"candy\"\n    size=\"lg\"\n    text={text}\n    fullScreen\n  />\n)\n\n// Composant de loading pour les boutons\nexport const ButtonLoading: React.FC<{ size?: 'sm' | 'md' | 'lg' }> = ({ size = 'md' }) => (\n  <Loading\n    variant=\"spinner\"\n    size={size}\n    color=\"white\"\n  />\n)\n\nexport { Loading }\n"], "names": [], "mappings": ";;;;;;AAGA;AACA;AAJA;;;;AAeA,MAAM,UAAkC,CAAC,EACvC,OAAO,IAAI,EACX,UAAU,SAAS,EACnB,QAAQ,SAAS,EACjB,IAAI,EACJ,aAAa,KAAK,EAClB,SAAS,EACV;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,eAAe;QACnB,SAAS;QACT,WAAW;QACX,OAAO;QACP,MAAM;IACR;IAEA,MAAM,kBAAkB;QACtB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,mBAAmB,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACxB,6CACA,cAAc,mDACd;IAGF,MAAM,gBAAgB,kBACpB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6DACA,WAAW,CAAC,KAAK,EACjB,YAAY,CAAC,MAAM;YAErB,SAAS;gBAAE,QAAQ;YAAI;YACvB,YAAY;gBAAE,UAAU;gBAAG,QAAQ;gBAAU,MAAM;YAAS;;;;;;IAIhE,MAAM,aAAa,kBACjB,4SAAC;YAAI,WAAU;sBACZ;gBAAC;gBAAG;gBAAG;aAAE,CAAC,GAAG,CAAC,CAAC,kBACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,SAAS,OAAO,YAAY,WACpF,YAAY,CAAC,MAAM,KAAK,eAAe,aACvC,YAAY,CAAC,MAAM,KAAK,kBAAkB,gBAC1C,YAAY,CAAC,MAAM,KAAK,oBAAoB,kBAAkB;oBAEhE,SAAS;wBACP,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,SAAS;4BAAC;4BAAK;4BAAG;yBAAI;oBACxB;oBACA,YAAY;wBACV,UAAU;wBACV,QAAQ;wBACR,OAAO,IAAI;oBACb;mBAhBK;;;;;;;;;;IAsBb,MAAM,cAAc,kBAClB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gBACA,WAAW,CAAC,KAAK,EACjB,YAAY,CAAC,MAAM,KAAK,eAAe,aACvC,YAAY,CAAC,MAAM,KAAK,kBAAkB,gBAC1C,YAAY,CAAC,MAAM,KAAK,oBAAoB,kBAAkB;YAEhE,SAAS;gBACP,OAAO;oBAAC;oBAAG;oBAAK;iBAAE;gBAClB,SAAS;oBAAC;oBAAK;oBAAG;iBAAI;YACxB;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ;YACV;;;;;;IAIJ,MAAM,cAAc,kBAClB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,YACA,SAAS,QAAQ,YACjB,SAAS,QAAQ,YACjB,SAAS,QAAQ,YACjB,SAAS,QAAQ;YAEnB,SAAS;gBACP,QAAQ;oBAAC;oBAAG;iBAAI;gBAChB,OAAO;oBAAC;oBAAG;oBAAK;iBAAE;YACpB;YACA,YAAY;gBACV,UAAU;gBACV,QAAQ;gBACR,MAAM;YACR;sBACD;;;;;;IAKH,MAAM,eAAe;QACnB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,4SAAC;QAAI,WAAW;;YACb;YACA,sBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gBACP,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oBACA,eAAe,CAAC,KAAK,EACrB,YAAY,CAAC,MAAM;gBAErB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAExB;;;;;;;;;;;;AAKX;KApJM;AAuJC,MAAM,cAA2C,CAAC,EAAE,OAAO,eAAe,EAAE,iBACjF,4SAAC;QACC,SAAQ;QACR,MAAK;QACL,MAAM;QACN,UAAU;;;;;;MALD;AAUN,MAAM,gBAAyD,CAAC,EAAE,OAAO,IAAI,EAAE,iBACpF,4SAAC;QACC,SAAQ;QACR,MAAM;QACN,OAAM;;;;;;MAJG", "debugId": null}}, {"offset": {"line": 3167, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/auth-store.ts"], "sourcesContent": ["import { create } from \"zustand\";\nimport { persist } from \"zustand/middleware\";\n\ninterface User {\n  id: string;\n  email: string;\n  firstName: string;\n  lastName: string;\n  phone?: string;\n  address?: string;\n  postalCode?: string;\n  city?: string;\n  role: \"USER\" | \"ADMIN\";\n  createdAt?: string;\n}\n\ninterface AuthState {\n  user: User | null;\n  isAuthenticated: boolean;\n  isLoading: boolean;\n\n  // Actions\n  login: (email: string, password: string) => Promise<void>;\n  register: (userData: {\n    email: string;\n    password: string;\n    firstName: string;\n    lastName: string;\n    phone: string;\n    address: string;\n    postalCode: string;\n    city: string;\n  }) => Promise<void>;\n  logout: () => Promise<void>;\n  checkAuth: () => Promise<void>;\n  setUser: (user: User | null) => void;\n  setLoading: (loading: boolean) => void;\n  isAdmin: () => boolean;\n}\n\nexport const useAuth = create<AuthState>()(\n  persist(\n    (set, get) => ({\n      user: null,\n      isAuthenticated: false,\n      isLoading: false,\n\n      login: async (email: string, password: string) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch(\"/api/auth/login\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            credentials: \"include\", // Important pour les cookies\n            body: JSON.stringify({ email, password }),\n          });\n\n          const data = await response.json();\n\n          if (!response.ok) {\n            throw new Error(data.error || \"Erreur de connexion\");\n          }\n\n          set({\n            user: data.user,\n            isAuthenticated: true,\n            isLoading: false,\n          });\n        } catch (error) {\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      register: async (userData) => {\n        set({ isLoading: true });\n        try {\n          const response = await fetch(\"/api/auth/register\", {\n            method: \"POST\",\n            headers: {\n              \"Content-Type\": \"application/json\",\n            },\n            body: JSON.stringify(userData),\n          });\n\n          const data = await response.json();\n\n          if (!response.ok) {\n            throw new Error(data.error || \"Erreur d'inscription\");\n          }\n\n          set({\n            user: data.user,\n            isAuthenticated: true,\n            isLoading: false,\n          });\n        } catch (error) {\n          set({ isLoading: false });\n          throw error;\n        }\n      },\n\n      logout: async () => {\n        try {\n          await fetch(\"/api/auth/logout\", {\n            method: \"POST\",\n          });\n\n          set({\n            user: null,\n            isAuthenticated: false,\n          });\n        } catch (error) {\n          console.error(\"Erreur lors de la déconnexion:\", error);\n          // Même en cas d'erreur, on déconnecte localement\n          set({\n            user: null,\n            isAuthenticated: false,\n          });\n        }\n      },\n\n      checkAuth: async () => {\n        try {\n          const response = await fetch(\"/api/auth/me\", {\n            credentials: \"include\", // Important pour inclure les cookies\n          });\n\n          if (response.ok) {\n            const data = await response.json();\n            set({\n              user: data.user,\n              isAuthenticated: true,\n            });\n          } else {\n            set({\n              user: null,\n              isAuthenticated: false,\n            });\n          }\n        } catch (error) {\n          console.error(\n            \"Erreur lors de la vérification de l'authentification:\",\n            error\n          );\n          set({\n            user: null,\n            isAuthenticated: false,\n          });\n        }\n      },\n\n      setUser: (user) => {\n        set({\n          user,\n          isAuthenticated: !!user,\n        });\n      },\n\n      setLoading: (loading) => {\n        set({ isLoading: loading });\n      },\n\n      isAdmin: () => {\n        const { user } = get();\n        return user?.role === \"ADMIN\";\n      },\n    }),\n    {\n      name: \"auth-storage\",\n      partialize: (state) => ({\n        user: state.user,\n        isAuthenticated: state.isAuthenticated,\n      }),\n    }\n  )\n);\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAuCO,MAAM,UAAU,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,IAC1B,CAAA,GAAA,4PAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,iBAAiB;QACjB,WAAW;QAEX,OAAO,OAAO,OAAe;YAC3B,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,mBAAmB;oBAC9C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,aAAa;oBACb,MAAM,KAAK,SAAS,CAAC;wBAAE;wBAAO;oBAAS;gBACzC;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,UAAU,OAAO;YACf,IAAI;gBAAE,WAAW;YAAK;YACtB,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,sBAAsB;oBACjD,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;gBACvB;gBAEA,MAAM,OAAO,MAAM,SAAS,IAAI;gBAEhC,IAAI,CAAC,SAAS,EAAE,EAAE;oBAChB,MAAM,IAAI,MAAM,KAAK,KAAK,IAAI;gBAChC;gBAEA,IAAI;oBACF,MAAM,KAAK,IAAI;oBACf,iBAAiB;oBACjB,WAAW;gBACb;YACF,EAAE,OAAO,OAAO;gBACd,IAAI;oBAAE,WAAW;gBAAM;gBACvB,MAAM;YACR;QACF;QAEA,QAAQ;YACN,IAAI;gBACF,MAAM,MAAM,oBAAoB;oBAC9B,QAAQ;gBACV;gBAEA,IAAI;oBACF,MAAM;oBACN,iBAAiB;gBACnB;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kCAAkC;gBAChD,iDAAiD;gBACjD,IAAI;oBACF,MAAM;oBACN,iBAAiB;gBACnB;YACF;QACF;QAEA,WAAW;YACT,IAAI;gBACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;oBAC3C,aAAa;gBACf;gBAEA,IAAI,SAAS,EAAE,EAAE;oBACf,MAAM,OAAO,MAAM,SAAS,IAAI;oBAChC,IAAI;wBACF,MAAM,KAAK,IAAI;wBACf,iBAAiB;oBACnB;gBACF,OAAO;oBACL,IAAI;wBACF,MAAM;wBACN,iBAAiB;oBACnB;gBACF;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CACX,yDACA;gBAEF,IAAI;oBACF,MAAM;oBACN,iBAAiB;gBACnB;YACF;QACF;QAEA,SAAS,CAAC;YACR,IAAI;gBACF;gBACA,iBAAiB,CAAC,CAAC;YACrB;QACF;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,SAAS;YACP,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,OAAO,MAAM,SAAS;QACxB;IACF,CAAC,GACD;IACE,MAAM;IACN,YAAY,CAAC,QAAU,CAAC;YACtB,MAAM,MAAM,IAAI;YAChB,iBAAiB,MAAM,eAAe;QACxC,CAAC;AACH", "debugId": null}}, {"offset": {"line": 3312, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/checkout-modal-store.ts"], "sourcesContent": ["\"use client\";\n\nimport { create } from \"zustand\";\n\ninterface CheckoutModalState {\n  isOpen: boolean;\n  openModal: () => void;\n  closeModal: () => void;\n}\n\nexport const useCheckoutModal = create<CheckoutModalState>((set) => ({\n  isOpen: false,\n  openModal: () => set({ isOpen: true }),\n  closeModal: () => set({ isOpen: false }),\n}));\n"], "names": [], "mappings": ";;;AAEA;AAFA;;AAUO,MAAM,mBAAmB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EAAsB,CAAC,MAAQ,CAAC;QACnE,QAAQ;QACR,WAAW,IAAM,IAAI;gBAAE,QAAQ;YAAK;QACpC,YAAY,IAAM,IAAI;gBAAE,QAAQ;YAAM;IACxC,CAAC", "debugId": null}}, {"offset": {"line": 3336, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/notification-store.ts"], "sourcesContent": ["import { create } from \"zustand\";\n\nexport interface Notification {\n  id: string;\n  type: \"success\" | \"error\" | \"warning\" | \"info\";\n  title: string;\n  message?: string;\n  duration?: number;\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n}\n\ninterface NotificationStore {\n  notifications: Notification[];\n  addNotification: (notification: Omit<Notification, \"id\">) => void;\n  removeNotification: (id: string) => void;\n  clearNotifications: () => void;\n}\n\nexport const useNotificationStore = create<NotificationStore>((set, get) => ({\n  notifications: [],\n\n  addNotification: (notification) => {\n    const id = `notification-${Date.now()}-${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n    const newNotification: Notification = {\n      ...notification,\n      id,\n      duration: notification.duration || 5000,\n    };\n\n    set((state) => ({\n      notifications: [...state.notifications, newNotification],\n    }));\n\n    // Auto-remove après la durée spécifiée\n    if (newNotification.duration && newNotification.duration > 0) {\n      setTimeout(() => {\n        get().removeNotification(id);\n      }, newNotification.duration);\n    }\n  },\n\n  removeNotification: (id) => {\n    set((state) => ({\n      notifications: state.notifications.filter(\n        (notification) => notification.id !== id\n      ),\n    }));\n  },\n\n  clearNotifications: () => {\n    set({ notifications: [] });\n  },\n}));\n\n// Hook personnalisé pour les notifications\nexport const useNotifications = () => {\n  const {\n    notifications,\n    addNotification,\n    removeNotification,\n    clearNotifications,\n  } = useNotificationStore();\n\n  const showSuccess = (\n    title: string,\n    message?: string,\n    options?: Partial<Notification>\n  ) => {\n    addNotification({ type: \"success\", title, message, ...options });\n  };\n\n  const showError = (\n    title: string,\n    message?: string,\n    options?: Partial<Notification>\n  ) => {\n    addNotification({ type: \"error\", title, message, ...options });\n  };\n\n  const showWarning = (\n    title: string,\n    message?: string,\n    options?: Partial<Notification>\n  ) => {\n    addNotification({ type: \"warning\", title, message, ...options });\n  };\n\n  const showInfo = (\n    title: string,\n    message?: string,\n    options?: Partial<Notification>\n  ) => {\n    addNotification({ type: \"info\", title, message, ...options });\n  };\n\n  return {\n    notifications,\n    addNotification,\n    removeNotification,\n    clearNotifications,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo,\n  };\n};\n"], "names": [], "mappings": ";;;;AAAA;;;AAqBO,MAAM,uBAAuB,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EAAqB,CAAC,KAAK,MAAQ,CAAC;QAC3E,eAAe,EAAE;QAEjB,iBAAiB,CAAC;YAChB,MAAM,KAAK,CAAC,aAAa,EAAE,KAAK,GAAG,GAAG,CAAC,EAAE,KAAK,MAAM,GACjD,QAAQ,CAAC,IACT,MAAM,CAAC,GAAG,IAAI;YACjB,MAAM,kBAAgC;gBACpC,GAAG,YAAY;gBACf;gBACA,UAAU,aAAa,QAAQ,IAAI;YACrC;YAEA,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe;2BAAI,MAAM,aAAa;wBAAE;qBAAgB;gBAC1D,CAAC;YAED,uCAAuC;YACvC,IAAI,gBAAgB,QAAQ,IAAI,gBAAgB,QAAQ,GAAG,GAAG;gBAC5D,WAAW;oBACT,MAAM,kBAAkB,CAAC;gBAC3B,GAAG,gBAAgB,QAAQ;YAC7B;QACF;QAEA,oBAAoB,CAAC;YACnB,IAAI,CAAC,QAAU,CAAC;oBACd,eAAe,MAAM,aAAa,CAAC,MAAM,CACvC,CAAC,eAAiB,aAAa,EAAE,KAAK;gBAE1C,CAAC;QACH;QAEA,oBAAoB;YAClB,IAAI;gBAAE,eAAe,EAAE;YAAC;QAC1B;IACF,CAAC;AAGM,MAAM,mBAAmB;;IAC9B,MAAM,EACJ,aAAa,EACb,eAAe,EACf,kBAAkB,EAClB,kBAAkB,EACnB,GAAG;IAEJ,MAAM,cAAc,CAClB,OACA,SACA;QAEA,gBAAgB;YAAE,MAAM;YAAW;YAAO;YAAS,GAAG,OAAO;QAAC;IAChE;IAEA,MAAM,YAAY,CAChB,OACA,SACA;QAEA,gBAAgB;YAAE,MAAM;YAAS;YAAO;YAAS,GAAG,OAAO;QAAC;IAC9D;IAEA,MAAM,cAAc,CAClB,OACA,SACA;QAEA,gBAAgB;YAAE,MAAM;YAAW;YAAO;YAAS,GAAG,OAAO;QAAC;IAChE;IAEA,MAAM,WAAW,CACf,OACA,SACA;QAEA,gBAAgB;YAAE,MAAM;YAAQ;YAAO;YAAS,GAAG,OAAO;QAAC;IAC7D;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GAlDa;;QAMP", "debugId": null}}, {"offset": {"line": 3436, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/ui-store.ts"], "sourcesContent": ["import { create } from 'zustand'\n\ninterface UIStore {\n  // États des modales\n  isCartOpen: boolean\n  isMenuOpen: boolean\n  isCheckoutOpen: boolean\n  isAuthModalOpen: boolean\n  \n  // États de chargement\n  isLoading: boolean\n  loadingMessage: string\n  \n  // États de l'interface\n  isMobile: boolean\n  scrollY: number\n  \n  // Actions pour les modales\n  openCart: () => void\n  closeCart: () => void\n  toggleCart: () => void\n  \n  openMenu: () => void\n  closeMenu: () => void\n  toggleMenu: () => void\n  \n  openCheckout: () => void\n  closeCheckout: () => void\n  \n  openAuthModal: () => void\n  closeAuthModal: () => void\n  \n  // Actions pour le chargement\n  setLoading: (loading: boolean, message?: string) => void\n  \n  // Actions pour l'interface\n  setIsMobile: (isMobile: boolean) => void\n  setScrollY: (scrollY: number) => void\n  \n  // Action pour fermer toutes les modales\n  closeAllModals: () => void\n}\n\nexport const useUIStore = create<UIStore>((set) => ({\n  // États initiaux\n  isCartOpen: false,\n  isMenuOpen: false,\n  isCheckoutOpen: false,\n  isAuthModalOpen: false,\n  isLoading: false,\n  loadingMessage: '',\n  isMobile: false,\n  scrollY: 0,\n\n  // Actions pour le panier\n  openCart: () => set({ isCartOpen: true }),\n  closeCart: () => set({ isCartOpen: false }),\n  toggleCart: () => set((state) => ({ isCartOpen: !state.isCartOpen })),\n\n  // Actions pour le menu\n  openMenu: () => set({ isMenuOpen: true }),\n  closeMenu: () => set({ isMenuOpen: false }),\n  toggleMenu: () => set((state) => ({ isMenuOpen: !state.isMenuOpen })),\n\n  // Actions pour le checkout\n  openCheckout: () => set({ isCheckoutOpen: true, isCartOpen: false }),\n  closeCheckout: () => set({ isCheckoutOpen: false }),\n\n  // Actions pour l'authentification\n  openAuthModal: () => set({ isAuthModalOpen: true }),\n  closeAuthModal: () => set({ isAuthModalOpen: false }),\n\n  // Actions pour le chargement\n  setLoading: (loading, message = '') => set({ isLoading: loading, loadingMessage: message }),\n\n  // Actions pour l'interface\n  setIsMobile: (isMobile) => set({ isMobile }),\n  setScrollY: (scrollY) => set({ scrollY }),\n\n  // Fermer toutes les modales\n  closeAllModals: () => set({\n    isCartOpen: false,\n    isMenuOpen: false,\n    isCheckoutOpen: false,\n    isAuthModalOpen: false,\n  }),\n}))\n\n// Hook personnalisé pour l'UI\nexport const useUI = () => {\n  const {\n    isCartOpen,\n    isMenuOpen,\n    isCheckoutOpen,\n    isAuthModalOpen,\n    isLoading,\n    loadingMessage,\n    isMobile,\n    scrollY,\n    openCart,\n    closeCart,\n    toggleCart,\n    openMenu,\n    closeMenu,\n    toggleMenu,\n    openCheckout,\n    closeCheckout,\n    openAuthModal,\n    closeAuthModal,\n    setLoading,\n    setIsMobile,\n    setScrollY,\n    closeAllModals,\n  } = useUIStore()\n\n  return {\n    // États\n    isCartOpen,\n    isMenuOpen,\n    isCheckoutOpen,\n    isAuthModalOpen,\n    isLoading,\n    loadingMessage,\n    isMobile,\n    scrollY,\n    \n    // Actions\n    openCart,\n    closeCart,\n    toggleCart,\n    openMenu,\n    closeMenu,\n    toggleMenu,\n    openCheckout,\n    closeCheckout,\n    openAuthModal,\n    closeAuthModal,\n    setLoading,\n    setIsMobile,\n    setScrollY,\n    closeAllModals,\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;;;AA2CO,MAAM,aAAa,CAAA,GAAA,uPAAA,CAAA,SAAM,AAAD,EAAW,CAAC,MAAQ,CAAC;QAClD,iBAAiB;QACjB,YAAY;QACZ,YAAY;QACZ,gBAAgB;QAChB,iBAAiB;QACjB,WAAW;QACX,gBAAgB;QAChB,UAAU;QACV,SAAS;QAET,yBAAyB;QACzB,UAAU,IAAM,IAAI;gBAAE,YAAY;YAAK;QACvC,WAAW,IAAM,IAAI;gBAAE,YAAY;YAAM;QACzC,YAAY,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,YAAY,CAAC,MAAM,UAAU;gBAAC,CAAC;QAEnE,uBAAuB;QACvB,UAAU,IAAM,IAAI;gBAAE,YAAY;YAAK;QACvC,WAAW,IAAM,IAAI;gBAAE,YAAY;YAAM;QACzC,YAAY,IAAM,IAAI,CAAC,QAAU,CAAC;oBAAE,YAAY,CAAC,MAAM,UAAU;gBAAC,CAAC;QAEnE,2BAA2B;QAC3B,cAAc,IAAM,IAAI;gBAAE,gBAAgB;gBAAM,YAAY;YAAM;QAClE,eAAe,IAAM,IAAI;gBAAE,gBAAgB;YAAM;QAEjD,kCAAkC;QAClC,eAAe,IAAM,IAAI;gBAAE,iBAAiB;YAAK;QACjD,gBAAgB,IAAM,IAAI;gBAAE,iBAAiB;YAAM;QAEnD,6BAA6B;QAC7B,YAAY,CAAC,SAAS,UAAU,EAAE,GAAK,IAAI;gBAAE,WAAW;gBAAS,gBAAgB;YAAQ;QAEzF,2BAA2B;QAC3B,aAAa,CAAC,WAAa,IAAI;gBAAE;YAAS;QAC1C,YAAY,CAAC,UAAY,IAAI;gBAAE;YAAQ;QAEvC,4BAA4B;QAC5B,gBAAgB,IAAM,IAAI;gBACxB,YAAY;gBACZ,YAAY;gBACZ,gBAAgB;gBAChB,iBAAiB;YACnB;IACF,CAAC;AAGM,MAAM,QAAQ;;IACnB,MAAM,EACJ,UAAU,EACV,UAAU,EACV,cAAc,EACd,eAAe,EACf,SAAS,EACT,cAAc,EACd,QAAQ,EACR,OAAO,EACP,QAAQ,EACR,SAAS,EACT,UAAU,EACV,QAAQ,EACR,SAAS,EACT,UAAU,EACV,YAAY,EACZ,aAAa,EACb,aAAa,EACb,cAAc,EACd,UAAU,EACV,WAAW,EACX,UAAU,EACV,cAAc,EACf,GAAG;IAEJ,OAAO;QACL,QAAQ;QACR;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA,UAAU;QACV;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACF;AACF;GArDa;;QAwBP", "debugId": null}}, {"offset": {"line": 3552, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/stores/index.ts"], "sourcesContent": ["// Export des stores\nexport { useAuth } from \"./auth-store\";\nexport { useCartStore } from \"./cart-store\";\nexport { useCheckoutModal } from \"./checkout-modal-store\";\nexport { useCustomer, useCustomerStore } from \"./customer-store\";\nexport { useNotificationStore, useNotifications } from \"./notification-store\";\nexport { useProductStore, useProducts } from \"./product-store\";\nexport { useUI, useUIStore } from \"./ui-store\";\n\n// Alias pour compatibilité\nexport { useCartStore as useCart } from \"./cart-store\";\nexport { useProductStore as useProduct } from \"./product-store\";\n\n// Export des types\nexport type { Notification } from \"./notification-store\";\n"], "names": [], "mappings": "AAAA,oBAAoB;;AACpB;AACA;AACA;AACA;AACA;AACA;AACA", "debugId": null}}, {"offset": {"line": 3594, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Toast.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\nimport { createPortal } from 'react-dom'\nimport { cn } from '@/lib/utils'\nimport { notificationSlide } from '@/lib/animations'\nimport { useNotifications, type Notification } from '@/stores'\n\nexport interface ToastProps {\n  notification: Notification\n  onRemove: (id: string) => void\n}\n\nconst Toast: React.FC<ToastProps> = ({ notification, onRemove }) => {\n  const { id, type, title, message, action } = notification\n\n  const typeConfig = {\n    success: {\n      icon: '✅',\n      bgColor: 'bg-green-50',\n      borderColor: 'border-green-200',\n      textColor: 'text-green-800',\n      iconColor: 'text-green-500'\n    },\n    error: {\n      icon: '❌',\n      bgColor: 'bg-red-50',\n      borderColor: 'border-red-200',\n      textColor: 'text-red-800',\n      iconColor: 'text-red-500'\n    },\n    warning: {\n      icon: '⚠️',\n      bgColor: 'bg-yellow-50',\n      borderColor: 'border-yellow-200',\n      textColor: 'text-yellow-800',\n      iconColor: 'text-yellow-500'\n    },\n    info: {\n      icon: 'ℹ️',\n      bgColor: 'bg-blue-50',\n      borderColor: 'border-blue-200',\n      textColor: 'text-blue-800',\n      iconColor: 'text-blue-500'\n    }\n  }\n\n  const config = typeConfig[type]\n\n  return (\n    <motion.div\n      className={cn(\n        'relative flex items-start p-4 rounded-lg border shadow-lg max-w-sm w-full',\n        config.bgColor,\n        config.borderColor\n      )}\n      initial={notificationSlide.initial}\n      animate={notificationSlide.animate}\n      exit={notificationSlide.exit}\n      layout\n    >\n      {/* Icon */}\n      <div className={cn('flex-shrink-0 mr-3', config.iconColor)}>\n        <span className=\"text-xl\">{config.icon}</span>\n      </div>\n\n      {/* Content */}\n      <div className=\"flex-1 min-w-0\">\n        <h4 className={cn('text-sm font-medium', config.textColor)}>\n          {title}\n        </h4>\n        {message && (\n          <p className={cn('mt-1 text-sm', config.textColor, 'opacity-90')}>\n            {message}\n          </p>\n        )}\n        {action && (\n          <div className=\"mt-2\">\n            <button\n              onClick={action.onClick}\n              className={cn(\n                'text-sm font-medium underline hover:no-underline',\n                config.textColor\n              )}\n            >\n              {action.label}\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Close Button */}\n      <button\n        onClick={() => onRemove(id)}\n        className={cn(\n          'flex-shrink-0 ml-3 p-1 rounded-md hover:bg-black/10 transition-colors',\n          config.textColor\n        )}\n        aria-label=\"Fermer\"\n      >\n        <svg\n          className=\"w-4 h-4\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M6 18L18 6M6 6l12 12\"\n          />\n        </svg>\n      </button>\n    </motion.div>\n  )\n}\n\n// Conteneur pour afficher les toasts\nexport const ToastContainer: React.FC = () => {\n  const { notifications, removeNotification } = useNotifications()\n\n  if (typeof window === 'undefined') return null\n\n  return createPortal(\n    <div className=\"fixed top-4 right-4 z-50 space-y-2\">\n      <AnimatePresence mode=\"popLayout\">\n        {notifications.map((notification) => (\n          <Toast\n            key={notification.id}\n            notification={notification}\n            onRemove={removeNotification}\n          />\n        ))}\n      </AnimatePresence>\n    </div>,\n    document.body\n  )\n}\n\nexport { Toast }\n"], "names": [], "mappings": ";;;;;AAGA;AAAA;AACA;AACA;AACA;AACA;AAAA;;;AAPA;;;;;;AAcA,MAAM,QAA8B,CAAC,EAAE,YAAY,EAAE,QAAQ,EAAE;IAC7D,MAAM,EAAE,EAAE,EAAE,IAAI,EAAE,KAAK,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IAE7C,MAAM,aAAa;QACjB,SAAS;YACP,MAAM;YACN,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,OAAO;YACL,MAAM;YACN,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,SAAS;YACP,MAAM;YACN,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;QACb;QACA,MAAM;YACJ,MAAM;YACN,SAAS;YACT,aAAa;YACb,WAAW;YACX,WAAW;QACb;IACF;IAEA,MAAM,SAAS,UAAU,CAAC,KAAK;IAE/B,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6EACA,OAAO,OAAO,EACd,OAAO,WAAW;QAEpB,SAAS,2HAAA,CAAA,oBAAiB,CAAC,OAAO;QAClC,SAAS,2HAAA,CAAA,oBAAiB,CAAC,OAAO;QAClC,MAAM,2HAAA,CAAA,oBAAiB,CAAC,IAAI;QAC5B,MAAM;;0BAGN,4SAAC;gBAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,sBAAsB,OAAO,SAAS;0BACvD,cAAA,4SAAC;oBAAK,WAAU;8BAAW,OAAO,IAAI;;;;;;;;;;;0BAIxC,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,uBAAuB,OAAO,SAAS;kCACtD;;;;;;oBAEF,yBACC,4SAAC;wBAAE,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,gBAAgB,OAAO,SAAS,EAAE;kCAChD;;;;;;oBAGJ,wBACC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BACC,SAAS,OAAO,OAAO;4BACvB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,oDACA,OAAO,SAAS;sCAGjB,OAAO,KAAK;;;;;;;;;;;;;;;;;0BAOrB,4SAAC;gBACC,SAAS,IAAM,SAAS;gBACxB,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,yEACA,OAAO,SAAS;gBAElB,cAAW;0BAEX,cAAA,4SAAC;oBACC,WAAU;oBACV,MAAK;oBACL,QAAO;oBACP,SAAQ;8BAER,cAAA,4SAAC;wBACC,eAAc;wBACd,gBAAe;wBACf,aAAa;wBACb,GAAE;;;;;;;;;;;;;;;;;;;;;;AAMd;KAvGM;AA0GC,MAAM,iBAA2B;;IACtC,MAAM,EAAE,aAAa,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAE7D,uCAAmC;;IAAU;IAE7C,qBAAO,CAAA,GAAA,mRAAA,CAAA,eAAY,AAAD,gBAChB,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC,kSAAA,CAAA,kBAAe;YAAC,MAAK;sBACnB,cAAc,GAAG,CAAC,CAAC,6BAClB,4SAAC;oBAEC,cAAc;oBACd,UAAU;mBAFL,aAAa,EAAE;;;;;;;;;;;;;;cAO5B,SAAS,IAAI;AAEjB;GAnBa;;QACmC,yIAAA,CAAA,mBAAgB;;;MADnD", "debugId": null}}, {"offset": {"line": 3795, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/ProductImage.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport { motion } from \"framer-motion\";\nimport Image from \"next/image\";\nimport React, { useState } from \"react\";\n\ninterface ProductImageProps {\n  src: string;\n  alt: string;\n  className?: string;\n  priority?: boolean;\n  fill?: boolean;\n  width?: number;\n  height?: number;\n  sizes?: string;\n  onLoad?: () => void;\n  onError?: () => void;\n}\n\nconst ProductImage: React.FC<ProductImageProps> = ({\n  src,\n  alt,\n  className,\n  priority = false,\n  fill = false,\n  width,\n  height,\n  sizes,\n  onLoad,\n  onError,\n}) => {\n  const [isLoading, setIsLoading] = useState(true);\n  const [hasError, setHasError] = useState(false);\n\n  const handleLoad = () => {\n    setIsLoading(false);\n    onLoad?.();\n  };\n\n  const handleError = () => {\n    setIsLoading(false);\n    setHasError(true);\n    onError?.();\n  };\n\n  // Image de fallback pour les erreurs\n  const fallbackSrc = \"/img/placeholder.svg\";\n\n  return (\n    <div className={cn(\"relative overflow-hidden\", className)}>\n      {/* Loading skeleton */}\n      {isLoading && (\n        <div className=\"absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 animate-pulse\" />\n      )}\n\n      {/* Image principale */}\n      <motion.div\n        initial={{ opacity: 0 }}\n        animate={{ opacity: isLoading ? 0 : 1 }}\n        transition={{ duration: 0.3 }}\n        className=\"relative w-full h-full\"\n      >\n        <Image\n          src={hasError ? fallbackSrc : src}\n          alt={alt}\n          fill={fill}\n          width={!fill ? width : undefined}\n          height={!fill ? height : undefined}\n          sizes={sizes}\n          priority={priority}\n          onLoad={handleLoad}\n          onError={handleError}\n          className={cn(\n            \"object-cover transition-transform duration-300 hover:scale-105\",\n            hasError && \"filter grayscale\"\n          )}\n        />\n      </motion.div>\n\n      {/* Badge THC */}\n      <div className=\"absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg\">\n        🌿 THC\n      </div>\n\n      {/* Badge 18+ */}\n      <div className=\"absolute bottom-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full shadow-lg\">\n        18+\n      </div>\n\n      {/* Overlay d'erreur */}\n      {hasError && (\n        <div className=\"absolute inset-0 bg-black/50 flex items-center justify-center\">\n          <div className=\"text-white text-center\">\n            <div className=\"text-2xl mb-2\">📷</div>\n            <div className=\"text-sm\">Image non disponible</div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\nexport { ProductImage };\nexport type { ProductImageProps };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;;;AALA;;;;;AAoBA,MAAM,eAA4C,CAAC,EACjD,GAAG,EACH,GAAG,EACH,SAAS,EACT,WAAW,KAAK,EAChB,OAAO,KAAK,EACZ,KAAK,EACL,MAAM,EACN,KAAK,EACL,MAAM,EACN,OAAO,EACR;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa;QACjB,aAAa;QACb;IACF;IAEA,MAAM,cAAc;QAClB,aAAa;QACb,YAAY;QACZ;IACF;IAEA,qCAAqC;IACrC,MAAM,cAAc;IAEpB,qBACE,4SAAC;QAAI,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EAAE,4BAA4B;;YAE5C,2BACC,4SAAC;gBAAI,WAAU;;;;;;0BAIjB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS,YAAY,IAAI;gBAAE;gBACtC,YAAY;oBAAE,UAAU;gBAAI;gBAC5B,WAAU;0BAEV,cAAA,4SAAC,+OAAA,CAAA,UAAK;oBACJ,KAAK,WAAW,cAAc;oBAC9B,KAAK;oBACL,MAAM;oBACN,OAAO,CAAC,OAAO,QAAQ;oBACvB,QAAQ,CAAC,OAAO,SAAS;oBACzB,OAAO;oBACP,UAAU;oBACV,QAAQ;oBACR,SAAS;oBACT,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,kEACA,YAAY;;;;;;;;;;;0BAMlB,4SAAC;gBAAI,WAAU;0BAAoG;;;;;;0BAKnH,4SAAC;gBAAI,WAAU;0BAAoG;;;;;;YAKlH,0BACC,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,4SAAC;4BAAI,WAAU;sCAAU;;;;;;;;;;;;;;;;;;;;;;;AAMrC;GAjFM;KAAA", "debugId": null}}, {"offset": {"line": 3936, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/Skeleton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { cn } from \"@/lib/utils\";\nimport React from \"react\";\n\ninterface SkeletonProps {\n  className?: string;\n  children?: React.ReactNode;\n}\n\nconst Skeleton: React.FC<SkeletonProps> = ({ className, children }) => {\n  return (\n    <div\n      className={cn(\n        \"animate-pulse rounded-md bg-gray-200\",\n        className\n      )}\n    >\n      {children}\n    </div>\n  );\n};\n\nexport { Skeleton };\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,WAAoC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE;IAChE,qBACE,4SAAC;QACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,wCACA;kBAGD;;;;;;AAGP;KAXM", "debugId": null}}, {"offset": {"line": 3967, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/ProductSkeleton.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\nimport { Skeleton } from \"./Skeleton\";\n\nconst ProductSkeleton: React.FC = () => {\n  return (\n    <motion.div\n      className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.5 }}\n    >\n      {/* Image skeleton */}\n      <div className=\"relative h-64 bg-gradient-to-br from-pink-50 to-purple-50 p-6\">\n        <Skeleton className=\"w-full h-full rounded-xl\" />\n      </div>\n\n      {/* Content skeleton */}\n      <div className=\"p-6\">\n        {/* Badge skeleton */}\n        <div className=\"text-center mb-4\">\n          <Skeleton className=\"inline-block h-6 w-24 rounded-full\" />\n        </div>\n\n        {/* Title skeleton */}\n        <Skeleton className=\"h-8 w-3/4 mx-auto mb-3\" />\n\n        {/* Description skeleton */}\n        <div className=\"space-y-2 mb-6\">\n          <Skeleton className=\"h-4 w-full\" />\n          <Skeleton className=\"h-4 w-5/6\" />\n          <Skeleton className=\"h-4 w-4/6\" />\n        </div>\n\n        {/* Price skeleton */}\n        <div className=\"text-center mb-6\">\n          <Skeleton className=\"h-6 w-20 mx-auto\" />\n        </div>\n\n        {/* Button skeleton */}\n        <Skeleton className=\"h-12 w-full rounded-lg\" />\n      </div>\n    </motion.div>\n  );\n};\n\nconst ProductGridSkeleton: React.FC = () => {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-8 max-w-4xl mx-auto\">\n      {Array.from({ length: 2 }).map((_, index) => (\n        <ProductSkeleton key={index} />\n      ))}\n    </div>\n  );\n};\n\nexport { ProductSkeleton, ProductGridSkeleton };\n"], "names": [], "mappings": ";;;;;AAEA;AAEA;AAJA;;;;AAMA,MAAM,kBAA4B;IAChC,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC,uIAAA,CAAA,WAAQ;oBAAC,WAAU;;;;;;;;;;;0BAItB,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAItB,4SAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;kCAGpB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,4SAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;0CACpB,4SAAC,uIAAA,CAAA,WAAQ;gCAAC,WAAU;;;;;;;;;;;;kCAItB,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,uIAAA,CAAA,WAAQ;4BAAC,WAAU;;;;;;;;;;;kCAItB,4SAAC,uIAAA,CAAA,WAAQ;wBAAC,WAAU;;;;;;;;;;;;;;;;;;AAI5B;KAxCM;AA0CN,MAAM,sBAAgC;IACpC,qBACE,4SAAC;QAAI,WAAU;kBACZ,MAAM,IAAI,CAAC;YAAE,QAAQ;QAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,4SAAC,qBAAqB;;;;;;;;;;AAI9B;MARM", "debugId": null}}, {"offset": {"line": 4126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/index.ts"], "sourcesContent": ["// Export des composants UI de base\nexport { Button } from \"./Button\";\nexport type { ButtonProps } from \"./Button\";\n\nexport {\n  Card,\n  CardContent,\n  CardDescription,\n  CardFooter,\n  CardHeader,\n  CardTitle,\n} from \"./Card\";\nexport type { CardProps } from \"./Card\";\n\nexport { <PERSON><PERSON>, <PERSON>dalBody, <PERSON>dalFooter, ModalHeader } from \"./Modal\";\nexport type { ModalProps } from \"./Modal\";\n\nexport { Input, Textarea } from \"./Input\";\nexport type { InputProps, TextareaProps } from \"./Input\";\n\nexport { Select } from \"./Select\";\nexport type { SelectProps } from \"./Select\";\n\nexport { Badge, LoyaltyBadge, OrderStatusBadge } from \"./Badge\";\nexport type {\n  BadgeProps,\n  LoyaltyBadgeProps,\n  OrderStatusBadgeProps,\n} from \"./Badge\";\n\nexport { ButtonLoading, Loading, PageLoading } from \"./Loading\";\nexport type { LoadingProps } from \"./Loading\";\n\nexport { Toast, ToastContainer } from \"./Toast\";\nexport type { ToastProps } from \"./Toast\";\n\nexport { ProductImage } from \"./ProductImage\";\nexport type { ProductImageProps } from \"./ProductImage\";\n\nexport { ProductGridSkeleton, ProductSkeleton } from \"./ProductSkeleton\";\nexport { Skeleton } from \"./Skeleton\";\n"], "names": [], "mappings": "AAAA,mCAAmC;;AACnC;AAGA;AAUA;AAGA;AAGA;AAGA;AAOA;AAGA;AAGA;AAGA;AACA", "debugId": null}}, {"offset": {"line": 4198, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/layout/Header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"@/components/ui\";\nimport { slideDown } from \"@/lib/animations\";\nimport { cn } from \"@/lib/utils\";\nimport { useAuth, useCart, useProduct, useUI } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport { ChevronDown, Menu, ShoppingCart, User, X } from \"lucide-react\";\nimport Link from \"next/link\";\nimport React, { useEffect, useState } from \"react\";\n\nconst Header: React.FC = () => {\n  const [isScrolled, setIsScrolled] = useState(false);\n  const { getTotalItems } = useCart();\n  const { openCart } = useUI();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const [isProductsDropdownOpen, setIsProductsDropdownOpen] = useState(false);\n  const { products, fetchProducts } = useProduct();\n  const { isAuthenticated, user, logout } = useAuth();\n\n  // Détecter le scroll pour changer l'apparence du header\n  useEffect(() => {\n    const handleScroll = () => {\n      setIsScrolled(window.scrollY > 50);\n    };\n\n    window.addEventListener(\"scroll\", handleScroll);\n    return () => window.removeEventListener(\"scroll\", handleScroll);\n  }, []);\n\n  // Récupérer les produits pour le menu déroulant\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  const totalItems = getTotalItems();\n\n  const navItems = [\n    { label: \"Accueil\", href: \"/\", type: \"link\" },\n    { label: \"À propos & Légal\", href: \"/about\", type: \"link\" },\n    {\n      label: \"Professionnels & Revendeurs\",\n      href: \"/professionals\",\n      type: \"link\",\n    },\n  ];\n\n  const handleNavigation = (item: { href: string; type: string }) => {\n    if (item.type === \"scroll\") {\n      const element = document.querySelector(item.href);\n      if (element) {\n        element.scrollIntoView({ behavior: \"smooth\" });\n      }\n    } else {\n      window.location.href = item.href;\n    }\n    setIsMobileMenuOpen(false);\n  };\n\n  const openCartModal = () => {\n    openCart();\n  };\n\n  return (\n    <motion.header\n      className={cn(\n        \"fixed top-0 left-0 right-0 z-40 transition-all duration-300\",\n        isScrolled\n          ? \"bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200\"\n          : \"bg-transparent\"\n      )}\n      initial={slideDown.initial}\n      animate={slideDown.animate}\n      transition={slideDown.transition}\n    >\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n        <div className=\"flex items-center justify-between h-14 sm:h-16 lg:h-20\">\n          {/* Logo */}\n          <motion.div\n            className=\"flex items-center space-x-1 sm:space-x-2\"\n            whileHover={{ scale: 1.05 }}\n            whileTap={{ scale: 0.95 }}\n          >\n            <Link href=\"/\" className=\"flex items-center space-x-1 sm:space-x-2\">\n              <motion.div\n                className=\"relative w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12\"\n                animate={{ rotate: [0, 5, -5, 0] }}\n                transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}\n              >\n                <img\n                  src=\"/img/logo.jpg\"\n                  alt=\"Deltagum Logo\"\n                  className=\"w-full h-full object-contain rounded-lg\"\n                />\n              </motion.div>\n              <span\n                className={cn(\n                  \"text-lg sm:text-xl lg:text-2xl font-bold bg-gradient-to-r from-pink-500 to-orange-400 bg-clip-text text-transparent\",\n                  \"hidden xs:block\"\n                )}\n              >\n                Deltagum\n              </span>\n            </Link>\n          </motion.div>\n\n          {/* Navigation Desktop */}\n          <nav className=\"hidden lg:flex items-center space-x-8\">\n            {/* Accueil */}\n            <Link href={navItems[0].href}>\n              <motion.button\n                className={cn(\n                  \"text-black hover:text-pink-500 font-medium transition-colors\",\n                  \"relative py-2\"\n                )}\n                whileHover={{ y: -2 }}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0 * 0.1 }}\n              >\n                {navItems[0].label}\n                <motion.div\n                  className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400\"\n                  initial={{ scaleX: 0 }}\n                  whileHover={{ scaleX: 1 }}\n                  transition={{ duration: 0.2 }}\n                />\n              </motion.button>\n            </Link>\n\n            {/* Menu déroulant Produits */}\n            <div className=\"relative\">\n              <motion.button\n                onClick={() =>\n                  setIsProductsDropdownOpen(!isProductsDropdownOpen)\n                }\n                className={cn(\n                  \"text-black hover:text-pink-500 font-medium transition-colors\",\n                  \"relative py-2 flex items-center space-x-1\"\n                )}\n                whileHover={{ y: -2 }}\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 1 * 0.1 }}\n              >\n                <span>Produits</span>\n                <ChevronDown\n                  className={cn(\n                    \"w-4 h-4 transition-transform\",\n                    isProductsDropdownOpen && \"rotate-180\"\n                  )}\n                />\n                <motion.div\n                  className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400\"\n                  initial={{ scaleX: 0 }}\n                  whileHover={{ scaleX: 1 }}\n                  transition={{ duration: 0.2 }}\n                />\n              </motion.button>\n\n              {/* Dropdown Menu */}\n              <AnimatePresence>\n                {isProductsDropdownOpen && (\n                  <motion.div\n                    initial={{ opacity: 0, y: 10 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    exit={{ opacity: 0, y: 10 }}\n                    className=\"absolute top-full left-0 mt-2 w-64 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50\"\n                  >\n                    {products.map((product) => (\n                      <Link\n                        key={product.id}\n                        href={`/products/${product.id}`}\n                        className=\"block px-4 py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors\"\n                        onClick={() => setIsProductsDropdownOpen(false)}\n                      >\n                        <div className=\"flex items-center space-x-3\">\n                          <img\n                            src={product.image}\n                            alt={product.name}\n                            className=\"w-8 h-8 rounded object-cover\"\n                          />\n                          <div>\n                            <div className=\"font-medium\">{product.name}</div>\n                          </div>\n                        </div>\n                      </Link>\n                    ))}\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Autres éléments de navigation (À propos & Légal, Professionnels & Revendeurs) */}\n            {navItems.slice(1).map((item, index) => (\n              <Link href={item.href} key={index}>\n                <motion.button\n                  className={cn(\n                    \"text-black hover:text-pink-500 font-medium transition-colors\",\n                    \"relative py-2\"\n                  )}\n                  whileHover={{ y: -2 }}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ delay: (index + 2) * 0.1 }}\n                >\n                  {item.label}\n                  <motion.div\n                    className=\"absolute bottom-0 left-0 right-0 h-0.5 bg-gradient-to-r from-pink-500 to-orange-400\"\n                    initial={{ scaleX: 0 }}\n                    whileHover={{ scaleX: 1 }}\n                    transition={{ duration: 0.2 }}\n                  />\n                </motion.button>\n              </Link>\n            ))}\n          </nav>\n\n          {/* Actions */}\n          <div className=\"flex items-center space-x-2 sm:space-x-4\">\n            {/* Connexion */}\n            {isAuthenticated ? (\n              <div className=\"relative group\">\n                <motion.button\n                  className=\"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  <User className=\"w-5 h-5 sm:w-6 sm:h-6 text-green-600\" />\n                </motion.button>\n\n                {/* Menu déroulant utilisateur */}\n                <div className=\"absolute right-0 top-full mt-2 w-48 bg-white rounded-lg shadow-lg border opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-200 z-9999\">\n                  <div className=\"p-3 border-b\">\n                    <p className=\"text-sm font-medium text-gray-900\">\n                      {user?.firstName} {user?.lastName}\n                    </p>\n                    <p className=\"text-xs text-gray-500\">{user?.email}</p>\n                  </div>\n                  <div className=\"p-2\">\n                    <Link href=\"/profile\">\n                      <button className=\"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1\">\n                        Mon profil\n                      </button>\n                    </Link>\n                    {user?.role === \"ADMIN\" && (\n                      <Link href=\"/admin/dashboard\">\n                        <button className=\"w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 rounded-md transition-colors mb-1\">\n                          Dashboard Admin\n                        </button>\n                      </Link>\n                    )}\n                    <button\n                      onClick={logout}\n                      className=\"w-full text-left px-3 py-2 text-sm text-red-600 hover:bg-red-50 rounded-md transition-colors\"\n                    >\n                      Se déconnecter\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ) : (\n              <Link href=\"/auth\">\n                <motion.button\n                  className=\"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                  whileHover={{ scale: 1.1 }}\n                  whileTap={{ scale: 0.9 }}\n                >\n                  <User className=\"w-5 h-5 sm:w-6 sm:h-6 text-black\" />\n                </motion.button>\n              </Link>\n            )}\n\n            {/* Panier */}\n            <motion.div className=\"relative\">\n              <motion.button\n                onClick={openCartModal}\n                className=\"relative p-1.5 sm:p-2 rounded-lg hover:bg-gray-100 transition-colors\"\n                whileHover={{ scale: 1.1 }}\n                whileTap={{ scale: 0.9 }}\n              >\n                <ShoppingCart className=\"w-5 h-5 sm:w-6 sm:h-6 text-black\" />\n\n                {/* Badge du nombre d'articles */}\n                <AnimatePresence>\n                  {totalItems > 0 && (\n                    <motion.div\n                      className=\"absolute -top-0.5 -right-0.5 sm:-top-1 sm:-right-1\"\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      exit={{ scale: 0 }}\n                    >\n                      <Badge\n                        variant=\"primary\"\n                        size=\"sm\"\n                        rounded\n                        className=\"min-w-[18px] h-4 sm:min-w-[20px] sm:h-5 text-xs\"\n                      >\n                        {totalItems}\n                      </Badge>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </motion.button>\n            </motion.div>\n\n            {/* Menu Mobile */}\n            <div className=\"lg:hidden\">\n              <Button\n                variant=\"ghost\"\n                size=\"md\"\n                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n                className=\"p-1.5 sm:p-2\"\n              >\n                <motion.div\n                  animate={isMobileMenuOpen ? { rotate: 180 } : { rotate: 0 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  {isMobileMenuOpen ? (\n                    <X className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                  ) : (\n                    <Menu className=\"w-5 h-5 sm:w-6 sm:h-6\" />\n                  )}\n                </motion.div>\n              </Button>\n            </div>\n          </div>\n        </div>\n\n        {/* Menu Mobile */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              className=\"lg:hidden border-t border-gray-200 bg-white/95 backdrop-blur-md\"\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: \"auto\" }}\n              exit={{ opacity: 0, height: 0 }}\n              transition={{ duration: 0.2 }}\n            >\n              <nav className=\"py-3 sm:py-4 space-y-1 sm:space-y-2\">\n                {navItems.map((item, index) => (\n                  <motion.button\n                    key={item.href}\n                    onClick={() => handleNavigation(item)}\n                    className=\"block w-full text-left px-3 sm:px-4 py-2.5 sm:py-3 text-gray-700 hover:text-pink-500 hover:bg-pink-50 transition-colors text-sm sm:text-base font-medium\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    transition={{ delay: index * 0.05 }}\n                  >\n                    {item.label}\n                  </motion.button>\n                ))}\n              </nav>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </motion.header>\n  );\n};\n\nexport { Header };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,SAAmB;;IACvB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IAChC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD;IACzB,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACrE,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,qLAAA,CAAA,aAAU,AAAD;IAC7C,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAEhD,wDAAwD;IACxD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;iDAAe;oBACnB,cAAc,OAAO,OAAO,GAAG;gBACjC;;YAEA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG,EAAE;IAEL,gDAAgD;IAChD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR;QACF;2BAAG;QAAC;KAAc;IAElB,MAAM,aAAa;IAEnB,MAAM,WAAW;QACf;YAAE,OAAO;YAAW,MAAM;YAAK,MAAM;QAAO;QAC5C;YAAE,OAAO;YAAoB,MAAM;YAAU,MAAM;QAAO;QAC1D;YACE,OAAO;YACP,MAAM;YACN,MAAM;QACR;KACD;IAED,MAAM,mBAAmB,CAAC;QACxB,IAAI,KAAK,IAAI,KAAK,UAAU;YAC1B,MAAM,UAAU,SAAS,aAAa,CAAC,KAAK,IAAI;YAChD,IAAI,SAAS;gBACX,QAAQ,cAAc,CAAC;oBAAE,UAAU;gBAAS;YAC9C;QACF,OAAO;YACL,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,IAAI;QAClC;QACA,oBAAoB;IACtB;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;QACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,+DACA,aACI,oEACA;QAEN,SAAS,2HAAA,CAAA,YAAS,CAAC,OAAO;QAC1B,SAAS,2HAAA,CAAA,YAAS,CAAC,OAAO;QAC1B,YAAY,2HAAA,CAAA,YAAS,CAAC,UAAU;kBAEhC,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;sCAExB,cAAA,4SAAC,8QAAA,CAAA,UAAI;gCAAC,MAAK;gCAAI,WAAU;;kDACvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,QAAQ;gDAAC;gDAAG;gDAAG,CAAC;gDAAG;6CAAE;wCAAC;wCACjC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;4CAAU,aAAa;wCAAE;kDAE5D,cAAA,4SAAC;4CACC,KAAI;4CACJ,KAAI;4CACJ,WAAU;;;;;;;;;;;kDAGd,4SAAC;wCACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,uHACA;kDAEH;;;;;;;;;;;;;;;;;sCAOL,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC,8QAAA,CAAA,UAAI;oCAAC,MAAM,QAAQ,CAAC,EAAE,CAAC,IAAI;8CAC1B,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;wCAEF,YAAY;4CAAE,GAAG,CAAC;wCAAE;wCACpB,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO,IAAI;wCAAI;;4CAE5B,QAAQ,CAAC,EAAE,CAAC,KAAK;0DAClB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDAAE,QAAQ;gDAAE;gDACrB,YAAY;oDAAE,QAAQ;gDAAE;gDACxB,YAAY;oDAAE,UAAU;gDAAI;;;;;;;;;;;;;;;;;8CAMlC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,SAAS,IACP,0BAA0B,CAAC;4CAE7B,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;4CAEF,YAAY;gDAAE,GAAG,CAAC;4CAAE;4CACpB,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,IAAI;4CAAI;;8DAE7B,4SAAC;8DAAK;;;;;;8DACN,4SAAC,2SAAA,CAAA,cAAW;oDACV,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gCACA,0BAA0B;;;;;;8DAG9B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,YAAY;wDAAE,QAAQ;oDAAE;oDACxB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;sDAKhC,4SAAC,kSAAA,CAAA,kBAAe;sDACb,wCACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC7B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,WAAU;0DAET,SAAS,GAAG,CAAC,CAAC,wBACb,4SAAC,8QAAA,CAAA,UAAI;wDAEH,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;wDAC/B,WAAU;wDACV,SAAS,IAAM,0BAA0B;kEAEzC,cAAA,4SAAC;4DAAI,WAAU;;8EACb,4SAAC;oEACC,KAAK,QAAQ,KAAK;oEAClB,KAAK,QAAQ,IAAI;oEACjB,WAAU;;;;;;8EAEZ,4SAAC;8EACC,cAAA,4SAAC;wEAAI,WAAU;kFAAe,QAAQ,IAAI;;;;;;;;;;;;;;;;;uDAZzC,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;gCAuB1B,SAAS,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,MAAM,sBAC5B,4SAAC,8QAAA,CAAA,UAAI;wCAAC,MAAM,KAAK,IAAI;kDACnB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,gEACA;4CAEF,YAAY;gDAAE,GAAG,CAAC;4CAAE;4CACpB,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,OAAO,CAAC,QAAQ,CAAC,IAAI;4CAAI;;gDAEtC,KAAK,KAAK;8DACX,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,QAAQ;oDAAE;oDACrB,YAAY;wDAAE,QAAQ;oDAAE;oDACxB,YAAY;wDAAE,UAAU;oDAAI;;;;;;;;;;;;uCAhBN;;;;;;;;;;;sCAwBhC,4SAAC;4BAAI,WAAU;;gCAEZ,gCACC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;4CACZ,WAAU;4CACV,YAAY;gDAAE,OAAO;4CAAI;4CACzB,UAAU;gDAAE,OAAO;4CAAI;sDAEvB,cAAA,4SAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;sDAIlB,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;;sEACb,4SAAC;4DAAE,WAAU;;gEACV,MAAM;gEAAU;gEAAE,MAAM;;;;;;;sEAE3B,4SAAC;4DAAE,WAAU;sEAAyB,MAAM;;;;;;;;;;;;8DAE9C,4SAAC;oDAAI,WAAU;;sEACb,4SAAC,8QAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,4SAAC;gEAAO,WAAU;0EAAsG;;;;;;;;;;;wDAIzH,MAAM,SAAS,yBACd,4SAAC,8QAAA,CAAA,UAAI;4DAAC,MAAK;sEACT,cAAA,4SAAC;gEAAO,WAAU;0EAAsG;;;;;;;;;;;sEAK5H,4SAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;;yDAOP,4SAAC,8QAAA,CAAA,UAAI;oCAAC,MAAK;8CACT,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;kDAEvB,cAAA,4SAAC,yRAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;;;;;;;;;;;8CAMtB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCAAC,WAAU;8CACpB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,UAAU;4CAAE,OAAO;wCAAI;;0DAEvB,4SAAC,6SAAA,CAAA,eAAY;gDAAC,WAAU;;;;;;0DAGxB,4SAAC,kSAAA,CAAA,kBAAe;0DACb,aAAa,mBACZ,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,WAAU;oDACV,SAAS;wDAAE,OAAO;oDAAE;oDACpB,SAAS;wDAAE,OAAO;oDAAE;oDACpB,MAAM;wDAAE,OAAO;oDAAE;8DAEjB,cAAA,4SAAC,oIAAA,CAAA,QAAK;wDACJ,SAAQ;wDACR,MAAK;wDACL,OAAO;wDACP,WAAU;kEAET;;;;;;;;;;;;;;;;;;;;;;;;;;;8CASb,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,oBAAoB,CAAC;wCACpC,WAAU;kDAEV,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS,mBAAmB;gDAAE,QAAQ;4CAAI,IAAI;gDAAE,QAAQ;4CAAE;4CAC1D,YAAY;gDAAE,UAAU;4CAAI;sDAE3B,iCACC,4SAAC,mRAAA,CAAA,IAAC;gDAAC,WAAU;;;;;qEAEb,4SAAC,yRAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAS5B,4SAAC,kSAAA,CAAA,kBAAe;8BACb,kCACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,4SAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,MAAM,sBACnB,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;oCAEZ,SAAS,IAAM,iBAAiB;oCAChC,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC9B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO,QAAQ;oCAAK;8CAEjC,KAAK,KAAK;mCAPN,KAAK,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiBlC;GA5VM;;QAEsB,4KAAA,CAAA,UAAO;QACZ,+HAAA,CAAA,QAAK;QAGU,qLAAA,CAAA,aAAU;QACJ,iIAAA,CAAA,UAAO;;;KAP7C", "debugId": null}}, {"offset": {"line": 4951, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/layout/Footer.tsx"], "sourcesContent": ["\"use client\";\n\nimport Link from \"next/link\";\nimport React from \"react\";\n\nconst Footer: React.FC = () => {\n  const scrollToSection = (href: string) => {\n    const element = document.querySelector(href);\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  const footerLinks = {\n    navigation: [\n      { label: \"Accueil\", href: \"/\" },\n      { label: \"À propos & Légal\", href: \"/about\" },\n      { label: \"Professionnels & Revendeurs\", href: \"/professionals\" },\n    ],\n    legal: [\n      { label: \"Mentions légales\", href: \"/legal\" },\n      { label: \"Politique de confidentialité\", href: \"/privacy\" },\n      { label: \"Conditions générales\", href: \"/terms\" },\n      { label: \"Vérification d'âge\", href: \"/age-verification\" },\n      { label: \"Cookies\", href: \"/cookies\" },\n    ],\n    social: [\n      {\n        label: \"Facebook\",\n        href: \"https://facebook.com/deltagum\",\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z\" />\n          </svg>\n        ),\n      },\n      {\n        label: \"Instagram\",\n        href: \"https://instagram.com/deltagum\",\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12.017 0C5.396 0 .029 5.367.029 11.987c0 6.62 5.367 11.987 11.988 11.987 6.62 0 11.987-5.367 11.987-11.987C24.014 5.367 18.637.001 12.017.001zM8.449 16.988c-1.297 0-2.448-.49-3.323-1.297C4.198 14.895 3.708 13.744 3.708 12.447s.49-2.448 1.297-3.323C5.902 8.198 7.053 7.708 8.35 7.708s2.448.49 3.323 1.297c.876.876 1.366 2.027 1.366 3.324s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366zm7.718 0c-1.297 0-2.448-.49-3.323-1.297-.876-.875-1.366-2.026-1.366-3.323s.49-2.448 1.366-3.323c.875-.876 2.026-1.366 3.323-1.366s2.448.49 3.323 1.366c.876.875 1.366 2.026 1.366 3.323s-.49 2.448-1.366 3.323c-.875.876-2.026 1.366-3.323 1.366z\" />\n          </svg>\n        ),\n      },\n      {\n        label: \"Twitter\",\n        href: \"https://twitter.com/deltagum\",\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z\" />\n          </svg>\n        ),\n      },\n      {\n        label: \"TikTok\",\n        href: \"https://tiktok.com/@deltagum\",\n        icon: (\n          <svg className=\"w-5 h-5\" fill=\"currentColor\" viewBox=\"0 0 24 24\">\n            <path d=\"M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z\" />\n          </svg>\n        ),\n      },\n    ],\n  };\n\n  return (\n    <footer className=\"bg-gradient-to-br from-gray-900 via-gray-800 to-gray-900 text-white\">\n      {/* Section principale */}\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8 py-8 sm:py-12 lg:py-16\">\n        <div className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8 lg:gap-12\">\n          {/* Logo et description */}\n          <div className=\"lg:col-span-1 text-center sm:text-left\">\n            <div className=\"flex items-center justify-center sm:justify-start space-x-2 sm:space-x-3 mb-4 sm:mb-6\">\n              <div className=\"w-8 h-8 sm:w-10 sm:h-10\">\n                <img\n                  src=\"/img/logo.jpg\"\n                  alt=\"Deltagum Logo\"\n                  className=\"w-full h-full object-contain rounded-lg\"\n                />\n              </div>\n              <span className=\"text-xl sm:text-2xl font-bold bg-gradient-to-r from-pink-400 to-orange-300 bg-clip-text text-transparent\">\n                Deltagum\n              </span>\n            </div>\n            <p className=\"text-gray-300 mb-4 sm:mb-6 leading-relaxed text-sm sm:text-base\">\n              Des bonbons artisanaux aux saveurs uniques qui éveillent vos sens.\n              Découvrez nos créations gourmandes aux parfums de fraise, myrtille\n              et pomme.\n            </p>\n          </div>\n\n          {/* Navigation */}\n          {/*<div className=\"text-center sm:text-left\">\n            <h3\n              className=\"font-bold text-white sm:text-lg mb-3 sm:mb-4\"\n              style={{ color: \"#ffffff\" }}\n            >\n              Navigation\n            </h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              {footerLinks.navigation.map((link) => (\n                <li key={link.href}>\n                  <button\n                    onClick={() => scrollToSection(link.href)}\n                    className=\"text-gray-300 hover:text-pink-300 transition-colors duration-200 text-sm sm:text-base\"\n                  >\n                    {link.label}\n                  </button>\n                </li>\n              ))}\n            </ul>\n          </div>*/}\n\n          {/* Informations légales */}\n          <div className=\"text-center sm:text-left\">\n            <h3\n              className=\"font-bold text-white sm:text-lg mb-3 sm:mb-4\"\n              style={{ color: \"#ffffff\" }}\n            >\n              Informations\n            </h3>\n            <ul className=\"space-y-1 sm:space-y-2\">\n              {footerLinks.legal.map((link) => (\n                <li key={link.href}>\n                  <Link\n                    href={link.href}\n                    className=\"text-gray-300 hover:text-pink-300 transition-colors duration-200 text-sm sm:text-base\"\n                  >\n                    {link.label}\n                  </Link>\n                </li>\n              ))}\n            </ul>\n          </div>\n\n          {/* Contact et réseaux sociaux */}\n          <div className=\"text-center sm:text-left\">\n            <h3\n              className=\"font-bold text-white sm:text-lg mb-3 sm:mb-4\"\n              style={{ color: \"#ffffff\" }}\n            >\n              Contact\n            </h3>\n            <div className=\"space-y-2 sm:space-y-3 mb-4 sm:mb-6\">\n              <p className=\"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base\">\n                <svg\n                  className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z\"\n                  />\n                </svg>\n                <EMAIL>\n              </p>\n              <p className=\"text-gray-300 flex items-center justify-center sm:justify-start text-sm sm:text-base\">\n                <svg\n                  className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\"\n                  fill=\"none\"\n                  stroke=\"currentColor\"\n                  viewBox=\"0 0 24 24\"\n                >\n                  <path\n                    strokeLinecap=\"round\"\n                    strokeLinejoin=\"round\"\n                    strokeWidth={2}\n                    d=\"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z\"\n                  />\n                </svg>\n                +33 1 23 45 67 89\n              </p>\n            </div>\n\n            <h4 className=\"font-semibold mb-2 sm:mb-3 text-pink-300 text-sm sm:text-base\">\n              Suivez-nous\n            </h4>\n            <div className=\"flex justify-center sm:justify-start space-x-2 sm:space-x-3\">\n              {footerLinks.social.map((social) => (\n                <a\n                  key={social.label}\n                  href={social.href}\n                  target=\"_blank\"\n                  rel=\"noopener noreferrer\"\n                  className=\"p-1.5 sm:p-2 bg-gray-800 rounded-lg text-gray-300 hover:text-pink-300 hover:bg-gray-700 transition-colors duration-200\"\n                  aria-label={social.label}\n                >\n                  {social.icon}\n                </a>\n              ))}\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Barre de copyright */}\n      <div className=\"border-t border-gray-700 py-4 sm:py-6\">\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n          {/* Avertissement Delta-9 THC */}\n          <div className=\"bg-yellow-900/20 border border-yellow-600/30 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6\">\n            <div className=\"text-center\">\n              <p className=\"text-yellow-300 text-xs sm:text-sm font-semibold mb-1 sm:mb-2\">\n                🌿 AVERTISSEMENT IMPORTANT - PRODUITS DELTA-9 THC\n              </p>\n              <p className=\"text-yellow-200 text-xs leading-relaxed\">\n                Nos produits sont strictement réservés aux personnes majeures\n                (18 ans et plus).\n                <br />\n                Ne pas conduire ou utiliser de machines après consommation.\n                Déconseillé aux femmes enceintes ou allaitantes.\n                <br />\n                Tenir hors de portée des enfants. Consommer avec modération.\n              </p>\n            </div>\n          </div>\n\n          <div className=\"flex flex-col sm:flex-row justify-between items-center space-y-3 sm:space-y-0\">\n            <p className=\"text-gray-400 text-xs sm:text-sm text-center sm:text-left\">\n              © 2024 Deltagum . Tous droits réservés. Fait avec ❤️ en France.\n            </p>\n            <div className=\"flex items-center space-x-2 sm:space-x-4 text-xs sm:text-sm text-gray-400\">\n              <span>🌿 Détente naturelle avec style ! ✨</span>\n            </div>\n          </div>\n        </div>\n      </div>\n    </footer>\n  );\n};\n\nexport { Footer };\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAKA,MAAM,SAAmB;IACvB,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,MAAM,cAAc;QAClB,YAAY;YACV;gBAAE,OAAO;gBAAW,MAAM;YAAI;YAC9B;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAA+B,MAAM;YAAiB;SAChE;QACD,OAAO;YACL;gBAAE,OAAO;gBAAoB,MAAM;YAAS;YAC5C;gBAAE,OAAO;gBAAgC,MAAM;YAAW;YAC1D;gBAAE,OAAO;gBAAwB,MAAM;YAAS;YAChD;gBAAE,OAAO;gBAAsB,MAAM;YAAoB;YACzD;gBAAE,OAAO;gBAAW,MAAM;YAAW;SACtC;QACD,QAAQ;YACN;gBACE,OAAO;gBACP,MAAM;gBACN,oBACE,4SAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,4SAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBACE,4SAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,4SAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBACE,4SAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,4SAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;YACA;gBACE,OAAO;gBACP,MAAM;gBACN,oBACE,4SAAC;oBAAI,WAAU;oBAAU,MAAK;oBAAe,SAAQ;8BACnD,cAAA,4SAAC;wBAAK,GAAE;;;;;;;;;;;YAGd;SACD;IACH;IAEA,qBACE,4SAAC;QAAO,WAAU;;0BAEhB,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDACC,KAAI;gDACJ,KAAI;gDACJ,WAAU;;;;;;;;;;;sDAGd,4SAAC;4CAAK,WAAU;sDAA2G;;;;;;;;;;;;8CAI7H,4SAAC;oCAAE,WAAU;8CAAkE;;;;;;;;;;;;sCA8BjF,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO;oCAAU;8CAC3B;;;;;;8CAGD,4SAAC;oCAAG,WAAU;8CACX,YAAY,KAAK,CAAC,GAAG,CAAC,CAAC,qBACtB,4SAAC;sDACC,cAAA,4SAAC,8QAAA,CAAA,UAAI;gDACH,MAAM,KAAK,IAAI;gDACf,WAAU;0DAET,KAAK,KAAK;;;;;;2CALN,KAAK,IAAI;;;;;;;;;;;;;;;;sCAaxB,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO;oCAAU;8CAC3B;;;;;;8CAGD,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAE,WAAU;;8DACX,4SAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,4SAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;gDAEA;;;;;;;sDAGR,4SAAC;4CAAE,WAAU;;8DACX,4SAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,4SAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;gDAEA;;;;;;;;;;;;;8CAKV,4SAAC;oCAAG,WAAU;8CAAgE;;;;;;8CAG9E,4SAAC;oCAAI,WAAU;8CACZ,YAAY,MAAM,CAAC,GAAG,CAAC,CAAC,uBACvB,4SAAC;4CAEC,MAAM,OAAO,IAAI;4CACjB,QAAO;4CACP,KAAI;4CACJ,WAAU;4CACV,cAAY,OAAO,KAAK;sDAEvB,OAAO,IAAI;2CAPP,OAAO,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAgB7B,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAE,WAAU;kDAAgE;;;;;;kDAG7E,4SAAC;wCAAE,WAAU;;4CAA0C;0DAGrD,4SAAC;;;;;4CAAK;0DAGN,4SAAC;;;;;4CAAK;;;;;;;;;;;;;;;;;;sCAMZ,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAE,WAAU;8CAA4D;;;;;;8CAGzE,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;kDAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB;KApOM", "debugId": null}}, {"offset": {"line": 5428, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/useAgeVerification.ts"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\n\nconst AGE_VERIFICATION_KEY = \"deltagum_age_verified\";\nconst VERIFICATION_EXPIRY = 24 * 60 * 60 * 1000; // 24 heures en millisecondes\n\nexport const useAgeVerification = () => {\n  const [isVerified, setIsVerified] = useState<boolean | null>(null);\n  const [showModal, setShowModal] = useState(false);\n\n  useEffect(() => {\n    // Vérifier si l'utilisateur a déjà été vérifié\n    const checkVerification = () => {\n      try {\n        const stored = localStorage.getItem(AGE_VERIFICATION_KEY);\n        if (stored) {\n          const { timestamp, verified } = JSON.parse(stored);\n          const now = Date.now();\n          \n          // Vérifier si la vérification n'a pas expiré\n          if (verified && (now - timestamp) < VERIFICATION_EXPIRY) {\n            setIsVerified(true);\n            setShowModal(false);\n            return;\n          } else {\n            // Supprimer la vérification expirée\n            localStorage.removeItem(AGE_VERIFICATION_KEY);\n          }\n        }\n        \n        // Pas de vérification valide, afficher le modal\n        setIsVerified(false);\n        setShowModal(true);\n      } catch (error) {\n        console.error(\"Erreur lors de la vérification d'âge:\", error);\n        setIsVerified(false);\n        setShowModal(true);\n      }\n    };\n\n    checkVerification();\n  }, []);\n\n  const confirmAge = () => {\n    try {\n      const verificationData = {\n        verified: true,\n        timestamp: Date.now(),\n      };\n      localStorage.setItem(AGE_VERIFICATION_KEY, JSON.stringify(verificationData));\n      setIsVerified(true);\n      setShowModal(false);\n    } catch (error) {\n      console.error(\"Erreur lors de la sauvegarde de la vérification:\", error);\n    }\n  };\n\n  const denyAge = () => {\n    setIsVerified(false);\n    setShowModal(false);\n    // Rediriger vers une page d'information ou fermer l'onglet\n    window.location.href = \"https://www.service-public.fr/particuliers/vosdroits/F32094\";\n  };\n\n  const resetVerification = () => {\n    localStorage.removeItem(AGE_VERIFICATION_KEY);\n    setIsVerified(false);\n    setShowModal(true);\n  };\n\n  return {\n    isVerified,\n    showModal,\n    confirmAge,\n    denyAge,\n    resetVerification,\n  };\n};\n"], "names": [], "mappings": ";;;AAEA;;AAFA;;AAIA,MAAM,uBAAuB;AAC7B,MAAM,sBAAsB,KAAK,KAAK,KAAK,MAAM,6BAA6B;AAEvE,MAAM,qBAAqB;;IAChC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAkB;IAC7D,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;wCAAE;YACR,+CAA+C;YAC/C,MAAM;kEAAoB;oBACxB,IAAI;wBACF,MAAM,SAAS,aAAa,OAAO,CAAC;wBACpC,IAAI,QAAQ;4BACV,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,KAAK,KAAK,CAAC;4BAC3C,MAAM,MAAM,KAAK,GAAG;4BAEpB,6CAA6C;4BAC7C,IAAI,YAAY,AAAC,MAAM,YAAa,qBAAqB;gCACvD,cAAc;gCACd,aAAa;gCACb;4BACF,OAAO;gCACL,oCAAoC;gCACpC,aAAa,UAAU,CAAC;4BAC1B;wBACF;wBAEA,gDAAgD;wBAChD,cAAc;wBACd,aAAa;oBACf,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,yCAAyC;wBACvD,cAAc;wBACd,aAAa;oBACf;gBACF;;YAEA;QACF;uCAAG,EAAE;IAEL,MAAM,aAAa;QACjB,IAAI;YACF,MAAM,mBAAmB;gBACvB,UAAU;gBACV,WAAW,KAAK,GAAG;YACrB;YACA,aAAa,OAAO,CAAC,sBAAsB,KAAK,SAAS,CAAC;YAC1D,cAAc;YACd,aAAa;QACf,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oDAAoD;QACpE;IACF;IAEA,MAAM,UAAU;QACd,cAAc;QACd,aAAa;QACb,2DAA2D;QAC3D,OAAO,QAAQ,CAAC,IAAI,GAAG;IACzB;IAEA,MAAM,oBAAoB;QACxB,aAAa,UAAU,CAAC;QACxB,cAAc;QACd,aAAa;IACf;IAEA,OAAO;QACL;QACA;QACA;QACA;QACA;IACF;AACF;GAvEa", "debugId": null}}, {"offset": {"line": 5516, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/hooks/useAuthInit.ts"], "sourcesContent": ["import { useAuth } from \"@/stores\";\nimport { useEffect } from \"react\";\n\nexport function useAuthInit() {\n  const { checkAuth } = useAuth();\n\n  useEffect(() => {\n    // Vérifier l'authentification au démarrage de l'application\n    checkAuth();\n  }, [checkAuth]);\n}\n"], "names": [], "mappings": ";;;AAAA;AAAA;AACA;;;;AAEO,SAAS;;IACd,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IAE5B,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,4DAA4D;YAC5D;QACF;gCAAG;QAAC;KAAU;AAChB;GAPgB;;QACQ,iIAAA,CAAA,UAAO", "debugId": null}}, {"offset": {"line": 5551, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/modals/AgeVerificationModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\ninterface AgeVerificationModalProps {\n  isOpen: boolean;\n  onConfirm: () => void;\n  onDeny: () => void;\n}\n\nconst AgeVerificationModal: React.FC<AgeVerificationModalProps> = ({\n  isOpen,\n  onConfirm,\n  onDeny,\n}) => {\n  const [showWarning, setShowWarning] = useState(false);\n\n  const handleDeny = () => {\n    setShowWarning(true);\n    setTimeout(() => {\n      onDeny();\n    }, 2000);\n  };\n\n  if (!isOpen) return null;\n\n  return (\n    <AnimatePresence>\n      <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n        {/* Overlay */}\n        <motion.div\n          className=\"absolute inset-0 bg-black/80 backdrop-blur-sm\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n        />\n\n        {/* Modal */}\n        <motion.div\n          className=\"relative z-10 w-full max-w-md mx-4\"\n          initial={{ opacity: 0, scale: 0.9, y: 20 }}\n          animate={{ opacity: 1, scale: 1, y: 0 }}\n          exit={{ opacity: 0, scale: 0.9, y: 20 }}\n          transition={{ duration: 0.3 }}\n        >\n          <div className=\"bg-white rounded-2xl shadow-2xl overflow-hidden\">\n            {/* Header avec gradient */}\n            <div className=\"bg-gradient-to-r from-pink-500 to-orange-500 p-6 text-center\">\n              <motion.div\n                className=\"text-6xl mb-4\"\n                animate={{ rotate: [0, 10, -10, 0] }}\n                transition={{ duration: 2, repeat: Infinity }}\n              >\n                🔞\n              </motion.div>\n              <h2 className=\"text-2xl font-bold text-white font-candy\">\n                Vérification d'âge\n              </h2>\n            </div>\n\n            {/* Content */}\n            <div className=\"p-6 text-center\">\n              <div className=\"mb-6\">\n                <h3 className=\"text-xl font-semibold text-gray-800 mb-3\">\n                  Accès réservé aux adultes\n                </h3>\n                <p className=\"text-gray-600 leading-relaxed\">\n                  Ce site propose des produits à base de{\" \"}\n                  <strong>Delta-9 THC</strong>.\n                  <br />\n                  L'accès est strictement réservé aux personnes majeures.\n                </p>\n              </div>\n\n              <div className=\"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6\">\n                <p className=\"text-sm text-yellow-800\">\n                  ⚠️ <strong>Avertissement :</strong> Ces produits contiennent\n                  du Delta-9 THC et ne sont pas destinés aux mineurs, aux femmes\n                  enceintes ou allaitantes.\n                </p>\n              </div>\n\n              <div className=\"text-lg font-medium text-gray-800 mb-6\">\n                Avez-vous 18 ans ou plus ?\n              </div>\n\n              {!showWarning ? (\n                <div className=\"flex gap-4\">\n                  <Button\n                    onClick={onConfirm}\n                    className=\"flex-1 bg-green-500 hover:bg-green-600 text-white font-semibold py-3\"\n                  >\n                    ✓ Oui, j'ai 18 ans ou plus\n                  </Button>\n                  <Button\n                    onClick={handleDeny}\n                    variant=\"outline\"\n                    className=\"flex-1 border-red-300 text-red-600 hover:bg-red-50 font-semibold py-3\"\n                  >\n                    ✗ Non, j'ai moins de 18 ans\n                  </Button>\n                </div>\n              ) : (\n                <motion.div\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  className=\"text-center\"\n                >\n                  <div className=\"text-red-600 font-semibold mb-4\">\n                    ❌ Accès refusé\n                  </div>\n                  <p className=\"text-gray-600\">\n                    Vous devez être majeur pour accéder à ce site.\n                    <br />\n                    Redirection en cours...\n                  </p>\n                </motion.div>\n              )}\n            </div>\n\n            {/* Footer */}\n            <div className=\"bg-gray-50 px-6 py-4 text-center\">\n              <p className=\"text-xs text-gray-500\">\n                En continuant, vous confirmez être majeur et acceptez nos\n                conditions d'utilisation.\n              </p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </AnimatePresence>\n  );\n};\n\nexport { AgeVerificationModal };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAAA;AACA;;;AAJA;;;;AAYA,MAAM,uBAA4D,CAAC,EACjE,MAAM,EACN,SAAS,EACT,MAAM,EACP;;IACC,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,MAAM,aAAa;QACjB,eAAe;QACf,WAAW;YACT;QACF,GAAG;IACL;IAEA,IAAI,CAAC,QAAQ,OAAO;IAEpB,qBACE,4SAAC,kSAAA,CAAA,kBAAe;kBACd,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;;;;;;8BAIrB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACzC,SAAS;wBAAE,SAAS;wBAAG,OAAO;wBAAG,GAAG;oBAAE;oBACtC,MAAM;wBAAE,SAAS;wBAAG,OAAO;wBAAK,GAAG;oBAAG;oBACtC,YAAY;wBAAE,UAAU;oBAAI;8BAE5B,cAAA,4SAAC;wBAAI,WAAU;;0CAEb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,QAAQ;gDAAC;gDAAG;gDAAI,CAAC;gDAAI;6CAAE;wCAAC;wCACnC,YAAY;4CAAE,UAAU;4CAAG,QAAQ;wCAAS;kDAC7C;;;;;;kDAGD,4SAAC;wCAAG,WAAU;kDAA2C;;;;;;;;;;;;0CAM3D,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,4SAAC;gDAAE,WAAU;;oDAAgC;oDACJ;kEACvC,4SAAC;kEAAO;;;;;;oDAAoB;kEAC5B,4SAAC;;;;;oDAAK;;;;;;;;;;;;;kDAKV,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC;4CAAE,WAAU;;gDAA0B;8DAClC,4SAAC;8DAAO;;;;;;gDAAwB;;;;;;;;;;;;kDAMvC,4SAAC;wCAAI,WAAU;kDAAyC;;;;;;oCAIvD,CAAC,4BACA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;0DAGD,4SAAC,qIAAA,CAAA,SAAM;gDACL,SAAS;gDACT,SAAQ;gDACR,WAAU;0DACX;;;;;;;;;;;6DAKH,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,WAAU;;0DAEV,4SAAC;gDAAI,WAAU;0DAAkC;;;;;;0DAGjD,4SAAC;gDAAE,WAAU;;oDAAgB;kEAE3B,4SAAC;;;;;oDAAK;;;;;;;;;;;;;;;;;;;0CAQd,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUnD;GA1HM;KAAA", "debugId": null}}, {"offset": {"line": 5860, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/modals/CartModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>dal<PERSON><PERSON>, ModalFooter } from \"@/components/ui\";\nimport { cn, formatPrice } from \"@/lib/utils\";\nimport { useAuth, useCart, useCheckoutModal, useNotifications } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport Image from \"next/image\";\nimport React from \"react\";\n\nexport interface CartModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst CartModal: React.FC<CartModalProps> = ({ isOpen, onClose }) => {\n  const { cart, removeItem, clearCart } = useCart();\n  const { openModal } = useCheckoutModal();\n  const { isAuthenticated, user } = useAuth();\n  const { addNotification } = useNotifications();\n  const [isProcessing, setIsProcessing] = React.useState(false);\n\n  const items = cart.items;\n  const total = cart.totalAmount;\n  const totalItems = cart.totalItems;\n  const shippingThreshold = 50; // Livraison gratuite à partir de 50€\n  const shippingCost = total >= shippingThreshold ? 0 : 5.99;\n  const finalTotal = total + shippingCost;\n\n  const handleCheckout = async () => {\n    if (isProcessing) return; // Éviter les clics multiples\n\n    // Vérifier l'authentification\n    if (!isAuthenticated) {\n      addNotification({\n        type: \"warning\",\n        title: \"🔒 Connexion requise\",\n        message:\n          \"Vous devez vous connecter pour passer commande. Redirection en cours...\",\n      });\n\n      onClose(); // Fermer le modal du panier\n\n      // Rediriger vers la page d'authentification après un court délai\n      setTimeout(() => {\n        window.location.href = \"/auth\";\n      }, 1500);\n      return;\n    }\n\n    try {\n      if (items.length === 0) {\n        addNotification({\n          type: \"error\",\n          title: \"Panier vide\",\n          message: \"Ajoutez des produits à votre panier avant de commander\",\n        });\n        return;\n      }\n\n      setIsProcessing(true);\n      console.log(\"🛒 Début du processus de commande...\");\n      console.log(\"📦 Articles du panier:\", items);\n      console.log(\"💰 Total:\", finalTotal);\n\n      // Préparer les données de commande\n      const orderData = {\n        items: items.map((item) => ({\n          productId: item.productId,\n          variantId: item.variantId,\n          quantity: item.quantity,\n        })),\n        shippingAddress: {\n          firstName: user?.firstName || \"Client\",\n          lastName: user?.lastName || \"Deltagum\",\n          email: user?.email || `client-${Date.now()}@deltagum.com`,\n          phone: user?.phone || \"0123456789\",\n          street: user?.address || \"123 Rue de la Livraison\",\n          city: user?.city || \"Paris\",\n          postalCode: user?.postalCode || \"75001\",\n          country: \"France\",\n        },\n        ...(user?.id && { customerId: user.id }),\n        totalAmount: finalTotal,\n      };\n\n      console.log(\"📤 Données de commande:\", orderData);\n\n      // Créer la commande\n      const orderResponse = await fetch(\"/api/orders\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify(orderData),\n      });\n\n      if (!orderResponse.ok) {\n        const errorData = await orderResponse.json();\n        console.error(\"❌ Erreur commande:\", errorData);\n        addNotification({\n          type: \"error\",\n          title: \"Erreur de commande\",\n          message: \"Impossible de créer la commande. Veuillez réessayer.\",\n        });\n        setIsProcessing(false);\n        return;\n      }\n\n      const { data: orderResult } = await orderResponse.json();\n      console.log(\"✅ Commande créée:\", orderResult.id);\n\n      // Créer la session Stripe\n      const sessionResponse = await fetch(\"/api/checkout/session\", {\n        method: \"POST\",\n        headers: { \"Content-Type\": \"application/json\" },\n        body: JSON.stringify({ orderId: orderResult.id }),\n      });\n\n      if (!sessionResponse.ok) {\n        const errorData = await sessionResponse.json();\n        console.error(\"❌ Erreur session:\", errorData);\n        addNotification({\n          type: \"error\",\n          title: \"Erreur de paiement\",\n          message:\n            \"Impossible de créer la session de paiement. Veuillez réessayer.\",\n        });\n        setIsProcessing(false);\n        return;\n      }\n\n      const sessionData = await sessionResponse.json();\n\n      if (sessionData.success && sessionData.data.url) {\n        console.log(\"✅ Session Stripe créée, redirection...\");\n\n        // Sauvegarder les informations du panier pour les restaurer en cas d'annulation\n        localStorage.setItem(\n          \"deltagum_pending_order\",\n          JSON.stringify({\n            orderId: orderResult.id,\n            cartItems: items,\n            timestamp: Date.now(),\n          })\n        );\n\n        // Fermer le modal et rediriger vers Stripe\n        onClose();\n        window.location.href = sessionData.data.url;\n      } else {\n        console.error(\"❌ Erreur session:\", sessionData);\n        addNotification({\n          type: \"error\",\n          title: \"Erreur de session\",\n          message: \"Problème lors de la création de la session de paiement\",\n        });\n        setIsProcessing(false);\n      }\n    } catch (error) {\n      console.error(\"❌ Erreur:\", error);\n      addNotification({\n        type: \"error\",\n        title: \"Erreur inattendue\",\n        message: \"Une erreur s'est produite. Veuillez réessayer.\",\n      });\n      setIsProcessing(false);\n    }\n  };\n\n  // Fonction handleQuantityChange supprimée car les quantités ne sont plus modifiables\n\n  const EmptyCart = () => (\n    <div className=\"text-center py-8 sm:py-12 px-4\">\n      <motion.div\n        className=\"text-4xl sm:text-5xl lg:text-6xl mb-3 sm:mb-4\"\n        animate={{ rotate: [0, 10, -10, 0] }}\n        transition={{ duration: 2, repeat: Infinity }}\n      >\n        🛒\n      </motion.div>\n      <h3 className=\"text-lg sm:text-xl font-semibold text-gray-900 mb-2\">\n        Votre panier est vide\n      </h3>\n      <p className=\"text-gray-600 mb-4 sm:mb-6 text-sm sm:text-base\">\n        Découvrez nos délicieux bonbons et ajoutez-les à votre panier !\n      </p>\n      <Button variant=\"primary\" onClick={onClose} className=\"w-full sm:w-auto\">\n        Continuer mes achats\n      </Button>\n    </div>\n  );\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title=\"Mon Panier\"\n      size=\"lg\"\n      className=\"max-h-[95vh] sm:max-h-[90vh]\"\n    >\n      <ModalBody className=\"p-0\">\n        {items.length === 0 ? (\n          <EmptyCart />\n        ) : (\n          <div className=\"space-y-4 sm:space-y-6\">\n            {/* Articles du panier */}\n            <div className=\"max-h-80 sm:max-h-96 overflow-y-auto px-3 sm:px-6\">\n              <AnimatePresence mode=\"popLayout\">\n                {items.map((item) => (\n                  <motion.div\n                    key={item.id}\n                    className=\"flex items-center space-x-2 sm:space-x-4 py-3 sm:py-4 border-b border-gray-200 last:border-b-0\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    layout\n                  >\n                    {/* Image du produit */}\n                    <div className=\"flex-shrink-0 w-12 h-12 sm:w-16 sm:h-16 bg-gray-100 rounded-lg overflow-hidden\">\n                      {item.image ? (\n                        <Image\n                          src={item.image}\n                          alt={item.name}\n                          width={64}\n                          height={64}\n                          className=\"w-full h-full object-cover\"\n                        />\n                      ) : (\n                        <div className=\"w-full h-full flex items-center justify-center text-lg sm:text-2xl\">\n                          🍭\n                        </div>\n                      )}\n                    </div>\n\n                    {/* Informations du produit */}\n                    <div className=\"flex-1 min-w-0\">\n                      <h4 className=\"font-medium text-gray-900 truncate text-sm sm:text-base\">\n                        {item.name}\n                      </h4>\n                      <p className=\"text-xs sm:text-sm text-gray-600\">\n                        Saveur: {item.flavor}\n                      </p>\n                      <div className=\"flex items-center space-x-2 mt-1\">\n                        <span className=\"font-semibold text-pink-600 text-sm sm:text-base\">\n                          {formatPrice(item.price)}\n                        </span>\n                      </div>\n                    </div>\n\n                    {/* Affichage de la quantité (non modifiable) */}\n                    <div className=\"flex items-center\">\n                      <span className=\"text-sm text-gray-600 bg-gray-100 px-3 py-1 rounded-full\">\n                        Qté: {item.quantity}\n                      </span>\n                    </div>\n\n                    {/* Prix total de l'article */}\n                    <div className=\"text-right\">\n                      <p className=\"font-semibold text-gray-900\">\n                        {formatPrice(item.price * item.quantity)}\n                      </p>\n                    </div>\n\n                    {/* Bouton supprimer */}\n                    <Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      onClick={() => removeItem(item.id)}\n                      className=\"text-red-500 hover:text-red-700 p-1\"\n                    >\n                      <svg\n                        className=\"w-4 h-4\"\n                        fill=\"none\"\n                        stroke=\"currentColor\"\n                        viewBox=\"0 0 24 24\"\n                      >\n                        <path\n                          strokeLinecap=\"round\"\n                          strokeLinejoin=\"round\"\n                          strokeWidth={2}\n                          d=\"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                        />\n                      </svg>\n                    </Button>\n                  </motion.div>\n                ))}\n              </AnimatePresence>\n            </div>\n\n            {/* Résumé de la commande */}\n            <div className=\"px-6 py-4 bg-gray-50 border-t\">\n              <div className=\"space-y-2\">\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-700\">\n                    Sous-total ({totalItems} article{totalItems > 1 ? \"s\" : \"\"})\n                  </span>\n                  <span className=\"text-gray-900 font-medium\">\n                    {formatPrice(total)}\n                  </span>\n                </div>\n\n                <div className=\"flex justify-between text-sm\">\n                  <span className=\"text-gray-700\">Livraison</span>\n                  <span\n                    className={cn(\n                      \"text-gray-900 font-medium\",\n                      shippingCost === 0 && \"text-green-600 font-semibold\"\n                    )}\n                  >\n                    {shippingCost === 0\n                      ? \"Gratuite\"\n                      : formatPrice(shippingCost)}\n                  </span>\n                </div>\n\n                <div className=\"border-t pt-2 flex justify-between font-semibold text-lg\">\n                  <span className=\"text-gray-900\">Total</span>\n                  <span className=\"text-pink-600 font-bold\">\n                    {formatPrice(finalTotal)}\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        )}\n      </ModalBody>\n\n      {items.length > 0 && (\n        <ModalFooter className=\"flex-col space-y-3\">\n          {/* Bouton de test Stripe (développement) - Désactivé car intégré dans \"Commander\" */}\n          {false && process.env.NODE_ENV === \"development\" && (\n            <div className=\"mb-3 text-center\">\n              <button\n                onClick={async () => {\n                  // Test direct de l'API checkout\n                  try {\n                    if (items.length === 0) {\n                      alert(\"❌ Panier vide ! Ajoutez des produits d'abord.\");\n                      return;\n                    }\n\n                    console.log(\"🧪 Test Stripe avec panier réel...\");\n                    console.log(\"📦 Articles du panier:\", items);\n                    console.log(\"💰 Total:\", finalTotal);\n\n                    // Préparer les données de commande avec email unique\n                    const orderData = {\n                      items: items.map((item) => ({\n                        productId: item.productId,\n                        variantId: item.variantId,\n                        quantity: item.quantity,\n                      })),\n                      shippingAddress: {\n                        firstName: \"Test\",\n                        lastName: \"User\",\n                        email: `test-${Date.now()}@example.com`, // Email unique pour chaque test\n                        phone: \"0123456789\",\n                        street: \"123 Rue de Test\",\n                        city: \"Paris\",\n                        postalCode: \"75001\",\n                        country: \"France\",\n                      },\n                      totalAmount: finalTotal || 0,\n                    };\n\n                    console.log(\"📤 Données envoyées:\", orderData);\n\n                    // D'abord créer une commande de test\n                    const orderResponse = await fetch(\"/api/orders\", {\n                      method: \"POST\",\n                      headers: { \"Content-Type\": \"application/json\" },\n                      body: JSON.stringify(orderData),\n                    });\n\n                    if (orderResponse.ok) {\n                      const { data: orderData } = await orderResponse.json();\n\n                      // Puis créer la session Stripe\n                      const sessionResponse = await fetch(\n                        \"/api/checkout/session\",\n                        {\n                          method: \"POST\",\n                          headers: { \"Content-Type\": \"application/json\" },\n                          body: JSON.stringify({ orderId: orderData.id }),\n                        }\n                      );\n\n                      const sessionData = await sessionResponse.json();\n                      console.log(\"Test Stripe:\", sessionData);\n\n                      if (sessionData.success && sessionData.data.url) {\n                        console.log(\"✅ Redirection vers Stripe...\");\n                        window.location.href = sessionData.data.url;\n                      } else {\n                        console.error(\"❌ Erreur session:\", sessionData);\n                        alert(\"❌ Erreur session: \" + sessionData.error);\n                      }\n                    } else {\n                      const errorData = await orderResponse.json();\n                      console.error(\"❌ Erreur commande:\", errorData);\n                      alert(\"❌ Erreur commande: \" + errorData.error);\n                    }\n                  } catch (error) {\n                    console.error(\"❌ Erreur test:\", error);\n                    alert(\"❌ Erreur: \" + error);\n                  }\n                }}\n                className=\"px-4 py-2 bg-blue-100 text-blue-700 rounded-lg text-sm hover:bg-blue-200 transition-colors\"\n              >\n                🧪 Test Stripe API\n              </button>\n            </div>\n          )}\n\n          <div className=\"flex space-x-3 w-full\">\n            <Button\n              variant=\"outline\"\n              onClick={() => clearCart()}\n              className=\"flex-1\"\n            >\n              Vider le panier\n            </Button>\n            <Button\n              variant=\"primary\"\n              onClick={handleCheckout}\n              disabled={isProcessing}\n              className=\"flex-1\"\n            >\n              {isProcessing ? (\n                <>\n                  <div className=\"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"></div>\n                  Traitement en cours...\n                </>\n              ) : (\n                `Commander (${formatPrice(finalTotal)})`\n              )}\n            </Button>\n          </div>\n          <p className=\"text-xs text-gray-500 text-center\">\n            Paiement sécurisé avec Stripe\n          </p>\n        </ModalFooter>\n      )}\n    </Modal>\n  );\n};\n\nexport { CartModal };\n"], "names": [], "mappings": ";;;AAwUoB;;AAtUpB;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;AACA;;;AAPA;;;;;;;AAcA,MAAM,YAAsC,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IAC9D,MAAM,EAAE,IAAI,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IAC9C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,EAAE,eAAe,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,UAAO,AAAD;IACxC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,4QAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEvD,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,QAAQ,KAAK,WAAW;IAC9B,MAAM,aAAa,KAAK,UAAU;IAClC,MAAM,oBAAoB,IAAI,qCAAqC;IACnE,MAAM,eAAe,SAAS,oBAAoB,IAAI;IACtD,MAAM,aAAa,QAAQ;IAE3B,MAAM,iBAAiB;QACrB,IAAI,cAAc,QAAQ,6BAA6B;QAEvD,8BAA8B;QAC9B,IAAI,CAAC,iBAAiB;YACpB,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SACE;YACJ;YAEA,WAAW,4BAA4B;YAEvC,iEAAiE;YACjE,WAAW;gBACT,OAAO,QAAQ,CAAC,IAAI,GAAG;YACzB,GAAG;YACH;QACF;QAEA,IAAI;YACF,IAAI,MAAM,MAAM,KAAK,GAAG;gBACtB,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;gBACA;YACF;YAEA,gBAAgB;YAChB,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,0BAA0B;YACtC,QAAQ,GAAG,CAAC,aAAa;YAEzB,mCAAmC;YACnC,MAAM,YAAY;gBAChB,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;wBAC1B,WAAW,KAAK,SAAS;wBACzB,WAAW,KAAK,SAAS;wBACzB,UAAU,KAAK,QAAQ;oBACzB,CAAC;gBACD,iBAAiB;oBACf,WAAW,MAAM,aAAa;oBAC9B,UAAU,MAAM,YAAY;oBAC5B,OAAO,MAAM,SAAS,CAAC,OAAO,EAAE,KAAK,GAAG,GAAG,aAAa,CAAC;oBACzD,OAAO,MAAM,SAAS;oBACtB,QAAQ,MAAM,WAAW;oBACzB,MAAM,MAAM,QAAQ;oBACpB,YAAY,MAAM,cAAc;oBAChC,SAAS;gBACX;gBACA,GAAI,MAAM,MAAM;oBAAE,YAAY,KAAK,EAAE;gBAAC,CAAC;gBACvC,aAAa;YACf;YAEA,QAAQ,GAAG,CAAC,2BAA2B;YAEvC,oBAAoB;YACpB,MAAM,gBAAgB,MAAM,MAAM,eAAe;gBAC/C,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,YAAY,MAAM,cAAc,IAAI;gBAC1C,QAAQ,KAAK,CAAC,sBAAsB;gBACpC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;gBACA,gBAAgB;gBAChB;YACF;YAEA,MAAM,EAAE,MAAM,WAAW,EAAE,GAAG,MAAM,cAAc,IAAI;YACtD,QAAQ,GAAG,CAAC,qBAAqB,YAAY,EAAE;YAE/C,0BAA0B;YAC1B,MAAM,kBAAkB,MAAM,MAAM,yBAAyB;gBAC3D,QAAQ;gBACR,SAAS;oBAAE,gBAAgB;gBAAmB;gBAC9C,MAAM,KAAK,SAAS,CAAC;oBAAE,SAAS,YAAY,EAAE;gBAAC;YACjD;YAEA,IAAI,CAAC,gBAAgB,EAAE,EAAE;gBACvB,MAAM,YAAY,MAAM,gBAAgB,IAAI;gBAC5C,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SACE;gBACJ;gBACA,gBAAgB;gBAChB;YACF;YAEA,MAAM,cAAc,MAAM,gBAAgB,IAAI;YAE9C,IAAI,YAAY,OAAO,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;gBAC/C,QAAQ,GAAG,CAAC;gBAEZ,gFAAgF;gBAChF,aAAa,OAAO,CAClB,0BACA,KAAK,SAAS,CAAC;oBACb,SAAS,YAAY,EAAE;oBACvB,WAAW;oBACX,WAAW,KAAK,GAAG;gBACrB;gBAGF,2CAA2C;gBAC3C;gBACA,OAAO,QAAQ,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG;YAC7C,OAAO;gBACL,QAAQ,KAAK,CAAC,qBAAqB;gBACnC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;gBACA,gBAAgB;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,aAAa;YAC3B,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,gBAAgB;QAClB;IACF;IAEA,qFAAqF;IAErF,MAAM,YAAY,kBAChB,4SAAC;YAAI,WAAU;;8BACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;oBAAC;oBACnC,YAAY;wBAAE,UAAU;wBAAG,QAAQ;oBAAS;8BAC7C;;;;;;8BAGD,4SAAC;oBAAG,WAAU;8BAAsD;;;;;;8BAGpE,4SAAC;oBAAE,WAAU;8BAAkD;;;;;;8BAG/D,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;oBAAS,WAAU;8BAAmB;;;;;;;;;;;;IAM7E,qBACE,4SAAC,oIAAA,CAAA,QAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;QACL,WAAU;;0BAEV,4SAAC,oIAAA,CAAA,YAAS;gBAAC,WAAU;0BAClB,MAAM,MAAM,KAAK,kBAChB,4SAAC;;;;yCAED,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,kSAAA,CAAA,kBAAe;gCAAC,MAAK;0CACnB,MAAM,GAAG,CAAC,CAAC,qBACV,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,MAAM;;0DAGN,4SAAC;gDAAI,WAAU;0DACZ,KAAK,KAAK,iBACT,4SAAC,+OAAA,CAAA,UAAK;oDACJ,KAAK,KAAK,KAAK;oDACf,KAAK,KAAK,IAAI;oDACd,OAAO;oDACP,QAAQ;oDACR,WAAU;;;;;yEAGZ,4SAAC;oDAAI,WAAU;8DAAqE;;;;;;;;;;;0DAOxF,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,4SAAC;wDAAE,WAAU;;4DAAmC;4DACrC,KAAK,MAAM;;;;;;;kEAEtB,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC;4DAAK,WAAU;sEACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK;;;;;;;;;;;;;;;;;0DAM7B,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDAAK,WAAU;;wDAA2D;wDACnE,KAAK,QAAQ;;;;;;;;;;;;0DAKvB,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDAAE,WAAU;8DACV,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK,GAAG,KAAK,QAAQ;;;;;;;;;;;0DAK3C,4SAAC,qIAAA,CAAA,SAAM;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS,IAAM,WAAW,KAAK,EAAE;gDACjC,WAAU;0DAEV,cAAA,4SAAC;oDACC,WAAU;oDACV,MAAK;oDACL,QAAO;oDACP,SAAQ;8DAER,cAAA,4SAAC;wDACC,eAAc;wDACd,gBAAe;wDACf,aAAa;wDACb,GAAE;;;;;;;;;;;;;;;;;uCAtEH,KAAK,EAAE;;;;;;;;;;;;;;;sCAgFpB,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAK,WAAU;;oDAAgB;oDACjB;oDAAW;oDAAS,aAAa,IAAI,MAAM;oDAAG;;;;;;;0DAE7D,4SAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;kDAIjB,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,4SAAC;gDACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,6BACA,iBAAiB,KAAK;0DAGvB,iBAAiB,IACd,aACA,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;kDAIpB,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAK,WAAU;0DAAgB;;;;;;0DAChC,4SAAC;gDAAK,WAAU;0DACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YAS1B,MAAM,MAAM,GAAG,mBACd,4SAAC,oIAAA,CAAA,cAAW;gBAAC,WAAU;;oBAEpB,SAAS,oDAAyB,+BACjC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BACC,SAAS;gCACP,gCAAgC;gCAChC,IAAI;oCACF,IAAI,MAAM,MAAM,KAAK,GAAG;wCACtB,MAAM;wCACN;oCACF;oCAEA,QAAQ,GAAG,CAAC;oCACZ,QAAQ,GAAG,CAAC,0BAA0B;oCACtC,QAAQ,GAAG,CAAC,aAAa;oCAEzB,qDAAqD;oCACrD,MAAM,YAAY;wCAChB,OAAO,MAAM,GAAG,CAAC,CAAC,OAAS,CAAC;gDAC1B,WAAW,KAAK,SAAS;gDACzB,WAAW,KAAK,SAAS;gDACzB,UAAU,KAAK,QAAQ;4CACzB,CAAC;wCACD,iBAAiB;4CACf,WAAW;4CACX,UAAU;4CACV,OAAO,CAAC,KAAK,EAAE,KAAK,GAAG,GAAG,YAAY,CAAC;4CACvC,OAAO;4CACP,QAAQ;4CACR,MAAM;4CACN,YAAY;4CACZ,SAAS;wCACX;wCACA,aAAa,cAAc;oCAC7B;oCAEA,QAAQ,GAAG,CAAC,wBAAwB;oCAEpC,qCAAqC;oCACrC,MAAM,gBAAgB,MAAM,MAAM,eAAe;wCAC/C,QAAQ;wCACR,SAAS;4CAAE,gBAAgB;wCAAmB;wCAC9C,MAAM,KAAK,SAAS,CAAC;oCACvB;oCAEA,IAAI,cAAc,EAAE,EAAE;wCACpB,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,MAAM,cAAc,IAAI;wCAEpD,+BAA+B;wCAC/B,MAAM,kBAAkB,MAAM,MAC5B,yBACA;4CACE,QAAQ;4CACR,SAAS;gDAAE,gBAAgB;4CAAmB;4CAC9C,MAAM,KAAK,SAAS,CAAC;gDAAE,SAAS,UAAU,EAAE;4CAAC;wCAC/C;wCAGF,MAAM,cAAc,MAAM,gBAAgB,IAAI;wCAC9C,QAAQ,GAAG,CAAC,gBAAgB;wCAE5B,IAAI,YAAY,OAAO,IAAI,YAAY,IAAI,CAAC,GAAG,EAAE;4CAC/C,QAAQ,GAAG,CAAC;4CACZ,OAAO,QAAQ,CAAC,IAAI,GAAG,YAAY,IAAI,CAAC,GAAG;wCAC7C,OAAO;4CACL,QAAQ,KAAK,CAAC,qBAAqB;4CACnC,MAAM,uBAAuB,YAAY,KAAK;wCAChD;oCACF,OAAO;wCACL,MAAM,YAAY,MAAM,cAAc,IAAI;wCAC1C,QAAQ,KAAK,CAAC,sBAAsB;wCACpC,MAAM,wBAAwB,UAAU,KAAK;oCAC/C;gCACF,EAAE,OAAO,OAAO;oCACd,QAAQ,KAAK,CAAC,kBAAkB;oCAChC,MAAM,eAAe;gCACvB;4BACF;4BACA,WAAU;sCACX;;;;;;;;;;;kCAML,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS,IAAM;gCACf,WAAU;0CACX;;;;;;0CAGD,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,SAAS;gCACT,UAAU;gCACV,WAAU;0CAET,6BACC;;sDACE,4SAAC;4CAAI,WAAU;;;;;;wCAAuE;;mDAIxF,CAAC,WAAW,EAAE,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,YAAY,CAAC,CAAC;;;;;;;;;;;;kCAI9C,4SAAC;wBAAE,WAAU;kCAAoC;;;;;;;;;;;;;;;;;;AAO3D;GA7aM;;QACoC,4KAAA,CAAA,UAAO;QACzB,8IAAA,CAAA,mBAAgB;QACJ,iIAAA,CAAA,UAAO;QACb,yIAAA,CAAA,mBAAgB;;;KAJxC", "debugId": null}}, {"offset": {"line": 6542, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/checkout/CheckoutForm.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button, Input, Select, Textarea } from \"@/components/ui\";\nimport { useCheckoutModal, useCustomer, useNotifications } from \"@/stores\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { z } from \"zod\";\n\nconst shippingSchema = z.object({\n  firstName: z.string().min(2, \"Le prénom doit contenir au moins 2 caractères\"),\n  lastName: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\"),\n  phone: z.string().min(10, \"Numéro de téléphone invalide\"),\n  address: z.string().min(5, \"Adresse trop courte\"),\n  city: z.string().min(2, \"Ville requise\"),\n  postalCode: z.string().regex(/^\\d{5}$/, \"Code postal invalide (5 chiffres)\"),\n  country: z.string().min(2, \"Pays requis\"),\n  deliveryInstructions: z.string().optional(),\n});\n\ntype ShippingFormData = z.infer<typeof shippingSchema>;\n\ninterface CheckoutFormProps {\n  onNext: () => void;\n  isProcessing: boolean;\n}\n\nconst CheckoutForm: React.FC<CheckoutFormProps> = ({\n  onNext,\n  isProcessing,\n}) => {\n  const { customer, updateCustomer } = useCustomer();\n  const { addNotification } = useNotifications();\n  const { closeModal } = useCheckoutModal();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isValid },\n    setValue,\n    watch,\n  } = useForm<ShippingFormData>({\n    resolver: zodResolver(shippingSchema),\n    defaultValues: {\n      firstName: customer?.firstName || \"\",\n      lastName: customer?.lastName || \"\",\n      email: customer?.email || \"\",\n      phone: customer?.phone || \"\",\n      address: \"\",\n      city: \"\",\n      postalCode: \"\",\n      country: \"France\",\n      deliveryInstructions: \"\",\n    },\n  });\n\n  const onSubmit = async (data: ShippingFormData) => {\n    try {\n      // Sauvegarder les informations client de base\n      updateCustomer({\n        firstName: data.firstName,\n        lastName: data.lastName,\n        email: data.email,\n        phone: data.phone,\n      });\n\n      addNotification({\n        type: \"success\",\n        title: \"Livraison\",\n        message: \"Informations de livraison sauvegardées\",\n      });\n\n      onNext();\n    } catch (error) {\n      addNotification({\n        type: \"error\",\n        title: \"Erreur\",\n        message: \"Erreur lors de la sauvegarde\",\n      });\n    }\n  };\n\n  const fillDemoData = () => {\n    setValue(\"firstName\", \"Marie\");\n    setValue(\"lastName\", \"Dupont\");\n    setValue(\"email\", \"<EMAIL>\");\n    setValue(\"phone\", \"0123456789\");\n    setValue(\"address\", \"123 Rue de la Paix\");\n    setValue(\"city\", \"Paris\");\n    setValue(\"postalCode\", \"75001\");\n    setValue(\"country\", \"France\");\n    setValue(\"deliveryInstructions\", \"Laisser devant la porte si absent\");\n  };\n\n  return (\n    <motion.form\n      onSubmit={handleSubmit(onSubmit)}\n      className=\"space-y-6\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Demo Data Button */}\n      <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n        <div className=\"flex items-center justify-between\">\n          <div>\n            <h4 className=\"font-medium text-blue-800\">Mode démonstration</h4>\n            <p className=\"text-sm text-blue-600\">\n              Remplir avec des données de test\n            </p>\n          </div>\n          <Button\n            type=\"button\"\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={fillDemoData}\n            className=\"border-blue-300 text-blue-600 hover:bg-blue-100\"\n          >\n            Remplir automatiquement\n          </Button>\n        </div>\n      </div>\n\n      {/* Personal Information */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Prénom *\n          </label>\n          <Input\n            {...register(\"firstName\")}\n            placeholder=\"Votre prénom\"\n            error={errors.firstName?.message}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Nom *\n          </label>\n          <Input\n            {...register(\"lastName\")}\n            placeholder=\"Votre nom\"\n            error={errors.lastName?.message}\n          />\n        </div>\n      </div>\n\n      {/* Contact Information */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Email *\n          </label>\n          <Input\n            {...register(\"email\")}\n            type=\"email\"\n            placeholder=\"<EMAIL>\"\n            error={errors.email?.message}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Téléphone *\n          </label>\n          <Input\n            {...register(\"phone\")}\n            type=\"tel\"\n            placeholder=\"01 23 45 67 89\"\n            error={errors.phone?.message}\n          />\n        </div>\n      </div>\n\n      {/* Address */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Adresse *\n        </label>\n        <Input\n          {...register(\"address\")}\n          placeholder=\"123 Rue de la Paix\"\n          error={errors.address?.message}\n        />\n      </div>\n\n      {/* City and Postal Code */}\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Ville *\n          </label>\n          <Input\n            {...register(\"city\")}\n            placeholder=\"Paris\"\n            error={errors.city?.message}\n          />\n        </div>\n\n        <div>\n          <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n            Code postal *\n          </label>\n          <Input\n            {...register(\"postalCode\")}\n            placeholder=\"75001\"\n            error={errors.postalCode?.message}\n          />\n        </div>\n      </div>\n\n      {/* Country */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Pays *\n        </label>\n        <Select {...register(\"country\")} error={errors.country?.message}>\n          <option value=\"France\">France</option>\n          <option value=\"Belgique\">Belgique</option>\n          <option value=\"Suisse\">Suisse</option>\n          <option value=\"Luxembourg\">Luxembourg</option>\n          <option value=\"Monaco\">Monaco</option>\n        </Select>\n      </div>\n\n      {/* Delivery Instructions */}\n      <div>\n        <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n          Instructions de livraison (optionnel)\n        </label>\n        <Textarea\n          {...register(\"deliveryInstructions\")}\n          placeholder=\"Laisser devant la porte, sonner chez le voisin, etc.\"\n          rows={3}\n        />\n      </div>\n\n      {/* Delivery Options */}\n      <div className=\"bg-gray-50 rounded-lg p-4\">\n        <h4 className=\"font-medium text-gray-800 mb-3\">Options de livraison</h4>\n        <div className=\"space-y-3\">\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"radio\"\n              name=\"deliveryOption\"\n              value=\"standard\"\n              defaultChecked\n              className=\"text-pink-500 focus:ring-pink-500\"\n            />\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"font-medium text-gray-800\">\n                  Livraison standard\n                </span>\n                <span className=\"text-green-600 font-medium\">Gratuite</span>\n              </div>\n              <p className=\"text-sm text-gray-600\">3-5 jours ouvrés</p>\n            </div>\n          </label>\n\n          <label className=\"flex items-center space-x-3 cursor-pointer\">\n            <input\n              type=\"radio\"\n              name=\"deliveryOption\"\n              value=\"express\"\n              className=\"text-pink-500 focus:ring-pink-500\"\n            />\n            <div className=\"flex-1\">\n              <div className=\"flex items-center justify-between\">\n                <span className=\"font-medium text-gray-800\">\n                  Livraison express\n                </span>\n                <span className=\"text-gray-800 font-medium\">4,99 €</span>\n              </div>\n              <p className=\"text-sm text-gray-600\">24-48h</p>\n            </div>\n          </label>\n        </div>\n      </div>\n\n      {/* Submit Button */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <button\n          type=\"button\"\n          onClick={closeModal}\n          className=\"text-gray-600 hover:text-gray-800 transition-colors\"\n        >\n          ← Retour au panier\n        </button>\n\n        <Button\n          type=\"submit\"\n          variant=\"primary\"\n          size=\"lg\"\n          disabled={!isValid || isProcessing}\n          className=\"min-w-[200px]\"\n        >\n          {isProcessing ? (\n            <span className=\"flex items-center\">\n              <motion.span\n                className=\"mr-2\"\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n              >\n                🍭\n              </motion.span>\n              Traitement...\n            </span>\n          ) : (\n            <span className=\"flex items-center\">\n              <span className=\"mr-2\">💳</span>\n              Continuer vers le paiement\n            </span>\n          )}\n        </Button>\n      </div>\n    </motion.form>\n  );\n};\n\nexport { CheckoutForm };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AAEA;AACA;;;AARA;;;;;;;AAUA,MAAM,iBAAiB,uNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC9B,WAAW,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC7B,UAAU,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,OAAO,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,OAAO,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAC1B,SAAS,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,MAAM,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,YAAY,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC,WAAW;IACxC,SAAS,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,sBAAsB,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;AAC3C;AASA,MAAM,eAA4C,CAAC,EACjD,MAAM,EACN,YAAY,EACb;;IACC,MAAM,EAAE,QAAQ,EAAE,cAAc,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC/C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IAEtC,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,OAAO,EAAE,EAC9B,QAAQ,EACR,KAAK,EACN,GAAG,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAoB;QAC5B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,WAAW,UAAU,aAAa;YAClC,UAAU,UAAU,YAAY;YAChC,OAAO,UAAU,SAAS;YAC1B,OAAO,UAAU,SAAS;YAC1B,SAAS;YACT,MAAM;YACN,YAAY;YACZ,SAAS;YACT,sBAAsB;QACxB;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,8CAA8C;YAC9C,eAAe;gBACb,WAAW,KAAK,SAAS;gBACzB,UAAU,KAAK,QAAQ;gBACvB,OAAO,KAAK,KAAK;gBACjB,OAAO,KAAK,KAAK;YACnB;YAEA,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YAEA;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;IACF;IAEA,MAAM,eAAe;QACnB,SAAS,aAAa;QACtB,SAAS,YAAY;QACrB,SAAS,SAAS;QAClB,SAAS,SAAS;QAClB,SAAS,WAAW;QACpB,SAAS,QAAQ;QACjB,SAAS,cAAc;QACvB,SAAS,WAAW;QACpB,SAAS,wBAAwB;IACnC;IAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;QACV,UAAU,aAAa;QACvB,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;;8CACC,4SAAC;oCAAG,WAAU;8CAA4B;;;;;;8CAC1C,4SAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAIvC,4SAAC,qIAAA,CAAA,SAAM;4BACL,MAAK;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;sCACX;;;;;;;;;;;;;;;;;0BAOL,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,YAAY;gCACzB,aAAY;gCACZ,OAAO,OAAO,SAAS,EAAE;;;;;;;;;;;;kCAI7B,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,WAAW;gCACxB,aAAY;gCACZ,OAAO,OAAO,QAAQ,EAAE;;;;;;;;;;;;;;;;;;0BAM9B,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,aAAY;gCACZ,OAAO,OAAO,KAAK,EAAE;;;;;;;;;;;;kCAIzB,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,QAAQ;gCACrB,MAAK;gCACL,aAAY;gCACZ,OAAO,OAAO,KAAK,EAAE;;;;;;;;;;;;;;;;;;0BAM3B,4SAAC;;kCACC,4SAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,4SAAC,oIAAA,CAAA,QAAK;wBACH,GAAG,SAAS,UAAU;wBACvB,aAAY;wBACZ,OAAO,OAAO,OAAO,EAAE;;;;;;;;;;;;0BAK3B,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,OAAO;gCACpB,aAAY;gCACZ,OAAO,OAAO,IAAI,EAAE;;;;;;;;;;;;kCAIxB,4SAAC;;0CACC,4SAAC;gCAAM,WAAU;0CAA+C;;;;;;0CAGhE,4SAAC,oIAAA,CAAA,QAAK;gCACH,GAAG,SAAS,aAAa;gCAC1B,aAAY;gCACZ,OAAO,OAAO,UAAU,EAAE;;;;;;;;;;;;;;;;;;0BAMhC,4SAAC;;kCACC,4SAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,4SAAC,qIAAA,CAAA,SAAM;wBAAE,GAAG,SAAS,UAAU;wBAAE,OAAO,OAAO,OAAO,EAAE;;0CACtD,4SAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,4SAAC;gCAAO,OAAM;0CAAW;;;;;;0CACzB,4SAAC;gCAAO,OAAM;0CAAS;;;;;;0CACvB,4SAAC;gCAAO,OAAM;0CAAa;;;;;;0CAC3B,4SAAC;gCAAO,OAAM;0CAAS;;;;;;;;;;;;;;;;;;0BAK3B,4SAAC;;kCACC,4SAAC;wBAAM,WAAU;kCAA+C;;;;;;kCAGhE,4SAAC,oIAAA,CAAA,WAAQ;wBACN,GAAG,SAAS,uBAAuB;wBACpC,aAAY;wBACZ,MAAM;;;;;;;;;;;;0BAKV,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAC/C,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAM,WAAU;;kDACf,4SAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,cAAc;wCACd,WAAU;;;;;;kDAEZ,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAG5C,4SAAC;wDAAK,WAAU;kEAA6B;;;;;;;;;;;;0DAE/C,4SAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;0CAIzC,4SAAC;gCAAM,WAAU;;kDACf,4SAAC;wCACC,MAAK;wCACL,MAAK;wCACL,OAAM;wCACN,WAAU;;;;;;kDAEZ,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAK,WAAU;kEAA4B;;;;;;kEAG5C,4SAAC;wDAAK,WAAU;kEAA4B;;;;;;;;;;;;0DAE9C,4SAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO7C,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBACC,MAAK;wBACL,SAAS;wBACT,WAAU;kCACX;;;;;;kCAID,4SAAC,qIAAA,CAAA,SAAM;wBACL,MAAK;wBACL,SAAQ;wBACR,MAAK;wBACL,UAAU,CAAC,WAAW;wBACtB,WAAU;kCAET,6BACC,4SAAC;4BAAK,WAAU;;8CACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;8CAC7D;;;;;;gCAEa;;;;;;iDAIhB,4SAAC;4BAAK,WAAU;;8CACd,4SAAC;oCAAK,WAAU;8CAAO;;;;;;gCAAS;;;;;;;;;;;;;;;;;;;;;;;;AAQ9C;GApSM;;QAIiC,qIAAA,CAAA,cAAW;QACpB,yIAAA,CAAA,mBAAgB;QACrB,8IAAA,CAAA,mBAAgB;QAQnC,0PAAA,CAAA,UAAO;;;KAdP", "debugId": null}}, {"offset": {"line": 7243, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/checkout/CheckoutProgress.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\nexport type CheckoutStep = \"shipping\" | \"payment\" | \"confirmation\";\n\ninterface CheckoutProgressProps {\n  currentStep: CheckoutStep;\n}\n\nconst CheckoutProgress: React.FC<CheckoutProgressProps> = ({ currentStep }) => {\n  const steps = [\n    {\n      id: \"shipping\" as CheckoutStep,\n      name: \"<PERSON><PERSON><PERSON>\",\n      icon: \"📦\",\n      description: \"Adresse de livraison\",\n    },\n    {\n      id: \"payment\" as CheckoutStep,\n      name: \"<PERSON>ie<PERSON>\",\n      icon: \"💳\",\n      description: \"Méthode de paiement\",\n    },\n    {\n      id: \"confirmation\" as CheckoutStep,\n      name: \"Confirmation\",\n      icon: \"✅\",\n      description: \"Commande confirmée\",\n    },\n  ];\n\n  const getCurrentStepIndex = () => {\n    return steps.findIndex((step) => step.id === currentStep);\n  };\n\n  const currentStepIndex = getCurrentStepIndex();\n\n  return (\n    <div className=\"w-full\">\n      {/* Progress Bar */}\n      <div className=\"relative\">\n        {/* Background Line */}\n        <div className=\"absolute top-1/2 left-0 right-0 h-1 bg-gray-200 rounded-full transform -translate-y-1/2\" />\n\n        {/* Progress Line */}\n        <motion.div\n          className=\"absolute top-1/2 left-0 h-1 bg-gradient-to-r from-pink-500 to-orange-500 rounded-full transform -translate-y-1/2\"\n          initial={{ width: \"0%\" }}\n          animate={{\n            width:\n              currentStepIndex === 0\n                ? \"0%\"\n                : currentStepIndex === 1\n                ? \"50%\"\n                : \"100%\",\n          }}\n          transition={{ duration: 0.5, ease: \"easeInOut\" }}\n        />\n\n        {/* Steps */}\n        <div className=\"relative flex justify-between\">\n          {steps.map((step, index) => {\n            const isCompleted = index < currentStepIndex;\n            const isCurrent = index === currentStepIndex;\n            const isUpcoming = index > currentStepIndex;\n\n            return (\n              <motion.div\n                key={step.id}\n                className=\"flex flex-col items-center\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: index * 0.1 }}\n              >\n                {/* Step Circle */}\n                <motion.div\n                  className={`\n                    relative w-12 h-12 rounded-full flex items-center justify-center text-lg font-semibold\n                    ${\n                      isCompleted\n                        ? \"bg-gradient-to-r from-pink-500 to-orange-500 text-white\"\n                        : isCurrent\n                        ? \"bg-white border-2 border-pink-500 text-pink-500 shadow-lg\"\n                        : \"bg-gray-200 text-gray-400\"\n                    }\n                  `}\n                  whileHover={{ scale: 1.05 }}\n                  animate={\n                    isCurrent\n                      ? {\n                          boxShadow: [\n                            \"0 0 0 0 rgba(236, 72, 153, 0.4)\",\n                            \"0 0 0 10px rgba(236, 72, 153, 0)\",\n                            \"0 0 0 0 rgba(236, 72, 153, 0)\",\n                          ],\n                        }\n                      : {}\n                  }\n                  transition={{\n                    boxShadow: { duration: 2, repeat: Infinity },\n                    scale: { duration: 0.2 },\n                  }}\n                >\n                  {isCompleted ? (\n                    <motion.span\n                      initial={{ scale: 0 }}\n                      animate={{ scale: 1 }}\n                      transition={{ delay: 0.2 }}\n                    >\n                      ✓\n                    </motion.span>\n                  ) : (\n                    <motion.span\n                      animate={\n                        isCurrent\n                          ? {\n                              scale: [1, 1.2, 1],\n                              rotate: [0, 5, -5, 0],\n                            }\n                          : {}\n                      }\n                      transition={{ duration: 2, repeat: Infinity }}\n                    >\n                      {step.icon}\n                    </motion.span>\n                  )}\n\n                  {/* Current Step Indicator */}\n                  {isCurrent && (\n                    <motion.div\n                      className=\"absolute -inset-1 rounded-full border-2 border-pink-300\"\n                      animate={{\n                        scale: [1, 1.2, 1],\n                        opacity: [0.5, 1, 0.5],\n                      }}\n                      transition={{ duration: 2, repeat: Infinity }}\n                    />\n                  )}\n                </motion.div>\n\n                {/* Step Info */}\n                <motion.div\n                  className=\"mt-3 text-center\"\n                  initial={{ opacity: 0 }}\n                  animate={{ opacity: 1 }}\n                  transition={{ delay: index * 0.1 + 0.2 }}\n                >\n                  <h4\n                    className={`\n                    text-sm font-semibold\n                    ${\n                      isCompleted || isCurrent\n                        ? \"text-gray-800\"\n                        : \"text-gray-400\"\n                    }\n                  `}\n                  >\n                    {step.name}\n                  </h4>\n                  <p\n                    className={`\n                    text-xs mt-1\n                    ${\n                      isCompleted || isCurrent\n                        ? \"text-gray-600\"\n                        : \"text-gray-400\"\n                    }\n                  `}\n                  >\n                    {step.description}\n                  </p>\n                </motion.div>\n\n                {/* Step Number for Mobile */}\n                <div className=\"md:hidden mt-1\">\n                  <span\n                    className={`\n                    text-xs px-2 py-1 rounded-full\n                    ${\n                      isCompleted\n                        ? \"bg-green-100 text-green-600\"\n                        : isCurrent\n                        ? \"bg-pink-100 text-pink-600\"\n                        : \"bg-gray-100 text-gray-400\"\n                    }\n                  `}\n                  >\n                    {index + 1}/3\n                  </span>\n                </div>\n              </motion.div>\n            );\n          })}\n        </div>\n      </div>\n\n      {/* Mobile Progress Text */}\n      <motion.div\n        className=\"md:hidden mt-6 text-center\"\n        initial={{ opacity: 0 }}\n        animate={{ opacity: 1 }}\n        transition={{ delay: 0.5 }}\n      >\n        <div className=\"bg-gray-50 rounded-lg p-4\">\n          <h3 className=\"font-semibold text-gray-800 mb-1\">\n            Étape {currentStepIndex + 1} sur {steps.length}\n          </h3>\n          <p className=\"text-sm text-gray-600\">\n            {steps[currentStepIndex].description}\n          </p>\n\n          {/* Progress Percentage */}\n          <div className=\"mt-3\">\n            <div className=\"flex justify-between text-xs text-gray-500 mb-1\">\n              <span>Progression</span>\n              <span>\n                {Math.round(((currentStepIndex + 1) / steps.length) * 100)}%\n              </span>\n            </div>\n            <div className=\"w-full bg-gray-200 rounded-full h-2\">\n              <motion.div\n                className=\"bg-gradient-to-r from-pink-500 to-orange-500 h-2 rounded-full\"\n                initial={{ width: \"0%\" }}\n                animate={{\n                  width: `${((currentStepIndex + 1) / steps.length) * 100}%`,\n                }}\n                transition={{ duration: 0.5, ease: \"easeInOut\" }}\n              />\n            </div>\n          </div>\n        </div>\n      </motion.div>\n\n      {/* Estimated Time */}\n      <motion.div\n        className=\"hidden md:block mt-6 text-center\"\n        initial={{ opacity: 0, y: 10 }}\n        animate={{ opacity: 1, y: 0 }}\n        transition={{ delay: 0.6 }}\n      >\n        <p className=\"text-sm text-gray-500\">\n          ⏱️ Temps estimé restant :{\" \"}\n          {currentStep === \"shipping\"\n            ? \"2-3 minutes\"\n            : currentStep === \"payment\"\n            ? \"1-2 minutes\"\n            : \"Terminé !\"}\n        </p>\n      </motion.div>\n    </div>\n  );\n};\n\nexport { CheckoutProgress };\n"], "names": [], "mappings": ";;;;AAEA;AAFA;;;AAUA,MAAM,mBAAoD,CAAC,EAAE,WAAW,EAAE;IACxE,MAAM,QAAQ;QACZ;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;QACf;KACD;IAED,MAAM,sBAAsB;QAC1B,OAAO,MAAM,SAAS,CAAC,CAAC,OAAS,KAAK,EAAE,KAAK;IAC/C;IAEA,MAAM,mBAAmB;IAEzB,qBACE,4SAAC;QAAI,WAAU;;0BAEb,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC;wBAAI,WAAU;;;;;;kCAGf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAK;wBACvB,SAAS;4BACP,OACE,qBAAqB,IACjB,OACA,qBAAqB,IACrB,QACA;wBACR;wBACA,YAAY;4BAAE,UAAU;4BAAK,MAAM;wBAAY;;;;;;kCAIjD,4SAAC;wBAAI,WAAU;kCACZ,MAAM,GAAG,CAAC,CAAC,MAAM;4BAChB,MAAM,cAAc,QAAQ;4BAC5B,MAAM,YAAY,UAAU;4BAC5B,MAAM,aAAa,QAAQ;4BAE3B,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO,QAAQ;gCAAI;;kDAGjC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAW,CAAC;;oBAEV,EACE,cACI,4DACA,YACA,8DACA,4BACL;kBACH,CAAC;wCACD,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,SACE,YACI;4CACE,WAAW;gDACT;gDACA;gDACA;6CACD;wCACH,IACA,CAAC;wCAEP,YAAY;4CACV,WAAW;gDAAE,UAAU;gDAAG,QAAQ;4CAAS;4CAC3C,OAAO;gDAAE,UAAU;4CAAI;wCACzB;;4CAEC,4BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SAAS;oDAAE,OAAO;gDAAE;gDACpB,SAAS;oDAAE,OAAO;gDAAE;gDACpB,YAAY;oDAAE,OAAO;gDAAI;0DAC1B;;;;;qEAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;gDACV,SACE,YACI;oDACE,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,QAAQ;wDAAC;wDAAG;wDAAG,CAAC;wDAAG;qDAAE;gDACvB,IACA,CAAC;gDAEP,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;0DAE3C,KAAK,IAAI;;;;;;4CAKb,2BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,OAAO;wDAAC;wDAAG;wDAAK;qDAAE;oDAClB,SAAS;wDAAC;wDAAK;wDAAG;qDAAI;gDACxB;gDACA,YAAY;oDAAE,UAAU;oDAAG,QAAQ;gDAAS;;;;;;;;;;;;kDAMlD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;wCAAE;wCACtB,SAAS;4CAAE,SAAS;wCAAE;wCACtB,YAAY;4CAAE,OAAO,QAAQ,MAAM;wCAAI;;0DAEvC,4SAAC;gDACC,WAAW,CAAC;;oBAEZ,EACE,eAAe,YACX,kBACA,gBACL;kBACH,CAAC;0DAEE,KAAK,IAAI;;;;;;0DAEZ,4SAAC;gDACC,WAAW,CAAC;;oBAEZ,EACE,eAAe,YACX,kBACA,gBACL;kBACH,CAAC;0DAEE,KAAK,WAAW;;;;;;;;;;;;kDAKrB,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC;4CACC,WAAW,CAAC;;oBAEZ,EACE,cACI,gCACA,YACA,8BACA,4BACL;kBACH,CAAC;;gDAEE,QAAQ;gDAAE;;;;;;;;;;;;;+BAvHV,KAAK,EAAE;;;;;wBA4HlB;;;;;;;;;;;;0BAKJ,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAG,WAAU;;gCAAmC;gCACxC,mBAAmB;gCAAE;gCAAM,MAAM,MAAM;;;;;;;sCAEhD,4SAAC;4BAAE,WAAU;sCACV,KAAK,CAAC,iBAAiB,CAAC,WAAW;;;;;;sCAItC,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;sDAAK;;;;;;sDACN,4SAAC;;gDACE,KAAK,KAAK,CAAC,AAAC,CAAC,mBAAmB,CAAC,IAAI,MAAM,MAAM,GAAI;gDAAK;;;;;;;;;;;;;8CAG/D,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAK;wCACvB,SAAS;4CACP,OAAO,GAAG,AAAC,CAAC,mBAAmB,CAAC,IAAI,MAAM,MAAM,GAAI,IAAI,CAAC,CAAC;wCAC5D;wCACA,YAAY;4CAAE,UAAU;4CAAK,MAAM;wCAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAQzD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAG;gBAC7B,SAAS;oBAAE,SAAS;oBAAG,GAAG;gBAAE;gBAC5B,YAAY;oBAAE,OAAO;gBAAI;0BAEzB,cAAA,4SAAC;oBAAE,WAAU;;wBAAwB;wBACT;wBACzB,gBAAgB,aACb,gBACA,gBAAgB,YAChB,gBACA;;;;;;;;;;;;;;;;;;AAKd;KAlPM", "debugId": null}}, {"offset": {"line": 7653, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/checkout/OrderSummary.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, CardContent, CardHeader } from \"@/components/ui\";\nimport { formatPrice } from \"@/lib/utils\";\nimport { useCart } from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nconst OrderSummary: React.FC = () => {\n  const { cart } = useCart();\n\n  // Configuration des saveurs\n  const flavorConfig = {\n    strawberry: { emoji: \"🍓\", color: \"text-pink-600\", bg: \"bg-pink-50\" },\n    blueberry: { emoji: \"🫐\", color: \"text-blue-600\", bg: \"bg-blue-50\" },\n    apple: { emoji: \"🍏\", color: \"text-green-600\", bg: \"bg-green-50\" },\n  };\n\n  // Calculer les valeurs dérivées\n  const subtotal = cart.totalAmount;\n  const tax = subtotal * 0.2; // TVA 20%\n  const shipping = subtotal >= 50 ? 0 : 5.99; // Livraison gratuite à partir de 50€\n  const total = subtotal + tax + shipping;\n  const totalSavings = 0; // Simplifié car pas de prix originaux\n\n  return (\n    <Card>\n      <CardHeader>\n        <h3 className=\"text-xl font-semibold text-gray-800\">\n          Récapitulatif de commande\n        </h3>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        {/* Order Items */}\n        <div className=\"space-y-4\">\n          <h4 className=\"font-medium text-gray-800 border-b border-gray-200 pb-2\">\n            Articles commandés ({cart.totalItems})\n          </h4>\n\n          <div className=\"space-y-3 max-h-60 overflow-y-auto\">\n            {cart.items.map((item) => {\n              const flavor = flavorConfig[\n                item.flavor.toLowerCase() as keyof typeof flavorConfig\n              ] || {\n                emoji: \"🍭\",\n                color: \"text-gray-600\",\n                bg: \"bg-gray-50\",\n              };\n\n              return (\n                <motion.div\n                  key={item.id}\n                  className=\"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg\"\n                  initial={{ opacity: 0, y: 10 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  transition={{ duration: 0.2 }}\n                >\n                  {/* Product Icon */}\n                  <div\n                    className={`w-10 h-10 rounded-lg ${flavor.bg} flex items-center justify-center text-lg`}\n                  >\n                    {flavor.emoji}\n                  </div>\n\n                  {/* Product Info */}\n                  <div className=\"flex-1 min-w-0\">\n                    <h5 className=\"font-medium text-gray-800 text-sm truncate\">\n                      {item.name}\n                    </h5>\n                    <div className=\"flex items-center space-x-2 mt-1\">\n                      <Badge\n                        variant=\"secondary\"\n                        size=\"sm\"\n                        className={`${flavor.color} border-current text-xs`}\n                      >\n                        {item.flavor}\n                      </Badge>\n                      <span className=\"text-xs text-gray-500\">\n                        Qté: {item.quantity}\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* Price */}\n                  <div className=\"text-right\">\n                    <div className=\"font-medium text-gray-800 text-sm\">\n                      {formatPrice(item.price * item.quantity)}\n                    </div>\n                  </div>\n                </motion.div>\n              );\n            })}\n          </div>\n        </div>\n\n        {/* Price Breakdown */}\n        <div className=\"space-y-3 border-t border-gray-200 pt-4\">\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Sous-total</span>\n            <span className=\"font-medium\">{formatPrice(subtotal)}</span>\n          </div>\n\n          {totalSavings > 0 && (\n            <div className=\"flex justify-between text-sm text-green-600\">\n              <span>Économies</span>\n              <span className=\"font-medium\">-{formatPrice(totalSavings)}</span>\n            </div>\n          )}\n\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Livraison</span>\n            <span className=\"font-medium\">\n              {shipping === 0 ? (\n                <span className=\"text-green-600\">Gratuite</span>\n              ) : (\n                formatPrice(shipping)\n              )}\n            </span>\n          </div>\n\n          {tax > 0 && (\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600\">TVA (20%)</span>\n              <span className=\"font-medium\">{formatPrice(tax)}</span>\n            </div>\n          )}\n        </div>\n\n        {/* Total */}\n        <div className=\"border-t border-gray-200 pt-4\">\n          <div className=\"flex justify-between text-lg font-bold\">\n            <span>Total à payer</span>\n            <motion.span\n              key={total}\n              className=\"text-pink-600\"\n              initial={{ scale: 1.1 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.2 }}\n            >\n              {formatPrice(total)}\n            </motion.span>\n          </div>\n        </div>\n\n        {/* Delivery Info */}\n        <div className=\"bg-blue-50 border border-blue-200 rounded-lg p-4\">\n          <div className=\"flex items-start space-x-3\">\n            <span className=\"text-blue-600 text-lg\">🚚</span>\n            <div>\n              <h4 className=\"font-medium text-blue-800 text-sm\">\n                Livraison estimée\n              </h4>\n              <p className=\"text-blue-600 text-sm\">\n                {shipping === 0 ? \"3-5 jours ouvrés\" : \"24-48h\"}\n              </p>\n              <p className=\"text-blue-600 text-xs mt-1\">\n                Suivi de commande par email\n              </p>\n            </div>\n          </div>\n        </div>\n\n        {/* Security Badges */}\n        <div className=\"grid grid-cols-2 gap-3 text-center text-xs text-gray-500\">\n          <div className=\"flex flex-col items-center\">\n            <span className=\"text-lg mb-1\">🔒</span>\n            <span>Paiement sécurisé</span>\n          </div>\n          <div className=\"flex flex-col items-center\">\n            <span className=\"text-lg mb-1\">↩️</span>\n            <span>Retour gratuit</span>\n          </div>\n        </div>\n\n        {/* Loyalty Points Preview */}\n        <div className=\"bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg p-3\">\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <p className=\"text-sm font-medium text-gray-800\">\n                Points de fidélité\n              </p>\n              <p className=\"text-xs text-gray-600\">\n                Vous gagnerez {Math.floor(total / 100)} points\n              </p>\n            </div>\n            <Badge variant=\"info\" size=\"sm\">\n              +{Math.floor(total / 100)} 🎁\n            </Badge>\n          </div>\n        </div>\n\n        {/* Contact Support */}\n        <div className=\"text-center\">\n          <p className=\"text-xs text-gray-500 mb-2\">\n            Une question sur votre commande ?\n          </p>\n          <button className=\"text-xs text-pink-600 hover:text-pink-700 font-medium\">\n            Contacter le support 💬\n          </button>\n        </div>\n      </CardContent>\n    </Card>\n  );\n};\n\nexport { OrderSummary };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AACA;;;AALA;;;;;AAQA,MAAM,eAAyB;;IAC7B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IAEvB,4BAA4B;IAC5B,MAAM,eAAe;QACnB,YAAY;YAAE,OAAO;YAAM,OAAO;YAAiB,IAAI;QAAa;QACpE,WAAW;YAAE,OAAO;YAAM,OAAO;YAAiB,IAAI;QAAa;QACnE,OAAO;YAAE,OAAO;YAAM,OAAO;YAAkB,IAAI;QAAc;IACnE;IAEA,gCAAgC;IAChC,MAAM,WAAW,KAAK,WAAW;IACjC,MAAM,MAAM,WAAW,KAAK,UAAU;IACtC,MAAM,WAAW,YAAY,KAAK,IAAI,MAAM,qCAAqC;IACjF,MAAM,QAAQ,WAAW,MAAM;IAC/B,MAAM,eAAe,GAAG,sCAAsC;IAE9D,qBACE,4SAAC,mIAAA,CAAA,OAAI;;0BACH,4SAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,4SAAC;oBAAG,WAAU;8BAAsC;;;;;;;;;;;0BAKtD,4SAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAG,WAAU;;oCAA0D;oCACjD,KAAK,UAAU;oCAAC;;;;;;;0CAGvC,4SAAC;gCAAI,WAAU;0CACZ,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC;oCACf,MAAM,SAAS,YAAY,CACzB,KAAK,MAAM,CAAC,WAAW,GACxB,IAAI;wCACH,OAAO;wCACP,OAAO;wCACP,IAAI;oCACN;oCAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,UAAU;wCAAI;;0DAG5B,4SAAC;gDACC,WAAW,CAAC,qBAAqB,EAAE,OAAO,EAAE,CAAC,yCAAyC,CAAC;0DAEtF,OAAO,KAAK;;;;;;0DAIf,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAG,WAAU;kEACX,KAAK,IAAI;;;;;;kEAEZ,4SAAC;wDAAI,WAAU;;0EACb,4SAAC,oIAAA,CAAA,QAAK;gEACJ,SAAQ;gEACR,MAAK;gEACL,WAAW,GAAG,OAAO,KAAK,CAAC,uBAAuB,CAAC;0EAElD,KAAK,MAAM;;;;;;0EAEd,4SAAC;gEAAK,WAAU;;oEAAwB;oEAChC,KAAK,QAAQ;;;;;;;;;;;;;;;;;;;0DAMzB,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDAAI,WAAU;8DACZ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK,GAAG,KAAK,QAAQ;;;;;;;;;;;;uCAnCtC,KAAK,EAAE;;;;;gCAwClB;;;;;;;;;;;;kCAKJ,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,4SAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;4BAG5C,eAAe,mBACd,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;kDAAK;;;;;;kDACN,4SAAC;wCAAK,WAAU;;4CAAc;4CAAE,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;0CAIhD,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,4SAAC;wCAAK,WAAU;kDACb,aAAa,kBACZ,4SAAC;4CAAK,WAAU;sDAAiB;;;;;mDAEjC,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;4BAKjB,MAAM,mBACL,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,4SAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;kCAMjD,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;8CAAK;;;;;;8CACN,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;oCAEV,WAAU;oCACV,SAAS;wCAAE,OAAO;oCAAI;oCACtB,SAAS;wCAAE,OAAO;oCAAE;oCACpB,YAAY;wCAAE,UAAU;oCAAI;8CAE3B,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;mCANR;;;;;;;;;;;;;;;;kCAYX,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAK,WAAU;8CAAwB;;;;;;8CACxC,4SAAC;;sDACC,4SAAC;4CAAG,WAAU;sDAAoC;;;;;;sDAGlD,4SAAC;4CAAE,WAAU;sDACV,aAAa,IAAI,qBAAqB;;;;;;sDAEzC,4SAAC;4CAAE,WAAU;sDAA6B;;;;;;;;;;;;;;;;;;;;;;;kCAQhD,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,4SAAC;kDAAK;;;;;;;;;;;;0CAER,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,4SAAC;kDAAK;;;;;;;;;;;;;;;;;;kCAKV,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;;sDACC,4SAAC;4CAAE,WAAU;sDAAoC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;;gDAAwB;gDACpB,KAAK,KAAK,CAAC,QAAQ;gDAAK;;;;;;;;;;;;;8CAG3C,4SAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,MAAK;;wCAAK;wCAC5B,KAAK,KAAK,CAAC,QAAQ;wCAAK;;;;;;;;;;;;;;;;;;kCAMhC,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAE,WAAU;0CAA6B;;;;;;0CAG1C,4SAAC;gCAAO,WAAU;0CAAwD;;;;;;;;;;;;;;;;;;;;;;;;AAOpF;GApMM;;QACa,4KAAA,CAAA,UAAO;;;KADpB", "debugId": null}}, {"offset": {"line": 8226, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/checkout/PaymentMethods.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, Card, CardContent } from \"@/components/ui\";\nimport { formatPrice } from \"@/lib/utils\";\nimport { useCart, useCustomer, useNotifications } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\ninterface PaymentMethodsProps {\n  onBack: () => void;\n  onSuccess: () => void;\n  isProcessing: boolean;\n  setIsProcessing: (processing: boolean) => void;\n}\n\ntype PaymentMethod = \"card\" | \"paypal\" | \"apple_pay\" | \"google_pay\";\n\nconst PaymentMethods: React.FC<PaymentMethodsProps> = ({\n  onBack,\n  onSuccess,\n  isProcessing,\n  setIsProcessing,\n}) => {\n  const { cart, clearCart } = useCart();\n  const { customer } = useCustomer();\n  const { addNotification } = useNotifications();\n  const [selectedMethod, setSelectedMethod] = useState<PaymentMethod>(\"card\");\n  const [cardDetails, setCardDetails] = useState({\n    number: \"\",\n    expiry: \"\",\n    cvc: \"\",\n    name: \"\",\n  });\n\n  const paymentMethods = [\n    {\n      id: \"card\" as PaymentMethod,\n      name: \"Carte bancaire\",\n      icon: \"💳\",\n      description: \"Visa, Mastercard, American Express\",\n      available: true,\n    },\n    {\n      id: \"paypal\" as PaymentMethod,\n      name: \"PayPal\",\n      icon: \"🅿️\",\n      description: \"Paiement sécurisé avec PayPal\",\n      available: true,\n    },\n    {\n      id: \"apple_pay\" as PaymentMethod,\n      name: \"Apple Pay\",\n      icon: \"🍎\",\n      description: \"Paiement rapide avec Touch ID\",\n      available: false,\n    },\n    {\n      id: \"google_pay\" as PaymentMethod,\n      name: \"Google Pay\",\n      icon: \"🔵\",\n      description: \"Paiement rapide avec Google\",\n      available: false,\n    },\n  ];\n\n  const handleCardInputChange = (\n    field: keyof typeof cardDetails,\n    value: string\n  ) => {\n    let formattedValue = value;\n\n    if (field === \"number\") {\n      // Format card number with spaces\n      formattedValue = value\n        .replace(/\\s/g, \"\")\n        .replace(/(.{4})/g, \"$1 \")\n        .trim();\n      if (formattedValue.length > 19)\n        formattedValue = formattedValue.slice(0, 19);\n    } else if (field === \"expiry\") {\n      // Format expiry as MM/YY\n      formattedValue = value.replace(/\\D/g, \"\").replace(/(\\d{2})(\\d)/, \"$1/$2\");\n      if (formattedValue.length > 5)\n        formattedValue = formattedValue.slice(0, 5);\n    } else if (field === \"cvc\") {\n      // Only numbers, max 4 digits\n      formattedValue = value.replace(/\\D/g, \"\").slice(0, 4);\n    }\n\n    setCardDetails((prev) => ({ ...prev, [field]: formattedValue }));\n  };\n\n  const fillDemoCardData = () => {\n    setCardDetails({\n      number: \"4242 4242 4242 4242\",\n      expiry: \"12/25\",\n      cvc: \"123\",\n      name:\n        customer?.firstName && customer?.lastName\n          ? `${customer.firstName} ${customer.lastName}`\n          : \"Marie Dupont\",\n    });\n  };\n\n  const validateCardDetails = () => {\n    const { number, expiry, cvc, name } = cardDetails;\n\n    if (!number || number.replace(/\\s/g, \"\").length < 16) {\n      addNotification({\n        type: \"error\",\n        title: \"Paiement\",\n        message: \"Numéro de carte invalide\",\n      });\n      return false;\n    }\n\n    if (!expiry || expiry.length < 5) {\n      addNotification({\n        type: \"error\",\n        title: \"Paiement\",\n        message: \"Date d'expiration invalide\",\n      });\n      return false;\n    }\n\n    if (!cvc || cvc.length < 3) {\n      addNotification({\n        type: \"error\",\n        title: \"Paiement\",\n        message: \"Code CVC invalide\",\n      });\n      return false;\n    }\n\n    if (!name.trim()) {\n      addNotification({\n        type: \"error\",\n        title: \"Paiement\",\n        message: \"Nom du titulaire requis\",\n      });\n      return false;\n    }\n\n    return true;\n  };\n\n  const processPayment = async () => {\n    if (selectedMethod === \"card\" && !validateCardDetails()) {\n      return;\n    }\n\n    setIsProcessing(true);\n\n    try {\n      // Vérifier que le panier n'est pas vide\n      if (cart.items.length === 0) {\n        addNotification({\n          type: \"error\",\n          title: \"Panier vide\",\n          message:\n            \"Votre panier est vide. Ajoutez des produits avant de procéder au paiement.\",\n        });\n        return;\n      }\n\n      // Créer la commande avec les vrais articles du panier\n      const orderResponse = await fetch(\"/api/orders\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          // Ne pas envoyer customerId s'il n'existe pas\n          ...(customer?.id && { customerId: customer.id }),\n          items: cart.items.map((item) => ({\n            productId: item.productId,\n            variantId: item.variantId,\n            quantity: item.quantity,\n          })),\n          shippingAddress: {\n            firstName: customer?.firstName || \"Client\",\n            lastName: customer?.lastName || \"Deltagum\",\n            email: customer?.email || \"<EMAIL>\",\n            phone: customer?.phone || \"0123456789\",\n            street: customer?.address || \"123 Rue de la Livraison\",\n            city: customer?.city || \"Paris\",\n            postalCode: customer?.postalCode || \"75001\",\n            country: \"France\",\n          },\n          totalAmount: cart.totalAmount,\n        }),\n      });\n\n      if (!orderResponse.ok) {\n        throw new Error(\"Erreur lors de la création de la commande\");\n      }\n\n      const { order } = await orderResponse.json();\n\n      // Créer la session de checkout Stripe\n      const checkoutResponse = await fetch(\"/api/checkout/session\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify({\n          orderId: order.id,\n        }),\n      });\n\n      if (!checkoutResponse.ok) {\n        throw new Error(\"Erreur lors de la création de la session de paiement\");\n      }\n\n      const { data } = await checkoutResponse.json();\n\n      // Rediriger vers Stripe Checkout\n      if (data.url) {\n        // Sauvegarder les informations du panier pour les restaurer en cas d'annulation\n        localStorage.setItem(\n          \"deltagum_pending_order\",\n          JSON.stringify({\n            orderId: order.id,\n            cartItems: cart.items,\n            timestamp: Date.now(),\n          })\n        );\n\n        // Rediriger vers Stripe\n        window.location.href = data.url;\n      } else {\n        throw new Error(\"URL de paiement non reçue\");\n      }\n    } catch (error) {\n      console.error(\"Erreur de paiement:\", error);\n\n      addNotification({\n        type: \"error\",\n        title: \"Erreur de paiement\",\n        message:\n          error instanceof Error\n            ? error.message\n            : \"Une erreur est survenue lors du paiement\",\n      });\n\n      setIsProcessing(false);\n    }\n  };\n\n  return (\n    <motion.div\n      className=\"space-y-6\"\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      transition={{ duration: 0.3 }}\n    >\n      {/* Payment Method Selection */}\n      <div>\n        <h4 className=\"font-medium text-gray-800 mb-4\">\n          Choisissez votre méthode de paiement\n        </h4>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n          {paymentMethods.map((method) => (\n            <motion.button\n              key={method.id}\n              onClick={() => method.available && setSelectedMethod(method.id)}\n              disabled={!method.available}\n              className={`\n                p-4 rounded-lg border-2 text-left transition-all duration-200\n                ${\n                  selectedMethod === method.id\n                    ? \"border-pink-500 bg-pink-50\"\n                    : method.available\n                    ? \"border-gray-200 hover:border-gray-300 bg-white\"\n                    : \"border-gray-100 bg-gray-50 opacity-50 cursor-not-allowed\"\n                }\n              `}\n              whileHover={method.available ? { scale: 1.02 } : {}}\n              whileTap={method.available ? { scale: 0.98 } : {}}\n            >\n              <div className=\"flex items-center space-x-3\">\n                <span className=\"text-2xl\">{method.icon}</span>\n                <div className=\"flex-1\">\n                  <h5 className=\"font-medium text-gray-800\">{method.name}</h5>\n                  <p className=\"text-sm text-gray-600\">{method.description}</p>\n                  {!method.available && (\n                    <p className=\"text-xs text-gray-400 mt-1\">\n                      Bientôt disponible\n                    </p>\n                  )}\n                </div>\n                {selectedMethod === method.id && (\n                  <motion.div\n                    className=\"w-5 h-5 bg-pink-500 rounded-full flex items-center justify-center\"\n                    initial={{ scale: 0 }}\n                    animate={{ scale: 1 }}\n                    transition={{ type: \"spring\", stiffness: 300 }}\n                  >\n                    <span className=\"text-white text-xs\">✓</span>\n                  </motion.div>\n                )}\n              </div>\n            </motion.button>\n          ))}\n        </div>\n      </div>\n\n      {/* Payment Details */}\n      <AnimatePresence mode=\"wait\">\n        {selectedMethod === \"card\" && (\n          <motion.div\n            key=\"card-form\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Card>\n              <CardContent className=\"p-6\">\n                <div className=\"flex items-center justify-between mb-4\">\n                  <h4 className=\"font-medium text-gray-800\">\n                    Informations de carte\n                  </h4>\n                  <Button\n                    type=\"button\"\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={fillDemoCardData}\n                    className=\"text-xs\"\n                  >\n                    Données de test\n                  </Button>\n                </div>\n\n                <div className=\"space-y-4\">\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Numéro de carte\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={cardDetails.number}\n                      onChange={(e) =>\n                        handleCardInputChange(\"number\", e.target.value)\n                      }\n                      placeholder=\"1234 5678 9012 3456\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    />\n                  </div>\n\n                  <div className=\"grid grid-cols-2 gap-4\">\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        Date d'expiration\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={cardDetails.expiry}\n                        onChange={(e) =>\n                          handleCardInputChange(\"expiry\", e.target.value)\n                        }\n                        placeholder=\"MM/YY\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      />\n                    </div>\n\n                    <div>\n                      <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                        CVC\n                      </label>\n                      <input\n                        type=\"text\"\n                        value={cardDetails.cvc}\n                        onChange={(e) =>\n                          handleCardInputChange(\"cvc\", e.target.value)\n                        }\n                        placeholder=\"123\"\n                        className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                      />\n                    </div>\n                  </div>\n\n                  <div>\n                    <label className=\"block text-sm font-medium text-gray-700 mb-2\">\n                      Nom du titulaire\n                    </label>\n                    <input\n                      type=\"text\"\n                      value={cardDetails.name}\n                      onChange={(e) =>\n                        handleCardInputChange(\"name\", e.target.value)\n                      }\n                      placeholder=\"Marie Dupont\"\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-transparent\"\n                    />\n                  </div>\n                </div>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n\n        {selectedMethod === \"paypal\" && (\n          <motion.div\n            key=\"paypal-info\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            exit={{ opacity: 0, height: 0 }}\n            transition={{ duration: 0.3 }}\n          >\n            <Card>\n              <CardContent className=\"p-6 text-center\">\n                <div className=\"text-4xl mb-4\">🅿️</div>\n                <h4 className=\"font-medium text-gray-800 mb-2\">\n                  Paiement PayPal\n                </h4>\n                <p className=\"text-gray-600 text-sm\">\n                  Vous serez redirigé vers PayPal pour finaliser votre paiement\n                  de manière sécurisée.\n                </p>\n              </CardContent>\n            </Card>\n          </motion.div>\n        )}\n      </AnimatePresence>\n\n      {/* Security Info */}\n      <div className=\"bg-green-50 border border-green-200 rounded-lg p-4\">\n        <div className=\"flex items-center space-x-3\">\n          <span className=\"text-green-600 text-xl\">🔒</span>\n          <div>\n            <h4 className=\"font-medium text-green-800\">\n              Paiement 100% sécurisé\n            </h4>\n            <p className=\"text-sm text-green-600\">\n              Vos données sont protégées par un cryptage SSL 256-bit\n            </p>\n          </div>\n        </div>\n      </div>\n\n      {/* Action Buttons */}\n      <div className=\"flex justify-between items-center pt-6\">\n        <Button variant=\"outline\" onClick={onBack} disabled={isProcessing}>\n          ← Retour aux informations\n        </Button>\n\n        <Button\n          variant=\"primary\"\n          size=\"lg\"\n          onClick={processPayment}\n          disabled={isProcessing}\n          className=\"min-w-[200px]\"\n        >\n          {isProcessing ? (\n            <span className=\"flex items-center\">\n              <motion.span\n                className=\"mr-2\"\n                animate={{ rotate: 360 }}\n                transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n              >\n                💳\n              </motion.span>\n              Traitement en cours...\n            </span>\n          ) : (\n            <span className=\"flex items-center\">\n              <span className=\"mr-2\">💰</span>\n              Payer{\" \"}\n              {formatPrice(\n                cart.totalAmount +\n                  cart.totalAmount * 0.2 +\n                  (cart.totalAmount >= 50 ? 0 : 5.99)\n              )}\n            </span>\n          )}\n        </Button>\n      </div>\n    </motion.div>\n  );\n};\n\nexport { PaymentMethods };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;AACA;;;AANA;;;;;;AAiBA,MAAM,iBAAgD,CAAC,EACrD,MAAM,EACN,SAAS,EACT,YAAY,EACZ,eAAe,EAChB;;IACC,MAAM,EAAE,IAAI,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IAClC,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;QAC7C,QAAQ;QACR,QAAQ;QACR,KAAK;QACL,MAAM;IACR;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;QACb;QACA;YACE,IAAI;YACJ,MAAM;YACN,MAAM;YACN,aAAa;YACb,WAAW;QACb;KACD;IAED,MAAM,wBAAwB,CAC5B,OACA;QAEA,IAAI,iBAAiB;QAErB,IAAI,UAAU,UAAU;YACtB,iCAAiC;YACjC,iBAAiB,MACd,OAAO,CAAC,OAAO,IACf,OAAO,CAAC,WAAW,OACnB,IAAI;YACP,IAAI,eAAe,MAAM,GAAG,IAC1B,iBAAiB,eAAe,KAAK,CAAC,GAAG;QAC7C,OAAO,IAAI,UAAU,UAAU;YAC7B,yBAAyB;YACzB,iBAAiB,MAAM,OAAO,CAAC,OAAO,IAAI,OAAO,CAAC,eAAe;YACjE,IAAI,eAAe,MAAM,GAAG,GAC1B,iBAAiB,eAAe,KAAK,CAAC,GAAG;QAC7C,OAAO,IAAI,UAAU,OAAO;YAC1B,6BAA6B;YAC7B,iBAAiB,MAAM,OAAO,CAAC,OAAO,IAAI,KAAK,CAAC,GAAG;QACrD;QAEA,eAAe,CAAC,OAAS,CAAC;gBAAE,GAAG,IAAI;gBAAE,CAAC,MAAM,EAAE;YAAe,CAAC;IAChE;IAEA,MAAM,mBAAmB;QACvB,eAAe;YACb,QAAQ;YACR,QAAQ;YACR,KAAK;YACL,MACE,UAAU,aAAa,UAAU,WAC7B,GAAG,SAAS,SAAS,CAAC,CAAC,EAAE,SAAS,QAAQ,EAAE,GAC5C;QACR;IACF;IAEA,MAAM,sBAAsB;QAC1B,MAAM,EAAE,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG;QAEtC,IAAI,CAAC,UAAU,OAAO,OAAO,CAAC,OAAO,IAAI,MAAM,GAAG,IAAI;YACpD,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT;QAEA,IAAI,CAAC,UAAU,OAAO,MAAM,GAAG,GAAG;YAChC,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT;QAEA,IAAI,CAAC,OAAO,IAAI,MAAM,GAAG,GAAG;YAC1B,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT;QAEA,IAAI,CAAC,KAAK,IAAI,IAAI;YAChB,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA,OAAO;QACT;QAEA,OAAO;IACT;IAEA,MAAM,iBAAiB;QACrB,IAAI,mBAAmB,UAAU,CAAC,uBAAuB;YACvD;QACF;QAEA,gBAAgB;QAEhB,IAAI;YACF,wCAAwC;YACxC,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;gBAC3B,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SACE;gBACJ;gBACA;YACF;YAEA,sDAAsD;YACtD,MAAM,gBAAgB,MAAM,MAAM,eAAe;gBAC/C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,8CAA8C;oBAC9C,GAAI,UAAU,MAAM;wBAAE,YAAY,SAAS,EAAE;oBAAC,CAAC;oBAC/C,OAAO,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,OAAS,CAAC;4BAC/B,WAAW,KAAK,SAAS;4BACzB,WAAW,KAAK,SAAS;4BACzB,UAAU,KAAK,QAAQ;wBACzB,CAAC;oBACD,iBAAiB;wBACf,WAAW,UAAU,aAAa;wBAClC,UAAU,UAAU,YAAY;wBAChC,OAAO,UAAU,SAAS;wBAC1B,OAAO,UAAU,SAAS;wBAC1B,QAAQ,UAAU,WAAW;wBAC7B,MAAM,UAAU,QAAQ;wBACxB,YAAY,UAAU,cAAc;wBACpC,SAAS;oBACX;oBACA,aAAa,KAAK,WAAW;gBAC/B;YACF;YAEA,IAAI,CAAC,cAAc,EAAE,EAAE;gBACrB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,cAAc,IAAI;YAE1C,sCAAsC;YACtC,MAAM,mBAAmB,MAAM,MAAM,yBAAyB;gBAC5D,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;oBACnB,SAAS,MAAM,EAAE;gBACnB;YACF;YAEA,IAAI,CAAC,iBAAiB,EAAE,EAAE;gBACxB,MAAM,IAAI,MAAM;YAClB;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,MAAM,iBAAiB,IAAI;YAE5C,iCAAiC;YACjC,IAAI,KAAK,GAAG,EAAE;gBACZ,gFAAgF;gBAChF,aAAa,OAAO,CAClB,0BACA,KAAK,SAAS,CAAC;oBACb,SAAS,MAAM,EAAE;oBACjB,WAAW,KAAK,KAAK;oBACrB,WAAW,KAAK,GAAG;gBACrB;gBAGF,wBAAwB;gBACxB,OAAO,QAAQ,CAAC,IAAI,GAAG,KAAK,GAAG;YACjC,OAAO;gBACL,MAAM,IAAI,MAAM;YAClB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YAErC,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SACE,iBAAiB,QACb,MAAM,OAAO,GACb;YACR;YAEA,gBAAgB;QAClB;IACF;IAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,YAAY;YAAE,UAAU;QAAI;;0BAG5B,4SAAC;;kCACC,4SAAC;wBAAG,WAAU;kCAAiC;;;;;;kCAG/C,4SAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,uBACnB,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;gCAEZ,SAAS,IAAM,OAAO,SAAS,IAAI,kBAAkB,OAAO,EAAE;gCAC9D,UAAU,CAAC,OAAO,SAAS;gCAC3B,WAAW,CAAC;;gBAEV,EACE,mBAAmB,OAAO,EAAE,GACxB,+BACA,OAAO,SAAS,GAChB,mDACA,2DACL;cACH,CAAC;gCACD,YAAY,OAAO,SAAS,GAAG;oCAAE,OAAO;gCAAK,IAAI,CAAC;gCAClD,UAAU,OAAO,SAAS,GAAG;oCAAE,OAAO;gCAAK,IAAI,CAAC;0CAEhD,cAAA,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAK,WAAU;sDAAY,OAAO,IAAI;;;;;;sDACvC,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAG,WAAU;8DAA6B,OAAO,IAAI;;;;;;8DACtD,4SAAC;oDAAE,WAAU;8DAAyB,OAAO,WAAW;;;;;;gDACvD,CAAC,OAAO,SAAS,kBAChB,4SAAC;oDAAE,WAAU;8DAA6B;;;;;;;;;;;;wCAK7C,mBAAmB,OAAO,EAAE,kBAC3B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,MAAM;gDAAU,WAAW;4CAAI;sDAE7C,cAAA,4SAAC;gDAAK,WAAU;0DAAqB;;;;;;;;;;;;;;;;;+BAlCtC,OAAO,EAAE;;;;;;;;;;;;;;;;0BA4CtB,4SAAC,kSAAA,CAAA,kBAAe;gBAAC,MAAK;;oBACnB,mBAAmB,wBAClB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4SAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAG,WAAU;0DAA4B;;;;;;0DAG1C,4SAAC,qIAAA,CAAA,SAAM;gDACL,MAAK;gDACL,SAAQ;gDACR,MAAK;gDACL,SAAS;gDACT,WAAU;0DACX;;;;;;;;;;;;kDAKH,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;;kEACC,4SAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,4SAAC;wDACC,MAAK;wDACL,OAAO,YAAY,MAAM;wDACzB,UAAU,CAAC,IACT,sBAAsB,UAAU,EAAE,MAAM,CAAC,KAAK;wDAEhD,aAAY;wDACZ,WAAU;;;;;;;;;;;;0DAId,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;;0EACC,4SAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,4SAAC;gEACC,MAAK;gEACL,OAAO,YAAY,MAAM;gEACzB,UAAU,CAAC,IACT,sBAAsB,UAAU,EAAE,MAAM,CAAC,KAAK;gEAEhD,aAAY;gEACZ,WAAU;;;;;;;;;;;;kEAId,4SAAC;;0EACC,4SAAC;gEAAM,WAAU;0EAA+C;;;;;;0EAGhE,4SAAC;gEACC,MAAK;gEACL,OAAO,YAAY,GAAG;gEACtB,UAAU,CAAC,IACT,sBAAsB,OAAO,EAAE,MAAM,CAAC,KAAK;gEAE7C,aAAY;gEACZ,WAAU;;;;;;;;;;;;;;;;;;0DAKhB,4SAAC;;kEACC,4SAAC;wDAAM,WAAU;kEAA+C;;;;;;kEAGhE,4SAAC;wDACC,MAAK;wDACL,OAAO,YAAY,IAAI;wDACvB,UAAU,CAAC,IACT,sBAAsB,QAAQ,EAAE,MAAM,CAAC,KAAK;wDAE9C,aAAY;wDACZ,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;uBAlFhB;;;;;oBA2FP,mBAAmB,0BAClB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBAET,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;sCACH,cAAA,4SAAC,mIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,4SAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,4SAAC;wCAAG,WAAU;kDAAiC;;;;;;kDAG/C,4SAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;uBAZrC;;;;;;;;;;;0BAuBV,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAK,WAAU;sCAAyB;;;;;;sCACzC,4SAAC;;8CACC,4SAAC;oCAAG,WAAU;8CAA6B;;;;;;8CAG3C,4SAAC;oCAAE,WAAU;8CAAyB;;;;;;;;;;;;;;;;;;;;;;;0BAQ5C,4SAAC;gBAAI,WAAU;;kCACb,4SAAC,qIAAA,CAAA,SAAM;wBAAC,SAAQ;wBAAU,SAAS;wBAAQ,UAAU;kCAAc;;;;;;kCAInE,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,WAAU;kCAET,6BACC,4SAAC;4BAAK,WAAU;;8CACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;oCACV,WAAU;oCACV,SAAS;wCAAE,QAAQ;oCAAI;oCACvB,YAAY;wCAAE,UAAU;wCAAG,QAAQ;wCAAU,MAAM;oCAAS;8CAC7D;;;;;;gCAEa;;;;;;iDAIhB,4SAAC;4BAAK,WAAU;;8CACd,4SAAC;oCAAK,WAAU;8CAAO;;;;;;gCAAS;gCAC1B;gCACL,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EACT,KAAK,WAAW,GACd,KAAK,WAAW,GAAG,MACnB,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;;;;;;;;;;;;;;;;;;;;;;;;AAQpD;GA/cM;;QAMwB,4KAAA,CAAA,UAAO;QACd,qIAAA,CAAA,cAAW;QACJ,yIAAA,CAAA,mBAAgB;;;KARxC", "debugId": null}}, {"offset": {"line": 8979, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/modals/CheckoutModal.tsx"], "sourcesContent": ["\"use client\";\n\nimport { CheckoutForm } from \"@/components/checkout/CheckoutForm\";\nimport {\n  CheckoutProgress,\n  type CheckoutStep,\n} from \"@/components/checkout/CheckoutProgress\";\nimport { OrderSummary } from \"@/components/checkout/OrderSummary\";\nimport { PaymentMethods } from \"@/components/checkout/PaymentMethods\";\nimport { Card, CardContent, CardHeader, Modal } from \"@/components/ui\";\nimport { useCart, useCustomer } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\nexport interface CheckoutModalProps {\n  isOpen: boolean;\n  onClose: () => void;\n}\n\nconst CheckoutModal: React.FC<CheckoutModalProps> = ({ isOpen, onClose }) => {\n  const [currentStep, setCurrentStep] = useState<CheckoutStep>(\"shipping\");\n  const [isProcessing, setIsProcessing] = useState(false);\n  const cart = useCart();\n  const customer = useCustomer();\n\n  // Reset modal state when opening\n  React.useEffect(() => {\n    if (isOpen) {\n      setCurrentStep(\"shipping\");\n      setIsProcessing(false);\n    }\n  }, [isOpen]);\n\n  // Si le panier est vide, ne pas afficher le modal\n  if (!cart?.cart?.items || cart.cart.items.length === 0) {\n    return null;\n  }\n\n  const handleStepChange = (step: CheckoutStep) => {\n    setCurrentStep(step);\n  };\n\n  const handlePaymentSuccess = () => {\n    setCurrentStep(\"confirmation\");\n  };\n\n  const handleClose = () => {\n    setCurrentStep(\"shipping\");\n    setIsProcessing(false);\n    onClose();\n  };\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={handleClose}\n      title=\"Finaliser votre commande\"\n      size=\"full\"\n      className=\"max-w-7xl\"\n    >\n      <div className=\"min-h-[600px]\">\n        {/* Progress Bar */}\n        <div className=\"mb-8\">\n          <CheckoutProgress currentStep={currentStep} />\n        </div>\n\n        {/* Content Grid */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8\">\n          {/* Main Checkout Form */}\n          <div className=\"lg:col-span-2\">\n            <AnimatePresence mode=\"wait\">\n              {currentStep === \"shipping\" && (\n                <motion.div\n                  key=\"shipping\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <Card>\n                    <CardHeader>\n                      <h3 className=\"text-xl font-semibold text-gray-800\">\n                        Informations de livraison\n                      </h3>\n                    </CardHeader>\n                    <CardContent>\n                      <CheckoutForm\n                        onNext={() => handleStepChange(\"payment\")}\n                        isProcessing={isProcessing}\n                      />\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n\n              {currentStep === \"payment\" && (\n                <motion.div\n                  key=\"payment\"\n                  initial={{ opacity: 0, x: -20 }}\n                  animate={{ opacity: 1, x: 0 }}\n                  exit={{ opacity: 0, x: 20 }}\n                  transition={{ duration: 0.3 }}\n                >\n                  <Card>\n                    <CardHeader>\n                      <h3 className=\"text-xl font-semibold text-gray-800\">\n                        Méthode de paiement\n                      </h3>\n                    </CardHeader>\n                    <CardContent>\n                      <PaymentMethods\n                        onBack={() => handleStepChange(\"shipping\")}\n                        onSuccess={handlePaymentSuccess}\n                        isProcessing={isProcessing}\n                        setIsProcessing={setIsProcessing}\n                      />\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n\n              {currentStep === \"confirmation\" && (\n                <motion.div\n                  key=\"confirmation\"\n                  initial={{ opacity: 0, scale: 0.95 }}\n                  animate={{ opacity: 1, scale: 1 }}\n                  transition={{ duration: 0.5 }}\n                >\n                  <Card>\n                    <CardContent className=\"text-center py-12\">\n                      <motion.div\n                        initial={{ scale: 0 }}\n                        animate={{ scale: 1 }}\n                        transition={{ delay: 0.2, type: \"spring\" }}\n                        className=\"text-6xl mb-6\"\n                      >\n                        🎉\n                      </motion.div>\n                      <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">\n                        Commande confirmée !\n                      </h3>\n                      <p className=\"text-gray-600 mb-8\">\n                        Merci pour votre commande ! Vous recevrez un email de\n                        confirmation sous peu.\n                      </p>\n\n                      <div className=\"space-y-4\">\n                        <button\n                          onClick={handleClose}\n                          className=\"bg-pink-500 text-white px-8 py-3 rounded-lg hover:bg-pink-600 transition-colors mr-4\"\n                        >\n                          Fermer\n                        </button>\n                      </div>\n                    </CardContent>\n                  </Card>\n                </motion.div>\n              )}\n            </AnimatePresence>\n          </div>\n\n          {/* Order Summary Sidebar */}\n          <div className=\"lg:col-span-1\">\n            <div className=\"sticky top-4\">\n              <OrderSummary />\n            </div>\n          </div>\n        </div>\n      </div>\n    </Modal>\n  );\n};\n\nexport { CheckoutModal };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAIA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AAZA;;;;;;;;;AAmBA,MAAM,gBAA8C,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IACtE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAgB;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,OAAO,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IACnB,MAAM,WAAW,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAE3B,iCAAiC;IACjC,4QAAA,CAAA,UAAK,CAAC,SAAS;mCAAC;YACd,IAAI,QAAQ;gBACV,eAAe;gBACf,gBAAgB;YAClB;QACF;kCAAG;QAAC;KAAO;IAEX,kDAAkD;IAClD,IAAI,CAAC,MAAM,MAAM,SAAS,KAAK,IAAI,CAAC,KAAK,CAAC,MAAM,KAAK,GAAG;QACtD,OAAO;IACT;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,eAAe;IACjB;IAEA,MAAM,cAAc;QAClB,eAAe;QACf,gBAAgB;QAChB;IACF;IAEA,qBACE,4SAAC,oIAAA,CAAA,QAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAM;QACN,MAAK;QACL,WAAU;kBAEV,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,qJAAA,CAAA,mBAAgB;wBAAC,aAAa;;;;;;;;;;;8BAIjC,4SAAC;oBAAI,WAAU;;sCAEb,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC,kSAAA,CAAA,kBAAe;gCAAC,MAAK;;oCACnB,gBAAgB,4BACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;;8DACH,4SAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,4SAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;8DAItD,4SAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,4SAAC,iJAAA,CAAA,eAAY;wDACX,QAAQ,IAAM,iBAAiB;wDAC/B,cAAc;;;;;;;;;;;;;;;;;uCAfhB;;;;;oCAsBP,gBAAgB,2BACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,GAAG,CAAC;wCAAG;wCAC9B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,MAAM;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC1B,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;;8DACH,4SAAC,mIAAA,CAAA,aAAU;8DACT,cAAA,4SAAC;wDAAG,WAAU;kEAAsC;;;;;;;;;;;8DAItD,4SAAC,mIAAA,CAAA,cAAW;8DACV,cAAA,4SAAC,mJAAA,CAAA,iBAAc;wDACb,QAAQ,IAAM,iBAAiB;wDAC/B,WAAW;wDACX,cAAc;wDACd,iBAAiB;;;;;;;;;;;;;;;;;uCAjBnB;;;;;oCAwBP,gBAAgB,gCACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCAET,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAK;wCACnC,SAAS;4CAAE,SAAS;4CAAG,OAAO;wCAAE;wCAChC,YAAY;4CAAE,UAAU;wCAAI;kDAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;sDACH,cAAA,4SAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;;kEACrB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wDACT,SAAS;4DAAE,OAAO;wDAAE;wDACpB,SAAS;4DAAE,OAAO;wDAAE;wDACpB,YAAY;4DAAE,OAAO;4DAAK,MAAM;wDAAS;wDACzC,WAAU;kEACX;;;;;;kEAGD,4SAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAGtD,4SAAC;wDAAE,WAAU;kEAAqB;;;;;;kEAKlC,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC;4DACC,SAAS;4DACT,WAAU;sEACX;;;;;;;;;;;;;;;;;;;;;;uCA3BH;;;;;;;;;;;;;;;;sCAuCZ,4SAAC;4BAAI,WAAU;sCACb,cAAA,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO3B;GAxJM;;QAGS,4KAAA,CAAA,UAAO;QACH,qIAAA,CAAA,cAAW;;;KAJxB", "debugId": null}}, {"offset": {"line": 9335, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/modals/ProductModal.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>dal<PERSON>ody, <PERSON>dal<PERSON>ooter, <PERSON><PERSON> } from '@/components/ui'\nimport { useProduct } from '@/stores'\n\nexport interface ProductModalProps {\n  isOpen: boolean\n  onClose: () => void\n}\n\nconst ProductModal: React.FC<ProductModalProps> = ({ isOpen, onClose }) => {\n  const { selectedProduct } = useProduct()\n\n  if (!selectedProduct) {\n    return null\n  }\n\n  return (\n    <Modal\n      isOpen={isOpen}\n      onClose={onClose}\n      title={selectedProduct.name}\n      size=\"lg\"\n    >\n      <ModalBody>\n        <div className=\"text-center py-8\">\n          <div className=\"text-6xl mb-4\">🍭</div>\n          <p className=\"text-gray-600\">\n            Modal de détail du produit - À développer dans les prochaines tâches\n          </p>\n        </div>\n      </ModalBody>\n      <ModalFooter>\n        <Button variant=\"outline\" onClick={onClose}>\n          Fermer\n        </Button>\n      </ModalFooter>\n    </Modal>\n  )\n}\n\nexport { ProductModal }\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AAAA;AACA;AAAA;;;AAJA;;;AAWA,MAAM,eAA4C,CAAC,EAAE,MAAM,EAAE,OAAO,EAAE;;IACpE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,qLAAA,CAAA,aAAU,AAAD;IAErC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,4SAAC,oIAAA,CAAA,QAAK;QACJ,QAAQ;QACR,SAAS;QACT,OAAO,gBAAgB,IAAI;QAC3B,MAAK;;0BAEL,4SAAC,oIAAA,CAAA,YAAS;0BACR,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,4SAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAKjC,4SAAC,oIAAA,CAAA,cAAW;0BACV,cAAA,4SAAC,qIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAU,SAAS;8BAAS;;;;;;;;;;;;;;;;;AAMpD;GA7BM;;QACwB,qLAAA,CAAA,aAAU;;;KADlC", "debugId": null}}, {"offset": {"line": 9432, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/providers/StripeProvider.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Elements } from \"@stripe/react-stripe-js\";\nimport { loadStripe } from \"@stripe/stripe-js\";\nimport { ReactNode } from \"react\";\n\n// Charger Stripe avec la clé publique\nconst stripePromise = loadStripe(process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!);\n\ninterface StripeProviderProps {\n  children: ReactNode;\n}\n\nexport function StripeProvider({ children }: StripeProviderProps) {\n  return (\n    <Elements \n      stripe={stripePromise}\n      options={{\n        appearance: {\n          theme: 'stripe',\n          variables: {\n            colorPrimary: '#ff6b9d',\n            colorBackground: '#ffffff',\n            colorText: '#30313d',\n            colorDanger: '#df1b41',\n            fontFamily: 'Inter, system-ui, sans-serif',\n            spacingUnit: '4px',\n            borderRadius: '8px',\n          },\n        },\n        locale: 'fr',\n      }}\n    >\n      {children}\n    </Elements>\n  );\n}\n"], "names": [], "mappings": ";;;AAOiC;;AALjC;AACA;AAAA;AAHA;;;;AAMA,sCAAsC;AACtC,MAAM,gBAAgB,CAAA,GAAA,kOAAA,CAAA,aAAU,AAAD;AAMxB,SAAS,eAAe,EAAE,QAAQ,EAAuB;IAC9D,qBACE,4SAAC,gSAAA,CAAA,WAAQ;QACP,QAAQ;QACR,SAAS;YACP,YAAY;gBACV,OAAO;gBACP,WAAW;oBACT,cAAc;oBACd,iBAAiB;oBACjB,WAAW;oBACX,aAAa;oBACb,YAAY;oBACZ,aAAa;oBACb,cAAc;gBAChB;YACF;YACA,QAAQ;QACV;kBAEC;;;;;;AAGP;KAvBgB", "debugId": null}}, {"offset": {"line": 9483, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/layout/Layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ToastContainer } from \"@/components/ui\";\nimport { useAgeVerification } from \"@/hooks/useAgeVerification\";\nimport { useAuthInit } from \"@/hooks/useAuthInit\";\nimport { cn } from \"@/lib/utils\";\nimport { useCheckoutModal, useUI } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useEffect } from \"react\";\nimport { Footer } from \"./Footer\";\nimport { Header } from \"./Header\";\n\n// Modals\nimport { AgeVerificationModal } from \"@/components/modals/AgeVerificationModal\";\nimport { CartModal } from \"@/components/modals/CartModal\";\nimport { CheckoutModal } from \"@/components/modals/CheckoutModal\";\nimport { ProductModal } from \"@/components/modals/ProductModal\";\nimport { StripeProvider } from \"@/components/providers/StripeProvider\";\n\nexport interface LayoutProps {\n  children: React.ReactNode;\n  className?: string;\n  showHeader?: boolean;\n  showFooter?: boolean;\n}\n\nconst Layout: React.FC<LayoutProps> = ({\n  children,\n  className,\n  showHeader = true,\n  showFooter = true,\n}) => {\n  const {\n    isCartOpen,\n    isAuthModalOpen,\n    setIsMobile,\n    isLoading,\n    closeAllModals,\n  } = useUI();\n\n  const { isOpen: isCheckoutModalOpen, closeModal: closeCheckoutModal } =\n    useCheckoutModal();\n\n  const { isVerified, showModal, confirmAge, denyAge } = useAgeVerification();\n\n  // Initialiser l'authentification\n  useAuthInit();\n\n  // État pour éviter les problèmes d'hydratation\n  const [isClient, setIsClient] = React.useState(false);\n\n  // Éviter les problèmes d'hydratation\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  // Détecter si on est sur mobile\n  useEffect(() => {\n    const checkMobile = () => {\n      setIsMobile(window.innerWidth < 768);\n    };\n\n    checkMobile();\n    window.addEventListener(\"resize\", checkMobile);\n    return () => window.removeEventListener(\"resize\", checkMobile);\n  }, [setIsMobile]);\n\n  // Empêcher le scroll quand une modal est ouverte\n  useEffect(() => {\n    const hasOpenModal = isCartOpen || isCheckoutModalOpen || isAuthModalOpen;\n    if (hasOpenModal) {\n      document.body.style.overflow = \"hidden\";\n    } else {\n      document.body.style.overflow = \"unset\";\n    }\n\n    return () => {\n      document.body.style.overflow = \"unset\";\n    };\n  }, [isCartOpen, isCheckoutModalOpen, isAuthModalOpen]);\n\n  // Ne pas afficher le contenu tant que la vérification n'est pas faite\n  if (isVerified === null) {\n    return (\n      <div className=\"min-h-screen bg-gradient-to-br from-pink-400 via-purple-500 to-orange-400 flex items-center justify-center\">\n        <div className=\"text-white text-center\">\n          <div className=\"animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4\"></div>\n          <p>Chargement...</p>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"min-h-screen flex flex-col bg-white\">\n      {/* Vérification d'âge */}\n      <AgeVerificationModal\n        isOpen={showModal}\n        onConfirm={confirmAge}\n        onDeny={denyAge}\n      />\n\n      {/* Contenu principal (affiché seulement si vérifié) */}\n      {isVerified && (\n        <StripeProvider>\n          {/* Header */}\n          {showHeader && <Header />}\n\n          {/* Contenu principal */}\n          <main\n            className={cn(\n              \"flex-1\",\n              showHeader && \"pt-16 lg:pt-20\", // Compenser la hauteur du header fixe\n              className\n            )}\n          >\n            {children}\n          </main>\n\n          {/* Footer */}\n          {showFooter && <Footer />}\n\n          {/* Modals */}\n          {isClient && (\n            <AnimatePresence mode=\"wait\">\n              <CartModal\n                key=\"cart-modal\"\n                isOpen={isCartOpen}\n                onClose={closeAllModals}\n              />\n              <ProductModal\n                key=\"product-modal\"\n                isOpen={false}\n                onClose={closeAllModals}\n              />\n              <CheckoutModal\n                key=\"checkout-modal\"\n                isOpen={isCheckoutModalOpen}\n                onClose={closeCheckoutModal}\n              />\n            </AnimatePresence>\n          )}\n        </StripeProvider>\n      )}\n\n      {/* Toast Notifications */}\n      <ToastContainer />\n\n      {/* Loading Overlay Global */}\n      <AnimatePresence>\n        {isLoading && (\n          <motion.div\n            className=\"fixed inset-0 bg-white/80 backdrop-blur-sm z-50 flex items-center justify-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            exit={{ opacity: 0 }}\n          >\n            <div className=\"text-center\">\n              <motion.div\n                className=\"text-6xl mb-4\"\n                animate={{\n                  rotate: [0, 360],\n                  scale: [1, 1.2, 1],\n                }}\n                transition={{\n                  duration: 2,\n                  repeat: Infinity,\n                  ease: \"easeInOut\",\n                }}\n              >\n                🍭\n              </motion.div>\n              <motion.p\n                className=\"text-lg font-medium text-gray-600\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.2 }}\n              >\n                Chargement en cours...\n              </motion.p>\n            </div>\n          </motion.div>\n        )}\n      </AnimatePresence>\n    </div>\n  );\n};\n\n// Layout spécialisé pour les pages d'erreur\nexport const ErrorLayout: React.FC<{ children: React.ReactNode }> = ({\n  children,\n}) => (\n  <Layout\n    showHeader={false}\n    showFooter={false}\n    className=\"flex items-center justify-center\"\n  >\n    {children}\n  </Layout>\n);\n\n// Layout spécialisé pour les pages d'authentification\nexport const AuthLayout: React.FC<{ children: React.ReactNode }> = ({\n  children,\n}) => (\n  <Layout\n    showHeader={false}\n    showFooter={false}\n    className=\"flex items-center justify-center min-h-screen bg-gradient-to-br from-pink-50 to-orange-50\"\n  >\n    {children}\n  </Layout>\n);\n\n// Layout spécialisé pour les pages admin\nexport const AdminLayout: React.FC<{ children: React.ReactNode }> = ({\n  children,\n}) => (\n  <Layout className=\"bg-gray-50\">\n    <div className=\"container mx-auto px-4 py-8\">{children}</div>\n  </Layout>\n);\n\nexport { Layout };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AACA;AACA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;AACA;AACA;AAEA,SAAS;AACT;AACA;AACA;AACA;AACA;;;AAjBA;;;;;;;;;;;;;;;AA0BA,MAAM,SAAgC,CAAC,EACrC,QAAQ,EACR,SAAS,EACT,aAAa,IAAI,EACjB,aAAa,IAAI,EAClB;;IACC,MAAM,EACJ,UAAU,EACV,eAAe,EACf,WAAW,EACX,SAAS,EACT,cAAc,EACf,GAAG,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD;IAER,MAAM,EAAE,QAAQ,mBAAmB,EAAE,YAAY,kBAAkB,EAAE,GACnE,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IAEjB,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,qBAAkB,AAAD;IAExE,iCAAiC;IACjC,CAAA,GAAA,8HAAA,CAAA,cAAW,AAAD;IAEV,+CAA+C;IAC/C,MAAM,CAAC,UAAU,YAAY,GAAG,4QAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAE/C,qCAAqC;IACrC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR,YAAY;QACd;2BAAG,EAAE;IAEL,gCAAgC;IAChC,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM;gDAAc;oBAClB,YAAY,OAAO,UAAU,GAAG;gBAClC;;YAEA;YACA,OAAO,gBAAgB,CAAC,UAAU;YAClC;oCAAO,IAAM,OAAO,mBAAmB,CAAC,UAAU;;QACpD;2BAAG;QAAC;KAAY;IAEhB,iDAAiD;IACjD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;4BAAE;YACR,MAAM,eAAe,cAAc,uBAAuB;YAC1D,IAAI,cAAc;gBAChB,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC,OAAO;gBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;YACjC;YAEA;oCAAO;oBACL,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;gBACjC;;QACF;2BAAG;QAAC;QAAY;QAAqB;KAAgB;IAErD,sEAAsE;IACtE,IAAI,eAAe,MAAM;QACvB,qBACE,4SAAC;YAAI,WAAU;sBACb,cAAA,4SAAC;gBAAI,WAAU;;kCACb,4SAAC;wBAAI,WAAU;;;;;;kCACf,4SAAC;kCAAE;;;;;;;;;;;;;;;;;IAIX;IAEA,qBACE,4SAAC;QAAI,WAAU;;0BAEb,4SAAC,uJAAA,CAAA,uBAAoB;gBACnB,QAAQ;gBACR,WAAW;gBACX,QAAQ;;;;;;YAIT,4BACC,4SAAC,oJAAA,CAAA,iBAAc;;oBAEZ,4BAAc,4SAAC,yIAAA,CAAA,SAAM;;;;;kCAGtB,4SAAC;wBACC,WAAW,CAAA,GAAA,sHAAA,CAAA,KAAE,AAAD,EACV,UACA,cAAc,kBACd;kCAGD;;;;;;oBAIF,4BAAc,4SAAC,yIAAA,CAAA,SAAM;;;;;oBAGrB,0BACC,4SAAC,kSAAA,CAAA,kBAAe;wBAAC,MAAK;;0CACpB,4SAAC,4IAAA,CAAA,YAAS;gCAER,QAAQ;gCACR,SAAS;+BAFL;;;;;0CAIN,4SAAC,+IAAA,CAAA,eAAY;gCAEX,QAAQ;gCACR,SAAS;+BAFL;;;;;0CAIN,4SAAC,gJAAA,CAAA,gBAAa;gCAEZ,QAAQ;gCACR,SAAS;+BAFL;;;;;;;;;;;;;;;;;0BAUd,4SAAC,oIAAA,CAAA,iBAAc;;;;;0BAGf,4SAAC,kSAAA,CAAA,kBAAe;0BACb,2BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;oBAAE;oBACtB,SAAS;wBAAE,SAAS;oBAAE;oBACtB,MAAM;wBAAE,SAAS;oBAAE;8BAEnB,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,WAAU;gCACV,SAAS;oCACP,QAAQ;wCAAC;wCAAG;qCAAI;oCAChB,OAAO;wCAAC;wCAAG;wCAAK;qCAAE;gCACpB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,MAAM;gCACR;0CACD;;;;;;0CAGD,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAC5B,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASf;GAhKM;;QAYA,+HAAA,CAAA,QAAK;QAGP,8IAAA,CAAA,mBAAgB;QAEqC,qIAAA,CAAA,qBAAkB;QAGzE,8HAAA,CAAA,cAAW;;;KApBP;AAmKC,MAAM,cAAuD,CAAC,EACnE,QAAQ,EACT,iBACC,4SAAC;QACC,YAAY;QACZ,YAAY;QACZ,WAAU;kBAET;;;;;;MARQ;AAaN,MAAM,aAAsD,CAAC,EAClE,QAAQ,EACT,iBACC,4SAAC;QACC,YAAY;QACZ,YAAY;QACZ,WAAU;kBAET;;;;;;MARQ;AAaN,MAAM,cAAuD,CAAC,EACnE,QAAQ,EACT,iBACC,4SAAC;QAAO,WAAU;kBAChB,cAAA,4SAAC;YAAI,WAAU;sBAA+B;;;;;;;;;;;MAJrC", "debugId": null}}]}