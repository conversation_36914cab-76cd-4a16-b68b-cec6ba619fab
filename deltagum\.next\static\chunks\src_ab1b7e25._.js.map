{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/cart/CartItem.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON><PERSON> } from \"@/components/ui\";\nimport { formatPrice } from \"@/lib/utils\";\nimport type { CartItem as CartItemType } from \"@/types\";\nimport { motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\ninterface CartItemProps {\n  item: CartItemType;\n  onQuantityUpdate: (itemId: string, newQuantity: number) => void;\n  onRemove: (itemId: string) => void;\n}\n\nconst CartItem: React.FC<CartItemProps> = ({\n  item,\n  onQuantityUpdate,\n  onRemove,\n}) => {\n  const [isUpdating, setIsUpdating] = useState(false);\n\n  const handleQuantityChange = async (newQuantity: number) => {\n    if (newQuantity < 0 || newQuantity > 10) return;\n\n    setIsUpdating(true);\n    try {\n      await onQuantityUpdate(item.id, newQuantity);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  const handleRemove = async () => {\n    setIsUpdating(true);\n    try {\n      await onRemove(item.id);\n    } finally {\n      setIsUpdating(false);\n    }\n  };\n\n  // Configuration des saveurs\n  const flavorConfig = {\n    strawberry: { emoji: \"🍓\", color: \"text-pink-600\", bg: \"bg-pink-50\" },\n    blueberry: { emoji: \"🫐\", color: \"text-blue-600\", bg: \"bg-blue-50\" },\n    apple: { emoji: \"🍏\", color: \"text-green-600\", bg: \"bg-green-50\" },\n  };\n\n  const flavor = flavorConfig[\n    item.flavor.toLowerCase() as keyof typeof flavorConfig\n  ] || {\n    emoji: \"🍭\",\n    color: \"text-gray-600\",\n    bg: \"bg-gray-50\",\n  };\n\n  const subtotal = item.price * item.quantity;\n\n  return (\n    <motion.div\n      className=\"p-6 hover:bg-gray-50 transition-colors duration-200\"\n      layout\n      initial={{ opacity: 0, y: 20 }}\n      animate={{ opacity: 1, y: 0 }}\n      exit={{ opacity: 0, y: -20 }}\n    >\n      <div className=\"flex items-center space-x-4\">\n        {/* Product Image/Icon */}\n        <motion.div\n          className=\"flex-shrink-0\"\n          whileHover={{ scale: 1.1, rotate: 5 }}\n          transition={{ type: \"spring\", stiffness: 300 }}\n        >\n          <div\n            className={`w-16 h-16 rounded-lg ${flavor.bg} flex items-center justify-center text-2xl`}\n          >\n            {flavor.emoji}\n          </div>\n        </motion.div>\n\n        {/* Product Info */}\n        <div className=\"flex-1 min-w-0\">\n          <div className=\"flex items-start justify-between\">\n            <div>\n              <h4 className=\"text-lg font-semibold text-gray-800 truncate\">\n                {item.name}\n              </h4>\n              <div className=\"flex items-center space-x-2 mt-1\">\n                <Badge\n                  variant=\"secondary\"\n                  size=\"sm\"\n                  className={`${flavor.color} border-current`}\n                >\n                  {flavor.emoji} {item.flavor}\n                </Badge>\n              </div>\n              <p className=\"text-sm text-gray-600 mt-1 line-clamp-2\">\n                Délicieux bonbons {item.flavor}\n              </p>\n            </div>\n\n            {/* Remove Button */}\n            <Button\n              variant=\"ghost\"\n              size=\"sm\"\n              onClick={handleRemove}\n              disabled={isUpdating}\n              className=\"text-red-500 hover:text-red-700 hover:bg-red-50 ml-4\"\n            >\n              <span className=\"sr-only\">Supprimer</span>✕\n            </Button>\n          </div>\n\n          {/* Price and Quantity Controls */}\n          <div className=\"flex items-center justify-between mt-4\">\n            {/* Quantity Controls */}\n            <div className=\"flex items-center space-x-3\">\n              <span className=\"text-sm text-gray-500\">Quantité :</span>\n              <div className=\"flex items-center space-x-2\">\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleQuantityChange(item.quantity - 1)}\n                  disabled={isUpdating || item.quantity <= 1}\n                  className=\"w-8 h-8 p-0\"\n                >\n                  -\n                </Button>\n\n                <motion.span\n                  key={item.quantity}\n                  className=\"w-8 text-center font-semibold\"\n                  initial={{ scale: 1.2, color: \"#ec4899\" }}\n                  animate={{ scale: 1, color: \"#374151\" }}\n                  transition={{ duration: 0.2 }}\n                >\n                  {item.quantity}\n                </motion.span>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={() => handleQuantityChange(item.quantity + 1)}\n                  disabled={isUpdating || item.quantity >= 10}\n                  className=\"w-8 h-8 p-0\"\n                >\n                  +\n                </Button>\n              </div>\n            </div>\n\n            {/* Price Info */}\n            <div className=\"text-right\">\n              <div className=\"flex items-center space-x-2\">\n                <span className=\"text-sm text-gray-500\">\n                  {formatPrice(item.price)} × {item.quantity}\n                </span>\n                <span className=\"text-lg font-bold text-gray-800\">\n                  {formatPrice(subtotal)}\n                </span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n\n      {/* Loading Overlay */}\n      {isUpdating && (\n        <motion.div\n          className=\"absolute inset-0 bg-white bg-opacity-50 flex items-center justify-center\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n        >\n          <motion.div\n            className=\"text-2xl\"\n            animate={{ rotate: 360 }}\n            transition={{ duration: 1, repeat: Infinity, ease: \"linear\" }}\n          >\n            🍭\n          </motion.div>\n        </motion.div>\n      )}\n    </motion.div>\n  );\n};\n\nexport { CartItem };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AAEA;AACA;;;AANA;;;;;AAcA,MAAM,WAAoC,CAAC,EACzC,IAAI,EACJ,gBAAgB,EAChB,QAAQ,EACT;;IACC,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,uBAAuB,OAAO;QAClC,IAAI,cAAc,KAAK,cAAc,IAAI;QAEzC,cAAc;QACd,IAAI;YACF,MAAM,iBAAiB,KAAK,EAAE,EAAE;QAClC,SAAU;YACR,cAAc;QAChB;IACF;IAEA,MAAM,eAAe;QACnB,cAAc;QACd,IAAI;YACF,MAAM,SAAS,KAAK,EAAE;QACxB,SAAU;YACR,cAAc;QAChB;IACF;IAEA,4BAA4B;IAC5B,MAAM,eAAe;QACnB,YAAY;YAAE,OAAO;YAAM,OAAO;YAAiB,IAAI;QAAa;QACpE,WAAW;YAAE,OAAO;YAAM,OAAO;YAAiB,IAAI;QAAa;QACnE,OAAO;YAAE,OAAO;YAAM,OAAO;YAAkB,IAAI;QAAc;IACnE;IAEA,MAAM,SAAS,YAAY,CACzB,KAAK,MAAM,CAAC,WAAW,GACxB,IAAI;QACH,OAAO;QACP,OAAO;QACP,IAAI;IACN;IAEA,MAAM,WAAW,KAAK,KAAK,GAAG,KAAK,QAAQ;IAE3C,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,MAAM;QACN,SAAS;YAAE,SAAS;YAAG,GAAG;QAAG;QAC7B,SAAS;YAAE,SAAS;YAAG,GAAG;QAAE;QAC5B,MAAM;YAAE,SAAS;YAAG,GAAG,CAAC;QAAG;;0BAE3B,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,YAAY;4BAAE,OAAO;4BAAK,QAAQ;wBAAE;wBACpC,YAAY;4BAAE,MAAM;4BAAU,WAAW;wBAAI;kCAE7C,cAAA,4SAAC;4BACC,WAAW,CAAC,qBAAqB,EAAE,OAAO,EAAE,CAAC,0CAA0C,CAAC;sCAEvF,OAAO,KAAK;;;;;;;;;;;kCAKjB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;;0DACC,4SAAC;gDAAG,WAAU;0DACX,KAAK,IAAI;;;;;;0DAEZ,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC,oIAAA,CAAA,QAAK;oDACJ,SAAQ;oDACR,MAAK;oDACL,WAAW,GAAG,OAAO,KAAK,CAAC,eAAe,CAAC;;wDAE1C,OAAO,KAAK;wDAAC;wDAAE,KAAK,MAAM;;;;;;;;;;;;0DAG/B,4SAAC;gDAAE,WAAU;;oDAA0C;oDAClC,KAAK,MAAM;;;;;;;;;;;;;kDAKlC,4SAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU;wCACV,WAAU;;0DAEV,4SAAC;gDAAK,WAAU;0DAAU;;;;;;4CAAgB;;;;;;;;;;;;;0CAK9C,4SAAC;gCAAI,WAAU;;kDAEb,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAK,WAAU;0DAAwB;;;;;;0DACxC,4SAAC;gDAAI,WAAU;;kEACb,4SAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;wDACpD,UAAU,cAAc,KAAK,QAAQ,IAAI;wDACzC,WAAU;kEACX;;;;;;kEAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;wDAEV,WAAU;wDACV,SAAS;4DAAE,OAAO;4DAAK,OAAO;wDAAU;wDACxC,SAAS;4DAAE,OAAO;4DAAG,OAAO;wDAAU;wDACtC,YAAY;4DAAE,UAAU;wDAAI;kEAE3B,KAAK,QAAQ;uDANT,KAAK,QAAQ;;;;;kEASpB,4SAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,qBAAqB,KAAK,QAAQ,GAAG;wDACpD,UAAU,cAAc,KAAK,QAAQ,IAAI;wDACzC,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAOL,4SAAC;wCAAI,WAAU;kDACb,cAAA,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAK,WAAU;;wDACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,KAAK,KAAK;wDAAE;wDAAI,KAAK,QAAQ;;;;;;;8DAE5C,4SAAC;oDAAK,WAAU;8DACb,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASxB,4BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBAAE,SAAS;gBAAE;gBACtB,SAAS;oBAAE,SAAS;gBAAE;gBACtB,MAAM;oBAAE,SAAS;gBAAE;0BAEnB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,QAAQ;oBAAI;oBACvB,YAAY;wBAAE,UAAU;wBAAG,QAAQ;wBAAU,MAAM;oBAAS;8BAC7D;;;;;;;;;;;;;;;;;AAOX;GA3KM;KAAA", "debugId": null}}, {"offset": {"line": 371, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/cart/CartSummary.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  Card<PERSON>ontent,\n  Card<PERSON>eader,\n  Input,\n} from \"@/components/ui\";\nimport { formatPrice } from \"@/lib/utils\";\nimport {\n  useCart,\n  useCheckoutModal,\n  useCustomer,\n  useNotifications,\n} from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\nconst CartSummary: React.FC = () => {\n  const { cart } = useCart();\n  const { customer } = useCustomer();\n  const { addNotification } = useNotifications();\n  const { openModal } = useCheckoutModal();\n  const [promoCode, setPromoCode] = useState(\"\");\n  const [isApplyingPromo, setIsApplyingPromo] = useState(false);\n\n  // Calculer les économies totales (simplifié car pas de prix originaux dans CartItem)\n  const totalSavings = 0;\n\n  // Calculer les points de fidélité\n  const loyaltyPoints = Math.floor(cart.totalAmount / 100); // 1 point par euro\n\n  // <PERSON>culer les valeurs dérivées\n  const subtotal = cart.totalAmount;\n  const tax = subtotal * 0.2; // TVA 20%\n  const shipping = subtotal >= 50 ? 0 : 5.99; // Livraison gratuite à partir de 50€\n  const total = subtotal + tax + shipping;\n\n  const handleApplyPromoCode = async () => {\n    if (!promoCode.trim()) {\n      addNotification({\n        type: \"error\",\n        title: \"Code promo\",\n        message: \"Veuillez saisir un code promo\",\n      });\n      return;\n    }\n\n    setIsApplyingPromo(true);\n    try {\n      // Simulation d'application de code promo\n      await new Promise((resolve) => setTimeout(resolve, 1000));\n\n      // Codes promo de démonstration\n      const validCodes = {\n        WELCOME10: { discount: 0.1, message: \"10% de réduction appliquée !\" },\n        SWEET20: { discount: 0.2, message: \"20% de réduction appliquée !\" },\n        FIRST5: { discount: 0.05, message: \"5% de réduction appliquée !\" },\n      };\n\n      const code =\n        validCodes[promoCode.toUpperCase() as keyof typeof validCodes];\n\n      if (code) {\n        addNotification({\n          type: \"success\",\n          title: \"Code promo\",\n          message: code.message,\n        });\n        // Ici on appliquerait la réduction dans le store\n      } else {\n        addNotification({\n          type: \"error\",\n          title: \"Code promo\",\n          message: \"Code promo invalide ou expiré\",\n        });\n      }\n    } catch (error) {\n      addNotification({\n        type: \"error\",\n        title: \"Code promo\",\n        message: \"Erreur lors de l'application du code promo\",\n      });\n    } finally {\n      setIsApplyingPromo(false);\n    }\n  };\n\n  const handleCheckout = () => {\n    if (cart.items.length === 0) {\n      addNotification({\n        type: \"error\",\n        title: \"Panier\",\n        message: \"Votre panier est vide\",\n      });\n      return;\n    }\n\n    // Ouvrir le modal de checkout\n    openModal();\n    addNotification({\n      type: \"info\",\n      title: \"Checkout\",\n      message: \"Ouverture du formulaire de commande...\",\n    });\n  };\n\n  return (\n    <Card className=\"sticky top-8\">\n      <CardHeader>\n        <h3 className=\"text-xl font-semibold text-gray-800\">\n          Récapitulatif de commande\n        </h3>\n      </CardHeader>\n\n      <CardContent className=\"space-y-6\">\n        {/* Order Summary */}\n        <div className=\"space-y-3\">\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">\n              Sous-total ({cart.totalItems} article\n              {cart.totalItems > 1 ? \"s\" : \"\"})\n            </span>\n            <span className=\"font-medium\">{formatPrice(subtotal)}</span>\n          </div>\n\n          {totalSavings > 0 && (\n            <div className=\"flex justify-between text-sm text-green-600\">\n              <span>Économies</span>\n              <span className=\"font-medium\">-{formatPrice(totalSavings)}</span>\n            </div>\n          )}\n\n          <div className=\"flex justify-between text-sm\">\n            <span className=\"text-gray-600\">Livraison</span>\n            <span className=\"font-medium\">\n              {shipping === 0 ? (\n                <span className=\"text-green-600\">Gratuite</span>\n              ) : (\n                formatPrice(shipping)\n              )}\n            </span>\n          </div>\n\n          {tax > 0 && (\n            <div className=\"flex justify-between text-sm\">\n              <span className=\"text-gray-600\">TVA</span>\n              <span className=\"font-medium\">{formatPrice(tax)}</span>\n            </div>\n          )}\n\n          <hr className=\"border-gray-200\" />\n\n          <div className=\"flex justify-between text-lg font-bold\">\n            <span>Total</span>\n            <motion.span\n              key={total}\n              className=\"text-pink-600\"\n              initial={{ scale: 1.1 }}\n              animate={{ scale: 1 }}\n              transition={{ duration: 0.2 }}\n            >\n              {formatPrice(total)}\n            </motion.span>\n          </div>\n        </div>\n\n        {/* Promo Code */}\n        <div className=\"space-y-3\">\n          <label className=\"block text-sm font-medium text-gray-700\">\n            Code promo\n          </label>\n          <div className=\"flex space-x-2\">\n            <Input\n              type=\"text\"\n              placeholder=\"Entrez votre code\"\n              value={promoCode}\n              onChange={(e) => setPromoCode(e.target.value)}\n              className=\"flex-1\"\n              onKeyPress={(e) => e.key === \"Enter\" && handleApplyPromoCode()}\n            />\n            <Button\n              variant=\"outline\"\n              size=\"sm\"\n              onClick={handleApplyPromoCode}\n              disabled={isApplyingPromo || !promoCode.trim()}\n            >\n              {isApplyingPromo ? \"...\" : \"Appliquer\"}\n            </Button>\n          </div>\n\n          {/* Promo Code Suggestions */}\n          <div className=\"text-xs text-gray-500\">\n            <p>Codes de démonstration :</p>\n            <div className=\"flex flex-wrap gap-1 mt-1\">\n              {[\"WELCOME10\", \"SWEET20\", \"FIRST5\"].map((code) => (\n                <button\n                  key={code}\n                  onClick={() => setPromoCode(code)}\n                  className=\"px-2 py-1 bg-gray-100 rounded text-xs hover:bg-gray-200 transition-colors\"\n                >\n                  {code}\n                </button>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Loyalty Points */}\n        {customer && (\n          <div className=\"p-3 bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <p className=\"text-sm font-medium text-gray-800\">\n                  Points de fidélité\n                </p>\n                <p className=\"text-xs text-gray-600\">\n                  Vous gagnerez {loyaltyPoints} points\n                </p>\n              </div>\n              <Badge variant=\"info\" size=\"sm\">\n                +{loyaltyPoints} 🎁\n              </Badge>\n            </div>\n          </div>\n        )}\n\n        {/* Checkout Button */}\n        <Button\n          variant=\"primary\"\n          size=\"lg\"\n          onClick={handleCheckout}\n          disabled={cart.items.length === 0}\n          fullWidth\n          className=\"text-lg py-4\"\n        >\n          <span className=\"mr-2\">💳</span>\n          Procéder au paiement\n        </Button>\n\n        {/* Security Badges */}\n        <div className=\"grid grid-cols-2 gap-3 text-center text-xs text-gray-500\">\n          <div className=\"flex flex-col items-center\">\n            <span className=\"text-lg mb-1\">🔒</span>\n            <span>Paiement sécurisé</span>\n          </div>\n          <div className=\"flex flex-col items-center\">\n            <span className=\"text-lg mb-1\">🚚</span>\n            <span>Livraison 24h</span>\n          </div>\n        </div>\n\n        {/* Free Shipping Threshold */}\n        {shipping > 0 && (\n          <motion.div\n            className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg\"\n            initial={{ opacity: 0, height: 0 }}\n            animate={{ opacity: 1, height: \"auto\" }}\n            transition={{ duration: 0.3 }}\n          >\n            <div className=\"text-sm text-blue-700\">\n              <p className=\"font-medium\">🚚 Livraison gratuite</p>\n              <p>\n                Ajoutez {formatPrice(5000 - subtotal)} pour la livraison\n                gratuite !\n              </p>\n            </div>\n          </motion.div>\n        )}\n      </CardContent>\n    </Card>\n  );\n};\n\nexport { CartSummary };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAMA;AACA;;;AAlBA;;;;;;AAoBA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IACrC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEvD,qFAAqF;IACrF,MAAM,eAAe;IAErB,kCAAkC;IAClC,MAAM,gBAAgB,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG,MAAM,mBAAmB;IAE7E,gCAAgC;IAChC,MAAM,WAAW,KAAK,WAAW;IACjC,MAAM,MAAM,WAAW,KAAK,UAAU;IACtC,MAAM,WAAW,YAAY,KAAK,IAAI,MAAM,qCAAqC;IACjF,MAAM,QAAQ,WAAW,MAAM;IAE/B,MAAM,uBAAuB;QAC3B,IAAI,CAAC,UAAU,IAAI,IAAI;YACrB,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,mBAAmB;QACnB,IAAI;YACF,yCAAyC;YACzC,MAAM,IAAI,QAAQ,CAAC,UAAY,WAAW,SAAS;YAEnD,+BAA+B;YAC/B,MAAM,aAAa;gBACjB,WAAW;oBAAE,UAAU;oBAAK,SAAS;gBAA+B;gBACpE,SAAS;oBAAE,UAAU;oBAAK,SAAS;gBAA+B;gBAClE,QAAQ;oBAAE,UAAU;oBAAM,SAAS;gBAA8B;YACnE;YAEA,MAAM,OACJ,UAAU,CAAC,UAAU,WAAW,GAA8B;YAEhE,IAAI,MAAM;gBACR,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS,KAAK,OAAO;gBACvB;YACA,iDAAiD;YACnD,OAAO;gBACL,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SAAS;gBACX;YACF;QACF,EAAE,OAAO,OAAO;YACd,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,SAAU;YACR,mBAAmB;QACrB;IACF;IAEA,MAAM,iBAAiB;QACrB,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;YAC3B,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;YACA;QACF;QAEA,8BAA8B;QAC9B;QACA,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,qBACE,4SAAC,mIAAA,CAAA,OAAI;QAAC,WAAU;;0BACd,4SAAC,mIAAA,CAAA,aAAU;0BACT,cAAA,4SAAC;oBAAG,WAAU;8BAAsC;;;;;;;;;;;0BAKtD,4SAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;;4CAAgB;4CACjB,KAAK,UAAU;4CAAC;4CAC5B,KAAK,UAAU,GAAG,IAAI,MAAM;4CAAG;;;;;;;kDAElC,4SAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;4BAG5C,eAAe,mBACd,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;kDAAK;;;;;;kDACN,4SAAC;wCAAK,WAAU;;4CAAc;4CAAE,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;0CAIhD,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,4SAAC;wCAAK,WAAU;kDACb,aAAa,kBACZ,4SAAC;4CAAK,WAAU;sDAAiB;;;;;mDAEjC,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;4BAKjB,MAAM,mBACL,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;kDAChC,4SAAC;wCAAK,WAAU;kDAAe,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;0CAI/C,4SAAC;gCAAG,WAAU;;;;;;0CAEd,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;kDAAK;;;;;;kDACN,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;wCAEV,WAAU;wCACV,SAAS;4CAAE,OAAO;wCAAI;wCACtB,SAAS;4CAAE,OAAO;wCAAE;wCACpB,YAAY;4CAAE,UAAU;wCAAI;kDAE3B,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;uCANR;;;;;;;;;;;;;;;;;kCAYX,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAM,WAAU;0CAA0C;;;;;;0CAG3D,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,oIAAA,CAAA,QAAK;wCACJ,MAAK;wCACL,aAAY;wCACZ,OAAO;wCACP,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,KAAK;wCAC5C,WAAU;wCACV,YAAY,CAAC,IAAM,EAAE,GAAG,KAAK,WAAW;;;;;;kDAE1C,4SAAC,qIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS;wCACT,UAAU,mBAAmB,CAAC,UAAU,IAAI;kDAE3C,kBAAkB,QAAQ;;;;;;;;;;;;0CAK/B,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;kDAAE;;;;;;kDACH,4SAAC;wCAAI,WAAU;kDACZ;4CAAC;4CAAa;4CAAW;yCAAS,CAAC,GAAG,CAAC,CAAC,qBACvC,4SAAC;gDAEC,SAAS,IAAM,aAAa;gDAC5B,WAAU;0DAET;+CAJI;;;;;;;;;;;;;;;;;;;;;;oBAYd,0BACC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;;sDACC,4SAAC;4CAAE,WAAU;sDAAoC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;;gDAAwB;gDACpB;gDAAc;;;;;;;;;;;;;8CAGjC,4SAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,MAAK;;wCAAK;wCAC5B;wCAAc;;;;;;;;;;;;;;;;;;kCAOxB,4SAAC,qIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU,KAAK,KAAK,CAAC,MAAM,KAAK;wBAChC,SAAS;wBACT,WAAU;;0CAEV,4SAAC;gCAAK,WAAU;0CAAO;;;;;;4BAAS;;;;;;;kCAKlC,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,4SAAC;kDAAK;;;;;;;;;;;;0CAER,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAK,WAAU;kDAAe;;;;;;kDAC/B,4SAAC;kDAAK;;;;;;;;;;;;;;;;;;oBAKT,WAAW,mBACV,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,YAAY;4BAAE,UAAU;wBAAI;kCAE5B,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAE,WAAU;8CAAc;;;;;;8CAC3B,4SAAC;;wCAAE;wCACQ,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,OAAO;wCAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAStD;GA9PM;;QACa,4KAAA,CAAA,UAAO;QACH,qIAAA,CAAA,cAAW;QACJ,yIAAA,CAAA,mBAAgB;QACtB,8IAAA,CAAA,mBAAgB;;;KAJlC", "debugId": null}}, {"offset": {"line": 969, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/cart/EmptyCart.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\nimport { <PERSON><PERSON>, <PERSON>, CardContent } from '@/components/ui'\nimport { fadeIn, slideUp } from '@/lib/animations'\n\nconst EmptyCart: React.FC = () => {\n  const handleStartShopping = () => {\n    const productsSection = document.getElementById('products')\n    productsSection?.scrollIntoView({ behavior: 'smooth' })\n  }\n\n  return (\n    <motion.div\n      className=\"max-w-2xl mx-auto\"\n      initial={fadeIn.initial}\n      animate={fadeIn.animate}\n      exit={fadeIn.initial}\n      transition={{ duration: 0.5 }}\n    >\n      <Card className=\"text-center\">\n        <CardContent className=\"py-16 px-8\">\n          {/* Empty Cart Animation */}\n          <motion.div\n            className=\"mb-8\"\n            initial={{ scale: 0 }}\n            animate={{ scale: 1 }}\n            transition={{ \n              type: 'spring', \n              stiffness: 200, \n              damping: 20,\n              delay: 0.2 \n            }}\n          >\n            <motion.div\n              className=\"text-8xl mb-4\"\n              animate={{ \n                rotate: [0, -10, 10, -5, 5, 0],\n                scale: [1, 1.1, 1]\n              }}\n              transition={{ \n                duration: 2,\n                repeat: Infinity,\n                repeatDelay: 3\n              }}\n            >\n              🛒\n            </motion.div>\n          </motion.div>\n\n          {/* Empty State Content */}\n          <motion.div\n            initial={slideUp.initial}\n            animate={slideUp.animate}\n            transition={{ delay: 0.4 }}\n          >\n            <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              Votre panier est vide\n            </h3>\n            <p className=\"text-gray-600 mb-8 max-w-md mx-auto\">\n              Il semble que vous n'ayez pas encore ajouté de délicieux bonbons à votre panier. \n              Découvrez nos créations artisanales aux saveurs uniques !\n            </p>\n          </motion.div>\n\n          {/* Floating Candies */}\n          <motion.div\n            className=\"relative mb-8 h-20\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 0.6 }}\n          >\n            {['🍓', '🫐', '🍏', '🍭', '🍬'].map((emoji, index) => (\n              <motion.div\n                key={emoji}\n                className=\"absolute text-2xl\"\n                style={{\n                  left: `${20 + index * 15}%`,\n                  top: '50%'\n                }}\n                animate={{\n                  y: [-10, 10, -10],\n                  rotate: [-5, 5, -5],\n                  scale: [0.8, 1.2, 0.8]\n                }}\n                transition={{\n                  duration: 3,\n                  repeat: Infinity,\n                  delay: index * 0.2,\n                  ease: 'easeInOut'\n                }}\n              >\n                {emoji}\n              </motion.div>\n            ))}\n          </motion.div>\n\n          {/* Call to Action */}\n          <motion.div\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.8 }}\n          >\n            <Button\n              variant=\"primary\"\n              size=\"lg\"\n              onClick={handleStartShopping}\n              className=\"text-lg px-8 py-4\"\n            >\n              <span className=\"mr-2\">🍭</span>\n              Découvrir nos bonbons\n            </Button>\n          </motion.div>\n\n          {/* Benefits */}\n          <motion.div\n            className=\"mt-12 grid grid-cols-1 md:grid-cols-3 gap-6\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1 }}\n          >\n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🎨</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">Artisanal</h4>\n              <p className=\"text-sm text-gray-600\">Créations uniques faites main</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">🌱</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">Naturel</h4>\n              <p className=\"text-sm text-gray-600\">Ingrédients de qualité premium</p>\n            </div>\n            \n            <div className=\"text-center\">\n              <div className=\"text-3xl mb-2\">💝</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">Parfait cadeau</h4>\n              <p className=\"text-sm text-gray-600\">Emballage soigné inclus</p>\n            </div>\n          </motion.div>\n\n          {/* Popular Flavors Preview */}\n          <motion.div\n            className=\"mt-12 p-6 bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg\"\n            initial={{ opacity: 0, scale: 0.9 }}\n            animate={{ opacity: 1, scale: 1 }}\n            transition={{ delay: 1.2 }}\n          >\n            <h4 className=\"font-semibold text-gray-800 mb-3\">Nos saveurs populaires</h4>\n            <div className=\"flex justify-center space-x-6\">\n              <motion.div\n                className=\"text-center\"\n                whileHover={{ scale: 1.1 }}\n                transition={{ type: 'spring', stiffness: 300 }}\n              >\n                <div className=\"text-2xl mb-1\">🍓</div>\n                <span className=\"text-sm text-pink-600 font-medium\">Fraise</span>\n              </motion.div>\n              \n              <motion.div\n                className=\"text-center\"\n                whileHover={{ scale: 1.1 }}\n                transition={{ type: 'spring', stiffness: 300 }}\n              >\n                <div className=\"text-2xl mb-1\">🫐</div>\n                <span className=\"text-sm text-blue-600 font-medium\">Myrtille</span>\n              </motion.div>\n              \n              <motion.div\n                className=\"text-center\"\n                whileHover={{ scale: 1.1 }}\n                transition={{ type: 'spring', stiffness: 300 }}\n              >\n                <div className=\"text-2xl mb-1\">🍏</div>\n                <span className=\"text-sm text-green-600 font-medium\">Pomme</span>\n              </motion.div>\n            </div>\n          </motion.div>\n\n          {/* Testimonial */}\n          <motion.div\n            className=\"mt-8 text-center\"\n            initial={{ opacity: 0 }}\n            animate={{ opacity: 1 }}\n            transition={{ delay: 1.4 }}\n          >\n            <blockquote className=\"text-sm text-gray-600 italic\">\n              \"Des bonbons qui éveillent vraiment les sens ! \n              La qualité artisanale se ressent à chaque bouchée.\"\n            </blockquote>\n            <cite className=\"text-xs text-gray-500 mt-2 block\">\n              - Marie, cliente satisfaite ⭐⭐⭐⭐⭐\n            </cite>\n          </motion.div>\n        </CardContent>\n      </Card>\n    </motion.div>\n  )\n}\n\nexport { EmptyCart }\n"], "names": [], "mappings": ";;;;AAGA;AACA;AAAA;AAAA;AACA;AALA;;;;;AAOA,MAAM,YAAsB;IAC1B,MAAM,sBAAsB;QAC1B,MAAM,kBAAkB,SAAS,cAAc,CAAC;QAChD,iBAAiB,eAAe;YAAE,UAAU;QAAS;IACvD;IAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAU;QACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;QACvB,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;QACvB,MAAM,2HAAA,CAAA,SAAM,CAAC,OAAO;QACpB,YAAY;YAAE,UAAU;QAAI;kBAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;YAAC,WAAU;sBACd,cAAA,4SAAC,mIAAA,CAAA,cAAW;gBAAC,WAAU;;kCAErB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,OAAO;wBAAE;wBACpB,SAAS;4BAAE,OAAO;wBAAE;wBACpB,YAAY;4BACV,MAAM;4BACN,WAAW;4BACX,SAAS;4BACT,OAAO;wBACT;kCAEA,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCACP,QAAQ;oCAAC;oCAAG,CAAC;oCAAI;oCAAI,CAAC;oCAAG;oCAAG;iCAAE;gCAC9B,OAAO;oCAAC;oCAAG;oCAAK;iCAAE;4BACpB;4BACA,YAAY;gCACV,UAAU;gCACV,QAAQ;gCACR,aAAa;4BACf;sCACD;;;;;;;;;;;kCAMH,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;wBACxB,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;wBACxB,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,4SAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,4SAAC;gCAAE,WAAU;0CAAsC;;;;;;;;;;;;kCAOrD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;kCAExB;4BAAC;4BAAM;4BAAM;4BAAM;4BAAM;yBAAK,CAAC,GAAG,CAAC,CAAC,OAAO,sBAC1C,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCAET,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC;oCAC3B,KAAK;gCACP;gCACA,SAAS;oCACP,GAAG;wCAAC,CAAC;wCAAI;wCAAI,CAAC;qCAAG;oCACjB,QAAQ;wCAAC,CAAC;wCAAG;wCAAG,CAAC;qCAAE;oCACnB,OAAO;wCAAC;wCAAK;wCAAK;qCAAI;gCACxB;gCACA,YAAY;oCACV,UAAU;oCACV,QAAQ;oCACR,OAAO,QAAQ;oCACf,MAAM;gCACR;0CAEC;+BAlBI;;;;;;;;;;kCAwBX,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,SAAS;4BACT,WAAU;;8CAEV,4SAAC;oCAAK,WAAU;8CAAO;;;;;;gCAAS;;;;;;;;;;;;kCAMpC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAE;;0CAEvB,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,4SAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,4SAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,4SAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,4SAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAGvC,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAI,WAAU;kDAAgB;;;;;;kDAC/B,4SAAC;wCAAG,WAAU;kDAAmC;;;;;;kDACjD,4SAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAKzC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAI;wBAClC,SAAS;4BAAE,SAAS;4BAAG,OAAO;wBAAE;wBAChC,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,4SAAC;gCAAG,WAAU;0CAAmC;;;;;;0CACjD,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,MAAM;4CAAU,WAAW;wCAAI;;0DAE7C,4SAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,4SAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,MAAM;4CAAU,WAAW;wCAAI;;0DAE7C,4SAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,4SAAC;gDAAK,WAAU;0DAAoC;;;;;;;;;;;;kDAGtD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAI;wCACzB,YAAY;4CAAE,MAAM;4CAAU,WAAW;wCAAI;;0DAE7C,4SAAC;gDAAI,WAAU;0DAAgB;;;;;;0DAC/B,4SAAC;gDAAK,WAAU;0DAAqC;;;;;;;;;;;;;;;;;;;;;;;;kCAM3D,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;wBAAE;wBACtB,SAAS;4BAAE,SAAS;wBAAE;wBACtB,YAAY;4BAAE,OAAO;wBAAI;;0CAEzB,4SAAC;gCAAW,WAAU;0CAA+B;;;;;;0CAIrD,4SAAC;gCAAK,WAAU;0CAAmC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/D;KA/LM", "debugId": null}}, {"offset": {"line": 1490, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/CartSection.tsx"], "sourcesContent": ["\"use client\";\n\n// import { CheckoutFloatingCandies } from \"@/components/animations\";\nimport { CartItem } from \"@/components/cart/CartItem\";\nimport { CartSummary } from \"@/components/cart/CartSummary\";\nimport { EmptyCart } from \"@/components/cart/EmptyCart\";\nimport { <PERSON>ge, Button, Card, CardContent, CardHeader } from \"@/components/ui\";\nimport {\n  fadeIn,\n  slideUp,\n  staggerContainer,\n  staggerItem,\n} from \"@/lib/animations\";\nimport { formatPrice } from \"@/lib/utils\";\nimport { useCart, useCheckoutModal, useNotifications } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React from \"react\";\n\nconst CartSection: React.FC = () => {\n  const { cart, updateQuantity, removeItem, clearCart } = useCart();\n\n  const items = cart.items;\n  const total = cart.totalAmount;\n  const itemCount = cart.totalItems;\n  const { addNotification } = useNotifications();\n  const { openModal } = useCheckoutModal();\n\n  const handleQuantityUpdate = (itemId: string, newQuantity: number) => {\n    if (newQuantity === 0) {\n      removeItem(itemId);\n      addNotification({\n        type: \"info\",\n        title: \"Panier\",\n        message: \"Produit retiré du panier\",\n      });\n    } else {\n      updateQuantity(itemId, newQuantity);\n    }\n  };\n\n  const handleRemoveItem = (itemId: string) => {\n    removeItem(itemId);\n    addNotification({\n      type: \"info\",\n      title: \"Panier\",\n      message: \"Produit retiré du panier\",\n    });\n  };\n\n  const handleClearCart = () => {\n    clearCart();\n    addNotification({\n      type: \"info\",\n      title: \"Panier\",\n      message: \"Panier vidé\",\n    });\n  };\n\n  return (\n    <section id=\"cart\" className=\"py-8 bg-gray-50 relative overflow-hidden\">\n      {/* Background Elements - Removed for cleaner design */}\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={fadeIn.initial}\n          whileInView={fadeIn.animate}\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-4xl md:text-5xl font-bold mb-6\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n          >\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\">\n              Votre Panier\n            </span>\n            <br />\n            <span className=\"text-gray-800\">Gourmand</span>\n          </motion.h2>\n\n          {itemCount > 0 && (\n            <motion.div\n              className=\"flex items-center justify-center space-x-4\"\n              initial={slideUp.initial}\n              whileInView={slideUp.animate}\n              viewport={{ once: true }}\n              transition={{ delay: 0.2 }}\n            >\n              <Badge variant=\"info\" size=\"lg\">\n                {itemCount} article{itemCount > 1 ? \"s\" : \"\"}\n              </Badge>\n              <Badge variant=\"success\" size=\"lg\">\n                Total: {formatPrice(total)}\n              </Badge>\n            </motion.div>\n          )}\n        </motion.div>\n\n        {/* Cart Content */}\n        <div className=\"max-w-6xl mx-auto\">\n          <AnimatePresence mode=\"wait\">\n            {items.length === 0 ? (\n              <EmptyCart key=\"empty\" />\n            ) : (\n              <motion.div\n                key=\"cart-content\"\n                className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\"\n                initial={{ opacity: 0 }}\n                animate={{ opacity: 1 }}\n                exit={{ opacity: 0 }}\n                transition={{ duration: 0.3 }}\n              >\n                {/* Cart Items */}\n                <div className=\"lg:col-span-2\">\n                  <Card>\n                    <CardHeader className=\"flex flex-row items-center justify-between\">\n                      <h3 className=\"text-xl font-semibold text-gray-800\">\n                        Articles dans votre panier\n                      </h3>\n                      {items.length > 1 && (\n                        <Button\n                          variant=\"outline\"\n                          size=\"sm\"\n                          onClick={handleClearCart}\n                          className=\"text-red-600 hover:text-red-700 hover:border-red-300\"\n                        >\n                          Vider le panier\n                        </Button>\n                      )}\n                    </CardHeader>\n\n                    <CardContent className=\"p-0\">\n                      <motion.div\n                        variants={staggerContainer}\n                        initial=\"initial\"\n                        animate=\"animate\"\n                        className=\"divide-y divide-gray-200\"\n                      >\n                        <AnimatePresence>\n                          {items.map((item) => (\n                            <motion.div\n                              key={item.id}\n                              variants={staggerItem}\n                              initial={{ opacity: 0, x: -20 }}\n                              animate={{ opacity: 1, x: 0 }}\n                              exit={{\n                                opacity: 0,\n                                x: 20,\n                                height: 0,\n                                marginTop: 0,\n                                marginBottom: 0,\n                                paddingTop: 0,\n                                paddingBottom: 0,\n                              }}\n                              transition={{ duration: 0.3 }}\n                              layout\n                            >\n                              <CartItem\n                                item={item}\n                                onQuantityUpdate={handleQuantityUpdate}\n                                onRemove={handleRemoveItem}\n                              />\n                            </motion.div>\n                          ))}\n                        </AnimatePresence>\n                      </motion.div>\n                    </CardContent>\n                  </Card>\n                </div>\n\n                {/* Cart Summary */}\n                <div className=\"lg:col-span-1\">\n                  <motion.div\n                    initial={{ opacity: 0, y: 20 }}\n                    animate={{ opacity: 1, y: 0 }}\n                    transition={{ delay: 0.3 }}\n                  >\n                    <CartSummary />\n                  </motion.div>\n                </div>\n              </motion.div>\n            )}\n          </AnimatePresence>\n        </div>\n\n        {/* Quick Actions */}\n        {items.length > 0 && (\n          <motion.div\n            className=\"mt-12 text-center\"\n            initial={{ opacity: 0, y: 20 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 0.5 }}\n          >\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center items-center\">\n              <Button\n                variant=\"outline\"\n                size=\"lg\"\n                onClick={() => {\n                  const productsSection = document.getElementById(\"products\");\n                  productsSection?.scrollIntoView({ behavior: \"smooth\" });\n                }}\n                className=\"min-w-[200px]\"\n              >\n                <span className=\"mr-2\">🍭</span>\n                Continuer mes achats\n              </Button>\n\n              <Button\n                variant=\"primary\"\n                size=\"lg\"\n                onClick={() => {\n                  openModal();\n                  addNotification({\n                    type: \"info\",\n                    title: \"Checkout\",\n                    message: \"Ouverture du formulaire de commande...\",\n                  });\n                }}\n                className=\"min-w-[200px]\"\n              >\n                <span className=\"mr-2\">💳</span>\n                Procéder au paiement\n              </Button>\n            </div>\n          </motion.div>\n        )}\n\n        {/* Trust Indicators */}\n        <motion.div\n          className=\"mt-16 grid grid-cols-1 md:grid-cols-3 gap-6 max-w-4xl mx-auto\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ delay: 0.6 }}\n        >\n          <div className=\"text-center\">\n            <div className=\"text-3xl mb-2\">🔒</div>\n            <h4 className=\"font-semibold text-gray-800 mb-1\">\n              Paiement sécurisé\n            </h4>\n            <p className=\"text-sm text-gray-600\">Vos données sont protégées</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"text-3xl mb-2\">🚚</div>\n            <h4 className=\"font-semibold text-gray-800 mb-1\">\n              Livraison rapide\n            </h4>\n            <p className=\"text-sm text-gray-600\">Expédition sous 24h</p>\n          </div>\n\n          <div className=\"text-center\">\n            <div className=\"text-3xl mb-2\">💝</div>\n            <h4 className=\"font-semibold text-gray-800 mb-1\">\n              Emballage soigné\n            </h4>\n            <p className=\"text-sm text-gray-600\">Parfait pour offrir</p>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport { CartSection };\n"], "names": [], "mappings": ";;;;AAEA,qEAAqE;AACrE;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AAMA;AACA;AAAA;AAAA;AAAA;AACA;AAAA;;;AAfA;;;;;;;;;AAkBA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,IAAI,EAAE,cAAc,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IAE9D,MAAM,QAAQ,KAAK,KAAK;IACxB,MAAM,QAAQ,KAAK,WAAW;IAC9B,MAAM,YAAY,KAAK,UAAU;IACjC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAC3C,MAAM,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8IAAA,CAAA,mBAAgB,AAAD;IAErC,MAAM,uBAAuB,CAAC,QAAgB;QAC5C,IAAI,gBAAgB,GAAG;YACrB,WAAW;YACX,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF,OAAO;YACL,eAAe,QAAQ;QACzB;IACF;IAEA,MAAM,mBAAmB,CAAC;QACxB,WAAW;QACX,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB;QACA,gBAAgB;YACd,MAAM;YACN,OAAO;YACP,SAAS;QACX;IACF;IAEA,qBACE,4SAAC;QAAQ,IAAG;QAAO,WAAU;kBAG3B,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;oBAC3B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4SAAC;oCAAK,WAAU;8CAA6E;;;;;;8CAG7F,4SAAC;;;;;8CACD,4SAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;wBAGjC,YAAY,mBACX,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,4SAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAO,MAAK;;wCACxB;wCAAU;wCAAS,YAAY,IAAI,MAAM;;;;;;;8CAE5C,4SAAC,oIAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAU,MAAK;;wCAAK;wCACzB,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE;;;;;;;;;;;;;;;;;;;8BAO5B,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,kSAAA,CAAA,kBAAe;wBAAC,MAAK;kCACnB,MAAM,MAAM,KAAK,kBAChB,4SAAC,0IAAA,CAAA,YAAS,MAAK;;;;iDAEf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;4BAAE;4BACtB,SAAS;gCAAE,SAAS;4BAAE;4BACtB,MAAM;gCAAE,SAAS;4BAAE;4BACnB,YAAY;gCAAE,UAAU;4BAAI;;8CAG5B,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,mIAAA,CAAA,OAAI;;0DACH,4SAAC,mIAAA,CAAA,aAAU;gDAAC,WAAU;;kEACpB,4SAAC;wDAAG,WAAU;kEAAsC;;;;;;oDAGnD,MAAM,MAAM,GAAG,mBACd,4SAAC,qIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS;wDACT,WAAU;kEACX;;;;;;;;;;;;0DAML,4SAAC,mIAAA,CAAA,cAAW;gDAAC,WAAU;0DACrB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,UAAU,2HAAA,CAAA,mBAAgB;oDAC1B,SAAQ;oDACR,SAAQ;oDACR,WAAU;8DAEV,cAAA,4SAAC,kSAAA,CAAA,kBAAe;kEACb,MAAM,GAAG,CAAC,CAAC,qBACV,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gEAET,UAAU,2HAAA,CAAA,cAAW;gEACrB,SAAS;oEAAE,SAAS;oEAAG,GAAG,CAAC;gEAAG;gEAC9B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,MAAM;oEACJ,SAAS;oEACT,GAAG;oEACH,QAAQ;oEACR,WAAW;oEACX,cAAc;oEACd,YAAY;oEACZ,eAAe;gEACjB;gEACA,YAAY;oEAAE,UAAU;gEAAI;gEAC5B,MAAM;0EAEN,cAAA,4SAAC,yIAAA,CAAA,WAAQ;oEACP,MAAM;oEACN,kBAAkB;oEAClB,UAAU;;;;;;+DAnBP,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8CA8B1B,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;kDAEzB,cAAA,4SAAC,4IAAA,CAAA,cAAW;;;;;;;;;;;;;;;;2BAxEZ;;;;;;;;;;;;;;;gBAiFX,MAAM,MAAM,GAAG,mBACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAC5B,YAAY;wBAAE,OAAO;oBAAI;8BAEzB,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP,MAAM,kBAAkB,SAAS,cAAc,CAAC;oCAChD,iBAAiB,eAAe;wCAAE,UAAU;oCAAS;gCACvD;gCACA,WAAU;;kDAEV,4SAAC;wCAAK,WAAU;kDAAO;;;;;;oCAAS;;;;;;;0CAIlC,4SAAC,qIAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,MAAK;gCACL,SAAS;oCACP;oCACA,gBAAgB;wCACd,MAAM;wCACN,OAAO;wCACP,SAAS;oCACX;gCACF;gCACA,WAAU;;kDAEV,4SAAC;wCAAK,WAAU;kDAAO;;;;;;oCAAS;;;;;;;;;;;;;;;;;;8BAQxC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,OAAO;oBAAI;;sCAEzB,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,4SAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,4SAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,4SAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,4SAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;sCAGvC,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;8CAAgB;;;;;;8CAC/B,4SAAC;oCAAG,WAAU;8CAAmC;;;;;;8CAGjD,4SAAC;oCAAE,WAAU;8CAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMjD;GAvPM;;QACoD,4KAAA,CAAA,UAAO;QAKnC,yIAAA,CAAA,mBAAgB;QACtB,8IAAA,CAAA,mBAAgB;;;KAPlC", "debugId": null}}, {"offset": {"line": 2060, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/ConfettiAnimation.tsx"], "sourcesContent": ["\"use client\";\n\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useCallback, useEffect, useMemo, useState } from \"react\";\n\nexport interface ConfettiAnimationProps {\n  trigger?: boolean;\n  duration?: number;\n  particleCount?: number;\n  colors?: string[];\n  size?: \"sm\" | \"md\" | \"lg\";\n  className?: string;\n}\n\ninterface Particle {\n  id: number;\n  x: number;\n  y: number;\n  rotation: number;\n  color: string;\n  size: number;\n  velocityX: number;\n  velocityY: number;\n  gravity: number;\n}\n\nconst ConfettiAnimation: React.FC<ConfettiAnimationProps> = ({\n  trigger = false,\n  duration = 3000,\n  particleCount = 50,\n  colors = [\"#ec4899\", \"#f97316\", \"#eab308\", \"#22c55e\", \"#3b82f6\", \"#8b5cf6\"],\n  size = \"md\",\n  className = \"\",\n}) => {\n  const [particles, setParticles] = useState<Particle[]>([]);\n  const [isActive, setIsActive] = useState(false);\n\n  const sizeConfig = useMemo(\n    () => ({\n      sm: { min: 4, max: 8 },\n      md: { min: 6, max: 12 },\n      lg: { min: 8, max: 16 },\n    }),\n    []\n  );\n\n  const createParticle = useCallback(\n    (id: number): Particle => {\n      const sizeRange = sizeConfig[size];\n      return {\n        id,\n        x: Math.random() * 100, // Percentage\n        y: -10, // Start above viewport\n        rotation: Math.random() * 360,\n        color: colors[Math.floor(Math.random() * colors.length)],\n        size: Math.random() * (sizeRange.max - sizeRange.min) + sizeRange.min,\n        velocityX: (Math.random() - 0.5) * 4, // Horizontal velocity\n        velocityY: Math.random() * 2 + 1, // Vertical velocity\n        gravity: Math.random() * 0.1 + 0.05, // Gravity effect\n      };\n    },\n    [colors, size, sizeConfig]\n  );\n\n  const createConfetti = useCallback(() => {\n    const newParticles: Particle[] = [];\n    for (let i = 0; i < particleCount; i++) {\n      newParticles.push(createParticle(i));\n    }\n    setParticles(newParticles);\n    setIsActive(true);\n\n    // Clean up after duration\n    setTimeout(() => {\n      setIsActive(false);\n      setParticles([]);\n    }, duration);\n  }, [particleCount, duration, createParticle]);\n\n  useEffect(() => {\n    if (trigger) {\n      createConfetti();\n    }\n  }, [trigger, createConfetti]);\n\n  if (!isActive) return null;\n\n  return (\n    <div className={`fixed inset-0 pointer-events-none z-50 ${className}`}>\n      <AnimatePresence>\n        {particles.map((particle) => (\n          <motion.div\n            key={particle.id}\n            className=\"absolute\"\n            initial={{\n              x: `${particle.x}vw`,\n              y: \"-10vh\",\n              rotate: particle.rotation,\n              scale: 0,\n              opacity: 0,\n            }}\n            animate={{\n              x: `${particle.x + particle.velocityX * 10}vw`,\n              y: \"110vh\",\n              rotate: particle.rotation + 720,\n              scale: [0, 1, 1, 0.8],\n              opacity: [0, 1, 1, 0],\n            }}\n            transition={{\n              duration: duration / 1000,\n              ease: \"easeOut\",\n              times: [0, 0.1, 0.8, 1],\n            }}\n            exit={{ opacity: 0, scale: 0 }}\n            style={{\n              width: particle.size,\n              height: particle.size,\n              backgroundColor: particle.color,\n              borderRadius: Math.random() > 0.5 ? \"50%\" : \"0%\",\n            }}\n          />\n        ))}\n      </AnimatePresence>\n    </div>\n  );\n};\n\n// Composant de confettis spécialisé pour les célébrations\nexport const CelebrationConfetti: React.FC<{ trigger: boolean }> = ({\n  trigger,\n}) => (\n  <ConfettiAnimation\n    trigger={trigger}\n    duration={4000}\n    particleCount={100}\n    size=\"lg\"\n    colors={[\n      \"#ec4899\",\n      \"#f97316\",\n      \"#eab308\",\n      \"#22c55e\",\n      \"#3b82f6\",\n      \"#8b5cf6\",\n      \"#ef4444\",\n    ]}\n  />\n);\n\n// Composant de confettis pour l'ajout au panier\nexport const CartConfetti: React.FC<{ trigger: boolean }> = ({ trigger }) => (\n  <ConfettiAnimation\n    trigger={trigger}\n    duration={2000}\n    particleCount={30}\n    size=\"md\"\n    colors={[\"#ec4899\", \"#f97316\", \"#eab308\"]}\n  />\n);\n\n// Composant de confettis pour les commandes\nexport const OrderConfetti: React.FC<{ trigger: boolean }> = ({ trigger }) => (\n  <ConfettiAnimation\n    trigger={trigger}\n    duration={5000}\n    particleCount={150}\n    size=\"lg\"\n    colors={[\"#22c55e\", \"#10b981\", \"#059669\", \"#047857\"]}\n  />\n);\n\nexport { ConfettiAnimation };\n"], "names": [], "mappings": ";;;;;;;AAEA;AAAA;AACA;;;AAHA;;;AA0BA,MAAM,oBAAsD,CAAC,EAC3D,UAAU,KAAK,EACf,WAAW,IAAI,EACf,gBAAgB,EAAE,EAClB,SAAS;IAAC;IAAW;IAAW;IAAW;IAAW;IAAW;CAAU,EAC3E,OAAO,IAAI,EACX,YAAY,EAAE,EACf;;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAc,EAAE;IACzD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,MAAM,aAAa,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;iDACvB,IAAM,CAAC;gBACL,IAAI;oBAAE,KAAK;oBAAG,KAAK;gBAAE;gBACrB,IAAI;oBAAE,KAAK;oBAAG,KAAK;gBAAG;gBACtB,IAAI;oBAAE,KAAK;oBAAG,KAAK;gBAAG;YACxB,CAAC;gDACD,EAAE;IAGJ,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yDAC/B,CAAC;YACC,MAAM,YAAY,UAAU,CAAC,KAAK;YAClC,OAAO;gBACL;gBACA,GAAG,KAAK,MAAM,KAAK;gBACnB,GAAG,CAAC;gBACJ,UAAU,KAAK,MAAM,KAAK;gBAC1B,OAAO,MAAM,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,OAAO,MAAM,EAAE;gBACxD,MAAM,KAAK,MAAM,KAAK,CAAC,UAAU,GAAG,GAAG,UAAU,GAAG,IAAI,UAAU,GAAG;gBACrE,WAAW,CAAC,KAAK,MAAM,KAAK,GAAG,IAAI;gBACnC,WAAW,KAAK,MAAM,KAAK,IAAI;gBAC/B,SAAS,KAAK,MAAM,KAAK,MAAM;YACjC;QACF;wDACA;QAAC;QAAQ;QAAM;KAAW;IAG5B,MAAM,iBAAiB,CAAA,GAAA,4QAAA,CAAA,cAAW,AAAD;yDAAE;YACjC,MAAM,eAA2B,EAAE;YACnC,IAAK,IAAI,IAAI,GAAG,IAAI,eAAe,IAAK;gBACtC,aAAa,IAAI,CAAC,eAAe;YACnC;YACA,aAAa;YACb,YAAY;YAEZ,0BAA0B;YAC1B;iEAAW;oBACT,YAAY;oBACZ,aAAa,EAAE;gBACjB;gEAAG;QACL;wDAAG;QAAC;QAAe;QAAU;KAAe;IAE5C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;uCAAE;YACR,IAAI,SAAS;gBACX;YACF;QACF;sCAAG;QAAC;QAAS;KAAe;IAE5B,IAAI,CAAC,UAAU,OAAO;IAEtB,qBACE,4SAAC;QAAI,WAAW,CAAC,uCAAuC,EAAE,WAAW;kBACnE,cAAA,4SAAC,kSAAA,CAAA,kBAAe;sBACb,UAAU,GAAG,CAAC,CAAC,yBACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,SAAS;wBACP,GAAG,GAAG,SAAS,CAAC,CAAC,EAAE,CAAC;wBACpB,GAAG;wBACH,QAAQ,SAAS,QAAQ;wBACzB,OAAO;wBACP,SAAS;oBACX;oBACA,SAAS;wBACP,GAAG,GAAG,SAAS,CAAC,GAAG,SAAS,SAAS,GAAG,GAAG,EAAE,CAAC;wBAC9C,GAAG;wBACH,QAAQ,SAAS,QAAQ,GAAG;wBAC5B,OAAO;4BAAC;4BAAG;4BAAG;4BAAG;yBAAI;wBACrB,SAAS;4BAAC;4BAAG;4BAAG;4BAAG;yBAAE;oBACvB;oBACA,YAAY;wBACV,UAAU,WAAW;wBACrB,MAAM;wBACN,OAAO;4BAAC;4BAAG;4BAAK;4BAAK;yBAAE;oBACzB;oBACA,MAAM;wBAAE,SAAS;wBAAG,OAAO;oBAAE;oBAC7B,OAAO;wBACL,OAAO,SAAS,IAAI;wBACpB,QAAQ,SAAS,IAAI;wBACrB,iBAAiB,SAAS,KAAK;wBAC/B,cAAc,KAAK,MAAM,KAAK,MAAM,QAAQ;oBAC9C;mBA3BK,SAAS,EAAE;;;;;;;;;;;;;;;AAiC5B;GAnGM;KAAA;AAsGC,MAAM,sBAAsD,CAAC,EAClE,OAAO,EACR,iBACC,4SAAC;QACC,SAAS;QACT,UAAU;QACV,eAAe;QACf,MAAK;QACL,QAAQ;YACN;YACA;YACA;YACA;YACA;YACA;YACA;SACD;;;;;;MAhBQ;AAqBN,MAAM,eAA+C,CAAC,EAAE,OAAO,EAAE,iBACtE,4SAAC;QACC,SAAS;QACT,UAAU;QACV,eAAe;QACf,MAAK;QACL,QAAQ;YAAC;YAAW;YAAW;SAAU;;;;;;MANhC;AAWN,MAAM,gBAAgD,CAAC,EAAE,OAAO,EAAE,iBACvE,4SAAC;QACC,SAAS;QACT,UAAU;QACV,eAAe;QACf,MAAK;QACL,QAAQ;YAAC;YAAW;YAAW;YAAW;SAAU;;;;;;MAN3C", "debugId": null}}, {"offset": {"line": 2289, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/FloatingCandy.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nexport interface FloatingCandyProps {\n  emoji?: string;\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  x?: number; // Position X en pourcentage (0-100)\n  y?: number; // Position Y en pourcentage (0-100)\n  delay?: number;\n  duration?: number;\n  amplitude?: number; // Amplitude du mouvement\n  className?: string;\n  style?: React.CSSProperties;\n}\n\nconst FloatingCandy: React.FC<FloatingCandyProps> = ({\n  emoji = \"🍭\",\n  size = \"md\",\n  x = 50,\n  y = 50,\n  delay = 0,\n  duration = 4,\n  amplitude = 20,\n  className = \"\",\n  style = {},\n}) => {\n  const sizeClasses = {\n    sm: \"text-2xl\",\n    md: \"text-4xl\",\n    lg: \"text-6xl\",\n    xl: \"text-8xl\",\n  };\n\n  return (\n    <motion.div\n      className={`absolute pointer-events-none select-none ${sizeClasses[size]} ${className}`}\n      style={{\n        left: `${x}%`,\n        top: `${y}%`,\n        transform: \"translate(-50%, -50%)\",\n        ...style,\n      }}\n      initial={{\n        y: 0,\n        x: 0,\n        rotate: 0,\n        scale: 0.8,\n        opacity: 0.7,\n      }}\n      animate={{\n        y: [-amplitude, amplitude, -amplitude],\n        x: [-amplitude / 2, amplitude / 2, -amplitude / 2],\n        rotate: [0, 10, -10, 0],\n        scale: [0.8, 1.1, 0.9, 1],\n        opacity: [0.7, 1, 0.8, 0.9],\n      }}\n      transition={{\n        duration,\n        repeat: Infinity,\n        ease: \"easeInOut\",\n        delay,\n        times: [0, 0.33, 0.66, 1],\n      }}\n    >\n      {emoji}\n    </motion.div>\n  );\n};\n\n// Composant pour créer plusieurs bonbons flottants\nexport interface FloatingCandyFieldProps {\n  candies?: Array<{\n    emoji: string;\n    x: number;\n    y: number;\n    delay?: number;\n    size?: \"sm\" | \"md\" | \"lg\" | \"xl\";\n  }>;\n  className?: string;\n}\n\nexport const FloatingCandyField: React.FC<FloatingCandyFieldProps> = ({\n  candies = [\n    { emoji: \"🍭\", x: 10, y: 20, delay: 0, size: \"md\" },\n    { emoji: \"🍬\", x: 80, y: 15, delay: 0.5, size: \"lg\" },\n    { emoji: \"🧁\", x: 15, y: 70, delay: 1, size: \"sm\" },\n    { emoji: \"🍰\", x: 85, y: 75, delay: 1.5, size: \"md\" },\n    { emoji: \"🎂\", x: 50, y: 10, delay: 2, size: \"xl\" },\n    { emoji: \"🍪\", x: 70, y: 60, delay: 2.5, size: \"lg\" },\n  ],\n  className = \"\",\n}) => {\n  return (\n    <div className={`absolute inset-0 overflow-hidden ${className}`}>\n      {candies.map((candy, index) => (\n        <FloatingCandy\n          key={index}\n          emoji={candy.emoji}\n          x={candy.x}\n          y={candy.y}\n          delay={candy.delay}\n          size={candy.size}\n        />\n      ))}\n    </div>\n  );\n};\n\n// Composants prédéfinis pour différents contextes\nexport const HeroFloatingCandies: React.FC = () => (\n  <FloatingCandyField\n    candies={[\n      { emoji: \"🍭\", x: 10, y: 20, delay: 0, size: \"lg\" },\n      { emoji: \"🍬\", x: 85, y: 15, delay: 0.8, size: \"md\" },\n      { emoji: \"🧁\", x: 15, y: 75, delay: 1.2, size: \"xl\" },\n      { emoji: \"🍰\", x: 90, y: 80, delay: 1.8, size: \"sm\" },\n      { emoji: \"🎂\", x: 50, y: 5, delay: 2.2, size: \"lg\" },\n      { emoji: \"🍪\", x: 75, y: 55, delay: 2.8, size: \"md\" },\n    ]}\n  />\n);\n\nexport const ProductFloatingCandies: React.FC = () => (\n  <FloatingCandyField\n    candies={[\n      { emoji: \"🍓\", x: 5, y: 30, delay: 0, size: \"md\" },\n      { emoji: \"🫐\", x: 95, y: 25, delay: 1, size: \"sm\" },\n      { emoji: \"🍏\", x: 8, y: 80, delay: 2, size: \"lg\" },\n      { emoji: \"🍭\", x: 92, y: 75, delay: 3, size: \"md\" },\n    ]}\n  />\n);\n\nexport const CheckoutFloatingCandies: React.FC = () => (\n  <FloatingCandyField\n    candies={[\n      { emoji: \"✨\", x: 20, y: 20, delay: 0, size: \"sm\" },\n      { emoji: \"🎉\", x: 80, y: 25, delay: 0.5, size: \"md\" },\n      { emoji: \"💫\", x: 25, y: 75, delay: 1, size: \"sm\" },\n      { emoji: \"🌟\", x: 75, y: 80, delay: 1.5, size: \"lg\" },\n    ]}\n  />\n);\n\n// Hook pour contrôler les animations de bonbons\nexport const useCandyAnimation = () => {\n  const [isPlaying, setIsPlaying] = React.useState(true);\n\n  const play = () => setIsPlaying(true);\n  const pause = () => setIsPlaying(false);\n  const toggle = () => setIsPlaying(!isPlaying);\n\n  return { isPlaying, play, pause, toggle };\n};\n\nexport { FloatingCandy };\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;;;AAHA;;;AAiBA,MAAM,gBAA8C,CAAC,EACnD,QAAQ,IAAI,EACZ,OAAO,IAAI,EACX,IAAI,EAAE,EACN,IAAI,EAAE,EACN,QAAQ,CAAC,EACT,WAAW,CAAC,EACZ,YAAY,EAAE,EACd,YAAY,EAAE,EACd,QAAQ,CAAC,CAAC,EACX;IACC,MAAM,cAAc;QAClB,IAAI;QACJ,IAAI;QACJ,IAAI;QACJ,IAAI;IACN;IAEA,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,WAAW,CAAC,yCAAyC,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC,EAAE,WAAW;QACvF,OAAO;YACL,MAAM,GAAG,EAAE,CAAC,CAAC;YACb,KAAK,GAAG,EAAE,CAAC,CAAC;YACZ,WAAW;YACX,GAAG,KAAK;QACV;QACA,SAAS;YACP,GAAG;YACH,GAAG;YACH,QAAQ;YACR,OAAO;YACP,SAAS;QACX;QACA,SAAS;YACP,GAAG;gBAAC,CAAC;gBAAW;gBAAW,CAAC;aAAU;YACtC,GAAG;gBAAC,CAAC,YAAY;gBAAG,YAAY;gBAAG,CAAC,YAAY;aAAE;YAClD,QAAQ;gBAAC;gBAAG;gBAAI,CAAC;gBAAI;aAAE;YACvB,OAAO;gBAAC;gBAAK;gBAAK;gBAAK;aAAE;YACzB,SAAS;gBAAC;gBAAK;gBAAG;gBAAK;aAAI;QAC7B;QACA,YAAY;YACV;YACA,QAAQ;YACR,MAAM;YACN;YACA,OAAO;gBAAC;gBAAG;gBAAM;gBAAM;aAAE;QAC3B;kBAEC;;;;;;AAGP;KApDM;AAkEC,MAAM,qBAAwD,CAAC,EACpE,UAAU;IACR;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAG,MAAM;IAAK;IAClD;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAK,MAAM;IAAK;IACpD;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAG,MAAM;IAAK;IAClD;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAK,MAAM;IAAK;IACpD;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAG,MAAM;IAAK;IAClD;QAAE,OAAO;QAAM,GAAG;QAAI,GAAG;QAAI,OAAO;QAAK,MAAM;IAAK;CACrD,EACD,YAAY,EAAE,EACf;IACC,qBACE,4SAAC;QAAI,WAAW,CAAC,iCAAiC,EAAE,WAAW;kBAC5D,QAAQ,GAAG,CAAC,CAAC,OAAO,sBACnB,4SAAC;gBAEC,OAAO,MAAM,KAAK;gBAClB,GAAG,MAAM,CAAC;gBACV,GAAG,MAAM,CAAC;gBACV,OAAO,MAAM,KAAK;gBAClB,MAAM,MAAM,IAAI;eALX;;;;;;;;;;AAUf;MAzBa;AA4BN,MAAM,sBAAgC,kBAC3C,4SAAC;QACC,SAAS;YACP;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YAClD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;YACpD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;YACpD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;YACpD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAG,OAAO;gBAAK,MAAM;YAAK;YACnD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;SACrD;;;;;;MATQ;AAaN,MAAM,yBAAmC,kBAC9C,4SAAC;QACC,SAAS;YACP;gBAAE,OAAO;gBAAM,GAAG;gBAAG,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YACjD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YAClD;gBAAE,OAAO;gBAAM,GAAG;gBAAG,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YACjD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;SACnD;;;;;;MAPQ;AAWN,MAAM,0BAAoC,kBAC/C,4SAAC;QACC,SAAS;YACP;gBAAE,OAAO;gBAAK,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YACjD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;YACpD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAG,MAAM;YAAK;YAClD;gBAAE,OAAO;gBAAM,GAAG;gBAAI,GAAG;gBAAI,OAAO;gBAAK,MAAM;YAAK;SACrD;;;;;;MAPQ;AAYN,MAAM,oBAAoB;;IAC/B,MAAM,CAAC,WAAW,aAAa,GAAG,4QAAA,CAAA,UAAK,CAAC,QAAQ,CAAC;IAEjD,MAAM,OAAO,IAAM,aAAa;IAChC,MAAM,QAAQ,IAAM,aAAa;IACjC,MAAM,SAAS,IAAM,aAAa,CAAC;IAEnC,OAAO;QAAE;QAAW;QAAM;QAAO;IAAO;AAC1C;GARa", "debugId": null}}, {"offset": {"line": 2596, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/ParallaxSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion, useScroll, useTransform } from \"framer-motion\";\nimport React, { useMemo, useRef } from \"react\";\n\nexport interface ParallaxSectionProps {\n  children: React.ReactNode;\n  speed?: number; // Vitesse du parallaxe (0.1 = lent, 1 = normal, 2 = rapide)\n  direction?: \"up\" | \"down\" | \"left\" | \"right\";\n  className?: string;\n  offset?: [number, number]; // Offset de début et fin pour le parallaxe\n}\n\nconst ParallaxSection: React.FC<ParallaxSectionProps> = ({\n  children,\n  speed = 0.5,\n  direction = \"up\",\n  className = \"\",\n  offset = [0, 1],\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"],\n  });\n\n  // Calculer la transformation basée sur la direction\n  const range = 100 * speed;\n  const transformUp = useTransform(scrollYProgress, offset, [range, -range]);\n  const transformDown = useTransform(scrollYProgress, offset, [-range, range]);\n  const transformLeft = useTransform(scrollYProgress, offset, [range, -range]);\n  const transformRight = useTransform(scrollYProgress, offset, [-range, range]);\n\n  const transform = useMemo(() => {\n    switch (direction) {\n      case \"up\":\n        return transformUp;\n      case \"down\":\n        return transformDown;\n      case \"left\":\n        return transformLeft;\n      case \"right\":\n        return transformRight;\n      default:\n        return transformUp;\n    }\n  }, [direction, transformUp, transformDown, transformLeft, transformRight]);\n\n  const getStyle = () => {\n    switch (direction) {\n      case \"up\":\n      case \"down\":\n        return { y: transform };\n      case \"left\":\n      case \"right\":\n        return { x: transform };\n      default:\n        return { y: transform };\n    }\n  };\n\n  return (\n    <div ref={ref} className={className}>\n      <motion.div style={getStyle()}>{children}</motion.div>\n    </div>\n  );\n};\n\n// Composant pour les éléments de background parallaxe\nexport interface ParallaxBackgroundProps {\n  layers?: Array<{\n    content: React.ReactNode;\n    speed: number;\n    zIndex?: number;\n    className?: string;\n  }>;\n  className?: string;\n}\n\nexport const ParallaxBackground: React.FC<ParallaxBackgroundProps> = ({\n  layers = [],\n  className = \"\",\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"],\n  });\n\n  // Créer les transformations pour chaque layer en dehors de la boucle\n  const layerTransforms = useMemo(\n    () =>\n      layers.map((layer) =>\n        useTransform(scrollYProgress, [0, 1], [0, -100 * layer.speed])\n      ),\n    [layers, scrollYProgress]\n  );\n\n  return (\n    <div ref={ref} className={`relative ${className}`}>\n      {layers.map((layer, index) => {\n        const y = layerTransforms[index];\n        return (\n          <motion.div\n            key={index}\n            className={`absolute inset-0 ${layer.className || \"\"}`}\n            style={{\n              y,\n              zIndex: layer.zIndex || index,\n            }}\n          >\n            {layer.content}\n          </motion.div>\n        );\n      })}\n    </div>\n  );\n};\n\n// Composant pour les images parallaxe\nexport interface ParallaxImageProps {\n  src: string;\n  alt: string;\n  speed?: number;\n  className?: string;\n  overlayClassName?: string;\n  overlay?: React.ReactNode;\n}\n\nexport const ParallaxImage: React.FC<ParallaxImageProps> = ({\n  src,\n  alt,\n  speed = 0.5,\n  className = \"\",\n  overlayClassName = \"\",\n  overlay,\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"],\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [0, -100 * speed]);\n\n  return (\n    <div ref={ref} className={`relative overflow-hidden ${className}`}>\n      <motion.div\n        style={{ y }}\n        className=\"absolute inset-0 scale-110\" // Scale pour éviter les espaces blancs\n      >\n        <img src={src} alt={alt} className=\"w-full h-full object-cover\" />\n      </motion.div>\n\n      {overlay && (\n        <div className={`relative z-10 ${overlayClassName}`}>{overlay}</div>\n      )}\n    </div>\n  );\n};\n\n// Composant pour les textes avec effet parallaxe\nexport interface ParallaxTextProps {\n  children: React.ReactNode;\n  speed?: number;\n  className?: string;\n  stagger?: boolean;\n}\n\nexport const ParallaxText: React.FC<ParallaxTextProps> = ({\n  children,\n  speed = 0.3,\n  className = \"\",\n  stagger = false,\n}) => {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"],\n  });\n\n  const y = useTransform(scrollYProgress, [0, 1], [50 * speed, -50 * speed]);\n  const opacity = useTransform(scrollYProgress, [0, 0.3, 0.7, 1], [0, 1, 1, 0]);\n\n  if (stagger && React.isValidElement(children)) {\n    // Pour les textes avec effet de décalage par mot/lettre\n    return (\n      <div ref={ref} className={className}>\n        <motion.div style={{ y, opacity }} className=\"inline-block\">\n          {children}\n        </motion.div>\n      </div>\n    );\n  }\n\n  return (\n    <div ref={ref} className={className}>\n      <motion.div style={{ y, opacity }}>{children}</motion.div>\n    </div>\n  );\n};\n\n// Hook pour créer des effets parallaxe personnalisés\nexport const useParallax = (\n  speed: number = 0.5,\n  offset: [number, number] = [0, 1]\n) => {\n  const ref = useRef<HTMLDivElement>(null);\n  const { scrollYProgress } = useScroll({\n    target: ref,\n    offset: [\"start end\", \"end start\"],\n  });\n\n  const y = useTransform(scrollYProgress, offset, [100 * speed, -100 * speed]);\n  const opacity = useTransform(scrollYProgress, [0, 0.2, 0.8, 1], [0, 1, 1, 0]);\n\n  return { ref, y, opacity, scrollYProgress };\n};\n\nexport { ParallaxSection };\n"], "names": [], "mappings": ";;;;;;;;AAEA;AAAA;AAAA;AACA;;;AAHA;;;AAaA,MAAM,kBAAkD,CAAC,EACvD,QAAQ,EACR,QAAQ,GAAG,EACX,YAAY,IAAI,EAChB,YAAY,EAAE,EACd,SAAS;IAAC;IAAG;CAAE,EAChB;;IACC,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,oDAAoD;IACpD,MAAM,QAAQ,MAAM;IACpB,MAAM,cAAc,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,QAAQ;QAAC;QAAO,CAAC;KAAM;IACzE,MAAM,gBAAgB,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,QAAQ;QAAC,CAAC;QAAO;KAAM;IAC3E,MAAM,gBAAgB,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,QAAQ;QAAC;QAAO,CAAC;KAAM;IAC3E,MAAM,iBAAiB,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,QAAQ;QAAC,CAAC;QAAO;KAAM;IAE5E,MAAM,YAAY,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;8CAAE;YACxB,OAAQ;gBACN,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT,KAAK;oBACH,OAAO;gBACT;oBACE,OAAO;YACX;QACF;6CAAG;QAAC;QAAW;QAAa;QAAe;QAAe;KAAe;IAEzE,MAAM,WAAW;QACf,OAAQ;YACN,KAAK;YACL,KAAK;gBACH,OAAO;oBAAE,GAAG;gBAAU;YACxB,KAAK;YACL,KAAK;gBACH,OAAO;oBAAE,GAAG;gBAAU;YACxB;gBACE,OAAO;oBAAE,GAAG;gBAAU;QAC1B;IACF;IAEA,qBACE,4SAAC;QAAI,KAAK;QAAK,WAAW;kBACxB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YAAC,OAAO;sBAAa;;;;;;;;;;;AAGtC;GArDM;;QAQwB,kRAAA,CAAA,YAAS;QAOjB,qRAAA,CAAA,eAAY;QACV,qRAAA,CAAA,eAAY;QACZ,qRAAA,CAAA,eAAY;QACX,qRAAA,CAAA,eAAY;;;KAlB/B;AAkEC,MAAM,qBAAwD,CAAC,EACpE,SAAS,EAAE,EACX,YAAY,EAAE,EACf;;;IACC,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,qEAAqE;IACrE,MAAM,kBAAkB,CAAA,GAAA,4QAAA,CAAA,UAAO,AAAD;uDAC5B,IACE,OAAO,GAAG;+DAAC,CAAC;;oBACV,OAAA,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;wBAAC;wBAAG;qBAAE,EAAE;wBAAC;wBAAG,CAAC,MAAM,MAAM,KAAK;qBAAC;gBAAA;;;;wBAA7D,qRAAA,CAAA,eAAY;;;;sDAEhB;QAAC;QAAQ;KAAgB;IAG3B,qBACE,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAC,SAAS,EAAE,WAAW;kBAC9C,OAAO,GAAG,CAAC,CAAC,OAAO;YAClB,MAAM,IAAI,eAAe,CAAC,MAAM;YAChC,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBAET,WAAW,CAAC,iBAAiB,EAAE,MAAM,SAAS,IAAI,IAAI;gBACtD,OAAO;oBACL;oBACA,QAAQ,MAAM,MAAM,IAAI;gBAC1B;0BAEC,MAAM,OAAO;eAPT;;;;;QAUX;;;;;;AAGN;IAtCa;;QAKiB,kRAAA,CAAA,YAAS;;;MAL1B;AAkDN,MAAM,gBAA8C,CAAC,EAC1D,GAAG,EACH,GAAG,EACH,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,mBAAmB,EAAE,EACrB,OAAO,EACR;;IACC,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC;QAAG,CAAC,MAAM;KAAM;IAEjE,qBACE,4SAAC;QAAI,KAAK;QAAK,WAAW,CAAC,yBAAyB,EAAE,WAAW;;0BAC/D,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,OAAO;oBAAE;gBAAE;gBACX,WAAU,6BAA6B,uCAAuC;;0BAE9E,cAAA,4SAAC;oBAAI,KAAK;oBAAK,KAAK;oBAAK,WAAU;;;;;;;;;;;YAGpC,yBACC,4SAAC;gBAAI,WAAW,CAAC,cAAc,EAAE,kBAAkB;0BAAG;;;;;;;;;;;;AAI9D;IA9Ba;;QASiB,kRAAA,CAAA,YAAS;QAK3B,qRAAA,CAAA,eAAY;;;MAdX;AAwCN,MAAM,eAA4C,CAAC,EACxD,QAAQ,EACR,QAAQ,GAAG,EACX,YAAY,EAAE,EACd,UAAU,KAAK,EAChB;;IACC,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;KAAE,EAAE;QAAC,KAAK;QAAO,CAAC,KAAK;KAAM;IACzE,MAAM,UAAU,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,IAAI,yBAAW,4QAAA,CAAA,UAAK,CAAC,cAAc,CAAC,WAAW;QAC7C,wDAAwD;QACxD,qBACE,4SAAC;YAAI,KAAK;YAAK,WAAW;sBACxB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,OAAO;oBAAE;oBAAG;gBAAQ;gBAAG,WAAU;0BAC1C;;;;;;;;;;;IAIT;IAEA,qBACE,4SAAC;QAAI,KAAK;QAAK,WAAW;kBACxB,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YAAC,OAAO;gBAAE;gBAAG;YAAQ;sBAAI;;;;;;;;;;;AAG1C;IA/Ba;;QAOiB,kRAAA,CAAA,YAAS;QAK3B,qRAAA,CAAA,eAAY;QACN,qRAAA,CAAA,eAAY;;;MAbjB;AAkCN,MAAM,cAAc,CACzB,QAAgB,GAAG,EACnB,SAA2B;IAAC;IAAG;CAAE;;IAEjC,MAAM,MAAM,CAAA,GAAA,4QAAA,CAAA,SAAM,AAAD,EAAkB;IACnC,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACpC,QAAQ;QACR,QAAQ;YAAC;YAAa;SAAY;IACpC;IAEA,MAAM,IAAI,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB,QAAQ;QAAC,MAAM;QAAO,CAAC,MAAM;KAAM;IAC3E,MAAM,UAAU,CAAA,GAAA,qRAAA,CAAA,eAAY,AAAD,EAAE,iBAAiB;QAAC;QAAG;QAAK;QAAK;KAAE,EAAE;QAAC;QAAG;QAAG;QAAG;KAAE;IAE5E,OAAO;QAAE;QAAK;QAAG;QAAS;IAAgB;AAC5C;IAda;;QAKiB,kRAAA,CAAA,YAAS;QAK3B,qRAAA,CAAA,eAAY;QACN,qRAAA,CAAA,eAAY", "debugId": null}}, {"offset": {"line": 2975, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/ContactFloatingCandies.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\n\nconst ContactFloatingCandies: React.FC = () => {\n  const contactElements = [\n    { emoji: '📧', delay: 0, x: '18%', y: '25%' },\n    { emoji: '📞', delay: 0.6, x: '82%', y: '20%' },\n    { emoji: '💬', delay: 1.2, x: '15%', y: '65%' },\n    { emoji: '📍', delay: 1.8, x: '85%', y: '70%' },\n    { emoji: '⏰', delay: 2.4, x: '50%', y: '15%' },\n    { emoji: '📱', delay: 3, x: '90%', y: '45%' },\n    { emoji: '✉️', delay: 3.6, x: '10%', y: '50%' },\n    { emoji: '🌐', delay: 4.2, x: '75%', y: '30%' }\n  ]\n\n  const socialElements = [\n    { emoji: '📘', delay: 1, x: '25%', y: '35%' },\n    { emoji: '📷', delay: 2, x: '70%', y: '25%' },\n    { emoji: '🐦', delay: 3, x: '30%', y: '75%' },\n    { emoji: '📺', delay: 4, x: '80%', y: '60%' }\n  ]\n\n  const supportElements = [\n    { emoji: '🤝', delay: 2.5, x: '40%', y: '40%' },\n    { emoji: '💝', delay: 4.5, x: '60%', y: '55%' },\n    { emoji: '⚡', delay: 6.5, x: '20%', y: '80%' },\n    { emoji: '🎯', delay: 8.5, x: '85%', y: '85%' }\n  ]\n\n  return (\n    <div className=\"absolute inset-0 pointer-events-none overflow-hidden\">\n      {/* Contact method icons */}\n      {contactElements.map((element, index) => (\n        <motion.div\n          key={`contact-${index}`}\n          className=\"absolute text-2xl opacity-20\"\n          style={{\n            left: element.x,\n            top: element.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0,\n            rotate: 0\n          }}\n          animate={{ \n            opacity: [0, 0.4, 0.1, 0.4],\n            scale: [0, 1.2, 0.8, 1],\n            rotate: [0, 25, -25, 0],\n            y: [-15, 15, -15]\n          }}\n          transition={{\n            duration: 7,\n            delay: element.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {element.emoji}\n        </motion.div>\n      ))}\n\n      {/* Social media icons */}\n      {socialElements.map((social, index) => (\n        <motion.div\n          key={`social-${index}`}\n          className=\"absolute text-3xl opacity-15\"\n          style={{\n            left: social.x,\n            top: social.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0\n          }}\n          animate={{ \n            opacity: [0, 0.3, 0.15],\n            scale: [0, 1.3, 1],\n            rotate: [0, 360],\n            x: [-20, 20, -20]\n          }}\n          transition={{\n            duration: 11,\n            delay: social.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {social.emoji}\n        </motion.div>\n      ))}\n\n      {/* Support and help icons */}\n      {supportElements.map((support, index) => (\n        <motion.div\n          key={`support-${index}`}\n          className=\"absolute text-2xl opacity-18\"\n          style={{\n            left: support.x,\n            top: support.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0\n          }}\n          animate={{ \n            opacity: [0, 0.35, 0.18],\n            scale: [0, 1.1, 1],\n            rotate: [0, 15, -15, 0],\n            y: [-10, 10, -10]\n          }}\n          transition={{\n            duration: 9,\n            delay: support.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {support.emoji}\n        </motion.div>\n      ))}\n      \n      {/* Large floating email icon */}\n      <motion.div\n        className=\"absolute top-1/6 right-1/6 text-5xl opacity-10\"\n        animate={{\n          rotate: [0, 10, -10, 0],\n          scale: [1, 1.2, 1],\n          opacity: [0.1, 0.18, 0.1]\n        }}\n        transition={{\n          duration: 10,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        📧\n      </motion.div>\n      \n      {/* Floating phone icon */}\n      <motion.div\n        className=\"absolute bottom-1/4 left-1/6 text-4xl opacity-12\"\n        animate={{\n          y: [-20, 20, -20],\n          rotate: [-12, 12, -12],\n          scale: [1, 1.15, 1]\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        📞\n      </motion.div>\n\n      {/* Floating heart for customer care */}\n      <motion.div\n        className=\"absolute top-1/2 right-1/8 text-3xl opacity-15\"\n        animate={{\n          y: [-25, 0, -25],\n          x: [-8, 8, -8],\n          scale: [0.9, 1.2, 0.9]\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        💖\n      </motion.div>\n\n      {/* Floating message bubble */}\n      <motion.div\n        className=\"absolute top-1/3 left-1/10 text-3xl opacity-12\"\n        animate={{\n          x: [-15, 15, -15],\n          y: [-8, 8, -8],\n          rotate: [0, 5, -5, 0],\n          scale: [1, 1.1, 1]\n        }}\n        transition={{\n          duration: 7,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        💬\n      </motion.div>\n\n      {/* Floating handshake for support */}\n      <motion.div\n        className=\"absolute bottom-1/3 right-1/7 text-3xl opacity-14\"\n        animate={{\n          scale: [1, 1.25, 1],\n          rotate: [0, 8, -8, 0],\n          opacity: [0.14, 0.22, 0.14]\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        🤝\n      </motion.div>\n    </div>\n  )\n}\n\nexport { ContactFloatingCandies }\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,yBAAmC;IACvC,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAK,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC7C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;KAC/C;IAED,MAAM,iBAAiB;QACrB;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;KAC7C;IAED,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAK,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC7C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;KAC/C;IAED,qBACE,4SAAC;QAAI,WAAU;;YAEZ,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,QAAQ,CAAC;wBACf,KAAK,QAAQ,CAAC;oBAChB;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACV;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAK;4BAAK;yBAAI;wBAC3B,OAAO;4BAAC;4BAAG;4BAAK;4BAAK;yBAAE;wBACvB,QAAQ;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;wBACvB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,QAAQ,KAAK;wBACpB,QAAQ;wBACR,MAAM;oBACR;8BAEC,QAAQ,KAAK;mBAxBT,CAAC,QAAQ,EAAE,OAAO;;;;;YA6B1B,eAAe,GAAG,CAAC,CAAC,QAAQ,sBAC3B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,OAAO,CAAC;wBACd,KAAK,OAAO,CAAC;oBACf;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAK;yBAAK;wBACvB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;yBAAI;wBAChB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,OAAO,KAAK;wBACnB,QAAQ;wBACR,MAAM;oBACR;8BAEC,OAAO,KAAK;mBAvBR,CAAC,OAAO,EAAE,OAAO;;;;;YA4BzB,gBAAgB,GAAG,CAAC,CAAC,SAAS,sBAC7B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,QAAQ,CAAC;wBACf,KAAK,QAAQ,CAAC;oBAChB;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAM;yBAAK;wBACxB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;wBACvB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,QAAQ,KAAK;wBACpB,QAAQ;wBACR,MAAM;oBACR;8BAEC,QAAQ,KAAK;mBAvBT,CAAC,QAAQ,EAAE,OAAO;;;;;0BA4B3B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,QAAQ;wBAAC;wBAAG;wBAAI,CAAC;wBAAI;qBAAE;oBACvB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAM;qBAAI;gBAC3B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,QAAQ;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACtB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAG,CAAC;qBAAG;oBAChB,GAAG;wBAAC,CAAC;wBAAG;wBAAG,CAAC;qBAAE;oBACd,OAAO;wBAAC;wBAAK;wBAAK;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,GAAG;wBAAC,CAAC;wBAAG;wBAAG,CAAC;qBAAE;oBACd,QAAQ;wBAAC;wBAAG;wBAAG,CAAC;wBAAG;qBAAE;oBACrB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;oBACnB,QAAQ;wBAAC;wBAAG;wBAAG,CAAC;wBAAG;qBAAE;oBACrB,SAAS;wBAAC;wBAAM;wBAAM;qBAAK;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;;;;;;;AAKP;KA9MM", "debugId": null}}, {"offset": {"line": 3403, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/FAQFloatingCandies.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\n\nconst FAQFloatingCandies: React.FC = () => {\n  const faqElements = [\n    { emoji: '❓', delay: 0, x: '12%', y: '22%' },\n    { emoji: '💡', delay: 0.7, x: '88%', y: '18%' },\n    { emoji: '📋', delay: 1.4, x: '15%', y: '68%' },\n    { emoji: '✅', delay: 2.1, x: '85%', y: '72%' },\n    { emoji: '🔍', delay: 2.8, x: '45%', y: '12%' },\n    { emoji: '📚', delay: 3.5, x: '92%', y: '45%' },\n    { emoji: '🎯', delay: 4.2, x: '8%', y: '52%' },\n    { emoji: '💬', delay: 4.9, x: '78%', y: '28%' }\n  ]\n\n  const helpIcons = [\n    { emoji: '🤝', delay: 1.5, x: '25%', y: '35%' },\n    { emoji: '📞', delay: 3, x: '65%', y: '25%' },\n    { emoji: '📧', delay: 4.5, x: '35%', y: '78%' },\n    { emoji: '⚡', delay: 6, x: '75%', y: '65%' }\n  ]\n\n  return (\n    <div className=\"absolute inset-0 pointer-events-none overflow-hidden\">\n      {/* FAQ-related icons */}\n      {faqElements.map((element, index) => (\n        <motion.div\n          key={`faq-${index}`}\n          className=\"absolute text-2xl opacity-20\"\n          style={{\n            left: element.x,\n            top: element.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0,\n            rotate: 0\n          }}\n          animate={{ \n            opacity: [0, 0.35, 0.1, 0.35],\n            scale: [0, 1.2, 0.8, 1],\n            rotate: [0, 20, -20, 0],\n            y: [-12, 12, -12]\n          }}\n          transition={{\n            duration: 8,\n            delay: element.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {element.emoji}\n        </motion.div>\n      ))}\n\n      {/* Help and support icons */}\n      {helpIcons.map((icon, index) => (\n        <motion.div\n          key={`help-${index}`}\n          className=\"absolute text-3xl opacity-15\"\n          style={{\n            left: icon.x,\n            top: icon.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0\n          }}\n          animate={{ \n            opacity: [0, 0.25, 0.15],\n            scale: [0, 1.3, 1],\n            rotate: [0, 180, 360],\n            x: [-15, 15, -15]\n          }}\n          transition={{\n            duration: 10,\n            delay: icon.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {icon.emoji}\n        </motion.div>\n      ))}\n      \n      {/* Large floating question mark */}\n      <motion.div\n        className=\"absolute top-1/5 right-1/5 text-6xl opacity-8\"\n        animate={{\n          rotate: [0, 15, -15, 0],\n          scale: [1, 1.2, 1],\n          opacity: [0.08, 0.15, 0.08]\n        }}\n        transition={{\n          duration: 12,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        ❓\n      </motion.div>\n      \n      {/* Floating lightbulb for ideas */}\n      <motion.div\n        className=\"absolute bottom-1/4 left-1/5 text-4xl opacity-12\"\n        animate={{\n          y: [-25, 25, -25],\n          rotate: [-8, 8, -8],\n          scale: [1, 1.15, 1]\n        }}\n        transition={{\n          duration: 7,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        💡\n      </motion.div>\n\n      {/* Floating search icon */}\n      <motion.div\n        className=\"absolute top-1/3 left-1/8 text-3xl opacity-12\"\n        animate={{\n          x: [-20, 20, -20],\n          y: [-10, 10, -10],\n          rotate: [0, 360],\n          scale: [0.9, 1.1, 0.9]\n        }}\n        transition={{\n          duration: 9,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        🔍\n      </motion.div>\n\n      {/* Floating checkmark for solutions */}\n      <motion.div\n        className=\"absolute bottom-1/3 right-1/8 text-3xl opacity-15\"\n        animate={{\n          scale: [1, 1.3, 1],\n          rotate: [0, 10, -10, 0],\n          opacity: [0.15, 0.25, 0.15]\n        }}\n        transition={{\n          duration: 5,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        ✅\n      </motion.div>\n    </div>\n  )\n}\n\nexport { FAQFloatingCandies }\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,qBAA+B;IACnC,MAAM,cAAc;QAClB;YAAE,OAAO;YAAK,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC3C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAK,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC7C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAM,GAAG;QAAM;QAC7C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;KAC/C;IAED,MAAM,YAAY;QAChB;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAK,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;KAC5C;IAED,qBACE,4SAAC;QAAI,WAAU;;YAEZ,YAAY,GAAG,CAAC,CAAC,SAAS,sBACzB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,QAAQ,CAAC;wBACf,KAAK,QAAQ,CAAC;oBAChB;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACV;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAM;4BAAK;yBAAK;wBAC7B,OAAO;4BAAC;4BAAG;4BAAK;4BAAK;yBAAE;wBACvB,QAAQ;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;wBACvB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,QAAQ,KAAK;wBACpB,QAAQ;wBACR,MAAM;oBACR;8BAEC,QAAQ,KAAK;mBAxBT,CAAC,IAAI,EAAE,OAAO;;;;;YA6BtB,UAAU,GAAG,CAAC,CAAC,MAAM,sBACpB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,KAAK,CAAC;wBACZ,KAAK,KAAK,CAAC;oBACb;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAM;yBAAK;wBACxB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;4BAAK;yBAAI;wBACrB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,KAAK,KAAK;wBACjB,QAAQ;wBACR,MAAM;oBACR;8BAEC,KAAK,KAAK;mBAvBN,CAAC,KAAK,EAAE,OAAO;;;;;0BA4BxB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,QAAQ;wBAAC;wBAAG;wBAAI,CAAC;wBAAI;qBAAE;oBACvB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAM;wBAAM;qBAAK;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,QAAQ;wBAAC,CAAC;wBAAG;wBAAG,CAAC;qBAAE;oBACnB,OAAO;wBAAC;wBAAG;wBAAM;qBAAE;gBACrB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,QAAQ;wBAAC;wBAAG;qBAAI;oBAChB,OAAO;wBAAC;wBAAK;wBAAK;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,QAAQ;wBAAC;wBAAG;wBAAI,CAAC;wBAAI;qBAAE;oBACvB,SAAS;wBAAC;wBAAM;wBAAM;qBAAK;gBAC7B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;;;;;;;AAKP;KAxJM", "debugId": null}}, {"offset": {"line": 3729, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/TestimonialFloatingCandies.tsx"], "sourcesContent": ["'use client'\n\nimport React from 'react'\nimport { motion } from 'framer-motion'\n\nconst TestimonialFloatingCandies: React.FC = () => {\n  const testimonialElements = [\n    { emoji: '⭐', delay: 0, x: '15%', y: '25%' },\n    { emoji: '💬', delay: 0.8, x: '85%', y: '20%' },\n    { emoji: '👥', delay: 1.6, x: '10%', y: '65%' },\n    { emoji: '❤️', delay: 2.4, x: '90%', y: '70%' },\n    { emoji: '🏆', delay: 3.2, x: '50%', y: '15%' },\n    { emoji: '✨', delay: 4, x: '75%', y: '80%' },\n    { emoji: '🎉', delay: 4.8, x: '25%', y: '85%' },\n    { emoji: '👍', delay: 5.6, x: '80%', y: '40%' }\n  ]\n\n  const floatingCandies = [\n    { emoji: '🍓', delay: 1, x: '20%', y: '30%' },\n    { emoji: '🫐', delay: 2, x: '70%', y: '25%' },\n    { emoji: '🍏', delay: 3, x: '30%', y: '75%' },\n    { emoji: '🍭', delay: 4, x: '85%', y: '60%' }\n  ]\n\n  return (\n    <div className=\"absolute inset-0 pointer-events-none overflow-hidden\">\n      {/* Testimonial-related icons */}\n      {testimonialElements.map((element, index) => (\n        <motion.div\n          key={`testimonial-${index}`}\n          className=\"absolute text-2xl opacity-20\"\n          style={{\n            left: element.x,\n            top: element.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0,\n            rotate: 0\n          }}\n          animate={{ \n            opacity: [0, 0.4, 0.1, 0.4],\n            scale: [0, 1.3, 0.7, 1],\n            rotate: [0, 15, -15, 0],\n            y: [-15, 15, -15]\n          }}\n          transition={{\n            duration: 7,\n            delay: element.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {element.emoji}\n        </motion.div>\n      ))}\n\n      {/* Floating candy flavors */}\n      {floatingCandies.map((candy, index) => (\n        <motion.div\n          key={`candy-${index}`}\n          className=\"absolute text-3xl opacity-15\"\n          style={{\n            left: candy.x,\n            top: candy.y\n          }}\n          initial={{ \n            opacity: 0, \n            scale: 0\n          }}\n          animate={{ \n            opacity: [0, 0.3, 0.15],\n            scale: [0, 1.2, 1],\n            rotate: [0, 360],\n            x: [-20, 20, -20],\n            y: [-10, 10, -10]\n          }}\n          transition={{\n            duration: 12,\n            delay: candy.delay,\n            repeat: Infinity,\n            ease: 'easeInOut'\n          }}\n        >\n          {candy.emoji}\n        </motion.div>\n      ))}\n      \n      {/* Large floating review stars */}\n      <motion.div\n        className=\"absolute top-1/6 right-1/6 text-5xl opacity-10\"\n        animate={{\n          rotate: [0, 360],\n          scale: [1, 1.3, 1],\n          opacity: [0.1, 0.2, 0.1]\n        }}\n        transition={{\n          duration: 15,\n          repeat: Infinity,\n          ease: 'linear'\n        }}\n      >\n        ⭐\n      </motion.div>\n      \n      <motion.div\n        className=\"absolute bottom-1/5 left-1/6 text-4xl opacity-12\"\n        animate={{\n          y: [-20, 20, -20],\n          rotate: [-10, 10, -10],\n          scale: [1, 1.1, 1]\n        }}\n        transition={{\n          duration: 8,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        💝\n      </motion.div>\n\n      {/* Floating hearts for love */}\n      <motion.div\n        className=\"absolute top-1/2 right-1/8 text-2xl opacity-15\"\n        animate={{\n          y: [-30, 0, -30],\n          x: [-10, 10, -10],\n          scale: [0.8, 1.2, 0.8]\n        }}\n        transition={{\n          duration: 6,\n          repeat: Infinity,\n          ease: 'easeInOut'\n        }}\n      >\n        💕\n      </motion.div>\n    </div>\n  )\n}\n\nexport { TestimonialFloatingCandies }\n"], "names": [], "mappings": ";;;;AAGA;AAHA;;;AAKA,MAAM,6BAAuC;IAC3C,MAAM,sBAAsB;QAC1B;YAAE,OAAO;YAAK,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC3C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAK,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC3C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;QAC9C;YAAE,OAAO;YAAM,OAAO;YAAK,GAAG;YAAO,GAAG;QAAM;KAC/C;IAED,MAAM,kBAAkB;QACtB;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;QAC5C;YAAE,OAAO;YAAM,OAAO;YAAG,GAAG;YAAO,GAAG;QAAM;KAC7C;IAED,qBACE,4SAAC;QAAI,WAAU;;YAEZ,oBAAoB,GAAG,CAAC,CAAC,SAAS,sBACjC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,QAAQ,CAAC;wBACf,KAAK,QAAQ,CAAC;oBAChB;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;wBACP,QAAQ;oBACV;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAK;4BAAK;yBAAI;wBAC3B,OAAO;4BAAC;4BAAG;4BAAK;4BAAK;yBAAE;wBACvB,QAAQ;4BAAC;4BAAG;4BAAI,CAAC;4BAAI;yBAAE;wBACvB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,QAAQ,KAAK;wBACpB,QAAQ;wBACR,MAAM;oBACR;8BAEC,QAAQ,KAAK;mBAxBT,CAAC,YAAY,EAAE,OAAO;;;;;YA6B9B,gBAAgB,GAAG,CAAC,CAAC,OAAO,sBAC3B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBAET,WAAU;oBACV,OAAO;wBACL,MAAM,MAAM,CAAC;wBACb,KAAK,MAAM,CAAC;oBACd;oBACA,SAAS;wBACP,SAAS;wBACT,OAAO;oBACT;oBACA,SAAS;wBACP,SAAS;4BAAC;4BAAG;4BAAK;yBAAK;wBACvB,OAAO;4BAAC;4BAAG;4BAAK;yBAAE;wBAClB,QAAQ;4BAAC;4BAAG;yBAAI;wBAChB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;wBACjB,GAAG;4BAAC,CAAC;4BAAI;4BAAI,CAAC;yBAAG;oBACnB;oBACA,YAAY;wBACV,UAAU;wBACV,OAAO,MAAM,KAAK;wBAClB,QAAQ;wBACR,MAAM;oBACR;8BAEC,MAAM,KAAK;mBAxBP,CAAC,MAAM,EAAE,OAAO;;;;;0BA6BzB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,QAAQ;wBAAC;wBAAG;qBAAI;oBAChB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;oBAClB,SAAS;wBAAC;wBAAK;wBAAK;qBAAI;gBAC1B;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,QAAQ;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACtB,OAAO;wBAAC;wBAAG;wBAAK;qBAAE;gBACpB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;0BAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,SAAS;oBACP,GAAG;wBAAC,CAAC;wBAAI;wBAAG,CAAC;qBAAG;oBAChB,GAAG;wBAAC,CAAC;wBAAI;wBAAI,CAAC;qBAAG;oBACjB,OAAO;wBAAC;wBAAK;wBAAK;qBAAI;gBACxB;gBACA,YAAY;oBACV,UAAU;oBACV,QAAQ;oBACR,MAAM;gBACR;0BACD;;;;;;;;;;;;AAKP;KAtIM", "debugId": null}}, {"offset": {"line": 4022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/OrderSuccessAnimation.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useEffect, useState } from 'react'\nimport { motion, AnimatePresence } from 'framer-motion'\n\ninterface OrderSuccessAnimationProps {\n  isVisible: boolean\n  onComplete?: () => void\n}\n\nconst OrderSuccessAnimation: React.FC<OrderSuccessAnimationProps> = ({\n  isVisible,\n  onComplete\n}) => {\n  const [showConfetti, setShow<PERSON>onfetti] = useState(false)\n  const [showCheckmark, setShowCheckmark] = useState(false)\n\n  useEffect(() => {\n    if (isVisible) {\n      // Séquence d'animation\n      setTimeout(() => setShowCheckmark(true), 300)\n      setTimeout(() => setShowConfetti(true), 600)\n      setTimeout(() => {\n        setShowConfetti(false)\n        onComplete?.()\n      }, 3000)\n    } else {\n      setShowCheckmark(false)\n      setShowConfetti(false)\n    }\n  }, [isVisible, onComplete])\n\n  const confettiPieces = Array.from({ length: 50 }, (_, i) => ({\n    id: i,\n    emoji: ['🎉', '🎊', '✨', '🌟', '💫', '🎈'][Math.floor(Math.random() * 6)],\n    delay: Math.random() * 2,\n    x: Math.random() * 100,\n    duration: 2 + Math.random() * 2\n  }))\n\n  const candyPieces = Array.from({ length: 20 }, (_, i) => ({\n    id: i,\n    emoji: ['🍓', '🫐', '🍏', '🍭', '🍬'][Math.floor(Math.random() * 5)],\n    delay: Math.random() * 1.5,\n    x: Math.random() * 100,\n    duration: 3 + Math.random() * 2\n  }))\n\n  return (\n    <AnimatePresence>\n      {isVisible && (\n        <motion.div\n          className=\"fixed inset-0 z-50 flex items-center justify-center bg-black/20 backdrop-blur-sm\"\n          initial={{ opacity: 0 }}\n          animate={{ opacity: 1 }}\n          exit={{ opacity: 0 }}\n          transition={{ duration: 0.3 }}\n        >\n          {/* Success Card */}\n          <motion.div\n            className=\"bg-white rounded-2xl p-8 shadow-2xl max-w-md mx-4 text-center relative overflow-hidden\"\n            initial={{ scale: 0, rotate: -10 }}\n            animate={{ scale: 1, rotate: 0 }}\n            exit={{ scale: 0, rotate: 10 }}\n            transition={{ \n              type: 'spring', \n              stiffness: 300, \n              damping: 20,\n              duration: 0.6 \n            }}\n          >\n            {/* Background gradient */}\n            <div className=\"absolute inset-0 bg-gradient-to-br from-green-50 to-emerald-50 opacity-50\" />\n            \n            {/* Checkmark Animation */}\n            <div className=\"relative z-10\">\n              <AnimatePresence>\n                {showCheckmark && (\n                  <motion.div\n                    className=\"w-20 h-20 mx-auto mb-6 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full flex items-center justify-center\"\n                    initial={{ scale: 0, rotate: -180 }}\n                    animate={{ scale: 1, rotate: 0 }}\n                    transition={{ \n                      type: 'spring', \n                      stiffness: 400, \n                      damping: 15 \n                    }}\n                  >\n                    <motion.svg\n                      className=\"w-10 h-10 text-white\"\n                      fill=\"none\"\n                      stroke=\"currentColor\"\n                      viewBox=\"0 0 24 24\"\n                      initial={{ pathLength: 0 }}\n                      animate={{ pathLength: 1 }}\n                      transition={{ duration: 0.8, delay: 0.2 }}\n                    >\n                      <motion.path\n                        strokeLinecap=\"round\"\n                        strokeLinejoin=\"round\"\n                        strokeWidth={3}\n                        d=\"M5 13l4 4L19 7\"\n                      />\n                    </motion.svg>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n\n              {/* Success Message */}\n              <motion.h2\n                className=\"text-2xl font-bold text-gray-800 mb-4\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n              >\n                Commande confirmée !\n              </motion.h2>\n\n              <motion.p\n                className=\"text-gray-600 mb-6\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.6 }}\n              >\n                Merci pour votre achat ! Vous allez recevoir un email de confirmation avec les détails de votre commande.\n              </motion.p>\n\n              {/* Order Details Preview */}\n              <motion.div\n                className=\"bg-gray-50 rounded-lg p-4 mb-6\"\n                initial={{ opacity: 0, scale: 0.9 }}\n                animate={{ opacity: 1, scale: 1 }}\n                transition={{ delay: 0.8 }}\n              >\n                <div className=\"flex items-center justify-center space-x-2 text-sm text-gray-600\">\n                  <span>📦</span>\n                  <span>Livraison estimée : 2-3 jours ouvrés</span>\n                </div>\n              </motion.div>\n\n              {/* Floating Success Elements */}\n              <div className=\"absolute inset-0 pointer-events-none\">\n                <motion.div\n                  className=\"absolute top-4 right-4 text-2xl\"\n                  animate={{\n                    rotate: [0, 360],\n                    scale: [1, 1.2, 1]\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: 'easeInOut'\n                  }}\n                >\n                  ✨\n                </motion.div>\n\n                <motion.div\n                  className=\"absolute top-4 left-4 text-2xl\"\n                  animate={{\n                    y: [-5, 5, -5],\n                    rotate: [-10, 10, -10]\n                  }}\n                  transition={{\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: 'easeInOut'\n                  }}\n                >\n                  🎉\n                </motion.div>\n\n                <motion.div\n                  className=\"absolute bottom-4 right-4 text-xl\"\n                  animate={{\n                    scale: [1, 1.3, 1],\n                    opacity: [0.7, 1, 0.7]\n                  }}\n                  transition={{\n                    duration: 2.5,\n                    repeat: Infinity,\n                    ease: 'easeInOut'\n                  }}\n                >\n                  💝\n                </motion.div>\n              </div>\n            </div>\n          </motion.div>\n\n          {/* Confetti Animation */}\n          <AnimatePresence>\n            {showConfetti && (\n              <div className=\"absolute inset-0 pointer-events-none\">\n                {confettiPieces.map((piece) => (\n                  <motion.div\n                    key={`confetti-${piece.id}`}\n                    className=\"absolute text-2xl\"\n                    style={{ left: `${piece.x}%`, top: '-10%' }}\n                    initial={{ \n                      y: -100, \n                      opacity: 0,\n                      rotate: 0,\n                      scale: 0\n                    }}\n                    animate={{ \n                      y: window.innerHeight + 100,\n                      opacity: [0, 1, 1, 0],\n                      rotate: [0, 360, 720],\n                      scale: [0, 1, 1, 0]\n                    }}\n                    transition={{\n                      duration: piece.duration,\n                      delay: piece.delay,\n                      ease: 'easeOut'\n                    }}\n                  >\n                    {piece.emoji}\n                  </motion.div>\n                ))}\n\n                {candyPieces.map((candy) => (\n                  <motion.div\n                    key={`candy-${candy.id}`}\n                    className=\"absolute text-3xl\"\n                    style={{ left: `${candy.x}%`, top: '-10%' }}\n                    initial={{ \n                      y: -100, \n                      opacity: 0,\n                      rotate: 0,\n                      scale: 0\n                    }}\n                    animate={{ \n                      y: window.innerHeight + 100,\n                      opacity: [0, 1, 1, 0],\n                      rotate: [0, 180, 360],\n                      scale: [0, 1.2, 1, 0],\n                      x: [0, Math.random() * 100 - 50, 0]\n                    }}\n                    transition={{\n                      duration: candy.duration,\n                      delay: candy.delay,\n                      ease: 'easeOut'\n                    }}\n                  >\n                    {candy.emoji}\n                  </motion.div>\n                ))}\n              </div>\n            )}\n          </AnimatePresence>\n        </motion.div>\n      )}\n    </AnimatePresence>\n  )\n}\n\nexport { OrderSuccessAnimation }\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;;;AAHA;;;AAUA,MAAM,wBAA8D,CAAC,EACnE,SAAS,EACT,UAAU,EACX;;IACC,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEnD,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;2CAAE;YACR,IAAI,WAAW;gBACb,uBAAuB;gBACvB;uDAAW,IAAM,iBAAiB;sDAAO;gBACzC;uDAAW,IAAM,gBAAgB;sDAAO;gBACxC;uDAAW;wBACT,gBAAgB;wBAChB;oBACF;sDAAG;YACL,OAAO;gBACL,iBAAiB;gBACjB,gBAAgB;YAClB;QACF;0CAAG;QAAC;QAAW;KAAW;IAE1B,MAAM,iBAAiB,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;YAC3D,IAAI;YACJ,OAAO;gBAAC;gBAAM;gBAAM;gBAAK;gBAAM;gBAAM;aAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;YACzE,OAAO,KAAK,MAAM,KAAK;YACvB,GAAG,KAAK,MAAM,KAAK;YACnB,UAAU,IAAI,KAAK,MAAM,KAAK;QAChC,CAAC;IAED,MAAM,cAAc,MAAM,IAAI,CAAC;QAAE,QAAQ;IAAG,GAAG,CAAC,GAAG,IAAM,CAAC;YACxD,IAAI;YACJ,OAAO;gBAAC;gBAAM;gBAAM;gBAAM;gBAAM;aAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,GAAG;YACpE,OAAO,KAAK,MAAM,KAAK;YACvB,GAAG,KAAK,MAAM,KAAK;YACnB,UAAU,IAAI,KAAK,MAAM,KAAK;QAChC,CAAC;IAED,qBACE,4SAAC,kSAAA,CAAA,kBAAe;kBACb,2BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;YACT,WAAU;YACV,SAAS;gBAAE,SAAS;YAAE;YACtB,SAAS;gBAAE,SAAS;YAAE;YACtB,MAAM;gBAAE,SAAS;YAAE;YACnB,YAAY;gBAAE,UAAU;YAAI;;8BAG5B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,OAAO;wBAAG,QAAQ,CAAC;oBAAG;oBACjC,SAAS;wBAAE,OAAO;wBAAG,QAAQ;oBAAE;oBAC/B,MAAM;wBAAE,OAAO;wBAAG,QAAQ;oBAAG;oBAC7B,YAAY;wBACV,MAAM;wBACN,WAAW;wBACX,SAAS;wBACT,UAAU;oBACZ;;sCAGA,4SAAC;4BAAI,WAAU;;;;;;sCAGf,4SAAC;4BAAI,WAAU;;8CACb,4SAAC,kSAAA,CAAA,kBAAe;8CACb,+BACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,OAAO;4CAAG,QAAQ,CAAC;wCAAI;wCAClC,SAAS;4CAAE,OAAO;4CAAG,QAAQ;wCAAE;wCAC/B,YAAY;4CACV,MAAM;4CACN,WAAW;4CACX,SAAS;wCACX;kDAEA,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,MAAK;4CACL,QAAO;4CACP,SAAQ;4CACR,SAAS;gDAAE,YAAY;4CAAE;4CACzB,SAAS;gDAAE,YAAY;4CAAE;4CACzB,YAAY;gDAAE,UAAU;gDAAK,OAAO;4CAAI;sDAExC,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;gDACV,eAAc;gDACd,gBAAe;gDACf,aAAa;gDACb,GAAE;;;;;;;;;;;;;;;;;;;;;8CAQZ,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;oCACR,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAC1B;;;;;;8CAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;oCACP,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,YAAY;wCAAE,OAAO;oCAAI;8CAC1B;;;;;;8CAKD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCACT,WAAU;oCACV,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAI;oCAClC,SAAS;wCAAE,SAAS;wCAAG,OAAO;oCAAE;oCAChC,YAAY;wCAAE,OAAO;oCAAI;8CAEzB,cAAA,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;0DAAK;;;;;;0DACN,4SAAC;0DAAK;;;;;;;;;;;;;;;;;8CAKV,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,QAAQ;oDAAC;oDAAG;iDAAI;gDAChB,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;4CACpB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;sDACD;;;;;;sDAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,GAAG;oDAAC,CAAC;oDAAG;oDAAG,CAAC;iDAAE;gDACd,QAAQ;oDAAC,CAAC;oDAAI;oDAAI,CAAC;iDAAG;4CACxB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;sDACD;;;;;;sDAID,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDACP,OAAO;oDAAC;oDAAG;oDAAK;iDAAE;gDAClB,SAAS;oDAAC;oDAAK;oDAAG;iDAAI;4CACxB;4CACA,YAAY;gDACV,UAAU;gDACV,QAAQ;gDACR,MAAM;4CACR;sDACD;;;;;;;;;;;;;;;;;;;;;;;;8BAQP,4SAAC,kSAAA,CAAA,kBAAe;8BACb,8BACC,4SAAC;wBAAI,WAAU;;4BACZ,eAAe,GAAG,CAAC,CAAC,sBACnB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;wCAAE,KAAK;oCAAO;oCAC1C,SAAS;wCACP,GAAG,CAAC;wCACJ,SAAS;wCACT,QAAQ;wCACR,OAAO;oCACT;oCACA,SAAS;wCACP,GAAG,OAAO,WAAW,GAAG;wCACxB,SAAS;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE;wCACrB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;wCACrB,OAAO;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE;oCACrB;oCACA,YAAY;wCACV,UAAU,MAAM,QAAQ;wCACxB,OAAO,MAAM,KAAK;wCAClB,MAAM;oCACR;8CAEC,MAAM,KAAK;mCArBP,CAAC,SAAS,EAAE,MAAM,EAAE,EAAE;;;;;4BAyB9B,YAAY,GAAG,CAAC,CAAC,sBAChB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCAET,WAAU;oCACV,OAAO;wCAAE,MAAM,GAAG,MAAM,CAAC,CAAC,CAAC,CAAC;wCAAE,KAAK;oCAAO;oCAC1C,SAAS;wCACP,GAAG,CAAC;wCACJ,SAAS;wCACT,QAAQ;wCACR,OAAO;oCACT;oCACA,SAAS;wCACP,GAAG,OAAO,WAAW,GAAG;wCACxB,SAAS;4CAAC;4CAAG;4CAAG;4CAAG;yCAAE;wCACrB,QAAQ;4CAAC;4CAAG;4CAAK;yCAAI;wCACrB,OAAO;4CAAC;4CAAG;4CAAK;4CAAG;yCAAE;wCACrB,GAAG;4CAAC;4CAAG,KAAK,MAAM,KAAK,MAAM;4CAAI;yCAAE;oCACrC;oCACA,YAAY;wCACV,UAAU,MAAM,QAAQ;wCACxB,OAAO,MAAM,KAAK;wCAClB,MAAM;oCACR;8CAEC,MAAM,KAAK;mCAtBP,CAAC,MAAM,EAAE,MAAM,EAAE,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;AAgC5C;GArPM;KAAA", "debugId": null}}, {"offset": {"line": 4503, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/animations/index.ts"], "sourcesContent": ["// Export des composants d'animation\nexport {\n  Cart<PERSON>on<PERSON>tti,\n  CelebrationCon<PERSON>tti,\n  ConfettiAnimation,\n  OrderConfetti,\n} from \"./ConfettiAnimation\";\nexport type { ConfettiAnimationProps } from \"./ConfettiAnimation\";\n\nexport {\n  CheckoutFloatingCandies,\n  FloatingCandy,\n  FloatingCandyField,\n  HeroFloatingCandies,\n  ProductFloatingCandies,\n  useCandyAnimation,\n} from \"./FloatingCandy\";\nexport type {\n  FloatingCandyFieldProps,\n  FloatingCandyProps,\n} from \"./FloatingCandy\";\n\nexport {\n  ParallaxBackground,\n  ParallaxImage,\n  ParallaxSection,\n  ParallaxText,\n  useParallax,\n} from \"./ParallaxSection\";\nexport type {\n  ParallaxBackgroundProps,\n  ParallaxImageProps,\n  ParallaxSectionProps,\n  ParallaxTextProps,\n} from \"./ParallaxSection\";\n\n// Section-specific floating animations\nexport { ContactFloatingCandies } from \"./ContactFloatingCandies\";\nexport { FAQFloatingCandies } from \"./FAQFloatingCandies\";\nexport { TestimonialFloatingCandies } from \"./TestimonialFloatingCandies\";\n\n// Special animations\nexport { OrderSuccessAnimation } from \"./OrderSuccessAnimation\";\n"], "names": [], "mappings": "AAAA,oCAAoC;;AACpC;AAQA;AAaA;AAcA,uCAAuC;AACvC;AACA;AACA;AAEA,qBAAqB;AACrB", "debugId": null}}, {"offset": {"line": 4545, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/CheckoutSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { CheckoutFloatingCandies } from \"@/components/animations\";\nimport { CheckoutForm } from \"@/components/checkout/CheckoutForm\";\nimport {\n  CheckoutProgress,\n  type CheckoutStep,\n} from \"@/components/checkout/CheckoutProgress\";\nimport { OrderSummary } from \"@/components/checkout/OrderSummary\";\nimport { PaymentMethods } from \"@/components/checkout/PaymentMethods\";\nimport { Card, CardContent, CardHeader } from \"@/components/ui\";\nimport { fadeIn, slideUp } from \"@/lib/animations\";\nimport { useCart, useCustomer } from \"@/stores\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\nconst CheckoutSection: React.FC = () => {\n  const { cart } = useCart();\n  const { customer } = useCustomer();\n  const [currentStep, setCurrentStep] = useState<CheckoutStep>(\"shipping\");\n  const [isProcessing, setIsProcessing] = useState(false);\n\n  // Rediriger si le panier est vide\n  if (cart.items.length === 0) {\n    return (\n      <section\n        id=\"checkout\"\n        className=\"py-20 bg-white relative overflow-hidden\"\n      >\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"text-center\">\n            <motion.div\n              className=\"text-6xl mb-4\"\n              animate={{ rotate: [0, -10, 10, 0] }}\n              transition={{ duration: 2, repeat: Infinity }}\n            >\n              🛒\n            </motion.div>\n            <h2 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              Votre panier est vide\n            </h2>\n            <p className=\"text-gray-600 mb-8\">\n              Ajoutez des produits à votre panier pour procéder au paiement.\n            </p>\n            <button\n              onClick={() => {\n                const productsSection = document.getElementById(\"products\");\n                productsSection?.scrollIntoView({ behavior: \"smooth\" });\n              }}\n              className=\"bg-pink-500 text-white px-6 py-3 rounded-lg hover:bg-pink-600 transition-colors\"\n            >\n              Découvrir nos produits\n            </button>\n          </div>\n        </div>\n      </section>\n    );\n  }\n\n  const handleStepChange = (step: CheckoutStep) => {\n    setCurrentStep(step);\n  };\n\n  const handlePaymentSuccess = () => {\n    setCurrentStep(\"confirmation\");\n  };\n\n  return (\n    <section\n      id=\"checkout\"\n      className=\"py-20 bg-gray-50 relative overflow-hidden\"\n    >\n      {/* Background Elements */}\n      <CheckoutFloatingCandies />\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-12\"\n          initial={fadeIn.initial}\n          whileInView={fadeIn.animate}\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-4xl md:text-5xl font-bold mb-6\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n          >\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\">\n              Finaliser\n            </span>\n            <br />\n            <span className=\"text-gray-800\">Votre Commande</span>\n          </motion.h2>\n\n          <motion.p\n            className=\"text-xl text-gray-600 max-w-2xl mx-auto\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2 }}\n          >\n            Plus qu'une étape pour savourer nos délicieux bonbons artisanaux !\n          </motion.p>\n        </motion.div>\n\n        {/* Checkout Progress */}\n        <motion.div\n          className=\"max-w-4xl mx-auto mb-12\"\n          initial={{ opacity: 0, y: 20 }}\n          animate={{ opacity: 1, y: 0 }}\n          transition={{ delay: 0.3 }}\n        >\n          <CheckoutProgress currentStep={currentStep} />\n        </motion.div>\n\n        {/* Checkout Content */}\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-3 gap-8\">\n            {/* Main Checkout Form */}\n            <div className=\"lg:col-span-2\">\n              <AnimatePresence mode=\"wait\">\n                {currentStep === \"shipping\" && (\n                  <motion.div\n                    key=\"shipping\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <Card>\n                      <CardHeader>\n                        <h3 className=\"text-xl font-semibold text-gray-800\">\n                          Informations de livraison\n                        </h3>\n                      </CardHeader>\n                      <CardContent>\n                        <CheckoutForm\n                          onNext={() => handleStepChange(\"payment\")}\n                          isProcessing={isProcessing}\n                        />\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                )}\n\n                {currentStep === \"payment\" && (\n                  <motion.div\n                    key=\"payment\"\n                    initial={{ opacity: 0, x: -20 }}\n                    animate={{ opacity: 1, x: 0 }}\n                    exit={{ opacity: 0, x: 20 }}\n                    transition={{ duration: 0.3 }}\n                  >\n                    <Card>\n                      <CardHeader>\n                        <h3 className=\"text-xl font-semibold text-gray-800\">\n                          Méthode de paiement\n                        </h3>\n                      </CardHeader>\n                      <CardContent>\n                        <PaymentMethods\n                          onBack={() => handleStepChange(\"shipping\")}\n                          onSuccess={handlePaymentSuccess}\n                          isProcessing={isProcessing}\n                          setIsProcessing={setIsProcessing}\n                        />\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                )}\n\n                {currentStep === \"confirmation\" && (\n                  <motion.div\n                    key=\"confirmation\"\n                    initial={{ opacity: 0, scale: 0.9 }}\n                    animate={{ opacity: 1, scale: 1 }}\n                    exit={{ opacity: 0, scale: 1.1 }}\n                    transition={{ duration: 0.5 }}\n                  >\n                    <Card className=\"text-center\">\n                      <CardContent className=\"py-16\">\n                        <motion.div\n                          className=\"text-6xl mb-6\"\n                          animate={{\n                            scale: [1, 1.2, 1],\n                            rotate: [0, 10, -10, 0],\n                          }}\n                          transition={{ duration: 1, repeat: 2 }}\n                        >\n                          🎉\n                        </motion.div>\n\n                        <h3 className=\"text-3xl font-bold text-gray-800 mb-4\">\n                          Commande confirmée !\n                        </h3>\n\n                        <p className=\"text-gray-600 mb-8 max-w-md mx-auto\">\n                          Merci pour votre commande ! Vous recevrez un email de\n                          confirmation avec les détails de livraison.\n                        </p>\n\n                        <div className=\"space-y-4\">\n                          <button\n                            onClick={() => {\n                              const heroSection =\n                                document.getElementById(\"hero\");\n                              heroSection?.scrollIntoView({\n                                behavior: \"smooth\",\n                              });\n                            }}\n                            className=\"bg-pink-500 text-white px-8 py-3 rounded-lg hover:bg-pink-600 transition-colors mr-4\"\n                          >\n                            Retour à l'accueil\n                          </button>\n\n                          <button\n                            onClick={() => {\n                              const productsSection =\n                                document.getElementById(\"products\");\n                              productsSection?.scrollIntoView({\n                                behavior: \"smooth\",\n                              });\n                            }}\n                            className=\"border border-pink-500 text-pink-500 px-8 py-3 rounded-lg hover:bg-pink-50 transition-colors\"\n                          >\n                            Continuer mes achats\n                          </button>\n                        </div>\n                      </CardContent>\n                    </Card>\n                  </motion.div>\n                )}\n              </AnimatePresence>\n            </div>\n\n            {/* Order Summary Sidebar */}\n            <div className=\"lg:col-span-1\">\n              <motion.div\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ delay: 0.4 }}\n                className=\"sticky top-8\"\n              >\n                <OrderSummary />\n              </motion.div>\n            </div>\n          </div>\n        </div>\n\n        {/* Security & Trust Indicators */}\n        <motion.div\n          className=\"mt-16 max-w-4xl mx-auto\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ delay: 0.6 }}\n        >\n          <div className=\"grid grid-cols-1 md:grid-cols-4 gap-6 text-center\">\n            <div className=\"flex flex-col items-center\">\n              <div className=\"text-3xl mb-2\">🔒</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">\n                Paiement sécurisé\n              </h4>\n              <p className=\"text-sm text-gray-600\">SSL 256-bit</p>\n            </div>\n\n            <div className=\"flex flex-col items-center\">\n              <div className=\"text-3xl mb-2\">🚚</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">\n                Livraison rapide\n              </h4>\n              <p className=\"text-sm text-gray-600\">24-48h</p>\n            </div>\n\n            <div className=\"flex flex-col items-center\">\n              <div className=\"text-3xl mb-2\">↩️</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">\n                Retour gratuit\n              </h4>\n              <p className=\"text-sm text-gray-600\">30 jours</p>\n            </div>\n\n            <div className=\"flex flex-col items-center\">\n              <div className=\"text-3xl mb-2\">🎧</div>\n              <h4 className=\"font-semibold text-gray-800 mb-1\">\n                Support client\n              </h4>\n              <p className=\"text-sm text-gray-600\">7j/7</p>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport { CheckoutSection };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AAIA;AACA;AACA;AAAA;AACA;AACA;AAAA;AAAA;AACA;AAAA;AACA;;;AAdA;;;;;;;;;;;AAgBA,MAAM,kBAA4B;;IAChC,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,4KAAA,CAAA,UAAO,AAAD;IACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAgB;IAC7D,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,kCAAkC;IAClC,IAAI,KAAK,KAAK,CAAC,MAAM,KAAK,GAAG;QAC3B,qBACE,4SAAC;YACC,IAAG;YACH,WAAU;sBAEV,cAAA,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,QAAQ;oCAAC;oCAAG,CAAC;oCAAI;oCAAI;iCAAE;4BAAC;4BACnC,YAAY;gCAAE,UAAU;gCAAG,QAAQ;4BAAS;sCAC7C;;;;;;sCAGD,4SAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,4SAAC;4BAAE,WAAU;sCAAqB;;;;;;sCAGlC,4SAAC;4BACC,SAAS;gCACP,MAAM,kBAAkB,SAAS,cAAc,CAAC;gCAChD,iBAAiB,eAAe;oCAAE,UAAU;gCAAS;4BACvD;4BACA,WAAU;sCACX;;;;;;;;;;;;;;;;;;;;;;IAOX;IAEA,MAAM,mBAAmB,CAAC;QACxB,eAAe;IACjB;IAEA,MAAM,uBAAuB;QAC3B,eAAe;IACjB;IAEA,qBACE,4SAAC;QACC,IAAG;QACH,WAAU;;0BAGV,4SAAC,oJAAA,CAAA,0BAAuB;;;;;0BAExB,4SAAC;gBAAI,WAAU;;kCAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;wBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;wBAC3B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;gCACR,WAAU;gCACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;gCACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;;kDAEvB,4SAAC;wCAAK,WAAU;kDAA6E;;;;;;kDAG7F,4SAAC;;;;;kDACD,4SAAC;wCAAK,WAAU;kDAAgB;;;;;;;;;;;;0CAGlC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gCACP,WAAU;gCACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;gCACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;gCAC5B,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,OAAO;gCAAI;0CAC1B;;;;;;;;;;;;kCAMH,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAC5B,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,4SAAC,qJAAA,CAAA,mBAAgB;4BAAC,aAAa;;;;;;;;;;;kCAIjC,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,kSAAA,CAAA,kBAAe;wCAAC,MAAK;;4CACnB,gBAAgB,4BACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;;sEACH,4SAAC,mIAAA,CAAA,aAAU;sEACT,cAAA,4SAAC;gEAAG,WAAU;0EAAsC;;;;;;;;;;;sEAItD,4SAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,4SAAC,iJAAA,CAAA,eAAY;gEACX,QAAQ,IAAM,iBAAiB;gEAC/B,cAAc;;;;;;;;;;;;;;;;;+CAfhB;;;;;4CAsBP,gBAAgB,2BACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,GAAG,CAAC;gDAAG;gDAC9B,SAAS;oDAAE,SAAS;oDAAG,GAAG;gDAAE;gDAC5B,MAAM;oDAAE,SAAS;oDAAG,GAAG;gDAAG;gDAC1B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;;sEACH,4SAAC,mIAAA,CAAA,aAAU;sEACT,cAAA,4SAAC;gEAAG,WAAU;0EAAsC;;;;;;;;;;;sEAItD,4SAAC,mIAAA,CAAA,cAAW;sEACV,cAAA,4SAAC,mJAAA,CAAA,iBAAc;gEACb,QAAQ,IAAM,iBAAiB;gEAC/B,WAAW;gEACX,cAAc;gEACd,iBAAiB;;;;;;;;;;;;;;;;;+CAjBnB;;;;;4CAwBP,gBAAgB,gCACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDAET,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAClC,SAAS;oDAAE,SAAS;oDAAG,OAAO;gDAAE;gDAChC,MAAM;oDAAE,SAAS;oDAAG,OAAO;gDAAI;gDAC/B,YAAY;oDAAE,UAAU;gDAAI;0DAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;oDAAC,WAAU;8DACd,cAAA,4SAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;;0EACrB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gEACT,WAAU;gEACV,SAAS;oEACP,OAAO;wEAAC;wEAAG;wEAAK;qEAAE;oEAClB,QAAQ;wEAAC;wEAAG;wEAAI,CAAC;wEAAI;qEAAE;gEACzB;gEACA,YAAY;oEAAE,UAAU;oEAAG,QAAQ;gEAAE;0EACtC;;;;;;0EAID,4SAAC;gEAAG,WAAU;0EAAwC;;;;;;0EAItD,4SAAC;gEAAE,WAAU;0EAAsC;;;;;;0EAKnD,4SAAC;gEAAI,WAAU;;kFACb,4SAAC;wEACC,SAAS;4EACP,MAAM,cACJ,SAAS,cAAc,CAAC;4EAC1B,aAAa,eAAe;gFAC1B,UAAU;4EACZ;wEACF;wEACA,WAAU;kFACX;;;;;;kFAID,4SAAC;wEACC,SAAS;4EACP,MAAM,kBACJ,SAAS,cAAc,CAAC;4EAC1B,iBAAiB,eAAe;gFAC9B,UAAU;4EACZ;wEACF;wEACA,WAAU;kFACX;;;;;;;;;;;;;;;;;;;;;;;+CAnDH;;;;;;;;;;;;;;;;8CA+DZ,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAG;wCAC7B,SAAS;4CAAE,SAAS;4CAAG,GAAG;wCAAE;wCAC5B,YAAY;4CAAE,OAAO;wCAAI;wCACzB,WAAU;kDAEV,cAAA,4SAAC,iJAAA,CAAA,eAAY;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOrB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS;4BAAE,SAAS;4BAAG,GAAG;wBAAG;wBAC7B,aAAa;4BAAE,SAAS;4BAAG,GAAG;wBAAE;wBAChC,UAAU;4BAAE,MAAM;wBAAK;wBACvB,YAAY;4BAAE,OAAO;wBAAI;kCAEzB,cAAA,4SAAC;4BAAI,WAAU;;8CACb,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,4SAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,4SAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,4SAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC;4CAAI,WAAU;sDAAgB;;;;;;sDAC/B,4SAAC;4CAAG,WAAU;sDAAmC;;;;;;sDAGjD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOnD;GAxRM;;QACa,4KAAA,CAAA,UAAO;QACH,qIAAA,CAAA,cAAW;;;KAF5B", "debugId": null}}, {"offset": {"line": 5255, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/ContactSection.tsx"], "sourcesContent": ["\"use client\";\n\n// import { ContactFloatingCandies } from \"@/components/animations\";\nimport { <PERSON><PERSON>, <PERSON>, CardContent, Input, Textarea } from \"@/components/ui\";\nimport {\n  fadeIn,\n  slideUp,\n  staggerContainer,\n  staggerItem,\n} from \"@/lib/animations\";\nimport { useNotifications } from \"@/stores\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\nimport { useForm } from \"react-hook-form\";\nimport { z } from \"zod\";\n\nconst contactSchema = z.object({\n  name: z.string().min(2, \"Le nom doit contenir au moins 2 caractères\"),\n  email: z.string().email(\"Email invalide\"),\n  subject: z.string().min(5, \"Le sujet doit contenir au moins 5 caractères\"),\n  message: z\n    .string()\n    .min(10, \"Le message doit contenir au moins 10 caractères\"),\n});\n\ntype ContactFormData = z.infer<typeof contactSchema>;\n\nconst ContactSection: React.FC = () => {\n  const { addNotification } = useNotifications();\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors, isSubmitting },\n    reset,\n  } = useForm<ContactFormData>({\n    resolver: zodResolver(contactSchema),\n  });\n\n  const onSubmit = async (data: ContactFormData) => {\n    try {\n      const response = await fetch(\"/api/contact\", {\n        method: \"POST\",\n        headers: {\n          \"Content-Type\": \"application/json\",\n        },\n        body: JSON.stringify(data),\n      });\n\n      const result = await response.json();\n\n      if (response.ok && result.success) {\n        addNotification({\n          type: \"success\",\n          title: \"Succès\",\n          message:\n            \"Message envoyé avec succès ! Nous vous répondrons sous 24h.\",\n        });\n        reset();\n      } else {\n        throw new Error(result.error || \"Erreur lors de l'envoi\");\n      }\n    } catch (error) {\n      console.error(\"Error sending contact message:\", error);\n      addNotification({\n        type: \"error\",\n        title: \"Erreur\",\n        message: \"Erreur lors de l'envoi du message. Veuillez réessayer.\",\n      });\n    }\n  };\n\n  const contactInfo = [\n    {\n      icon: \"📧\",\n      title: \"Email\",\n      value: \"<EMAIL>\",\n      description: \"Réponse sous 24h\",\n    },\n    {\n      icon: \"📞\",\n      title: \"Téléphone\",\n      value: \"+33 1 23 45 67 89\",\n      description: \"Lun-Ven 9h-18h\",\n    },\n    {\n      icon: \"📍\",\n      title: \"Adresse\",\n      value: \"123 Rue de la Gourmandise\",\n      description: \"75001 Paris, France\",\n    },\n    {\n      icon: \"⏰\",\n      title: \"Horaires\",\n      value: \"Lun-Ven 9h-18h\",\n      description: \"Sam 10h-16h\",\n    },\n  ];\n\n  return (\n    <section\n      id=\"contact\"\n      className=\"py-8 bg-gradient-to-br from-gray-50 to-pink-50 relative overflow-hidden\"\n    >\n      {/* Background Elements - Removed for cleaner design */}\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={fadeIn.initial}\n          whileInView={fadeIn.animate}\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-4xl md:text-5xl font-bold mb-6\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n          >\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\">\n              Contactez\n            </span>\n            <br />\n            <span className=\"text-gray-800\">Notre Équipe</span>\n          </motion.h2>\n\n          <motion.p\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2 }}\n          >\n            Une question, une suggestion, ou simplement envie de nous dire\n            bonjour ? Nous sommes là pour vous écouter !\n          </motion.p>\n        </motion.div>\n\n        <div className=\"max-w-6xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-12\">\n            {/* Contact Form */}\n            <motion.div\n              initial={{ opacity: 0, x: -50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6 }}\n            >\n              <Card className=\"h-full shadow-lg border-0 bg-white/80 backdrop-blur-sm\">\n                <CardContent className=\"p-8\">\n                  <div className=\"text-center mb-8\">\n                    <div className=\"text-4xl mb-4\">💬</div>\n                    <h3 className=\"text-2xl font-bold text-gray-800 mb-2\">\n                      Envoyez-nous un message\n                    </h3>\n                    <p className=\"text-gray-600\">\n                      Nous vous répondrons dans les plus brefs délais\n                    </p>\n                  </div>\n\n                  <form onSubmit={handleSubmit(onSubmit)} className=\"space-y-8\">\n                    <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                      <div className=\"space-y-2\">\n                        <label className=\"block text-sm font-semibold text-gray-700\">\n                          Nom complet *\n                        </label>\n                        <Input\n                          {...register(\"name\")}\n                          placeholder=\"Votre nom complet\"\n                          error={errors.name?.message}\n                          variant=\"filled\"\n                          inputSize=\"lg\"\n                          fullWidth\n                        />\n                      </div>\n\n                      <div className=\"space-y-2\">\n                        <label className=\"block text-sm font-semibold text-gray-700\">\n                          Email *\n                        </label>\n                        <Input\n                          {...register(\"email\")}\n                          type=\"email\"\n                          placeholder=\"<EMAIL>\"\n                          error={errors.email?.message}\n                          variant=\"filled\"\n                          inputSize=\"lg\"\n                          fullWidth\n                        />\n                      </div>\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-semibold text-gray-700\">\n                        Sujet *\n                      </label>\n                      <Input\n                        {...register(\"subject\")}\n                        placeholder=\"Objet de votre message\"\n                        error={errors.subject?.message}\n                        variant=\"filled\"\n                        inputSize=\"lg\"\n                        fullWidth\n                      />\n                    </div>\n\n                    <div className=\"space-y-2\">\n                      <label className=\"block text-sm font-semibold text-gray-700\">\n                        Message *\n                      </label>\n                      <Textarea\n                        {...register(\"message\")}\n                        placeholder=\"Décrivez votre demande en détail...\"\n                        rows={6}\n                        error={errors.message?.message}\n                        variant=\"filled\"\n                        inputSize=\"lg\"\n                        fullWidth\n                      />\n                    </div>\n\n                    <motion.div\n                      whileHover={{ scale: 1.02 }}\n                      whileTap={{ scale: 0.98 }}\n                    >\n                      <Button\n                        type=\"submit\"\n                        variant=\"primary\"\n                        size=\"lg\"\n                        disabled={isSubmitting}\n                        fullWidth\n                        className=\"bg-gradient-to-r from-pink-500 to-orange-500 hover:from-pink-600 hover:to-orange-600 text-white font-semibold py-4 px-8 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300\"\n                      >\n                        {isSubmitting ? (\n                          <span className=\"flex items-center justify-center\">\n                            <motion.span\n                              className=\"mr-3 text-xl\"\n                              animate={{ rotate: 360 }}\n                              transition={{\n                                duration: 1,\n                                repeat: Infinity,\n                                ease: \"linear\",\n                              }}\n                            >\n                              📧\n                            </motion.span>\n                            Envoi en cours...\n                          </span>\n                        ) : (\n                          <span className=\"flex items-center justify-center\">\n                            <span className=\"mr-3 text-xl\">📨</span>\n                            Envoyer le message\n                          </span>\n                        )}\n                      </Button>\n                    </motion.div>\n                  </form>\n                </CardContent>\n              </Card>\n            </motion.div>\n\n            {/* Contact Info */}\n            <motion.div\n              initial={{ opacity: 0, x: 50 }}\n              whileInView={{ opacity: 1, x: 0 }}\n              viewport={{ once: true }}\n              transition={{ duration: 0.6 }}\n              className=\"space-y-8\"\n            >\n              <div>\n                <h3 className=\"text-2xl font-bold text-gray-800 mb-6\">\n                  Nos coordonnées\n                </h3>\n\n                <motion.div\n                  className=\"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-1 gap-6\"\n                  variants={staggerContainer}\n                  initial=\"initial\"\n                  whileInView=\"animate\"\n                  viewport={{ once: true }}\n                >\n                  {contactInfo.map((info, index) => (\n                    <motion.div\n                      key={index}\n                      variants={staggerItem}\n                      className=\"flex items-start space-x-4 p-4 bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300\"\n                    >\n                      <div className=\"text-2xl\">{info.icon}</div>\n                      <div>\n                        <h4 className=\"font-semibold text-gray-800\">\n                          {info.title}\n                        </h4>\n                        <p className=\"text-gray-700 font-medium\">\n                          {info.value}\n                        </p>\n                        <p className=\"text-sm text-gray-500\">\n                          {info.description}\n                        </p>\n                      </div>\n                    </motion.div>\n                  ))}\n                </motion.div>\n              </div>\n\n              {/* Social Media */}\n              <div>\n                <h4 className=\"text-lg font-semibold text-gray-800 mb-4\">\n                  Suivez-nous\n                </h4>\n                <div className=\"flex space-x-4\">\n                  {[\n                    {\n                      icon: \"📘\",\n                      name: \"Facebook\",\n                      color: \"hover:text-blue-600\",\n                    },\n                    {\n                      icon: \"📷\",\n                      name: \"Instagram\",\n                      color: \"hover:text-pink-600\",\n                    },\n                    {\n                      icon: \"🐦\",\n                      name: \"Twitter\",\n                      color: \"hover:text-blue-400\",\n                    },\n                    {\n                      icon: \"📺\",\n                      name: \"YouTube\",\n                      color: \"hover:text-red-600\",\n                    },\n                  ].map((social, index) => (\n                    <motion.button\n                      key={index}\n                      className={`text-2xl text-gray-400 ${social.color} transition-colors duration-300`}\n                      whileHover={{ scale: 1.2 }}\n                      whileTap={{ scale: 0.9 }}\n                      title={social.name}\n                    >\n                      {social.icon}\n                    </motion.button>\n                  ))}\n                </div>\n              </div>\n\n              {/* Quick Response Promise */}\n              <div className=\"bg-gradient-to-r from-pink-50 to-orange-50 rounded-lg p-6\">\n                <div className=\"flex items-center space-x-3 mb-3\">\n                  <span className=\"text-2xl\">⚡</span>\n                  <h4 className=\"font-semibold text-gray-800\">\n                    Réponse rapide garantie\n                  </h4>\n                </div>\n                <p className=\"text-gray-600 text-sm\">\n                  Nous nous engageons à répondre à tous les messages dans les 24\n                  heures. Pour les urgences, n'hésitez pas à nous appeler\n                  directement.\n                </p>\n              </div>\n\n              {/* FAQ Link */}\n              <div className=\"text-center\">\n                <p className=\"text-gray-600 mb-4\">\n                  Avant de nous contacter, consultez notre FAQ\n                </p>\n                <motion.button\n                  onClick={() => {\n                    const faqSection = document.getElementById(\"faq\");\n                    faqSection?.scrollIntoView({ behavior: \"smooth\" });\n                  }}\n                  className=\"text-pink-600 hover:text-pink-700 font-medium underline\"\n                  whileHover={{ scale: 1.05 }}\n                >\n                  Voir les questions fréquentes →\n                </motion.button>\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n};\n\nexport { ContactSection };\n"], "names": [], "mappings": ";;;;AAEA,oEAAoE;AACpE;AAAA;AAAA;AAAA;AACA;AAMA;AAAA;AACA;AACA;AAEA;AACA;;;AAfA;;;;;;;;AAiBA,MAAM,gBAAgB,uNAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,MAAM,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACxB,OAAO,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK,CAAC;IACxB,SAAS,uNAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC3B,SAAS,uNAAA,CAAA,IAAC,CACP,MAAM,GACN,GAAG,CAAC,IAAI;AACb;AAIA,MAAM,iBAA2B;;IAC/B,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,mBAAgB,AAAD;IAE3C,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,EACnC,KAAK,EACN,GAAG,CAAA,GAAA,0PAAA,CAAA,UAAO,AAAD,EAAmB;QAC3B,UAAU,CAAA,GAAA,wQAAA,CAAA,cAAW,AAAD,EAAE;IACxB;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,gBAAgB;gBAC3C,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,MAAM,SAAS,MAAM,SAAS,IAAI;YAElC,IAAI,SAAS,EAAE,IAAI,OAAO,OAAO,EAAE;gBACjC,gBAAgB;oBACd,MAAM;oBACN,OAAO;oBACP,SACE;gBACJ;gBACA;YACF,OAAO;gBACL,MAAM,IAAI,MAAM,OAAO,KAAK,IAAI;YAClC;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,kCAAkC;YAChD,gBAAgB;gBACd,MAAM;gBACN,OAAO;gBACP,SAAS;YACX;QACF;IACF;IAEA,MAAM,cAAc;QAClB;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;QACA;YACE,MAAM;YACN,OAAO;YACP,OAAO;YACP,aAAa;QACf;KACD;IAED,qBACE,4SAAC;QACC,IAAG;QACH,WAAU;kBAIV,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;oBAC3B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4SAAC;oCAAK,WAAU;8CAA6E;;;;;;8CAG7F,4SAAC;;;;;8CACD,4SAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAI;sCAC1B;;;;;;;;;;;;8BAMH,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;;0CAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG,CAAC;gCAAG;gCAC9B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;0CAE5B,cAAA,4SAAC,mIAAA,CAAA,OAAI;oCAAC,WAAU;8CACd,cAAA,4SAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,4SAAC;wDAAG,WAAU;kEAAwC;;;;;;kEAGtD,4SAAC;wDAAE,WAAU;kEAAgB;;;;;;;;;;;;0DAK/B,4SAAC;gDAAK,UAAU,aAAa;gDAAW,WAAU;;kEAChD,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAI,WAAU;;kFACb,4SAAC;wEAAM,WAAU;kFAA4C;;;;;;kFAG7D,4SAAC,oIAAA,CAAA,QAAK;wEACH,GAAG,SAAS,OAAO;wEACpB,aAAY;wEACZ,OAAO,OAAO,IAAI,EAAE;wEACpB,SAAQ;wEACR,WAAU;wEACV,SAAS;;;;;;;;;;;;0EAIb,4SAAC;gEAAI,WAAU;;kFACb,4SAAC;wEAAM,WAAU;kFAA4C;;;;;;kFAG7D,4SAAC,oIAAA,CAAA,QAAK;wEACH,GAAG,SAAS,QAAQ;wEACrB,MAAK;wEACL,aAAY;wEACZ,OAAO,OAAO,KAAK,EAAE;wEACrB,SAAQ;wEACR,WAAU;wEACV,SAAS;;;;;;;;;;;;;;;;;;kEAKf,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAM,WAAU;0EAA4C;;;;;;0EAG7D,4SAAC,oIAAA,CAAA,QAAK;gEACH,GAAG,SAAS,UAAU;gEACvB,aAAY;gEACZ,OAAO,OAAO,OAAO,EAAE;gEACvB,SAAQ;gEACR,WAAU;gEACV,SAAS;;;;;;;;;;;;kEAIb,4SAAC;wDAAI,WAAU;;0EACb,4SAAC;gEAAM,WAAU;0EAA4C;;;;;;0EAG7D,4SAAC,oIAAA,CAAA,WAAQ;gEACN,GAAG,SAAS,UAAU;gEACvB,aAAY;gEACZ,MAAM;gEACN,OAAO,OAAO,OAAO,EAAE;gEACvB,SAAQ;gEACR,WAAU;gEACV,SAAS;;;;;;;;;;;;kEAIb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wDACT,YAAY;4DAAE,OAAO;wDAAK;wDAC1B,UAAU;4DAAE,OAAO;wDAAK;kEAExB,cAAA,4SAAC,qIAAA,CAAA,SAAM;4DACL,MAAK;4DACL,SAAQ;4DACR,MAAK;4DACL,UAAU;4DACV,SAAS;4DACT,WAAU;sEAET,6BACC,4SAAC;gEAAK,WAAU;;kFACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;wEACV,WAAU;wEACV,SAAS;4EAAE,QAAQ;wEAAI;wEACvB,YAAY;4EACV,UAAU;4EACV,QAAQ;4EACR,MAAM;wEACR;kFACD;;;;;;oEAEa;;;;;;qFAIhB,4SAAC;gEAAK,WAAU;;kFACd,4SAAC;wEAAK,WAAU;kFAAe;;;;;;oEAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAYxD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gCACT,SAAS;oCAAE,SAAS;oCAAG,GAAG;gCAAG;gCAC7B,aAAa;oCAAE,SAAS;oCAAG,GAAG;gCAAE;gCAChC,UAAU;oCAAE,MAAM;gCAAK;gCACvB,YAAY;oCAAE,UAAU;gCAAI;gCAC5B,WAAU;;kDAEV,4SAAC;;0DACC,4SAAC;gDAAG,WAAU;0DAAwC;;;;;;0DAItD,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,UAAU,2HAAA,CAAA,mBAAgB;gDAC1B,SAAQ;gDACR,aAAY;gDACZ,UAAU;oDAAE,MAAM;gDAAK;0DAEtB,YAAY,GAAG,CAAC,CAAC,MAAM,sBACtB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wDAET,UAAU,2HAAA,CAAA,cAAW;wDACrB,WAAU;;0EAEV,4SAAC;gEAAI,WAAU;0EAAY,KAAK,IAAI;;;;;;0EACpC,4SAAC;;kFACC,4SAAC;wEAAG,WAAU;kFACX,KAAK,KAAK;;;;;;kFAEb,4SAAC;wEAAE,WAAU;kFACV,KAAK,KAAK;;;;;;kFAEb,4SAAC;wEAAE,WAAU;kFACV,KAAK,WAAW;;;;;;;;;;;;;uDAbhB;;;;;;;;;;;;;;;;kDAsBb,4SAAC;;0DACC,4SAAC;gDAAG,WAAU;0DAA2C;;;;;;0DAGzD,4SAAC;gDAAI,WAAU;0DACZ;oDACC;wDACE,MAAM;wDACN,MAAM;wDACN,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,MAAM;wDACN,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,MAAM;wDACN,OAAO;oDACT;oDACA;wDACE,MAAM;wDACN,MAAM;wDACN,OAAO;oDACT;iDACD,CAAC,GAAG,CAAC,CAAC,QAAQ,sBACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;wDAEZ,WAAW,CAAC,uBAAuB,EAAE,OAAO,KAAK,CAAC,+BAA+B,CAAC;wDAClF,YAAY;4DAAE,OAAO;wDAAI;wDACzB,UAAU;4DAAE,OAAO;wDAAI;wDACvB,OAAO,OAAO,IAAI;kEAEjB,OAAO,IAAI;uDANP;;;;;;;;;;;;;;;;kDAab,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAK,WAAU;kEAAW;;;;;;kEAC3B,4SAAC;wDAAG,WAAU;kEAA8B;;;;;;;;;;;;0DAI9C,4SAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAQvC,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDAAE,WAAU;0DAAqB;;;;;;0DAGlC,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS;oDACP,MAAM,aAAa,SAAS,cAAc,CAAC;oDAC3C,YAAY,eAAe;wDAAE,UAAU;oDAAS;gDAClD;gDACA,WAAU;gDACV,YAAY;oDAAE,OAAO;gDAAK;0DAC3B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAUjB;GAnWM;;QACwB,yIAAA,CAAA,mBAAgB;QAOxC,0PAAA,CAAA,UAAO;;;KARP", "debugId": null}}, {"offset": {"line": 5976, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/FAQSection.tsx"], "sourcesContent": ["\"use client\";\n\n// import { FAQFloatingCandies } from \"@/components/animations\";\nimport { Card, CardContent } from \"@/components/ui\";\nimport {\n  fadeIn,\n  slideUp,\n  staggerContainer,\n  staggerItem,\n} from \"@/lib/animations\";\nimport { AnimatePresence, motion } from \"framer-motion\";\nimport React, { useState } from \"react\";\n\ninterface FAQItem {\n  id: string;\n  question: string;\n  answer: string;\n  category: \"general\" | \"shipping\" | \"products\" | \"payment\";\n  icon: string;\n}\n\nconst FAQSection: React.FC = () => {\n  const [openFAQ, setOpenFAQ] = useState<string | null>(\"1\");\n  const [selectedCategory, setSelectedCategory] = useState<string>(\"all\");\n\n  const faqItems: FAQItem[] = [\n    {\n      id: \"1\",\n      question: \"Les produits Deltagum sont-ils légaux en France ?\",\n      answer:\n        \"Oui, les produits Deltagum sont parfaitement légaux en France. Ils contiennent moins de 0,2% de THC conformément à la réglementation européenne. Nos produits sont testés en laboratoire et certifiés pour garantir leur conformité.\",\n      category: \"general\",\n      icon: \"⚖️\",\n    },\n    {\n      id: \"2\",\n      question: \"À partir de quel âge peut-on consommer Deltagum ?\",\n      answer:\n        \"Nos délices Deltagum sont strictement réservés aux adultes de 18 ans et plus. Une vérification d'âge est obligatoire lors de l'achat. Nous déconseillons la consommation aux femmes enceintes ou allaitantes.\",\n      category: \"general\",\n      icon: \"🔞\",\n    },\n    {\n      id: \"3\",\n      question: \"Combien de produits Deltagum puis-je consommer par jour ?\",\n      answer:\n        \"Nous recommandons de commencer par 1 de nos produits par jour et d'ajuster selon vos besoins. Ne pas dépasser 3 par jour. Attendez 2h entre chaque prise pour évaluer les effets. Consultez un professionnel de santé si vous prenez des médicaments.\",\n      category: \"products\",\n      icon: \"💊\",\n    },\n    {\n      id: \"4\",\n      question: \"Combien de temps prend la livraison ?\",\n      answer:\n        \"Livraison standard gratuite en 3-5 jours ouvrés, express (4,99€) en 24-48h. Commandes avant 14h expédiées le jour même. Emballage discret et sécurisé. Suivi par email avec numéro de tracking.\",\n      category: \"shipping\",\n      icon: \"🚚\",\n    },\n  ];\n\n  const categories = [\n    { id: \"all\", name: \"Toutes\", icon: \"📋\" },\n    { id: \"general\", name: \"Général\", icon: \"❓\" },\n    { id: \"products\", name: \"Produits\", icon: \"🌿\" },\n    { id: \"shipping\", name: \"Livraison\", icon: \"📦\" },\n    { id: \"payment\", name: \"Paiement\", icon: \"💰\" },\n  ];\n\n  const filteredFAQs =\n    selectedCategory === \"all\"\n      ? faqItems\n      : faqItems.filter((item) => item.category === selectedCategory);\n\n  const toggleFAQ = (id: string) => {\n    setOpenFAQ(openFAQ === id ? null : id);\n  };\n\n  return (\n    <section id=\"faq\" className=\"py-8 bg-white relative overflow-hidden\">\n      {/* Background Elements - Removed for cleaner design */}\n\n      <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-8\"\n          initial={fadeIn.initial}\n          whileInView={fadeIn.animate}\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-4xl md:text-5xl font-bold mb-6\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n          >\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\">\n              Questions\n            </span>\n            <br />\n            <span className=\"text-gray-800\">Fréquentes</span>\n          </motion.h2>\n\n          <motion.p\n            className=\"text-xl text-gray-600 max-w-3xl mx-auto\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2 }}\n          >\n            Trouvez rapidement les réponses à vos questions sur nos produits, la\n            livraison, et nos services.\n          </motion.p>\n        </motion.div>\n\n        {/* Category Filter */}\n        <motion.div\n          className=\"flex flex-wrap justify-center gap-3 mb-8\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ delay: 0.3 }}\n        >\n          {categories.map((category) => (\n            <motion.button\n              key={category.id}\n              onClick={() => setSelectedCategory(category.id)}\n              className={`\n                px-6 py-3 rounded-full font-medium transition-all duration-300\n                ${\n                  selectedCategory === category.id\n                    ? \"bg-gradient-to-r from-pink-500 to-orange-500 text-white shadow-lg\"\n                    : \"bg-gray-100 text-gray-600 hover:bg-gray-200\"\n                }\n              `}\n              whileHover={{ scale: 1.05 }}\n              whileTap={{ scale: 0.95 }}\n            >\n              <span className=\"mr-2\">{category.icon}</span>\n              {category.name}\n            </motion.button>\n          ))}\n        </motion.div>\n\n        {/* FAQ Items */}\n        <div className=\"max-w-4xl mx-auto\">\n          <motion.div\n            className=\"space-y-4\"\n            variants={staggerContainer}\n            initial=\"initial\"\n            whileInView=\"animate\"\n            viewport={{ once: true }}\n          >\n            <AnimatePresence>\n              {filteredFAQs.map((faq) => (\n                <motion.div\n                  key={faq.id}\n                  variants={staggerItem}\n                  initial={{ opacity: 0, y: 20 }}\n                  animate={{ opacity: 1, y: 0 }}\n                  exit={{ opacity: 0, y: -20 }}\n                  transition={{ duration: 0.3 }}\n                  layout\n                >\n                  <Card className=\"overflow-hidden hover:shadow-lg transition-shadow duration-300\">\n                    <motion.button\n                      onClick={() => toggleFAQ(faq.id)}\n                      className=\"w-full p-6 text-left focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-inset\"\n                      whileHover={{\n                        backgroundColor: \"rgba(249, 250, 251, 0.5)\",\n                      }}\n                    >\n                      <div className=\"flex items-center justify-between\">\n                        <div className=\"flex items-center space-x-4\">\n                          <span className=\"text-2xl\">{faq.icon}</span>\n                          <h3 className=\"text-lg font-semibold text-gray-800 pr-4\">\n                            {faq.question}\n                          </h3>\n                        </div>\n\n                        <motion.div\n                          animate={{ rotate: openFAQ === faq.id ? 180 : 0 }}\n                          transition={{ duration: 0.3 }}\n                          className=\"flex-shrink-0\"\n                        >\n                          <svg\n                            className=\"w-6 h-6 text-gray-400\"\n                            fill=\"none\"\n                            stroke=\"currentColor\"\n                            viewBox=\"0 0 24 24\"\n                          >\n                            <path\n                              strokeLinecap=\"round\"\n                              strokeLinejoin=\"round\"\n                              strokeWidth={2}\n                              d=\"M19 9l-7 7-7-7\"\n                            />\n                          </svg>\n                        </motion.div>\n                      </div>\n                    </motion.button>\n\n                    <AnimatePresence>\n                      {openFAQ === faq.id && (\n                        <motion.div\n                          initial={{ height: 0, opacity: 0 }}\n                          animate={{ height: \"auto\", opacity: 1 }}\n                          exit={{ height: 0, opacity: 0 }}\n                          transition={{ duration: 0.3, ease: \"easeInOut\" }}\n                        >\n                          <CardContent className=\"px-6 pb-6 pt-0\">\n                            <div className=\"pl-12\">\n                              <motion.p\n                                className=\"text-gray-600 leading-relaxed\"\n                                initial={{ opacity: 0, y: 10 }}\n                                animate={{ opacity: 1, y: 0 }}\n                                transition={{ delay: 0.1 }}\n                              >\n                                {faq.answer}\n                              </motion.p>\n                            </div>\n                          </CardContent>\n                        </motion.div>\n                      )}\n                    </AnimatePresence>\n                  </Card>\n                </motion.div>\n              ))}\n            </AnimatePresence>\n          </motion.div>\n        </div>\n\n        {/* Contact Support */}\n        <motion.div\n          className=\"text-center mt-16\"\n          initial={{ opacity: 0, y: 20 }}\n          whileInView={{ opacity: 1, y: 0 }}\n          viewport={{ once: true }}\n          transition={{ delay: 0.6 }}\n        >\n          <div className=\"bg-gradient-to-r from-pink-50 to-orange-50 rounded-2xl p-8 max-w-2xl mx-auto\">\n            <div className=\"text-4xl mb-4\">🤝</div>\n            <h3 className=\"text-2xl font-bold text-gray-800 mb-4\">\n              Vous ne trouvez pas votre réponse ?\n            </h3>\n            <p className=\"text-gray-600 mb-6\">\n              Notre équipe support est là pour vous aider ! Contactez-nous et\n              nous vous répondrons dans les plus brefs délais.\n            </p>\n\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <motion.button\n                onClick={() => {\n                  const contactSection = document.getElementById(\"contact\");\n                  contactSection?.scrollIntoView({ behavior: \"smooth\" });\n                }}\n                className=\"bg-gradient-to-r from-pink-500 to-orange-500 text-white px-6 py-3 rounded-lg font-semibold hover:shadow-lg transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <span className=\"mr-2\">💬</span>\n                Nous contacter\n              </motion.button>\n\n              <motion.a\n                href=\"mailto:<EMAIL>\"\n                className=\"border-2 border-pink-500 text-pink-500 px-6 py-3 rounded-lg font-semibold hover:bg-pink-50 transition-all duration-300\"\n                whileHover={{ scale: 1.05 }}\n                whileTap={{ scale: 0.95 }}\n              >\n                <span className=\"mr-2\">📧</span>\n                <EMAIL>\n              </motion.a>\n            </div>\n          </div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport { FAQSection };\n"], "names": [], "mappings": ";;;;AAEA,gEAAgE;AAChE;AAAA;AACA;AAMA;AAAA;AACA;;;AAXA;;;;;AAqBA,MAAM,aAAuB;;IAC3B,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAiB;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAU;IAEjE,MAAM,WAAsB;QAC1B;YACE,IAAI;YACJ,UAAU;YACV,QACE;YACF,UAAU;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,UAAU;YACV,QACE;YACF,UAAU;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,UAAU;YACV,QACE;YACF,UAAU;YACV,MAAM;QACR;QACA;YACE,IAAI;YACJ,UAAU;YACV,QACE;YACF,UAAU;YACV,MAAM;QACR;KACD;IAED,MAAM,aAAa;QACjB;YAAE,IAAI;YAAO,MAAM;YAAU,MAAM;QAAK;QACxC;YAAE,IAAI;YAAW,MAAM;YAAW,MAAM;QAAI;QAC5C;YAAE,IAAI;YAAY,MAAM;YAAY,MAAM;QAAK;QAC/C;YAAE,IAAI;YAAY,MAAM;YAAa,MAAM;QAAK;QAChD;YAAE,IAAI;YAAW,MAAM;YAAY,MAAM;QAAK;KAC/C;IAED,MAAM,eACJ,qBAAqB,QACjB,WACA,SAAS,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK;IAElD,MAAM,YAAY,CAAC;QACjB,WAAW,YAAY,KAAK,OAAO;IACrC;IAEA,qBACE,4SAAC;QAAQ,IAAG;QAAM,WAAU;kBAG1B,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;oBAC3B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4SAAC;oCAAK,WAAU;8CAA6E;;;;;;8CAG7F,4SAAC;;;;;8CACD,4SAAC;oCAAK,WAAU;8CAAgB;;;;;;;;;;;;sCAGlC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAI;sCAC1B;;;;;;;;;;;;8BAOH,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,OAAO;oBAAI;8BAExB,WAAW,GAAG,CAAC,CAAC,yBACf,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;4BAEZ,SAAS,IAAM,oBAAoB,SAAS,EAAE;4BAC9C,WAAW,CAAC;;gBAEV,EACE,qBAAqB,SAAS,EAAE,GAC5B,sEACA,8CACL;cACH,CAAC;4BACD,YAAY;gCAAE,OAAO;4BAAK;4BAC1B,UAAU;gCAAE,OAAO;4BAAK;;8CAExB,4SAAC;oCAAK,WAAU;8CAAQ,SAAS,IAAI;;;;;;gCACpC,SAAS,IAAI;;2BAdT,SAAS,EAAE;;;;;;;;;;8BAoBtB,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,UAAU,2HAAA,CAAA,mBAAgB;wBAC1B,SAAQ;wBACR,aAAY;wBACZ,UAAU;4BAAE,MAAM;wBAAK;kCAEvB,cAAA,4SAAC,kSAAA,CAAA,kBAAe;sCACb,aAAa,GAAG,CAAC,CAAC,oBACjB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oCAET,UAAU,2HAAA,CAAA,cAAW;oCACrB,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAG;oCAC7B,SAAS;wCAAE,SAAS;wCAAG,GAAG;oCAAE;oCAC5B,MAAM;wCAAE,SAAS;wCAAG,GAAG,CAAC;oCAAG;oCAC3B,YAAY;wCAAE,UAAU;oCAAI;oCAC5B,MAAM;8CAEN,cAAA,4SAAC,mIAAA,CAAA,OAAI;wCAAC,WAAU;;0DACd,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;gDACZ,SAAS,IAAM,UAAU,IAAI,EAAE;gDAC/B,WAAU;gDACV,YAAY;oDACV,iBAAiB;gDACnB;0DAEA,cAAA,4SAAC;oDAAI,WAAU;;sEACb,4SAAC;4DAAI,WAAU;;8EACb,4SAAC;oEAAK,WAAU;8EAAY,IAAI,IAAI;;;;;;8EACpC,4SAAC;oEAAG,WAAU;8EACX,IAAI,QAAQ;;;;;;;;;;;;sEAIjB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4DACT,SAAS;gEAAE,QAAQ,YAAY,IAAI,EAAE,GAAG,MAAM;4DAAE;4DAChD,YAAY;gEAAE,UAAU;4DAAI;4DAC5B,WAAU;sEAEV,cAAA,4SAAC;gEACC,WAAU;gEACV,MAAK;gEACL,QAAO;gEACP,SAAQ;0EAER,cAAA,4SAAC;oEACC,eAAc;oEACd,gBAAe;oEACf,aAAa;oEACb,GAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0DAOZ,4SAAC,kSAAA,CAAA,kBAAe;0DACb,YAAY,IAAI,EAAE,kBACjB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oDACT,SAAS;wDAAE,QAAQ;wDAAG,SAAS;oDAAE;oDACjC,SAAS;wDAAE,QAAQ;wDAAQ,SAAS;oDAAE;oDACtC,MAAM;wDAAE,QAAQ;wDAAG,SAAS;oDAAE;oDAC9B,YAAY;wDAAE,UAAU;wDAAK,MAAM;oDAAY;8DAE/C,cAAA,4SAAC,mIAAA,CAAA,cAAW;wDAAC,WAAU;kEACrB,cAAA,4SAAC;4DAAI,WAAU;sEACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;gEACP,WAAU;gEACV,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAG;gEAC7B,SAAS;oEAAE,SAAS;oEAAG,GAAG;gEAAE;gEAC5B,YAAY;oEAAE,OAAO;gEAAI;0EAExB,IAAI,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;mCA9DpB,IAAI,EAAE;;;;;;;;;;;;;;;;;;;;8BA6ErB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS;wBAAE,SAAS;wBAAG,GAAG;oBAAG;oBAC7B,aAAa;wBAAE,SAAS;wBAAG,GAAG;oBAAE;oBAChC,UAAU;wBAAE,MAAM;oBAAK;oBACvB,YAAY;wBAAE,OAAO;oBAAI;8BAEzB,cAAA,4SAAC;wBAAI,WAAU;;0CACb,4SAAC;gCAAI,WAAU;0CAAgB;;;;;;0CAC/B,4SAAC;gCAAG,WAAU;0CAAwC;;;;;;0CAGtD,4SAAC;gCAAE,WAAU;0CAAqB;;;;;;0CAKlC,4SAAC;gCAAI,WAAU;;kDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,MAAM;wCACZ,SAAS;4CACP,MAAM,iBAAiB,SAAS,cAAc,CAAC;4CAC/C,gBAAgB,eAAe;gDAAE,UAAU;4CAAS;wCACtD;wCACA,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,4SAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;kDAIlC,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;wCACP,MAAK;wCACL,WAAU;wCACV,YAAY;4CAAE,OAAO;wCAAK;wCAC1B,UAAU;4CAAE,OAAO;wCAAK;;0DAExB,4SAAC;gDAAK,WAAU;0DAAO;;;;;;4CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAShD;GAjQM;KAAA", "debugId": null}}, {"offset": {"line": 6520, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/HeroSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { Button } from \"@/components/ui\";\n// Animations removed - using inline animations\nimport { useUI } from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport React, { useEffect, useState } from \"react\";\n\nconst HeroSection: React.FC = () => {\n  const { openCart } = useUI();\n  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });\n  // useScroll supprimé avec les effets parallax\n\n  // Parallax effects supprimés avec les emojis\n\n  // Mouse tracking for interactive elements\n  useEffect(() => {\n    const handleMouseMove = (e: MouseEvent) => {\n      setMousePosition({\n        x: (e.clientX / window.innerWidth) * 100,\n        y: (e.clientY / window.innerHeight) * 100,\n      });\n    };\n\n    window.addEventListener(\"mousemove\", handleMouseMove);\n    return () => window.removeEventListener(\"mousemove\", handleMouseMove);\n  }, []);\n\n  const scrollToProducts = () => {\n    const element = document.querySelector(\"#products\");\n    if (element) {\n      element.scrollIntoView({ behavior: \"smooth\" });\n    }\n  };\n\n  const openCartModal = () => {\n    openCart();\n  };\n\n  // Suppression des emojis flottants pour un design plus épuré\n\n  return (\n    <section\n      id=\"hero\"\n      className=\"relative min-h-[80vh] flex items-center justify-center overflow-hidden\"\n      style={{\n        background: \"var(--gradient-hero)\",\n      }}\n    >\n      {/* Background Pattern */}\n      <div className=\"absolute inset-0 opacity-30\">\n        <div\n          className=\"absolute inset-0 bg-repeat opacity-20\"\n          style={{\n            backgroundImage: `url(\"data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23f472b6' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E\")`,\n          }}\n        />\n      </div>\n\n      {/* Section des emojis flottants supprimée pour un design plus épuré */}\n\n      {/* Interactive Background Elements */}\n      <motion.div\n        className=\"absolute inset-0 pointer-events-none\"\n        style={{\n          background: `radial-gradient(circle at ${mousePosition.x}% ${mousePosition.y}%, rgba(236, 72, 153, 0.1) 0%, transparent 50%)`,\n        }}\n      />\n\n      {/* Main Content */}\n      <motion.div className=\"container mx-auto px-3 sm:px-4 lg:px-8 relative z-10\">\n        <div className=\"max-w-7xl mx-auto\">\n          <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 sm:gap-8 lg:gap-12 items-center min-h-[70vh] sm:min-h-[75vh] lg:min-h-[80vh]\">\n            {/* Colonne de texte */}\n            <div className=\"text-center lg:text-left px-2 sm:px-0\">\n              {/* Badge Nouveau */}\n              <motion.div\n                className=\"inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 mb-6 sm:mb-8 mx-auto lg:mx-0\"\n                initial={{ opacity: 0, y: -20, scale: 0.8 }}\n                animate={{\n                  opacity: 1,\n                  y: 0,\n                  scale: 1,\n                  rotate: [0, 1, -1, 0],\n                }}\n                transition={{\n                  delay: 0.2,\n                  duration: 0.6,\n                  rotate: {\n                    duration: 2,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  },\n                }}\n              >\n                <div className=\"relative\">\n                  <div className=\"absolute inset-0 bg-gradient-to-r from-pink-400 via-purple-500 to-orange-400 rounded-full blur-sm opacity-75\"></div>\n                  <div className=\"relative bg-gradient-to-r from-pink-500 via-purple-600 to-orange-500 text-white px-3 sm:px-4 lg:px-6 py-2 sm:py-2.5 lg:py-3 rounded-full shadow-lg border-2 border-white/20\">\n                    <div className=\"flex items-center space-x-1 sm:space-x-2\">\n                      <motion.span\n                        className=\"text-sm sm:text-base lg:text-lg\"\n                        animate={{ rotate: [0, 10, -10, 0] }}\n                        transition={{ duration: 1.5, repeat: Infinity }}\n                      >\n                        🌿\n                      </motion.span>\n                      <span className=\"font-bold text-xs sm:text-sm lg:text-base\">\n                        Découvrez notre gamme Deltagum !\n                      </span>\n                      <motion.span\n                        className=\"text-sm sm:text-base lg:text-lg\"\n                        animate={{ scale: [1, 1.2, 1] }}\n                        transition={{ duration: 1, repeat: Infinity }}\n                      >\n                        ✨\n                      </motion.span>\n                    </div>\n                  </div>\n                </div>\n              </motion.div>\n\n              {/* Main Title */}\n              <motion.h1\n                className=\"text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold mb-4 sm:mb-6 leading-tight font-serif\"\n                initial={{ opacity: 0, y: 50, scale: 0.9 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 0.8, ease: \"easeOut\" }}\n              >\n                <span className=\"block bg-gradient-to-r from-pink-500 via-purple-500 to-orange-500 bg-clip-text text-transparent\">\n                  Gamme\n                </span>\n                <span className=\"block bg-gradient-to-r from-green-500 via-blue-500 to-purple-500 bg-clip-text text-transparent\">\n                  Deltagum\n                </span>\n                <span className=\"block bg-gradient-to-r from-orange-500 via-pink-500 to-red-500 bg-clip-text text-transparent\">\n                  Premium\n                </span>\n              </motion.h1>\n\n              {/* Subtitle */}\n              <motion.p\n                className=\"text-base sm:text-lg md:text-xl lg:text-2xl text-gray-800 mb-6 sm:mb-8 max-w-2xl mx-auto lg:mx-0 leading-relaxed font-medium px-2 sm:px-0\"\n                initial={{ opacity: 0, y: 30 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.8, delay: 0.3, ease: \"easeOut\" }}\n              >\n                Découvrez notre gamme complète Deltagum : des produits premium\n                aux saveurs naturelles. Nos délicieux produits sont disponibles\n                en plusieurs variétés pour satisfaire tous les goûts. Relaxation\n                et bien-être dans chaque produit !\n                <br />\n                <span className=\"text-xs sm:text-sm text-gray-700 mt-2 block font-semibold\">\n                  🔞 Produit réservé aux adultes - Contient du Delta-9 THC\n                </span>\n              </motion.p>\n\n              {/* CTA Buttons */}\n              <motion.div\n                className=\"flex flex-col sm:flex-row gap-3 sm:gap-4 justify-center lg:justify-start items-center mb-8 sm:mb-12 px-2 sm:px-0\"\n                initial={{ opacity: 0, y: 30, scale: 0.9 }}\n                animate={{ opacity: 1, y: 0, scale: 1 }}\n                transition={{ duration: 0.8, delay: 0.5, ease: \"easeOut\" }}\n              >\n                <Button\n                  variant=\"primary\"\n                  size=\"lg\"\n                  onClick={scrollToProducts}\n                  className=\"w-full sm:w-auto text-sm sm:text-base lg:text-lg px-6 sm:px-8 py-3 sm:py-4 shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300\"\n                >\n                  Découvrir nos produits\n                </Button>\n\n                <Button\n                  variant=\"outline\"\n                  size=\"lg\"\n                  onClick={openCartModal}\n                  className=\"w-full sm:w-auto text-sm sm:text-base lg:text-lg px-6 sm:px-8 py-3 sm:py-4 border-2 hover:bg-pink-50 hover:text-pink-600 text-pink-600\"\n                >\n                  <span className=\"mr-2\">🛒</span>\n                  Voir mon panier\n                </Button>\n              </motion.div>\n            </div>\n\n            {/* Colonne d'image */}\n            <div className=\"relative mt-8 lg:mt-0\">\n              <motion.div\n                className=\"relative w-full max-w-lg sm:max-w-xl lg:max-w-2xl mx-auto\"\n                initial={{ opacity: 0, x: 50, scale: 0.9 }}\n                animate={{ opacity: 1, x: 0, scale: 1 }}\n                transition={{ duration: 1, delay: 0.4, ease: \"easeOut\" }}\n              >\n                {/* Image décorative */}\n                <div className=\"relative rounded-2xl sm:rounded-3xl overflow-hidden \">\n                  <img\n                    src=\"/img/herobn.png\"\n                    alt=\"Délicieux délices colorés - Image décorative\"\n                    className=\"w-full h-auto object-cover\"\n                    loading=\"eager\"\n                    onError={(e) => {\n                      e.currentTarget.src = \"/img/placeholder.svg\";\n                    }}\n                  />\n                </div>\n\n                {/* Éléments décoratifs flottants */}\n                <motion.div\n                  className=\"absolute -top-6 -left-6 w-12 h-12 bg-pink-400 rounded-full opacity-70\"\n                  animate={{\n                    y: [0, -10, 0],\n                    rotate: [0, 180, 360],\n                  }}\n                  transition={{\n                    duration: 4,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                />\n                <motion.div\n                  className=\"absolute -bottom-4 -right-4 w-8 h-8 bg-orange-400 rounded-full opacity-70\"\n                  animate={{\n                    y: [0, 10, 0],\n                    rotate: [0, -180, -360],\n                  }}\n                  transition={{\n                    duration: 3,\n                    repeat: Infinity,\n                    ease: \"easeInOut\",\n                  }}\n                />\n              </motion.div>\n            </div>\n          </div>\n\n          {/* Stats - Masqué sur mobile */}\n          <motion.div\n            className=\"hidden sm:grid grid-cols-3 gap-12 max-w-3xl mx-auto mt-8\"\n            initial={{ opacity: 0, y: 50 }}\n            animate={{ opacity: 1, y: 0 }}\n            transition={{ delay: 1.2 }}\n          >\n            <div className=\"text-center px-4 py-2\">\n              <motion.div\n                className=\"text-3xl font-bold text-pink-500 mb-3 min-h-[3rem] flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 1.4, type: \"spring\", stiffness: 200 }}\n              >\n                3\n              </motion.div>\n              <p className=\"text-gray-600 text-sm\">Variantes de saveurs</p>\n            </div>\n\n            <div className=\"text-center px-4 py-2\">\n              <motion.div\n                className=\"text-3xl font-bold text-orange-500 mb-3 min-h-[3rem] flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 1.6, type: \"spring\", stiffness: 200 }}\n              >\n                100%\n              </motion.div>\n              <p className=\"text-gray-600 text-sm\">Artisanal</p>\n            </div>\n\n            <div className=\"text-center px-4 py-2\">\n              <motion.div\n                className=\"text-3xl font-bold text-purple-500 mb-3 min-h-[3rem] flex items-center justify-center\"\n                initial={{ scale: 0 }}\n                animate={{ scale: 1 }}\n                transition={{ delay: 1.8, type: \"spring\", stiffness: 200 }}\n              >\n                ∞\n              </motion.div>\n              <p className=\"text-gray-600 text-sm\">Bien-être garanti</p>\n            </div>\n          </motion.div>\n        </div>\n      </motion.div>\n\n      {/* Indicateur de scroll supprimé pour un design plus épuré */}\n\n      {/* Decorative Elements */}\n      {/* Emojis d'arrière-plan supprimés pour un design plus épuré */}\n    </section>\n  );\n};\n\nexport { HeroSection };\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA,+CAA+C;AAC/C;AAAA;AACA;AACA;;;AANA;;;;;AAQA,MAAM,cAAwB;;IAC5B,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,QAAK,AAAD;IACzB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,GAAG;QAAG,GAAG;IAAE;IAChE,8CAA8C;IAE9C,6CAA6C;IAE7C,0CAA0C;IAC1C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM;yDAAkB,CAAC;oBACvB,iBAAiB;wBACf,GAAG,AAAC,EAAE,OAAO,GAAG,OAAO,UAAU,GAAI;wBACrC,GAAG,AAAC,EAAE,OAAO,GAAG,OAAO,WAAW,GAAI;oBACxC;gBACF;;YAEA,OAAO,gBAAgB,CAAC,aAAa;YACrC;yCAAO,IAAM,OAAO,mBAAmB,CAAC,aAAa;;QACvD;gCAAG,EAAE;IAEL,MAAM,mBAAmB;QACvB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,IAAI,SAAS;YACX,QAAQ,cAAc,CAAC;gBAAE,UAAU;YAAS;QAC9C;IACF;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,6DAA6D;IAE7D,qBACE,4SAAC;QACC,IAAG;QACH,WAAU;QACV,OAAO;YACL,YAAY;QACd;;0BAGA,4SAAC;gBAAI,WAAU;0BACb,cAAA,4SAAC;oBACC,WAAU;oBACV,OAAO;wBACL,iBAAiB,CAAC,gQAAgQ,CAAC;oBACrR;;;;;;;;;;;0BAOJ,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBACT,WAAU;gBACV,OAAO;oBACL,YAAY,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC,EAAE,EAAE,cAAc,CAAC,CAAC,+CAA+C,CAAC;gBAC/H;;;;;;0BAIF,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gBAAC,WAAU;0BACpB,cAAA,4SAAC;oBAAI,WAAU;;sCACb,4SAAC;4BAAI,WAAU;;8CAEb,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;gDAAI,OAAO;4CAAI;4CAC1C,SAAS;gDACP,SAAS;gDACT,GAAG;gDACH,OAAO;gDACP,QAAQ;oDAAC;oDAAG;oDAAG,CAAC;oDAAG;iDAAE;4CACvB;4CACA,YAAY;gDACV,OAAO;gDACP,UAAU;gDACV,QAAQ;oDACN,UAAU;oDACV,QAAQ;oDACR,MAAM;gDACR;4CACF;sDAEA,cAAA,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;;;;;;kEACf,4SAAC;wDAAI,WAAU;kEACb,cAAA,4SAAC;4DAAI,WAAU;;8EACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;oEACV,WAAU;oEACV,SAAS;wEAAE,QAAQ;4EAAC;4EAAG;4EAAI,CAAC;4EAAI;yEAAE;oEAAC;oEACnC,YAAY;wEAAE,UAAU;wEAAK,QAAQ;oEAAS;8EAC/C;;;;;;8EAGD,4SAAC;oEAAK,WAAU;8EAA4C;;;;;;8EAG5D,4SAAC,mSAAA,CAAA,SAAM,CAAC,IAAI;oEACV,WAAU;oEACV,SAAS;wEAAE,OAAO;4EAAC;4EAAG;4EAAK;yEAAE;oEAAC;oEAC9B,YAAY;wEAAE,UAAU;wEAAG,QAAQ;oEAAS;8EAC7C;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAST,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4CACR,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAI,OAAO;4CAAI;4CACzC,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAG,OAAO;4CAAE;4CACtC,YAAY;gDAAE,UAAU;gDAAK,MAAM;4CAAU;;8DAE7C,4SAAC;oDAAK,WAAU;8DAAkG;;;;;;8DAGlH,4SAAC;oDAAK,WAAU;8DAAiG;;;;;;8DAGjH,4SAAC;oDAAK,WAAU;8DAA+F;;;;;;;;;;;;sDAMjH,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;4CACP,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAG;4CAC7B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,YAAY;gDAAE,UAAU;gDAAK,OAAO;gDAAK,MAAM;4CAAU;;gDAC1D;8DAKC,4SAAC;;;;;8DACD,4SAAC;oDAAK,WAAU;8DAA4D;;;;;;;;;;;;sDAM9E,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAI,OAAO;4CAAI;4CACzC,SAAS;gDAAE,SAAS;gDAAG,GAAG;gDAAG,OAAO;4CAAE;4CACtC,YAAY;gDAAE,UAAU;gDAAK,OAAO;gDAAK,MAAM;4CAAU;;8DAEzD,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;8DACX;;;;;;8DAID,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,SAAS;oDACT,WAAU;;sEAEV,4SAAC;4DAAK,WAAU;sEAAO;;;;;;wDAAS;;;;;;;;;;;;;;;;;;;8CAOtC,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wCACT,WAAU;wCACV,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAI,OAAO;wCAAI;wCACzC,SAAS;4CAAE,SAAS;4CAAG,GAAG;4CAAG,OAAO;wCAAE;wCACtC,YAAY;4CAAE,UAAU;4CAAG,OAAO;4CAAK,MAAM;wCAAU;;0DAGvD,4SAAC;gDAAI,WAAU;0DACb,cAAA,4SAAC;oDACC,KAAI;oDACJ,KAAI;oDACJ,WAAU;oDACV,SAAQ;oDACR,SAAS,CAAC;wDACR,EAAE,aAAa,CAAC,GAAG,GAAG;oDACxB;;;;;;;;;;;0DAKJ,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,GAAG;wDAAC;wDAAG,CAAC;wDAAI;qDAAE;oDACd,QAAQ;wDAAC;wDAAG;wDAAK;qDAAI;gDACvB;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,MAAM;gDACR;;;;;;0DAEF,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;gDACT,WAAU;gDACV,SAAS;oDACP,GAAG;wDAAC;wDAAG;wDAAI;qDAAE;oDACb,QAAQ;wDAAC;wDAAG,CAAC;wDAAK,CAAC;qDAAI;gDACzB;gDACA,YAAY;oDACV,UAAU;oDACV,QAAQ;oDACR,MAAM;gDACR;;;;;;;;;;;;;;;;;;;;;;;sCAOR,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BACT,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,OAAO;4BAAI;;8CAEzB,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,OAAO;gDAAK,MAAM;gDAAU,WAAW;4CAAI;sDAC1D;;;;;;sDAGD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,OAAO;gDAAK,MAAM;gDAAU,WAAW;4CAAI;sDAC1D;;;;;;sDAGD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;8CAGvC,4SAAC;oCAAI,WAAU;;sDACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4CACT,WAAU;4CACV,SAAS;gDAAE,OAAO;4CAAE;4CACpB,SAAS;gDAAE,OAAO;4CAAE;4CACpB,YAAY;gDAAE,OAAO;gDAAK,MAAM;gDAAU,WAAW;4CAAI;sDAC1D;;;;;;sDAGD,4SAAC;4CAAE,WAAU;sDAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYnD;GAtRM;;QACiB,+HAAA,CAAA,QAAK;;;KADtB", "debugId": null}}, {"offset": {"line": 7146, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/testimonial-cards.tsx"], "sourcesContent": ["\"use client\";\n\nimport { motion } from \"framer-motion\";\nimport * as React from \"react\";\n\n// Photos Unsplash réelles de personnes pour les témoignages\nconst getAvatarUrl = (id: number): string => {\n  const avatars = [\n    \"https://images.unsplash.com/photo-1494790108755-2616b612b47c?w=128&h=128&fit=crop&crop=face\", // Femme souriante\n    \"https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=128&h=128&fit=crop&crop=face\", // Homme barbu\n    \"https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=128&h=128&fit=crop&crop=face\", // Femme brune\n    \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=128&h=128&fit=crop&crop=face\", // Homme en costume\n    \"https://images.unsplash.com/photo-1544005313-94ddf0286df2?w=128&h=128&fit=crop&crop=face\", // Femme blonde\n    \"https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=128&h=128&fit=crop&crop=face\", // Homme souriant\n  ];\n  return avatars[(id - 1) % avatars.length];\n};\n\ninterface TestimonialCardProps {\n  handleShuffle: () => void;\n  testimonial: string;\n  position: \"front\" | \"middle\" | \"back\";\n  id: number;\n  author: string;\n}\n\nexport function TestimonialCard({\n  handleShuffle,\n  testimonial,\n  position,\n  id,\n  author,\n}: TestimonialCardProps) {\n  const dragRef = React.useRef(0);\n  const isFront = position === \"front\";\n\n  return (\n    <motion.div\n      style={{\n        zIndex: position === \"front\" ? \"2\" : position === \"middle\" ? \"1\" : \"0\",\n      }}\n      animate={{\n        rotate:\n          position === \"front\"\n            ? \"-6deg\"\n            : position === \"middle\"\n            ? \"0deg\"\n            : \"6deg\",\n        x: position === \"front\" ? \"0%\" : position === \"middle\" ? \"25%\" : \"50%\",\n        y: position === \"front\" ? \"0%\" : position === \"middle\" ? \"5%\" : \"10%\",\n      }}\n      drag={true}\n      dragElastic={0.35}\n      dragListener={isFront}\n      dragConstraints={{\n        top: 0,\n        left: 0,\n        right: 0,\n        bottom: 0,\n      }}\n      onDragStart={(e) => {\n        const clientX =\n          \"clientX\" in e ? e.clientX : e.touches?.[0]?.clientX || 0;\n        dragRef.current = clientX;\n      }}\n      onDragEnd={(e) => {\n        const clientX =\n          \"clientX\" in e ? e.clientX : e.changedTouches?.[0]?.clientX || 0;\n        if (dragRef.current - clientX > 150) {\n          handleShuffle();\n        }\n        dragRef.current = 0;\n      }}\n      transition={{ duration: 0.35 }}\n      className={`absolute left-0 top-0 grid h-[380px] w-[280px] sm:h-[450px] sm:w-[350px] select-none place-content-center space-y-4 sm:space-y-6 rounded-xl sm:rounded-2xl border-2 border-pink-200 bg-white/90 p-4 sm:p-6 shadow-xl backdrop-blur-md ${\n        isFront ? \"cursor-grab active:cursor-grabbing\" : \"\"\n      }`}\n    >\n      <img\n        src={getAvatarUrl(id)}\n        alt={`Avatar de ${author}`}\n        className=\"pointer-events-none mx-auto h-20 w-20 sm:h-32 sm:w-32 rounded-full border-2 border-pink-200 bg-pink-50 object-cover\"\n        onError={(e) => {\n          // Fallback to a default avatar if Unsplash fails\n          e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(\n            author.split(\" \")[0]\n          )}&size=128&background=fbbf24&color=fff`;\n        }}\n      />\n      <span className=\"text-center text-sm sm:text-base lg:text-lg italic text-gray-700 leading-relaxed\">\n        \"{testimonial}\"\n      </span>\n      <span className=\"text-center text-xs sm:text-sm font-medium text-pink-600\">\n        {author}\n      </span>\n    </motion.div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,4DAA4D;AAC5D,MAAM,eAAe,CAAC;IACpB,MAAM,UAAU;QACd;QACA;QACA;QACA;QACA;QACA;KACD;IACD,OAAO,OAAO,CAAC,CAAC,KAAK,CAAC,IAAI,QAAQ,MAAM,CAAC;AAC3C;AAUO,SAAS,gBAAgB,EAC9B,aAAa,EACb,WAAW,EACX,QAAQ,EACR,EAAE,EACF,MAAM,EACe;;IACrB,MAAM,UAAU,CAAA,GAAA,4QAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,MAAM,UAAU,aAAa;IAE7B,qBACE,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;QACT,OAAO;YACL,QAAQ,aAAa,UAAU,MAAM,aAAa,WAAW,MAAM;QACrE;QACA,SAAS;YACP,QACE,aAAa,UACT,UACA,aAAa,WACb,SACA;YACN,GAAG,aAAa,UAAU,OAAO,aAAa,WAAW,QAAQ;YACjE,GAAG,aAAa,UAAU,OAAO,aAAa,WAAW,OAAO;QAClE;QACA,MAAM;QACN,aAAa;QACb,cAAc;QACd,iBAAiB;YACf,KAAK;YACL,MAAM;YACN,OAAO;YACP,QAAQ;QACV;QACA,aAAa,CAAC;YACZ,MAAM,UACJ,aAAa,IAAI,EAAE,OAAO,GAAG,EAAE,OAAO,EAAE,CAAC,EAAE,EAAE,WAAW;YAC1D,QAAQ,OAAO,GAAG;QACpB;QACA,WAAW,CAAC;YACV,MAAM,UACJ,aAAa,IAAI,EAAE,OAAO,GAAG,EAAE,cAAc,EAAE,CAAC,EAAE,EAAE,WAAW;YACjE,IAAI,QAAQ,OAAO,GAAG,UAAU,KAAK;gBACnC;YACF;YACA,QAAQ,OAAO,GAAG;QACpB;QACA,YAAY;YAAE,UAAU;QAAK;QAC7B,WAAW,CAAC,sOAAsO,EAChP,UAAU,uCAAuC,IACjD;;0BAEF,4SAAC;gBACC,KAAK,aAAa;gBAClB,KAAK,CAAC,UAAU,EAAE,QAAQ;gBAC1B,WAAU;gBACV,SAAS,CAAC;oBACR,iDAAiD;oBACjD,EAAE,aAAa,CAAC,GAAG,GAAG,CAAC,iCAAiC,EAAE,mBACxD,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,EACpB,qCAAqC,CAAC;gBAC1C;;;;;;0BAEF,4SAAC;gBAAK,WAAU;;oBAAmF;oBAC/F;oBAAY;;;;;;;0BAEhB,4SAAC;gBAAK,WAAU;0BACb;;;;;;;;;;;;AAIT;GAvEgB;KAAA", "debugId": null}}, {"offset": {"line": 7260, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/ui/shuffle-cards.tsx"], "sourcesContent": ["\"use client\";\n\nimport { TestimonialCard } from \"@/components/ui/testimonial-cards\";\nimport { useState } from \"react\";\n\nconst testimonials = [\n  {\n    id: 1,\n    testimonial:\n      \"Deltagum m'aide énormément à gérer mon stress quotidien. La saveur fraise est délicieuse et l'effet relaxant se fait sentir rapidement.\",\n    author: \"<PERSON>\",\n  },\n  {\n    id: 2,\n    testimonial:\n      \"J'ai découvert Deltagum pour mes problèmes de sommeil. Les délices à la myrtille m'aident à me détendre le soir. Qualité exceptionnelle.\",\n    author: \"<PERSON>\",\n  },\n  {\n    id: 3,\n    testimonial:\n      \"Excellente alternative naturelle pour la relaxation ! Les délices Deltagum saveur pomme sont parfaits pour décompresser.\",\n    author: \"<PERSON>\",\n  },\n  {\n    id: 4,\n    testimonial:\n      \"En tant que professionnel du bien-être, je recommande Deltagum. Produit de qualité premium, dosage parfait et saveurs naturelles remarquables.\",\n    author: \"<PERSON>\",\n  },\n  {\n    id: 5,\n    testimonial:\n      \"Service client très professionnel ! Ils m'ont bien expliqué les effets et conseillé le bon dosage. Produit efficace pour la détente.\",\n    author: \"Emma Moreau - Bordeaux\",\n  },\n  {\n    id: 6,\n    testimonial:\n      \"Livraison rapide et discrète. Les délices Deltagum sont devenus indispensables dans ma routine bien-être quotidienne.\",\n    author: \"Lucas Petit - Nice\",\n  },\n];\n\nfunction ShuffleCards() {\n  const [currentIndex, setCurrentIndex] = useState(0);\n\n  const handleShuffle = () => {\n    setCurrentIndex((prev) => (prev + 1) % testimonials.length);\n  };\n\n  // Afficher seulement 3 témoignages à la fois\n  const visibleTestimonials = [\n    testimonials[currentIndex],\n    testimonials[(currentIndex + 1) % testimonials.length],\n    testimonials[(currentIndex + 2) % testimonials.length],\n  ];\n  const positions = [\"front\", \"middle\", \"back\"];\n\n  return (\n    <div className=\"flex justify-center items-center px-4 sm:px-8 py-8 sm:py-12 min-h-[480px] sm:min-h-[600px] w-full overflow-visible\">\n      <div className=\"relative h-[380px] w-[280px] sm:h-[450px] sm:w-[350px] mx-auto\">\n        {visibleTestimonials.map((testimonial, index) => (\n          <TestimonialCard\n            key={testimonial.id}\n            {...testimonial}\n            handleShuffle={handleShuffle}\n            position={positions[index] as \"front\" | \"middle\" | \"back\"}\n          />\n        ))}\n      </div>\n    </div>\n  );\n}\n\nexport { ShuffleCards };\n"], "names": [], "mappings": ";;;;AAEA;AACA;;;AAHA;;;AAKA,MAAM,eAAe;IACnB;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;IACA;QACE,IAAI;QACJ,aACE;QACF,QAAQ;IACV;CACD;AAED,SAAS;;IACP,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;IAEjD,MAAM,gBAAgB;QACpB,gBAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,aAAa,MAAM;IAC5D;IAEA,6CAA6C;IAC7C,MAAM,sBAAsB;QAC1B,YAAY,CAAC,aAAa;QAC1B,YAAY,CAAC,CAAC,eAAe,CAAC,IAAI,aAAa,MAAM,CAAC;QACtD,YAAY,CAAC,CAAC,eAAe,CAAC,IAAI,aAAa,MAAM,CAAC;KACvD;IACD,MAAM,YAAY;QAAC;QAAS;QAAU;KAAO;IAE7C,qBACE,4SAAC;QAAI,WAAU;kBACb,cAAA,4SAAC;YAAI,WAAU;sBACZ,oBAAoB,GAAG,CAAC,CAAC,aAAa,sBACrC,4SAAC,mJAAA,CAAA,kBAAe;oBAEb,GAAG,WAAW;oBACf,eAAe;oBACf,UAAU,SAAS,CAAC,MAAM;mBAHrB,YAAY,EAAE;;;;;;;;;;;;;;;AAS/B;GA7BS;KAAA", "debugId": null}}, {"offset": {"line": 7358, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/TestimonialsSection.tsx"], "sourcesContent": ["\"use client\";\n\nimport { ShuffleCards } from \"@/components/ui/shuffle-cards\";\nimport {\n  fadeIn,\n  slideUp,\n  staggerContainer,\n  staggerItem,\n} from \"@/lib/animations\";\nimport { motion } from \"framer-motion\";\nimport React from \"react\";\n\nconst TestimonialsSection: React.FC = () => {\n  return (\n    <section\n      id=\"testimonials\"\n      className=\"py-12 sm:py-16 bg-gradient-to-br from-pink-50 to-orange-50 relative overflow-hidden\"\n    >\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n        {/* Section Header */}\n        <motion.div\n          className=\"text-center mb-8 sm:mb-12\"\n          initial={fadeIn.initial}\n          whileInView={fadeIn.animate}\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold mb-4 sm:mb-6\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n          >\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-500 bg-clip-text text-transparent\">\n              Nos Clients\n            </span>\n            <br />\n            <span className=\"text-black\">Nous Font Confiance</span>\n          </motion.h2>\n          <motion.p\n            className=\"text-base sm:text-lg lg:text-xl text-black max-w-2xl mx-auto px-2 sm:px-0\"\n            initial={slideUp.initial}\n            whileInView={slideUp.animate}\n            viewport={{ once: true }}\n            transition={{ delay: 0.2 }}\n          >\n            Découvrez les témoignages authentiques de nos clients satisfaits\n          </motion.p>\n        </motion.div>\n\n        {/* Interactive Testimonial Cards */}\n        <div className=\"flex justify-center mb-8 sm:mb-12 px-2 sm:px-0 overflow-visible\">\n          <div className=\"w-full max-w-md sm:max-w-lg lg:max-w-xl\">\n            <ShuffleCards />\n          </div>\n        </div>\n\n        {/* Statistics */}\n        <motion.div\n          className=\"grid grid-cols-2 md:grid-cols-4 gap-4 sm:gap-6 max-w-4xl mx-auto px-2 sm:px-0\"\n          variants={staggerContainer}\n          initial=\"initial\"\n          whileInView=\"animate\"\n          viewport={{ once: true }}\n        >\n          <motion.div variants={staggerItem} className=\"text-center\">\n            <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-pink-600 mb-1 sm:mb-2\">\n              4.9/5\n            </div>\n            <p className=\"text-black text-xs sm:text-sm\">Note moyenne</p>\n          </motion.div>\n\n          <motion.div variants={staggerItem} className=\"text-center\">\n            <div className=\"text-2xl sm:text-3xl md:text-4xl font-bold text-pink-600 mb-1 sm:mb-2\">\n              500+\n            </div>\n            <p className=\"text-black text-xs sm:text-sm\">Avis clients</p>\n          </motion.div>\n\n          <motion.div variants={staggerItem} className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-pink-600 mb-2\">\n              98%\n            </div>\n            <p className=\"text-black\">Clients satisfaits</p>\n          </motion.div>\n\n          <motion.div variants={staggerItem} className=\"text-center\">\n            <div className=\"text-3xl md:text-4xl font-bold text-pink-600 mb-2\">\n              24h\n            </div>\n            <p className=\"text-black\">Livraison moyenne</p>\n          </motion.div>\n        </motion.div>\n      </div>\n    </section>\n  );\n};\n\nexport { TestimonialsSection };\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAMA;AATA;;;;;AAYA,MAAM,sBAAgC;IACpC,qBACE,4SAAC;QACC,IAAG;QACH,WAAU;kBAEV,cAAA,4SAAC;YAAI,WAAU;;8BAEb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;oBAC3B,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;;8CAEvB,4SAAC;oCAAK,WAAU;8CAA6E;;;;;;8CAG7F,4SAAC;;;;;8CACD,4SAAC;oCAAK,WAAU;8CAAa;;;;;;;;;;;;sCAE/B,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,SAAS,2HAAA,CAAA,UAAO,CAAC,OAAO;4BACxB,aAAa,2HAAA,CAAA,UAAO,CAAC,OAAO;4BAC5B,UAAU;gCAAE,MAAM;4BAAK;4BACvB,YAAY;gCAAE,OAAO;4BAAI;sCAC1B;;;;;;;;;;;;8BAMH,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC;wBAAI,WAAU;kCACb,cAAA,4SAAC,+IAAA,CAAA,eAAY;;;;;;;;;;;;;;;8BAKjB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,2HAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,2HAAA,CAAA,cAAW;4BAAE,WAAU;;8CAC3C,4SAAC;oCAAI,WAAU;8CAAwE;;;;;;8CAGvF,4SAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAG/C,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,2HAAA,CAAA,cAAW;4BAAE,WAAU;;8CAC3C,4SAAC;oCAAI,WAAU;8CAAwE;;;;;;8CAGvF,4SAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;sCAG/C,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,2HAAA,CAAA,cAAW;4BAAE,WAAU;;8CAC3C,4SAAC;oCAAI,WAAU;8CAAoD;;;;;;8CAGnE,4SAAC;oCAAE,WAAU;8CAAa;;;;;;;;;;;;sCAG5B,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAAC,UAAU,2HAAA,CAAA,cAAW;4BAAE,WAAU;;8CAC3C,4SAAC;oCAAI,WAAU;8CAAoD;;;;;;8CAGnE,4SAAC;oCAAE,WAAU;8CAAa;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMtC;KAnFM", "debugId": null}}, {"offset": {"line": 7605, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/index.ts"], "sourcesContent": ["// Export des sections\nexport { CartSection } from \"./CartSection\";\nexport { CheckoutSection } from \"./CheckoutSection\";\nexport { ContactSection } from \"./ContactSection\";\nexport { FAQSection } from \"./FAQSection\";\nexport { HeroSection } from \"./HeroSection\";\n\nexport { TestimonialsSection } from \"./TestimonialsSection\";\n"], "names": [], "mappings": "AAAA,sBAAsB;;AACtB;AACA;AACA;AACA;AACA;AAEA", "debugId": null}}, {"offset": {"line": 7642, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/components/sections/ProductOverview.tsx"], "sourcesContent": ["\"use client\";\n\nimport { <PERSON><PERSON>, <PERSON>, <PERSON>ontent, CardHeader } from \"@/components/ui\";\nimport { fadeIn, staggerContainer, staggerItem } from \"@/lib/animations\";\nimport { formatPrice } from \"@/lib/utils\";\nimport { useProducts } from \"@/stores\";\nimport { motion } from \"framer-motion\";\nimport { Eye } from \"lucide-react\";\nimport Link from \"next/link\";\nimport { useEffect } from \"react\";\n\nconst ProductOverview = () => {\n  const { products, fetchProducts, loading, error } = useProducts();\n\n  useEffect(() => {\n    fetchProducts();\n  }, [fetchProducts]);\n\n  // Debug: afficher les produits dans la console\n  useEffect(() => {\n    console.log(\"ProductOverview - products:\", products);\n  }, [products]);\n\n  const getBasePrice = (product: any) => {\n    if (product.priceTiers && product.priceTiers.length > 0) {\n      const sortedTiers = [...product.priceTiers].sort(\n        (a, b) => a.quantity - b.quantity\n      );\n      return Number(sortedTiers[0].price);\n    }\n    return Number(product.basePrice);\n  };\n\n  const getBaseQuantity = (product: any) => {\n    if (product.priceTiers && product.priceTiers.length > 0) {\n      const sortedTiers = [...product.priceTiers].sort(\n        (a, b) => a.quantity - b.quantity\n      );\n      return sortedTiers[0].quantity;\n    }\n    return 1;\n  };\n\n  return (\n    <section id=\"products\" className=\"py-12 sm:py-16 lg:py-20 bg-white\">\n      <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n        <motion.div\n          className=\"text-center mb-10 sm:mb-12 lg:mb-16\"\n          variants={staggerContainer}\n          initial=\"initial\"\n          whileInView=\"animate\"\n          viewport={{ once: true }}\n        >\n          <motion.h2\n            className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6\"\n            variants={staggerItem}\n          >\n            Nos Produits{\" \"}\n            <span className=\"bg-gradient-to-r from-pink-500 to-orange-400 bg-clip-text text-transparent\">\n              Deltagum\n            </span>\n          </motion.h2>\n          <motion.p\n            className=\"text-base sm:text-lg lg:text-xl text-gray-600 max-w-3xl mx-auto px-2 sm:px-0\"\n            variants={staggerItem}\n          >\n            Découvrez notre gamme de produits Deltagum premium\n          </motion.p>\n        </motion.div>\n\n        {/* État de chargement avec skeleton */}\n        {loading && (\n          <motion.div\n            className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto\"\n            initial={fadeIn.initial}\n            animate={fadeIn.animate}\n          >\n            {Array.from({ length: 2 }).map((_, index) => (\n              <motion.div\n                key={index}\n                className=\"bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100\"\n                initial={{ opacity: 0, y: 20 }}\n                animate={{ opacity: 1, y: 0 }}\n                transition={{ duration: 0.5, delay: index * 0.1 }}\n              >\n                {/* Image skeleton */}\n                <div className=\"relative h-64 bg-gradient-to-br from-pink-50 to-purple-50 p-6\">\n                  <div className=\"w-full h-full bg-gray-200 animate-pulse rounded-xl\"></div>\n                </div>\n\n                {/* Content skeleton */}\n                <div className=\"p-6\">\n                  {/* Badge skeleton */}\n                  <div className=\"text-center mb-4\">\n                    <div className=\"inline-block h-6 w-24 bg-gray-200 animate-pulse rounded-full\"></div>\n                  </div>\n\n                  {/* Title skeleton */}\n                  <div className=\"h-8 w-3/4 bg-gray-200 animate-pulse rounded mx-auto mb-3\"></div>\n\n                  {/* Description skeleton */}\n                  <div className=\"space-y-2 mb-6\">\n                    <div className=\"h-4 w-full bg-gray-200 animate-pulse rounded\"></div>\n                    <div className=\"h-4 w-5/6 bg-gray-200 animate-pulse rounded\"></div>\n                    <div className=\"h-4 w-4/6 bg-gray-200 animate-pulse rounded\"></div>\n                  </div>\n\n                  {/* Price skeleton */}\n                  <div className=\"text-center mb-6\">\n                    <div className=\"h-6 w-20 bg-gray-200 animate-pulse rounded mx-auto\"></div>\n                  </div>\n\n                  {/* Button skeleton */}\n                  <div className=\"h-12 w-full bg-gray-200 animate-pulse rounded-lg\"></div>\n                </div>\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* État d'erreur */}\n        {error && !loading && (\n          <motion.div\n            className=\"text-center py-16\"\n            initial={fadeIn.initial}\n            animate={fadeIn.animate}\n          >\n            <div className=\"text-6xl mb-4\">⚠️</div>\n            <h3 className=\"text-xl font-semibold text-red-600 mb-2\">\n              Erreur de chargement\n            </h3>\n            <p className=\"text-gray-600 mb-4\">{error}</p>\n            <Button\n              onClick={() => fetchProducts()}\n              variant=\"primary\"\n              className=\"mt-4\"\n            >\n              Réessayer\n            </Button>\n          </motion.div>\n        )}\n\n        {/* Produits chargés avec succès */}\n        {!loading && !error && products.length > 0 && (\n          <motion.div\n            className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6 lg:gap-8 max-w-4xl mx-auto\"\n            variants={staggerContainer}\n            initial=\"initial\"\n            whileInView=\"animate\"\n            viewport={{ once: true }}\n          >\n            {products.map((product) => (\n              <motion.div key={product.id} variants={staggerItem}>\n                <Card className=\"h-full hover:shadow-xl transition-all duration-300 group overflow-hidden\">\n                  {/* Image en pleine largeur en haut de la carte */}\n                  <div className=\"relative w-full h-40 sm:h-44 lg:h-48 bg-gray-100 overflow-hidden\">\n                    <img\n                      src={product.image || \"/img/placeholder.svg\"}\n                      alt={product.name}\n                      className=\"w-full h-full object-cover transition-transform duration-300 group-hover:scale-105\"\n                      onError={(e) => {\n                        e.currentTarget.src = \"/img/placeholder.svg\";\n                      }}\n                    />\n                    <div className=\"absolute bottom-1.5 sm:bottom-2 left-1.5 sm:left-2 bg-red-500 text-white text-xs font-bold px-1.5 sm:px-2 py-0.5 sm:py-1 rounded-full shadow-lg\">\n                      18+\n                    </div>\n                  </div>\n\n                  <CardHeader className=\"text-center pb-3 sm:pb-4 px-3 sm:px-6\">\n                    <h3 className=\"text-lg sm:text-xl font-bold text-gray-900 mb-2\">\n                      {product.name}\n                    </h3>\n                  </CardHeader>\n\n                  <CardContent className=\"pt-0 px-3 sm:px-6\">\n                    <p className=\"text-gray-600 text-xs sm:text-sm mb-3 sm:mb-4\">\n                      {product.description}\n                    </p>\n\n                    <div className=\"text-center mb-3 sm:mb-4\">\n                      <div className=\"text-lg sm:text-xl lg:text-2xl font-bold text-pink-600\">\n                        À partir de {formatPrice(getBasePrice(product))}\n                      </div>\n                      <div className=\"text-xs sm:text-sm text-gray-500\">\n                        pour {getBaseQuantity(product)} unité\n                      </div>\n                    </div>\n\n                    <div className=\"mb-3 sm:mb-4\">\n                      <div className=\"text-xs sm:text-sm font-medium text-gray-700 mb-2\">\n                        Saveurs disponibles : {product.variants?.length || 0}\n                      </div>\n                      <div className=\"flex justify-center space-x-1\">\n                        {product.variants\n                          ?.slice(0, 3)\n                          .map((variant: any, idx: number) => (\n                            <div\n                              key={idx}\n                              className=\"w-3 h-3 sm:w-4 sm:h-4 rounded-full border-2 border-white shadow-sm\"\n                              style={{ backgroundColor: variant.color }}\n                            />\n                          ))}\n                      </div>\n                    </div>\n\n                    <div className=\"flex items-center justify-center space-x-2 mb-3 sm:mb-4\">\n                      <div className=\"w-2 h-2 rounded-full bg-green-500\" />\n                      <span className=\"text-xs sm:text-sm text-gray-600\">\n                        En stock\n                      </span>\n                    </div>\n\n                    <Link href={`/products/${product.id}`}>\n                      <Button\n                        variant=\"primary\"\n                        size=\"md\"\n                        className=\"w-full text-sm sm:text-base\"\n                      >\n                        <Eye className=\"w-3 h-3 sm:w-4 sm:h-4 mr-1 sm:mr-2\" />\n                        Voir le produit\n                      </Button>\n                    </Link>\n                  </CardContent>\n                </Card>\n              </motion.div>\n            ))}\n          </motion.div>\n        )}\n\n        {/* Aucun produit trouvé (après chargement réussi) */}\n        {!loading && !error && products.length === 0 && (\n          <motion.div\n            className=\"text-center py-16\"\n            initial={fadeIn.initial}\n            animate={fadeIn.animate}\n          >\n            <div className=\"text-6xl mb-4\">🍬</div>\n            <h3 className=\"text-2xl font-bold text-gray-900 mb-2\">\n              Nos produits arrivent bientôt !\n            </h3>\n            <p className=\"text-gray-600\">\n              Nous préparons une sélection exceptionnelle de produits Deltagum\n              pour vous.\n            </p>\n          </motion.div>\n        )}\n      </div>\n    </section>\n  );\n};\n\nexport default ProductOverview;\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;;;AATA;;;;;;;;;AAWA,MAAM,kBAAkB;;IACtB,MAAM,EAAE,QAAQ,EAAE,aAAa,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,oIAAA,CAAA,cAAW,AAAD;IAE9D,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR;QACF;oCAAG;QAAC;KAAc;IAElB,+CAA+C;IAC/C,CAAA,GAAA,4QAAA,CAAA,YAAS,AAAD;qCAAE;YACR,QAAQ,GAAG,CAAC,+BAA+B;QAC7C;oCAAG;QAAC;KAAS;IAEb,MAAM,eAAe,CAAC;QACpB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;YACvD,MAAM,cAAc;mBAAI,QAAQ,UAAU;aAAC,CAAC,IAAI,CAC9C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAEnC,OAAO,OAAO,WAAW,CAAC,EAAE,CAAC,KAAK;QACpC;QACA,OAAO,OAAO,QAAQ,SAAS;IACjC;IAEA,MAAM,kBAAkB,CAAC;QACvB,IAAI,QAAQ,UAAU,IAAI,QAAQ,UAAU,CAAC,MAAM,GAAG,GAAG;YACvD,MAAM,cAAc;mBAAI,QAAQ,UAAU;aAAC,CAAC,IAAI,CAC9C,CAAC,GAAG,IAAM,EAAE,QAAQ,GAAG,EAAE,QAAQ;YAEnC,OAAO,WAAW,CAAC,EAAE,CAAC,QAAQ;QAChC;QACA,OAAO;IACT;IAEA,qBACE,4SAAC;QAAQ,IAAG;QAAW,WAAU;kBAC/B,cAAA,4SAAC;YAAI,WAAU;;8BACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,2HAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;;sCAEvB,4SAAC,mSAAA,CAAA,SAAM,CAAC,EAAE;4BACR,WAAU;4BACV,UAAU,2HAAA,CAAA,cAAW;;gCACtB;gCACc;8CACb,4SAAC;oCAAK,WAAU;8CAA6E;;;;;;;;;;;;sCAI/F,4SAAC,mSAAA,CAAA,SAAM,CAAC,CAAC;4BACP,WAAU;4BACV,UAAU,2HAAA,CAAA,cAAW;sCACtB;;;;;;;;;;;;gBAMF,yBACC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;8BAEtB,MAAM,IAAI,CAAC;wBAAE,QAAQ;oBAAE,GAAG,GAAG,CAAC,CAAC,GAAG,sBACjC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAET,WAAU;4BACV,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAG;4BAC7B,SAAS;gCAAE,SAAS;gCAAG,GAAG;4BAAE;4BAC5B,YAAY;gCAAE,UAAU;gCAAK,OAAO,QAAQ;4BAAI;;8CAGhD,4SAAC;oCAAI,WAAU;8CACb,cAAA,4SAAC;wCAAI,WAAU;;;;;;;;;;;8CAIjB,4SAAC;oCAAI,WAAU;;sDAEb,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAI,WAAU;;;;;;;;;;;sDAIjB,4SAAC;4CAAI,WAAU;;;;;;sDAGf,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;oDAAI,WAAU;;;;;;8DACf,4SAAC;oDAAI,WAAU;;;;;;8DACf,4SAAC;oDAAI,WAAU;;;;;;;;;;;;sDAIjB,4SAAC;4CAAI,WAAU;sDACb,cAAA,4SAAC;gDAAI,WAAU;;;;;;;;;;;sDAIjB,4SAAC;4CAAI,WAAU;;;;;;;;;;;;;2BAlCZ;;;;;;;;;;gBA0CZ,SAAS,CAAC,yBACT,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;;sCAEvB,4SAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,4SAAC;4BAAG,WAAU;sCAA0C;;;;;;sCAGxD,4SAAC;4BAAE,WAAU;sCAAsB;;;;;;sCACnC,4SAAC,qIAAA,CAAA,SAAM;4BACL,SAAS,IAAM;4BACf,SAAQ;4BACR,WAAU;sCACX;;;;;;;;;;;;gBAOJ,CAAC,WAAW,CAAC,SAAS,SAAS,MAAM,GAAG,mBACvC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,UAAU,2HAAA,CAAA,mBAAgB;oBAC1B,SAAQ;oBACR,aAAY;oBACZ,UAAU;wBAAE,MAAM;oBAAK;8BAEtB,SAAS,GAAG,CAAC,CAAC,wBACb,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;4BAAkB,UAAU,2HAAA,CAAA,cAAW;sCAChD,cAAA,4SAAC,mIAAA,CAAA,OAAI;gCAAC,WAAU;;kDAEd,4SAAC;wCAAI,WAAU;;0DACb,4SAAC;gDACC,KAAK,QAAQ,KAAK,IAAI;gDACtB,KAAK,QAAQ,IAAI;gDACjB,WAAU;gDACV,SAAS,CAAC;oDACR,EAAE,aAAa,CAAC,GAAG,GAAG;gDACxB;;;;;;0DAEF,4SAAC;gDAAI,WAAU;0DAAkJ;;;;;;;;;;;;kDAKnK,4SAAC,mIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,4SAAC;4CAAG,WAAU;sDACX,QAAQ,IAAI;;;;;;;;;;;kDAIjB,4SAAC,mIAAA,CAAA,cAAW;wCAAC,WAAU;;0DACrB,4SAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAGtB,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;;4DAAyD;4DACzD,CAAA,GAAA,sHAAA,CAAA,cAAW,AAAD,EAAE,aAAa;;;;;;;kEAExC,4SAAC;wDAAI,WAAU;;4DAAmC;4DAC1C,gBAAgB;4DAAS;;;;;;;;;;;;;0DAInC,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;;4DAAoD;4DAC1C,QAAQ,QAAQ,EAAE,UAAU;;;;;;;kEAErD,4SAAC;wDAAI,WAAU;kEACZ,QAAQ,QAAQ,EACb,MAAM,GAAG,GACV,IAAI,CAAC,SAAc,oBAClB,4SAAC;gEAEC,WAAU;gEACV,OAAO;oEAAE,iBAAiB,QAAQ,KAAK;gEAAC;+DAFnC;;;;;;;;;;;;;;;;0DAQf,4SAAC;gDAAI,WAAU;;kEACb,4SAAC;wDAAI,WAAU;;;;;;kEACf,4SAAC;wDAAK,WAAU;kEAAmC;;;;;;;;;;;;0DAKrD,4SAAC,8QAAA,CAAA,UAAI;gDAAC,MAAM,CAAC,UAAU,EAAE,QAAQ,EAAE,EAAE;0DACnC,cAAA,4SAAC,qIAAA,CAAA,SAAM;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;;sEAEV,4SAAC,uRAAA,CAAA,MAAG;4DAAC,WAAU;;;;;;wDAAuC;;;;;;;;;;;;;;;;;;;;;;;;2BAnE/C,QAAQ,EAAE;;;;;;;;;;gBA+EhC,CAAC,WAAW,CAAC,SAAS,SAAS,MAAM,KAAK,mBACzC,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;oBACT,WAAU;oBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;oBACvB,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;;sCAEvB,4SAAC;4BAAI,WAAU;sCAAgB;;;;;;sCAC/B,4SAAC;4BAAG,WAAU;sCAAwC;;;;;;sCAGtD,4SAAC;4BAAE,WAAU;sCAAgB;;;;;;;;;;;;;;;;;;;;;;;AASzC;GA/OM;;QACgD,oIAAA,CAAA,cAAW;;;KAD3D;uCAiPS", "debugId": null}}, {"offset": {"line": 8200, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport {\n  ContactSection,\n  FAQSection,\n  HeroSection,\n  TestimonialsSection,\n} from \"@/components/sections\";\nimport ProductOverview from \"@/components/sections/ProductOverview\";\n\nexport default function Home() {\n  return (\n    <main>\n      {/* Section d'accueil avec hero */}\n      <HeroSection />\n\n      {/* Section des produits */}\n      <ProductOverview />\n\n      {/* Section des témoignages */}\n      <TestimonialsSection />\n\n      {/* Section FAQ */}\n      <FAQSection />\n\n      {/* Section de contact */}\n      <ContactSection />\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AAAA;AAAA;AAAA;AAMA;AARA;;;;AAUe,SAAS;IACtB,qBACE,4SAAC;;0BAEC,4SAAC,gJAAA,CAAA,cAAW;;;;;0BAGZ,4SAAC,oJAAA,CAAA,UAAe;;;;;0BAGhB,4SAAC,wJAAA,CAAA,sBAAmB;;;;;0BAGpB,4SAAC,+IAAA,CAAA,aAAU;;;;;0BAGX,4SAAC,mJAAA,CAAA,iBAAc;;;;;;;;;;;AAGrB;KAnBwB", "debugId": null}}]}