{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/src/app/professionals/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { But<PERSON> } from \"@/components/ui\";\nimport { fadeIn } from \"@/lib/animations\";\nimport { motion } from \"framer-motion\";\nimport { useState } from \"react\";\n\nexport default function ProfessionalsPage() {\n  const [formData, setFormData] = useState({\n    company: \"\",\n    name: \"\",\n    email: \"\",\n    phone: \"\",\n    businessType: \"\",\n    message: \"\",\n  });\n\n  const handleSubmit = (e: React.FormEvent) => {\n    e.preventDefault();\n    // Logique d'envoi du formulaire\n    console.log(\"Formulaire soumis:\", formData);\n    alert(\"Votre demande a été envoyée ! Nous vous recontacterons sous 48h.\");\n  };\n\n  const handleChange = (\n    e: React.ChangeEvent<\n      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement\n    >\n  ) => {\n    setFormData({\n      ...formData,\n      [e.target.name]: e.target.value,\n    });\n  };\n\n  return (\n    <main className=\"pt-16 sm:pt-20\">\n      {/* Hero Section */}\n      <section className=\"py-12 sm:py-16 bg-gradient-to-br from-pink-50 to-orange-50\">\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n          <motion.div\n            className=\"text-center max-w-4xl mx-auto\"\n            initial={fadeIn.initial}\n            animate={fadeIn.animate}\n            transition={{ duration: 0.8 }}\n          >\n            <h1 className=\"text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold text-gray-900 mb-4 sm:mb-6\">\n              Professionnels &{\" \"}\n              <span className=\"bg-gradient-to-r from-pink-500 to-orange-400 bg-clip-text text-transparent\">\n                Revendeurs\n              </span>\n            </h1>\n            <p className=\"text-base sm:text-lg lg:text-xl text-gray-600 leading-relaxed px-2 sm:px-0\">\n              Prospection <strong>B2B</strong> : boutiques spécialisées, vapes,\n              etc. Bénéficiez de <strong>tarifs spéciaux</strong> pour les\n              détaillants.\n            </p>\n          </motion.div>\n        </div>\n      </section>\n\n      {/* Tarifs spéciaux pour détaillants */}\n      {/*<section className=\"py-16 bg-white\">\n        <div className=\"container mx-auto px-4 sm:px-6 lg:px-8\">\n          <motion.div\n            className=\"text-center mb-12\"\n            initial={fadeIn.initial}\n            whileInView={fadeIn.animate}\n            viewport={{ once: true }}\n          >\n            <h2 className=\"text-3xl font-bold text-gray-900 mb-4\">\n              💰 Tarifs spéciaux pour les détaillants\n            </h2>\n            <p className=\"text-gray-600 max-w-2xl mx-auto\">\n              Bénéficiez de conditions préférentielles pour développer votre\n              activité.\n            </p>\n          </motion.div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto\">\n            <motion.div\n              className=\"bg-blue-50 rounded-xl p-6 text-center border-2 border-blue-200\"\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n            >\n              <div className=\"text-4xl mb-4\">🏪</div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Boutiques spécialisées\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Tarifs préférentiels pour les boutiques spécialisées\n              </p>\n              <div className=\"text-2xl font-bold text-blue-600\">-20%</div>\n            </motion.div>\n\n            <motion.div\n              className=\"bg-purple-50 rounded-xl p-6 text-center border-2 border-purple-200\"\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.1 }}\n            >\n              <div className=\"text-4xl mb-4\">💨</div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Vape Shops\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Conditions spéciales pour les magasins de vape\n              </p>\n              <div className=\"text-2xl font-bold text-purple-600\">-25%</div>\n            </motion.div>\n\n            <motion.div\n              className=\"bg-green-50 rounded-xl p-6 text-center border-2 border-green-200\"\n              initial={{ opacity: 0, y: 30 }}\n              whileInView={{ opacity: 1, y: 0 }}\n              viewport={{ once: true }}\n              transition={{ delay: 0.2 }}\n            >\n              <div className=\"text-4xl mb-4\">📦</div>\n              <h3 className=\"text-xl font-bold text-gray-900 mb-3\">\n                Achat en gros\n              </h3>\n              <p className=\"text-gray-600 mb-4\">\n                Remises dégressives selon les volumes\n              </p>\n              <div className=\"text-2xl font-bold text-green-600\">\n                Jusqu'à -30%\n              </div>\n            </motion.div>\n          </div>\n        </div>\n      </section>*/}\n\n      {/* Formulaire de contact pour achat en gros */}\n      <section className=\"py-12 sm:py-16 bg-gray-50\">\n        <div className=\"container mx-auto px-3 sm:px-4 lg:px-8\">\n          <motion.div\n            className=\"max-w-4xl mx-auto\"\n            initial={fadeIn.initial}\n            whileInView={fadeIn.animate}\n            viewport={{ once: true }}\n          >\n            <div className=\"text-center mb-8 sm:mb-12\">\n              <h2 className=\"text-2xl sm:text-3xl font-bold text-gray-900 mb-3 sm:mb-4\">\n                📋 Formulaire de contact pour achat en gros\n              </h2>\n              <p className=\"text-gray-600 max-w-2xl mx-auto text-sm sm:text-base px-2 sm:px-0\">\n                Contactez-nous pour obtenir vos tarifs préférentiels et\n                développer votre activité.\n              </p>\n            </div>\n\n            <div className=\"bg-white rounded-xl sm:rounded-2xl shadow-lg p-4 sm:p-6 lg:p-8\">\n              <form onSubmit={handleSubmit} className=\"space-y-4 sm:space-y-6\">\n                <div className=\"grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6\">\n                  <div>\n                    <label\n                      htmlFor=\"company\"\n                      className=\"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2\"\n                    >\n                      Nom de l'entreprise *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"company\"\n                      name=\"company\"\n                      required\n                      value={formData.company}\n                      onChange={handleChange}\n                      className=\"w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900 placeholder-gray-400 text-sm sm:text-base\"\n                      placeholder=\"Votre entreprise\"\n                    />\n                  </div>\n\n                  <div>\n                    <label\n                      htmlFor=\"name\"\n                      className=\"block text-xs sm:text-sm font-medium text-gray-700 mb-1 sm:mb-2\"\n                    >\n                      Nom et prénom *\n                    </label>\n                    <input\n                      type=\"text\"\n                      id=\"name\"\n                      name=\"name\"\n                      required\n                      value={formData.name}\n                      onChange={handleChange}\n                      className=\"w-full px-3 sm:px-4 py-2 sm:py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900 placeholder-gray-400 text-sm sm:text-base\"\n                      placeholder=\"Votre nom complet\"\n                    />\n                  </div>\n\n                  <div>\n                    <label\n                      htmlFor=\"email\"\n                      className=\"block text-sm font-medium text-gray-700 mb-2\"\n                    >\n                      Email professionnel *\n                    </label>\n                    <input\n                      type=\"email\"\n                      id=\"email\"\n                      name=\"email\"\n                      required\n                      value={formData.email}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900 placeholder-gray-400\"\n                      placeholder=\"<EMAIL>\"\n                    />\n                  </div>\n\n                  <div>\n                    <label\n                      htmlFor=\"phone\"\n                      className=\"block text-sm font-medium text-gray-700 mb-2\"\n                    >\n                      Téléphone\n                    </label>\n                    <input\n                      type=\"tel\"\n                      id=\"phone\"\n                      name=\"phone\"\n                      value={formData.phone}\n                      onChange={handleChange}\n                      className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900 placeholder-gray-400\"\n                      placeholder=\"06 12 34 56 78\"\n                    />\n                  </div>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"businessType\"\n                    className=\"block text-sm font-medium text-gray-700 mb-2\"\n                  >\n                    Type d'activité *\n                  </label>\n                  <select\n                    id=\"businessType\"\n                    name=\"businessType\"\n                    required\n                    value={formData.businessType}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900\"\n                  >\n                    <option value=\"\">Sélectionnez votre activité</option>\n                    <option value=\"boutique-specialisee\">\n                      Boutique spécialisée\n                    </option>\n                    <option value=\"vape-shop\">Vape Shop</option>\n                    <option value=\"grossiste\">Grossiste</option>\n                    <option value=\"pharmacie\">Pharmacie</option>\n                    <option value=\"autre\">Autre</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label\n                    htmlFor=\"message\"\n                    className=\"block text-sm font-medium text-gray-700 mb-2\"\n                  >\n                    Message / Besoins spécifiques\n                  </label>\n                  <textarea\n                    id=\"message\"\n                    name=\"message\"\n                    rows={4}\n                    value={formData.message}\n                    onChange={handleChange}\n                    className=\"w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:border-pink-500 transition-colors text-gray-900 placeholder-gray-400\"\n                    placeholder=\"Décrivez vos besoins, volumes souhaités, questions...\"\n                  />\n                </div>\n\n                <div className=\"text-center\">\n                  <Button\n                    type=\"submit\"\n                    variant=\"primary\"\n                    size=\"lg\"\n                    className=\"w-full sm:w-auto px-6 sm:px-8 text-sm sm:text-base\"\n                  >\n                    📧 Envoyer ma demande\n                  </Button>\n                  <p className=\"text-xs sm:text-sm text-gray-500 mt-2 sm:mt-3\">\n                    Nous vous recontacterons sous 48h avec une offre\n                    personnalisée\n                  </p>\n                </div>\n              </form>\n            </div>\n          </motion.div>\n        </div>\n      </section>\n    </main>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AACA;AACA;;;AALA;;;;;AAOe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,4QAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,SAAS;QACT,MAAM;QACN,OAAO;QACP,OAAO;QACP,cAAc;QACd,SAAS;IACX;IAEA,MAAM,eAAe,CAAC;QACpB,EAAE,cAAc;QAChB,gCAAgC;QAChC,QAAQ,GAAG,CAAC,sBAAsB;QAClC,MAAM;IACR;IAEA,MAAM,eAAe,CACnB;QAIA,YAAY;YACV,GAAG,QAAQ;YACX,CAAC,EAAE,MAAM,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,KAAK;QACjC;IACF;IAEA,qBACE,4SAAC;QAAK,WAAU;;0BAEd,4SAAC;gBAAQ,WAAU;0BACjB,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;wBACvB,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;wBACvB,YAAY;4BAAE,UAAU;wBAAI;;0CAE5B,4SAAC;gCAAG,WAAU;;oCAAoF;oCAC/E;kDACjB,4SAAC;wCAAK,WAAU;kDAA6E;;;;;;;;;;;;0CAI/F,4SAAC;gCAAE,WAAU;;oCAA6E;kDAC5E,4SAAC;kDAAO;;;;;;oCAAY;kDACb,4SAAC;kDAAO;;;;;;oCAAwB;;;;;;;;;;;;;;;;;;;;;;;0BAkF3D,4SAAC;gBAAQ,WAAU;0BACjB,cAAA,4SAAC;oBAAI,WAAU;8BACb,cAAA,4SAAC,mSAAA,CAAA,SAAM,CAAC,GAAG;wBACT,WAAU;wBACV,SAAS,2HAAA,CAAA,SAAM,CAAC,OAAO;wBACvB,aAAa,2HAAA,CAAA,SAAM,CAAC,OAAO;wBAC3B,UAAU;4BAAE,MAAM;wBAAK;;0CAEvB,4SAAC;gCAAI,WAAU;;kDACb,4SAAC;wCAAG,WAAU;kDAA4D;;;;;;kDAG1E,4SAAC;wCAAE,WAAU;kDAAoE;;;;;;;;;;;;0CAMnF,4SAAC;gCAAI,WAAU;0CACb,cAAA,4SAAC;oCAAK,UAAU;oCAAc,WAAU;;sDACtC,4SAAC;4CAAI,WAAU;;8DACb,4SAAC;;sEACC,4SAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,4SAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,OAAO;4DACvB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,4SAAC;;sEACC,4SAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,4SAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,IAAI;4DACpB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,4SAAC;;sEACC,4SAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,4SAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,QAAQ;4DACR,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;8DAIhB,4SAAC;;sEACC,4SAAC;4DACC,SAAQ;4DACR,WAAU;sEACX;;;;;;sEAGD,4SAAC;4DACC,MAAK;4DACL,IAAG;4DACH,MAAK;4DACL,OAAO,SAAS,KAAK;4DACrB,UAAU;4DACV,WAAU;4DACV,aAAY;;;;;;;;;;;;;;;;;;sDAKlB,4SAAC;;8DACC,4SAAC;oDACC,SAAQ;oDACR,WAAU;8DACX;;;;;;8DAGD,4SAAC;oDACC,IAAG;oDACH,MAAK;oDACL,QAAQ;oDACR,OAAO,SAAS,YAAY;oDAC5B,UAAU;oDACV,WAAU;;sEAEV,4SAAC;4DAAO,OAAM;sEAAG;;;;;;sEACjB,4SAAC;4DAAO,OAAM;sEAAuB;;;;;;sEAGrC,4SAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,4SAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,4SAAC;4DAAO,OAAM;sEAAY;;;;;;sEAC1B,4SAAC;4DAAO,OAAM;sEAAQ;;;;;;;;;;;;;;;;;;sDAI1B,4SAAC;;8DACC,4SAAC;oDACC,SAAQ;oDACR,WAAU;8DACX;;;;;;8DAGD,4SAAC;oDACC,IAAG;oDACH,MAAK;oDACL,MAAM;oDACN,OAAO,SAAS,OAAO;oDACvB,UAAU;oDACV,WAAU;oDACV,aAAY;;;;;;;;;;;;sDAIhB,4SAAC;4CAAI,WAAU;;8DACb,4SAAC,qIAAA,CAAA,SAAM;oDACL,MAAK;oDACL,SAAQ;oDACR,MAAK;oDACL,WAAU;8DACX;;;;;;8DAGD,4SAAC;oDAAE,WAAU;8DAAgD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/E;GAnSwB;KAAA", "debugId": null}}]}