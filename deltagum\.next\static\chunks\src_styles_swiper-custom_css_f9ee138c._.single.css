/* [project]/src/styles/swiper-custom.css [app-client] (css) */
.swiper-pagination-bullet {
  opacity: .7 !important;
  background: #f3f4f6 !important;
  width: 12px !important;
  height: 12px !important;
  transition: all .3s !important;
}

.swiper-pagination-bullet-active {
  opacity: 1 !important;
  background: linear-gradient(135deg, #ec4899, #f97316) !important;
  transform: scale(1.2) !important;
}

.swiper-pagination {
  padding-top: 20px !important;
  bottom: 0 !important;
}

.flavor-nav-button {
  z-index: 10;
  color: #6b7280;
  cursor: pointer;
  background: #fff;
  border: none;
  border-radius: 50%;
  justify-content: center;
  align-items: center;
  width: 44px;
  height: 44px;
  transition: all .3s;
  display: flex;
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  box-shadow: 0 4px 12px #00000026;
}

.flavor-nav-button:hover {
  color: #ec4899;
  transform: translateY(-50%)scale(1.05);
  box-shadow: 0 6px 20px #0003;
}

.flavor-nav-button:active {
  transform: translateY(-50%)scale(.95);
}

.flavor-nav-button.prev {
  left: -22px;
}

.flavor-nav-button.next {
  right: -22px;
}

@media (width <= 640px) {
  .flavor-nav-button {
    width: 36px;
    height: 36px;
  }

  .flavor-nav-button.prev {
    left: -18px;
  }

  .flavor-nav-button.next {
    right: -18px;
  }
}

.flavor-card {
  transition: all .3s cubic-bezier(.4, 0, .2, 1);
}

.flavor-card:hover {
  transform: translateY(-4px);
}

.flavor-card.selected {
  transform: translateY(-2px)scale(1.02);
}

.flavor-gradient-overlay {
  transition: opacity .3s;
}

.flavor-badge {
  -webkit-backdrop-filter: blur(8px);
}

.selection-indicator {
  animation: 2s infinite pulse;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.05);
  }
}

.main-flavor-image {
  transition: all .5s cubic-bezier(.4, 0, .2, 1);
}

.main-flavor-image:hover {
  transform: scale(1.02);
}

.flavor-nav-button:focus, .flavor-card:focus {
  outline-offset: 2px;
  outline: 2px solid #ec4899;
}

.loading-shimmer {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%) 0 0 / 200% 100%;
  animation: 1.5s infinite shimmer;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

/*# sourceMappingURL=src_styles_swiper-custom_css_f9ee138c._.single.css.map*/