{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/styles/swiper-custom.css"], "sourcesContent": ["/* Styles personnalisés pour Swiper */\n\n/* Pagination bullets */\n.swiper-pagination-bullet {\n  width: 12px !important;\n  height: 12px !important;\n  background: #f3f4f6 !important;\n  opacity: 0.7 !important;\n  transition: all 0.3s ease !important;\n}\n\n.swiper-pagination-bullet-active {\n  background: linear-gradient(135deg, #ec4899, #f97316) !important;\n  opacity: 1 !important;\n  transform: scale(1.2) !important;\n}\n\n/* Espacement de la pagination */\n.swiper-pagination {\n  bottom: 0 !important;\n  padding-top: 20px !important;\n}\n\n/* Styles pour les boutons de navigation personnalisés */\n.flavor-nav-button {\n  position: absolute;\n  top: 50%;\n  transform: translateY(-50%);\n  z-index: 10;\n  width: 44px;\n  height: 44px;\n  background: white;\n  border-radius: 50%;\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  color: #6b7280;\n  transition: all 0.3s ease;\n  border: none;\n  cursor: pointer;\n}\n\n.flavor-nav-button:hover {\n  color: #ec4899;\n  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);\n  transform: translateY(-50%) scale(1.05);\n}\n\n.flavor-nav-button:active {\n  transform: translateY(-50%) scale(0.95);\n}\n\n.flavor-nav-button.prev {\n  left: -22px;\n}\n\n.flavor-nav-button.next {\n  right: -22px;\n}\n\n/* Responsive adjustments */\n@media (max-width: 640px) {\n  .flavor-nav-button {\n    width: 36px;\n    height: 36px;\n  }\n  \n  .flavor-nav-button.prev {\n    left: -18px;\n  }\n  \n  .flavor-nav-button.next {\n    right: -18px;\n  }\n}\n\n/* Animation pour les cartes */\n.flavor-card {\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.flavor-card:hover {\n  transform: translateY(-4px);\n}\n\n.flavor-card.selected {\n  transform: translateY(-2px) scale(1.02);\n}\n\n/* Gradient overlay animation */\n.flavor-gradient-overlay {\n  transition: opacity 0.3s ease;\n}\n\n/* Styles pour les badges */\n.flavor-badge {\n  backdrop-filter: blur(8px);\n  -webkit-backdrop-filter: blur(8px);\n}\n\n/* Animation pour l'indicateur de sélection */\n.selection-indicator {\n  animation: pulse 2s infinite;\n}\n\n@keyframes pulse {\n  0%, 100% {\n    transform: scale(1);\n  }\n  50% {\n    transform: scale(1.05);\n  }\n}\n\n/* Styles pour l'image principale */\n.main-flavor-image {\n  transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);\n}\n\n.main-flavor-image:hover {\n  transform: scale(1.02);\n}\n\n/* Amélioration de l'accessibilité */\n.flavor-nav-button:focus {\n  outline: 2px solid #ec4899;\n  outline-offset: 2px;\n}\n\n.flavor-card:focus {\n  outline: 2px solid #ec4899;\n  outline-offset: 2px;\n}\n\n/* Animation de chargement */\n.loading-shimmer {\n  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);\n  background-size: 200% 100%;\n  animation: shimmer 1.5s infinite;\n}\n\n@keyframes shimmer {\n  0% {\n    background-position: -200% 0;\n  }\n  100% {\n    background-position: 200% 0;\n  }\n}\n"], "names": [], "mappings": "AAGA;;;;;;;;AAQA;;;;;;AAOA;;;;;AAMA;;;;;;;;;;;;;;;;;;;AAmBA;;;;;;AAMA;;;;AAIA;;;;AAIA;;;;AAKA;EACE;;;;;EAKA;;;;EAIA;;;;;AAMF;;;;AAIA;;;;AAIA;;;;AAKA;;;;AAKA;;;;AAMA;;;;AAIA;;;;;;;;;;AAUA;;;;AAIA;;;;AAKA;;;;;AAWA;;;;;AAMA"}}]}