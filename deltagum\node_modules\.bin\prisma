#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules/prisma/build/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules/prisma/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules/prisma/build/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules/prisma/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/prisma@6.11.1_typescript@5.8.3/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../prisma/build/index.js" "$@"
else
  exec node  "$basedir/../prisma/build/index.js" "$@"
fi
