@SETLOCAL
@IF NOT DEFINED NODE_PATH (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules\react-native\node_modules;C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules;C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\node_modules"
) ELSE (
  @SET "NODE_PATH=C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules\react-native\node_modules;C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules;C:\Users\<USER>\Documents\augment-projects\Deltagum\deltagum\node_modules\.pnpm\node_modules;%NODE_PATH%"
)
@IF EXIST "%~dp0\node.exe" (
  "%~dp0\node.exe"  "%~dp0\..\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules\react-native\cli.js" %*
) ELSE (
  @SET PATHEXT=%PATHEXT:;.JS;=;%
  node  "%~dp0\..\.pnpm\react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca\node_modules\react-native\cli.js" %*
)
