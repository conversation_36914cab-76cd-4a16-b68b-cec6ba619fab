#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca/node_modules/react-native/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca/node_modules/react-native/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/react-native@0.80.1_@babel+_e6355918dec44e87eec97426abffe2ca/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../../react-native/cli.js" "$@"
else
  exec node  "$basedir/../../../../react-native/cli.js" "$@"
fi
