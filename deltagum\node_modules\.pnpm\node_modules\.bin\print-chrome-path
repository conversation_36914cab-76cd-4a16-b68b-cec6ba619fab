#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules/chrome-launcher/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/chrome-launcher@0.15.2/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../chrome-launcher/bin/print-chrome-path.js" "$@"
else
  exec node  "$basedir/../chrome-launcher/bin/print-chrome-path.js" "$@"
fi
