#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/bin/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/dist/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules/next/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/next@15.3.5_@babel+core@7.2_185ca0f072c7c00081c01751178945af/node_modules:/mnt/c/Users/<USER>/Documents/augment-projects/Deltagum/deltagum/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../next/dist/bin/next" "$@"
else
  exec node  "$basedir/../../../next/dist/bin/next" "$@"
fi
