{"name": "deltagum", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "pnpm dlx prisma generate && next build", "vercel-build": "pnpm dlx prisma generate && pnpm dlx prisma db push && next build", "start": "next start", "lint": "next lint", "db:generate": "pnpm dlx prisma generate", "db:push": "pnpm dlx prisma db push", "db:migrate": "pnpm dlx prisma migrate dev", "db:seed": "tsx prisma/seed.ts", "update-products": "tsx scripts/update-products.ts", "db:studio": "pnpm dlx prisma studio", "postinstall": "pnpm dlx prisma generate"}, "dependencies": {"@auth/prisma-adapter": "^2.10.0", "@hookform/resolvers": "^5.1.1", "@prisma/client": "^6.11.1", "@react-spring/web": "^10.0.1", "@stripe/react-stripe-js": "^3.7.0", "@stripe/stripe-js": "^7.4.0", "@types/jsonwebtoken": "^9.0.10", "@uploadthing/react": "^7.3.2", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.0", "jsonwebtoken": "^9.0.2", "lottie-react": "^2.4.1", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^4.24.11", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.60.0", "react-spring": "^10.0.1", "resend": "^4.6.0", "stripe": "^18.3.0", "swiper": "^11.2.10", "tailwind-merge": "^3.3.1", "uploadthing": "^7.7.3", "zod": "^3.25.75", "zustand": "^5.0.6"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^24.0.13", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.5", "prisma": "^6.11.1", "tailwindcss": "^4", "tsx": "^4.20.3", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}