"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui";
import { motion } from "framer-motion";
import { Users, Search, Mail, Phone, Calendar } from "lucide-react";
import { useEffect, useState } from "react";

const fadeIn = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

export default function AdminCustomersPage() {
  const [customers, setCustomers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const loadCustomers = async () => {
    try {
      setLoading(true);
      // Pour l'instant, on simule des données
      // Plus tard, on créera l'API /api/admin/customers
      setCustomers([]);
    } catch (error) {
      console.error("Erreur lors du chargement des clients:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadCustomers();
  }, []);

  return (
    <div className="space-y-6">
      {/* Header */}
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Gestion des clients
          </h1>
          <p className="text-gray-600 mt-1">
            Bientôt disponible - En cours de développement
          </p>
        </div>
        
        <Button
          onClick={loadCustomers}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <Users className="w-4 h-4" />
          <span>Actualiser</span>
        </Button>
      </motion.div>

      {/* Recherche */}
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="bg-white rounded-xl shadow-sm border border-gray-200 p-6"
      >
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Rechercher un client..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-pink-500 focus:border-transparent"
            disabled
          />
        </div>
      </motion.div>

      {/* Contenu principal */}
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="bg-white rounded-xl shadow-sm border border-gray-200"
      >
        <div className="p-8 text-center">
          <Users className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h3 className="text-lg font-semibold text-gray-900 mb-2">
            Gestion des clients
          </h3>
          <p className="text-gray-600 mb-6">
            Cette fonctionnalité est en cours de développement. Elle permettra de :
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 max-w-4xl mx-auto">
            <div className="p-4 bg-gray-50 rounded-lg">
              <Mail className="w-8 h-8 text-blue-500 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900 mb-1">Gestion des contacts</h4>
              <p className="text-sm text-gray-600">Voir et gérer les informations clients</p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <Phone className="w-8 h-8 text-green-500 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900 mb-1">Historique des commandes</h4>
              <p className="text-sm text-gray-600">Consulter l'historique d'achat</p>
            </div>
            
            <div className="p-4 bg-gray-50 rounded-lg">
              <Calendar className="w-8 h-8 text-purple-500 mx-auto mb-2" />
              <h4 className="font-medium text-gray-900 mb-1">Statistiques clients</h4>
              <p className="text-sm text-gray-600">Analyser le comportement d'achat</p>
            </div>
          </div>
          
          <div className="mt-8">
            <p className="text-sm text-gray-500">
              En attendant, vous pouvez consulter les informations clients via les commandes dans la section 
              <span className="font-medium text-pink-600"> Gestion des commandes</span>.
            </p>
          </div>
        </div>
      </motion.div>
    </div>
  );
}
