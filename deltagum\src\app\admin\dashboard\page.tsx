"use client";

import {
  <PERSON><PERSON>,
  <PERSON>,
  CardContent,
  <PERSON><PERSON><PERSON>er,
  CardTitle,
} from "@/components/ui";
import { motion } from "framer-motion";
import {
  DollarSign,
  Eye,
  Package,
  ShoppingCart,
  TrendingUp,
  Users,
} from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

const fadeIn = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

export default function AdminDashboard() {
  const [statsData, setStatsData] = useState({
    products: 0,
    orders: 0,
    customers: 0,
    revenue: 0,
  });
  const [recentOrders, setRecentOrders] = useState([]);
  const [loading, setLoading] = useState(true);

  const loadStats = async () => {
    try {
      setLoading(true);

      const [productsRes, ordersRes] = await Promise.all([
        fetch("/api/products"),
        fetch("/api/admin/orders?limit=5"),
      ]);

      const [productsData, ordersData] = await Promise.all([
        productsRes.json(),
        ordersRes.json(),
      ]);

      console.log("📊 Données reçues:", { productsData, ordersData });

      // Correction pour les produits
      let products = 0;
      if (productsData.success && productsData.data) {
        if (Array.isArray(productsData.data)) {
          products = productsData.data.length;
        } else if (
          productsData.data.products &&
          Array.isArray(productsData.data.products)
        ) {
          // Structure: { products: [...], total: number }
          products = productsData.data.products.length;
        } else if (productsData.data.total) {
          // Utiliser le total si disponible
          products = productsData.data.total;
        }
      }

      // Correction pour les commandes
      const orders = ordersData.success ? ordersData.data.totalOrders || 0 : 0;
      const revenue =
        ordersData.success && ordersData.data.orders
          ? ordersData.data.orders.reduce(
              (sum: number, order: any) => sum + Number(order.totalAmount),
              0
            )
          : 0;

      setStatsData({
        products,
        orders,
        customers: 0,
        revenue,
      });

      if (ordersData.success) {
        setRecentOrders(ordersData.data.orders.slice(0, 5));
      }
    } catch (error) {
      console.error("Erreur lors du chargement des statistiques:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadStats();
  }, []);

  return (
    <div className="space-y-8">
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4"
      >
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold text-gray-900">
            Vue d'ensemble
          </h1>
          <p className="text-gray-600 mt-1">
            Tableau de bord administrateur Deltagum
          </p>
        </div>
        <Button
          onClick={loadStats}
          variant="outline"
          className="flex items-center space-x-2"
        >
          <TrendingUp className="w-4 h-4" />
          <span>Actualiser</span>
        </Button>
      </motion.div>

      {/* Statistiques principales */}
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
      >
        <Link href="/admin/products">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Produits
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? "..." : statsData.products}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-blue-500 group-hover:scale-110 transition-transform">
                  <Package className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Link href="/admin/orders">
          <Card className="hover:shadow-lg transition-all duration-200 cursor-pointer group">
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">
                    Commandes
                  </p>
                  <p className="text-2xl font-bold text-gray-900">
                    {loading ? "..." : statsData.orders}
                  </p>
                </div>
                <div className="p-3 rounded-full bg-green-500 group-hover:scale-110 transition-transform">
                  <ShoppingCart className="w-6 h-6 text-white" />
                </div>
              </div>
            </CardContent>
          </Card>
        </Link>

        <Card className="opacity-75">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Clients
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {loading ? "..." : statsData.customers}
                </p>
                <p className="text-xs text-gray-500">Bientôt disponible</p>
              </div>
              <div className="p-3 rounded-full bg-purple-500">
                <Users className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 mb-1">
                  Chiffre d'affaires
                </p>
                <p className="text-2xl font-bold text-gray-900">
                  {loading
                    ? "..."
                    : `${Number(statsData.revenue || 0).toFixed(2)}€`}
                </p>
              </div>
              <div className="p-3 rounded-full bg-orange-500">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Commandes récentes et actions rapides */}
      <motion.div
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="grid grid-cols-1 lg:grid-cols-2 gap-8"
      >
        {/* Commandes récentes */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <CardTitle className="text-lg font-semibold">
              Commandes récentes
            </CardTitle>
            <Link href="/admin/orders">
              <Button
                variant="outline"
                size="sm"
                className="flex items-center space-x-1"
              >
                <Eye className="w-4 h-4" />
                <span>Voir tout</span>
              </Button>
            </Link>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-4">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-pink-500 mx-auto"></div>
              </div>
            ) : recentOrders.length === 0 ? (
              <p className="text-gray-500 text-center py-4">
                Aucune commande récente
              </p>
            ) : (
              <div className="space-y-3">
                {recentOrders.map((order: any) => (
                  <div
                    key={order.id}
                    className="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div>
                      <p className="font-medium text-gray-900">
                        #{order.id.slice(-8)}
                      </p>
                      <p className="text-sm text-gray-600">
                        {order.customer.firstName} {order.customer.lastName}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium text-gray-900">
                        {Number(order.totalAmount).toFixed(2)}€
                      </p>
                      <p className="text-xs text-gray-500">
                        {new Date(order.createdAt).toLocaleDateString("fr-FR")}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Actions rapides */}
        <Card>
          <CardHeader>
            <CardTitle className="text-lg font-semibold">
              Actions rapides
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <Link href="/admin/products" className="mt-2">
                <Button variant="outline" className="w-full justify-start">
                  <Package className="w-4 h-4 mr-2" />
                  Gérer les produits
                </Button>
              </Link>
              <Link href="/admin/orders" className="mt-2">
                <Button variant="outline" className="w-full justify-start">
                  <ShoppingCart className="w-4 h-4 mr-2" />
                  Voir les commandes
                </Button>
              </Link>
              <Link href="/" className="mt-2">
                <Button variant="outline" className="w-full justify-start">
                  <Eye className="w-4 h-4 mr-2" />
                  Voir le site
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
