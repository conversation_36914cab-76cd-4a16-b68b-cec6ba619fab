"use client";

import { useAuth } from "@/stores/auth-store";
import { motion } from "framer-motion";
import {
  DollarSign,
  Home,
  LogOut,
  Package,
  ShoppingCart,
  Users,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import { useEffect } from "react";

const slideIn = {
  initial: { x: -50, opacity: 0 },
  animate: { x: 0, opacity: 1, transition: { duration: 0.5 } },
};

const fadeIn = {
  initial: { opacity: 0, y: 20 },
  animate: { opacity: 1, y: 0, transition: { duration: 0.5 } },
};

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, isAuthenticated, logout, isAdmin, checkAuth } = useAuth();
  const router = useRouter();
  const pathname = usePathname();

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  useEffect(() => {
    if (!isAuthenticated) {
      router.push("/auth");
      return;
    }

    if (!isAdmin()) {
      router.push("/");
      return;
    }
  }, [isAuthenticated, isAdmin, router]);

  if (!isAuthenticated || !isAdmin()) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-pink-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Vérification des permissions...</p>
        </div>
      </div>
    );
  }

  const menuItems = [
    {
      id: "dashboard",
      label: "Vue d'ensemble",
      icon: DollarSign,
      href: "/admin/dashboard",
      description: "Statistiques générales",
    },
    {
      id: "products",
      label: "Produits",
      icon: Package,
      href: "/admin/products",
      description: "Gestion des produits",
    },
    {
      id: "orders",
      label: "Commandes",
      icon: ShoppingCart,
      href: "/admin/orders",
      description: "Gestion des commandes",
    },
    {
      id: "customers",
      label: "Clients",
      icon: Users,
      href: "/admin/customers",
      description: "Gestion des clients",
    },
  ];

  const isActive = (href: string) => {
    if (href === "/admin/dashboard") {
      return pathname === "/admin/dashboard" || pathname === "/admin";
    }
    return pathname.startsWith(href);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-pink-50 via-white to-orange-50">
      {/* Header */}
      <motion.header
        initial={fadeIn.initial}
        animate={fadeIn.animate}
        className="bg-white shadow-sm border-b border-gray-200"
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-4">
              <Link
                href="/"
                className="flex items-center space-x-2 text-gray-600 hover:text-pink-600 transition-colors"
              >
                <Home className="w-5 h-5" />
                <span className="hidden sm:inline">Retour au site</span>
              </Link>
              <div className="h-6 w-px bg-gray-300"></div>
              <h1 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-pink-600 to-orange-500 bg-clip-text text-transparent">
                Dashboard Admin
              </h1>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden sm:block text-right">
                <p className="text-sm font-medium text-gray-900">
                  {user?.firstName} {user?.lastName}
                </p>
                <p className="text-xs text-gray-500">Administrateur</p>
              </div>
              <button
                onClick={logout}
                className="flex items-center space-x-2 px-3 py-2 text-sm text-gray-600 hover:text-red-600 hover:bg-red-50 rounded-lg transition-colors"
              >
                <LogOut className="w-4 h-4" />
                <span className="hidden sm:inline">Déconnexion</span>
              </button>
            </div>
          </div>
        </div>
      </motion.header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col lg:flex-row gap-8">
          {/* Sidebar fixe */}
          <motion.div
            initial={slideIn.initial}
            animate={slideIn.animate}
            className="w-full lg:w-64 bg-white rounded-xl shadow-lg border border-gray-200 lg:sticky lg:top-8 lg:h-fit"
          >
            <div className="p-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-4">
                Navigation
              </h2>
              <nav className="space-y-2">
                {menuItems.map((item) => (
                  <Link
                    key={item.id}
                    href={item.href}
                    className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-all duration-200 group ${
                      isActive(item.href)
                        ? "bg-gradient-to-r from-pink-500 to-orange-500 text-white shadow-md"
                        : "text-gray-700 hover:bg-gray-50 hover:text-pink-600"
                    }`}
                  >
                    <item.icon
                      className={`w-5 h-5 ${
                        isActive(item.href)
                          ? "text-white"
                          : "text-gray-500 group-hover:text-pink-500"
                      }`}
                    />
                    <div className="flex-1">
                      <span className="font-medium">{item.label}</span>
                      <p
                        className={`text-xs mt-0.5 ${
                          isActive(item.href)
                            ? "text-pink-100"
                            : "text-gray-500"
                        }`}
                      >
                        {item.description}
                      </p>
                    </div>
                  </Link>
                ))}
              </nav>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className="flex-1">
            <motion.div
              initial={fadeIn.initial}
              animate={fadeIn.animate}
              className="space-y-6"
            >
              {children}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
}
