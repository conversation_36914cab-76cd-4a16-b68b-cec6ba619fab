import { prisma } from "@/lib/prisma";
import { ApiResponse } from "@/types";
import { NextRequest, NextResponse } from "next/server";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Paramètres de pagination
    const page = parseInt(searchParams.get("page") || "1");
    const limit = parseInt(searchParams.get("limit") || "10");
    const skip = (page - 1) * limit;
    
    // Paramètres de filtrage
    const search = searchParams.get("search") || "";
    const status = searchParams.get("status") || "";
    
    // Construction des filtres
    const where: any = {};
    
    // Filtre par statut
    if (status && status !== "all") {
      where.status = status;
    }
    
    // Filtre de recherche
    if (search) {
      where.OR = [
        {
          id: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          customer: {
            firstName: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          customer: {
            lastName: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          customer: {
            email: {
              contains: search,
              mode: "insensitive",
            },
          },
        },
        {
          shippingFirstName: {
            contains: search,
            mode: "insensitive",
          },
        },
        {
          shippingLastName: {
            contains: search,
            mode: "insensitive",
          },
        },
      ];
    }

    // Compter le total des commandes
    const totalOrders = await prisma.order.count({ where });
    
    // Récupérer les commandes avec pagination
    const orders = await prisma.order.findMany({
      where,
      include: {
        customer: {
          select: {
            id: true,
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                id: true,
                name: true,
              },
            },
            variant: {
              select: {
                id: true,
                flavor: true,
              },
            },
          },
        },
      },
      orderBy: {
        createdAt: "desc",
      },
      skip,
      take: limit,
    });

    // Calculer les métadonnées de pagination
    const totalPages = Math.ceil(totalOrders / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    const response: ApiResponse = {
      success: true,
      data: {
        orders,
        pagination: {
          currentPage: page,
          totalPages,
          totalOrders,
          ordersPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
        // Compatibilité avec l'ancien format
        totalOrders,
        totalPages,
      },
      message: `${orders.length} commande${orders.length > 1 ? 's' : ''} récupérée${orders.length > 1 ? 's' : ''}`,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Erreur lors de la récupération des commandes:", error);

    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Erreur interne du serveur",
    };

    return NextResponse.json(response, { status: 500 });
  }
}

// Mettre à jour le statut d'une commande
export async function PATCH(request: NextRequest) {
  try {
    const body = await request.json();
    const { orderId, status } = body;

    if (!orderId || !status) {
      return NextResponse.json(
        {
          success: false,
          error: "ID de commande et statut requis",
        },
        { status: 400 }
      );
    }

    // Vérifier que le statut est valide
    const validStatuses = ["pending", "confirmed", "shipped", "delivered", "cancelled"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        {
          success: false,
          error: "Statut invalide",
        },
        { status: 400 }
      );
    }

    // Mettre à jour la commande
    const updatedOrder = await prisma.order.update({
      where: { id: orderId },
      data: { status },
      include: {
        customer: {
          select: {
            firstName: true,
            lastName: true,
            email: true,
          },
        },
        items: {
          include: {
            product: {
              select: {
                name: true,
              },
            },
            variant: {
              select: {
                flavor: true,
              },
            },
          },
        },
      },
    });

    const response: ApiResponse = {
      success: true,
      data: updatedOrder,
      message: "Statut de la commande mis à jour avec succès",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Erreur lors de la mise à jour de la commande:", error);

    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Erreur interne du serveur",
    };

    return NextResponse.json(response, { status: 500 });
  }
}

// Supprimer une commande (admin seulement)
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const orderId = searchParams.get("id");

    if (!orderId) {
      return NextResponse.json(
        {
          success: false,
          error: "ID de commande requis",
        },
        { status: 400 }
      );
    }

    // Supprimer la commande et ses items (cascade)
    await prisma.order.delete({
      where: { id: orderId },
    });

    const response: ApiResponse = {
      success: true,
      message: "Commande supprimée avec succès",
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Erreur lors de la suppression de la commande:", error);

    const response: ApiResponse = {
      success: false,
      error: error instanceof Error ? error.message : "Erreur interne du serveur",
    };

    return NextResponse.json(response, { status: 500 });
  }
}
