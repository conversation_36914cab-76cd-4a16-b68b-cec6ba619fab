@import "tailwindcss";

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
  font-weight: 400;
}

/* Assurer que la police Inter est bien chargée */
.font-sans {
  font-family: var(--font-inter), -apple-system, BlinkMacSystemFont, "Segoe UI",
    "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans",
    "Helvetica Neue", sans-serif;
}

/* Assurer que les classes de poids de police fonctionnent */
.font-bold {
  font-weight: 700 !important;
}

.font-semibold {
  font-weight: 600 !important;
}

.font-medium {
  font-weight: 500 !important;
}

.font-normal {
  font-weight: 400 !important;
}

.font-light {
  font-weight: 300 !important;
}

/* Classes spécifiques pour forcer l'affichage en gras */
h1,
h2,
h3,
h4,
h5,
h6 {
  font-weight: inherit;
}

.text-bold {
  font-weight: 700 !important;
}

.text-semibold {
  font-weight: 600 !important;
}

/* Améliorer la lisibilité des textes */
.text-gray-900 {
  color: #111827 !important;
}

.text-gray-800 {
  color: #1f2937 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

/* S'assurer que les titres sont bien visibles */
h1,
h2,
h3,
h4,
h5,
h6 {
  color: #1f2937;
  font-weight: 600;
}

/* Classes pour les cartes de dashboard */
.dashboard-card-title {
  color: #1f2937 !important;
  font-weight: 600 !important;
  font-size: 1.125rem !important;
}
