"use client";

import {
  ContactSection,
  FAQSection,
  HeroSection,
  TestimonialsSection,
} from "@/components/sections";
import ProductOverview from "@/components/sections/ProductOverview";

export default function Home() {
  return (
    <main>
      {/* Section d'accueil avec hero */}
      <HeroSection />

      {/* Section des produits */}
      <ProductOverview />

      {/* Section des témoignages */}
      <TestimonialsSection />

      {/* Section FAQ */}
      <FAQSection />

      {/* Section de contact */}
      <ContactSection />

      {/* Panneau de debug Stripe (développement uniquement) */}
      {process.env.NODE_ENV === "development" && (
        <div className="fixed bottom-4 right-4 bg-white border rounded-lg shadow-lg p-4 z-50 max-w-xs">
          <h3 className="font-bold mb-3">🧪 Debug Stripe</h3>

          {/* Bouton pour vérifier la DB */}
          <button
            onClick={async () => {
              try {
                const response = await fetch("/api/seed");
                const data = await response.json();

                if (data.success) {
                  alert(
                    `📊 État de la DB:\n- Produits: ${data.data.products}\n- Clients: ${data.data.customers}\n- Commandes: ${data.data.orders}`
                  );
                } else {
                  alert("❌ " + data.error);
                }
              } catch (error) {
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            📊 Vérifier DB
          </button>

          {/* Bouton pour nettoyer la DB */}
          <button
            onClick={async () => {
              if (
                !confirm(
                  "⚠️ Êtes-vous sûr de vouloir nettoyer la base de données ?"
                )
              ) {
                return;
              }
              try {
                const response = await fetch("/api/seed", {
                  method: "DELETE",
                });
                const data = await response.json();

                if (data.success) {
                  alert("✅ " + data.message);
                } else {
                  alert("❌ " + data.error);
                }
              } catch (error) {
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-red-500 text-white rounded text-sm"
          >
            🗑️ Nettoyer DB
          </button>

          {/* Bouton pour initialiser la DB */}
          <button
            onClick={async () => {
              try {
                const response = await fetch("/api/seed", {
                  method: "POST",
                });
                const data = await response.json();

                if (data.success) {
                  alert("✅ " + data.message);
                  // Recharger la page pour afficher les nouveaux produits
                  window.location.reload();
                } else {
                  alert("❌ " + data.error);
                }
              } catch (error) {
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-green-500 text-white rounded text-sm"
          >
            🌱 Initialiser DB
          </button>

          {/* Bouton de diagnostic */}
          <button
            onClick={async () => {
              try {
                // Vérifier le panier
                const cart = JSON.parse(
                  localStorage.getItem("cart-storage") || "{}"
                );
                const cartItems = cart?.state?.cart?.items || [];

                console.log("🛒 Articles dans le panier:", cartItems);

                // Vérifier les produits en base
                const productsResponse = await fetch("/api/products");
                const productsData = await productsResponse.json();

                console.log(
                  "📦 Produits en base:",
                  productsData.data?.products || []
                );

                // Vérifier les clients en base
                const customersResponse = await fetch("/api/customers");
                const customersData = await customersResponse.json();

                console.log(
                  "👥 Clients en base:",
                  customersData.data?.customers || []
                );

                alert(
                  `🔍 Diagnostic:\n\nPanier: ${
                    cartItems.length
                  } articles\nProduits: ${
                    productsData.data?.products?.length || 0
                  }\nClients: ${
                    customersData.data?.customers?.length || 0
                  }\n\nVoir la console pour plus de détails`
                );
              } catch (error) {
                alert("❌ Erreur diagnostic: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-purple-500 text-white rounded text-sm"
          >
            🔍 Diagnostic
          </button>

          {/* Bouton de test Prisma */}
          <button
            onClick={async () => {
              try {
                console.log("🧪 Test Prisma...");

                const response = await fetch("/api/test-prisma");
                const data = await response.json();
                console.log("📥 Réponse:", data);

                if (response.ok) {
                  alert("✅ Test Prisma réussi !");
                } else {
                  alert("❌ Erreur Prisma: " + data.error);
                }
              } catch (error) {
                console.error("❌ Erreur:", error);
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-orange-500 text-white rounded text-sm"
          >
            🧪 Test Prisma
          </button>

          {/* Bouton de test Commandes */}
          <button
            onClick={async () => {
              try {
                console.log("🧪 Test Commandes...");

                const response = await fetch("/api/test-orders");
                const data = await response.json();
                console.log("📥 Réponse commandes:", data);

                if (response.ok) {
                  alert(`✅ ${data.data.totalOrders} commandes trouvées !`);
                } else {
                  alert("❌ Erreur commandes: " + data.error);
                }
              } catch (error) {
                console.error("❌ Erreur:", error);
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full mb-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            🧪 Test Commandes
          </button>

          {/* Bouton de test principal */}
          <button
            onClick={async () => {
              try {
                console.log("🧪 Début du test Stripe...");

                // 1. Récupérer les produits existants
                const productsResponse = await fetch("/api/products");
                const productsData = await productsResponse.json();

                if (
                  !productsData.success ||
                  !productsData.data.products.length
                ) {
                  alert("❌ Aucun produit trouvé dans la base de données");
                  return;
                }

                const product = productsData.data.products[0];
                const variant = product.variants?.[0];

                console.log("✅ Produit trouvé:", product.name);

                // 2. Créer une commande avec de vrais IDs
                const orderResponse = await fetch("/api/orders", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({
                    items: [
                      {
                        productId: product.id,
                        variantId: variant?.id || product.id,
                        quantity: 1,
                      },
                    ],
                    shippingAddress: {
                      firstName: "Test",
                      lastName: "User",
                      email: "<EMAIL>",
                      phone: "0123456789",
                      street: "123 Rue de Test",
                      city: "Paris",
                      postalCode: "75001",
                      country: "France",
                    },
                    totalAmount: product.basePrice || 12.99,
                  }),
                });

                const orderData = await orderResponse.json();

                if (!orderResponse.ok) {
                  console.error("❌ Erreur commande:", orderData);
                  alert("❌ Erreur commande: " + orderData.error);
                  return;
                }

                console.log("✅ Commande créée:", orderData.data.id);

                // 3. Créer la session Stripe
                const sessionResponse = await fetch("/api/checkout/session", {
                  method: "POST",
                  headers: { "Content-Type": "application/json" },
                  body: JSON.stringify({ orderId: orderData.data.id }),
                });

                const sessionData = await sessionResponse.json();

                if (sessionResponse.ok && sessionData.success) {
                  console.log(
                    "✅ Session Stripe créée:",
                    sessionData.data.sessionId
                  );

                  if (confirm("🚀 Ouvrir la page de paiement Stripe ?")) {
                    window.location.href = sessionData.data.url;
                  } else {
                    alert(
                      "✅ Test réussi ! Session ID: " +
                        sessionData.data.sessionId
                    );
                  }
                } else {
                  console.error("❌ Erreur session:", sessionData);
                  alert("❌ Erreur session: " + sessionData.error);
                }
              } catch (error) {
                console.error("❌ Erreur:", error);
                alert("❌ Erreur: " + error);
              }
            }}
            className="w-full px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            🚀 Test Stripe
          </button>
        </div>
      )}
    </main>
  );
}
