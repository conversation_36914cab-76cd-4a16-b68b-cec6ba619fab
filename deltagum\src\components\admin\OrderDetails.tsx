"use client";

import { <PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui";
import { motion } from "framer-motion";
import { 
  ArrowLeft, 
  User, 
  Mail, 
  Phone, 
  MapPin, 
  Calendar,
  Package,
  DollarSign,
  Edit,
  Printer,
  Truck,
  CheckCircle,
  XCircle
} from "lucide-react";
import Image from "next/image";

interface Order {
  id: string;
  total: number;
  status: string;
  createdAt: string;
  customer: {
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
    address?: string;
    postalCode?: string;
    city?: string;
  };
  items: Array<{
    quantity: number;
    price: number;
    product: {
      name: string;
      image: string;
    };
    variant?: {
      flavor: string;
      color?: string;
    };
  }>;
}

interface OrderDetailsProps {
  order: Order;
  onBack: () => void;
  onUpdateStatus?: (orderId: string, status: string) => void;
}

export default function OrderDetails({ order, onBack, onUpdateStatus }: OrderDetailsProps) {
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('fr-FR', {
      day: 'numeric',
      month: 'long',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: 'EUR'
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'PROCESSING':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusLabel = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'Terminée';
      case 'PENDING':
        return 'En attente';
      case 'CANCELLED':
        return 'Annulée';
      case 'PROCESSING':
        return 'En cours';
      default:
        return status;
    }
  };

  const subtotal = order.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
  const shipping = 0; // Livraison gratuite pour l'instant
  const tax = subtotal * 0.2; // TVA 20%

  return (
    <motion.div
      initial={{ opacity: 0, x: 20 }}
      animate={{ opacity: 1, x: 0 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            size="sm"
            onClick={onBack}
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour
          </Button>
          <div>
            <h2 className="text-2xl font-bold text-gray-900">
              Commande #{order.id.slice(-8)}
            </h2>
            <p className="text-gray-600">Détails de la commande</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <span className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${getStatusColor(order.status)}`}>
            {getStatusLabel(order.status)}
          </span>
          <Button variant="outline" size="sm">
            <Printer className="w-4 h-4 mr-2" />
            Imprimer
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Informations principales */}
        <div className="lg:col-span-2 space-y-6">
          {/* Articles commandés */}
          <Card>
            <CardHeader>
              <CardTitle>Articles commandés ({order.items.length})</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {order.items.map((item, index) => (
                  <div key={index} className="flex items-center space-x-4 p-4 bg-gray-50 rounded-lg">
                    <div className="relative w-16 h-16 rounded-lg overflow-hidden bg-white">
                      <Image
                        src={item.product.image || '/img/placeholder.svg'}
                        alt={item.product.name}
                        fill
                        className="object-cover"
                        onError={(e) => {
                          e.currentTarget.src = '/img/placeholder.svg';
                        }}
                      />
                    </div>
                    
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{item.product.name}</h4>
                      {item.variant && (
                        <p className="text-sm text-gray-600 capitalize">
                          Saveur: {item.variant.flavor}
                        </p>
                      )}
                      <p className="text-sm text-gray-500">
                        {formatCurrency(item.price)} × {item.quantity}
                      </p>
                    </div>
                    
                    <div className="text-right">
                      <p className="font-semibold text-gray-900">
                        {formatCurrency(item.price * item.quantity)}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Informations client */}
          <Card>
            <CardHeader>
              <CardTitle>Informations client</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-500">Nom complet</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <User className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-900">{order.customer.firstName} {order.customer.lastName}</p>
                  </div>
                </div>
                
                <div>
                  <label className="text-sm font-medium text-gray-500">Email</label>
                  <div className="flex items-center space-x-2 mt-1">
                    <Mail className="w-4 h-4 text-gray-400" />
                    <p className="text-gray-900">{order.customer.email}</p>
                  </div>
                </div>
                
                {order.customer.phone && (
                  <div>
                    <label className="text-sm font-medium text-gray-500">Téléphone</label>
                    <div className="flex items-center space-x-2 mt-1">
                      <Phone className="w-4 h-4 text-gray-400" />
                      <p className="text-gray-900">{order.customer.phone}</p>
                    </div>
                  </div>
                )}
              </div>
              
              {order.customer.address && (
                <div>
                  <label className="text-sm font-medium text-gray-500">Adresse de livraison</label>
                  <div className="flex items-start space-x-2 mt-1">
                    <MapPin className="w-4 h-4 text-gray-400 mt-0.5" />
                    <div>
                      <p className="text-gray-900">{order.customer.address}</p>
                      {order.customer.postalCode && order.customer.city && (
                        <p className="text-gray-600">{order.customer.postalCode} {order.customer.city}</p>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Résumé de la commande */}
          <Card>
            <CardHeader>
              <CardTitle>Résumé</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-gray-600">Sous-total</span>
                <span className="font-medium">{formatCurrency(subtotal)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">Livraison</span>
                <span className="font-medium">{shipping === 0 ? 'Gratuite' : formatCurrency(shipping)}</span>
              </div>
              
              <div className="flex justify-between">
                <span className="text-gray-600">TVA (20%)</span>
                <span className="font-medium">{formatCurrency(tax)}</span>
              </div>
              
              <div className="border-t pt-3">
                <div className="flex justify-between">
                  <span className="text-lg font-semibold">Total</span>
                  <span className="text-lg font-bold text-pink-600">{formatCurrency(order.total)}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Informations de commande */}
          <Card>
            <CardHeader>
              <CardTitle>Informations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div>
                <label className="text-sm font-medium text-gray-500">Date de commande</label>
                <div className="flex items-center space-x-2 mt-1">
                  <Calendar className="w-4 h-4 text-gray-400" />
                  <p className="text-gray-900">{formatDate(order.createdAt)}</p>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Numéro de commande</label>
                <p className="text-gray-900 font-mono">#{order.id}</p>
              </div>
              
              <div>
                <label className="text-sm font-medium text-gray-500">Articles</label>
                <div className="flex items-center space-x-2 mt-1">
                  <Package className="w-4 h-4 text-gray-400" />
                  <p className="text-gray-900">{order.items.length} article(s)</p>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {order.status === 'PENDING' && (
                <>
                  <Button 
                    className="w-full bg-blue-600 hover:bg-blue-700"
                    onClick={() => onUpdateStatus?.(order.id, 'PROCESSING')}
                  >
                    <Edit className="w-4 h-4 mr-2" />
                    Marquer en cours
                  </Button>
                  <Button 
                    className="w-full bg-green-600 hover:bg-green-700"
                    onClick={() => onUpdateStatus?.(order.id, 'COMPLETED')}
                  >
                    <CheckCircle className="w-4 h-4 mr-2" />
                    Marquer terminée
                  </Button>
                </>
              )}
              
              {order.status === 'PROCESSING' && (
                <Button 
                  className="w-full bg-green-600 hover:bg-green-700"
                  onClick={() => onUpdateStatus?.(order.id, 'COMPLETED')}
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Marquer terminée
                </Button>
              )}
              
              {order.status !== 'CANCELLED' && order.status !== 'COMPLETED' && (
                <Button 
                  variant="danger"
                  className="w-full"
                  onClick={() => onUpdateStatus?.(order.id, 'CANCELLED')}
                >
                  <XCircle className="w-4 h-4 mr-2" />
                  Annuler la commande
                </Button>
              )}
              
              <Button variant="outline" className="w-full">
                <Mail className="w-4 h-4 mr-2" />
                Contacter le client
              </Button>
              
              <Button variant="outline" className="w-full">
                <Truck className="w-4 h-4 mr-2" />
                Suivi de livraison
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    </motion.div>
  );
}
