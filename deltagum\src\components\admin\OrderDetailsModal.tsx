"use client";

import { But<PERSON> } from "@/components/ui";
import { motion, AnimatePresence } from "framer-motion";
import { X, Package, User, MapPin, Calendar, CreditCard } from "lucide-react";

interface OrderItem {
  id: string;
  quantity: number;
  price: number;
  product: {
    name: string;
  };
  variant?: {
    flavor: string;
  };
}

interface Order {
  id: string;
  customerId: string;
  customer: {
    firstName: string;
    lastName: string;
    email: string;
  };
  totalAmount: number;
  status: string;
  createdAt: string;
  items: OrderItem[];
  shippingFirstName: string;
  shippingLastName: string;
  shippingStreet: string;
  shippingCity: string;
  shippingPostalCode: string;
  shippingPhone?: string;
}

interface OrderDetailsModalProps {
  order: Order | null;
  isOpen: boolean;
  onClose: () => void;
}

const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    case 'confirmed':
      return 'bg-blue-100 text-blue-800 border-blue-200';
    case 'shipped':
      return 'bg-purple-100 text-purple-800 border-purple-200';
    case 'delivered':
      return 'bg-green-100 text-green-800 border-green-200';
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200';
    default:
      return 'bg-gray-100 text-gray-800 border-gray-200';
  }
};

const getStatusLabel = (status: string) => {
  switch (status.toLowerCase()) {
    case 'pending':
      return 'En attente';
    case 'confirmed':
      return 'Confirmée';
    case 'shipped':
      return 'Expédiée';
    case 'delivered':
      return 'Livrée';
    case 'cancelled':
      return 'Annulée';
    default:
      return status;
  }
};

export default function OrderDetailsModal({ order, isOpen, onClose }: OrderDetailsModalProps) {
  if (!order) return null;

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <motion.div
            initial={{ opacity: 0, scale: 0.95, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: 20 }}
            className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden shadow-2xl"
          >
            {/* Header */}
            <div className="bg-gradient-to-r from-pink-500 to-orange-500 px-6 py-4 text-white">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold">
                    Commande #{order.id.slice(-8)}
                  </h2>
                  <p className="text-pink-100 text-sm">
                    {new Date(order.createdAt).toLocaleDateString('fr-FR', {
                      year: 'numeric',
                      month: 'long',
                      day: 'numeric',
                      hour: '2-digit',
                      minute: '2-digit'
                    })}
                  </p>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`px-3 py-1 rounded-full text-sm font-medium border ${getStatusColor(order.status)}`}>
                    {getStatusLabel(order.status)}
                  </span>
                  <Button
                    onClick={onClose}
                    variant="outline"
                    size="sm"
                    className="text-white border-white hover:bg-white hover:text-pink-600"
                  >
                    <X className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                {/* Informations client */}
                <div className="space-y-6">
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <User className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">Informations client</h3>
                    </div>
                    <div className="space-y-2">
                      <p className="text-gray-900 font-medium">
                        {order.customer.firstName} {order.customer.lastName}
                      </p>
                      <p className="text-gray-600 text-sm">{order.customer.email}</p>
                      <p className="text-gray-500 text-xs">ID: {order.customerId}</p>
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <MapPin className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">Adresse de livraison</h3>
                    </div>
                    <div className="space-y-1">
                      <p className="text-gray-900 font-medium">
                        {order.shippingFirstName} {order.shippingLastName}
                      </p>
                      <p className="text-gray-700">{order.shippingStreet}</p>
                      <p className="text-gray-700">
                        {order.shippingPostalCode} {order.shippingCity}
                      </p>
                      {order.shippingPhone && (
                        <p className="text-gray-600 text-sm">Tél: {order.shippingPhone}</p>
                      )}
                    </div>
                  </div>

                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-3">
                      <CreditCard className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">Résumé financier</h3>
                    </div>
                    <div className="space-y-2">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Sous-total</span>
                        <span className="text-gray-900">{Number(order.totalAmount).toFixed(2)}€</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Livraison</span>
                        <span className="text-gray-900">Gratuite</span>
                      </div>
                      <div className="border-t pt-2 flex justify-between font-bold">
                        <span className="text-gray-900">Total</span>
                        <span className="text-pink-600 text-lg">{Number(order.totalAmount).toFixed(2)}€</span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Articles commandés */}
                <div>
                  <div className="bg-gray-50 rounded-lg p-4">
                    <div className="flex items-center space-x-2 mb-4">
                      <Package className="w-5 h-5 text-gray-600" />
                      <h3 className="font-semibold text-gray-900">Articles commandés</h3>
                      <span className="bg-pink-100 text-pink-800 text-xs px-2 py-1 rounded-full">
                        {order.items.length} article{order.items.length > 1 ? 's' : ''}
                      </span>
                    </div>
                    
                    <div className="space-y-3">
                      {order.items.map((item) => (
                        <div key={item.id} className="bg-white rounded-lg p-4 border border-gray-200">
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <h4 className="font-medium text-gray-900 mb-1">
                                {item.product.name}
                              </h4>
                              {item.variant?.flavor && (
                                <p className="text-sm text-gray-600 mb-2">
                                  Saveur: {item.variant.flavor}
                                </p>
                              )}
                              <div className="flex items-center space-x-4 text-sm text-gray-500">
                                <span>Quantité: {item.quantity}</span>
                                <span>Prix unitaire: {Number(item.price).toFixed(2)}€</span>
                              </div>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-gray-900">
                                {(Number(item.price) * item.quantity).toFixed(2)}€
                              </p>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Footer */}
            <div className="bg-gray-50 px-6 py-4 border-t">
              <div className="flex justify-between items-center">
                <div className="text-sm text-gray-500">
                  Commande créée le {new Date(order.createdAt).toLocaleDateString('fr-FR')}
                </div>
                <div className="flex space-x-3">
                  <Button variant="outline" onClick={onClose}>
                    Fermer
                  </Button>
                  <Button variant="primary">
                    Modifier le statut
                  </Button>
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
}
