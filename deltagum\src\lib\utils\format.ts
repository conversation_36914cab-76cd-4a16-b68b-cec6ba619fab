/**
 * Utilitaires de formatage pour l'application Deltagum
 */

/**
 * Formate un prix en string avec 2 décimales
 * @param price - Le prix à formater (peut être string, number, ou undefined)
 * @returns Le prix formaté avec 2 décimales
 */
export const formatPrice = (price: any): string => {
  const numPrice = Number(price || 0);
  return isNaN(numPrice) ? "0.00" : numPrice.toFixed(2);
};

/**
 * Formate un prix avec le symbole euro
 * @param price - Le prix à formater
 * @returns Le prix formaté avec le symbole €
 */
export const formatPriceWithCurrency = (price: any): string => {
  return `${formatPrice(price)}€`;
};

/**
 * Formate une date en français
 * @param date - La date à formater
 * @returns La date formatée en français
 */
export const formatDate = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

/**
 * Formate une date avec l'heure en français
 * @param date - La date à formater
 * @returns La date et l'heure formatées en français
 */
export const formatDateTime = (date: string | Date): string => {
  const dateObj = typeof date === 'string' ? new Date(date) : date;
  return dateObj.toLocaleDateString('fr-FR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

/**
 * Sécurise l'accès à un tableau qui peut être undefined
 * @param array - Le tableau à sécuriser
 * @returns Un tableau vide si undefined, sinon le tableau original
 */
export const safeArray = <T>(array: T[] | undefined | null): T[] => {
  return array || [];
};

/**
 * Sécurise l'accès à une propriété qui peut être undefined
 * @param value - La valeur à sécuriser
 * @param defaultValue - La valeur par défaut
 * @returns La valeur ou la valeur par défaut
 */
export const safeValue = <T>(value: T | undefined | null, defaultValue: T): T => {
  return value ?? defaultValue;
};
