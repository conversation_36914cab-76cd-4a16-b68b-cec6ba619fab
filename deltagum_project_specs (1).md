# Deltagum - Site E-commerce One-Page
## Architecture React Full Stack (From Scratch)

### 🎯 Vue d'ensemble du projet

**Deltagum** est un site e-commerce one-page développé entièrement from scratch avec une stack moderne React/Next.js. Cette approche offre un contrôle total sur l'architecture et les fonctionnalités, optimisée pour un mono-produit avec 3 variantes de saveurs.

**Architecture :**
```
React Frontend (Next.js) ←→ API Routes (Next.js) ←→ Base de données (PostgreSQL)
        ↓                        ↓                        ↓
   Interface custom         Logique métier           Données produits
   Animations fluides       Paiements Stripe         Commandes/Stock
```

---

## 🏗️ Stack technique complète

### Frontend
- **React 18** + **TypeScript**
- **Next.js 14** (App Router + API Routes)
- **Tailwind CSS** + **Framer Motion**
- **Zustand** (state management)
- **React Hook Form** + **Zod** (validation)

### Backend (API Routes Next.js)
- **Next.js API Routes** (endpoints REST)
- **Prisma ORM** (base de données)
- **PostgreSQL** (Supabase ou Vercel Postgres)
- **NextAuth.js** (authentification)

### Paiements & Services
- **Stripe** (paiements sécurisés)
- **Resend** (emails transactionnels)
- **Uploadthing** (gestion fichiers)

### Animations & UX
- **Framer Motion** (animations fluides)
- **Lottie React** (animations complexes)
- **React Spring** (micro-interactions)

### Hébergement
- **Vercel** (frontend + API + base de données)

---

## 🗄️ Modèle de données

### Schema Prisma
```prisma
// prisma/schema.prisma
generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model Product {
  id          String   @id @default(cuid())
  name        String
  description String
  price       Decimal  @db.Decimal(10, 2)
  image       String
  active      Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  
  variants    ProductVariant[]
  orderItems  OrderItem[]
  
  @@map("products")
}

model ProductVariant {
  id        String   @id @default(cuid())
  productId String
  flavor    FlavorType
  color     String
  stock     Int      @default(0)
  sku       String   @unique
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  product    Product     @relation(fields: [productId], references: [id])
  orderItems OrderItem[]
  
  @@map("product_variants")
}

model Customer {
  id        String   @id @default(cuid())
  email     String   @unique
  firstName String
  lastName  String
  phone     String?
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt
  
  orders    Order[]
  loyalty   LoyaltyProgram?
  
  @@map("customers")
}

model Order {
  id             String      @id @default(cuid())
  customerId     String
  status         OrderStatus @default(PENDING)
  totalAmount    Decimal     @db.Decimal(10, 2)
  stripePaymentId String?
  shippingAddress Json
  createdAt      DateTime    @default(now())
  updatedAt      DateTime    @updatedAt
  
  customer   Customer    @relation(fields: [customerId], references: [id])
  items      OrderItem[]
  
  @@map("orders")
}

model OrderItem {
  id        String @id @default(cuid())
  orderId   String
  productId String
  variantId String
  quantity  Int
  price     Decimal @db.Decimal(10, 2)
  
  order   Order          @relation(fields: [orderId], references: [id])
  product Product        @relation(fields: [productId], references: [id])
  variant ProductVariant @relation(fields: [variantId], references: [id])
  
  @@map("order_items")
}

model LoyaltyProgram {
  id         String @id @default(cuid())
  customerId String @unique
  points     Int    @default(0)
  level      LoyaltyLevel @default(BRONZE)
  
  customer Customer @relation(fields: [customerId], references: [id])
  
  @@map("loyalty_programs")
}

enum FlavorType {
  STRAWBERRY
  BLUEBERRY
  APPLE
}

enum OrderStatus {
  PENDING
  PAID
  SHIPPED
  DELIVERED
  CANCELLED
}

enum LoyaltyLevel {
  BRONZE
  SILVER
  GOLD
}
```

---

## 🧩 Architecture des composants

### Structure du projet
```
src/
├── app/
│   ├── api/
│   │   ├── products/
│   │   │   └── route.ts
│   │   ├── orders/
│   │   │   ├── route.ts
│   │   │   └── [id]/route.ts
│   │   ├── customers/
│   │   │   └── route.ts
│   │   ├── stripe/
│   │   │   ├── webhook/route.ts
│   │   │   └── create-payment-intent/route.ts
│   │   └── auth/
│   │       └── [...nextauth]/route.ts
│   ├── page.tsx
│   └── layout.tsx
├── components/
│   ├── layout/
│   │   ├── Header.tsx
│   │   ├── Footer.tsx
│   │   └── Navigation.tsx
│   ├── sections/
│   │   ├── HeroSection.tsx
│   │   ├── ProductSection.tsx
│   │   ├── FlavorSelector.tsx
│   │   ├── CartSection.tsx
│   │   ├── CheckoutForm.tsx
│   │   ├── TestimonialsSection.tsx
│   │   └── FAQSection.tsx
│   ├── ui/
│   │   ├── Button.tsx
│   │   ├── Card.tsx
│   │   ├── Modal.tsx
│   │   ├── Input.tsx
│   │   └── Badge.tsx
│   └── animations/
│       ├── FloatingCandy.tsx
│       ├── ParallaxSection.tsx
│       └── ConfettiAnimation.tsx
├── lib/
│   ├── prisma.ts
│   ├── stripe.ts
│   ├── validations.ts
│   └── utils.ts
├── store/
│   ├── cart.ts
│   ├── products.ts
│   └── customer.ts
├── types/
│   └── index.ts
└── prisma/
    ├── schema.prisma
    └── seed.ts
```

---

## 🔧 API Routes Next.js

### API Products
```typescript
// app/api/products/route.ts
import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';

export async function GET() {
  try {
    const products = await prisma.product.findMany({
      where: { active: true },
      include: {
        variants: {
          where: { stock: { gt: 0 } }
        }
      }
    });

    return NextResponse.json(products);
  } catch (error) {
    console.error('Error fetching products:', error);
    return NextResponse.json(
      { error: 'Failed to fetch products' },
      { status: 500 }
    );
  }
}
```

### API Orders
```typescript
// app/api/orders/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { stripe } from '@/lib/stripe';
import { z } from 'zod';

const createOrderSchema = z.object({
  customerId: z.string(),
  items: z.array(z.object({
    productId: z.string(),
    variantId: z.string(),
    quantity: z.number().positive()
  })),
  shippingAddress: z.object({
    firstName: z.string(),
    lastName: z.string(),
    street: z.string(),
    city: z.string(),
    postalCode: z.string(),
    country: z.string()
  })
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const validatedData = createOrderSchema.parse(body);

    // Calculer le montant total
    const variants = await prisma.productVariant.findMany({
      where: {
        id: { in: validatedData.items.map(item => item.variantId) }
      },
      include: { product: true }
    });

    const totalAmount = validatedData.items.reduce((sum, item) => {
      const variant = variants.find(v => v.id === item.variantId);
      return sum + (variant?.product.price.toNumber() || 0) * item.quantity;
    }, 0);

    // Créer la commande
    const order = await prisma.order.create({
      data: {
        customerId: validatedData.customerId,
        totalAmount,
        shippingAddress: validatedData.shippingAddress,
        items: {
          create: validatedData.items.map(item => {
            const variant = variants.find(v => v.id === item.variantId);
            return {
              productId: item.productId,
              variantId: item.variantId,
              quantity: item.quantity,
              price: variant?.product.price || 0
            };
          })
        }
      },
      include: {
        items: {
          include: {
            product: true,
            variant: true
          }
        }
      }
    });

    // Créer l'intent de paiement Stripe
    const paymentIntent = await stripe.paymentIntents.create({
      amount: Math.round(totalAmount * 100), // en centimes
      currency: 'eur',
      metadata: {
        orderId: order.id
      }
    });

    return NextResponse.json({
      order,
      clientSecret: paymentIntent.client_secret
    });

  } catch (error) {
    console.error('Error creating order:', error);
    return NextResponse.json(
      { error: 'Failed to create order' },
      { status: 500 }
    );
  }
}
```

### Webhook Stripe
```typescript
// app/api/stripe/webhook/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { stripe } from '@/lib/stripe';
import { prisma } from '@/lib/prisma';
import { headers } from 'next/headers';

export async function POST(request: NextRequest) {
  const body = await request.text();
  const signature = headers().get('stripe-signature')!;

  let event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      signature,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (error) {
    console.error('Webhook signature verification failed:', error);
    return NextResponse.json({ error: 'Invalid signature' }, { status: 400 });
  }

  switch (event.type) {
    case 'payment_intent.succeeded':
      const paymentIntent = event.data.object;
      const orderId = paymentIntent.metadata.orderId;

      // Mettre à jour le statut de la commande
      await prisma.order.update({
        where: { id: orderId },
        data: {
          status: 'PAID',
          stripePaymentId: paymentIntent.id
        }
      });

      // Décrémenter le stock
      const order = await prisma.order.findUnique({
        where: { id: orderId },
        include: { items: true }
      });

      if (order) {
        for (const item of order.items) {
          await prisma.productVariant.update({
            where: { id: item.variantId },
            data: { stock: { decrement: item.quantity } }
          });
        }
      }

      break;

    default:
      console.log(`Unhandled event type: ${event.type}`);
  }

  return NextResponse.json({ received: true });
}
```

---

## 🛍️ Gestion du panier (Zustand)

### Store panier
```typescript
// store/cart.ts
import { create } from 'zustand';
import { persist } from 'zustand/middleware';

interface CartItem {
  id: string;
  productId: string;
  variantId: string;
  flavor: 'strawberry' | 'blueberry' | 'apple';
  quantity: number;
  price: number;
  name: string;
  image: string;
}

interface CartState {
  items: CartItem[];
  total: number;
  addItem: (item: Omit<CartItem, 'id'>) => void;
  removeItem: (id: string) => void;
  updateQuantity: (id: string, quantity: number) => void;
  clearCart: () => void;
  getItemCount: () => number;
}

export const useCartStore = create<CartState>()(
  persist(
    (set, get) => ({
      items: [],
      total: 0,

      addItem: (newItem) => {
        const items = get().items;
        const existingItem = items.find(
          item => item.productId === newItem.productId && 
                  item.variantId === newItem.variantId
        );

        if (existingItem) {
          set({
            items: items.map(item =>
              item.id === existingItem.id
                ? { ...item, quantity: item.quantity + newItem.quantity }
                : item
            )
          });
        } else {
          set({
            items: [...items, { ...newItem, id: crypto.randomUUID() }]
          });
        }

        // Recalculer le total
        const newTotal = get().items.reduce(
          (sum, item) => sum + (item.price * item.quantity), 0
        );
        set({ total: newTotal });
      },

      removeItem: (id) => {
        set({
          items: get().items.filter(item => item.id !== id)
        });
        
        const newTotal = get().items.reduce(
          (sum, item) => sum + (item.price * item.quantity), 0
        );
        set({ total: newTotal });
      },

      updateQuantity: (id, quantity) => {
        if (quantity <= 0) {
          get().removeItem(id);
          return;
        }

        set({
          items: get().items.map(item =>
            item.id === id ? { ...item, quantity } : item
          )
        });

        const newTotal = get().items.reduce(
          (sum, item) => sum + (item.price * item.quantity), 0
        );
        set({ total: newTotal });
      },

      clearCart: () => {
        set({ items: [], total: 0 });
      },

      getItemCount: () => {
        return get().items.reduce((sum, item) => sum + item.quantity, 0);
      }
    }),
    {
      name: 'deltagum-cart'
    }
  )
);
```

---

## 🎨 Composants avec animations

### Sélecteur de saveurs
```typescript
// components/sections/FlavorSelector.tsx
'use client';

import { useState } from 'react';
import { motion } from 'framer-motion';
import { useCartStore } from '@/store/cart';
import { LottiePlayer } from '@lottiefiles/react-lottie-player';
import confetti from 'canvas-confetti';

interface Product {
  id: string;
  name: string;
  price: number;
  image: string;
  variants: {
    id: string;
    flavor: 'strawberry' | 'blueberry' | 'apple';
    color: string;
    stock: number;
  }[];
}

interface FlavorSelectorProps {
  product: Product;
}

const flavorConfig = {
  strawberry: {
    name: 'Fraise',
    color: '#FF6B9D',
    animation: '/animations/strawberry.json'
  },
  blueberry: {
    name: 'Myrtille',
    color: '#4ECDC4',
    animation: '/animations/blueberry.json'
  },
  apple: {
    name: 'Pomme',
    color: '#95E1D3',
    animation: '/animations/apple.json'
  }
};

export const FlavorSelector = ({ product }: FlavorSelectorProps) => {
  const [selectedVariant, setSelectedVariant] = useState(product.variants[0]);
  const [quantity, setQuantity] = useState(1);
  const [isAdding, setIsAdding] = useState(false);
  const { addItem } = useCartStore();

  const handleAddToCart = async () => {
    if (!selectedVariant) return;

    setIsAdding(true);

    try {
      addItem({
        productId: product.id,
        variantId: selectedVariant.id,
        flavor: selectedVariant.flavor,
        quantity,
        price: product.price,
        name: product.name,
        image: product.image
      });

      // Animation de succès
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 },
        colors: [selectedVariant.color]
      });

      // Reset quantité
      setQuantity(1);
    } finally {
      setIsAdding(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto p-8">
      <motion.h2 
        className="text-4xl font-bold text-center mb-12 font-poppins"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        Choisissez votre saveur préférée
      </motion.h2>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
        {product.variants.map((variant, index) => {
          const config = flavorConfig[variant.flavor];
          const isSelected = selectedVariant?.id === variant.id;
          
          return (
            <motion.div
              key={variant.id}
              className="relative cursor-pointer"
              initial={{ opacity: 0, y: 30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: index * 0.1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => setSelectedVariant(variant)}
            >
              <motion.div
                className="p-8 rounded-3xl border-4 text-center transition-all duration-300 bg-white shadow-lg"
                animate={{
                  borderColor: isSelected ? config.color : '#E5E7EB',
                  backgroundColor: isSelected ? `${config.color}15` : '#ffffff',
                  boxShadow: isSelected 
                    ? `0 20px 40px ${config.color}30` 
                    : '0 10px 30px rgba(0,0,0,0.1)'
                }}
              >
                <div className="w-32 h-32 mx-auto mb-6 relative">
                  <LottiePlayer
                    src={config.animation}
                    autoplay
                    loop
                    className="w-full h-full"
                  />
                  {isSelected && (
                    <motion.div
                      className="absolute -top-2 -right-2 w-8 h-8 rounded-full flex items-center justify-center"
                      style={{ backgroundColor: config.color }}
                      initial={{ scale: 0 }}
                      animate={{ scale: 1 }}
                      transition={{ type: 'spring', stiffness: 300 }}
                    >
                      <svg className="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    </motion.div>
                  )}
                </div>

                <h3 className="font-bold text-2xl mb-3 font-poppins">
                  {config.name}
                </h3>
                <p className="text-gray-600 mb-4">
                  Stock: {variant.stock} unités
                </p>
                <div className="text-3xl font-bold" style={{ color: config.color }}>
                  {product.price.toFixed(2)}€
                </div>
              </motion.div>
            </motion.div>
          );
        })}
      </div>

      {selectedVariant && (
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="bg-white rounded-3xl p-8 shadow-xl border-2"
          style={{ borderColor: `${selectedVariant.color}30` }}
        >
          <div className="flex flex-col md:flex-row items-center justify-between gap-6">
            <div className="flex items-center space-x-4">
              <div className="w-16 h-16 rounded-full flex items-center justify-center"
                   style={{ backgroundColor: `${selectedVariant.color}20` }}>
                <LottiePlayer
                  src={flavorConfig[selectedVariant.flavor].animation}
                  autoplay
                  loop
                  className="w-12 h-12"
                />
              </div>
              <div>
                <h3 className="text-2xl font-bold font-poppins">
                  {flavorConfig[selectedVariant.flavor].name} sélectionné
                </h3>
                <p className="text-gray-600">
                  {product.name} • {product.price.toFixed(2)}€ l'unité
                </p>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <div className="flex items-center space-x-3">
                <label className="text-lg font-medium">Quantité:</label>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    className="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                    </svg>
                  </button>
                  <span className="w-12 text-center text-xl font-bold">{quantity}</span>
                  <button
                    onClick={() => setQuantity(Math.min(selectedVariant.stock, quantity + 1))}
                    className="w-10 h-10 rounded-full bg-gray-200 hover:bg-gray-300 flex items-center justify-center transition-colors"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                  </button>
                </div>
              </div>

              <motion.button
                onClick={handleAddToCart}
                disabled={isAdding || selectedVariant.stock === 0}
                className="px-8 py-4 rounded-2xl font-bold text-lg text-white disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ 
                  backgroundColor: selectedVariant.color,
                  boxShadow: `0 10px 30px ${selectedVariant.color}40`
                }}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {isAdding ? (
                  <div className="flex items-center space-x-2">
                    <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                    <span>Ajout...</span>
                  </div>
                ) : (
                  `Ajouter ${quantity} • ${(product.price * quantity).toFixed(2)}€`
                )}
              </motion.button>
            </div>
          </div>
        </motion.div>
      )}
    </div>
  );
};
```

---

## 💳 Intégration Stripe

### Configuration Stripe
```typescript
// lib/stripe.ts
import Stripe from 'stripe';

export const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, {
  apiVersion: '2023-10-16',
});
```

### Formulaire de checkout
```typescript
// components/sections/CheckoutForm.tsx
'use client';

import { useState } from 'react';
import { useStripe, useElements, CardElement } from '@stripe/react-stripe-js';
import { motion } from 'framer-motion';
import { useCartStore } from '@/store/cart';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';

const checkoutSchema = z.object({
  email: z.string().email('Email invalide'),
  firstName: z.string().min(1, 'Prénom requis'),
  lastName: z.string().min(1, 'Nom requis'),
  street: z.string().min(1, 'Adresse requise'),
  city: z.string().min(1, 'Ville requise'),
  postalCode: z.string().min(5, 'Code postal invalide'),
  country: z.string().min(1, 'Pays requis'),
});

type CheckoutFormData = z.infer<typeof checkoutSchema>;

export const CheckoutForm = () => {
  const stripe = useStripe();
  const elements = useElements();
  const { items, total, clearCart } = useCartStore();
  const [processing, setProcessing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const { register, handleSubmit, formState: { errors } } = useForm<CheckoutFormData>({
    resolver: zodResolver(checkoutSchema)
  });

  const onSubmit = async (data: CheckoutFormData) => {
    if (!stripe || !elements) return;

    setProcessing(true);
    setError(null);

    try {
      // Créer la commande
      const orderResponse = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          items: items.map(item => ({
            productId: item.productId,
            variantId: item.variantId,
            quantity: item.quantity
          })),
          shippingAddress: data
        })
      });

      const { clientSecret } = await orderResponse.json();

      // Confirmer le paiement
      const result = await stripe.confirmCardPayment(clientSecret, {
        payment_method: {
          card: elements.getElement(CardElement)!,
          billing_details: {
            name: `${data.firstName} ${data.lastName}`,
            email: data.email,
            address: {
              line1: data.street,
              city: data.city,
              postal_code: data.postalCode,
              country: data.country
            }
          }
        }
      });

      if (result.error) {
        setError(result.error.message || 'Erreur de paiement');
      } else {
        // Paiement réussi
        clearCart();
        // Rediriger vers page de confirmation
        window.location.href = '/confirmation';
      }
    } catch (err) {
      setError('Erreur lors du traitement de la commande');
    } finally {
      setProcessing(false);
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="max-w-2xl mx-auto p-8"
    >
      <h2 className="text-3xl font-bold mb-8 text-center font-poppins">
        Finaliser votre commande
      </h2>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
        {/* Informations personnelles */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Prénom</label>
            <input
              {...register('firstName')}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="John"
            />
            {errors.firstName && (
              <p className="text-red-500 text-sm mt-1">{errors.firstName.message}</p>
            )}
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Nom</label>
            <input
              {...register('lastName')}
              className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Doe"
            />
            {errors.lastName && (
              <p className="text-red-500 text-sm mt-1">{errors.lastName.message}</p>
            )}
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium mb-2">Email</label>
          <input
            {...register('email')}
            type="email"
            className="w-full px